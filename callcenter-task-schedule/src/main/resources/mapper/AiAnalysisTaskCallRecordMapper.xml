<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.taskcenter.mapper.crm.AiAnalysisTaskCallRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.taskcenter.entity.AiAnalysisTaskCallRecord">
        <id column="id" property="id" />
        <result column="analysis_task_id" property="analysisTaskId" />
        <result column="analysis_task_data_id" property="analysisTaskDataId" />
        <result column="call_record_id" property="callRecordId" />
        <result column="chat_contact_id" property="chatContactId" />
        <result column="planner_no" property="plannerNo" />
        <result column="agent_name" property="agentName" />
        <result column="uuid" property="uuid" />
        <result column="call_type" property="callType" />
        <result column="phone" property="phone" />
        <result column="agent_dept" property="agentDept" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="analysis_time" property="analysisTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="record_url" property="recordUrl" />
        <result column="record_type" property="recordType" />
        <result column="customer_id" property="customerId" />
        <result column="total_sub_task_count" property="totalSubTaskCount" />
        <result column="finished_sub_task_count" property="finishedSubTaskCount" />
        <result column="customer_base_data" property="customerBaseData" />
        <result column="customer_business_data" property="customerBusinessData" />
        <result column="data_source_types" property="dataSourceTypes" />
    </resultMap>
    <resultMap id="AiAnalysisTaskCallRecordExcelMap" type="com.yirendai.taskcenter.model.excel.AiAnalysisTaskCallRecordExcel">
        <id column="id" property="id" />
        <result column="analysis_task_id" property="analysisTaskId" />
        <result column="call_record_id" property="callRecordId" />
        <result column="chat_contact_id" property="chatContactId" />
        <result column="planner_no" property="plannerNo" />
        <result column="agent_name" property="agentName" />
        <result column="uuid" property="uuid" />
        <result column="call_type" property="callType" />
        <result column="phone" property="phone" />
        <result column="agent_dept" property="agentDept" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user_name" property="createUserName" />
        <result column="status" property="status" />
        <result column="analysis_time" property="analysisTime" />
        <result column="processed_content" property="processedContent" />
        <result column="origin_content" property="originContent" />
        <result column="customer_uid" property="customerUid" />
        <result column="customer_name" property="customerName" />
        <result column="start_stamp" property="startStamp" />
    </resultMap>

    <select id="pageCallRecordList" resultMap="AiAnalysisTaskCallRecordExcelMap">
        select
            record.id,
            record.analysis_task_id,
            record.call_record_id,
            record.chat_contact_id,
            record.planner_no,
            record.agent_name,
            record.uuid,
            record.call_type,
            record.phone,
            record.agent_dept,
            record.tenant_id,
            record.create_user_name,
            record.status,
            record.analysis_time,
            contact.processed_content  ,
            contact.origin_content,
            call_record.customer_uid,
            call_record.customer_name,
            call_record.start_stamp
        from ai_analysis_task_call_record record
        left join ai_planner_chat_contact contact on record.chat_contact_id = contact.id
        left join callcenter_call_record call_record on record.call_record_id = call_record.id
        <where>
            <if test="req.analysisTaskId != null">
                and record.analysis_task_id = #{req.analysisTaskId}
            </if>
            <if test="req.plannerNo != null and req.plannerNo !=''">
                and record.planner_no = #{req.plannerNo}
            </if>
            <if test="req.agentName != null  and req.agentName !=''" >
                and record.agent_name like concat('%',#{req.agentName},'%')
            </if>
            <if test="req.uuid != null  and req.uuid !=''">
                and record.uuid = #{req.uuid}
            </if>
            <if test="req.callType != null">
                and record.call_type = #{req.callType}
            </if>
            <if test="req.phone != null and req.phone !=''">
                and record.phone = #{req.phone}
            </if>
            <if test="req.agentDept != null  and req.agentDept !=''">
                and record.agent_dept = #{req.agentDept}
            </if>
            <if test="req.tenantId != null  and req.tenantId !=''">
                and record.tenant_id = #{req.tenantId}
            </if>
            <if test="req.status != null and req.status.size()>0">
                and record.status in
                <foreach item="item" index="index" collection="req.status" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.analysisTime != null">
                and record.analysis_time = #{req.analysisTime}
            </if>
            <if test="req.configSceneIds != null and req.configSceneIds.size()>0 ">
                and record.id in
                (select
                analysis_task_call_record_id
                from ai_analysis_task_result result
                where result.analysis_task_call_record_id = record.id
                and result.status =2
                and result.scene_id in
                <foreach item="item" collection="req.configSceneIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                and result.scene_result_id >0
                )

            </if>
            <if test="req.configSceneResultIds != null and req.configSceneResultIds.size()>0 ">
                and record.id in
                (select
                analysis_task_call_record_id
                from ai_analysis_task_result result
                where result.analysis_task_call_record_id = record.id
                and result.scene_result_id in
                <foreach item="item" collection="req.configSceneResultIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                )
            </if>
            and record.is_deleted = 0
        </where>
    </select>

    <!-- 客户分析导出查询 -->
    <select id="pageListForCustomerExport" resultMap="AiAnalysisTaskCallRecordExcelMap">
        select
            data.id,
            data.customer_id as customer_uid,
            data.customer_name,
            data.status,
            data.result_name as result,
            data.result_des as result_desc,
            data.analysis_task_id
        from ai_analysis_task_data data
        <where>
            <if test="req.analysisTaskId != null">
                and data.analysis_task_id = #{req.analysisTaskId}
            </if>
            <if test="req.customerName != null and req.customerName != ''">
                and data.customer_name = #{req.customerName}
            </if>
            <if test="req.customerId != null and req.customerId != ''">
                and data.customer_id = #{req.customerId}
            </if>
            <if test="req.status != null and req.status.size() > 0">
                and data.status in
                <foreach item="item" index="index" collection="req.status" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.configSceneIds != null and req.configSceneIds.size() > 0">
                and data.scene_id in
                <foreach item="item" collection="req.configSceneIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and data.is_deleted = 0
        </where>
        order by data.create_time desc
    </select>


    <select id="listByCallRecordIdAndConditions"
            resultType="com.yirendai.taskcenter.model.AiAnalysisTaskResultVO">
        select id,
        analysis_task_id analysisTaskId,
        analysis_task_call_record_id analysisTaskCallRecordId ,
        chat_contact_id chatContactId,
        scene_name sceneName,
        scene_id sceneId,
        result_name resultName,
        scene_result_id sceneResultId,
        tenant_id tenantId,
        create_user createUser,
        create_user_name createUserName,
        create_time createTime,
        update_user updateUser,
        update_time updateTime,
        status,
        analysis_time analysisTime,
        is_deleted isDeleted,
        final_status finalStatus,
        result_des resultDes,
        data_source_types dataSourceTypes,
        match_reasons matchReasons,
        msg_times msgTimes,
        sub_task_id subTaskId
        from ai_analysis_task_result
        where analysis_task_call_record_id = #{callRecordId}
        and status = 2
        and is_deleted = 0
        <if test="sceneIds != null and sceneIds.size() > 0">
            <foreach item="item" index="index" collection="sceneIds" open="and scene_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="resultIds != null and resultIds.size() > 0">
            <foreach item="item" index="index" collection="resultIds" open="and scene_result_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
