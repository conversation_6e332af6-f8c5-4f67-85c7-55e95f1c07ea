package com.yirendai.taskcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户注册信息表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_registry_info")
public class UserRegistryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id，唯一
     */
    private Long userId;

    /**
     * 登录名称(邮箱或手机号)，唯一
     */
    private String loginName;

    /**
     * 密码
     */
    private String passWord;

    /**
     * salt，暂不用
     */
    private String salt;

    /**
     * 注册手机号，每个用户在系统中只能有一个手机号！
     */
    private String mobileNo;

    /**
     * 注册邮箱。不支持邮箱注册，为了兼容旧系统，可登录
     */
    private String email;

    /**
     * 邮箱验证状态 0 发送失败 1发送成功 2 验证成功
     */
    private String emailVerifyState;

    /**
     * 邮箱激活时间
     */
    private LocalDateTime emailVerifyDate;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像url
     */
    private String avatarUrl;

    /**
     * 注册来源
     */
    private String sourceType;

    /**
     * 注册子渠道，由渠道自定义
     */
    private String subSourceType;

    /**
     * 注册ip
     */
    private String ip;

    /**
     * 用户状态 0已注销 1已激活
     */
    private String userStatus;

    /**
     * user_flag用户标志 0 借款 1 出借
     */
    private String userFlag;

    /**
     * 角色类型  0 普通用户 1企业用户 2平台用户
     */
    private String roleType;

    /**
     * 最新更新时间,初始同创建时间，更新记录时修改,迁移数据存在空字段
     */
    private LocalDateTime lastUpdateDate;

    /**
     * 注册时间,迁移数据存在空字段
     */
    private LocalDateTime createDate;

    /**
     * 备注
     */
    private String comments;

    /**
     * 最近修改密码时间
     */
    private LocalDate lastPasswordUpdateDate;
}