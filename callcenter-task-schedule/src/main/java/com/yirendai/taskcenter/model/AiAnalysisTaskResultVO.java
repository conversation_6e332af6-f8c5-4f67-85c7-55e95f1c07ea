package com.yirendai.taskcenter.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * ai分析任务通话记录分析结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AiAnalysisTaskResult对象", description = "ai分析任务通话记录分析结果表")
public class AiAnalysisTaskResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "ai分析任务Id ai_analysis_task表ID")
    private Long analysisTaskId;

    @ApiModelProperty(value = "ai分析任务数据母表ID ai_analysis_task_data表ID")
    private Long analysisTaskDataId;

    @ApiModelProperty(value = "通话记录ID ai_analysis_task_call_record 表ID")
    private Long analysisTaskCallRecordId;

    @ApiModelProperty(value = "用户沟通会话ID ai_planner_chat_contact 表ID")
    private Long chatContactId;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "场景表ID ai_analysis_config_scene表ID")
    private Long sceneId;

    @ApiModelProperty(value = "分析结果")
    private String resultName;

    @ApiModelProperty(value = "分析结果描述")
    private String resultDes;

    @ApiModelProperty(value = "场景结果项表ID  ai_analysis_config_scene_result表ID")
    private Long sceneResultId;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "AI分析状态 0未开始，1进行中，2已完成,3失败")
    private Integer status;

    @ApiModelProperty(value = "AI分析完成时间")
    private LocalDateTime analysisTime;

    @ApiModelProperty(value = "是否已删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "完成状态 0未完成，1完成")
    private Integer finalStatus;

    @ApiModelProperty(value = "数据源类型，多个用逗号分隔：1通话记录，2短信记录，3微信记录，4企微记录，5客户基本信息，6客户业务数据，7客户订单数据")
    private String dataSourceTypes;

    @ApiModelProperty(value = "匹配原因，多个用分号分隔")
    private String matchReasons;

    @ApiModelProperty(value = "消息时间，多个用逗号分隔")
    private String msgTimes;

    @ApiModelProperty(value = "子任务ID")
    private Long subTaskId;


}
