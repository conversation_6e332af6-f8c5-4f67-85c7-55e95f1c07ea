package com.yirendai.taskcenter.service.impl;


import cn.hutool.core.util.PhoneUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yirendai.taskcenter.annotation.TaskJob;
import com.yirendai.taskcenter.constant.enums.TaskStatusEnum;
import com.yirendai.taskcenter.entity.CallCenterTask;
import com.yirendai.taskcenter.entity.RobotCallTasksCustomer;
import com.yirendai.taskcenter.mapper.crm.CallcenterForbiddenMapper;
import com.yirendai.taskcenter.mapper.crm.RobotCallTasksCustomerMapper;
import com.yirendai.taskcenter.mapper.owmuc.UserRegistryInfoMapper;
import com.yirendai.workbench.enums.ImportTypeEnum;
import com.yirendai.workbench.model.callcenter.BatchImportResult;
import com.yirendai.workbench.model.callcenter.ImportRobotTaskCustomerParam;
import com.yirendai.workbench.model.callcenter.RobotAttributeVariableDto;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.importExcel.easyExcel.DynamicImportResult;
import com.yirendai.workbench.service.importExcel.easyExcel.ImportExcelHelper;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.vo.req.QuerUserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springblade.core.tool.api.R;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

import static com.yirendai.workbench.constant.TaskScheduleConstants.AI_TASK_CUSTOMER_IMPORT_TASK;

@Component
@TaskJob(value = AI_TASK_CUSTOMER_IMPORT_TASK)
@Slf4j
public class RobotCustomerImportTaskHandler extends AbstractImportTaskProcessHandler {

    @Resource
    private RobotCallTasksCustomerMapper robotCallTasksCustomerMapper;

    @Resource
    private CallcenterForbiddenMapper callcenterForbiddenMapper;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private UserRegistryInfoMapper userRegistryInfoMapper;

    private static final String callUserNameKey = "callUserName";
    @Override
    public void run(CallCenterTask task) {
        File file = new File(task.getFilePath());
        ImportRobotTaskCustomerParam importRobotTaskCustomerParam = getParam(task.getParam());
        BatchImportResult batchImportResult =  batchImport(file,task,importRobotTaskCustomerParam);

        task.setTaskStatus(TaskStatusEnum.SUCCESS.getCode());
        task.setCompletedTime(LocalDateTime.now());
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(batchImportResult.getCustomerHasError())) {
            generateErrorFile(batchImportResult.getCustomerHasError(), batchImportResult.getHeaders(), task);
        }
        task.setExecutionResult("正确：" + (batchImportResult.getTotalRecords() - batchImportResult.getCustomerHasError().size())
                + "条，错误：" + batchImportResult.getCustomerHasError().size() + "条");
    }
    /**
     * 批量导入AI 外呼任务客户（优化版）
     */
    public BatchImportResult batchImport(File file,CallCenterTask task,ImportRobotTaskCustomerParam importRobotTaskCustomerParam){
        log.info("开始批量导入客户（优化版）, file:{}, task:{}, importRobotTaskCustomerParam:{}", file.getAbsolutePath(), task, importRobotTaskCustomerParam);
        Integer importType = importRobotTaskCustomerParam.getImportType();
        Long taskId = importRobotTaskCustomerParam.getTaskId();
        String tenantId = task.getTenantId();
        String userName = task.getCreateUser();
        LocalDateTime createdAt = LocalDateTime.now();

        // 1. 解析excel
        DynamicImportResult importList = ImportExcelHelper.getImportResult(file, 0, 1, false, null);
        if (CollectionUtils.isEmpty(importList.getData())){
            log.info("导入数据解析结果为空");
            return new BatchImportResult(0, null, Collections.emptyList());
        }

        // 2. 查询禁呼列表
        List<String> forbiddenPhoneList = callcenterForbiddenMapper.robotPhoneListByTenantId(tenantId);
        List<String> forbiddenUserIdList = callcenterForbiddenMapper.robotUserIdListByTenantId(tenantId);
        java.util.Set<String> forbiddenPhoneSet = new java.util.HashSet<>(forbiddenPhoneList);
        java.util.Set<String> forbiddenUserIdSet = new java.util.HashSet<>(forbiddenUserIdList);

        List<RobotAttributeVariableDto> variableList = importRobotTaskCustomerParam.getVariableList();
        List<LinkedHashMap<Integer, String>> customerHasError = new ArrayList<>();
        int headerSize = importList.getHeaders().size();

        // 3. 【优化1】如果是userId导入，批量查询用户信息
        java.util.Map<String, SimpleUserInfoDto> userInfoMap = new java.util.HashMap<>();
        if (importType == ImportTypeEnum.USER_ID.getCode()) {
            List<String> userIdList = new ArrayList<>();
            for (LinkedHashMap<Integer, String> importData : importList.getData()) {
                if (StringUtils.isNotBlank(importData.get(0))) {
                    userIdList.add(importData.get(0));
                }
            }
            log.info("批量查询用户信息，数量={}", userIdList.size());
            userInfoMap = batchQueryUserInfo(userIdList);
            log.info("批量查询用户信息完成，返回数量={}", userInfoMap.size());
        }

        // 4. 【优化2】收集所有号码，批量查询已存在的号码
        List<String> allPhones = new ArrayList<>();
        if (importType == ImportTypeEnum.USER_ID.getCode()) {
            // userId导入时，从userInfoMap获取手机号
            for (SimpleUserInfoDto userInfo : userInfoMap.values()) {
                if (StringUtils.isNotBlank(userInfo.getMobileNo())) {
                    allPhones.add(userInfo.getMobileNo());
                }
            }
        } else {
            // 手机号导入时，直接从Excel获取
            for (LinkedHashMap<Integer, String> importData : importList.getData()) {
                if (StringUtils.isNotBlank(importData.get(0))) {
                    allPhones.add(importData.get(0));
                }
            }
        }

        log.info("批量查询已存在号码，数量={}", allPhones.size());
        java.util.Set<String> existingPhoneSet = new java.util.HashSet<>();
        // 分批查询已存在的号码（每批200条）
        int batchSize = 200;
        for (int i = 0; i < allPhones.size(); i += batchSize) {
            int toIndex = Math.min(i + batchSize, allPhones.size());
            List<String> batch = allPhones.subList(i, toIndex);
            List<String> existingBatch = robotCallTasksCustomerMapper.selectExistingPhones(taskId, batch);
            existingPhoneSet.addAll(existingBatch);
        }
        log.info("批量查询已存在号码完成，已存在数量={}", existingPhoneSet.size());

        // 5. 遍历处理每条数据，收集待插入的记录
        List<RobotCallTasksCustomer> batchInsertList = new ArrayList<>();
        int count = 0;

        for (LinkedHashMap<Integer, String> importData : importList.getData()) {
            count++;

            if(StringUtils.isBlank(importData.get(0))){
                log.info("第{}行被叫号码不能为空", count);
                this.putError(importData, customerHasError,headerSize, "被叫号码不能为空");
                continue;
            }

            String calledNumber = importData.get(0);
            String callUserName = null;
            String userId = null;
            RobotCallTasksCustomer robotCallTasksCustomer = new RobotCallTasksCustomer();

            if (importType == ImportTypeEnum.USER_ID.getCode()) {
                userId = calledNumber;

                // 检查是否为禁呼用户
                if(forbiddenUserIdSet.contains(userId)){
                    log.info("当前客户ID为禁呼客户 {}", userId);
                    this.putError(importData, customerHasError,headerSize, "当前客户ID为禁呼客户");
                    continue;
                }

                // 从批量查询结果中获取用户信息
                SimpleUserInfoDto response = userInfoMap.get(userId);
                if(response != null && response.getMobileNo() != null){
                    calledNumber = response.getMobileNo();
                    callUserName = response.getUserName();
                    robotCallTasksCustomer.setUserId(userId);
                } else{
                    log.info("用户ID不存在 {}", userId);
                    this.putError(importData, customerHasError,headerSize, "用户ID不存在");
                    continue;
                }
            } else {
                // 手机号格式校验
                if (!PhoneUtil.isMobile(calledNumber)) {
                    log.info("手机号格式错误 {}",calledNumber);
                    this.putError(importData, customerHasError,headerSize, "手机号格式错误");
                    continue;
                }
            }

            // 检查号码是否已存在（使用批量查询结果）
            if (existingPhoneSet.contains(calledNumber)) {
                log.info("导入号码已存在 {}",calledNumber);
                this.putError(importData, customerHasError,headerSize, "导入号码已存在");
                continue;
            }

            // 检查是否为禁呼手机号
            if (forbiddenPhoneSet.contains(calledNumber)) {
                log.info("当前手机号为禁呼手机号 {}",calledNumber);
                this.putError(importData, customerHasError,headerSize, "当前手机号为禁呼手机号");
                continue;
            }

            // 构建变量
            boolean exitCustomerName = org.apache.commons.lang3.StringUtils.isNotBlank(callUserName);
            robotCallTasksCustomer.setCalledNumber(calledNumber);
            StringBuilder value = new StringBuilder();
            for (int j = 0; j < importData.size()&&j < variableList.size(); j++) {
                String key = variableList.get(j ).getVariableKey();
                if (exitCustomerName && key.equals(callUserNameKey)) {
                    continue;
                }
                value.append(key).append(":{");
                value.append(importData.get(j) );
                value.append("}\n");
            }
            // 变量增加用户姓名
            if (exitCustomerName) {
                value.append(callUserNameKey).append(":{");
                value.append(callUserName);
                value.append("}\n");
            }

            robotCallTasksCustomer.setVariables(value.toString());
            robotCallTasksCustomer.setImportedBy(userName);
            robotCallTasksCustomer.setTenantId(tenantId);
            robotCallTasksCustomer.setImportTime(createdAt);
            robotCallTasksCustomer.setTaskId(taskId);

            // 加入批量插入列表
            batchInsertList.add(robotCallTasksCustomer);

            // 每500条批量插入一次
            if (batchInsertList.size() >= 500) {
                log.info("批量插入客户，数量={}", batchInsertList.size());
                robotCallTasksCustomerMapper.insertBatch(batchInsertList);
                batchInsertList.clear();
            }
        }

        // 6. 【优化3】插入剩余数据
        if (!batchInsertList.isEmpty()) {
            log.info("批量插入剩余客户，数量={}", batchInsertList.size());
            robotCallTasksCustomerMapper.insertBatch(batchInsertList);
        }

        log.info("批量导入完成，总记录数={}，成功数={}，失败数={}", count, count - customerHasError.size(), customerHasError.size());
        return new BatchImportResult(count, importList.getHeaders(), customerHasError);
    }

    @Override
    protected List handlerData(List dataList, CallCenterTask task) {
        return Collections.emptyList();
    }

    public void putError(LinkedHashMap<Integer, String> importData, List<LinkedHashMap<Integer, String>> customerHasError,int headerSize,String errorMsg) {
        if(importData.size()<headerSize){
            for (int i = importData.size(); i < headerSize; i++) {
                importData.put(i, "");
            }
        }
        importData.put(headerSize, errorMsg);
        customerHasError.add(importData);
    }

    private static ImportRobotTaskCustomerParam getParam(String taskParam){
        if (StringUtils.isBlank(taskParam)){
            return null;
        }
        ImportRobotTaskCustomerParam param = JsonUtilExt.jsonToBean(taskParam, ImportRobotTaskCustomerParam.class);
        return param;
    }

    private SimpleUserInfoDto callUrl(String url,String userId,String tenantId){
        QuerUserInfoDTO querUserInfoDTO = new QuerUserInfoDTO();
        querUserInfoDTO.setUserId(userId);
        querUserInfoDTO.setTenantId(tenantId);
        // 调用服务接口
        ResponseEntity<String> response = restTemplate.postForEntity(url, querUserInfoDTO, String.class );
        // 处理响应结果
        if (response.getStatusCode().is2xxSuccessful()) {
            String body  = response.getBody();
            R<SimpleUserInfoDto> result = JsonUtilExt.jsonToBean(body, new TypeReference<R<SimpleUserInfoDto>>(){}  );
            if(result.isSuccess()){
                return result.getData();
            }else{
                log.error("查询用户信息失败，用户id为[{}]",userId);
                return null;
            }
        }else{
            log.error("查询用户信息失败，用户id为[{}]",userId);
            return null;
        }

    }

    /**
     * 批量查询用户信息（使用MyBatis-Plus，分批查询，每批最多200条）
     * @param userIdList 用户ID列表
     * @return Map<userId, SimpleUserInfoDto>
     */
    private java.util.Map<String, SimpleUserInfoDto> batchQueryUserInfo(List<String> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }

        log.info("批量查询用户信息，总数量={}", userIdList.size());

        // 转换为Long类型
        List<Long> userIdLongList = new ArrayList<>();
        for (String userId : userIdList) {
            try {
                userIdLongList.add(Long.parseLong(userId));
            } catch (NumberFormatException e) {
                log.warn("用户ID格式错误，跳过: {}", userId);
            }
        }

        if (userIdLongList.isEmpty()) {
            return Collections.emptyMap();
        }

        java.util.Map<String, SimpleUserInfoDto> resultMap = new java.util.HashMap<>();

        // 分批查询，每批最多200条
        int batchSize = 200;
        for (int i = 0; i < userIdLongList.size(); i += batchSize) {
            int toIndex = Math.min(i + batchSize, userIdLongList.size());
            List<Long> batch = userIdLongList.subList(i, toIndex);

            log.info("批量查询用户信息，当前批次：{}/{}", toIndex, userIdLongList.size());

            // 使用MyBatis-Plus批量查询
            com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.yirendai.taskcenter.entity.UserRegistryInfo> wrapper =
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
            wrapper.in(com.yirendai.taskcenter.entity.UserRegistryInfo::getUserId, batch);

            List<com.yirendai.taskcenter.entity.UserRegistryInfo> userList = userRegistryInfoMapper.selectList(wrapper);

            // 转换为Map
            for (com.yirendai.taskcenter.entity.UserRegistryInfo user : userList) {
                SimpleUserInfoDto dto = SimpleUserInfoDto.builder()
                    .userId(String.valueOf(user.getUserId()))
                    .mobileNo(user.getMobileNo())
                    .userName(user.getNickName())
                    .build();
                resultMap.put(String.valueOf(user.getUserId()), dto);
            }
        }

        log.info("批量查询用户信息完成，查询数量={}，返回数量={}", userIdList.size(), resultMap.size());
        return resultMap;
    }

}
