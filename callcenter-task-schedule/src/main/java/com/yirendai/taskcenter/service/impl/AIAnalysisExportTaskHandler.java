package com.yirendai.taskcenter.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.taskcenter.annotation.TaskJob;
import com.yirendai.taskcenter.constant.enums.TaskStatusEnum;
import com.yirendai.taskcenter.constant.exception.BusinessException;
import com.yirendai.taskcenter.entity.CallCenterTask;
import com.yirendai.taskcenter.mapper.crm.AiAnalysisTaskCallRecordMapper;
import com.yirendai.taskcenter.model.AiAnalysisTaskResultVO;
import com.yirendai.taskcenter.model.excel.AiAnalysisTaskCallRecordExcel;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.enums.AnalysisTaskCallRecordStatusEnum;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.util.DesensitizationUtil;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.vo.req.analysis.AiAnalysisTaskCallRecordReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.yirendai.workbench.constant.TaskScheduleConstants.AI_ANALYSIS_EXPORT_TASK;

@Slf4j
@Component
@TaskJob(value = AI_ANALYSIS_EXPORT_TASK)
public class AIAnalysisExportTaskHandler extends AbstractExportTaskProcessHandler{

    @Resource
    private AiAnalysisTaskCallRecordMapper aiAnalysisTaskCallRecordMapper;

    @Resource
    CallcenterProperty exportProperty;
    @Override
    public void run(CallCenterTask task) {
        log.info("任务id={}开始执行", task.getId());
        if (StrUtil.isBlank(task.getParam()) || StrUtil.isBlank(task.getTenantId())) {
            throw new BusinessException(BusinessError.PARAM_INVALID);
        }
        AiAnalysisTaskCallRecordReq req = JsonUtilExt.jsonToBean(task.getParam(), AiAnalysisTaskCallRecordReq.class);
        if (Objects.isNull(req)) {
            throw new BusinessException(BusinessError.PARAM_INVALID);
        }
        String exportPath = export(req, task.getTenantId());
        task.setFilePath(exportPath);
        task.setFileName(FileUtil.getName(exportPath));
        task.setTaskStatus(TaskStatusEnum.SUCCESS.getCode());
        task.setCompletedTime(LocalDateTime.now());
        log.info("任务id={}执行完成", task.getId());
    }

    /**
     * 导出AI 分析任务
     * @param req 请求参数
     * @param tenantId 租户ID
     * @return 导出文件路径
     */
    String export(AiAnalysisTaskCallRecordReq req, String tenantId) {
        log.info("导出AI分析任务req={}开始执行", req);
        
        // 根据recordType判断导出类型
        if (req.getRecordType() != null && req.getRecordType() == 1) {
            // 客户分析导出
            return exportCustomerAnalysis(req, tenantId);
        } else {
            // 通话记录分析导出（原有逻辑）
            return exportCallRecordAnalysis(req, tenantId);
        }
    }
    
    /**
     * 导出客户分析结果
     * @param req 请求参数
     * @param tenantId 租户ID
     * @return 导出文件路径
     */
    private String exportCustomerAnalysis(AiAnalysisTaskCallRecordReq req, String tenantId) {
        log.info("导出客户分析任务req={}开始执行", req);
        
        // 创建 Excel 文件路径
        String exportDir = exportProperty.getExportExcelDir();
        FileUtil.mkdir(exportDir);
        String filePath = exportDir + "/AI客户分析结果_" + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN) + ".xlsx";
        
        ExcelWriter writer = null;
        try {
            writer = ExcelUtil.getWriter(filePath, "客户分析结果");
            
            // 写入表头
            writer.writeHeadRow(Arrays.asList("客户uid", "客户姓名", "AI分析状态", "AI分析结果", "AI分析详情"));
            
            int pageSize = 200;
            int pageNo = 1;
            
            while (true) {
                Page<AiAnalysisTaskCallRecordExcel> page = new Page<>(pageNo, pageSize);
                // 注意：这里需要查询 ai_analysis_task_data 表
                IPage<AiAnalysisTaskCallRecordExcel> resultPage = aiAnalysisTaskCallRecordMapper.pageListForCustomerExport(page, req);

                if (resultPage == null) {
                    break;
                }
                
                List<AiAnalysisTaskCallRecordExcel> records = resultPage.getRecords();
                if (CollUtil.isEmpty(records)) {
                    break;
                }
                
                for (AiAnalysisTaskCallRecordExcel record : records) {
                    // 状态转换
                    AnalysisTaskCallRecordStatusEnum status = AnalysisTaskCallRecordStatusEnum.fromCode(record.getStatus());
                    String statusStr = status != null ? status.getMessage() : "";
                    
                    // 写入一行数据
                    writer.writeRow(Arrays.asList(
                        record.getCustomerUid(),          // 客户uid
                        record.getCustomerName(),         // 客户姓名
                        statusStr,                        // AI分析状态
                        record.getResult(),               // AI分析结果
                        record.getResultDesc()            // AI分析详情
                    ));
                }
                
                pageNo++;
            }
            
            return filePath;
            
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }
    
    /**
     * 导出通话记录分析结果（原有逻辑）
     * @param req 请求参数
     * @param tenantId 租户ID
     * @return 导出文件路径
     */
    private String exportCallRecordAnalysis(AiAnalysisTaskCallRecordReq req, String tenantId) {
        log.info("导出通话记录分析任务req={}开始执行", req);
        int pageSize = 200;
        int pageNo = 1;

        // 用于判断是否已经写入表头
        boolean headerWritten = false;
        ExcelWriter writer = null;

        // 创建 Excel 文件路径
        String exportDir = exportProperty.getExportExcelDir();
        FileUtil.mkdir(exportDir);
        String filePath = exportDir + "/AI会话分析结果_" + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN) + ".xlsx";

        try {
            while (true) {
                Page<AiAnalysisTaskCallRecordExcel> page = new Page<>(pageNo, pageSize);
                IPage<AiAnalysisTaskCallRecordExcel> resultPage = aiAnalysisTaskCallRecordMapper.pageCallRecordList(page, req);

                List<AiAnalysisTaskCallRecordExcel> records = resultPage.getRecords();
                if (CollUtil.isEmpty(records)) {
                    break;
                }

                // 如果是第一次读取数据，创建 ExcelWriter 并写入表头
                if (!headerWritten) {
                    writer = ExcelUtil.getWriter(filePath,"AI分析结果");
                }

                for (AiAnalysisTaskCallRecordExcel excelRecord : records) {
                    //手机号脱敏
                    excelRecord.setPhone(DesensitizationUtil.hidePhone(excelRecord.getPhone(),3,4));
                    Map<String, Object> row = new LinkedHashMap<>();
                    AnalysisTaskCallRecordStatusEnum status = AnalysisTaskCallRecordStatusEnum.fromCode(excelRecord.getStatus());
                    if(status != null){
                        excelRecord.setStatusStr(status.getMessage());
                    }
                    excelRecord.setCallTypeStr(excelRecord.getCallType() == 1 ? "呼入" : "呼出");
                    if(excelRecord.getStartStamp() != null){
                        excelRecord.setStartDateTime(DateUtil.format(excelRecord.getStartStamp(),"yyyy-MM-dd HH:mm:ss"));
                        excelRecord.setStartDate(DateUtil.format(excelRecord.getStartStamp(),"yyyy-MM-dd"));
                    }
                    // 查询分析结果并提取 sceneName / resultName
                    List<AiAnalysisTaskResultVO> results = aiAnalysisTaskCallRecordMapper.listByCallRecordIdAndConditions(
                            excelRecord.getId(), null, null);

                    if (CollUtil.isNotEmpty(results)) {
                        StringBuffer resultName = new StringBuffer();
                        StringBuffer resultDesc = new StringBuffer();
                        results.forEach(r -> {
                            resultName.append(r.getSceneName() + "-");
                            resultName.append(r.getResultName() +",");
                            resultDesc.append(r.getSceneName() + "-");
                            resultDesc.append(r.getResultDes() + "\n");
                        });
                        resultName.deleteCharAt(resultName.length()-1);
                        excelRecord.setResult(resultName.toString());
                        excelRecord.setResultDesc(resultDesc.toString());
                    }

                    // 填充主表字段（使用字段注释作为表头）
                    Arrays.stream(excelRecord.getClass().getDeclaredFields())
                            .forEach(field -> {
                                try {
                                    field.setAccessible(true);
                                    ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
                                    if (annotation != null ) {
                                        //如果表头是音转文结果横向拆分一个单元格最多放 10000字
                                        if( annotation.value()[0].equals("音转文结果")){
                                            String result =   (String) field.get(excelRecord);
                                            if(result ==null ||  result.length() <=10000){
                                                row.put(annotation.value()[0], result);
                                            }else{
                                                //按10000字分一组
                                                int count = result.length() / 10000;
                                                for(int i = 0; i <= count; i++){
                                                    String subResult = result.substring(i * 10000, Math.min((i + 1) * 10000, result.length()));
                                                    row.put(annotation.value()[0]+i, subResult);
                                                }
                                            }

                                        }else{
                                            row.put(annotation.value()[0], field.get(excelRecord));
                                        }

                                    }
                                } catch (IllegalAccessException e) {
                                    log.error("读取字段失败: {}", field.getName(), e);
                                }
                            });



                    // 如果还没写入表头，则现在写入
                    if (!headerWritten) {
                        List<String> headers = new ArrayList<>(row.keySet());
                        writer.writeHeadRow(headers);
                        headerWritten = true;
                    }

                    // 写入当前行数据
                    List<Object> rowData = new ArrayList<>();
                    row.forEach((key, value) -> rowData.add(value));
                    writer.writeRow(rowData);
                }

                pageNo++;
            }

            if (!headerWritten) {
                log.info("无结果数据");
                return null;
            }

        } finally {
            if (writer != null) {
                writer.close();
            }
        }

        return filePath;
    }
}
