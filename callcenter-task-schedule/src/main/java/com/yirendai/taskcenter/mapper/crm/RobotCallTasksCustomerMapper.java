package com.yirendai.taskcenter.mapper.crm;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerQueryReqVO;
import com.yirendai.taskcenter.entity.RobotCallTasksCustomer;
import com.yirendai.taskcenter.model.excel.RobotCallTasksCustomerExcel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外呼通话任务客户号码导入表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
public interface RobotCallTasksCustomerMapper extends BaseMapper<RobotCallTasksCustomer> {

    /**
     * 根据被叫号码查询
     * @param taskId
     * @param calledNumber
     * @return
     */
    Long countByCalledNumber(Long taskId, String calledNumber);


    /**
     * 根据任务客户ID删除
     * @param taskCustomerId
     * @return
     */
    int deleteByIdAndSessionIsNull(@Param("taskCustomerId") Long taskCustomerId);

    /**
     * 根据任务ID查询任务客户
     * @param taskId
     * @param robotConcurrency 获取条数
     * @return
     */
    List<RobotCallTasksCustomer> getCallTasksCustomers(@Param("taskId") Long taskId,@Param("robotConcurrency") Integer robotConcurrency);

    /**
     * 待呼叫总数
     * @param taskId
     * @return
     */
    int selectRobotCount(@Param("taskId") Long taskId);

    /**
     * 拷贝接听失败的用户到另一个任务
     * @param targetTaskId
     * @param sourceTaskId
     * @return
     */
    int copyCustomer(@Param("targetTaskId") Long targetTaskId,@Param("sourceTaskId") Long sourceTaskId,@Param("importedBy") String importedBy);

    Long countByBizUniqueId(Long taskId, String bizUniqueId);

    /**
     * 分页查询
     * @param queryReqVO
     * @param page
     * @return
     */
    IPage<RobotCallTasksCustomerExcel> pageList(@Param("req") RobotCallTasksCustomerQueryReqVO queryReqVO, @Param("page") IPage<RobotCallTasksCustomerExcel> page);

    /**
     * 批量插入（使用MyBatis-Plus的foreach）
     * @param list 客户列表
     * @return 插入条数
     */
    int insertBatch(@Param("list") List<RobotCallTasksCustomer> list);

    /**
     * 批量查询已存在的号码
     * @param taskId 任务ID
     * @param phoneNumbers 号码列表
     * @return 已存在的号码列表
     */
    List<String> selectExistingPhones(@Param("taskId") Long taskId, @Param("phoneNumbers") List<String> phoneNumbers);

}
