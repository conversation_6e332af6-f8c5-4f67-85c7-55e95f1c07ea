package com.yirendai.taskcenter.mapper.crm;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.taskcenter.entity.AiAnalysisTaskCallRecord;
import com.yirendai.taskcenter.model.AiAnalysisTaskResultVO;
import com.yirendai.taskcenter.model.excel.AiAnalysisTaskCallRecordExcel;
import com.yirendai.workbench.vo.req.analysis.AiAnalysisTaskCallRecordReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * ai分析任务通话记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface AiAnalysisTaskCallRecordMapper extends BaseMapper<AiAnalysisTaskCallRecord> {

    /**
     * 分页查询
     * @param page
     * @param req
     * @return
     */
    IPage<AiAnalysisTaskCallRecordExcel> pageCallRecordList(@Param("page") IPage<AiAnalysisTaskCallRecordExcel> page, @Param("req") AiAnalysisTaskCallRecordReq req);
    
    /**
     * 分页查询客户分析导出数据
     * @param page
     * @param req
     * @return
     */
    IPage<AiAnalysisTaskCallRecordExcel> pageListForCustomerExport(@Param("page") IPage<AiAnalysisTaskCallRecordExcel> page, @Param("req") AiAnalysisTaskCallRecordReq req);
    
    /**
     * 根据通话记录ID和可选场景/结果ID筛选分析结果
     */
    List<AiAnalysisTaskResultVO> listByCallRecordIdAndConditions(
            @Param("callRecordId") Long callRecordId,
            @Param("sceneIds") List<Long> sceneIds,
            @Param("resultIds") List<Long> resultIds
    );
}
