# 建联页面配置API文档

## 接口概述
建联页面配置管理接口，提供完整的增删改查功能，所有接口均返回JSON格式数据。

## 基础路径
```
/connectionPageConfig
```

## 接口列表

### 1. 分页查询列表
**接口地址：** `GET /connectionPageConfig/dataGrid`

**请求参数：**
```json
{
    "page": 1,                    // 页码（可选）
    "rows": 10,                   // 每页条数（可选）
    "pageName": "测试页面",         // 页面名称模糊查询（可选）
    "status": 1,                  // 状态：0-禁用，1-启用（可选）
    "bindRelationCheck": 1,       // 是否判断绑定关系（可选）
    "operator": "admin",          // 操作人模糊查询（可选）
    "createTimeStart": "2024-01-01 00:00:00",  // 创建时间开始（可选）
    "createTimeEnd": "2024-12-31 23:59:59"     // 创建时间结束（可选）
}
```

**响应示例：**
```json
{
    "total": 100,
    "rows": [
        {
            "id": 1,
            "pageName": "测试页面",
            "bindRelationCheck": 1,
            "noBindBgUrl": "http://example.com/bg1.jpg",
            "noBindQrTop": 100,
            "noBindQrLeft": 50,
            "tagConfigs": "[{\"tagId\":\"1\",\"tagName\":\"标签1\",\"qrCodeUrl\":\"http://example.com/qr1.jpg\"}]",
            "hasBindBgUrl": "http://example.com/bg2.jpg",
            "hasBindQrTop": 120,
            "hasBindQrLeft": 60,
            "fallbackQrUrl": "http://example.com/fallback.jpg",
            "appJumpUrl": "http://example.com/app",
            "smsJumpUrl": "http://example.com/sms",
            "status": 1,
            "operator": "admin",
            "createTime": "2024-11-10 10:00:00",
            "updateTime": "2024-11-10 10:00:00"
        }
    ]
}
```

### 2. 根据ID查询详情
**接口地址：** `GET /connectionPageConfig/getById`

**请求参数：**
```
id: 1  // 主键ID
```

**响应示例：**
```json
{
    "success": true,
    "msg": "查询成功",
    "obj": {
        "id": 1,
        "pageName": "测试页面",
        "bindRelationCheck": 1,
        // ... 其他字段
    }
}
```

### 3. 根据页面名称查询
**接口地址：** `GET /connectionPageConfig/getByPageName`

**请求参数：**
```
pageName: "测试页面"  // 页面名称
```

**响应示例：**
```json
{
    "success": true,
    "msg": "查询成功",
    "obj": {
        "id": 1,
        "pageName": "测试页面",
        // ... 其他字段
    }
}
```

### 4. 新增配置
**接口地址：** `POST /connectionPageConfig/add`

**请求参数：**
```json
{
    "pageName": "新页面",                    // 必填，页面名称，最大100字符
    "bindRelationCheck": 1,                 // 必填，是否判断绑定关系：0-否，1-是
    "noBindBgUrl": "http://example.com/bg1.jpg",
    "noBindQrTop": 100,
    "noBindQrLeft": 50,
    "tagConfigs": "[{\"tagId\":\"1\",\"tagName\":\"标签1\",\"qrCodeUrl\":\"http://example.com/qr1.jpg\"}]",
    "hasBindBgUrl": "http://example.com/bg2.jpg",
    "hasBindQrTop": 120,
    "hasBindQrLeft": 60,
    "fallbackQrUrl": "http://example.com/fallback.jpg",
    "appJumpUrl": "http://example.com/app",
    "smsJumpUrl": "http://example.com/sms",
    "status": 1                             // 可选，状态：0-禁用，1-启用，默认1
}
```

**响应示例：**
```json
{
    "success": true,
    "msg": "操作成功"
}
```

### 5. 编辑配置
**接口地址：** `POST /connectionPageConfig/edit`

**请求参数：**
```json
{
    "id": 1,                                // 必填，主键ID
    "pageName": "修改后的页面名称",           // 可选，页面名称，最大100字符
    "bindRelationCheck": 0,                 // 可选，是否判断绑定关系
    // ... 其他可选字段
}
```

**响应示例：**
```json
{
    "success": true,
    "msg": "操作成功"
}
```

### 6. 删除配置
**接口地址：** `POST /connectionPageConfig/delete`

**请求参数：**
```
id: 1  // 主键ID
```

**响应示例：**
```json
{
    "success": true,
    "msg": "操作成功"
}
```

### 7. 批量删除
**接口地址：** `POST /connectionPageConfig/deleteBatch`

**请求参数：**
```
ids: "1,2,3"  // 主键ID列表，逗号分隔
```

**响应示例：**
```json
{
    "success": true,
    "msg": "操作成功"
}
```

### 8. 启用配置
**接口地址：** `POST /connectionPageConfig/enable`

**请求参数：**
```
id: 1  // 主键ID
```

**响应示例：**
```json
{
    "success": true,
    "msg": "操作成功"
}
```

### 9. 禁用配置
**接口地址：** `POST /connectionPageConfig/disable`

**请求参数：**
```
id: 1  // 主键ID
```

**响应示例：**
```json
{
    "success": true,
    "msg": "操作成功"
}
```

### 10. 批量启用
**接口地址：** `POST /connectionPageConfig/enableBatch`

**请求参数：**
```
ids: "1,2,3"  // 主键ID列表，逗号分隔
```

**响应示例：**
```json
{
    "success": true,
    "msg": "操作成功"
}
```

### 11. 批量禁用
**接口地址：** `POST /connectionPageConfig/disableBatch`

**请求参数：**
```
ids: "1,2,3"  // 主键ID列表，逗号分隔
```

**响应示例：**
```json
{
    "success": true,
    "msg": "操作成功"
}
```

### 12. 检查页面名称是否存在
**接口地址：** `GET /connectionPageConfig/checkPageNameExists`

**请求参数：**
```
pageName: "测试页面"     // 页面名称
excludeId: 1           // 排除的ID（编辑时使用，可选）
```

**响应示例：**
```json
{
    "success": true,
    "msg": "检查完成",
    "obj": false          // true-存在，false-不存在
}
```

## 错误响应格式
```json
{
    "success": false,
    "msg": "错误信息描述"
}
```

## 字段说明

### 状态字段
- `status`: 0-禁用，1-启用
- `bindRelationCheck`: 0-否，1-是

### 标签配置JSON格式
```json
[
    {
        "tagId": "标签ID",
        "tagName": "标签名称", 
        "qrCodeUrl": "二维码链接"
    }
]
```

## 注意事项
1. 所有接口都返回JSON格式数据
2. 页面名称具有唯一性约束
3. 页面名称最大长度为100字符
4. 创建时间和更新时间由系统自动维护
5. 操作人信息会自动记录当前用户
