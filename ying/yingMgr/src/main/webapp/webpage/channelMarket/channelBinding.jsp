<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<script type="text/javascript">
    var channelBindingGrid;
    var targetDataGrid;

    // 初始化页面
    $(function() {
        initTargetDataGrid();
        initChannelBindingGrid();
        
        // 处理预选的背景图ID
        var bgPicIds = getUrlParameter('bgPicIds');
        if (bgPicIds) {
            preSelectBackgroundPics(bgPicIds);
        }
    });

    // 获取URL参数
    function getUrlParameter(name) {
        var urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // 预选背景图
    function preSelectBackgroundPics(bgPicIds) {
        if (!bgPicIds) return;
        
        var ids = bgPicIds.split(',');
        // 等待数据加载完成后选中
        setTimeout(function() {
            targetDataGrid.datagrid('uncheckAll');
            targetDataGrid.datagrid('load', function() {
                ids.forEach(function(id) {
                    var row = targetDataGrid.datagrid('getRowByIndex', parseInt(id) - 1);
                    if (row) {
                        targetDataGrid.datagrid('checkRow', targetDataGrid.datagrid('getRowIndex', row));
                    }
                });
            });
        }, 500);
    }

    // 初始化目标数据表格（使用背景图数据）
    function initTargetDataGrid() {
        targetDataGrid = $('#targetDataTable').datagrid({
            url: '${pageContext.request.contextPath}/card/bgDataGrid.action',
            fit: true,
            fitColumns: false,
            border: false,
            pagination: true,
            idField: 'id',
            pageSize: 10,
            pageList: [10, 20, 30, 40, 50],
            sortName: 'id',
            sortOrder: 'desc',
            checkOnSelect: true,
            selectOnCheck: true,
            nowrap: true,
            striped: true,
            rownumbers: true,
            columns: [[
                {
                    field: 'ck',
                    checkbox: true,
                    hidden: false
                },
                {
                    field: 'id',
                    title: 'ID',
                    width: 80,
                    hidden: false
                },
                {
                    field: 'name',
                    title: '背景图名称',
                    width: 150,
                    hidden: false
                },
                {
                    field: 'picUrl',
                    title: '图片URL',
                    width: 200,
                    hidden: false,
                    formatter: function(value, row, index) {
                        if (value && value.length > 30) {
                            return '<span title="' + value + '">' + value.substring(0, 30) + '...</span>';
                        }
                        return value || '';
                    }
                },
                {
                    field: 'picThumbUrl',
                    title: '缩略图',
                    width: 100,
                    hidden: false,
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<img src="' + value + '" style="width:50px;height:30px;" title="' + row.name + '"/>';
                        }
                        return '';
                    }
                },
                {
                    field: 'useFor',
                    title: '用途',
                    width: 100,
                    hidden: false
                },
                {
                    field: 'onlineTime',
                    title: '上线时间',
                    width: 150,
                    hidden: false
                },
                {
                    field: 'offlineTime',
                    title: '下线时间',
                    width: 150,
                    hidden: false
                }
            ]]
        });
    }

    // 初始化渠道绑定表格
    function initChannelBindingGrid() {
        channelBindingGrid = $('#channelBindingTable').datagrid({
            url: '${pageContext.request.contextPath}/channelBinding/getChannelList.action',
            fit: true,
            fitColumns: false,
            border: false,
            pagination: true,
            idField: 'id',
            pageSize: 10,
            pageList: [10, 20, 30, 40, 50],
            sortName: 'id',
            sortOrder: 'desc',
            checkOnSelect: true,
            selectOnCheck: true,
            nowrap: true,
            striped: true,
            rownumbers: true,
            columns: [[
                {
                    field: 'ck',
                    checkbox: true,
                    hidden: false
                },
                {
                    field: 'id',
                    title: 'ID',
                    width: 80,
                    hidden: false
                },
                {
                    field: 'channelName',
                    title: '渠道名称',
                    width: 150,
                    hidden: false
                },
                {
                    field: 'channelParam',
                    title: '渠道参数',
                    width: 120,
                    hidden: false
                },
                {
                    field: 'marketId',
                    title: '市场包号',
                    width: 120,
                    hidden: false
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    width: 150,
                    hidden: false
                }
            ]]
        });
    }

    // 搜索目标数据（背景图）
    function searchTargetData() {
        var cardName = $('#targetName').val();
        var useFor = $('#targetType').val();
        var onlineStatus = $('#targetStatus').val();
        
        targetDataGrid.datagrid('load', {
            cardName: cardName,
            useFor: useFor,
            onlineStatus: onlineStatus
        });
    }

    // 搜索渠道数据
    function searchChannelData() {
        var channelName = $('#channelName').val();
        
        channelBindingGrid.datagrid('load', {
            channelName: channelName
        });
    }

    // 打开渠道绑定对话框
    function openChannelBindingDialog() {
        var selRows = targetDataGrid.datagrid('getChecked');
        if (selRows == null || selRows.length == 0) {
            $.messager.alert('提示', '请先选择要绑定的背景图！', 'warning');
            return;
        }

        // 显示已选择的目标数据
        var selectedNames = [];
        selRows.forEach(function(row) {
            selectedNames.push(row.name);
        });
        $('#selectedTargetNames').text('已选择背景图: ' + selectedNames.join(', '));

        $("#channelBindingDialog").dialog({
            autoOpen: false,
            modal: true,
            title: "背景图渠道绑定",
            width: 800,
            height: 500,
            onClose: function () {
                $('#channelName').val("");
                $('#channelType').val("");
                $('#channelStatus').val("");
                if (channelBindingGrid != undefined) {
                    channelBindingGrid.datagrid('clearSelections');
                }
            }
        }).dialog("open");
    }

    // 执行渠道绑定
    function executeChannelBinding() {
        var targetIds = [];
        var channelIds = [];
        
        // 获取选中的目标数据
        var selTargetRows = targetDataGrid.datagrid('getChecked');
        selTargetRows.forEach(function (e) {
            targetIds.push(e.id);
        });

        // 获取选中的渠道数据
        var selChannelRows = channelBindingGrid.datagrid('getChecked');
        if (selChannelRows == null || selChannelRows.length == 0) {
            $.messager.show({
                title: '提示',
                msg: "未选择渠道，将取消所有绑定关系"
            });
        } else {
            selChannelRows.forEach(function (e) {
                channelIds.push(e.id);
            });
        }

        var params = {
            targetIds: targetIds.join(";"),
            channelIds: channelIds.join(";"),
            bindingType: 'BACKGROUND_PIC' // 背景图绑定类型
        };

        $.ajax({
            url: '${pageContext.request.contextPath}/channelBinding/bind.action',
            type: "post",
            dataType: 'json',
            data: params,
            async: false,
            success: function (json) {
                if (!json.success) {
                    $.messager.alert("操作提示", json.msg, "error");
                    return false;
                } else {
                    $.messager.show({
                        title: '提示',
                        msg: "绑定成功"
                    });
                    $('#channelBindingDialog').dialog('close');
                    searchTargetData();
                    return true;
                }
            },
            error: function() {
                $.messager.alert("操作提示", "绑定失败，请重试", "error");
            }
        });
    }

    // 查看绑定关系
    function viewBindingRelations() {
        var selRows = targetDataGrid.datagrid('getChecked');
        if (selRows == null || selRows.length == 0) {
            $.messager.alert('提示', '请先选择要查看的背景图！', 'warning');
            return;
        }

        var targetId = selRows[0].id;
        $('#bindingRelationDialog').dialog({
            autoOpen: false,
            modal: true,
            title: "背景图绑定关系查看",
            width: 600,
            height: 400,
            onOpen: function() {
                loadBindingRelations(targetId);
            }
        }).dialog("open");
    }

    // 加载绑定关系
    function loadBindingRelations(targetId) {
        $('#bindingRelationTable').datagrid({
            url: '${pageContext.request.contextPath}/channelBinding/getBindingRelations.action?targetId=' + targetId,
            fit: true,
            fitColumns: false,
            border: false,
            pagination: true,
            idField: 'id',
            pageSize: 10,
            columns: [[
                {
                    field: 'id',
                    title: 'ID',
                    width: 80
                },
                {
                    field: 'channelName',
                    title: '渠道名称',
                    width: 150
                },
                {
                    field: 'channelCode',
                    title: '渠道编码',
                    width: 120
                },
                {
                    field: 'bindingType',
                    title: '绑定类型',
                    width: 100
                },
                {
                    field: 'createTime',
                    title: '绑定时间',
                    width: 150
                },
                {
                    field: 'action',
                    title: '操作',
                    width: 100,
                    formatter: function(value, row, index) {
                        return '<a href="javascript:void(0)" onclick="unbindChannel(' + row.id + ')">解绑</a>';
                    }
                }
            ]]
        });
    }

    // 解绑渠道
    function unbindChannel(bindingId) {
        $.messager.confirm('确认', '确定要解绑该渠道吗？', function(r) {
            if (r) {
                $.ajax({
                    url: '${pageContext.request.contextPath}/channelBinding/unbind.action',
                    type: "post",
                    dataType: 'json',
                    data: { bindingId: bindingId },
                    success: function (json) {
                        if (json.success) {
                            $.messager.show({
                                title: '提示',
                                msg: "解绑成功"
                            });
                            loadBindingRelations($('#targetDataTable').datagrid('getChecked')[0].id);
                        } else {
                            $.messager.alert("操作提示", json.msg, "error");
                        }
                    }
                });
            }
        });
    }

    // 新增目标数据
    function addTargetData() {
        $('#targetDataForm').form('clear');
        $('#targetDataDialog').dialog({
            autoOpen: false,
            modal: true,
            title: "新增目标数据",
            width: 500,
            height: 400,
            onClose: function() {
                $('#targetDataForm').form('clear');
            }
        }).dialog("open");
    }

    // 保存目标数据
    function saveTargetData() {
        $('#targetDataForm').form('submit', {
            url: '${pageContext.request.contextPath}/channelBinding/saveTargetData.action',
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(data) {
                var json = eval('(' + data + ')');
                if (json.success) {
                    $.messager.show({
                        title: '提示',
                        msg: "保存成功"
                    });
                    $('#targetDataDialog').dialog('close');
                    searchTargetData();
                } else {
                    $.messager.alert("操作提示", json.msg, "error");
                }
            }
        });
    }

    // 修改目标数据
    function editTargetData() {
        var selRows = targetDataGrid.datagrid('getChecked');
        if (selRows == null || selRows.length == 0) {
            $.messager.alert('提示', '请先选择要修改的数据！', 'warning');
            return;
        }
        if (selRows.length > 1) {
            $.messager.alert('提示', '只能选择一条数据进行修改！', 'warning');
            return;
        }

        var row = selRows[0];
        $('#targetDataForm').form('load', row);
        $('#targetDataDialog').dialog({
            autoOpen: false,
            modal: true,
            title: "修改目标数据",
            width: 500,
            height: 400
        }).dialog("open");
    }

    // 删除目标数据
    function deleteTargetData() {
        var selRows = targetDataGrid.datagrid('getChecked');
        if (selRows == null || selRows.length == 0) {
            $.messager.alert('提示', '请先选择要删除的数据！', 'warning');
            return;
        }

        $.messager.confirm('确认', '确定要删除选中的数据吗？', function(r) {
            if (r) {
                var ids = [];
                selRows.forEach(function(row) {
                    ids.push(row.id);
                });

                $.ajax({
                    url: '${pageContext.request.contextPath}/channelBinding/deleteTargetData.action',
                    type: "post",
                    dataType: 'json',
                    data: { ids: ids.join(";") },
                    success: function (json) {
                        if (json.success) {
                            $.messager.show({
                                title: '提示',
                                msg: "删除成功"
                            });
                            searchTargetData();
                        } else {
                            $.messager.alert("操作提示", json.msg, "error");
                        }
                    }
                });
            }
        });
    }

    // 打开背景图管理页面
    function openBgManager() {
        window.open('${pageContext.request.contextPath}/card/bgmanager.action', '_blank');
    }
</script>

<!-- 主页面 -->
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'north',split:false" style="height:80px;padding:10px">
        <form id="searchForm">
            <table>
                <tr>
                    <td>背景图名称:</td>
                    <td><input id="targetName" name="targetName" class="easyui-textbox" style="width:150px"/></td>
                    <td>用途:</td>
                    <td>
                        <input id="targetType" name="targetType" class="easyui-combobox" style="width:120px" data-options="
                            required:false,
                            editable:true,
                            valueField: 'code',
                            textField: 'description',
                            url:'${pageContext.request.contextPath}/card/getBgManagerUseFors.action'"/>
                    </td>
                    <td>上线状态:</td>
                    <td>
                        <select id="targetStatus" name="targetStatus" class="easyui-combobox" style="width:120px" data-options="editable:false">
                            <option value="">全部</option>
                            <option value="1">在线</option>
                            <option value="2">已下线</option>
                            <option value="0">未上线</option>
                        </select>
                    </td>
                    <td>
                        <a href="javascript:void(0);" class="easyui-linkbutton" onclick="searchTargetData()">查询</a>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    
    <div data-options="region:'center'">
        <div id="toolbar" style="padding:5px;">
            <a href="javascript:void(0);" class="easyui-linkbutton" onclick="openBgManager()">背景图管理</a>
            <a href="javascript:void(0);" class="easyui-linkbutton" onclick="openChannelBindingDialog()">渠道绑定</a>
            <a href="javascript:void(0);" class="easyui-linkbutton" onclick="viewBindingRelations()">查看绑定关系</a>
        </div>
        
        <table id="targetDataTable" class="easyui-datagrid" data-options="toolbar:'#toolbar'"></table>
    </div>
</div>

<!-- 渠道绑定对话框 -->
<div id="channelBindingDialog" class="easyui-dialog" style="width:800px;height:500px;padding:10px 20px" closed="true">
    <div style="margin-bottom:10px;">
        <span id="selectedTargetNames" style="font-weight:bold;color:#0066CC;"></span>
    </div>
    
    <div style="margin-bottom:10px;">
        <form id="channelSearchForm">
            <table>
                <tr>
                    <td>渠道名称:</td>
                    <td><input id="channelName" name="channelName" class="easyui-textbox" style="width:150px"/></td>
                    <td>
                        <a href="javascript:void(0);" class="easyui-linkbutton" onclick="searchChannelData()">查询</a>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    
    <table id="channelBindingTable" class="easyui-datagrid"></table>
    
    <div style="text-align:center;margin-top:10px;">
        <a href="javascript:void(0);" class="easyui-linkbutton" onclick="executeChannelBinding()">确定绑定</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" onclick="$('#channelBindingDialog').dialog('close')">取消</a>
    </div>
</div>

<!-- 绑定关系查看对话框 -->
<div id="bindingRelationDialog" class="easyui-dialog" style="width:600px;height:400px;padding:10px 20px" closed="true">
    <table id="bindingRelationTable" class="easyui-datagrid"></table>
</div>

<!-- 目标数据编辑对话框 -->
<div id="targetDataDialog" class="easyui-dialog" style="width:500px;height:400px;padding:10px 20px" closed="true">
    <form id="targetDataForm" method="post">
        <table class="table table-hover table-condensed">
            <tr>
                <th>名称:</th>
                <td>
                    <input id="name" name="name" class="easyui-validatebox" data-options="required:true,validType:'maxLength[100]'"/>
                </td>
            </tr>
            <tr>
                <th>类型:</th>
                <td>
                    <select id="type" name="type" class="easyui-combobox" data-options="required:true,editable:false">
                        <option value="CARD">卡片</option>
                        <option value="PRODUCT">产品</option>
                        <option value="ACTIVITY">活动</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th>描述:</th>
                <td>
                    <input id="description" name="description" class="easyui-textbox" data-options="multiline:true,height:60,validType:'maxLength[500]'"/>
                </td>
            </tr>
            <tr>
                <th>状态:</th>
                <td>
                    <select id="status" name="status" class="easyui-combobox" data-options="required:true,editable:false">
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </td>
            </tr>
        </table>
    </form>
    
    <div style="text-align:center;margin-top:10px;">
        <a href="javascript:void(0);" class="easyui-linkbutton" onclick="saveTargetData()">保存</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" onclick="$('#targetDataDialog').dialog('close')">取消</a>
    </div>
</div>
