<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<!DOCTYPE html>
<html>
<head>
    <title>大v渠道配置</title>
    <jsp:include page="/inc.jsp"></jsp:include>
<%--    <c:set var="updateUrl" value="/findChannelConfig/updateCardTagConfig.action" scope="request"/>--%>
<%--    <c:set var="updateTagGroupUrl" value="/findChannelConfig/updateCardTagConfig.action" scope="request"/>--%>
<%--    <jsp:include page="../taglib/editUserTag.jsp"></jsp:include>--%>
    <script type="text/javascript" src="${pageContext.request.contextPath}/jslib/yingMgr-common.js"
            charset="utf-8"></script>
    <script type="text/javascript">
        var dataGrid;
        var cardId;
        var myGrayGrid;
        var editStatusRow = undefined;
        $(function () {
            dataGrid = $('#dataGrid').datagrid(
                            {
                                url: '${pageContext.request.contextPath}/findChannelConfig/dataGrid.action',
                                fit: true,
                                fitColumns: false,
                                border: false,
                                pagination: true,
                                idField: 'id',
                                pageSize: 20,
                                pageList: [10, 20, 30, 40, 50],
                                checkOnSelect: true,
                                selectOnCheck: true,
                                nowrap: true,//在一行上显示数据，防止撑宽数据行
                                striped: true,//斑马线
                                rownumbers: true,//显示一个行号列
                                columns: [[
                                    {
                                        field: 'configId',
                                        align: 'center',
                                        title: '配置ID',
                                        hidden: false,
                                        width: 50
                                    },
                                    {
                                        field: 'configName',
                                        title: '位置',
                                        align: 'center',
                                        width: 50
                                    },
                                    {
                                        field: 'configValue',
                                        title: '显示tab名',
                                        align: 'center',
                                        width: 100
                                    },
                                    {
                                        field: 'unselectedIconUrl',
                                        title: '未选中图标',
                                        align: 'center',
                                        width: 200
                                    },
                                    {
                                        field: 'selectedIconUrl',
                                        title: '选中图标',
                                        align: 'center',
                                        width: 200
                                    },
                                    {
                                        field: 'onlineTime',
                                        title: '开始时间',
                                        width: 150,
                                        align: 'center',
                                        formatter : function(value, row, index) {
                                            if(!value){
                                                return '';
                                            }
                                            var timeVal;
                                            if(isNaN(value)){
                                                timeVal=new String(value).substring(0, 19);
                                            }else{
                                                timeVal=new Date(value).format("yyyy-MM-dd hh:mm:ss");
                                            }
                                            return timeVal;
                                        }
                                    },
                                    {
                                        field: 'offlineTime',
                                        title: '结束时间',
                                        width: 150,
                                        align: 'center',
                                        formatter : function(value, row, index) {
                                            if(!value){
                                                return '';
                                            }
                                            var timeVal;
                                            if(isNaN(value)){
                                                timeVal=new String(value).substring(0, 19);
                                            }else{
                                                timeVal=new Date(value).format("yyyy-MM-dd hh:mm:ss");
                                            }
                                            return timeVal;
                                        }
                                    },
                                    {
                                        field: 'updateTime',
                                        title: '更新时间',
                                        width: 150,
                                        align: 'center',
                                        formatter : function(value, row, index) {
                                            if(!value){
                                                return '';
                                            }
                                            var timeVal;
                                            if(isNaN(value)){
                                                timeVal=new String(value).substring(0, 19);
                                            }else{
                                                timeVal=new Date(value).format("yyyy-MM-dd hh:mm:ss");
                                            }
                                            return timeVal;
                                        }
                                    },
                                    {
                                        field: 'openStatus',
                                        title: '状态',
                                        align: 'center',
                                        width: 50,
                                        formatter : function(value, row, index) {
                                            var ret;
                                            if(value === 1){
                                                ret='生效中';
                                            }else{
                                                ret='未生效';
                                            }
                                            return ret;
                                        }
                                    },
                                    {
                                        field: 'action',
                                        title: '操作',
                                        width: 120,
                                        align: 'center',
                                        formatter: function (value, row, index) {
                                            // var e = '<a href="#" onclick="editConfig(\'' + row.configId + '\')">配置</a>';
                                            // var d = '<a href="#" onclick="deleterow(this,\'' + row.configId + '\')">删除</a> ';
                                            // var kq = '  ';
                                            // if (row != undefined && row.parentId == null) {
                                            //     return e + kq + d;
                                            // }

                                            if (row.editing){
                                                var s = '<a href="#" onclick="saverow(this)">保存</a> ';
                                                var c = '<a href="#" onclick="cancelrow(this)">取消</a>';
                                                return s+c;
                                            } else {
                                                var e = '<a href="#" onclick="editConfig(\'' + row.configId + '\')">修改</a>&ensp;';
                                                if(row.openStatus === 1){
                                                    var d = '<a href="#" onclick="closerow(this,\'' + row.configId + '\')">关闭</a>&ensp;';
                                                }else {
                                                    var d = '<a href="#" onclick="openrow(this,\'' + row.configId + '\')">开启</a>&ensp;';
                                                }
                                                if (row != undefined && row.parentId == null) {
                                                    return e + d;
                                                }
                                            }

                                        }
                                    }
                                ]],
                                toolbar: '#toolbar',
                                onLoadSuccess: function () {
                                    $('#searchForm table').show();
                                    parent.$.messager.progress('close');

                                    $(this).datagrid('tooltip');
                                },
                                onRowContextMenu : function(e, rowIndex, rowData) {
                                    e.preventDefault();
                                    //$(this).datagrid('unselectAll');
                                    $(this).datagrid('clearChecked');
                                    $(this).datagrid('clearSelections');
                                    $(this).datagrid('selectRow', rowIndex);
                                    $('#menu').menu('show', {
                                        left : e.pageX,
                                        top : e.pageY
                                    });
                                },

                                onBeforeEdit:function(index,row){
                                    row.editing = true;
                                    updateActions(index);
                                },
                                onAfterEdit:function(rowIndex, rowData, changes){
                                    var idParam=rowData.configId?rowData.configId:'';
                                    var params="configId="+idParam+
                                        "&channelCode="+rowData.channelCode;
                                    $.messager.confirm('确认','确定保存吗？',function(r){
                                        if (r){
                                            $.ajax({
                                                url : "${pageContext.request.contextPath}/findChannelConfig/saveChannelConfigLink.action",
                                                type : "post",
                                                dataType : 'json',
                                                data:params,
                                                async:false,
                                                success : function(json) {
                                                    if(!json.success){
                                                        $.messager.alert('错误', json.msg,'error');
                                                        dataGrid.datagrid('rejectChanges');
                                                        return;
                                                    }else{
                                                        $.messager.show( {
                                                            title : '提示',
                                                            msg : "保存成功！"
                                                        });
                                                    }
                                                }
                                            });
                                        }else{
                                            dataGrid.datagrid('beginEdit',rowIndex);
                                        }
                                    });
                                    rowData.editing = false;
                                    updateActions(rowIndex);
                                    editStatusRow = undefined;
                                },
                                onCancelEdit:function(index,row,value){
                                    row.editing = false;
                                    updateActions(index);
                                }
                            });



            $('#searchForm input').keyup(function (event) {
                if (event.keyCode == '13') {
                    searchFun();
                }
            });
        });

        function addConfig(){
            var myUrl='${pageContext.request.contextPath}/findChannelConfig/add.action';
            parent.$.modalDialog({
                title : '新增渠道配置',
                width : 700,
                height : 800,
                href : myUrl,
                buttons : [ {
                    text : '确定',
                    handler : function() {
                        parent.$.modalDialog.openner_dataGrid = dataGrid;
                        var f = parent.$.modalDialog.handler.find('#form');
                        f.submit();
                    }
                }]
            });
        }

        function editConfig(configId){
            var myUrl = '${pageContext.request.contextPath}/findChannelConfig/edit.action?configId=' + configId;
            parent.$.modalDialog({
                title : '修改配置',
                width : 500,
                height : 400,
                href : myUrl,
                buttons : [ {
                    text : '确定',
                    handler : function() {
                        parent.$.modalDialog.openner_dataGrid = dataGrid;
                        var f = parent.$.modalDialog.handler.find('#form');
                        f.submit();
                    }
                }]
            });
        }

        function searchFun() {
            dataGrid.datagrid('load', $.serializeObject($('#searchForm')));
            dataGrid.datagrid('uncheckAll').datagrid('unselectAll').datagrid('clearSelections').datagrid('clearChecked');
            editStatusRow = undefined;
        }

        function cleanFun() {
            $('#searchForm input').val('');
            dataGrid.datagrid('load', {});
            editStatusRow = undefined;
        }

        function updateActions(index){
            dataGrid.datagrid('updateRow',{
                index: index,
                row:{}
            });
        }

        function getRowIndex(target){
            var tr = $(target).closest('tr.datagrid-row');
            return parseInt(tr.attr('datagrid-row-index'));
        }

        function editrow(target,id){
            if(editStatusRow == undefined){
                dataGrid.datagrid('beginEdit', getRowIndex(target));
                editStatusRow = getRowIndex(target);
            }
        }

        function closerow(target, id) {
            var params = "configId=" + id;
            var url = "/findChannelConfig/close.action"

            $.messager.confirm('确认', '确定关闭吗？', function (r) {
                if (r) {
                    $.ajax({
                        url: "${pageContext.request.contextPath}" + url,
                        type: "post",
                        dataType: 'json',
                        data: params,
                        async: false,
                        success: function (json) {
                            if (!json.success) {
                                $.messager.alert('错误', json.msg, 'error');
                                dataGrid.datagrid('rejectChanges');
                                return;
                            } else {
                                $.messager.show({
                                    title: '提示',
                                    msg: "成功！"
                                });
                                searchFun();
                            }
                        }
                    });
                }
            });
        }
        function openrow(target, id) {
            var params = "configId=" + id;
            var url = "/findChannelConfig/open.action"

            $.messager.confirm('确认', '确定开启吗？', function (r) {
                if (r) {
                    $.ajax({
                        url: "${pageContext.request.contextPath}" + url,
                        type: "post",
                        dataType: 'json',
                        data: params,
                        async: false,
                        success: function (json) {
                            if (!json.success) {
                                $.messager.alert('错误', json.msg, 'error');
                                dataGrid.datagrid('rejectChanges');
                                return;
                            } else {
                                $.messager.show({
                                    title: '提示',
                                    msg: "成功！"
                                });
                                searchFun();
                            }
                        }
                    });
                }
            });
        }

        function saverow(target){
            dataGrid.datagrid('endEdit', getRowIndex(target));
        }

        function cancelrow(target){
            $.messager.confirm('确认','确定取消吗？',function(r){
                if (r){
                    dataGrid.datagrid('cancelEdit', getRowIndex(target));
                    dataGrid.datagrid('rejectChanges');
                    editStatusRow = undefined;
                }
            });
        }

        function getRowIndex(target) {
            var tr = $(target).closest('tr.datagrid-row');
            return parseInt(tr.attr('datagrid-row-index'));
        }
    </script>
</head>
<body>
<div class="easyui-layout" data-options="fit : true,border : false">
    <div data-options="region:'north',title:'查询条件',border:false"
         style="height: 64px; overflow: hidden;padding-top:3px">
        <form id="searchForm">
                配置类型 :<input id="configId" name="configId" class="easyui-combobox span2" data-options="
                     required:false,
                     editable:true,
                     valueField: 'configId',
                     textField: 'configName',
                     url:'${pageContext.request.contextPath}/findChannelConfig/selectConfigList.action' "/>
        </form>
    </div>
    <div data-options="region:'center',border:false">
        <table id="dataGrid"></table>
    </div>
</div>


<div id="toolbar" style="display: none;">
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_go',plain:true" onclick="searchFun();">查 询</a>
</div>
</body>
</html>