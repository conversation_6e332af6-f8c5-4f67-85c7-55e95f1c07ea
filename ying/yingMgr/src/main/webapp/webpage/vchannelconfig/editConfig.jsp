<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/style/card.css" type="text/css" />

<script type="text/javascript">
    $(function () {
        window.setTimeout(function () {parent.$.messager.progress('close');}, 1);

        // 初始化时检查配置名称并控制输入框的显示
        toggleFieldsVisibility();

        // 监听配置名称输入框的变化
        $('#configName').on('input change', function() {
            toggleFieldsVisibility();
        });

        // 控制输入框显示/隐藏的函数
        function toggleFieldsVisibility() {
            var configName = $('#configName').val();
            var h5UrlRow = $('#tabH5Url').closest('tr');
            var selectedIconUrlRow = $('#selectedIconUrl').closest('tr');

            if (configName === 'tab3') {
                // tab3时显示h5链接，隐藏选中图标url
                h5UrlRow.show();
                selectedIconUrlRow.hide();
                // 隐藏时清空选中图标url的值
                $('#selectedIconUrl').val('');
            } else {
                // 非tab3时隐藏h5链接，显示选中图标url
                h5UrlRow.hide();
                selectedIconUrlRow.show();
                // 隐藏时清空h5链接的值
                $('#tabH5Url').val('');
            }
        }

        $('#form').form({
            url: "${pageContext.request.contextPath}/findChannelConfig/updateById.action",
            onSubmit: function (params) {
                parent.$.messager.progress({title: '提示',text: '数据处理中，请稍后....'});
                var isValid = $(this).form('validate');
                if (!isValid) {
                    parent.$.messager.progress('close');
                }
                return isValid;
            },
            success: function (result) {
                parent.$.messager.progress('close');
                result = $.parseJSON(result);
                if (result.success) {
                    // 清除父页面数据表格的全选状态
                    parent.$.modalDialog.openner_dataGrid.datagrid('uncheckAll');
                    parent.$.modalDialog.openner_dataGrid.datagrid('unselectAll');
                    parent.$.modalDialog.openner_dataGrid.datagrid('clearSelections');
                    parent.$.modalDialog.openner_dataGrid.datagrid('clearChecked');

                    parent.$.modalDialog.openner_dataGrid.datagrid('reload');
                    parent.$.modalDialog.handler.dialog('close');
                    parent.$.messager.show({title: '提示',msg: "更新成功!"});
                } else {
                    parent.$.messager.alert('错误', result.msg, 'error');
                }
            }
        });
    });
</script>

<div class="easyui-layout" data-options="fit:true,border:false">
    <div data-options="region:'center',border:false" title="" style="position:relative;">
        <form id="form" method="post">
            <table class="table table-hover table-condensed">
                <tr>
                    <th>配置名称</th>
                    <td> <input id="configName" readonly name="configName" value="${channelConfigPO.configName}" class="easyui-validatebox span2" style="width:200px;" data-options="required:false,editable:true,validType:'maxLength[20]'"/>
                        <input type="hidden" name="configId" value="${channelConfigPO.configId}"/>
                    </td>
                </tr>
                <tr>
                    <th>显示tab名</th>
                    <td> <input id="configValue" name="configValue" placeholder="显示tab名" value="${channelConfigPO.configValue}" class="easyui-validatebox span2" style="width:200px;" data-options="required:false,editable:true,validType:'maxLength[4]'"/>
                    </td>
                </tr>
                <tr>
                    <th>未选中图标url</th>
                    <td> <input id="unselectedIconUrl" name="unselectedIconUrl" placeholder="未选中图标url" value="${channelConfigPO.unselectedIconUrl}" class="easyui-validatebox span2" style="width:200px;" data-options="required:false,editable:true,validType:'maxLength[255]'"/>
                    </td>
                </tr>
                <tr>
                    <th>选中图标url</th>
                    <td> <input id="selectedIconUrl" name="selectedIconUrl" placeholder="选中图标url" value="${channelConfigPO.selectedIconUrl}" class="easyui-validatebox span2" style="width:200px;" data-options="required:false,editable:true,validType:'maxLength[255]'"/>
                    </td>
                </tr>
                <tr>
                    <th>选中后文字颜色</th>
                    <td> <input id="selectedCharColor" name="selectedCharColor" placeholder="选中后文字颜色" value="${channelConfigPO.selectedCharColor}" class="easyui-validatebox span2" style="width:200px;" data-options="required:false,editable:true,validType:'maxLength[255]'"/>
                    </td>
                </tr>
                <tr>
                    <th>生效开始时间</th>
                    <td> <input id="onlineTime" name="onlineTime" placeholder="生效开始时间" value="<fmt:formatDate value="${channelConfigPO.onlineTime}" pattern="yyyy-MM-dd HH:mm:ss" />" class="easyui-datetimebox span2" style="width:200px;" data-options="
							required:true
					"/>
                    </td>
                </tr>
                <tr>
                    <th>生效结束时间</th>
                    <td> <input id="offlineTime" name="offlineTime" placeholder="生效结束时间" value="<fmt:formatDate value="${channelConfigPO.offlineTime}" pattern="yyyy-MM-dd HH:mm:ss" />" class="easyui-datetimebox span2" style="width:200px;" data-options="
							required:true
					"/>
                    </td>
                </tr>
                <tr id="h5UrlRow">
                    <th>h5链接</th>
                    <td> <input id="tabH5Url" name="tabH5Url" placeholder="h5链接" value="${channelConfigPO.tabH5Url}" class="easyui-validatebox span2" style="width:200px;" data-options="required:false,editable:true,validType:'maxLength[255]'"/>
                    </td>
                </tr>
            </table>
        </form>
    </div>
</div>
</body>
</html>