<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <title>热点配置</title>
    <jsp:include page="/inc.jsp"></jsp:include>
    <c:set var="updateUrl" value="/hotspotLiving/updateCardTagConfig.action" scope="request"/>
    <c:set var="updateTagGroupUrl" value="/hotspotLiving/updateCardTagConfig.action" scope="request"/>
    <jsp:include page="../../taglib/editUserTag.jsp"></jsp:include>
    <script type="text/javascript">
        var dataGrid;
        var cardId;
        var myGrayGrid;
        $(function () {
            dataGrid = $('#dataGrid').datagrid(
                {
                    url: '${pageContext.request.contextPath}/hotspotLiving/dataGrid.action',
                    fit: true,
                    fitColumns: false,
                    border: false,
                    pagination: true,
                    idField: 'id',
                    pageSize: 20,
                    pageList: [10, 20, 30, 40, 50],
                    sortName: 'id',
                    sortOrder: 'desc',
                    checkOnSelect: true,
                    selectOnCheck: true,
                    nowrap: true,//在一行上显示数据，防止撑宽数据行
                    striped: true,//斑马线
                    rownumbers: true,//显示一个行号列
                    columns: [[
                        {
                            field: 'ck',
                            checkbox: true,
                            hidden: false
                        },
                        {
                            field: 'id',
                            align: 'center',
                            title: '卡片ID',
                            hidden: false,
                            width: 50
                        },

                        {
                            field: 'tab',
                            title: '发现/直播',
                            align: 'center',
                            width: 80,
                            formatter: function (value, row, index) {
                                if (value == 1) {
                                    return "发现";
                                }
                                if (value == 2) {
                                    return "直播";
                                }
                                if (value == 3) {
                                    return "发现直播";
                                }
                                return "";
                            }
                        },
                        {
                            field: 'iCardType',
                            hidden: true
                        },
                        {
                            field: 'title',
                            title: '卡片标题',
                            align: 'center',
                            width: 120
                        },
                        {
                            field: 'content',
                            title: '内容',
                            align: 'center',
                            width: 120
                        },
                        {
                            field: 'cardTemplateType',
                            title: '卡片模板类型',
                            align: 'center',
                            width: 120,
                            formatter: function (value, row, index) {
                                if (value == 1) {
                                    return "顶部标题卡片";
                                }
                                if (value == 2) {
                                    return "直播卡片";
                                }
                                if (value == 3) {
                                    return "短视频卡片";
                                }
                                if (value == 4) {
                                    return "话题A卡片";
                                }
                                if (value == 5) {
                                    return "话题B卡片";
                                }
                                if (value == 6) {
                                    return "专题卡片";
                                }
                                if (value == 7) {
                                    return "财富专栏卡片";
                                }
                                if (value == 8) {
                                    return "主理人A卡片";
                                }
                                if (value == 9) {
                                    return "主理人B卡片";
                                }
                                if (value == 10) {
                                    return "直播热门栏目卡片";
                                }
                                if (value == 11) {
                                    return "学习圈合集推荐";
                                }
                                return "";
                            }
                        },
                        {
                            field: 'titleImageUrl',
                            title: '标题图片链接',
                            width: 150,
                            align: 'center'
                        },
                        {
                            field: 'titleRedirectUrl',
                            title: '标题跳转url',
                            align: 'center',
                            width: 70
                        },
                        {
                            field: 'more',
                            title: '更多按钮',
                            align: 'center',
                            width: 70
                        },
                        {
                            field: 'url',
                            title: '更多按钮跳转url',
                            align: 'center',
                            width: 70
                        },
                        {
                            field: 'arrowRedirectUrl',
                            title: '向右箭头链接',
                            align: 'center',
                            width: 70
                        },
                        {
                            field: 'hasVedioIcon',
                            title: 'video标签是否展示',
                            align: 'center',
                            width: 70,
                            formatter: function (value, row, index) {
                                return value == 0 ? "<span style='color:green'>不显示</span>" : "<span style='color:red'>显示</span>";
                            }
                        },
                        /*{
                            field: 'hasNewIcon',
                            title: 'new标签是否展示',
                            align: 'center',
                            width: 70,
                            formatter: function (value, row, index) {
                                return value == 0 ? "<span style='color:green'>不显示</span>" : "<span style='color:red'>显示</span>";
                            }
                        },*/
                        {
                            field: 'resourceUrl',
                            title: '背景url',
                            align: 'center',
                            width: 70
                        },
                        {
                            field: 'priority',
                            title: '权重',
                            width: 60,
                            align: 'center'
                        },
                        {
                            field: 'visiableStatus',
                            title: '前端是否可见',
                            width: 180,
                            align: 'center',
                            formatter: function (value, row, index) {
                                var marketNameAs = "";
                                var assign = "<font color='grey'>修改</font>";
                                var grayUserNum = row.grayUserNum;
                                if (row.status == "0") {
                                    marketNameAs = "<div>全部不可见</div>";
                                } else if (row.status == "1") {
                                    marketNameAs = "<div>全部可见</div>";
                                } else if (row.status == "2") {
                                    marketNameAs = "<div>部分用户可见</div>";
                                    marketNameAs += "<div>【" + row.specialUserTagNames + "】</div>";
                                } else if (row.status == "3") {
                                    marketNameAs = "<div>部分用户不可见</div>";
                                    marketNameAs += "<div>【" + row.specialUserTagNames + "】</div>";
                                }
                                assign = '<a href="#" onclick="editShowAndUserTags(\'' + row.id + '\',\'' + row.status + '\')"> 修改 </a>';
                                return marketNameAs + assign;
                            }
                        },
                        {
                            field: 'onlineTime',
                            title: '上线时间',
                            width: 150,
                            align: 'center',
                            formatter: function (value, row, index) {
                                return new Date(value).format("yyyy-MM-dd hh:mm:ss");
                            }
                        },
                        {
                            field: 'offlineTime',
                            title: '下线时间',
                            width: 150,
                            align: 'center',
                            formatter: function (value, row, index) {
                                if (value != null) {
                                    return new Date(value).format("yyyy-MM-dd hh:mm:ss");
                                }
                            }
                        },
                        {
                            field: 'action',
                            title: '操作',
                            width: 80,
                            align: 'center',
                            formatter: function (value, row, index) {
                                // console.log("111")
                                // console.log("value:" + value + "  row.id:" + row.id + "  row.type:" + row.cardTemplateType)
                                var e = '<a href="#" onclick="editConfig(\'' + row.id + '\',\'' + row.cardTemplateType + '\')">配置</a>';
                                var d = '<a href="#" onclick="deleterow(this,\'' + row.id + '\')">删除</a> ';
                                var kq = '  ';
                                if (row != undefined && row.parentId == null) {
                                    return e + kq + d;
                                }

                            }
                        },
                        {
                            field: 'remark',
                            title: '备注',
                            width: 150,
                            align: 'center'
                        }
                    ]],
                    toolbar: '#toolbar',
                    onLoadSuccess: function () {
                        $('#searchForm table').show();
                        parent.$.messager.progress('close');

                        $(this).datagrid('tooltip');
                    }
                });

            $('#searchForm input').keyup(function (event) {
                if (event.keyCode == '13') {
                    searchFun();
                }
            });
        });

        function addConfig(cardId, cardType) {
            console.log(cardId, cardType)
            var myUrl = '${pageContext.request.contextPath}/hotspotLiving/add.action';
            parent.$.modalDialog({
                title: '新增卡片',
                width: 600,
                height: 700,
                href: myUrl,
                buttons: [{
                    text: '确认',
                    handler: function () {
                        parent.$.modalDialog.openner_dataGrid = dataGrid;
                        var f = parent.$.modalDialog.handler.find('#form');
                        f.submit();
                    }
                }]
            });
        }

        function editConfig(cardId, cardType) {
            var myUrl = '${pageContext.request.contextPath}/hotspotLiving/editCard.action?cardId=' + cardId + '&cardType=' + cardType;

            parent.$.modalDialog({
                title: '编辑卡片',
                width: 600,
                height: 800,
                href: myUrl,
                buttons: [{
                    text: '确认',
                    handler: function () {
                        parent.$.modalDialog.openner_dataGrid = dataGrid;
                        var f = parent.$.modalDialog.handler.find('#form');
                        f.submit();
                    }
                }]
            });
        }

        function searchFun() {
            dataGrid.datagrid('load', $.serializeObject($('#searchForm')));
            dataGrid.datagrid('uncheckAll').datagrid('unselectAll').datagrid('clearSelections').datagrid('clearChecked');
            editStatusRow = undefined;
        }

        function cleanFun() {
            $('#searchForm input').val('');
            dataGrid.datagrid('load', {});
            editStatusRow = undefined;
        }

        function deleterow(target, id, cardChannelId) {
            var params;
            var url;
            params = "id=" + id;
            url = "/hotspotLiving/deleteById.action"
            $.messager.confirm('确认', '确定删除吗？', function (r) {
                if (r) {
                    $.ajax({
                        url: "${pageContext.request.contextPath}" + url,
                        type: "post",
                        dataType: 'json',
                        data: params,
                        async: false,
                        success: function (json) {
                            if (!json.success) {
                                $.messager.alert('错误', json.msg, 'error');
                                dataGrid.datagrid('rejectChanges');
                                return;
                            } else {
                                $.messager.show({
                                    title: '提示',
                                    msg: "成功！"
                                });
                                dataGrid.datagrid('deleteRow', getRowIndex(target));
                            }
                        }
                    });
                }
            });
        }

        function getRowIndex(target) {
            var tr = $(target).closest('tr.datagrid-row');
            return parseInt(tr.attr('datagrid-row-index'));
        }
    </script>
</head>
<body>
<div class="easyui-layout" data-options="fit : true,border : false">
    <div data-options="region:'north',title:'查询条件',border:false"
         style="height: 64px; overflow: hidden;padding-top:3px">
        <form id="searchForm">
            &nbsp;tab:
            <select class="easyui-combobox" id="tab" name="tab" class="easyui-validatebox" style="width: 80px;">
                <option value="">--全部--</option>
                <option value="1">发现</option>
                <option value="2">直播</option>
                <option value="3">发现直播</option>
            </select>
            卡片名称:<input id="cardName" name="cardName" class="easyui-textbox span2"/>
            卡片类型:<input id="cardTemplateType" name="cardTemplateType" class="easyui-combobox span2" data-options="
                            required:false,
                            editable:true,
                            valueField: 'code',
                            textField: 'description',
                            url:'${pageContext.request.contextPath}/hotspotLiving/getCardTypes.action' "/>

            <%--&nbsp; 渠道配置卡片 : <input type="checkbox" id="showCardChannel" name="showCardChannel" onchange="searchFun();">--%>
            卡片ID:<input id="cardId" name="cardId" class="easyui-textbox span2"/>
            分栏类型 :<input id="tagIds" name='tagIds' style="width:135px;height:30px;" class="easyui-combobox"
                         data-options="required:false,editable:false, valueField: 'tagId',textField: 'tagName',panelHeight: '200',url: '${pageContext.request.contextPath}/tag/getSelectListOfAll.action'"/>
            前端是否可见 :
            <select class="easyui-combobox" id="status" name="status" class="easyui-validatebox" style="width: 80px;">
                <option value="">--全部--</option>
                <option value="0">全部不可见</option>
                <option value="1">全部可见</option>
                <option value="2">部分用户可见</option>
                <option value="3">部分用户不可见</option>
            </select>
        </form>
    </div>
    <div data-options="region:'center',border:false">
        <table id="dataGrid"></table>
    </div>
</div>


<div id="toolbar" style="display: none;">
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_add',plain:true"
       onclick="addConfig();">新增</a>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_delete',plain:true"
       onclick="cleanFun();">清空条件</a>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_go',plain:true" onclick="searchFun();">查 询</a>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_go',plain:true" onclick="channelBindingModule.configTargetChannel();">关联渠道</a>
</div>

<!-- 引入渠道绑定模块 -->
<jsp:include page="../../common/channelBindingModule.jsp">
    <jsp:param name="moduleId" value="hotspotBinding"/>
    <jsp:param name="targetType" value="HOTSPOT"/>
    <jsp:param name="targetDataGridId" value="dataGrid"/>
    <jsp:param name="onBindingSuccess" value="onHotspotBindingSuccess"/>
</jsp:include>

<script type="text/javascript">
    // 热点渠道绑定成功回调
    function onHotspotBindingSuccess(targetIds, channelIds) {
        console.log('热点渠道绑定成功:', targetIds, channelIds);
        $.messager.show({
            title: '提示',
            msg: '热点渠道绑定成功！',
            timeout: 3000,
            showType: 'slide'
        });
    }
</script>

</body>
</html>