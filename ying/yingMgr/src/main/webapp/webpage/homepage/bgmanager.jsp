<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <title>背景图配置</title>
    <jsp:include page="/inc.jsp"></jsp:include>
    <c:set var="updateUrl" value="/taglib/updateTagConfig.action" scope="request" />
    <c:set var="updateTagGroupUrl" value="/taglib/updateTagConfig.action" scope="request"/>
    <c:set var="type" value="bgPic" scope="request" />
    <c:set var="statusField" value="status" scope="request" />
    <jsp:include page="../taglib/editUserTag.jsp"></jsp:include>
    <script type="text/javascript">
        var dataGrid;
        
        $(function () {
            dataGrid = $('#dataGrid')
                .datagrid(
                    {
                        url: '${pageContext.request.contextPath}/card/bgDataGrid.action',
                        fit: true,
                        fitColumns: false,
                        border: false,
                        pagination: true,
                        idField: 'id',
                        pageSize: 20,
                        pageList: [10, 20, 30, 40, 50],
                        loadMsg: '数据处理中，请稍后...',
                        checkOnSelect: true,
                        selectOnCheck: true,
                        nowrap: true,//在一行上显示数据，防止撑宽数据行
                        striped: true,//斑马线
                        rownumbers: true,//显示一个行号列
                        singleSelect: true,
                        sortName: 'id',
                        sortOrder: 'desc',
                        columns: [[
                            {
                                field: 'id',
                                hidden: true
                            },
                            {
                                field: 'name',
                                title: '名称',
                                align: 'center',
                                width: 120
                            },
                            {
                                field: 'picUrl',
                                title: '图片URL地址',
                                align: 'center',
                                width: 120
                            },
                            {
                                field: 'picThumbUrl',
                                title: '缩略图URL地址',
                                align: 'center',
                                width: 120
                            },
                            {
                                field: 'picType',
                                title: '图片类型',
                                align: 'center',
                                width: 80,
                                formatter: function (value) {
                                    if(value == 1) {
                                        return '头部大图';
                                    } else if (value == 2) {
                                        return "头部小图";
                                    } else if (value == 3) {
                                        return "头部无图";
                                    } else {
                                        return '未知';
                                    }
                                }
                            },
                            {
                                field: 'picGotoUrl',
                                title: '图片跳转地址',
                                align: 'center',
                                width: 120
                            },
                            {
                                field: 'clientType',
                                title: '前端系统类型',
                                align: 'center',
                                formatter: function (value) {
                                    if(value == 1) {
                                        return 'iOS-2x';
                                    } else if (value == 3) {
                                        return "iOS-3x";
                                    } else if (value == 4) {
                                        return "iPhone X";
                                    } else if(value == 2) {
                                        return 'Android';
                                    } else if (value == 0) {
                                        return '通用';
                                    } else {
                                        return '未知';
                                    }
                                },
                                width: 80
                            },
                            {
                                field: 'priority',
                                title: '权重',
                                align: 'center',
                                width: 60
                            },
                            {
                                field: 'useFor1',
                                title: '未登录;未登录A;未登录B',
                                align: 'center',
                                width: 90
                            },
                            {
                                field: 'useFor2',
                                title: '登录未购买',
                                align: 'center',
                                width: 90
                            },
                            {
                                field: 'useFor3',
                                title: '已购买',
                                width: 70,
                                align: 'center'
                            },
                            {
                                field: 'useFor4',
                                title: '老客在投态',
                                width: 70,
                                align: 'center'
                            },
                            {
                                field: 'useFor5',
                                title: '老客流失态',
                                width: 70,
                                align: 'center'
                            },
                            {
                                field: 'useFor6',
                                title: '登录设备',
                                align: 'center',
                                width: 90
                            },
                            {
                                field: 'useFor7',
                                title: '未登录新人',
                                align: 'center',
                                width: 70
                            },
                            {
                                field: 'zcCode',
                                title: '渠道注册码',
                                align: 'center',
                                width: 200
                            },
                            {
                                field: 'abTest',
                                title: 'ABTest',
                                align: 'center',
                                formatter: function (value) {
                                    if (value == 0) {
                                        return '全部展示';
                                    } else if (value == 1) {
                                        return "A展示";
                                    } else if (value == 2) {
                                        return "B展示";
                                    } else {
                                        return '--';
                                    }
                                },
                                width: 80
                            },
                            {
                                field: 'onlineTime',
                                title: '上线时间',
                                width: 150,
                                align: 'center',
                                formatter: function (value, row, index) {
                                    return new Date(value).format("yyyy-MM-dd hh:mm:ss");
                                }
                            },
                            {
                                field: 'offlineTime',
                                title: '下线时间',
                                width: 150,
                                align: 'center',
                                formatter: function (value, row, index) {
                                    if (value != null) {
                                        return new Date(value).format("yyyy-MM-dd hh:mm:ss");
                                    }
                                }
                            },
                            {
                                field: 'versionLimit',
                                title: '最低版本',
                                width: 80,
                                align: 'center'
                            },
                            {
                                field: 'status',
                                title: '前端是否可见',
                                width: 80,
                                align: 'center',
                                formatter: function (value, row, index) {
                                    var marketNameAs = "";
                                    if (row.status == "1") {
                                        marketNameAs = "<div>全部可见</div>";
                                    } else if (row.status == "2") {
                                        marketNameAs = "<div>部分用户可见</div>";
                                        marketNameAs += "<div>【" + row.specialUserTagNames + "】</div>";
                                    } else if (row.status == "3") {
                                        marketNameAs = "<div>部分用户不可见</div>";
                                        marketNameAs += "<div>【" + row.specialUserTagNames + "】</div>";
                                    } else {
                                        marketNameAs = "<div>全部不可见</div>";
                                    }
                                    assign = '<a href="#" onclick="editShowAndUserTags(\'' + row.id + '\',\'' + row.status + '\')" title="修改前端可见性及用户标签" > 修改 </a>';
                                    return marketNameAs + assign;
                                }
                            },
                            {
                                field: 'joinAudit',
                                title: '是否纳入运营位配置',
                                width: 180,
                                align: 'center',
                                formatter: function (value, row, index) {
                                    if(value == 1){
                                        return "纳入";
                                    }
                                    if(value == 0){
                                        return "不纳入";
                                    }
                                    return "-";
                                }
                            },
                            {
                                field: 'action',
                                title: '操作',
                                width: 200,
                                align: 'center',
                                formatter: function (value, row, index) {
                                    var e = '<a href="#" onclick="editBgPic(\'' + row.id + '\',\'' + row.iCardType + '\')">配置</a>';
                                    var d = '<a href="#" onclick="deleterow(this,\'' + row.id + '\')">删除</a> ';
                                    var kq = '  ';
                                    if (row != undefined && row.parentId == null) {
                                        return e + ' ' + d;
                                    }
                                    return '';
                                }
                            },
                            {
                                field: 'remark',
                                title: '备注',
                                width: 150,
                                align: 'center'
                            }
                            ]],
                        toolbar: '#toolbar',
                        onLoadSuccess: function () {
                            $('#searchForm table').show();
                            $(this).datagrid('tooltip');
                        },
                        onLoadError: function() {
                            $('#searchForm table').show();
                        }
                    });

            $('#searchForm input').keyup(function (event) {
                if (event.keyCode == '13') {
                    searchFun();
                }
            });
        });

        function addBgPic() {
            var myUrl = '${pageContext.request.contextPath}/card/addBgPic.action';
            parent.$.modalDialog({
                title: '新增卡片背景图',
                width: 600,
                height: 500,
                href: myUrl,
                buttons: [{
                    text: '确认',
                    handler: function () {
                        parent.$.modalDialog.openner_dataGrid = dataGrid;
                        var f = parent.$.modalDialog.handler.find('#form');
                        f.submit();
                    }
                }]
            });
        }

        function editBgPic(bgPicId) {
            var myUrl = '${pageContext.request.contextPath}/card/editBgPic.action?bgPicId=' + bgPicId;
            parent.$.modalDialog({
                title: '编辑卡片背景图',
                width: 600,
                height: 500,
                href: myUrl,
                buttons: [{
                    text: '确认',
                    handler: function () {
                        parent.$.modalDialog.openner_dataGrid = dataGrid;
                        var f = parent.$.modalDialog.handler.find('#form');
                        f.submit();
                    }
                }]
            });
        }

        function searchFun() {
            dataGrid.datagrid('load', $.serializeObject($('#searchForm')));
            dataGrid.datagrid('uncheckAll').datagrid('unselectAll').datagrid('clearSelections').datagrid('clearChecked');
        }

        function cleanFun() {
            $('#searchForm input').val('');
            dataGrid.datagrid('load', {});
        }
        // 渠道绑定成功回调
        function onBackgroundPicBindingSuccess(targetIds, channelIds) {
            console.log('背景图渠道绑定成功:', targetIds, channelIds);
            $.messager.show({
                title: '提示',
                msg: '背景图渠道绑定成功！',
                timeout: 3000,
                showType: 'slide'
            });
        }
    </script>
</head>
<body>
<div class="easyui-layout" data-options="fit : true,border : false">
    <div data-options="region:'north',title:'查询条件',border:false"
         style="height: 64px; overflow: hidden;">
        <form id="searchForm">
            <table class="table table-hover table-condensed"
                   style="display: none;">
                <tr>
                    <th>
                        背景图名称
                    </th>
                    <td>
                        <input id="cardName" name="cardName" class="easyui-textbox span2"/>
                    </td>

                    <th>客态筛选</th>
                    <td>
                        <input id="useFor" name="useFor" class="easyui-combobox span2" data-options="
                     required:false,
                     editable:true,
                     valueField: 'code',
                     textField: 'description',
                     url:'${pageContext.request.contextPath}/card/getBgManagerUseFors.action' "/>
                    </td>

                    <th>上线状态筛选</th>
                    <td>
                        <select class="easyui-combobox" style="width: 100px" name="onlineStatus" id="onlineStatus">
                            <option value="1">在线</option>
                            <option value="2">已下线</option>
                            <option value="0">未上线</option>
                            <option value="">全部</option>
                        </select>
                    </td>

                    <th>是否纳入运营位配置</th>
                    <td>
                        <select class="easyui-combobox" style="width: 100px" name="joinAudit" id="joinAudit">
                            <option value="-1">全部</option>
                            <option value="1">纳入</option>
                            <option value="0">不纳入</option>
                        </select>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div data-options="region:'center',border:false">
        <table id="dataGrid"></table>
    </div>
</div>

<div id="toolbar" style="display: none;">
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_add',plain:true"
       onclick="addBgPic();">新增</a>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_delete',plain:true"
       onclick="cleanFun();">清空条件</a>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_go',plain:true" onclick="searchFun();">查 询</a>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       data-options="iconCls:'brick_go',plain:true" onclick="channelBindingModule.configTargetChannel();">关联渠道</a>
</div>

<!-- 引入渠道绑定模块 -->
<jsp:include page="../common/channelBindingModule.jsp">
    <jsp:param name="moduleId" value="backgroundPicBinding"/>
    <jsp:param name="targetType" value="BACKGROUND_PIC"/>
    <jsp:param name="targetDataGridId" value="dataGrid"/>
    <jsp:param name="onBindingSuccess" value="onBackgroundPicBindingSuccess"/>
</jsp:include>

</body>
</html>