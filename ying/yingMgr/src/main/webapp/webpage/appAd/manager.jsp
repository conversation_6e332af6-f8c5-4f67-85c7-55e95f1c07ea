<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html>
<html>
<head>
	<title>推广位配置</title>
	<jsp:include page="../../inc.jsp"></jsp:include>
	<c:set var="updateUrl" value="/taglib/updateTagConfig.action" scope="request"/>
	<c:set var="updateTagGroupUrl" value="/taglib/updateTagConfig.action" scope="request"/>
	<c:set var="type" value="appAd" scope="request" />
	<c:set var="statusField" value="enable" scope="request" />
	<c:set var="typeToBindChannel" value="appAd" scope="request"/>
	<jsp:include page="../taglib/editUserTag.jsp"></jsp:include>
	<jsp:include page="../channelMarket/configCardChannel.jsp"></jsp:include>


<script type="text/javascript">
	var editStatusRow = undefined;
	var dataGrid;
	
	//1:未登录 2:登录未出借 3:已出借
	var states = {"1":"未登录","11":"未登录A","12":"未登录B","2":"登录未出借","3":"已出借","4":"老客在投","5":"老客流失","6":"注册当日"};

	$(function() {
 		dataGrid = $('#dataGrid').datagrid({
			url : '${pageContext.request.contextPath}/appAdController/getAppAds.action',
			fit : true,
			fitColumns : false,
			border : false,
			pagination : true,
			idField : 'id',
			pageSize : 10,
			pageList : [ 10, 20, 30, 40, 50 ],
			sortName : 'update_time',
			sortOrder : 'desc',
			checkOnSelect : true,
			selectOnCheck : true,
			nowrap : true,//在一行上显示数据，防止撑宽数据行
			striped : true,//斑马线
			rownumbers : true,//显示一个行号列
			//singleSelect : true,
			columns : [ [
			{
				field: 'ck',
				checkbox: true,
				hidden: false
			},
			{
				field:'action',
				title:'操作',
				width:60,
				align:'center',
				formatter:function(value,row,index){
						if (row.editing){
							var s = '<a href="#" onclick="saverow(this)">保存</a> ';
							var c = '<a href="#" onclick="cancelrow(this)">取消</a>';
							return s+c;
						}else if(row.composeType==2){
						    //只有组合
							var e = '<a href="#" onclick="updatePage(\''+row.id+'\')">编辑</a> ';
							var d = '';
							return e+d;
						}else {
							var e = '<a href="#" onclick="editrow(this,\''+row.id+'\')">编辑</a> ';
							var d = '';
							//var d = '<a href="#" onclick="deleterow(this,\''+row.id+'\')">删除</a>';
							return e+d;
						}
					}
			},
			{
				field : 'id',
				title : 'id',
				width : 35,
				hidden : false,
				sortable : false,
				align:'center',
				
				formatter : function(value, row, index) {
					return value;
				}
			}
			,{
				field : 'title',
				title : '推广标题',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor:{
					type:'validatebox',
					options:{
						required:true,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					//return $.timeStamp2String(value);
					return value;
				}
				
			}
			,{
				field : 'x',
				title : '展示图片',
				width : 176,
				hidden : false,
				sortable : false,
				align:'center',
				formatter : function(value, row, index) {
					return "<img style='width:150px;height:50px;padding:3px'  src='"+row.showPic+"' onerror='this.src=\"https://appsource.yirencf.com/Ymall/home/<USER>"'></img>";
				}
			}
			,{
				field : 'adDesc',
				title : '描述信息',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor:{
					type:'validatebox',
					options:{
						required:false,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					//return $.timeStamp2String(value);
					return value;
				}
				
			}
			,{
				field : 'showPosition',
				title : '展示位置',
				width : 150,
				editor:{
					type:'combobox',
					options:{
						valueField:'code',
						textField:'name',
						required:true,
						editable:true,
						url:'${pageContext.request.contextPath}/appAdController/getPositionsConfig.action',
						panelHeight:'400',
						onChange:function(value, row, index){
						}
					}
				},
				formatter: function(value,row,index){
					var name="";
					$.ajax({
						url : '${pageContext.request.contextPath}/appAdController/getPositionByCode.action?positionCode='+value,
						type : "post",
						dataType : 'json',
						async:false,
						success : function(json) {
							if(json){
								 name = json.name + '-position:' + value;
							}else{
								name = "---";
							}
						}
					});
					return name;
				}
			},
			{
				field: 'adKey',
				title: '关联的直播id值',
				width: 150,
				align: 'center',
                editor:{
                    type:'validatebox',
                    options:{
                        required:false,
                        onChange:function(value, row, index){
                        }
                    }
                },
                formatter : function(value, row, index) {
                    return value;
                }
			},
			{
				field: 'channelName',
				title: '渠道名称',
				width: 150,
				align: 'center'
			},
			{
				field : 'clientTypes',
				title : '客户端',
				width : 70,
				hidden : false,
				sortable : false,
				align:'center',
				editor : {
					type : 'combobox',
					options : {
						data : [
						    {
						        "name":"通用",
						        "value":0
						    },
						    {
						        "name":"IOS",
						        "value":1
						    },
						    {
						        "name":"ANDROID",
						        "value":2
						    },
							{
								"name":"H5",
								"value":4
							},
                            {
                                "name":"鸿蒙",
                                "value":6
                            }
						],
						valueField : 'value',
						textField : 'name',
						editable:false,
						required:true,
						multiple: true,
						onChange : function(value, row,
								index) {
						}
					}
				},
				formatter : function(value, row, index) {
					var arr = new String(value).split(',');
					var clientTypeShow = "";
					for (let i in arr) {
						var clientType = arr[i];
						if(clientType === '0') {
							clientTypeShow = clientTypeShow + "通用，";
						} else if(clientType === '1') {
							clientTypeShow = clientTypeShow + "IOS，";
						} else if(clientType === '2') {
							clientTypeShow = clientTypeShow + "ANDROID，";
						}  else if(clientType === '4') {
							clientTypeShow = clientTypeShow + "H5，";
						}  else if(clientType === '6') {
                            clientTypeShow = clientTypeShow + "鸿蒙，";
                        } else {
							clientTypeShow = clientTypeShow + clientType + "，";
						}
					}
					return clientTypeShow.substr(0, clientTypeShow.length-1);
				}
			}
			,{
				field : 'tag',
				title : 'tag（尺寸设置等）',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor:{
					type:'validatebox',
					options:{
						required:false,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					//return $.timeStamp2String(value);
					return value;
				}

			}
			,{
				field : 'minVersion',
				title : '最低版本',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor:{
					type:'validatebox',
					options:{
						required:false,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					return value;
				}
			}
			,{
				field : 'maxVersion',
				title : '最高版本',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor:{
					type:'validatebox',
					options:{
						required:false,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					return value;
				}
			}
			,{
				field : 'goType',
				title : '打开方式',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor : {
					type : 'combobox',
					options : {
						data : [
						   {
						    "name":"APP内打开",
						    "value":1
						   },
						   {
						    "name":"APP外打开",
						    "value":2
						   },
						   {
						    "name":"Native打开",
						    "value":3
						   }
						],
						valueField : 'value',
						textField : 'name',
						editable:false,
						required:true,
						onChange : function(value, row,
								index) {
						}
					}
				},

				formatter : function(value, row, index) {
					if (value == 1) {
						return 'APP内打开';
					} else if (value == 2) {
						return 'APP外打开';
					} else if (value == 3) {
					    return 'Native打开';
					}else {
					    return "--";
					}
				}
			}
			,{
				field : 'showPic',
				title : '展示图片',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor:{
					type:'validatebox',
					options:{
						required:false,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					return value;
				}
			}
			,{
				field : 'goUrl',
				title : '跳转地址',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor:{
					type:'validatebox',
					options:{
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					return value;
				}
			}
			,{
				field : 'orders',
				title : '展示顺序',
				width : 60,
				hidden : false,
				sortable : false,
				align:'center',
				editor:{
					type:'numberbox',
					options:{
						required:true,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					return value;
				}
			},	
			 {
                 field: 'onlineTime',
                 title: '上线时间',
                 width: 140,
                 align: 'center',
 				editor:{
					type:'datetimebox',
					options:{
						required:false,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					var timeVal;
					if (isNaN(value)) {
						timeVal = value;
					} else {
						timeVal = $
								.timeStamp2String(value);
					}
					return timeVal;
				}
             },
             {
                 field: 'offlineTime',
                 title: '下线时间',
                 width: 140,
                 align: 'center',
 				editor:{
					type:'datetimebox',
					options:{
						required:false,
						onChange:function(value, row, index){
						}
					}
				},
				formatter : function(value, row, index) {
					var timeVal;
					if (isNaN(value)) {
						timeVal = value;
					} else {
						timeVal = $
								.timeStamp2String(value);
					}
					return timeVal;
				}
             }
             ,{
 				field : 'userStates',
 				title : '用户态',
 				width : 160,
 				hidden : false,
 				sortable : false,
 				align:'center',
 				formatter : function(value, row, index) {
 				    if (value != null && value != ""){
 				    	var userStates = value.split(",");
 				    	var userStatesNames = [];
 	 				    userStates.forEach(function(e){
 	 				    	userStatesNames.push(states[e]);
 	 				    });
 				    	return userStatesNames.join(",");
 				    }
 				    return value;
 				}
 			}
             ,{
  				field : 'xx',
  				title : '用户态(编辑)',
  				width : 170,
  				hidden : false,
  				sortable : false,
  				align:'center',
  				editor : {
					type : 'combobox',
					options : {
						data : [ {
                            "name" : "未登录",
                            "value" : "1"
                        }, {
							"name" : "未登录A",
							"value" : "11"
						}, {
							"name" : "未登录B",
							"value" : "12"
						}, {
                            "name" : "登录未出借",
                            "value" : "2"
						},{
                            "name" : "已出借",
                            "value" : "3",
						},{
                            "name" : "老客在投",
                            "value" : "4",
                        },{
                            "name" : "老客流失",
                            "value" : "5",
                        },{
                            "name" : "注册当日",
                            "value" : "6",
                        } ],
						valueField : 'value',
						textField : 'name',
						editable:false,
						multiple:true,
						onChange : function(value, row, index) {
							row.userStates = value;
						}
					}
				}
  			},
			{
				field : 'joinAudit',
				title : '是否纳入运营位配置',
				width : 100,
				editor : {
					type : 'combobox',
					options : {
						data : [ {
							"name" : "不纳入",
							"value" : 0
						}, {
							"name" : "纳入",
							"value" : 1
						} ],
						valueField : 'value',
						textField : 'name',
						editable:false,
						required:true,
						onChange : function(value, row, index) {
						}
					}
				},
				formatter: function(value,row,index){
					var joinAuditval = "";
					if (value == 0) {
						joinAuditval = "<div>不纳入</div>";
					} else if (value == 1) {
						joinAuditval = "<div>纳入</div>";
					} else {
						joinAuditval = value;
					}
					return joinAuditval;
				}
			},
			{
				field : 'enable',
				title : '可见性',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				editor : {
					type : 'combobox',
					options : {
						data : [ {
                            "name" : "全部不可见",
                            "value" : 0
                        }, {
                            "name" : "全部可见",
                            "value" : 1
                        }, {
                            "name" : "部分用户可见",
                            "value" : 2
						}, {
                            "name" : "部分用户不可见",
                            "value" : 3
						} ],
						valueField : 'value',
						textField : 'name',
						editable:false,
						required:true,
						onChange : function(value, row, index) {
						}
					}
				},

				formatter : function(value, row, index) {
                    var marketNameAs = "";
                    if (value == 0) {
                        marketNameAs = "<div>全部不可见</div>";
                    } else if (value == 1) {
                        marketNameAs = "<div>全部可见</div>";
                    } else if (value == 2) {
                        marketNameAs = "<div>部分用户可见</div>";
                        marketNameAs += "<div>【" + row.specialUserTagNames + "】</div>";
                    } else if (value == 3) {
                        marketNameAs = "<div>部分用户不可见</div>";
                        marketNameAs += "<div>【" + row.specialUserTagNames + "】</div>";
                    }
                    var assign = '<a href="#" onclick="editShowAndUserTags(\'' + row.id + '\',\'' + row.status + '\')" title="修改前端可见性及用户标签" > 修改 </a>';
                    return marketNameAs + assign;
				}
			}
			,{
				field : 'operator',
				title : '上次操作人',
				width : 100,
				hidden : false,
				sortable : false,
				align:'center',
				formatter : function(value, row, index) {
					return value;
				}
			}
			,{
				field : 'updateTime',
				title : '上次操作时间',
				width : 100,
				hidden : false,
				sortable : false,
				editable: false,
				align:'center',
				formatter : function(value, row, index) {
					var timeVal;
					if (isNaN(value)) {
						timeVal = value;
					} else {
						timeVal = $.timeStamp2String(value);
					}
					return timeVal;
				}
				
			}
				] ],

			toolbar : '#toolbar',
			onLoadSuccess : function() {
				$('#searchForm table').show();
				parent.$.messager.progress('close');

				$(this).datagrid('tooltip');
			},

			onRowContextMenu : function(e, rowIndex, rowData) {
				e.preventDefault();
				//$(this).datagrid('unselectAll');
				$(this).datagrid('clearChecked');
				$(this).datagrid('clearSelections');
				$(this).datagrid('selectRow', rowIndex);
				$('#menu').menu('show', {
					left : e.pageX,
					top : e.pageY
				});
			},

			onBeforeEdit:function(index,row){
				row.editing = true;
				updateActions(index);
			},
			onAfterEdit:function(rowIndex, rowData, changes){
                var params = new Object();
                params.id = rowData.id;
                params.title = rowData.title;
                params.adDesc = rowData.adDesc;
                params.showPosition = rowData.showPosition;
                // params.clientType = rowData.clientType;
                params.clientTypes = rowData.clientTypes;
                params.tag = rowData.tag;
                params.minVersion = rowData.minVersion;
                params.maxVersion = rowData.maxVersion;
                params.goType = rowData.goType;
                params.showPic = rowData.showPic.replace(/\s/g,'');
				params.goUrl = rowData.goUrl.replace(/\s/g,'');
                params.orders = rowData.orders;
                params.enable = rowData.enable;
                params.onlineTime= rowData.onlineTime;
                params.offlineTime=rowData.offlineTime;
                params.enable=rowData.enable;
                params.adKey=rowData.adKey;
				params.joinAudit=rowData.joinAudit;
                //params.userStates = rowData.userStates;
                if (rowData.xx != null && rowData.xx != ""){
                	params.userStates = rowData.xx;
                }

				// 版本字段相互依赖验证
				var minVersion = params.minVersion || '';
				var maxVersion = params.maxVersion || '';
				if ((minVersion && !maxVersion) || (!minVersion && maxVersion)) {
					$.messager.alert('提示', '最低版本和最高版本必须同时填写或同时不填', 'warning');
					dataGrid.datagrid('cancelEdit', rowIndex);
					rowData.editing = false;
					updateActions(rowIndex);
					editStatusRow = undefined;
					return;
				}

				$.messager.confirm('确认','确定保存吗？',function(r){
					if (r){
						$.ajax({
							url : "${pageContext.request.contextPath}/appAdController/saveAppAd.action",
							type : "post",
							dataType : 'json',
							data:params,
							async:false,
							success : function(json) {
								if(!json.success){
									$.messager.alert('错误', json.msg,'error');
									dataGrid.datagrid('rejectChanges');
									return;
								}else{
									$.messager.show( {
										title : '提示',
										msg : "保存成功！"
									});
									searchFun();
								}
							}
						});
					}else{
						dataGrid.datagrid('beginEdit',rowIndex);
					}
				});
				rowData.editing = false;
				updateActions(rowIndex);
				editStatusRow = undefined;
			},
			onCancelEdit:function(index,row,value){
				row.editing = false;
				updateActions(index);
			}
			
			
		});
		
		$('#searchForm input').keyup(function(event) {
			if (event.keyCode == '13') {
				searchFun();
			}
		});
	});
	
	function searchFun() {
		dataGrid.datagrid('load', $.serializeObject($('#searchForm')));
		dataGrid.datagrid('uncheckAll').datagrid('unselectAll').datagrid('clearSelections').datagrid('clearChecked');
		editStatusRow = undefined;
	}
	function cleanFun() {
		$('#searchForm input').val('');
		dataGrid.datagrid('load', {});
		editStatusRow = undefined;
	}
	
	
	function updateActions(index){
		dataGrid.datagrid('updateRow',{
			index: index,
			row:{}
		});
	}
	
	function getRowIndex(target){
		var tr = $(target).closest('tr.datagrid-row');
		return parseInt(tr.attr('datagrid-row-index'));
	}
	
	function editrow(target,id){
	    if (editStatusRow === undefined) {
		    editStatusRow = getRowIndex(target);
		    dataGrid.datagrid('beginEdit', editStatusRow);
		}
	}
	
	function deleterow(target,id){
		var params="id="+id;
		$.messager.confirm('确认','确定删除吗？',function(r){
			if (r){
				$.ajax({
					url : "${pageContext.request.contextPath}/appAdController/deleteAppAdById.action",
					type : "post",
					dataType : 'json',
					data:params,
					async:false,
					success : function(json) {
						if(!json.success){
							$.messager.alert('错误', json.msg,'error');
							dataGrid.datagrid('rejectChanges');
							return;
						}else{
							$.messager.show( {
								title : '提示',
								msg : "成功！"
							});
							//dataGrid.datagrid('load', {});
							dataGrid.datagrid('deleteRow', getRowIndex(target));
						}
					}
				});
			}
		});
	}
	
	function saverow(target){
		try {
			dataGrid.datagrid('endEdit', getRowIndex(target));
		}
		catch(err){
			console.log(err);
		}


	}
	
	function cancelrow(target){
		$.messager.confirm('确认','确定取消吗？',function(r){
			if (r){
				dataGrid.datagrid('cancelEdit', getRowIndex(target));
				//dataGrid.datagrid('rejectChanges');
				editStatusRow = undefined;
			}
		});
	}	
	
	function addPage(){

		var myUrl='${pageContext.request.contextPath}/appAdController/add.action';
		//window.open(myUrl);
		parent.$.modalDialog({
			title : '新增推广位',
			width : 700,
			height : 750,
			href : myUrl,
			buttons : [ {
				text : '确定',
				handler : function() {
					parent.$.modalDialog.openner_dataGrid = dataGrid;
					var f = parent.$.modalDialog.handler.find('#form');
					f.submit();
				}
			}]
		});
	}
	function updatePage(id ){
		var myUrl='${pageContext.request.contextPath}/appAdController/update.action?id='+id;
		parent.$.modalDialog({
			title : '修改',
			width : 700,
			height : 750,
			href : myUrl,
			buttons : [ {
				text : '预览',
				handler : function() {
					window.open('${pageContext.request.contextPath}/appAdController/preview.action?id='+id);
				}
			},{
				text : '修改',
				handler : function() {
					parent.$.modalDialog.openner_dataGrid = dataGrid;
					var f = parent.$.modalDialog.handler.find('#form');
					f.submit();
				}
			},{
				text : '取消',
				handler : function() {
					parent.$.modalDialog.handler.dialog('close');
				}
			}]
		});
	}


</script>
	</head>
	<body>
		<div class="easyui-layout" data-options="fit : true,border : false">
			<div data-options="region:'north',title:'查询条件',border:false"
				style="height: 64px; overflow: hidden;">
				<form id="searchForm">
					<table class="table table-hover table-condensed"
						style="display: none;">
						<tr>
							<th>
								id
							</th>
							<td>
								<input name="id" placeholder="" class="span2" style="width: 40px;"/>
							</td>
							<th>
							    标题
							</th>
							<td>
								<input name="title" placeholder="" class="span2"  style="width: 40px;"/>
							</td>
							<th>
								是否展示
							</th>
							<td>
                                <select class="easyui-combobox" id="enable"
                                						name="enable" class="easyui-validatebox" style="width: 80px;">
                                    <option value="">全部展示</option>
                                    <option value="0">不展示</option>
                                    <option value="1">展示</option>
                                </select>
							</td>
							<th>
								展示位置
							</th>
							<td>
								<input id="showPosition" name="showPosition" class="easyui-combobox span2" style="width: 150px;" data-options="
                                    valueField: 'code',
                                    textField: 'name',
                                    url:'${pageContext.request.contextPath}/appAdController/getPositionsConfig.action' "
								/>
							</td>
							<th>
								只看渠道
							</th>
							<td>
								<input type="checkbox" id="onlyChannel" name="onlyChannel"
									   onchange="searchFun();">
							</td>

							<th>
								上线状态
							</th>
							<td>
								<select class="easyui-combobox" id="onlineStatus"
										name="onlineStatus" class="easyui-validatebox" style="width: 80px;">
									<option value="1">--在线--</option>
									<option value="0">全部</option>
									<option value="1">在线</option>
									<option value="2">已下线</option>
									<option value="3">未上线</option>
								</select>
							</td>

							<th>
								客态筛选
							</th>
							<td>
								<input id="userStatus" name="userStatus" class="easyui-combobox span2" data-options="
                     required:false,
                     editable:true,
                     valueField: 'code',
                     textField: 'description',
                     url:'${pageContext.request.contextPath}/card/getConditionTypes.action?paramType=1' "/>
							</td>
							<th>
								是否纳入运营位配置
							</th>
							<td>
								<select class="easyui-combobox" style="width: 100px" name="joinAudit" id="joinAudit">
									<option value="-1">全部</option>
									<option value="1">纳入</option>
									<option value="0">不纳入</option>
								</select>
							</td>
						</tr>
					</table>
				</form>
			</div>
			<div data-options="region:'center',border:false">
				<table id="dataGrid"></table>
			</div>
		</div>
		<div id="toolbar" style="display: none;">
			<a href="javascript:void(0);" class="easyui-linkbutton"
				data-options="iconCls:'brick_add',plain:true"
				onclick="addPage();">新增</a>
			<a href="javascript:void(0);" class="easyui-linkbutton"
				data-options="iconCls:'brick_delete',plain:true"
				onclick="cleanFun();">清空条件</a>
			<a href="javascript:void(0);" class="easyui-linkbutton"
				data-options="iconCls:'brick_go',plain:true" onclick="searchFun();">查 询</a>
			<a href="javascript:void(0);" class="easyui-linkbutton"
			   data-options="iconCls:'brick_go',plain:true" onclick="bindChannel();">关联渠道</a>
		</div>
	</body>
</html>
