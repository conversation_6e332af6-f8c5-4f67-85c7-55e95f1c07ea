<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<script type="text/javascript" src="${pageContext.request.contextPath}/jslib/mobileMoney_validatebox.extend.js" charset="utf-8"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/style/card.css" type="text/css"/>
<script type="text/javascript">

var editor;
var p2pserviceComboGrid;
$(function() {
	window.setTimeout(function() {
		parent.$.messager.progress('close');
	}, 1);

	$('#form').form({
		url : '${pageContext.request.contextPath}/appAdController/saveAppAd.action',
		onSubmit : function(param) {
			parent.$.messager.progress( {
				title : '提示',
				text : '数据处理中，请稍后....'
			});
			var isValid = $(this).form('validate');
			if (!isValid) {
				parent.$.messager.progress('close');
				return false;
			}
			
			// 版本字段相互依赖验证
			var minVersion = $('#minVersion').val();
			var maxVersion = $('#maxVersion').val();
			if ((minVersion && !maxVersion) || (!minVersion && maxVersion)) {
				parent.$.messager.alert('提示', '最低版本和最高版本必须同时填写或同时不填', 'warning');
				parent.$.messager.progress('close');
				return false;
			}
			
			return isValid;
		},
		success : function(result) {
			parent.$.messager.progress('close');
			result = $.parseJSON(result);
			if (result.success) {
				parent.$.modalDialog.openner_dataGrid
						.datagrid('reload');//之所以能在这里调用到parent.$.modalDialog.openner_dataGrid这个对象，是因为user.jsp页面预定义好了
				parent.$.modalDialog.handler.dialog('close');
				parent.$.messager.show( {
					title : '提示',
					msg : "添加成功!"
				});
			} else {
				parent.$.messager.alert('错误', result.msg,'error');
			}
		}
	});

	$("#composeType").combobox({
		//相当于html >> select >> onChange事件
		onChange:function(){
			if($("#composeType").combobox('getValue') == "2"){
				$("#prompt-composeTable").show();
				document.getElementById("showPic").readOnly=true;
				document.getElementById("goUrl").readOnly=true;
			}else{
				$("#prompt-composeTable").hide();
				document.getElementById("showPic").readOnly=false;
				document.getElementById("goUrl").readOnly=false;
			}
		}});



	$("#showPosition").on("input",function(e){
		//获取input输入的值
		console.log(e.delegateTarget.value);
	});

	$("#showPosition").combobox({
		//相当于html >> select >> onChange事件
		onChange:function(){
			if($("#showPosition").combobox('getValue') == "26"){
				$("#prompt-fCardPopWin").show();
				$("#prompt-composeType").show();

			}else{
				$("#prompt-fCardPopWin").hide();
				$("#prompt-composeType").hide();


			}
		}})
});




//上传预览
function upload(o,imgName) {
	var file = $(o)[0].files[0];
	var img = $(o).parent().parent().next().find("img");
	var inputPicUrl = $(o).parent().parent();
	if (file) {
		var reader = new FileReader();
		reader.readAsDataURL(file);
		savefile(file, img, inputPicUrl,imgName);
	}
}

function savefile(file, img, inputPicUrl,imgName) {
	var formData = new FormData();
	formData.append('file', file);
	formData.append("path", "//opt//yrd_nas//appsource//member//");
	$.ajax({
		url: '${pageContext.request.contextPath}/memberPrivilegeController/uploadImgRename.action',
		dataType: 'json',
		type: 'POST',
		async: false,
		data: formData,
		processData: false, // 使数据不做处理
		contentType: false, // 不要设置Content-Type请求头
		beforeSend: function () {
			$(".loading").show();
		},
		success: function (result) {
			if (result && result.success) {
				$(img).attr("src", result.msg);
				$("<img/>").attr("src", $(img).attr("src")).load(function () {
					$(img).css({"width": 240, "height": 160});
					$(inputPicUrl).find("input[name='widths']").val(this.width);
					$(inputPicUrl).find("input[name='highs']").val(this.height);
				});
				//$(inputPicUrl).find("input[name='imgUrls']").val(result.msg);
				$("#"+imgName).val(result.msg);
				parent.$.messager.show({title: '提示', msg: "好嗨喔,上传成功!"});
			} else {
				alert("上传失败");

			}
			$(".loading").hide();
		},
		error: function (data) {
			console.log(data);
			alert("上传失败请检查");
			$(".loading").hide();
		}
	});
}

</script>

<div class="easyui-layout" data-options="fit:true,border:false">
	<div data-options="region:'center',border:false" title=""
		style="overflow: auto;">
		<form id="form" method="post">
			<table class="table table-hover table-condensed">
			<tr>
				<th>
				推广标题
				</th>
				<td>
					<input id="title" name="title" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:true,
							validType:'maxLength[18]'
					"/>
				</td>
			</tr>
			<tr>
				<th>
				描述信息
				</th>
				<td>
					<input id="adDesc" name="adDesc" class="easyui-textbox span2" style="width:250px;height:55px" data-options="
							required:false,
							multiline:true,
							validType:'maxLength[200]'
					"/>
				</td>
			</tr>
			<tr>
				<th>
				展示位置
				</th>
				<td>
                        <input id="showPosition" name="showPosition" class="easyui-combobox span2" style="width:250px;" data-options="
                            required:true,
                            editable:false,
                            valueField: 'code',
                            textField: 'name',
                            url:'${pageContext.request.contextPath}/appAdController/getPositionsConfig.action' "
                        />
					<div id="prompt-fCardPopWin" style="display: none"><p>注1意:如果用户有福卡，弹窗会被替换为福卡。</p> <p>可以在系统配置中的fortune:app:support:fcard:switch:popWinFcardAd参数指定关闭福卡弹窗，防止被替换。</p>
					</div>


				</td>
			</tr>

			<tr  id="prompt-composeType"  style="display: none">
				<th>
					弹窗类型1
				</th>
				<td>
					<input id="composeType" name="composeType" class="easyui-combobox span2" data-options="
											required:false,
											valueField: 'code',
											editable:false,
											textField: 'name',
											data : [
												{
													code:'1',
													name:'单个'
												},
												{
													code:'2',
													name:'组合'
												}
											]
										"/>
				</td>
			</tr>

			<tr>
				<th>
					关联直播id(直播浮球位置时可配置)
				</th>
				<td>
					<input id="adKey" name="adKey" class="easyui-textbox span2" style="width:250px;height:55px" data-options="
						required:false,
						multiline:true,
						validType:'maxLength[200]' "/>
				</td>
			</tr>
			<tr>
				<th>
				客户端类型
				</th>
				<td>
					<input id="clientTypes" name="clientTypes" class="easyui-combobox span2" data-options="
                                                required:true,
                                                valueField: 'code',
                                                editable:false,
                                                textField: 'name',
                                                multiple: true,
                                                data : [
                                                    {
                                                        code:'0',
                                                        name:'通用'
                                                    },
                                                    {
                                                        code:'1',
                                                        name:'IOS'
                                                    },
                                                    {
                                                        code:'2',
                                                        name:'ANDROID'
                                                    },
                                                    {
                                                        code:'4',
                                                        name:'H5'
                                                    },
                                                    {
                                                        code:'6',
                                                        name:'鸿蒙'
                                                    }
                                                ]
                                            "/>
				</td>
			</tr>
			<tr>
				<th>
				最低版本
				</th>
				<td>
					<input id="minVersion" name="minVersion" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[50]'
					"/>
					<div>例如: 8.0.0</div>
				</td>
			</tr>
			<tr>
				<th>
				最高版本
				</th>
				<td>
					<input id="maxVersion" name="maxVersion" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[50]'
					"/>
					<div>例如: 9.0.0</div>
				</td>
			</tr>
			<tr>
				<th>
				tag
				</th>
				<td>
					<input id="tag" name="tag" class="easyui-validatebox span2" style="width:200px;" data-options="
							required:false,
							validType:'maxLength[50]'
					"/>
					<div>(IOS:1136_640,1334_750,2208_1242; AD:1920_1080,1080_720) </div>
				</td>
			</tr>
			<tr>
				<th>
				打开方式
				</th>
				<td>
					<input id="goType" name="goType" class="easyui-combobox span2" data-options="
                                                required:true,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [
                                                    {
                                                        code:'1',
                                                        name:'APP内打开'
                                                    },
                                                    {
                                                        code:'2',
                                                        name:'APP外打开'
                                                    },
                                                    {
                                                        code:'3',
                                                        name:'Native打开'
                                                    }
                                                ]
                                            "/>

				</td>
			</tr>
			<tr>
				<th>
				展示图片
				</th>
				<td>
					<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="showPic" name="showPic" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[150]'
					"/>
				</td>
			</tr>
			<tr>
				<th>
				 跳转地址:
				</th>
				<td>
					<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="goUrl" name="goUrl" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[500]'
					"/>
				</td>
			</tr>
			<tr>
				<th>
				 展示顺序
				</th>
				<td>
					<input id="orders" name="orders" class="easyui-numberbox span2" style="width:100px;" data-options="
							required:false,
							precision:0
					"/>
				</td>
			</tr>
			<tr>
				<th>
				 可见性
				</th>
				<td>
					<input id="enable" name="enable" class="easyui-combobox span2" data-options="
                                                required:true,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '全部不可见'
                                                    }, {
                                                        code: '1', name: '全部可见'
                                                    }, {
                                                    	code: '2', name: '部分可见',
                                                    }, {
                                                    	code: '3', name: '部分不可见'
                                                    }
                                                ]
                                            "/>
				</td>
			</tr>
			<tr>
				<th>上线时间</th>
				<td>
					<input name="onlineTime" value="" class="easyui-datetimebox span2"style="width:150px;height:27px" data-options="required:false"/>
				</td>
			</tr>
            <tr>
            	<th>下线时间</th>
            	<td>
            		<input name="offlineTime" value="" class="easyui-datetimebox span2"style="width:150px;height:27px" data-options="required:false"/>
            	</td>
            </tr>
             <tr>
            	<th>用户态</th>
            	<td>
            		<select multiple="multiple" size="5" name="userStates" id="userStates">
					  <option value ="1">未登录</option>
					  <option value ="11">未登录A</option>
					  <option value ="12">未登录B</option>
					  <option value ="2">登录未出借</option>
					  <option value ="3">已出借</option>
					  <option value ="4">老客在投</option>
					  <option value ="5">老客流失</option>
					  <option value ="6">注册当日</option>
					</select>
            	</td>
            </tr>
			<tr>
				<th>
					是否纳入运营位配置
				</th>
				<td>
					<input id="joinAudit" name="joinAudit" class="easyui-combobox span2" data-options="
										required:true,
										editable:false,
										valueField: 'code',
										textField: 'name',
										data : [{
												code: '0', name: '不纳入'
											}, {
												code: '1', name: '纳入'
											}
										]
									"/>
				</td>
			</tr>
			</table>


			<table id="prompt-composeTable"   style="display: none" class="table table-hover table-condensed">

				<tr>
					<th>
						<font color="#FF0000">活动弹窗是否展示</font>
					</th>
					<td>
						<input id="activity1_show_type"  name="activity1_show_type"  class="easyui-combobox span2" data-options="
                                                required:false,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '不展示'
                                                    }, {
                                                        code: '1', name: '展示'
                                                    }
                                                ]
                                            "/>
					</td>
				</tr>
				<tr>
					<th>
						展示图片
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="activity1_showPic"   name="activity1_showPic"  class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[150]'
					"/>
					</td>
				</tr>

				<tr>
					<td>
						<div class="messager-p-msg">
							<span>英文+数字:</span>
							<input multiple="false" type="file"
								   onchange="upload(this,'activity1_showPic');"
								   name="myfile"
								   class="file"
								   style="position: relative;left: 55px;top: -23px;"/>
						</div>

					</td>
					<td>
						<span class="imgArea"><img class="tpl_18_picUrl_img" src=""/></span><span
							class="preview">图片名纯英文和数字</span>
					</td>
				</tr>
				<tr>
					<th>
						跳转地址
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="activity1_goUrl"  name="activity1_goUrl"  class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[500]'
					"/>
					</td>
				</tr>
				<tr>
					<th>
						展示顺序
					</th>
					<td>
						<input id="activity1_orders"  name="activity1_orders"  class="easyui-numberbox span2" style="width:100px;" data-options="
							required:false,
							precision:0
					"/>
					</td>
				</tr>

				<tr>
					<th>
						弹窗背景颜色:(#fc5b3b)
					</th>
					<td>
						<input id="backgroundColor"  name="backgroundColor"  onkeyup="this.value=this.value.replace(/\s+/g,'')"  class="easyui-validatebox span2" style="width:250px;" data-options="
							validType:'maxLength[10]'
					"/>
					</td>
				</tr>
<!--   福卡2 开始-->
				<tr>
					<th>
						<font color="#FF0000">福卡是否展示</font>
					</th>
					<td>
						<input id="fix2_show_type"  name="fix2_show_type"  class="easyui-combobox span2" data-options="
                                                required:false,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '不展示'
                                                    }, {
                                                        code: '1', name: '展示'
                                                    }
                                                ]
                                            "/>
					</td>
				</tr>

				<tr>
					<th>
						跳转地址
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="fix2_goUrl"  name="fix2_goUrl"  class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[500]'
					"/>
					</td>
				</tr>
				<tr>
					<th>
						展示顺序
					</th>
					<td>
						<input id="fix2_orders"  name="fix2_orders"  class="easyui-numberbox span2" style="width:100px;" data-options="
							required:false,
							precision:0
					"/>
					</td>
				</tr>

				<tr>
					<th>
						福卡中心地址
					</th>
					<td>
						HomeScheme://NATIVE/myFuCard
					</td>
				</tr>
				<!--   redPacket3 开始-->
				<tr>
					<th>
						<font color="#FF0000">红包券是否展示</font>
					</th>
					<td>
						<input id="redPacket3_show_type"  name="redPacket3_show_type"   class="easyui-combobox span2" data-options="
                                                required:false,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '不展示'
                                                    }, {
                                                        code: '1', name: '展示'
                                                    }
                                                ]
                                            "/>
					</td>
				</tr>
				<tr>
					<th>
						展示种类
					</th>
					<td>
						<input id="insureFundType"  name="insureFundType"  class="easyui-combobox span2" data-options="
                                                required:false,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '保险+基金'
                                                    }, {
                                                        code: '1', name: '保险'
                                                    }, {
                                                        code: '2', name: '基金'
                                                    }
                                                ]
                                            "/>
					</td>
				</tr>
				<tr>
					<th>
						跳转地址
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="redPacket3_goUrl" name="redPacket3_goUrl"  class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[500]'
					"/>
					</td>
				</tr>
				<tr>
					<th>
						展示顺序
					</th>
					<td>
						<input id="redPacket3_orders"   name="redPacket3_orders" class="easyui-numberbox span2" style="width:100px;" data-options="
							required:false,
							precision:0
					"/>
					</td>
				</tr>

				<tr>
					<th>
						红包券
					</th>
					<td>
						HomeScheme://NATIVE/myWealth
					</td>
				</tr>
				<!--   yrb4 开始-->
				<tr>
					<th>
						<font color="#FF0000">宜人币是否展示</font>
					</th>
					<td>
						<input id="yrb4_show_type"  name="yrb4_show_type"  class="easyui-combobox span2" data-options="
                                                required:false,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '不展示'
                                                    }, {
                                                        code: '1', name: '展示'
                                                    }
                                                ]
                                            "/>
					</td>
				</tr>

				<tr>
					<th>
						跳转地址
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="yrb4_goUrl"  name="yrb4_goUrl" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[500]'
					"/>
					</td>
				</tr>

				<tr>
					<th>
						展示顺序
					</th>
					<td>
						<input id="yrb4_orders"  name="yrb4_orders"  class="easyui-numberbox span2" style="width:100px;" data-options="
							required:false,
							precision:0
					"/>
					</td>
				</tr>

				<tr>
					<th>
						宜人币端内连接
					</th>
					<td>
						https://5g.yirendai.com/dh/app/yrb/index.html
					</td>
				</tr>

				<!--     points5 开始-->

				<tr>
					<th>
						<font color="#FF0000">积分是否展示</font>
					</th>
					<td>
						<input id="points5_show_type"  name="points5_show_type" class="easyui-combobox span2" data-options="
                                                required:false,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '不展示'
                                                    }, {
                                                        code: '1', name: '展示'
                                                    }
                                                ]
                                            "/>
					</td>
				</tr>
				<tr>
					<th>
						跳转地址
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="points5_goUrl"  name="points5_goUrl"  class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[500]'
					"/>
					</td>
				</tr>
				<tr>
					<th>
						展示顺序
					</th>
					<td>
						<input id="points5_orders"  name="points5_orders"   class="easyui-numberbox span2" style="width:100px;" data-options="
							required:false,
							precision:0
					"/>
					</td>
				</tr>
				<tr>
					<th>
						积分端内连接
					</th>
					<td>
						HomeScheme://NATIVE/earnPoint
					</td>
				</tr>
				<!--     limitTime6 开始-->
				<tr>
					<th>
						<font color="#FF0000">限时是否展示</font>
					</th>
					<td>
						<input id="limitTime6_show_type"  name="limitTime6_show_type"  class="easyui-combobox span2" data-options="
                                                required:false,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '不展示'
                                                    }, {
                                                        code: '1', name: '展示'
                                                    }
                                                ]
                                            "/>
					</td>
				</tr>
				<tr>
					<th>
						展示图片
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="limitTime6_showPic"  name="limitTime6_showPic"   class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[150]'
					"/>
					</td>
				</tr>
				<tr>
					<td>
						<div class="messager-p-msg">
							<span>英文+数字:</span>
							<input multiple="false" type="file"
								   onchange="upload(this,'limitTime6_showPic');"
								   name="myfile"
								   class="file"
								   style="position: relative;left: 55px;top: -23px;"/>
						</div>

					</td>
					<td>
						<span class="imgArea"><img class="tpl_18_picUrl_img" src=""/></span><span
							class="preview">图片名纯英文和数字</span>
					</td>
				</tr>

				<tr>
					<th>
						跳转地址
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="limitTime6_goUrl"   name="limitTime6_goUrl"  class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[500]'
					"/>
					</td>
				</tr>

				<tr>
					<th>
						大标题
					</th>
					<td>
						<input id="limitTime6_middleDesc" name="limitTime6_middleDesc" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[18]'
					"/>
					</td>
				</tr>

				<tr>
					<th>
						小标题
					</th>
					<td>
						<input id="limitTime6_bottomDesc" name="limitTime6_bottomDesc" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[18]'
					"/>
					</td>
				</tr>
				<tr>
					<th>
						背景图
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="backgroundPic"  name="backgroundPic"   class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[150]'
					"/>
					</td>
				</tr>
				<tr>
					<td>
						<div class="messager-p-msg">
							<span>英文+数字:</span>
							<input multiple="false" type="file"
								   onchange="upload(this,'backgroundPic');"
								   name="myfile"
								   class="file"
								   style="position: relative;left: 55px;top: -23px;"/>
						</div>

					</td>
					<td>
						<span class="imgArea"><img class="tpl_18_picUrl_img" src=""/></span><span
							class="preview">图片名纯英文和数字</span>
					</td>
				</tr>
				<tr>
					<th>
						按钮文案
					</th>
					<td>
						<input id="limitTime6_buttonDesc" name="limitTime6_buttonDesc" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[3]'
					"/>
					</td>
				</tr>

				<tr>
					<th>
						展示顺序
					</th>
					<td>
						<input id="limitTime6_orders"   name="limitTime6_orders" class="easyui-numberbox span2" style="width:100px;" data-options="
							required:false,
							precision:0
					"/>
					</td>
				</tr>
				<!--     activity7 开始-->

				<tr>
					<th>
						<font color="#FF0000">是否有活动</font>
					</th>
					<td>
						<input id="activity7_show_type"  name="activity7_show_type" class="easyui-combobox span2" data-options="
                                                required:false,
                                                editable:false,
                                                valueField: 'code',
                                                textField: 'name',
                                                data : [{
                                                        code: '0', name: '不展示'
                                                    }, {
                                                        code: '1', name: '展示'
                                                    }
                                                ]
                                            "/>
					</td>
				</tr>
				<tr>
					<th>
						展示图片
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="activity7_showPic"   name="activity7_showPic" class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[150]'
					"/>
					</td>
				</tr>

				<tr>
					<td>
						<div class="messager-p-msg">
							<span>英文+数字:</span>
							<input multiple="false" type="file"
								   onchange="upload(this,'activity7_showPic');"
								   name="myfile"
								   class="file"
								   style="position: relative;left: 55px;top: -23px;"/>
						</div>

					</td>
					<td>
						<span class="imgArea"><img class="tpl_18_picUrl_img" src=""/></span><span
							class="preview">图片名纯英文和数字</span>
					</td>
				</tr>
				<tr>
					<th>
						跳转地址
					</th>
					<td>
						<input onkeyup="this.value=this.value.replace(/\s+/g,'')" id="activity7_goUrl"  name="activity7_goUrl"  class="easyui-validatebox span2" style="width:250px;" data-options="
							required:false,
							validType:'maxLength[500]'
					"/>
					</td>
				</tr>
				<tr>
					<th>
						展示顺序
					</th>
					<td>
						<input id="activity7_orders"  name="activity7_orders"   class="easyui-numberbox span2" style="width:100px;" data-options="
							required:false,
							precision:0
					"/>
					</td>
				</tr>
			</table>

		</form>
	</div>
</div>
