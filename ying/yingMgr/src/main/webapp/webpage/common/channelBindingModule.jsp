<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!-- 渠道绑定模块 - 可复用的渠道绑定功能 -->
<!-- 使用方式：
    jsp:include page="../common/channelBindingModule.jsp"
        jsp:param name="moduleId" value="uniqueModuleId"
        jsp:param name="targetType" value="BACKGROUND_PIC"
        jsp:param name="targetDataGridId" value="dataGrid"
        jsp:param name="onBindingSuccess" value="onBindingSuccessCallback"
    /jsp:include
-->

<script type="text/javascript">
    // 获取模块参数
    var channelBindingModuleId = '${param.moduleId}' || 'defaultChannelBinding';
    var channelBindingTargetType = '${param.targetType}' || 'BACKGROUND_PIC';
    var channelBindingTargetDataGridId = '${param.targetDataGridId}' || 'dataGrid';
    var channelBindingOnSuccess = '${param.onBindingSuccess}' || '';
    
    // 模块化变量，避免全局污染
    var channelBindingModule = {
        moduleId: channelBindingModuleId,
        targetType: channelBindingTargetType,
        targetDataGridId: channelBindingTargetDataGridId,
        onSuccess: channelBindingOnSuccess,
        selectedTargetIds: [],
        lastSelectedChannels: [],
        channelBindingGrid: null,
        bindingRelationGrid: null
    };
    
    // 初始化渠道绑定模块
    function initChannelBindingModule() {
        console.log('初始化渠道绑定模块:', channelBindingModule.moduleId);
        
        // 绑定事件到目标数据表格
        var targetGrid = $('#' + channelBindingModule.targetDataGridId);
        if (targetGrid.length > 0) {
            // 延迟添加操作列，确保表格已完全初始化
            setTimeout(function() {
                addOperationColumn(targetGrid);
            }, 500);
        }
    }
    
    // 添加操作列到目标数据表格
    function addOperationColumn(targetGrid) {
        // 检查表格是否已初始化
        if (!targetGrid.length || !targetGrid.datagrid('options')) {
            console.log('目标表格未初始化，跳过添加操作列');
            return;
        }
        
        var columns = targetGrid.datagrid('options').columns[0];
        var hasOperationColumn = false;
        
        // 检查是否已有操作列
        for (var i = 0; i < columns.length; i++) {
            if (columns[i].field === 'operation' || columns[i].title === '操作') {
                hasOperationColumn = true;
                break;
            }
        }
        
        // 如果没有操作列，添加一个
        if (!hasOperationColumn) {
            columns.push({
                title: '操作',
                field: 'operation',
                width: 120,
                align: 'center',
                formatter: function(value, row) {
                    return '<a href="#" onclick="channelBindingModule.bindChannel(' + row.id + ')">关联渠道</a> | ' +
                           '<a href="#" onclick="channelBindingModule.viewBindings(' + row.id + ')">查看绑定</a>';
                }
            });
            
            // 重新加载表格
            targetGrid.datagrid('reload');
        }
    }
    
    // 批量关联渠道
    channelBindingModule.configTargetChannel = function() {
        var targetGrid = $('#' + channelBindingModule.targetDataGridId);
        var selRows = targetGrid.datagrid('getChecked');
        
        if (selRows == null || selRows.length == 0) {
            $.messager.alert('提示', '请选择要关联渠道的数据!', 'warning');
            return;
        }
        
        // 限制只能选中一条记录
        if (selRows.length > 1) {
            $.messager.alert('提示', '只能选择一条记录进行渠道绑定!', 'warning');
            return;
        }
        
        // 收集选中的目标数据ID
        channelBindingModule.selectedTargetIds = [];
        selRows.forEach(function(row) {
            channelBindingModule.selectedTargetIds.push(row.id);
        });
        
        // 打开渠道选择对话框
        channelBindingModule.openChannelSelectionDialog();
    };
    
    // 单个数据关联渠道
    channelBindingModule.bindChannel = function(targetId) {
        channelBindingModule.selectedTargetIds = [targetId];
        channelBindingModule.openChannelSelectionDialog();
    };
    
    // 查看绑定关系
    channelBindingModule.viewBindings = function(targetId) {
        channelBindingModule.openBindingRelationDialog(targetId);
    };
    
    // 打开渠道选择对话框
    channelBindingModule.openChannelSelectionDialog = function() {
        // 显示选中的目标数据ID
        $('#selectedTargetIdsDisplay_' + channelBindingModule.moduleId).text(channelBindingModule.selectedTargetIds.join(', '));
        
        // 清空上次的选择记录
        channelBindingModule.lastSelectedChannels = [];
        
        // 先清空表格，避免显示旧数据
        if (channelBindingModule.channelBindingGrid) {
            channelBindingModule.channelBindingGrid.datagrid('loadData', []);
            channelBindingModule.channelBindingGrid.datagrid('uncheckAll');
            // 重置分页到第一页
            channelBindingModule.channelBindingGrid.datagrid('options').pageNumber = 1;
        }
        
        // 先显示对话框元素并移除隐藏样式
        var dialogElement = $('#channelSelectionDialog_' + channelBindingModule.moduleId);
        dialogElement.css({
            'display': 'block',
            'position': 'static',
            'top': 'auto'
        });
        
        dialogElement.dialog({
            title: '选择渠道',
            width: 900,
            height: 600,
            closed: false,
            cache: false,
            modal: true,
            resizable: true,
            maximizable: true,
            onOpen: function() {
                console.log('对话框完全打开，开始初始化表格');
                // 延迟初始化表格，确保对话框完全渲染
                setTimeout(function() {
                    channelBindingModule.initChannelSelectionGrid();
                    // 强制重置分页到第一页
                    setTimeout(function() {
                        if (channelBindingModule.channelBindingGrid) {
                            channelBindingModule.channelBindingGrid.datagrid('options').pageNumber = 1;
                            channelBindingModule.channelBindingGrid.datagrid('reload');
                        }
                    }, 100);
                }, 300);
            },
            onResize: function() {
                // 对话框大小改变时刷新表格
                if (channelBindingModule.channelBindingGrid) {
                    channelBindingModule.channelBindingGrid.datagrid('resize');
                }
            },
            buttons: [{
                text: '确定绑定',
                handler: function() {
                    channelBindingModule.executeChannelBinding();
                }
            }, {
                text: '解绑所有',
                handler: function() {
                    // 清空所有选择
                    channelBindingModule.channelBindingGrid.datagrid('uncheckAll');
                    channelBindingModule.executeChannelBinding();
                }
            }, {
                text: '取消',
                handler: function() {
                    $('#channelSelectionDialog_' + channelBindingModule.moduleId).dialog('close');
                }
            }]
        });
    };
    
    // 初始化渠道选择表格
    channelBindingModule.initChannelSelectionGrid = function() {
        console.log('开始初始化渠道选择表格');
        var tableElement = $('#channelSelectionTable_' + channelBindingModule.moduleId);
        console.log('表格元素:', tableElement.length);
        console.log('表格元素可见性:', tableElement.is(':visible'));
        
        if (tableElement.length === 0) {
            console.log('表格元素不存在，延迟重试');
            setTimeout(function() {
                channelBindingModule.initChannelSelectionGrid();
            }, 200);
            return;
        }
        
        // 如果表格已经初始化，先销毁
        if (channelBindingModule.channelBindingGrid) {
            console.log('销毁已存在的表格');
            try {
                channelBindingModule.channelBindingGrid.datagrid('destroy');
            } catch (e) {
                console.log('销毁表格时出错，继续初始化:', e);
            }
        }
        
        console.log('开始创建新的DataGrid');
        channelBindingModule.channelBindingGrid = tableElement.datagrid({
            url: '${pageContext.request.contextPath}/channelBinding/getChannelList.action',
            fit: true,
            fitColumns: false,
            border: true,
            pagination: true,
            idField: 'id',
            pageSize: 15,
            pageList: [10, 15, 20, 30, 40, 50],
            pageNumber: 1,  // 确保从第一页开始
            sortName: 'id',
            sortOrder: 'desc',
            checkOnSelect: true,
            selectOnCheck: true,
            nowrap: false,
            striped: true,
            rownumbers: true,
            loadMsg: '正在加载渠道数据...',
            autoRowHeight: false,
            onLoadSuccess: function(data) {
                console.log('渠道数据加载完成，数据:', data);
                console.log('数据行数:', data.rows ? data.rows.length : 0);
                if (data.rows && data.rows.length > 0) {
                    console.log('第一行数据:', data.rows[0]);
                }
                // 确保分页显示正确
                var pager = channelBindingModule.channelBindingGrid.datagrid('getPager');
                if (pager) {
                    pager.pagination('refresh');
                }
                setTimeout(function() {
                    channelBindingModule.preSelectBoundChannels();
                }, 50);
            },
            onLoadError: function(xhr, status, error) {
                console.log('渠道数据加载失败:', status, error);
                console.log('响应内容:', xhr.responseText);
            },
            columns: [[
                {
                    field: 'ck',
                    checkbox: true,
                    hidden: false
                },
                {
                    title: '渠道ID',
                    field: 'id',
                    width: 80,
                    align: 'center'
                },
                {
                    title: '渠道名称',
                    field: 'channelName',
                    width: 200,
                    align: 'left'
                },
                {
                    title: '渠道参数',
                    field: 'channelParam',
                    width: 150,
                    align: 'left'
                },
                {
                    title: '市场ID',
                    field: 'marketId',
                    width: 120,
                    align: 'center'
                },
                {
                    title: '创建人',
                    field: 'creater',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '创建时间',
                    field: 'createTime',
                    width: 180,
                    align: 'center',
                    formatter: function(value) {
                        if (value) {
                            return new Date(value).toLocaleString();
                        }
                        return '';
                    }
                }
            ]]
        });
        
        // 强制加载数据
        console.log('强制加载数据');
        try {
            // 使用reload方法替代load方法，更安全
            channelBindingModule.channelBindingGrid.datagrid('reload');
        } catch (e) {
            console.error('加载数据失败:', e);
            // 如果reload失败，尝试使用load方法
            try {
                channelBindingModule.channelBindingGrid.datagrid('load');
            } catch (e2) {
                console.error('load方法也失败:', e2);
            }
        }
        
        // 延迟刷新表格布局
        setTimeout(function() {
            if (channelBindingModule.channelBindingGrid) {
                channelBindingModule.channelBindingGrid.datagrid('resize');
                console.log('表格布局已刷新');
            }
        }, 100);
    };
    
    // 预选已绑定的渠道
    channelBindingModule.preSelectBoundChannels = function() {
        if (channelBindingModule.selectedTargetIds.length === 0) return;
        
        console.log('预选渠道 - 选中的目标数据ID:', channelBindingModule.selectedTargetIds);
        console.log('预选渠道 - 上次选择的渠道:', channelBindingModule.lastSelectedChannels);
        
        // 确保表格已加载完成
        try {
            var rows = channelBindingModule.channelBindingGrid ? channelBindingModule.channelBindingGrid.datagrid('getRows') : null;
            if (!channelBindingModule.channelBindingGrid || !rows) {
                console.log('表格数据未加载完成，延迟执行预选');
                setTimeout(function() {
                    channelBindingModule.preSelectBoundChannels();
                }, 100);
                return;
            }
        } catch (e) {
            console.log('获取表格数据时出错，延迟执行预选:', e);
            setTimeout(function() {
                channelBindingModule.preSelectBoundChannels();
            }, 100);
            return;
        }
        
        // 先清空所有选择
        channelBindingModule.channelBindingGrid.datagrid('uncheckAll');
        
        // 如果有上次选择的渠道，优先使用上次的选择
        if (channelBindingModule.lastSelectedChannels.length > 0) {
            console.log('使用上次选择的渠道进行预选');
            channelBindingModule.preSelectChannelsFromSet(channelBindingModule.lastSelectedChannels);
            return;
        }
        
        // 获取所有选中目标数据的绑定关系
        console.log('从数据库获取绑定关系');
        channelBindingModule.getAllBoundChannels(channelBindingModule.selectedTargetIds);
    };
    
    // 获取所有选中目标数据的绑定渠道
    channelBindingModule.getAllBoundChannels = function(targetIds) {
        if (targetIds.length === 0) return;
        
        // 如果只有一个目标数据，直接获取其绑定关系
        if (targetIds.length === 1) {
            channelBindingModule.getSingleTargetBindings(targetIds[0]);
            return;
        }
        
        // 多个目标数据时，获取所有目标数据的绑定关系
        var allBoundChannelIds = new Set();
        var completedRequests = 0;
        var totalRequests = targetIds.length;
        
        targetIds.forEach(function(targetId) {
            $.ajax({
                url: '${pageContext.request.contextPath}/channelBinding/getBindingRelations.action',
                type: 'GET',
                data: { targetId: targetId },
                success: function(data) {
                    if (data && data.rows) {
                        data.rows.forEach(function(row) {
                            allBoundChannelIds.add(row.channelId);
                        });
                    }
                    completedRequests++;
                    if (completedRequests === totalRequests) {
                        channelBindingModule.preSelectChannelsFromSet(Array.from(allBoundChannelIds));
                    }
                },
                error: function() {
                    completedRequests++;
                    if (completedRequests === totalRequests) {
                        channelBindingModule.preSelectChannelsFromSet(Array.from(allBoundChannelIds));
                    }
                }
            });
        });
    };
    
    // 获取单个目标数据的绑定关系
    channelBindingModule.getSingleTargetBindings = function(targetId) {
        $.ajax({
            url: '${pageContext.request.contextPath}/channelBinding/getBindingRelations.action',
            type: 'GET',
            data: { targetId: targetId },
            success: function(data) {
                console.log('目标数据', targetId, '的绑定关系:', data);
                if (data && data.rows) {
                    var boundChannelIds = [];
                    data.rows.forEach(function(row) {
                        boundChannelIds.push(row.channelId);
                    });
                    console.log('目标数据', targetId, '绑定的渠道ID:', boundChannelIds);
                    channelBindingModule.preSelectChannelsFromSet(boundChannelIds);
                } else {
                    console.log('目标数据', targetId, '没有绑定任何渠道');
                    channelBindingModule.preSelectChannelsFromSet([]);
                }
            },
            error: function() {
                console.log('获取绑定关系失败，ID: ' + targetId);
            }
        });
    };
    
    // 根据渠道ID集合预选渠道
    channelBindingModule.preSelectChannelsFromSet = function(boundChannelIds) {
        console.log('预选渠道，渠道ID列表:', boundChannelIds);
        
        // 确保表格已加载完成
        try {
            var rows = channelBindingModule.channelBindingGrid ? channelBindingModule.channelBindingGrid.datagrid('getRows') : null;
            if (!channelBindingModule.channelBindingGrid || !rows) {
                console.log('表格数据未加载完成，延迟执行预选');
                setTimeout(function() {
                    channelBindingModule.preSelectChannelsFromSet(boundChannelIds);
                }, 100);
                return;
            }
        } catch (e) {
            console.log('获取表格数据时出错，延迟执行预选:', e);
            setTimeout(function() {
                channelBindingModule.preSelectChannelsFromSet(boundChannelIds);
            }, 100);
            return;
        }
        
        // 先清空所有选择
        channelBindingModule.channelBindingGrid.datagrid('uncheckAll');
        
        if (boundChannelIds && boundChannelIds.length > 0) {
            var selectedCount = 0;
            channelBindingModule.channelBindingGrid.datagrid('getRows').forEach(function(row) {
                if (boundChannelIds.indexOf(row.id) !== -1) {
                    var rowIndex = channelBindingModule.channelBindingGrid.datagrid('getRowIndex', row);
                    channelBindingModule.channelBindingGrid.datagrid('checkRow', rowIndex);
                    selectedCount++;
                    console.log('预选渠道，ID:', row.id, '名称:', row.channelName);
                }
            });
            console.log('预选完成，共选中', selectedCount, '个渠道');
        } else {
            console.log('没有渠道需要预选');
        }
    };
    
    // 执行渠道绑定
    channelBindingModule.executeChannelBinding = function() {
        var selChannelRows = channelBindingModule.channelBindingGrid.datagrid('getChecked');
        var channelIds = [];
        
        // 允许不选择任何渠道，表示解绑所有渠道
        if (selChannelRows != null && selChannelRows.length > 0) {
            selChannelRows.forEach(function(row) {
                channelIds.push(row.id);
            });
        }
        
        // 记录用户当前的选择
        channelBindingModule.lastSelectedChannels = channelIds.slice();
        
        var params = {
            targetIds: channelBindingModule.selectedTargetIds.join(";"),
            channelIds: channelIds.join(";"),
            targetType: channelBindingModule.targetType
        };
        
        // 根据是否选择渠道显示不同的确认信息
        var confirmMessage = channelIds.length > 0 ? 
            '确定要绑定选中的渠道吗？' : 
            '确定要解绑所有渠道吗？';
            
        $.messager.confirm('确认', confirmMessage, function(r) {
            if (r) {
                $.ajax({
                    url: '${pageContext.request.contextPath}/channelBinding/bind.action',
                    type: 'POST',
                    data: params,
                    success: function(json) {
                        if (json.success) {
                            var successMessage = channelIds.length > 0 ? '绑定成功!' : '解绑成功!';
                            $.messager.alert('提示', successMessage, 'info');
                            $('#channelSelectionDialog_' + channelBindingModule.moduleId).dialog('close');
                            
                            // 执行成功回调
                            if (channelBindingModule.onSuccess && window[channelBindingModule.onSuccess]) {
                                window[channelBindingModule.onSuccess](channelBindingModule.selectedTargetIds, channelIds);
                            }
                        } else {
                            $.messager.alert('提示', json.msg || '操作失败!', 'error');
                        }
                    },
                    error: function() {
                        $.messager.alert('提示', '操作失败!', 'error');
                    }
                });
            }
        });
    };
    
    // 打开绑定关系对话框
    channelBindingModule.openBindingRelationDialog = function(targetId) {
        $('#bindingRelationDialog_' + channelBindingModule.moduleId).dialog({
            title: '绑定关系',
            width: 900,
            height: 600,
            closed: false,
            cache: false,
            modal: true,
            onOpen: function() {
                channelBindingModule.loadBindingRelations(targetId);
            },
            buttons: [{
                text: '关闭',
                handler: function() {
                    $('#bindingRelationDialog_' + channelBindingModule.moduleId).dialog('close');
                }
            }]
        });
    };
    
    // 加载绑定关系
    channelBindingModule.loadBindingRelations = function(targetId) {
        channelBindingModule.bindingRelationGrid = $('#bindingRelationTable_' + channelBindingModule.moduleId).datagrid({
            url: '${pageContext.request.contextPath}/channelBinding/getBindingRelations.action',
            queryParams: { targetId: targetId },
            fit: true,
            fitColumns: false,
            border: false,
            pagination: true,
            idField: 'id',
            pageSize: 15,
            pageList: [10, 15, 20, 30, 40, 50],
            sortName: 'id',
            sortOrder: 'desc',
            nowrap: true,
            striped: true,
            rownumbers: true,
            columns: [[
                {
                    title: '绑定ID',
                    field: 'id',
                    width: 80,
                    align: 'center'
                },
                {
                    title: '目标数据ID',
                    field: 'targetId',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '目标数据名称',
                    field: 'targetName',
                    width: 150,
                    align: 'left'
                },
                {
                    title: '渠道ID',
                    field: 'channelId',
                    width: 80,
                    align: 'center'
                },
                {
                    title: '渠道名称',
                    field: 'channelName',
                    width: 150,
                    align: 'left'
                },
                {
                    title: '绑定类型',
                    field: 'bindingType',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '状态',
                    field: 'status',
                    width: 80,
                    align: 'center',
                    formatter: function(value) {
                        return value === 1 ? '启用' : '禁用';
                    }
                },
                {
                    title: '创建人',
                    field: 'creator',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '创建时间',
                    field: 'createTime',
                    width: 150,
                    align: 'center',
                    formatter: function(value) {
                        if (value) {
                            return new Date(value).toLocaleString();
                        }
                        return '';
                    }
                },
                {
                    title: '操作',
                    field: 'operation',
                    width: 100,
                    align: 'center',
                    formatter: function(value, row) {
                        return '<a href="#" onclick="channelBindingModule.unbindChannel(' + row.id + ')">解绑</a>';
                    }
                }
            ]],
            onLoadSuccess: function(data) {
                console.log('绑定关系加载成功，数据:', data);
            },
            onLoadError: function() {
                console.error('绑定关系加载失败');
            }
        });
    };
    
    // 解绑渠道
    channelBindingModule.unbindChannel = function(bindingId) {
        $.messager.confirm('确认', '确定要解绑该渠道吗？', function(r) {
            if (r) {
                $.ajax({
                    url: '${pageContext.request.contextPath}/channelBinding/unbind.action',
                    type: "post",
                    dataType: 'json',
                    data: { bindingId: bindingId },
                    success: function (json) {
                        if (json.success) {
                            $.messager.alert('提示', '解绑成功!', 'info');
                            // 刷新绑定关系表格
                            $('#bindingRelationTable_' + channelBindingModule.moduleId).datagrid('reload');
                        } else {
                            $.messager.alert('提示', json.msg || '解绑失败!', 'error');
                        }
                    },
                    error: function() {
                        $.messager.alert('提示', '解绑失败!', 'error');
                    }
                });
            }
        });
    };
    
    // 页面加载完成后初始化模块
    $(document).ready(function() {
        initChannelBindingModule();
    });
</script>

<!-- 渠道选择对话框 -->
<div id="channelSelectionDialog_${param.moduleId}" style="display: none; position: absolute; top: -9999px;">
    <div style="padding: 10px; height: 100%;">
        <p>选中的目标数据ID: <span id="selectedTargetIdsDisplay_${param.moduleId}"></span></p>
        <p style="color: #666; font-size: 12px; margin: 5px 0;">
            提示：选择渠道进行绑定，或点击"解绑所有"按钮解绑所有渠道
        </p>
        <div style="height: calc(100% - 60px);">
            <table id="channelSelectionTable_${param.moduleId}" style="width: 100%; height: 100%;"></table>
        </div>
    </div>
</div>

<!-- 绑定关系对话框 -->
<div id="bindingRelationDialog_${param.moduleId}" style="display: none;">
    <div style="padding: 10px;">
        <table id="bindingRelationTable_${param.moduleId}" style="width: 100%; height: 400px;"></table>
    </div>
</div>
