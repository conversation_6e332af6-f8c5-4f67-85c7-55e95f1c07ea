<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creditease.ying.yingMgr.service.vchannelconfig.dao.TVChannelConfigMapper">
  <resultMap id="BaseResultMap" type="com.creditease.ying.yingMgr.service.vchannelconfig.bean.TVChannelConfig">
    <id column="CONFIG_ID" jdbcType="INTEGER" property="configId" />
    <result column="CONFIG_NAME" jdbcType="VARCHAR" property="configName" />
    <result column="CONFIG_VALUE" jdbcType="VARCHAR" property="configValue" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ONLINE_TIME" jdbcType="TIMESTAMP" property="onlineTime" />
    <result column="OFFLINE_TIME" jdbcType="TIMESTAMP" property="offlineTime" />
    <result column="VERSION_LIMIT" jdbcType="VARCHAR" property="versionLimit" />
    <result column="VERSION_MAX_LIMIT" jdbcType="VARCHAR" property="versionMaxLimit" />
    <result column="unselected_icon_url" jdbcType="VARCHAR" property="unselectedIconUrl" />
    <result column="selected_icon_url" jdbcType="VARCHAR" property="selectedIconUrl" />
    <result column="selected_char_color" jdbcType="VARCHAR" property="selectedCharColor" />
    <result column="tab_h5_url" jdbcType="VARCHAR" property="tabH5Url" />
    <result column="open_status" jdbcType="INTEGER" property="openStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    CONFIG_ID, CONFIG_NAME, CONFIG_VALUE, CREATE_TIME, UPDATE_TIME, ONLINE_TIME, OFFLINE_TIME, 
    VERSION_LIMIT, VERSION_MAX_LIMIT, unselected_icon_url, selected_icon_url, selected_char_color, 
    tab_h5_url, open_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_v_channel_config
    where CONFIG_ID = #{configId,jdbcType=INTEGER}
  </select>
  <select id="selectPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_v_channel_config
    where 1=1
    <if test="configId != null and configId != ''">
      and CONFIG_ID = #{configId}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_v_channel_config
    where CONFIG_ID = #{configId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.TVChannelConfig">
    insert into t_v_channel_config (CONFIG_ID, CONFIG_NAME, CONFIG_VALUE, 
      CREATE_TIME, UPDATE_TIME, ONLINE_TIME, 
      OFFLINE_TIME, VERSION_LIMIT, VERSION_MAX_LIMIT, 
      unselected_icon_url, selected_icon_url, selected_char_color, 
      tab_h5_url, open_status)
    values (#{configId,jdbcType=INTEGER}, #{configName,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{onlineTime,jdbcType=TIMESTAMP}, 
      #{offlineTime,jdbcType=TIMESTAMP}, #{versionLimit,jdbcType=VARCHAR}, #{versionMaxLimit,jdbcType=VARCHAR}, 
      #{unselectedIconUrl,jdbcType=VARCHAR}, #{selectedIconUrl,jdbcType=VARCHAR}, #{selectedCharColor,jdbcType=VARCHAR}, 
      #{tabH5Url,jdbcType=VARCHAR}, #{openStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.TVChannelConfig">
    insert into t_v_channel_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        CONFIG_ID,
      </if>
      <if test="configName != null">
        CONFIG_NAME,
      </if>
      <if test="configValue != null">
        CONFIG_VALUE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="onlineTime != null">
        ONLINE_TIME,
      </if>
      <if test="offlineTime != null">
        OFFLINE_TIME,
      </if>
      <if test="versionLimit != null">
        VERSION_LIMIT,
      </if>
      <if test="versionMaxLimit != null">
        VERSION_MAX_LIMIT,
      </if>
      <if test="unselectedIconUrl != null">
        unselected_icon_url,
      </if>
      <if test="selectedIconUrl != null">
        selected_icon_url,
      </if>
      <if test="selectedCharColor != null">
        selected_char_color,
      </if>
      <if test="tabH5Url != null">
        tab_h5_url,
      </if>
      <if test="openStatus != null">
        open_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        #{configId,jdbcType=INTEGER},
      </if>
      <if test="configName != null">
        #{configName,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlineTime != null">
        #{onlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="offlineTime != null">
        #{offlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="versionLimit != null">
        #{versionLimit,jdbcType=VARCHAR},
      </if>
      <if test="versionMaxLimit != null">
        #{versionMaxLimit,jdbcType=VARCHAR},
      </if>
      <if test="unselectedIconUrl != null">
        #{unselectedIconUrl,jdbcType=VARCHAR},
      </if>
      <if test="selectedIconUrl != null">
        #{selectedIconUrl,jdbcType=VARCHAR},
      </if>
      <if test="selectedCharColor != null">
        #{selectedCharColor,jdbcType=VARCHAR},
      </if>
      <if test="tabH5Url != null">
        #{tabH5Url,jdbcType=VARCHAR},
      </if>
      <if test="openStatus != null">
        #{openStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.TVChannelConfig">
    update t_v_channel_config
    <set>
      <if test="configName != null">
        CONFIG_NAME = #{configName,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        CONFIG_VALUE = #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlineTime != null">
        ONLINE_TIME = #{onlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="offlineTime != null">
        OFFLINE_TIME = #{offlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="versionLimit != null">
        VERSION_LIMIT = #{versionLimit,jdbcType=VARCHAR},
      </if>
      <if test="versionMaxLimit != null">
        VERSION_MAX_LIMIT = #{versionMaxLimit,jdbcType=VARCHAR},
      </if>
      <if test="unselectedIconUrl != null">
        unselected_icon_url = #{unselectedIconUrl,jdbcType=VARCHAR},
      </if>
      <if test="selectedIconUrl != null">
        selected_icon_url = #{selectedIconUrl,jdbcType=VARCHAR},
      </if>
      <if test="selectedCharColor != null">
        selected_char_color = #{selectedCharColor,jdbcType=VARCHAR},
      </if>
      <if test="tabH5Url != null">
        tab_h5_url = #{tabH5Url,jdbcType=VARCHAR},
      </if>
      <if test="openStatus != null">
        open_status = #{openStatus,jdbcType=INTEGER},
      </if>
    </set>
    where CONFIG_ID = #{configId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.TVChannelConfig">
    update t_v_channel_config
    set CONFIG_NAME = #{configName,jdbcType=VARCHAR},
      CONFIG_VALUE = #{configValue,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ONLINE_TIME = #{onlineTime,jdbcType=TIMESTAMP},
      OFFLINE_TIME = #{offlineTime,jdbcType=TIMESTAMP},
      VERSION_LIMIT = #{versionLimit,jdbcType=VARCHAR},
      VERSION_MAX_LIMIT = #{versionMaxLimit,jdbcType=VARCHAR},
      unselected_icon_url = #{unselectedIconUrl,jdbcType=VARCHAR},
      selected_icon_url = #{selectedIconUrl,jdbcType=VARCHAR},
      selected_char_color = #{selectedCharColor,jdbcType=VARCHAR},
      tab_h5_url = #{tabH5Url,jdbcType=VARCHAR},
      open_status = #{openStatus,jdbcType=INTEGER}
    where CONFIG_ID = #{configId,jdbcType=INTEGER}
  </update>
</mapper>