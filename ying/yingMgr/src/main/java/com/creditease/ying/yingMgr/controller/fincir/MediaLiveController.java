package com.creditease.ying.yingMgr.controller.fincir;

import com.creditease.ying.fincir.bean.FincirLiveBarrageEntity;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import redis.clients.jedis.JedisCluster;

import com.creditease.homepage.enums.PrizeTypeEnum;
import com.creditease.homepage.enums.SourceTypeEnum;
import com.creditease.homepage.enums.TaskTypeEnum;
import com.creditease.ying.api.support.SupportServiceApi;
import com.creditease.ying.api.support.bean.GoodsInfo;
import com.creditease.ying.common.model.DataModel;
import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.common.util.BeanUtil;
import com.creditease.ying.common.util.ConstantsForRedis;
import com.creditease.ying.common.util.DateUtil;
import com.creditease.ying.common.util.HtmlCharacterEscapeUtils;
import com.creditease.ying.common.util.HttpClientUtils;
import com.creditease.ying.common.util.Json;
import com.creditease.ying.common.util.JsonUtil;
import com.creditease.ying.common.util.ReturnJsonUtil;
import com.creditease.ying.common.util.StringUtil;
import com.creditease.ying.common.util.UuidUtil;
import com.creditease.ying.common.util.report.ExportExcel;
import com.creditease.ying.fincir.bean.ArticleCommentBean;
import com.creditease.ying.fincir.bean.EnumPO;
import com.creditease.ying.fincir.bean.FincirContentTagRelationPO;
import com.creditease.ying.fincir.bean.FincirLiveTaskPO;
import com.creditease.ying.fincir.bean.FincirLiveTaskVO;
import com.creditease.ying.fincir.bean.GuestDesBean;
import com.creditease.ying.fincir.bean.MediaDataBean;
import com.creditease.ying.fincir.bean.MediaLiveBean;
import com.creditease.ying.fincir.bean.MediaLiveReq;
import com.creditease.ying.fincir.bean.MediaPosterBean;
import com.creditease.ying.fincir.bean.SubjectBean;
import com.creditease.ying.fincir.bean.SubjectMediaBean;
import com.creditease.ying.fincir.bean.TLiveChannel;
import com.creditease.ying.fincir.bean.UserActivityBean;
import com.creditease.ying.fincir.dao.IMediaPosterDao;
import com.creditease.ying.fincir.dao.ISubjectDao;
import com.creditease.ying.fincir.dao.MediaDataDao;
import com.creditease.ying.fincir.dao.MediaLiveDao;
import com.creditease.ying.fincir.enums.PosterPublishStatusTypeEnum;
import com.creditease.ying.fincir.enums.StatusEnum;
import com.creditease.ying.fincir.service.impl.FincirLiveBarrageService;
import com.creditease.ying.fincir.mapper.TLiveChannelMapper;
import com.creditease.ying.fincir.mapper.TLivePayUserMapper;
import com.creditease.ying.fincir.service.IArticleCommentService;
import com.creditease.ying.fincir.service.IFincirContentTagRelationService;
import com.creditease.ying.fincir.service.IFincirLiveSegmentsService;
import com.creditease.ying.fincir.service.IFincirLiveTaskService;
import com.creditease.ying.fincir.service.IFincirRedisService;
import com.creditease.ying.fincir.service.IMediaService;
import com.creditease.ying.fincir.service.ISubjectService;
import com.creditease.ying.fincir.service.SubjectMediaService;
import com.creditease.ying.fincir.vo.MallProductListVo;
import com.creditease.ying.fincir.vo.MallProductVo;
import com.creditease.ying.fincir.vo.TLiveChannelContentListReq;
import com.creditease.ying.fincir.vo.TLiveChannelContentListRes;
import com.creditease.ying.fincir.vo.TLiveChannelContentRemoveReq;
import com.creditease.ying.fincir.vo.TLiveChannelCreateReq;
import com.creditease.ying.fincir.vo.TLiveChannelListByTypeRes;
import com.creditease.ying.fincir.vo.TLiveChannelPageReq;
import com.creditease.ying.fincir.vo.TLiveChannelPageRes;
import com.creditease.ying.fincir.vo.TLiveChannelUpdateContentOrderReq;
import com.creditease.ying.fincir.vo.TLiveChannelUpdateReq;
import com.creditease.ying.fincir.vo.TLiveChannelValidSkuIdReq;
import com.creditease.ying.push.service.IPushMessageService;
import com.creditease.ying.transaction.thread.LiveOrderRemindPushThread;
import com.creditease.ying.user.bean.UserBean;
import com.creditease.ying.util.Constants;
import com.creditease.ying.yingMgr.controller.BaseController;
import com.creditease.ying.yingMgr.service.fincir.MediaLiveService;
import java.time.LocalDateTime;

import static com.creditease.ying.fincir.enums.SubjectMediaTypeEnum.LIVE;

/**
 * @Author: HT
 * @Description:
 * @Date:Created in 11:13 2019-06-04
 **/
@Controller
@RequestMapping("/mediaLive")
public class MediaLiveController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(MediaLiveController.class);

    @Autowired
    MediaLiveDao mediaLiveDao;
    @Autowired
    MediaDataDao mediaDataDao;
    @Autowired
    IMediaPosterDao mediaPosterDao;
    @Autowired
    IArticleCommentService commentService;
    @Autowired
    private IArticleCommentService articleCommentService;
    @Autowired
    private IMediaService mediaService;
    @Autowired
    private IPushMessageService pushMessageService;
    @Autowired
    private JedisCluster jedisCluster;
    @Autowired
    private ISubjectDao subjectDao;
    @Autowired
    private ISubjectService subjectService;
    @Autowired
    private IFincirLiveTaskService liveTaskService;
    @Autowired
    private SubjectMediaService subjectMediaService;
    @Autowired
    private IFincirLiveSegmentsService liveSegmentsService;
    @Autowired
    private IFincirContentTagRelationService contentTagRelationService;
    @Autowired
    private SupportServiceApi supportServiceApi;
    @Autowired
    private MediaLiveService mediaLiveService;
    @Autowired
    private FincirLiveBarrageService fincirLiveBarrageService;

    @Value("${lifeserver.url}")
    private String lifeServerUrl;
    @Value("${lifeserver.channelCode}")
    private String lifeServerChannelCodes;
    @Value("${lifeserver.storeId}")
    private String lifeServerStoreId;
    @Value("${h5.url}")
    private String h5Url;

    private static final String REDIS_SUCCESS = "OK";

    /**
     * 管理页面
     *
     * @param request
     * @return
     */
    @RequestMapping("/manager")
    public String manager(HttpServletRequest request) {
        request.setAttribute("h5Url", h5Url);
        return "/webpage/fincir/media/liveList";
    }

    /**
     * 页面DateGrid
     */
    @RequestMapping("/dataGrid")
    @ResponseBody
    public DataGrid dataGrid(MediaLiveBean mediaLiveBean) {
        DataGrid dataGrid = new DataGrid();
        List<MediaLiveBean> list = mediaLiveDao.getLiveListWithData(mediaLiveBean);
        for (MediaLiveBean bean : list) {
            bean.setStatus(bean.getStatus() <= 0 ? bean.getStatus() : bean.getStartTime().getTime() > new Date().getTime() ? 2 : bean.getEndTime() == null || bean.getEndTime().getTime() > new Date().getTime() ? 1 : 3);
            SubjectMediaBean subject = new SubjectMediaBean();
            subject.setMediaId(bean.getId());
            subject.setMediaType(LIVE.getSubjectMediaType());
            String subjectId = subjectDao.getSubjectIdsByMediaId(subject);
            if (StringUtils.isNotBlank(subjectId) && !"none".equals(subjectId)) {
                List<String> subjectIdList = Arrays.asList(subjectId.split(","));
                StringBuilder subjectTitles = new StringBuilder();
                List<SubjectBean> subjectBeanList = subjectService.getSubjectListByIdList(subjectIdList);
                for (SubjectBean subjectBean : subjectBeanList) {
                    subjectTitles.append(subjectBean.getSubjectTitle()).append(",");
                }
                bean.setSubjectId(subjectTitles.toString().substring(0, subjectTitles.length() - 1));
            }
            //设置是否有福利活动
            FincirLiveTaskPO paramTask = new FincirLiveTaskPO();
            paramTask.setLiveId(bean.getId());
            List<FincirLiveTaskPO> taskPOS = liveTaskService.getFincirLiveTaskPOList(paramTask);
            bean.setIsHaveTask((taskPOS != null && taskPOS.size() > 0) ? "1" : "0");

        }
        dataGrid.setRows(list);
        dataGrid.setTotal(Long.parseLong(String.valueOf(mediaLiveDao.countList(mediaLiveBean))));
        return dataGrid;
    }

    @RequestMapping("/toDetailPage")
    public String toDetailPage(HttpServletRequest request, String liveId) {
        MediaLiveBean liveBean = mediaLiveDao.getLiveInfo(liveId);
        MediaLiveReq req = new MediaLiveReq();

        req.setId(liveBean.getId());
        req.setArticleTitle(liveBean.getArticleTitle());
        SubjectMediaBean bean = new SubjectMediaBean();
        bean.setMediaType(4);
        bean.setMediaId(liveBean.getId());
        String subjectId = subjectDao.getSubjectIdsByMediaId(bean);
        req.setSubjectId(subjectId);
        req.setPlayerImg(liveBean.getPlayerImg());
        req.setVerticalPlayerImg(liveBean.getVerticalPlayerImg());
        req.setPreheatUrl(liveBean.getPreheatUrl());
        req.setOrderlistUrl(liveBean.getOrderlistUrl());
        req.setImgUrl(liveBean.getImgUrl());
        req.setHeaderImgUrl(liveBean.getHeaderImgUrl());
        req.setAdImgUrl(liveBean.getAdImgUrl());
        req.setContentImg(liveBean.getContentImg());
        req.setMediaUrl(liveBean.getMediaUrl());
        req.setMediaType(liveBean.getMediaType());
        req.setStartTime(liveBean.getStartTime());
        req.setEndTime(liveBean.getEndTime());
        req.setOrderCount(liveBean.getOrderCount());
        req.setLeftPoint(liveBean.getLeftPoint());
        Map<String, Object> map = new HashMap<>();
        map = (Map<String, Object>) JsonUtil.jsonToBean(liveBean.getExtendInfo(), map.getClass());
        req.setSubTitle((String) map.get("subTitle"));
        req.setContentDes((String) map.get("contentDes"));
        req.setHistoryRecommend((String) map.get("historyRecommend"));
        req.setCodeUrl((String) map.get("codeUrl"));
        req.setCodeDes((String) map.get("codeDes"));
        req.setPaperArticleId((String) map.get("paperArticleId"));
        req.setMediaUrlForH5((String) map.get("mediaUrlForH5"));

        List<GuestDesBean> guestDesBeanList = (List<GuestDesBean>) map.get("guests");
        req.setGuestStr(JsonUtil.bean2PrettyJson(guestDesBeanList));
        req.setStartTimeStr(DateUtil.getDateLongTimePlusStr(liveBean.getStartTime()));
        req.setEndTimeStr(DateUtil.getDateLongTimePlusStr(liveBean.getEndTime()));
        req.setLeftPoint(liveBean.getLeftPoint());
        req.setReadCountScore(liveBean.getReadCountScore());
        req.setDisplayResourcePosition(liveBean.getDisplayResourcePosition());

        if(Objects.nonNull(liveBean.getChannelId())){
            TLiveChannel tLiveChannel = tLiveChannelMapper.selectByPrimaryKey(Long.parseLong(liveBean.getChannelId() + ""));
            req.setChannelType(tLiveChannel.getIsFree());
        }
        req.setFreeVipType(liveBean.getFreeVipType());
        req.setChannelId(liveBean.getChannelId());
        req.setIsFree(liveBean.getIsFree());
        req.setIsSeparateSale(liveBean.getIsSeparateSale());
        if(StringUtils.isNotEmpty(liveBean.getSkuId())){
            List<String> list = new ArrayList<>();
            list.add(liveBean.getSkuId());
            req.setPrice(getSkuIdPrice(list).get(liveBean.getSkuId()));
        }
        req.setSkuId(liveBean.getSkuId());
        req.setChannelPreviewDuration(liveBean.getChannelPreviewDuration());
        req.setChannelOrder(liveBean.getChannelOrder());
        req.setSeparateSalePreviewDuration(liveBean.getSeparateSalePreviewDuration());

        request.setAttribute("live", req);
        request.setAttribute("liveId", liveId);
        Integer status = liveBean.getStatus() <= 0 ? liveBean.getStatus() : liveBean.getStartTime().getTime() > new Date().getTime() ? 2 : liveBean.getEndTime() == null || liveBean.getEndTime().getTime() > new Date().getTime() ? 1 : 3;
        request.setAttribute("status", status);
        FincirContentTagRelationPO paramTagRelationPO = new FincirContentTagRelationPO(null, null, liveId, String.valueOf(SourceTypeEnum.LIVE_SERVICE.getSourceType()));
        List<FincirContentTagRelationPO> tagRelationPOList = contentTagRelationService.queryListByParam(paramTagRelationPO);
        request.setAttribute("contentTags", contentTagRelationService.joinTagIds(tagRelationPOList));
        return "/webpage/fincir/media/updateLive";
    }
    @RequestMapping("/detail")
    @ResponseBody
    public Map<String, Object> detail(String liveId) {
        Map<String, Object> retMap = new HashMap<>();
        MediaLiveBean liveBean = mediaLiveDao.getLiveInfo(liveId);
        MediaLiveReq req = new MediaLiveReq();

        req.setId(liveBean.getId());
        req.setArticleTitle(liveBean.getArticleTitle());
        SubjectMediaBean bean = new SubjectMediaBean();
        bean.setMediaType(4);
        bean.setMediaId(liveBean.getId());
        String subjectId = subjectDao.getSubjectIdsByMediaId(bean);
        req.setSubjectId(subjectId);
        req.setPlayerImg(liveBean.getPlayerImg());
        req.setVerticalPlayerImg(liveBean.getVerticalPlayerImg());
        req.setPreheatUrl(liveBean.getPreheatUrl());
        req.setOrderlistUrl(liveBean.getOrderlistUrl());
        req.setImgUrl(liveBean.getImgUrl());
        req.setHeaderImgUrl(liveBean.getHeaderImgUrl());
        req.setAdImgUrl(liveBean.getAdImgUrl());
        req.setContentImg(liveBean.getContentImg());
        req.setMediaUrl(liveBean.getMediaUrl());
        req.setMediaType(liveBean.getMediaType());
        req.setStartTime(liveBean.getStartTime());
        req.setEndTime(liveBean.getEndTime());
        req.setOrderCount(liveBean.getOrderCount());
        req.setLeftPoint(liveBean.getLeftPoint());
        Map<String, Object> map = new HashMap<>();
        map = (Map<String, Object>) JsonUtil.jsonToBean(liveBean.getExtendInfo(), map.getClass());
        req.setSubTitle((String) map.get("subTitle"));
        req.setContentDes((String) map.get("contentDes"));
        req.setHistoryRecommend((String) map.get("historyRecommend"));
        req.setCodeUrl((String) map.get("codeUrl"));
        req.setCodeDes((String) map.get("codeDes"));
        req.setPaperArticleId((String) map.get("paperArticleId"));
        req.setMediaUrlForH5((String) map.get("mediaUrlForH5"));

        List<GuestDesBean> guestDesBeanList = (List<GuestDesBean>) map.get("guests");
        req.setGuestStr(JsonUtil.bean2PrettyJson(guestDesBeanList));
        req.setStartTimeStr(DateUtil.getDateLongTimePlusStr(liveBean.getStartTime()));
        req.setEndTimeStr(DateUtil.getDateLongTimePlusStr(liveBean.getEndTime()));
        req.setLeftPoint(liveBean.getLeftPoint());
        req.setReadCountScore(liveBean.getReadCountScore());
        req.setDisplayResourcePosition(liveBean.getDisplayResourcePosition());

        if(Objects.nonNull(liveBean.getChannelId())){
            TLiveChannel tLiveChannel = tLiveChannelMapper.selectByPrimaryKey(Long.parseLong(liveBean.getChannelId() + ""));
            req.setChannelType(tLiveChannel.getIsFree());
        }
        req.setChannelId(liveBean.getChannelId());
        req.setIsFree(liveBean.getIsFree());
        req.setIsSeparateSale(liveBean.getIsSeparateSale());
        if(StringUtils.isNotEmpty(liveBean.getSkuId())){
            List<String> list = new ArrayList<>();
            list.add(liveBean.getSkuId());
            req.setPrice(getSkuIdPrice(list).get(liveBean.getSkuId()));
        }
        req.setFreeVipType(liveBean.getFreeVipType());
        req.setSkuId(liveBean.getSkuId());
        req.setChannelPreviewDuration(liveBean.getChannelPreviewDuration());
        req.setChannelOrder(liveBean.getChannelOrder());
        req.setSeparateSalePreviewDuration(liveBean.getSeparateSalePreviewDuration());
        req.setChannelListUrl(liveBean.getChannelListUrl());
        req.setShareStatus(liveBean.getShareStatus());
        retMap.put("live", req);
        retMap.put("liveId", liveId);
        Integer status = liveBean.getStatus() <= 0 ? liveBean.getStatus() : liveBean.getStartTime().getTime() > new Date().getTime() ? 2 : liveBean.getEndTime() == null || liveBean.getEndTime().getTime() > new Date().getTime() ? 1 : 3;
        retMap.put("status", status);
        FincirContentTagRelationPO paramTagRelationPO = new FincirContentTagRelationPO(null, null, liveId, String.valueOf(SourceTypeEnum.LIVE_SERVICE.getSourceType()));
        List<FincirContentTagRelationPO> tagRelationPOList = contentTagRelationService.queryListByParam(paramTagRelationPO);
        retMap.put("contentTags", contentTagRelationService.joinTagIds(tagRelationPOList));
        return retMap;
    }

    // 福利互动任务编辑页面
    @RequestMapping("/toTaskInfoEditPage")
    public String toTaskInfoEditPage(HttpServletRequest request, String liveId) {
        FincirLiveTaskPO paramLiveTask = new FincirLiveTaskPO();
        paramLiveTask.setLiveId(liveId);
        paramLiveTask.setStatus(StatusEnum.YES.getStatus().toString());
        List<FincirLiveTaskPO> liveTaskPOList = liveTaskService.getFincirLiveTaskPOList(paramLiveTask);

        request.setAttribute("liveTaskList", JsonUtil.beanToJson(liveTaskPOList));
        request.setAttribute("liveId", liveId);
        return "/webpage/fincir/media/editTaskInfo";
    }

    @RequestMapping("/getTaskTypeList")
    @ResponseBody
    public List<EnumPO> getTaskTypeList() {
        List<EnumPO> taskTypeEnums = new ArrayList<>();
        for (TaskTypeEnum taskTypeEnum : TaskTypeEnum.values()) {
            EnumPO taskTypeEnumPO = new EnumPO();
            taskTypeEnumPO.setKey(taskTypeEnum.getTaskType());
            taskTypeEnumPO.setDesc(taskTypeEnum.getDesc());
            taskTypeEnums.add(taskTypeEnumPO);
        }
        return taskTypeEnums;
    }

    @RequestMapping("/getPrizeTypeList")
    @ResponseBody
    public List<EnumPO> getPrizeTypeList() {
        List<EnumPO> prizeTypeEnumPOS = new ArrayList<>();
        for (PrizeTypeEnum prizeTypeEnum : PrizeTypeEnum.values()) {
            EnumPO prizeTypeEnumPO = new EnumPO();
            prizeTypeEnumPO.setKey(prizeTypeEnum.getPrizeType());
            prizeTypeEnumPO.setDesc(prizeTypeEnum.getDesc());
            prizeTypeEnumPOS.add(prizeTypeEnumPO);
        }
        return prizeTypeEnumPOS;
    }

    @RequestMapping("/getStatus")
    @ResponseBody
    public List<EnumPO> getStatus() {
        List<EnumPO> statusTypeEnumPOS = new ArrayList<>();
        for (StatusEnum statusEnum : StatusEnum.values()) {
            EnumPO enumPO = new EnumPO();
            enumPO.setKey(statusEnum.getStatus().toString());
            enumPO.setDesc(statusEnum.getStatusName());
            statusTypeEnumPOS.add(enumPO);
        }
        return statusTypeEnumPOS;
    }

    /**
     * 更新福利互动任务
     */
    @RequestMapping("/updateTaskInfo")
    @ResponseBody
    public Json updateTaskInfo(String liveId, FincirLiveTaskVO liveTaskVO) {
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        String failName = "";

        try {
            if (liveTaskVO == null) {
                return ReturnJsonUtil.getReturnJson(false);
            }
            FincirLiveTaskPO paramLiveTask = new FincirLiveTaskPO();
            paramLiveTask.setLiveId(liveId);
            paramLiveTask.setStatus(StatusEnum.YES.getStatus().toString());
            if (liveTaskVO.getTaskTypes() == null || liveTaskVO.getTaskTypes().size() < 1) {
                //全部删除
                paramLiveTask.setStatus(StatusEnum.NO.getStatus().toString());
                boolean flag = liveTaskService.updateFincirLiveTaskByLiveId(paramLiveTask);
                return ReturnJsonUtil.getReturnJson(flag);
            }

            List<FincirLiveTaskPO> liveTaskPOList = liveTaskService.getFincirLiveTaskPOList(paramLiveTask);

            //转成list 保存、修改或删除
            List<String> updateOrInsertId = new ArrayList<>();
            for (int i = 0; i < liveTaskVO.getTaskTypes().size(); i++) {
                FincirLiveTaskPO bean = new FincirLiveTaskPO();
                bean.setTaskName(liveTaskVO.getTaskNames().get(i));
                bean.setTaskType(liveTaskVO.getTaskTypes().get(i));
                bean.setTaskValue(liveTaskVO.getTaskValues().get(i));
                bean.setPrizeType(liveTaskVO.getPrizeTypes().get(i));
                bean.setPrizeId(liveTaskVO.getPrizeIds().get(i));
                bean.setPrizeNumb(liveTaskVO.getPrizeNumbs().get(i));

                if (CollectionUtils.isNotEmpty(liveTaskVO.getPrizeDescs()) && StringUtils.isNotEmpty(liveTaskVO.getPrizeDescs().get(i))) {
                    bean.setPrizeDesc(liveTaskVO.getPrizeDescs().get(i));
                }
                if (CollectionUtils.isNotEmpty(liveTaskVO.getPrizeGotoUrls()) && StringUtils.isNotEmpty(liveTaskVO.getPrizeGotoUrls().get(i))) {
                    bean.setPrizeGotoUrl(liveTaskVO.getPrizeGotoUrls().get(i).trim());
                }
                try {
                    boolean flag = true;
                    if (CollectionUtils.isNotEmpty(liveTaskVO.getIds()) && StringUtils.isNotEmpty(liveTaskVO.getIds().get(i))) {
                        updateOrInsertId.add(liveTaskVO.getIds().get(i));
                        bean.setId(liveTaskVO.getIds().get(i));
                        flag = liveTaskService.updateFincirLiveTaskById(bean);
                    } else {
                        bean.setLiveId(liveId);
                        flag = liveTaskService.saveFincirLiveTask(bean);
                    }
                    failName = flag == false ? failName + bean.getTaskName() + "," : failName;

                } catch (Exception e) {
                    logger.error("更新直播福利任务失败。Exception:" + e.getMessage());
                    failName = failName + bean.getTaskName() + ",";
                }
            }

            //处理删除的情况
            for (FincirLiveTaskPO taskPO : liveTaskPOList) {
                try {
                    if (!updateOrInsertId.contains(taskPO.getId())) {
                        taskPO.setStatus(StatusEnum.NO.getStatus().toString());
                        liveTaskService.updateFincirLiveTaskById(taskPO);
                    }
                } catch (Exception e) {
                    logger.error("删除直播福利任务失败。Exception:" + e.getMessage());
                    failName = failName + taskPO.getTaskName() + ",";
                }
            }
        } catch (Exception e) {
            logger.error("更新直播福利任务失败。Exception:" + e.getMessage());
        }

        if (!failName.isEmpty()) {
            jsonResponse.setMsg(failName + "互动任务保存失败");
            jsonResponse.setSuccess(false);
            return jsonResponse;

        }
        return ReturnJsonUtil.getReturnJson(true);
    }


    // 福利互动任务查看页面
    @RequestMapping("/toTaskInfoPage")
    public String toTaskInfoPage(HttpServletRequest request, String liveId) {
        FincirLiveTaskPO paramLiveTask = new FincirLiveTaskPO();
        paramLiveTask.setLiveId(liveId);
        paramLiveTask.setStatus(StatusEnum.YES.getStatus().toString());
        List<FincirLiveTaskPO> liveTaskPOList = liveTaskService.getFincirLiveTaskPOList(paramLiveTask);
        request.setAttribute("liveId", liveId);
        request.setAttribute("liveTaskList", liveTaskPOList);
        return "/webpage/fincir/media/catTaskInfo";
    }

    /**
     * 更新
     */
    @RequestMapping("/update")
    @ResponseBody
    public Json update(MediaLiveReq req) {
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            MediaLiveBean bean = req.getLiveBean();
            bean.setExtendInfo(StringEscapeUtils.unescapeHtml(bean.getExtendInfo()));
            if (StringUtils.isNotBlank(req.getSubjectId())) {
                subjectMediaService.resetSubjectMediasRelations(LIVE, req.getSubjectId(), req.getId(), req.getArticleTitle());
                req.setSubjectId(null);
            }
            if (StringUtils.isNotBlank(bean.getOrderlistUrl())) {
                bean.setOrderlistUrl(StringEscapeUtils.unescapeHtml(bean.getOrderlistUrl()));
            }
            MediaLiveBean queryBean = mediaLiveDao.getLiveInfo(bean.getId());
            if (StringUtil.isNotEmpty(queryBean.getSkuId()) && !queryBean.getSkuId().equals(bean.getSkuId())) {
                jsonResponse.setMsg("skuId不能修改！");
                jsonResponse.setSuccess(false);
                return jsonResponse;
            }
            //处理内容标签
            contentTagRelationService.dealContentTagRelations(Constants.UPDATE, bean.getId(), String.valueOf(SourceTypeEnum.LIVE_SERVICE.getSourceType()), bean.getContentTags());
            logger.info("title======={},{}", bean.getArticleTitle(), bean.getArticleTitle().length());
            bean.setArticleTitle(HtmlCharacterEscapeUtils.Escape(bean.getArticleTitle()));
            logger.info("title=====end=={}", bean.getArticleTitle());
            mediaLiveDao.updateLiveInfo(bean);
            delLiveRedisKeys(bean.getId());
        } catch (Exception e) {
            logger.error("更新直播失败。Exception:" + e.getMessage());
            return ReturnJsonUtil.failResult(e.getMessage());
        }
        return ReturnJsonUtil.getReturnJson(true);

    }

    @RequestMapping("/updateStatus")
    @ResponseBody
    public Json updateStatus(MediaLiveBean bean) {
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            mediaLiveDao.updateLiveInfo(bean);
            delLiveRedisKeys(bean.getId());
        } catch (Exception e) {
            logger.error("更新直播失败。Exception:" + e.getMessage());
            return ReturnJsonUtil.getReturnJson(false);

        }
        return ReturnJsonUtil.getReturnJson(true);

    }

    @RequestMapping("/updateDuration")
    @ResponseBody
    public Json updateDuration(MediaLiveBean bean) {
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            liveSegmentsService.deleteLiveSegmentCache(bean.getId());
            mediaLiveDao.updateDuration(bean);
            liveSegmentsService.deleteLiveSegmentCache(bean.getId());
        } catch (Exception e) {
            logger.error("更新直播回放时长失败。", e);
            return ReturnJsonUtil.getReturnJson(false);
        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    @RequestMapping("/toAddPage")
    public String toAddLivePage(HttpServletRequest request) {
        return "/webpage/fincir/media/addLive";
    }

    @RequestMapping("/addLive")
    @ResponseBody
    public Json addTopic(MediaLiveReq bean) {
        logger.info("新增直播。param:" + JsonUtil.beanToJson(bean));
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            String id = UuidUtil.getUUID();
            bean.setId(id);
            if (StringUtils.isNotBlank(bean.getSubjectId())) {
                subjectMediaService.resetSubjectMediasRelations(LIVE, bean.getSubjectId(), bean.getId(), bean.getArticleTitle());
                bean.setSubjectId(null);
            }
            if(StringUtil.isNotEmpty(bean.getSkuId())){
                TLiveChannelValidSkuIdReq tLiveChannelValidSkuIdReq = new TLiveChannelValidSkuIdReq();
                tLiveChannelValidSkuIdReq.setSkuId(bean.getSkuId());
                Json validJson = this.liveChannelValidSkuId(tLiveChannelValidSkuIdReq);
                if(!validJson.isSuccess()){
                    return validJson;
                }
            }
            if (StringUtils.isNotBlank(bean.getOrderlistUrl())) {
                bean.setOrderlistUrl(StringEscapeUtils.unescapeHtml(bean.getOrderlistUrl()));
            }
            //处理内容标签
            if (StringUtil.isNotEmpty(bean.getContentTags())) {
                contentTagRelationService.dealContentTagRelations(Constants.INSERT, bean.getId(), String.valueOf(SourceTypeEnum.LIVE_SERVICE.getSourceType()), bean.getContentTags());
            }
            MediaLiveBean liveBean = bean.getLiveBean();
            if(Objects.nonNull(bean.getChannelId())){
                mediaLiveDao.updateChannelOrder(liveBean);
                liveBean.setChannelOrder(1);
            }
            mediaLiveDao.addLiveInfo(liveBean);
            MediaDataBean mediaDataBean = new MediaDataBean();
            mediaDataBean.setTargetId(id);
            mediaDataBean.setTargetType(5);
            mediaDataBean.setTargetTitle(HtmlCharacterEscapeUtils.Escape(bean.getArticleTitle()));
            mediaDataDao.addDataInfo(mediaDataBean);

            delLiveRedisKeys();
        } catch (Exception e) {
            logger.error("新增直播失败。Exception:" + e.getMessage());
            return ReturnJsonUtil.failResult(e.getMessage());

        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    @RequestMapping("/toCommentPage")
    public String toCommentPage(HttpServletRequest request, String liveId) {
        logger.info("toCommentPage,liveId = " + liveId);
        MediaLiveBean vo = mediaLiveDao.getLiveInfo(liveId);
        request.setAttribute("mediaLive", vo);
        return "/webpage/fincir/media/addComment";
    }

    @RequestMapping("/addComment")
    @ResponseBody
    public Json addComment(ArticleCommentBean bean) {
        if(!org.springframework.util.StringUtils.hasText(bean.getCommentContent())){
            logger.warn("直播:{},官方评论消息不能为空",bean.getArticleTitle());
            return ReturnJsonUtil.getReturnJson(false);
        }
        Json jsonResponse = new Json();
        bean.setCommentId(UuidUtil.getUUID());
        bean.setP2pUserId("86438156");
        bean.setPassportId("Y20181226000026948");
        bean.setToken("21f0c899e9994563a9f7f0e4bf3beb04");
        bean.setUserName("宜人优选官方助手");
        bean.setUserType("2");
        bean.setCommentTime(new Date());
        bean.setCreateTime(new Date());
        bean.setUpdateTime(new Date());
        bean.setSourceType(5);
        bean.setStatus(1);
        bean.setIsApprove(1);
        bean.setApproveStep(5);   //默认已审核
        bean.setCommentContent(StringEscapeUtils.unescapeHtml(bean.getCommentContent()));
        boolean flag = commentService.saveArticleComment(bean);
        if (flag) {
            //增加评论条数
            articleCommentService.accessComment(bean);
            jsonResponse = ReturnJsonUtil.getReturnJson(true);
        } else {
            jsonResponse = ReturnJsonUtil.getReturnJson(false);
        }
        return jsonResponse;
    }

    @RequestMapping("/posterDataGrid")
    @ResponseBody
    public DataGrid posterDataGrid(String liveId) {
        DataGrid dataGrid = new DataGrid();
        MediaPosterBean bean = new MediaPosterBean();
        bean.setMediaId(liveId);
        bean.setMediaType(5);
        List<MediaPosterBean> list = mediaPosterDao.getMediaPosterList(bean);
        if (CollectionUtils.isNotEmpty(list)) {
            for (MediaPosterBean mediaPosterBean : list) {
                if (mediaPosterBean.getPublishStatus().equals(PosterPublishStatusTypeEnum.PUBLISH.getCode()) && mediaPosterBean.getEndTime().before(new Date())) {
                    mediaPosterBean.setPublishStatus(PosterPublishStatusTypeEnum.PUBLISH_FINISH.getCode());
                }
            }
        }
        dataGrid.setRows(list);
        dataGrid.setTotal((long) list.size());
        request.setAttribute("liveId", liveId);
        return dataGrid;
    }


    @RequestMapping("/addPoster")
    @ResponseBody
    public Json addPoster(MediaPosterBean bean) {
        logger.info("新增直播广告 addPoster 原始param:" + JsonUtil.beanToJson(bean));
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            jsonResponse = mediaLiveService.savePoster(bean);
        } catch (Exception e) {
            return ReturnJsonUtil.getReturnJson(false);
        }
        return jsonResponse;
    }

    @RequestMapping("/deletePoster")
    @ResponseBody
    public Json deletePoster(Integer id, String mediaId) {
        logger.info("删除直播广告。param:" + id);
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        MediaPosterBean posterBean = null;
        try {
            posterBean = mediaPosterDao.getMediaPoster(id);
            if (posterBean.getPublishStatus().equals(PosterPublishStatusTypeEnum.PUBLISH.getCode()) && posterBean.getEndTime().after(new Date())) {
                return ReturnJsonUtil.failResult("推广中不可移除！");
            }
            mediaPosterDao.deleteMediaPoster(id);
        } catch (Exception e) {
            logger.error("删除直播广告失败。Exception:" + e.getMessage());
            return ReturnJsonUtil.getReturnJson(false);
        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    /**
     * 根据福利平台skuId查询商品名称
     *
     * @param skuId
     * @return
     */
    private String getGoodsNameById(String skuId) {
        List<String> skuIdList = new ArrayList<>();
        skuIdList.add(skuId);
        List<GoodsInfo> goodsInfoList = supportServiceApi.getGoodsInfoById(skuIdList, "").get();
        String goodsName = (null != goodsInfoList && goodsInfoList.size() > 0) ? goodsInfoList.get(0).getGoodsInfoName() : "";
        return goodsName;
    }

    @RequestMapping("/editPoster")
    @ResponseBody
    public Json editPoster(MediaPosterBean bean) {
        logger.info("editPoster 原始param:" + JsonUtil.beanToJson(bean));
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            jsonResponse = mediaLiveService.savePoster(bean);
        } catch (Exception e) {
            return ReturnJsonUtil.getReturnJson(false);
        }
        return jsonResponse;
    }

    @RequestMapping("/publishPoster")
    @ResponseBody
    public Json publishPoster(Integer id) {
        logger.info("publishPoster id: {}", id);
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        MediaPosterBean posterBean = null;
        try {
            posterBean = mediaPosterDao.getMediaPoster(id);
            if (posterBean.getActiveTime() == null) {
                jsonResponse.setMsg("推广时长未配置！");
                jsonResponse.setSuccess(false);
                return jsonResponse;
            }
            //校验是否直播中
            MediaLiveBean liveBean = mediaLiveDao.getLiveInfo(posterBean.getMediaId());
            Date now = new Date();
            if (liveBean.getStatus() > 0 && !(liveBean.getStartTime().compareTo(now) < 0 && liveBean.getEndTime().compareTo(now) > 0)) {
                jsonResponse.setMsg("操作失败，不在直播中！");
                jsonResponse.setSuccess(false);
                return jsonResponse;
            }
            //查询是否有正在推广中的广告
            posterBean.setPublishTime(now);
            List<MediaPosterBean> list = mediaPosterDao.getPublishingMediaPosterByMediaId(posterBean);
            if (list.size() > 0) {
                jsonResponse.setMsg("操作失败，现在有商品正在推广中！");
                jsonResponse.setSuccess(false);
                return jsonResponse;
            }
            posterBean.setPublishStatus(PosterPublishStatusTypeEnum.PUBLISH.getCode());
            posterBean.setEndTime(DateUtils.addSeconds(now, posterBean.getActiveTime()));
            mediaPosterDao.publishMediaPoster(posterBean);
        } catch (Exception e) {
            logger.error("推广直播广告失败。Exception:", e);
            return ReturnJsonUtil.getReturnJson(false);
        }
        return ReturnJsonUtil.getReturnJson(true);
    }


    @RequestMapping("/toUpdateLivePosterPushTimePage")
    public String toUpdateLivePosterPushTimePage(String posterId, String pushTime) {
        request.setAttribute("posterId", posterId);
        request.setAttribute("pushTime", pushTime);
        return "/webpage/fincir/media/posterPushTimeUpdate";
    }

    @RequestMapping("/updatePushTimeBatchPage")
    public String updatePushTimeBatchPage(String liveId) {
        request.setAttribute("mediaId", liveId);
        return "/webpage/fincir/media/updatePushTimeBatch";
    }

    @RequestMapping("/updatePushTimeBatch")
    @ResponseBody
    public Json updatePushTimeBatch(String mediaId, String updateType, String times) {
        logger.info("updatePushTimeBatch mediaId: {},updateType:{},times:{}", mediaId, updateType, times);
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            Integer dealTimes = Integer.valueOf(times);
            List<MediaPosterBean> mediaPosterBeans = mediaPosterDao.getMediaPosterListOfPushTime(mediaId);
            for (MediaPosterBean mediaPosterBean : mediaPosterBeans) {
                try {
                    if (Constants.COMMON_TRUE.equals(updateType)) {  // 1-增加
                        mediaPosterBean.setPushTime(mediaPosterBean.getPushTime() + dealTimes);
                    } else {
                        mediaPosterBean.setPushTime(mediaPosterBean.getPushTime() - dealTimes);
                    }
                    mediaPosterDao.updateMediaPosterSelectiveById(mediaPosterBean);
                } catch (Exception e) {
                    logger.error("标记回放时间失败。posterId:{} ,Exception:", mediaPosterBean.getId(), e);
                }
            }
        } catch (Exception e) {
            logger.error("标记回放时间失败。Exception:", e);
            return ReturnJsonUtil.getReturnJson(false);
        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    @RequestMapping("/updatePosterPushTimeById")
    @ResponseBody
    public Json updatePosterPushTimeById(Integer id, String pushTime) {
        logger.info("updatePosterPushTimeById id: {},pushTime:{}", id, pushTime);
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        MediaPosterBean posterBean = new MediaPosterBean();
        try {
            posterBean.setId(id);
            posterBean.setPushTime(Integer.valueOf(pushTime));
            mediaPosterDao.updateMediaPosterSelectiveById(posterBean);
        } catch (Exception e) {
            logger.error("标记回放时间失败。Exception:", e);
            return ReturnJsonUtil.getReturnJson(false);
        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    @RequestMapping("/setPosterPushTime")
    @ResponseBody
    public Json setPosterPushTime(Integer id) {
        logger.info("setPosterPushTime id: {}", id);
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        MediaPosterBean posterBean = null;
        try {
            posterBean = mediaPosterDao.getMediaPoster(id);
            MediaLiveBean mediaLiveBean = mediaService.getMediaLiveById(posterBean.getMediaId());
            if (null == mediaLiveBean || mediaLiveBean.getPushFlowTime() == null) {
                jsonResponse.setMsg("直播开始时间异常！");
                jsonResponse.setSuccess(false);
                return jsonResponse;
            }
            posterBean.setPushTime(DateUtil.getSecondsOfTwoDate(mediaLiveBean.getPushFlowTime()));
            mediaPosterDao.updateMediaPosterSelectiveById(posterBean);
        } catch (Exception e) {
            logger.error("标记回放时间失败。Exception:", e);
            return ReturnJsonUtil.getReturnJson(false);
        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    @RequestMapping("/unpublishPoster")
    @ResponseBody
    public Json unpublishPoster(Integer id) {
        logger.info("unpublishPoster id: {}", id);
        Json jsonResponse = new Json();
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        if (userBean == null) {
            jsonResponse.setMsg("请重新登录！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        MediaPosterBean posterBean = null;
        try {
            posterBean = mediaPosterDao.getMediaPoster(id);
            //校验是否直播中
            MediaLiveBean liveBean = mediaLiveDao.getLiveInfo(posterBean.getMediaId());
            Date now = new Date();
            if (liveBean.getStatus() > 0 && !(liveBean.getStartTime().compareTo(now) < 0 && liveBean.getEndTime().compareTo(now) > 0)) {
                jsonResponse.setMsg("操作失败，不在直播中！");
                jsonResponse.setSuccess(false);
                return jsonResponse;
            }
            if (posterBean.getEndTime().before(now)) {
                jsonResponse.setMsg("操作失败，推广已结束！");
                jsonResponse.setSuccess(false);
                return jsonResponse;
            }
            posterBean.setPublishStatus(PosterPublishStatusTypeEnum.UNPUBLISH.getCode());
            mediaPosterDao.updateMediaPosterSelectiveById(posterBean);
        } catch (Exception e) {
            logger.error("取消推广直播广告失败。Exception:", e);
            return ReturnJsonUtil.getReturnJson(false);
        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    @RequestMapping("/frame")
    public String frame(String liveId) {
        request.setAttribute("liveId", liveId);
        return "/webpage/fincir/media/frame";
    }

    @RequestMapping("/toPosterPage")
    public String toPosterPage(HttpServletRequest request, String liveId) {
        request.setAttribute("liveId", liveId);
        return "/webpage/fincir/media/livePoster";
    }

    @RequestMapping("/toAddPosterPage")
    public String toAddPosterPage(HttpServletRequest request, String liveId) {
        request.setAttribute("mediaId", liveId);
        return "/webpage/fincir/media/addLivePoster";
    }

    @RequestMapping("/toAddPosterProductPage")
    public String toAddPosterProductPage(HttpServletRequest request, String liveId) {
        request.setAttribute("mediaId", liveId);
        return "/webpage/fincir/media/addLivePosterProduct";
    }

    @RequestMapping("/export")
    @ResponseBody
    public void exportUserActivityInfos(String liveId, HttpSession session, ServletResponse servletResponse) {
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        try {
            ServletOutputStream out = response.getOutputStream();
            List<UserActivityBean> list = mediaLiveDao.getJoinActivityUsers(liveId);
            response.reset();
            response.setContentType("application/x-download;charset=utf-8");
            response.addHeader("Content-Disposition", "attachment;filename=users.xls");
            String path = session.getServletContext().getRealPath("/");
            String fileUrl = path + "templates/" + "users.xls";
            ExportExcel ee = new ExportExcel(fileUrl);
            InputStream is = ee.export(4, 5, list, UserActivityBean.class);

            byte[] buffer = new byte[1024];
            int count;
            while ((count = is.read(buffer)) != -1) {
                out.write(buffer, 0, count);
            }
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 测试直播预约推送
    @RequestMapping("/testPush")
    public void testPush() {
        //查询待直播id
        List<MediaLiveBean> mediaLiveBeanList = mediaService.getMediaLiveList();
        if (null != mediaLiveBeanList && mediaLiveBeanList.size() > 0) {
            for (MediaLiveBean mediaLiveBean : mediaLiveBeanList) {
                //再次校验一下直播的开始时间是否大于当前时间
                if (mediaLiveBean.getStartTime().after(new Date())) {
                    //每个直播开启一个线程进行推送
                    LiveOrderRemindPushThread liveOrderRemindPushThread = new LiveOrderRemindPushThread(mediaService, mediaLiveBean, pushMessageService);
                    new Thread(liveOrderRemindPushThread).start();
                }
            }
        }
    }

    /**
     * 回复直播员页面
     * @param request
     * @param liveId
     * @return
     */
    @RequestMapping("/toReplyPage")
    public String toReplyPage(HttpServletRequest request, String liveId) {
        logger.info("toReplyPage,liveId = {}", liveId);
        MediaLiveBean vo = mediaLiveDao.getLiveInfo(liveId);
        request.setAttribute("mediaLive", vo);
        return "/webpage/fincir/media/addReply";
    }

    /**
     * 添加回复直播员
     * @param bean
     * @return
     */
    @RequestMapping("/addReply")
    @ResponseBody
    public Json addReply(FincirLiveBarrageEntity bean) {
        if(!org.springframework.util.StringUtils.hasText(bean.getContent())){
            logger.warn("直播:{},回复直播员消息不能为空",bean.getLiveTitle());
            return ReturnJsonUtil.getReturnJson(false);
        }
        Json jsonResponse;
        LocalDateTime now = LocalDateTime.now();
        bean.setYrUserId("86438156");
        bean.setYrUserName("宜人优选官方助手");
        bean.setVisibilityFlag(1);
        bean.setMessageTime(now);
        bean.setCreateTime(now);
        bean.setContent(StringEscapeUtils.unescapeHtml(bean.getContent()));
        boolean flag = fincirLiveBarrageService.addLiveBarrage(bean);
        if (flag) {
            jsonResponse = ReturnJsonUtil.getReturnJson(true);
        } else {
            jsonResponse = ReturnJsonUtil.getReturnJson(false);
        }
        return jsonResponse;
    }

    /**
     * 新增直播后删除数据
     */
    private void delLiveRedisKeys() {
        //延迟2秒双删最近场次的直播信息
        delayDelRecentlyLiveMedia();
    }
    /**
     * 直播数据发生变化时 删除缓存数据
     */
    private void delLiveRedisKeys(String articleId) {
        // 更新直播详情的缓存值
        String mediaInfoKey = ConstantsForRedis.FINCIR_REDIS_MEDIA_DETAIL + articleId;
        MediaLiveBean mediaLiveBean = mediaLiveDao.getLiveInfo(articleId);
        if (mediaLiveBean != null) {
            //放入缓存 24* 60min
            String mediaInfoStr = JsonUtil.beanToJson(mediaLiveBean);
            logger.info(" 将直播详情放入缓存 detail=  {} ", mediaInfoStr);
            jedisCluster.setex(mediaInfoKey, ConstantsForRedis.FINCIR_MEDIA_COMMENT_LIST_EXPIRE, mediaInfoStr);
        }

        List<String> keys = Arrays.asList(ConstantsForRedis.FINCIR_REDIS_LIVE_0703, ConstantsForRedis.FINCIR_REDIS_INDEX_LIVE_LIST);
        for (String key : keys) {
            logger.info("直播数据发生变化 删除缓存的数据，KEY为：{}，删除结果：{}", key, jedisCluster.del(key));
        }

        if (jedisCluster.exists(ConstantsForRedis.FINCIR_REDIS_LIVE_MEDIA_LIVING)) {
            String redisValue = jedisCluster.get(ConstantsForRedis.FINCIR_REDIS_LIVE_MEDIA_LIVING);
            MediaLiveBean redisMediaBean = JsonUtil.jsonToBean(redisValue, MediaLiveBean.class);
            if (Objects.nonNull(mediaLiveBean) && mediaLiveBean.getId().equals(redisMediaBean.getId())) {
                jedisCluster.del(ConstantsForRedis.FINCIR_REDIS_LIVE_MEDIA_LIVING);
            }
        }
        //延迟2秒双删最近场次的直播信息
        delayDelRecentlyLiveMedia();
    }

    private void delayDelRecentlyLiveMedia() {
        if (jedisCluster.exists(ConstantsForRedis.FINCIR_REDIS_RECENTLY_LIVE_MEDIA_LIVED)) {
            jedisCluster.del(ConstantsForRedis.FINCIR_REDIS_RECENTLY_LIVE_MEDIA_LIVED);
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(2000);
                    jedisCluster.del(ConstantsForRedis.FINCIR_REDIS_RECENTLY_LIVE_MEDIA_LIVED);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }

    /**
     * 直播广告数据发生变化时 更新缓存数据
     */
    private boolean updateLivePosterRedisValue(String liveId) {
        String mediaPosterRedisKey = ConstantsForRedis.FINCIR_REDIS_MEDIA_PRODUCT_RECOMMEND + liveId;
        MediaPosterBean bean = new MediaPosterBean();
        bean.setMediaId(liveId);
        bean.setMediaType(5);
        List<MediaPosterBean> list = mediaPosterDao.getMediaPosterList(bean);
        //排序，最近推广的放在第一个
        list.sort(new Comparator<MediaPosterBean>() {
            @Override
            public int compare(MediaPosterBean o1, MediaPosterBean o2) {
                if (o1.getPublishStatus().compareTo(o2.getPublishStatus()) == 0) {
                    if (o1.getPublishTime() == null || o2.getPublishTime() == null) {
                        return o1.getId().compareTo(o2.getId());
                    } else {
                        return o2.getPublishTime().compareTo(o1.getPublishTime());
                    }
                } else {
                    return o2.getPublishStatus().compareTo(o1.getPublishStatus());
                }
            }
        });
        //放入缓存 3小时
        if (list.size() > 0) {
            String mediaPostersResult = JsonUtil.beanToJson(list);
            String result = jedisCluster.setex(mediaPosterRedisKey, 60 * 60 * 3, mediaPostersResult);
            logger.info("将直播liveId={} 的推荐产品 mediaPostersResult={} 放入缓存result={}", liveId, mediaPostersResult, result);
            return REDIS_SUCCESS.equals(result);
        } else {
            String result = jedisCluster.setex(mediaPosterRedisKey, 60 * 60 * 3, "null");
            logger.info("将直播liveId={} 的推荐产品 mediaPostersResult=null 放入缓存result={}", liveId, result);
            return REDIS_SUCCESS.equals(result);
        }
    }

    @Resource
    TLiveChannelMapper tLiveChannelMapper;

    @Resource
    TLivePayUserMapper tLivePayUserMapper;

    @RequestMapping("/liveChannel/page")
    @ResponseBody
    public DataGrid liveChannelPage(TLiveChannelPageReq liveChannelPageVo) {
        DataGrid dataGrid = new DataGrid();
        List<TLiveChannel> list = tLiveChannelMapper.selectPageByIdOrName(liveChannelPageVo);
        if(CollectionUtils.isNotEmpty(list)){
            List<String> skuIdList = list.stream().map(TLiveChannel::getSkuId).filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            Map<String, BigDecimal> skuIdPriceMap = getSkuIdPrice(skuIdList);
            List<TLiveChannelPageRes> tLiveChannelPageResList = list.stream().map(c -> {
                TLiveChannelPageRes tLiveChannelPageRes = new TLiveChannelPageRes();
                BeanUtil.copyProperties(tLiveChannelPageRes, c);
                if(Objects.nonNull(tLiveChannelPageRes.getBriefIntroduction())){
                    tLiveChannelPageRes.setBriefIntroduction(StringEscapeUtils.unescapeHtml(tLiveChannelPageRes.getBriefIntroduction()));
                }
                if(Objects.nonNull(tLiveChannelPageRes.getName())){
                    tLiveChannelPageRes.setName(StringEscapeUtils.unescapeHtml(tLiveChannelPageRes.getName()));
                }
                tLiveChannelPageRes.setPrice(skuIdPriceMap.get(c.getSkuId()));
                Integer payCount = tLivePayUserMapper.selectCountByTypeRelatedId(1, c.getId());
                tLiveChannelPageRes.setPayCount(payCount);
                tLiveChannelPageRes.setContentCount(mediaLiveDao.selectCountByChannelId(c.getId() + ""));
                return tLiveChannelPageRes;
            }).collect(Collectors.toList());
            dataGrid.setRows(tLiveChannelPageResList);
        }
        Integer contentCount = tLiveChannelMapper.selectCountByIdOrName(liveChannelPageVo);
        dataGrid.setTotal(Long.parseLong( contentCount + ""));
        return dataGrid;
    }
    @RequestMapping("/liveChannel/getSkuIdPrice")
    @ResponseBody
    public Map<String, BigDecimal> getSkuIdPrice(String skuIds){
        List<String> list = Arrays.asList(skuIds.split(","));
        return this.getSkuIdPrice(list);
    }

    public Map<String, BigDecimal> getSkuIdPrice(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        String skuIdStr = StringUtils.join(list, ",");
        Map<String, String> formData = new HashMap<>();
        formData.put("channelCodeList", lifeServerChannelCodes);
        formData.put("storeId", lifeServerStoreId);
        formData.put("pageNo", "1");
        formData.put("pageSize", list.size() + "");
        formData.put("skuIdList", skuIdStr);
        String response = null;
        try {
            response = HttpClientUtils.doPost(lifeServerUrl + "/life/mall/getGoodsByChannelCodeList", formData);
            DataModel<MallProductListVo> dataModel = JSON.parseObject(response,
                    new TypeReference<DataModel<MallProductListVo>>() {

                    });
            MallProductListVo mallProductListVo = dataModel.getData();
            if (mallProductListVo != null && mallProductListVo.getProductList() != null) {
                return mallProductListVo.getProductList().stream().filter(v -> Objects.nonNull(v.getPointPrice()))
                        .collect(Collectors.toMap(MallProductVo::getProductSubIdStr,
                                v -> new BigDecimal(v.getPointPrice() + "")));
            }
        } catch (Exception e) {
            logger.error("getSkuIdPrice error", e);
        }
        return Maps.newHashMap();
    }

    @RequestMapping("/liveChannel/delete")
    @ResponseBody
    @Transactional
    public Json liveChannelDelete(Long id) {
        logger.info("liveChannelDelete id: {}", id);
        Json jsonResponse = new Json();
        TLiveChannel tLiveChannel = tLiveChannelMapper.selectByPrimaryKey(id);
        if (tLiveChannel == null) {
            jsonResponse.setMsg("该专栏不存在！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        Integer payCount = tLivePayUserMapper.selectCountByTypeRelatedId(1, tLiveChannel.getId());
        if(payCount > 0){
            jsonResponse.setMsg("该专栏有付费用户，不能删除！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        MediaLiveBean mediaLiveBean = new MediaLiveBean();
        mediaLiveBean.setChannelId(tLiveChannel.getId().intValue());
        mediaLiveDao.clearChannelId(mediaLiveBean);
        tLiveChannelMapper.deleteByPrimaryKey(id);
        return ReturnJsonUtil.getReturnJson(true);
    }
    @RequestMapping("/liveChannel/create")
    @ResponseBody
    public Json liveChannelCreate(TLiveChannelCreateReq tLiveChannelCreateReq) {
        if(Objects.nonNull(tLiveChannelCreateReq.getBriefIntroduction())){
            tLiveChannelCreateReq.setBriefIntroduction(StringEscapeUtils.unescapeHtml(tLiveChannelCreateReq.getBriefIntroduction()));
        }
        if(Objects.nonNull(tLiveChannelCreateReq.getName())){
            tLiveChannelCreateReq.setName(StringEscapeUtils.unescapeHtml(tLiveChannelCreateReq.getName()));
        }
        logger.info("liveChannelCreate param: {}", JsonUtil.beanToJson(tLiveChannelCreateReq));
        Json jsonResponse = new Json();
        if(tLiveChannelCreateReq.getIsFree() == 1){
            TLiveChannelValidSkuIdReq tLiveChannelValidSkuIdReq = new TLiveChannelValidSkuIdReq();
            tLiveChannelValidSkuIdReq.setSkuId(tLiveChannelCreateReq.getSkuId());
            Json validJson = this.liveChannelValidSkuId(tLiveChannelValidSkuIdReq);
            if(!validJson.isSuccess()){
                return validJson;
            }
        }
        try {
            TLiveChannel tLiveChannel = new TLiveChannel();
            BeanUtil.copyProperties(tLiveChannel, tLiveChannelCreateReq);
            tLiveChannelMapper.insertSelective(tLiveChannel);
        } catch (Exception e) {
            logger.error("liveChannelCreate error: {}", e.getMessage());
            jsonResponse.setMsg("创建专栏失败！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    @RequestMapping("/liveChannel/detail")
    @ResponseBody
    public Json liveChannelDetail(Long id) {
        logger.info("liveChannelDetail param: {}", id);
        Json jsonResponse = new Json();
        try {
            TLiveChannel tLiveChannel = tLiveChannelMapper.selectByPrimaryKey(id);
            if(Objects.nonNull(tLiveChannel.getBriefIntroduction())){
                tLiveChannel.setBriefIntroduction(StringEscapeUtils.unescapeHtml(tLiveChannel.getBriefIntroduction()));
            }
            if(Objects.nonNull(tLiveChannel.getName())){
                tLiveChannel.setName(StringEscapeUtils.unescapeHtml(tLiveChannel.getName()));
            }
            return new Json(true, "成功", tLiveChannel);
        } catch (Exception e) {
            logger.error("liveChannelDetail error: {}", e.getMessage());
            jsonResponse.setMsg("查询专栏失败！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
    }

    @RequestMapping("/liveChannel/validSkuId")
    @ResponseBody
    public Json liveChannelValidSkuId(TLiveChannelValidSkuIdReq tLiveChannelValidSkuIdReq) {
        logger.info("liveChannelValidSkuId param: {}", JsonUtil.beanToJson(tLiveChannelValidSkuIdReq));

        if (StringUtils.isEmpty(tLiveChannelValidSkuIdReq.getSkuId())) {
            logger.info("SKU ID为空");
            return ReturnJsonUtil.failResult("skuId不能为空！");
        }

        String skuId = tLiveChannelValidSkuIdReq.getSkuId();
        Long currentId = tLiveChannelValidSkuIdReq.getId();

        boolean inTLiveChannel = isSkuInTLiveChannel(skuId, currentId);
        if (!inTLiveChannel) {
            logger.info("SKU {} 已在专栏中使用", skuId);
            return ReturnJsonUtil.failResult("该skuId已存在！");
        }

        boolean inMediaLive = isSkuInMediaLive(skuId, currentId);
        if (!inMediaLive) {
            logger.info("SKU {} 已在直播内容中使用", skuId);
            return ReturnJsonUtil.failResult("该skuId已存在！");
        }

        return ReturnJsonUtil.getReturnJson(true);
    }

    private boolean isSkuInTLiveChannel(String skuId, Long currentId) {
        TLiveChannel existingChannel = tLiveChannelMapper.selectBySkuId(skuId);
        return existingChannel == null || (currentId != null && currentId.equals(existingChannel.getId()));
    }
    private boolean isSkuInMediaLive(String skuId, Long currentId) {
        MediaLiveBean liveBean = mediaLiveDao.getLiveInfoBySkuId(skuId);
        return liveBean == null || (currentId != null && currentId.equals(Long.parseLong(liveBean.getId())));
    }

    @RequestMapping("/liveChannel/update")
    @ResponseBody
    public Json liveChannelUpdate(TLiveChannelUpdateReq tLiveChannelUpdateReq) {
        logger.info("liveChannelUpdate param: {}", JsonUtil.beanToJson(tLiveChannelUpdateReq));
        Json jsonResponse = new Json();
        if(tLiveChannelUpdateReq.getId() == null){
            jsonResponse.setMsg("id不能为空！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        TLiveChannel tLiveChannel = tLiveChannelMapper.selectByPrimaryKey(tLiveChannelUpdateReq.getId());
        if(Objects.isNull(tLiveChannel)){
            jsonResponse.setMsg("该专栏不存在！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        if (StringUtil.isNotEmpty(tLiveChannel.getSkuId()) && !tLiveChannel.getSkuId()
                .equals(tLiveChannelUpdateReq.getSkuId())) {
            jsonResponse.setMsg("专栏skuId不能修改！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            cn.hutool.core.bean.BeanUtil.copyProperties(tLiveChannelUpdateReq, tLiveChannel, "create_time");
            if(Objects.nonNull(tLiveChannel.getBriefIntroduction())){
                tLiveChannel.setBriefIntroduction(StringEscapeUtils.unescapeHtml(tLiveChannel.getBriefIntroduction()));
            }
            if(Objects.nonNull(tLiveChannel.getName())){
                tLiveChannel.setName(StringEscapeUtils.unescapeHtml(tLiveChannel.getName()));
            }
            tLiveChannel.setUpdateTime(new Date());
            tLiveChannelMapper.updateByPrimaryKey(tLiveChannel);
            //删除专栏缓存
            deleteLiveChannelRedisCache(tLiveChannelUpdateReq.getId());

        } catch (Exception e) {
            logger.error("liveChannelUpdate error: {}", e.getMessage());
            jsonResponse.setMsg("编辑专栏失败！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        return ReturnJsonUtil.getReturnJson(true);
    }

    //删除专栏缓存
    private void deleteLiveChannelRedisCache(Long id) {
        if(Objects.isNull(id)){
            return;
        }
        String redisKey = String.format(ConstantsForRedis.FINCIR_REDIS_LIVE_CHANNEL_KEY,id);
        jedisCluster.del(redisKey);
    }

    @RequestMapping("/liveChannel/contentList")
    @ResponseBody
    public DataGrid liveChannelContentList(TLiveChannelContentListReq tLiveChannelContentListReq) {
        DataGrid dataGrid = new DataGrid();
        logger.info("liveChannelContentList param: {}", JsonUtil.beanToJson(tLiveChannelContentListReq));
        MediaLiveBean queryBean = new MediaLiveBean();
        queryBean.setChannelId(Integer.parseInt(tLiveChannelContentListReq.getChannelId()));
        queryBean.setId(tLiveChannelContentListReq.getQuery());
        List<MediaLiveBean> mediaLiveBeanList = mediaLiveDao.selectLiveListByChannelId(queryBean);
        if(CollectionUtils.isNotEmpty(mediaLiveBeanList)){
            List<TLiveChannelContentListRes> tLiveChannelContentListResList = mediaLiveBeanList.stream().map(m -> {
                TLiveChannelContentListRes tLiveChannelContentListRes = new TLiveChannelContentListRes();
                tLiveChannelContentListRes.setId(m.getId());
                tLiveChannelContentListRes.setArticleTitle(m.getArticleTitle());
                tLiveChannelContentListRes.setType(1);
                tLiveChannelContentListRes.setChannelOrder(m.getChannelOrder());
                tLiveChannelContentListRes.setStatus(m.getStatus() <= 0 ? m.getStatus() : m.getStartTime().getTime() > new Date().getTime() ? 2 : m.getEndTime() == null || m.getEndTime().getTime() > new Date().getTime() ? 1 : 3);
                tLiveChannelContentListRes.setStartTime(m.getStartTime());
                return tLiveChannelContentListRes;
            }).collect(Collectors.toList());
            dataGrid.setRows(tLiveChannelContentListResList);
        }
        return dataGrid;
    }

    @RequestMapping("/liveChannel/contentRemove")
    @ResponseBody
    public Json liveChannelContentRemove(TLiveChannelContentRemoveReq tLiveChannelContentRemoveReq) {
        logger.info("liveChannelContentRemove param: {}", JsonUtil.beanToJson(tLiveChannelContentRemoveReq));
        Json jsonResponse = new Json();
        if(tLiveChannelContentRemoveReq.getId() == null){
            jsonResponse.setMsg("id不能为空！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        MediaLiveBean mediaLiveBean = mediaLiveDao.getLiveInfo(tLiveChannelContentRemoveReq.getId());
        if(Objects.isNull(mediaLiveBean)){
            jsonResponse.setMsg("该内容不存在！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        try {
            mediaLiveDao.updateChannelId(mediaLiveBean);
        } catch (Exception e) {
            logger.error("liveChannelContentRemove error: {}", e.getMessage());
            jsonResponse.setMsg("移出内容失败！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        return ReturnJsonUtil.getReturnJson(true);
    }
    @RequestMapping("/liveChannel/updateContentOrder")
    @ResponseBody
    public Json updateContentOrder(@RequestBody TLiveChannelUpdateContentOrderReq tLiveChannelUpdateContentOrderReq) {
        logger.info("liveChannelContentOrder param: {}", JsonUtil.beanToJson(tLiveChannelUpdateContentOrderReq));
        Json jsonResponse = new Json();
        if(CollectionUtils.isEmpty(tLiveChannelUpdateContentOrderReq.getOrderList())){
            jsonResponse.setMsg("orderList不能为空！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
        List<TLiveChannelUpdateContentOrderReq.UpdateContentOrder> orderList = tLiveChannelUpdateContentOrderReq.getOrderList();
        orderList.forEach(order -> {
            try {
                MediaLiveBean mediaLiveBean = mediaLiveDao.getLiveInfo(order.getId());
                if(Objects.isNull(mediaLiveBean)){
                    jsonResponse.setMsg("该内容不存在！");
                    jsonResponse.setSuccess(false);
                    return;
                }
                mediaLiveBean.setChannelOrder(order.getChannelOrder());
                mediaLiveDao.updateLiveInfo(mediaLiveBean);
            } catch (Exception e) {
                logger.error("liveChannelContentOrder error: {}", e.getMessage());
            }
        });
        return ReturnJsonUtil.getReturnJson(true);
    }

    @RequestMapping("/liveChannel/listByType")
    @ResponseBody
    public DataGrid listByType(String type) {
        DataGrid dataGrid = new DataGrid();
        List<TLiveChannel> list = tLiveChannelMapper.listByType(type);
        if(CollectionUtils.isNotEmpty(list)){
            List<TLiveChannelListByTypeRes> tLiveChannelListByTypeResList = list.stream().map(c -> {
                TLiveChannelListByTypeRes tLiveChannelListByTypeRes = new TLiveChannelListByTypeRes();
                BeanUtil.copyProperties(tLiveChannelListByTypeRes, c);
                return tLiveChannelListByTypeRes;
            }).collect(Collectors.toList());
            dataGrid.setRows(tLiveChannelListByTypeResList);
        }
        dataGrid.setTotal(Long.parseLong(list.size() + ""));
        return dataGrid;
    }
}
