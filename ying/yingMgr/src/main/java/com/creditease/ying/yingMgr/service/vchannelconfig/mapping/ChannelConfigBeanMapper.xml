<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.creditease.ying.yingMgr.service.vchannelconfig.dao.IChannelConfigDao">
	<resultMap id="BaseResultMap" type="com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO">
		<result column="config_id" property="configId" jdbcType="INTEGER"/>
		<result column="config_name" property="configName" jdbcType="VARCHAR"/>
		<result column="config_value" property="configValue" jdbcType="VARCHAR"/>
		<result column="version_limit" property="versionLimit" jdbcType="VARCHAR"/>
		<result column="version_max_limit" property="versionMaxLimit" jdbcType="VARCHAR"/>
		<result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="online_time" property="onlineTime" jdbcType="TIMESTAMP"/>
		<result column="offline_time" property="offlineTime" jdbcType="TIMESTAMP"/>
		<result column="unselected_icon_url" jdbcType="VARCHAR" property="unselectedIconUrl" />
		<result column="selected_icon_url" jdbcType="VARCHAR" property="selectedIconUrl" />
		<result column="selected_char_color" jdbcType="VARCHAR" property="selectedCharColor" />
		<result column="tab_h5_url" jdbcType="VARCHAR" property="tabH5Url" />
		<result column="open_status" jdbcType="INTEGER" property="openStatus" />
	</resultMap>

	<sql id="Base_Column_List">
		t.config_id, t.config_name, t.config_value, t.version_limit, t.version_max_limit,group_concat(r.channel_code) AS channel_code,t.create_time,t.update_time,t.online_time,t.offline_time
	</sql>

	<sql id="Base_Column_Where">
		<where>
			<if test="configId != null and configId !=''">
				and t.config_id = #{configId,jdbcType=INTEGER}
			</if>
			<if test="configName != null and configName !=''">
				and t.config_name = #{configName,jdbcType=VARCHAR}
			</if>
			<if test="configValue != null and configValue !=''">
				and t.config_value = #{configValue,jdbcType=VARCHAR}
			</if>
			<if test="versionLimit != null and versionLimit !=''">
				and t.version_limit = #{versionLimit,jdbcType=VARCHAR}
			</if>
			<if test="versionMaxLimit != null and versionMaxLimit !=''">
				and t.version_max_limit = #{versionMaxLimit,jdbcType=VARCHAR}
			</if>
			<if test="onlineTime != null and onlineTime !=''">
				and t.online_time = #{onlineTime,jdbcType=VARCHAR}
			</if>
			<if test="offlineTime != null and offlineTime !=''">
				and t.offline_time = #{offlineTime,jdbcType=VARCHAR}
			</if>
			<if test="channelCode != null and channelCode !=''">
				and r.channel_code = #{channelCode,jdbcType=VARCHAR}
			</if>
			<if test="createTime != null and createTime !=''">
				and t.create_time = #{createTime,jdbcType=VARCHAR}
			</if>
			<if test="updateTime != null and updateTime !=''">
				and t.update_time = #{updateTime,jdbcType=VARCHAR}
			</if>
		</where>
	</sql>

	<insert id="insert" parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO">
		<selectKey resultType="java.lang.String" keyProperty="configId" order="AFTER" >
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into t_v_channel_config
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="configId != null and configId !=''">
				config_id ,
			</if>
			<if test="configName != null and configName !=''">
				config_name ,
			</if>
			<if test="configValue != null and configValue !=''">
				config_value ,
			</if>
			<if test="versionLimit != null and versionLimit !=''">
				version_limit ,
			</if>
			<if test="versionMaxLimit != null and versionMaxLimit !=''">
				version_max_limit ,
			</if>
			<if test="onlineTime != null and onlineTime !=''">
				 online_time ,
			</if>
			<if test="offlineTime != null and offlineTime !=''">
				 offline_time ,
			</if>
			<if test="createTime != null and createTime !=''">
				 create_time ,
			</if>
			<if test="updateTime != null and updateTime !=''">
				 update_time ,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="configId != null and configId !=''">
				#{configId,jdbcType=INTEGER},
			</if>
			<if test="configName != null and configName !=''">
				#{configName,jdbcType=VARCHAR},
			</if>
			<if test="configValue != null and configValue !=''">
				#{configValue,jdbcType=VARCHAR},
			</if>
			<if test="versionLimit != null and versionLimit !=''">
				#{versionLimit,jdbcType=VARCHAR},
			</if>
			<if test="versionMaxLimit != null and versionMaxLimit !=''">
				#{versionMaxLimit,jdbcType=VARCHAR},
			</if>
			<if test="onlineTime != null and onlineTime !=''">
				#{onlineTime,jdbcType=VARCHAR},
			</if>
			<if test="offlineTime != null and offlineTime !=''">
				#{offlineTime,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null and createTime !=''">
				#{createTime,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null and updateTime !=''">
				#{updateTime,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>

	<insert id="insertLink" parameterType="java.util.List" useGeneratedKeys="false">
		INSERT INTO t_v_channel_link
		(
		channel_id, config_id, channel_code,
		create_time
		)
		VALUES
		<foreach collection="list" item="model" separator=",">
			(
			#{model.channelId}, #{model.configId}, #{model.channelCode}, #{model.createTime}
			)
		</foreach>
	</insert>
	
	
	<delete id="deleteById" parameterType="java.lang.String">
		delete from t_v_channel_config
		where config_id = #{configId,jdbcType=VARCHAR}
	</delete>
	<delete id="deleteByIdLink" parameterType="java.lang.String">
		delete from t_v_channel_link
		where config_id = #{configId,jdbcType=VARCHAR}
	</delete>
	
	<update id="updateById" parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO">
		update t_v_channel_config
		<set>
			<if test="configId != null and configId !=''">
				config_id =  #{configId,jdbcType=VARCHAR},
			</if>
			<if test="configName != null and configName !=''">
				config_name =  #{configName,jdbcType=VARCHAR},
			</if>
			<if test="configValue != null and configValue !=''">
				config_value =  #{configValue,jdbcType=VARCHAR},
			</if>
			<if test="versionLimit != null and versionLimit !=''">
				version_limit =  #{versionLimit,jdbcType=VARCHAR},
			</if>
			<if test="versionMaxLimit != null and versionMaxLimit !=''">
				version_max_limit =  #{versionMaxLimit,jdbcType=VARCHAR},
			</if>
			<if test="onlineTime != null and onlineTime !=''">
				 online_time =  #{onlineTime,jdbcType=VARCHAR},
			</if>
			<if test="offlineTime != null and offlineTime !=''">
				 offline_time =  #{offlineTime,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null and createTime !=''">
				 create_time =  #{createTime,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null and updateTime !=''">
				 update_time =  #{updateTime,jdbcType=VARCHAR},
			</if>
		</set>
		where config_id = #{configId,jdbcType=VARCHAR}
	</update>
	
	<select id="selectById"  parameterType="java.lang.String" resultMap="BaseResultMap">
		SELECT
			<include refid="Base_Column_List"/>
		FROM t_v_channel_config t
		LEFT JOIN t_v_channel_link r ON t.config_id = r.config_id
		WHERE t.config_id =  #{configId,jdbcType=VARCHAR}
	</select>

	<select id="count"  parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT count(0)
		FROM  t_v_channel_link
		WHERE config_id<![CDATA[<> ]]>#{configId} and channel_code in
		<foreach collection="params" item="param" index="index" open="(" close=")" separator=",">
			#{param}
		</foreach>
	</select>
	
	
	<select id="selectList"  parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO" resultMap="BaseResultMap">
		SELECT
			<include refid="Base_Column_List"/>
		FROM t_v_channel_config t
		LEFT JOIN t_v_channel_link r ON t.config_id = r.config_id
			<include refid="Base_Column_Where"/>
		group by t.config_id
	</select>
	
	<select id="selectPageCount"  parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO" resultType="java.lang.Long">
		SELECT 
			count(DISTINCT t.config_id)
		FROM t_v_channel_config t
		LEFT JOIN t_v_channel_link r ON t.config_id = r.config_id
			<include refid="Base_Column_Where"/>
	</select>

	<select id="selectConfigList" resultMap="BaseResultMap">
		SELECT
		t.config_id, t.config_name
		FROM t_v_channel_config t
	</select>

	<select id="selectPageList"  parameterType="com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO" resultMap="BaseResultMap">
		SELECT
			<include refid="Base_Column_List"/>
		FROM t_v_channel_config t
		LEFT JOIN t_v_channel_link r ON t.config_id = r.config_id
			<include refid="Base_Column_Where"/>
		<if test="sort != null and sort != '' ">
			order by ${sort} ${order}
		</if>
		limit #{pageStart},#{rows}
	</select>

	<select id="selectOtherConfigByChannelCode" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List"/>
		FROM t_v_channel_config t
		LEFT JOIN t_v_channel_link r ON t.config_id = r.config_id
		where t.config_id != #{configId} and r.channel_code in
		<foreach item="item" collection="channelCodes" separator="," open="(" close=")" index="index">
			#{item}
		</foreach>
		group by t.config_id
	</select>

</mapper>