package com.creditease.ying.yingMgr.controller.channelMarket;

import com.creditease.ying.common.util.Json;
import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.user.bean.UserBean;
import com.creditease.ying.yingMgr.service.channelMarket.IChannelBindingService;
import com.creditease.ying.yingMgr.enums.TargetTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 渠道绑定控制器
 * 支持多种类型数据的渠道绑定功能
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Controller
@RequestMapping("/channelBinding")
@Slf4j
public class ChannelBindingController {

    @Autowired
    private IChannelBindingService channelBindingService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 渠道绑定页面
     */
    @RequestMapping("/channelBinding")
    public String channelBinding() {
        log.info("访问渠道绑定页面");
        return "/webpage/channelMarket/channelBinding";
    }

    /**
     * 测试方法
     */
    @RequestMapping("/test")
    @ResponseBody
    public String test() {
        log.info("测试方法被调用");
        return "ChannelBindingController is working!";
    }

    /**
     * 获取目标数据列表（背景图数据）
     * 直接使用现有的背景图接口
     */
    @RequestMapping("/getTargetDataList")
    @ResponseBody
    public DataGrid getTargetDataList(@RequestParam(value = "cardName", required = false) String cardName,
                                      @RequestParam(value = "useFor", required = false) String useFor,
                                      @RequestParam(value = "onlineStatus", required = false) String onlineStatus,
                                      @RequestParam(value = "page", defaultValue = "1") int page,
                                      @RequestParam(value = "rows", defaultValue = "15") int rows) {
        try {
            // 直接重定向到背景图数据接口
            return channelBindingService.getBackgroundPicList(cardName, useFor, onlineStatus, page, rows);
        } catch (Exception e) {
            log.error("获取背景图列表失败", e);
            return new DataGrid();
        }
    }

    /**
     * 获取渠道列表
     */
    @RequestMapping("/getChannelList")
    @ResponseBody
    public DataGrid getChannelList(@RequestParam(value = "channelName", required = false) String channelName,
                                   @RequestParam(value = "page", defaultValue = "1") int page,
                                   @RequestParam(value = "rows", defaultValue = "10") int rows) {
        try {
            return channelBindingService.getChannelList(channelName, page, rows);
        } catch (Exception e) {
            log.error("获取渠道列表失败", e);
            return new DataGrid();
        }
    }

    /**
     * 执行渠道绑定
     */
    @RequestMapping("/bind")
    @ResponseBody
    public Json bindChannels(@RequestParam(value = "targetIds") String targetIdsStr,
                             @RequestParam(value = "channelIds") String channelIdsStr,
                             @RequestParam(value = "targetType", defaultValue = "BACKGROUND_PIC") String targetType) {
        log.info("执行渠道绑定 | targetIds: {}, channelIds: {}, targetType: {}", targetIdsStr, channelIdsStr, targetType);

        List<Integer> targetIds = new ArrayList<>();
        List<Integer> channelIds = new ArrayList<>();

        // 解析目标数据ID
        if (targetIdsStr != null && !targetIdsStr.trim().isEmpty()) {
            for (String id : targetIdsStr.split(";")) {
                if (!id.trim().isEmpty()) {
                    targetIds.add(Integer.parseInt(id.trim()));
                }
            }
        }

        // 解析渠道ID
        if (channelIdsStr != null && !channelIdsStr.trim().isEmpty()) {
            for (String id : channelIdsStr.split(";")) {
                if (!id.trim().isEmpty()) {
                    channelIds.add(Integer.parseInt(id.trim()));
                }
            }
        }

        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        String creator = userBean != null ? userBean.getUserName() : "system";

        try {
            channelBindingService.bindChannels(targetIds, channelIds, targetType, creator);
            return Json.success("绑定成功");
        } catch (Exception e) {
            log.error("渠道绑定失败", e);
            return Json.error("绑定失败: " + e.getMessage());
        }
    }

    /**
     * 获取绑定关系
     */
    @RequestMapping("/getBindingRelations")
    @ResponseBody
    public DataGrid getBindingRelations(@RequestParam(value = "targetId") Integer targetId,
                                        @RequestParam(value = "page", defaultValue = "1") int page,
                                        @RequestParam(value = "rows", defaultValue = "15") int rows) {
        try {
            log.info("获取绑定关系 | targetId: {}, page: {}, rows: {}", targetId, page, rows);
            DataGrid result = channelBindingService.getBindingRelations(targetId, page, rows);
            log.info("绑定关系查询结果 | total: {}, rows: {}", result.getTotal(), result.getRows() != null ? result.getRows().size() : 0);
            return result;
        } catch (Exception e) {
            log.error("获取绑定关系失败", e);
            return new DataGrid();
        }
    }

    /**
     * 解绑渠道
     */
    @RequestMapping("/unbind")
    @ResponseBody
    public Json unbindChannel(@RequestParam(value = "bindingId") Integer bindingId) {
        try {
            channelBindingService.unbindChannel(bindingId);
            return Json.success("解绑成功");
        } catch (Exception e) {
            log.error("解绑渠道失败", e);
            return Json.error("解绑失败: " + e.getMessage());
        }
    }

    /**
     * 保存目标数据（背景图数据）
     * 重定向到背景图保存接口
     */
    @RequestMapping("/saveTargetData")
    @ResponseBody
    public Json saveTargetData(@RequestParam(value = "id", required = false) Integer id,
                               @RequestParam(value = "name") String name,
                               @RequestParam(value = "type") String type,
                               @RequestParam(value = "description", required = false) String description,
                               @RequestParam(value = "status") Integer status) {
        try {
            // 重定向到背景图保存接口
            return Json.success("请使用背景图管理页面进行保存操作");
        } catch (Exception e) {
            log.error("保存目标数据失败", e);
            return Json.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 删除目标数据（背景图数据）
     * 重定向到背景图删除接口
     */
    @RequestMapping("/deleteTargetData")
    @ResponseBody
    public Json deleteTargetData(@RequestParam(value = "ids") String idsStr) {
        try {
            // 重定向到背景图删除接口
            return Json.success("请使用背景图管理页面进行删除操作");
        } catch (Exception e) {
            log.error("删除目标数据失败", e);
            return Json.error("删除失败: " + e.getMessage());
        }
    }
}
