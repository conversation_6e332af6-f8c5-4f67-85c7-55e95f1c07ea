package com.creditease.ying.yingMgr.service.vchannelconfig.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.creditease.ying.common.exception.BusinessException;
import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.common.util.AppVersionUtil;
import com.creditease.ying.yingMgr.service.vchannelconfig.IChannelConfigService;
import com.creditease.ying.yingMgr.service.vchannelconfig.bean.*;
import com.creditease.ying.yingMgr.service.vchannelconfig.constant.RedisKeyConstant;
import com.creditease.ying.yingMgr.service.vchannelconfig.dao.IChannelConfigDao;
import com.creditease.ying.yingMgr.service.vchannelconfig.dao.TVChannelConfigMapper;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;

import java.text.SimpleDateFormat;
import java.util.*;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 大v渠道配置
 * @Date 2021/9/9 15:19
 **/
@Service
public class ChannelConfigServiceImpl implements IChannelConfigService {

    private static final String VERSION_814 = "8.14.0";
    private static final String VERSION_9 = "9.0.0";

    @Autowired
    private IChannelConfigDao dao;

    @Autowired
    private JedisCluster jedisCluster;

    @Resource
    TVChannelConfigMapper tvChannelConfigMapper;

    /**
     * @param listBean
     * @return DataGrid
     * <AUTHOR>
     * @Description 查询渠道配置列表
     * @Date 16:27 2021/9/10
     * @Param [listBean]
     */
    @Override
    public DataGrid selectList(ChannelConfigPO listBean) {
        DataGrid dg = new DataGrid();
        List<TVChannelConfig> tvChannelConfigList = tvChannelConfigMapper.selectPage(listBean);
        dg.setRows(tvChannelConfigList);
        dg.setTotal(Long.parseLong(tvChannelConfigList.size() + ""));
        return dg;
    }

    /**
     * @param configId
     * @return ChannelConfigPO
     * <AUTHOR>
     * @Description 根据configId查询渠道配置详情
     * @Date 16:27 2021/9/10
     * @Param [configId]
     */
    @Override
    public TVChannelConfig selectById(String configId) {
        TVChannelConfig configPO = tvChannelConfigMapper.selectByPrimaryKey(Integer.valueOf(configId));
        return configPO;
    }

    /**
     * @param vo
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 插入配置信息
     * @Date 16:27 2021/9/10
     * @Param [po]
     */
    @Override
    public String insert(ChannelConfigModifyVO vo) {
        checkTabLegal(vo);
        String value = buildConfigValueJson(vo);
        ChannelConfigPO po = new ChannelConfigPO();
        po.setConfigValue(value);
        po.setConfigName(vo.getConfigName());
        po.setVersionLimit(vo.getVersionLimit());
        po.setVersionMaxLimit(vo.getVersionMaxLimit());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        po.setCreateTime(sdf.format(new Date()));
        po.setUpdateTime(sdf.format(new Date()));
        dao.insert(po);
        //清除该key对应的缓存
        jedisCluster.del(RedisKeyConstant.CHANNEL_CONFIG+po.getConfigId());
        return po.getConfigId();
    }

    /**
     * 大v tab配置json拼装
     * @param vo
     * @return
     */
    private String buildConfigValueJson(ChannelConfigModifyVO vo) {
        if(vo == null || vo.getTabType() == null || vo.getTabType().size() < 1) {
            throw new BusinessException("-1","参数不正确！");
        }
        //转成配置json
        List<ListChannelConfigBean> list = Lists.newArrayList();
        for(int i = 0; i< vo.getTabType().size(); i++) {
            ListChannelConfigBean bean = new ListChannelConfigBean();
            bean.setType(vo.getTabType().get(i) + "");
            bean.setPriority(vo.getTabPriority().get(i));
            if(CollectionUtils.isNotEmpty(vo.getTabComment()) && StringUtils.isNotEmpty(vo.getTabComment().get(i))) {
                bean.setComment(vo.getTabComment().get(i));
            }
            if(CollectionUtils.isNotEmpty(vo.getTabIconNative()) && StringUtils.isNotEmpty(vo.getTabIconNative().get(i))) {
                bean.setIconNative(vo.getTabIconNative().get(i).trim());
            }
            if(CollectionUtils.isNotEmpty(vo.getTabIconActive()) && StringUtils.isNotEmpty(vo.getTabIconActive().get(i))) {
                bean.setIconActive(vo.getTabIconActive().get(i).trim());
            }
            if(CollectionUtils.isNotEmpty(vo.getTabUrl()) && StringUtils.isNotEmpty(vo.getTabUrl().get(i))) {
                bean.setUrl(vo.getTabUrl().get(i).trim());
            }
            list.add(bean);
        }
        return JSON.toJSONString(list);
    }

    /**
     * 关联渠道 or 修改的时候校验版本号是否有重叠
     * @param configId 当前配置id
     * @param registerChannel 注册渠道，多个用英文逗号分隔
     * @param versionLimit 最低版本
     * @param versionMaxLimit 最高版本
     */
    private void checkVersionLegal(String configId, String registerChannel, String versionLimit, String versionMaxLimit) {
        String[] channelCodeArr = registerChannel.split(",");
        //查询其他已配置该渠道的版本号是否有冲突
        List<ChannelConfigPO> channelConfigPOS = dao.selectOtherConfigByChannelCode(configId, channelCodeArr);
        if(CollectionUtils.isNotEmpty(channelConfigPOS)) {
            for(ChannelConfigPO channelConfigPO : channelConfigPOS) {
                if(AppVersionUtil.isVersionIntervalOverlapping(versionLimit, versionMaxLimit, channelConfigPO.getVersionLimit(), channelConfigPO.getVersionMaxLimit())) {
                    throw new RuntimeException("与配置\"" +  channelConfigPO.getConfigId() + "\"版本号有重叠，请检查！！");
                }
            }
        }
    }

    /**
     * 校验tab配置是否合法，避免运营人员配置错误
     * 8.14.0 版本之前：最多4个TAB，8.14.0版本之后最多5个tab
     * 8.14.0 版本之前：支持【首页、基金、看点、我的】
     * 8.14.0- 9.0 版本: 支持【首页、基金、看点、我的、生活】
     * 9.0 版本之后:支持【首页、看点、我的、生活、投资】
     * @param vo
     */
    private void checkTabLegal(ChannelConfigModifyVO vo) {
        int[] tabTypeNumArr = new int[BottomTabType.values().length];
        for(Integer type : vo.getTabType()) {
            int idx = BottomTabType.codeOf(type).ordinal();
            tabTypeNumArr[idx] = tabTypeNumArr[idx] + 1;
        }
        for(int i = 0; i< tabTypeNumArr.length; i++) {
            if(i != BottomTabType.WEB.ordinal() && tabTypeNumArr[i] > 1) {
                throw new BusinessException("-1","非web类型最多配置1条");
            }
        }
        int version814 = AppVersionUtil.getIntVersionForCompare(VERSION_814);
        int version9 = AppVersionUtil.getIntVersionForCompare(VERSION_9);
        int minVersion = AppVersionUtil.getIntVersionForCompare(vo.getVersionLimit());
        int maxVersion = AppVersionUtil.getIntVersionForCompare(vo.getVersionMaxLimit());
        if(minVersion >= maxVersion) {
            throw new BusinessException("-1","最高版本必须大于最低版本");
        }
        int tabNum = vo.getTabType().size();
        //tab数量校验
        if(minVersion < version814) { //存在8.14.0之前的版本
            if(tabNum > 4) {
                throw new BusinessException("-1","8.14版本之前最多支持4个Tab");
            }
        } else { //其他情况
            if(tabNum > 5) {
                throw new BusinessException("-1","最多支持5个Tab");
            }
        }
        //tab校验
        if(minVersion < version814) { //8.14.0之前的版本
            if(vo.getTabType().contains(BottomTabType.LIFE.getType()) || vo.getTabType().contains(BottomTabType.INVEST.getType())) {
                throw new BusinessException("-1","8.14版本之前不支持生活、投资Tab");
            }
        }
        if(minVersion < version9 && maxVersion > version814) {//8.14-9.0
            if(vo.getTabType().contains(BottomTabType.INVEST.getType())) {
                throw new BusinessException("-1","9.0.0版本之前不支持投资Tab");
            }
        }
        if(maxVersion > version9) {//9.0之后
            if(vo.getTabType().contains(BottomTabType.FUND.getType())) {
                throw new BusinessException("-1","9.0.0版本之后不支持基金Tab");
            }
        }
        List<Integer> priorityList = vo.getTabPriority();
        Set<Integer> prioritySet=new HashSet<>(priorityList);
        if (priorityList.size() != prioritySet.size()) {
            throw new BusinessException("-1","Tab优先级不能重复");
        }
    }

    /**
     * @param vo
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 更新配置信息
     * @Date 16:28 2021/9/10
     * @Param [po]
     */
    @Override
    public Boolean updateById(TVChannelConfig vo) {
        TVChannelConfig oldPo = selectById(vo.getConfigId() + "");
        if(oldPo == null) {
            throw new BusinessException("-1", "该条配置不存在，可能已被删除");
        }
        if(StringUtils.isNotEmpty(vo.getTabH5Url())){
            vo.setTabH5Url(vo.getTabH5Url().replace("&amp;", "&"));
        }
        vo.setUpdateTime(new Date());
        int i = tvChannelConfigMapper.updateByPrimaryKeySelective( vo);
        //清除该key对应的缓存
        jedisCluster.del(RedisKeyConstant.CHANNEL_CONFIG + vo.getConfigId());
        jedisCluster.del(RedisKeyConstant.BOTTOM_TAB_CONFIG);

        return i > 0;
    }

    /**
     * @param configId
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 根据configId删除配置信息
     * @Date 16:32 2021/9/10
     * @Param [configId]
     */
    @Override
    public Boolean openById(String configId) {
        TVChannelConfig po = selectById(configId);
        if(Objects.isNull( po)){
            return false;
        }
        po.setOpenStatus(1);
        po.setOnlineTime(new Date());
        po.setOfflineTime(new Date(System.currentTimeMillis() + 100* 100L * 60 * 60 * 24 * 365 * 10));
        po.setUpdateTime(new Date());
        tvChannelConfigMapper.updateByPrimaryKeySelective(po);
        //清除该key对应的缓存
        jedisCluster.del(RedisKeyConstant.CHANNEL_CONFIG+configId);
        jedisCluster.del(RedisKeyConstant.BOTTOM_TAB_CONFIG);
        return true;
    }
    @Override
    public Boolean closeById(String configId) {
        TVChannelConfig po = selectById(configId);
        if(Objects.isNull( po)){
            return false;
        }
        po.setOpenStatus(0);
        po.setOfflineTime(new Date());
        po.setUpdateTime(new Date());
        tvChannelConfigMapper.updateByPrimaryKeySelective(po);
        //清除该key对应的缓存
        jedisCluster.del(RedisKeyConstant.CHANNEL_CONFIG+configId);
        jedisCluster.del(RedisKeyConstant.BOTTOM_TAB_CONFIG);
        return true;
    }

    /**
     * @return List<ChannelConfigPO>
     * <AUTHOR>
     * @Description 配置下拉列表
     * @Date 9:46 2021/9/14
     * @Param []
     **/
    @Override
    public List<ChannelConfigPO> selectConfigList() {
        return dao.selectConfigList();
    }

    /**
     * @param params
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 查询渠道ZC码是否已配置
     * @Date 13:37 2021/9/23
     * @Param [params]
     */
    @Override
    public Boolean count(ChannelConfigPO po) {
        return dao.count(po.getConfigId(), po.getChannelCode().split(",")) > 0;
    }


}
