package com.creditease.ying.yingMgr.service.vchannelconfig;

import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO;
import com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigModifyVO;
import com.creditease.ying.yingMgr.service.vchannelconfig.bean.TVChannelConfig;

import java.util.List;


/**
 * <AUTHOR>
 * @Description 大v渠道配置
 * @Date 15:18 2021/9/9
 **/
public interface IChannelConfigService {
    /**
     * <AUTHOR>
     * @Description 查询渠道配置列表
     * @Date 16:27 2021/9/10
     * @Param [listBean]
     * @return DataGrid
     **/
    DataGrid selectList(ChannelConfigPO listBean);

    /**
     * <AUTHOR>
     * @Description 根据configId查询渠道配置详情
     * @Date 16:27 2021/9/10
     * @Param [configId]
     * @return ChannelConfigPO
     **/
    TVChannelConfig selectById(String configId);

    /**
     * <AUTHOR>
     * @Description 插入配置信息
     * @Date 16:27 2021/9/10
     * @Param [po]
     * @return java.lang.Boolean
     **/
    String insert(ChannelConfigModifyVO vo);

    /**
     * <AUTHOR>
     * @Description 保存配置和渠道的关联关系
     * @Date 16:28 2021/9/10
     * @Param [po]
     * @return java.lang.Boolean
     **/
//    Boolean saveLink(ChannelConfigPO po);

    /**
     * <AUTHOR>
     * @Description 更新配置信息
     * @Date 16:28 2021/9/10
     * @Param [po]
     * @return java.lang.Boolean
     **/
    Boolean updateById(TVChannelConfig po);

    /**
     * <AUTHOR>
     * @Description 根据configId删除配置信息
     * @Date 16:32 2021/9/10
     * @Param [configId]
     * @return java.lang.Boolean
     **/
    Boolean openById(String configId);
    Boolean closeById(String configId);

    /**
     * <AUTHOR>
     * @Description 配置下拉列表
     * @Date 9:46 2021/9/14
     * @Param []
     * @return List<ChannelConfigPO>
     **/
    List<ChannelConfigPO> selectConfigList();

    /**
     * <AUTHOR>
     * @Description 查询渠道ZC码是否已配置
     * @Date 13:37 2021/9/23
     * @Param [params]
     * @return java.lang.Boolean
     **/
    Boolean count(ChannelConfigPO po);

}
