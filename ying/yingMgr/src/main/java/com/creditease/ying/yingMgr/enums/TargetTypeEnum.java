package com.creditease.ying.yingMgr.enums;

/**
 * 渠道绑定目标类型枚举
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
public enum TargetTypeEnum {
    
    /**
     * 背景图
     */
    BACKGROUND_PIC("BACKGROUND_PIC", "背景图", "背景图管理"),
    
    /**
     * 热点卡片
     */
    HOTSPOT("HOTSPOT", "热点卡片", "热点管理");
    
    /**
     * 类型代码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 模块描述
     */
    private final String module;
    
    TargetTypeEnum(String code, String name, String module) {
        this.code = code;
        this.name = name;
        this.module = module;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getModule() {
        return module;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 类型代码
     * @return 对应的枚举，如果不存在返回null
     */
    public static TargetTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (TargetTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 验证类型代码是否有效
     * 
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
    
    /**
     * 获取所有支持的类型代码
     * 
     * @return 类型代码数组
     */
    public static String[] getAllCodes() {
        TargetTypeEnum[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }
    
    @Override
    public String toString() {
        return code;
    }
}
