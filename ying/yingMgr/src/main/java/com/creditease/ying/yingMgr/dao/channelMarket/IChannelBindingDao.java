package com.creditease.ying.yingMgr.dao.channelMarket;

import com.creditease.ying.yingMgr.model.channelMarket.ChannelBinding;
import com.creditease.ying.yingMgr.model.channelMarket.TargetData;
import com.creditease.ying.yingMgr.model.channelMarket.ChannelInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 渠道绑定DAO接口
 * 支持多种类型数据的渠道绑定功能
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IChannelBindingDao {

    /**
     * 获取目标数据列表
     * 
     * @param name 名称
     * @param type 类型
     * @param status 状态
     * @param pageStart 分页开始位置
     * @param rows 每页行数
     * @return 目标数据列表
     */
    List<TargetData> selectTargetDataList(@Param("name") String name, 
                                          @Param("type") String type, 
                                          @Param("status") String status,
                                          @Param("pageStart") int pageStart, 
                                          @Param("rows") int rows);

    /**
     * 统计目标数据总数
     * 
     * @param name 名称
     * @param type 类型
     * @param status 状态
     * @return 总数
     */
    int countTargetData(@Param("name") String name, 
                        @Param("type") String type, 
                        @Param("status") String status);

    /**
     * 获取渠道列表
     * 
     * @param channelName 渠道名称
     * @param channelType 渠道类型
     * @param status 状态
     * @param pageStart 分页开始位置
     * @param rows 每页行数
     * @return 渠道列表
     */
    List<ChannelInfo> selectChannelList(@Param("channelName") String channelName,
                                        @Param("channelType") String channelType,
                                        @Param("status") String status,
                                        @Param("pageStart") int pageStart,
                                        @Param("rows") int rows);

    /**
     * 统计渠道总数
     * 
     * @param channelName 渠道名称
     * @param channelType 渠道类型
     * @param status 状态
     * @return 总数
     */
    int countChannel(@Param("channelName") String channelName,
                     @Param("channelType") String channelType,
                     @Param("status") String status);

    /**
     * 插入渠道绑定关系
     * 
     * @param channelBindings 绑定关系列表
     * @return 影响行数
     */
    int insertChannelBindings(@Param("list") List<ChannelBinding> channelBindings);

    /**
     * 获取绑定关系列表
     * 
     * @param targetId 目标数据ID
     * @param pageStart 分页开始位置
     * @param rows 每页行数
     * @return 绑定关系列表
     */
    List<ChannelBinding> selectBindingRelations(@Param("targetId") Integer targetId,
                                                @Param("pageStart") int pageStart,
                                                @Param("rows") int rows);

    /**
     * 统计绑定关系总数
     * 
     * @param targetId 目标数据ID
     * @return 总数
     */
    int countBindingRelations(@Param("targetId") Integer targetId);

    /**
     * 删除绑定关系
     * 
     * @param bindingId 绑定关系ID
     * @return 影响行数
     */
    int deleteChannelBinding(@Param("bindingId") Integer bindingId);

    /**
     * 插入目标数据
     * 
     * @param targetData 目标数据
     * @return 影响行数
     */
    int insertTargetData(TargetData targetData);

    /**
     * 更新目标数据
     * 
     * @param targetData 目标数据
     * @return 影响行数
     */
    int updateTargetData(TargetData targetData);

    /**
     * 删除目标数据
     * 
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteTargetData(@Param("ids") List<Integer> ids);

    /**
     * 根据ID获取目标数据
     * 
     * @param id ID
     * @return 目标数据
     */
    TargetData selectTargetDataById(@Param("id") Integer id);

    /**
     * 检查绑定关系是否存在
     * 
     * @param targetId 目标数据ID
     * @param channelId 渠道ID
     * @return 绑定关系数量
     */
    int checkBindingExists(@Param("targetId") Integer targetId, @Param("channelId") Integer channelId);

    /**
     * 根据目标数据ID删除所有绑定关系
     * 
     * @param targetId 目标数据ID
     * @return 影响行数
     */
    int deleteByTargetId(@Param("targetId") Integer targetId);

    /**
     * 根据目标数据ID查询所有绑定关系
     * 
     * @param targetId 目标数据ID
     * @return 绑定关系列表
     */
    List<ChannelBinding> selectByTargetId(@Param("targetId") Integer targetId);

    /**
     * 根据目标数据ID和渠道ID删除特定绑定关系
     * 
     * @param targetId 目标数据ID
     * @param channelId 渠道ID
     * @return 影响行数
     */
    int deleteByTargetIdAndChannelId(@Param("targetId") Integer targetId, @Param("channelId") Integer channelId);
}
