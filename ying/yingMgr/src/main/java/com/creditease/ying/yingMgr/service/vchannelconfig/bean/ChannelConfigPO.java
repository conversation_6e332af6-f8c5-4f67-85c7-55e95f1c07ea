package com.creditease.ying.yingMgr.service.vchannelconfig.bean;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 渠道配置参数定义
 * @Date 2021/9/9 15:35
 **/
@Data
public class ChannelConfigPO {
    /**
     * 配置id
     */
    private String configId;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 配置值
     */
    private String configValue;
    /**
     * 最低版本
     */
    private String versionLimit;
    /**
     * 最高版本
     */
    private String versionMaxLimit;

    /**
     * 渠道ZC码
     */
    private String channelCode;

    /**
     * 上线时间
     */
    private Date onlineTime;

    /**
     * 下线时间
     */
    private Date offlineTime;

    /**
     * 渠道id
     */
    private String channelId;

    private String createTime;
    private String updateTime;

    private int pageId;

    private ListChannelConfigBean[] item;

    /**
     * 未选中图标url
     */
    private String unselectedIconUrl;

    /**
     * 选中图标url
     */
    private String selectedIconUrl;

    /**
     * 选中文字颜色
     */
    private String selectedCharColor;

    /**
     * tab3 ht链接
     */
    private String tabH5Url;

    /**
     * 是否开启 0 否 1 是
     */
    private Integer openStatus;
}
