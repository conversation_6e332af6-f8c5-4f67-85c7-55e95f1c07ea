package com.creditease.ying.yingMgr.model.channelMarket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 渠道绑定实体类
 * 记录目标数据与渠道的绑定关系
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelBinding {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 目标数据ID
     */
    private Integer targetId;

    /**
     * 目标数据类型（CARD-卡片, PRODUCT-产品, ACTIVITY-活动等）
     */
    private String targetType;

    /**
     * 目标数据名称
     */
    private String targetName;

    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 绑定状态（1-有效, 0-无效）
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;
}
