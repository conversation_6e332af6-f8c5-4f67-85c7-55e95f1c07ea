package com.creditease.ying.yingMgr.model.channelMarket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 目标数据实体类
 * 支持多种类型的数据绑定
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TargetData {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 数据名称
     */
    private String name;

    /**
     * 数据类型（CARD-卡片, PRODUCT-产品, ACTIVITY-活动等）
     */
    private String type;

    /**
     * 数据编码
     */
    private String code;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态（1-启用, 0-禁用）
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;
}
