package com.creditease.ying.yingMgr.controller.channelMarket;

import com.creditease.homepage.bean.GetCardChannelListBean;
import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.common.util.Json;
import com.creditease.ying.user.bean.UserBean;
import com.creditease.ying.withchannel.bean.ChannelMarketPo;
import com.creditease.ying.withchannel.service.IChannelMarketService;
import com.creditease.ying.yingMgr.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import redis.clients.jedis.JedisCluster;

import java.util.Date;

/**
 * <AUTHOR> on 2019/12/14.
 * 渠道关联的卡片列表页
 */
@Slf4j(topic = "channelMarket")
@Controller
@RequestMapping("/channelMarket")
public class ChannelMarketController extends BaseController {

    @Autowired
    IChannelMarketService channelMarketService;

    @Autowired
    private JedisCluster jedisCluster;

    private static final String CACHE_KEY = "finance:channel:market:cache";

    /**
     * 清除渠道市场缓存
     */
    private void clearChannelMarketCache() {
        try {
            jedisCluster.del(CACHE_KEY);
            log.info("成功清除渠道市场缓存: {}", CACHE_KEY);
        } catch (Exception e) {
            log.error("清除渠道市场缓存失败: {}", CACHE_KEY, e);
        }
    }

    /**
     * 列表页面
     * @param
     * @return
     */
    @RequestMapping("/list")
    public String list() {
       return "webpage/channelMarket/channelMarketList";
    }

    /**
     * 查询列表
     * @param
     * @return
     */
    @RequestMapping("/dataGrid")
    @ResponseBody
    public DataGrid dataGrid(GetCardChannelListBean queryBean) {
        try{
            return channelMarketService.selectList(queryBean);
        }catch (Exception e){
            log.error("dataGrid error : " , e);
            return new DataGrid();
        }
    }

    /**
     * 更新渠道市场数据
     *
     * @return
     */
    @RequestMapping("/update")
    @ResponseBody
    public Json edit(ChannelMarketPo channelMarket) {
        try {
            UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
            if(userBean!=null){
                channelMarket.setCreater(userBean.getUserName());
            }
            channelMarket.setUpdateTime(new Date());
            if(channelMarketService.update(channelMarket)){
                // 数据更新成功，清除缓存
                clearChannelMarketCache();
                return Json.success("更新成功");
            }
        }catch (Exception e) {
            log.error("更新渠道市场数据失败", e);
            return Json.error(e);
        }
        return Json.error();
    }

    @RequestMapping("/toAdd")
    public String toAdd(){
        return "webpage/channelMarket/channelMarketAdd";
    }

    /**
     * 新增渠道市场数据
     *
     * @return
     */
    @RequestMapping("/insert")
    @ResponseBody
    public Json insert(ChannelMarketPo channelMarket) {
        try {
            UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
            if(userBean!=null){
                channelMarket.setCreater(userBean.getUserName());
            }
            channelMarket.setCreateTime(new Date());
            channelMarket.setUpdateTime(new Date());
            if(channelMarketService.insert(channelMarket)){
                // 数据新增成功，清除缓存
                clearChannelMarketCache();
                return Json.success("添加成功");
            }
        }catch (Exception e) {
            log.error("新增渠道市场数据失败", e);
            return Json.error(e);
        }
        return Json.error();
    }

    /**
     * 删除渠道市场数据
     *
     * @param channelId
     * @return
     */
    @RequestMapping("/delete")
    @ResponseBody
    public Json delete(@RequestParam(value = "channelMarketId") Integer channelId) {
        Json jsonResponse = new Json();
        try{
            channelMarketService.deleteChannelAndCardChannel(channelId);
            // 数据删除成功，清除缓存
            clearChannelMarketCache();
            jsonResponse.setMsg("删除成功！");
            jsonResponse.setSuccess(true);
            return jsonResponse;
        }catch (Exception e){
            log.error("删除渠道市场数据失败", e);
            jsonResponse.setMsg("删除失败！");
            jsonResponse.setSuccess(false);
            return jsonResponse;
        }
    }

}