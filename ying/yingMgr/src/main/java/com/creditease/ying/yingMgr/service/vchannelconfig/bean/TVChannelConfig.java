/*
*
* TVChannelConfig.java
* <AUTHOR>
* @date 2025-09-17
*/
package com.creditease.ying.yingMgr.service.vchannelconfig.bean;

import java.util.Date;

public class TVChannelConfig {
    /**
     * 
     */
    private Integer configId;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * tab栏配置的json值
     */
    private String configValue;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 上线时间
     */
    private Date onlineTime;

    /**
     * 下线时间
     */
    private Date offlineTime;

    /**
     * 最低版本
     */
    private String versionLimit;

    /**
     * 最高版本
     */
    private String versionMaxLimit;

    /**
     * 未选中图标url
     */
    private String unselectedIconUrl;

    /**
     * 选中图标url
     */
    private String selectedIconUrl;

    /**
     * 选中文字颜色
     */
    private String selectedCharColor;

    /**
     * tab3 ht链接
     */
    private String tabH5Url;

    /**
     * 是否开启 0 否 1 是
     */
    private Integer openStatus;

    /**
     * 
     * @return CONFIG_ID 
     */
    public Integer getConfigId() {
        return configId;
    }

    /**
     * 
     * @param configId 
     */
    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    /**
     * 配置名称
     * @return CONFIG_NAME 配置名称
     */
    public String getConfigName() {
        return configName;
    }

    /**
     * 配置名称
     * @param configName 配置名称
     */
    public void setConfigName(String configName) {
        this.configName = configName == null ? null : configName.trim();
    }

    /**
     * tab栏配置的json值
     * @return CONFIG_VALUE tab栏配置的json值
     */
    public String getConfigValue() {
        return configValue;
    }

    /**
     * tab栏配置的json值
     * @param configValue tab栏配置的json值
     */
    public void setConfigValue(String configValue) {
        this.configValue = configValue == null ? null : configValue.trim();
    }

    /**
     * 创建时间
     * @return CREATE_TIME 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间
     * @return UPDATE_TIME 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 上线时间
     * @return ONLINE_TIME 上线时间
     */
    public Date getOnlineTime() {
        return onlineTime;
    }

    /**
     * 上线时间
     * @param onlineTime 上线时间
     */
    public void setOnlineTime(Date onlineTime) {
        this.onlineTime = onlineTime;
    }

    /**
     * 下线时间
     * @return OFFLINE_TIME 下线时间
     */
    public Date getOfflineTime() {
        return offlineTime;
    }

    /**
     * 下线时间
     * @param offlineTime 下线时间
     */
    public void setOfflineTime(Date offlineTime) {
        this.offlineTime = offlineTime;
    }

    /**
     * 最低版本
     * @return VERSION_LIMIT 最低版本
     */
    public String getVersionLimit() {
        return versionLimit;
    }

    /**
     * 最低版本
     * @param versionLimit 最低版本
     */
    public void setVersionLimit(String versionLimit) {
        this.versionLimit = versionLimit == null ? null : versionLimit.trim();
    }

    /**
     * 最高版本
     * @return VERSION_MAX_LIMIT 最高版本
     */
    public String getVersionMaxLimit() {
        return versionMaxLimit;
    }

    /**
     * 最高版本
     * @param versionMaxLimit 最高版本
     */
    public void setVersionMaxLimit(String versionMaxLimit) {
        this.versionMaxLimit = versionMaxLimit == null ? null : versionMaxLimit.trim();
    }

    /**
     * 未选中图标url
     * @return unselected_icon_url 未选中图标url
     */
    public String getUnselectedIconUrl() {
        return unselectedIconUrl;
    }

    /**
     * 未选中图标url
     * @param unselectedIconUrl 未选中图标url
     */
    public void setUnselectedIconUrl(String unselectedIconUrl) {
        this.unselectedIconUrl = unselectedIconUrl == null ? null : unselectedIconUrl.trim();
    }

    /**
     * 选中图标url
     * @return selected_icon_url 选中图标url
     */
    public String getSelectedIconUrl() {
        return selectedIconUrl;
    }

    /**
     * 选中图标url
     * @param selectedIconUrl 选中图标url
     */
    public void setSelectedIconUrl(String selectedIconUrl) {
        this.selectedIconUrl = selectedIconUrl == null ? null : selectedIconUrl.trim();
    }

    /**
     * 选中文字颜色
     * @return selected_char_color 选中文字颜色
     */
    public String getSelectedCharColor() {
        return selectedCharColor;
    }

    /**
     * 选中文字颜色
     * @param selectedCharColor 选中文字颜色
     */
    public void setSelectedCharColor(String selectedCharColor) {
        this.selectedCharColor = selectedCharColor == null ? null : selectedCharColor.trim();
    }

    /**
     * tab3 ht链接
     * @return tab_h5_url tab3 ht链接
     */
    public String getTabH5Url() {
        return tabH5Url;
    }

    /**
     * tab3 ht链接
     * @param tabH5Url tab3 ht链接
     */
    public void setTabH5Url(String tabH5Url) {
        this.tabH5Url = tabH5Url == null ? null : tabH5Url.trim();
    }

    /**
     * 是否开启 0 否 1 是
     * @return open_status 是否开启 0 否 1 是
     */
    public Integer getOpenStatus() {
        return openStatus;
    }

    /**
     * 是否开启 0 否 1 是
     * @param openStatus 是否开启 0 否 1 是
     */
    public void setOpenStatus(Integer openStatus) {
        this.openStatus = openStatus;
    }
}