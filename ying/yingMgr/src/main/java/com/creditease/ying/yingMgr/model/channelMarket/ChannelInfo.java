package com.creditease.ying.yingMgr.model.channelMarket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 渠道信息实体类（基于t_channel_market表，只使用现有字段）
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelInfo {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道参数（对应channel_param字段）
     */
    private String channelParam;

    /**
     * 市场包号（对应market_id字段）
     */
    private String marketId;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
