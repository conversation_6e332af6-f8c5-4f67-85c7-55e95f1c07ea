package com.creditease.ying.yingMgr.controller.connection;

import com.creditease.ying.common.model.Json;
import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.connection.bean.ConnectionPageConfig;
import com.creditease.ying.connection.bean.ConnectionPageConfigVO;
import com.creditease.ying.connection.service.IConnectionPageConfigService;
import com.creditease.ying.yingMgr.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 建联页面配置Controller
 * 
 * <AUTHOR>
 * @date 2024-11-10
 */
@Controller
@RequestMapping("/connectionPageConfig")
@Slf4j
public class ConnectionPageConfigController extends BaseController {

    @Autowired
    private IConnectionPageConfigService connectionPageConfigService;

    private static final String OPERATE_SUCCESS_MSG = "操作成功";
    private static final String OPERATE_FAIL_MSG = "操作失败";



    /**
     * 分页查询建联页面配置列表
     */
    @RequestMapping("/dataGrid")
    @ResponseBody
    public DataGrid dataGrid(ConnectionPageConfigVO vo) {
        try {
            return connectionPageConfigService.getPageList(vo);
        } catch (Exception e) {
            log.error("查询建联页面配置列表异常", e);
            return new DataGrid();
        }
    }

    /**
     * 根据ID查询建联页面配置详情
     */
    @RequestMapping("/getById")
    @ResponseBody
    public Json getById(@RequestParam("id") Long id) {
        try {
            if (id == null) {
                return Json.error("ID不能为空");
            }
            
            ConnectionPageConfig config = connectionPageConfigService.getById(id);
            if (config == null) {
                return Json.error("配置不存在");
            }
            
            return Json.success("查询成功", config);
        } catch (Exception e) {
            log.error("查询建联页面配置详情异常，id={}", id, e);
            return Json.error("查询失败");
        }
    }

    /**
     * 根据页面名称查询建联页面配置
     */
    @RequestMapping("/getByPageName")
    @ResponseBody
    public Json getByPageName(@RequestParam("pageName") String pageName) {
        try {
            if (StringUtils.isEmpty(pageName)) {
                return Json.error("页面名称不能为空");
            }
            
            ConnectionPageConfig config = connectionPageConfigService.getByPageName(pageName);
            return Json.success("查询成功", config);
        } catch (Exception e) {
            log.error("根据页面名称查询建联页面配置异常，pageName={}", pageName, e);
            return Json.error("查询失败");
        }
    }

    /**
     * 新增建联页面配置
     */
    @RequestMapping("/add")
    @ResponseBody
    public Json add(ConnectionPageConfig config, HttpServletRequest request) {
        try {
            // 参数校验
            if (config == null) {
                return Json.error("配置信息不能为空");
            }
            if (StringUtils.isEmpty(config.getPageName())) {
                return Json.error("页面名称不能为空");
            }
            if (config.getPageName().length() > 100) {
                return Json.error("页面名称长度不能超过100字符");
            }
            
            // 设置操作人
            String operator = getCurrentUserName(request);
            config.setOperator(operator);
            
            boolean success = connectionPageConfigService.save(config);
            if (success) {
                log.info("新增建联页面配置成功，pageName={}, operator={}", config.getPageName(), operator);
                return Json.success(OPERATE_SUCCESS_MSG);
            } else {
                return Json.error(OPERATE_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("新增建联页面配置异常", e);
            return Json.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 编辑建联页面配置
     */
    @RequestMapping("/edit")
    @ResponseBody
    public Json edit(ConnectionPageConfig config, HttpServletRequest request) {
        try {
            // 参数校验
            if (config == null || config.getId() == null) {
                return Json.error("配置信息或ID不能为空");
            }
            if (!StringUtils.isEmpty(config.getPageName()) && config.getPageName().length() > 100) {
                return Json.error("页面名称长度不能超过100字符");
            }

            // 设置操作人
            String operator = getCurrentUserName(request);
            config.setOperator(operator);

            boolean success = connectionPageConfigService.updateById(config);
            if (success) {
                log.info("编辑建联页面配置成功，id={}, operator={}", config.getId(), operator);
                return Json.success(OPERATE_SUCCESS_MSG);
            } else {
                return Json.error(OPERATE_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("编辑建联页面配置异常，id={}", config != null ? config.getId() : null, e);
            return Json.error("编辑失败：" + e.getMessage());
        }
    }

    /**
     * 删除建联页面配置
     */
    @RequestMapping("/delete")
    @ResponseBody
    public Json delete(@RequestParam("id") Long id, HttpServletRequest request) {
        try {
            if (id == null) {
                return Json.error("ID不能为空");
            }

            boolean success = connectionPageConfigService.deleteById(id);
            if (success) {
                String operator = getCurrentUserName(request);
                log.info("删除建联页面配置成功，id={}, operator={}", id, operator);
                return Json.success(OPERATE_SUCCESS_MSG);
            } else {
                return Json.error(OPERATE_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("删除建联页面配置异常，id={}", id, e);
            return Json.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除建联页面配置
     */
    @RequestMapping("/deleteBatch")
    @ResponseBody
    public Json deleteBatch(@RequestParam("ids") String ids, HttpServletRequest request) {
        try {
            if (StringUtils.isEmpty(ids)) {
                return Json.error("请选择要删除的记录");
            }

            List<String> idList = Arrays.asList(ids.split(","));
            List<Long> longIds = idList.stream().map(Long::valueOf).collect(java.util.stream.Collectors.toList());

            if (CollectionUtils.isEmpty(longIds)) {
                return Json.error("请选择要删除的记录");
            }

            boolean success = connectionPageConfigService.deleteBatchByIds(longIds);
            if (success) {
                String operator = getCurrentUserName(request);
                log.info("批量删除建联页面配置成功，ids={}, operator={}", ids, operator);
                return Json.success(OPERATE_SUCCESS_MSG);
            } else {
                return Json.error(OPERATE_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("批量删除建联页面配置异常，ids={}", ids, e);
            return Json.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 启用建联页面配置
     */
    @RequestMapping("/enable")
    @ResponseBody
    public Json enable(@RequestParam("id") Long id, HttpServletRequest request) {
        try {
            if (id == null) {
                return Json.error("ID不能为空");
            }

            String operator = getCurrentUserName(request);
            boolean success = connectionPageConfigService.enable(id, operator);
            if (success) {
                log.info("启用建联页面配置成功，id={}, operator={}", id, operator);
                return Json.success(OPERATE_SUCCESS_MSG);
            } else {
                return Json.error(OPERATE_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("启用建联页面配置异常，id={}", id, e);
            return Json.error("启用失败：" + e.getMessage());
        }
    }

    /**
     * 禁用建联页面配置
     */
    @RequestMapping("/disable")
    @ResponseBody
    public Json disable(@RequestParam("id") Long id, HttpServletRequest request) {
        try {
            if (id == null) {
                return Json.error("ID不能为空");
            }

            String operator = getCurrentUserName(request);
            boolean success = connectionPageConfigService.disable(id, operator);
            if (success) {
                log.info("禁用建联页面配置成功，id={}, operator={}", id, operator);
                return Json.success(OPERATE_SUCCESS_MSG);
            } else {
                return Json.error(OPERATE_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("禁用建联页面配置异常，id={}", id, e);
            return Json.error("禁用失败：" + e.getMessage());
        }
    }

    /**
     * 批量启用建联页面配置
     */
    @RequestMapping("/enableBatch")
    @ResponseBody
    public Json enableBatch(@RequestParam("ids") String ids, HttpServletRequest request) {
        try {
            if (StringUtils.isEmpty(ids)) {
                return Json.error("请选择要启用的记录");
            }

            List<String> idList = Arrays.asList(ids.split(","));
            List<Long> longIds = idList.stream().map(Long::valueOf).collect(java.util.stream.Collectors.toList());

            if (CollectionUtils.isEmpty(longIds)) {
                return Json.error("请选择要启用的记录");
            }

            String operator = getCurrentUserName(request);
            boolean success = connectionPageConfigService.enableBatch(longIds, operator);
            if (success) {
                log.info("批量启用建联页面配置成功，ids={}, operator={}", ids, operator);
                return Json.success(OPERATE_SUCCESS_MSG);
            } else {
                return Json.error(OPERATE_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("批量启用建联页面配置异常，ids={}", ids, e);
            return Json.error("批量启用失败：" + e.getMessage());
        }
    }

    /**
     * 批量禁用建联页面配置
     */
    @RequestMapping("/disableBatch")
    @ResponseBody
    public Json disableBatch(@RequestParam("ids") String ids, HttpServletRequest request) {
        try {
            if (StringUtils.isEmpty(ids)) {
                return Json.error("请选择要禁用的记录");
            }

            List<String> idList = Arrays.asList(ids.split(","));
            List<Long> longIds = idList.stream().map(Long::valueOf).collect(java.util.stream.Collectors.toList());

            if (CollectionUtils.isEmpty(longIds)) {
                return Json.error("请选择要禁用的记录");
            }

            String operator = getCurrentUserName(request);
            boolean success = connectionPageConfigService.disableBatch(longIds, operator);
            if (success) {
                log.info("批量禁用建联页面配置成功，ids={}, operator={}", ids, operator);
                return Json.success(OPERATE_SUCCESS_MSG);
            } else {
                return Json.error(OPERATE_FAIL_MSG);
            }
        } catch (Exception e) {
            log.error("批量禁用建联页面配置异常，ids={}", ids, e);
            return Json.error("批量禁用失败：" + e.getMessage());
        }
    }

    /**
     * 检查页面名称是否已存在
     */
    @RequestMapping("/checkPageNameExists")
    @ResponseBody
    public Json checkPageNameExists(@RequestParam("pageName") String pageName,
                                   @RequestParam(value = "excludeId", required = false) Long excludeId) {
        try {
            if (StringUtils.isEmpty(pageName)) {
                return Json.error("页面名称不能为空");
            }

            boolean exists = connectionPageConfigService.checkPageNameExists(pageName, excludeId);
            return Json.success("检查完成", exists);
        } catch (Exception e) {
            log.error("检查页面名称是否存在异常，pageName={}, excludeId={}", pageName, excludeId, e);
            return Json.error("检查失败");
        }
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUserName(HttpServletRequest request) {
        // 这里可以根据实际的用户认证方式获取用户名
        // 例如从session、token等获取
        return "system"; // 默认返回system，实际项目中需要根据具体情况修改
    }
}
