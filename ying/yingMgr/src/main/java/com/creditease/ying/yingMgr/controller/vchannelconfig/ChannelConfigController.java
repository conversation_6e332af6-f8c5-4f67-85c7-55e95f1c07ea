package com.creditease.ying.yingMgr.controller.vchannelconfig;

import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.common.util.AppVersionUtil;
import com.creditease.ying.common.util.Json;
import com.creditease.ying.common.util.JsonUtil;
import com.creditease.ying.user.bean.UserBean;
import com.creditease.ying.yingMgr.controller.BaseController;
import com.creditease.ying.yingMgr.service.vchannelconfig.IChannelConfigService;
import com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO;
import com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigModifyVO;
import com.creditease.ying.yingMgr.service.vchannelconfig.bean.TVChannelConfig;
import com.creditease.ying.yingMgr.service.vchannelconfig.constant.RedisKeyConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 大V渠道配置
 * @Date 17:33 2021/9/10
 **/
@Controller
@RequestMapping("/findChannelConfig")
@Slf4j
public class ChannelConfigController extends BaseController {

    @Autowired
    private IChannelConfigService service;

    private final static String OPERATE_SUCCESS_MSG = "操作成功";

    @RequestMapping("/manager")
    public String manager(HttpServletRequest request) {
        return "/webpage/vchannelconfig/manager";
    }

    /**
     * <AUTHOR>
     * @Description 查询渠道配置信息列表
     * @Date 10:09 2021/9/11
     * @Param [listBean]
     * @return DataGrid
     **/
    @RequestMapping("/dataGrid")
    @ResponseBody
    public DataGrid list(ChannelConfigPO listBean) {
        try {
            return service.selectList(listBean);
        } catch (Exception e) {
            log.error("查询渠道配置信息error：", e);
        }
        return new DataGrid();
    }

    /**
     * <AUTHOR>
     * @Description 根据configId查询渠道配置详情
     * @Date 10:15 2021/9/11
     * @Param [cardId]
     * @return com.creditease.homepage.model.ChannelConfigPO
     **/
    @RequestMapping("/selectById")
    @ResponseBody
    public TVChannelConfig selectById(String configId) {
        try {
            return service.selectById(configId);
        } catch (Exception e) {
            log.error("查询渠道配置信息error：", e);
        }
        return new TVChannelConfig();
    }

    /**
     * 添加配置信息, 页面路径是对的
     * @return
     */
    @RequestMapping("/add")
    public String toAdd(){
        return "/webpage/vchannelconfig/addConfig";
    }


    @RequestMapping("/open")
    @ResponseBody
    public Json open(String configId) {
        if(service.openById(configId)){
            return  Json.success("删除成功");
        }
        return  Json.error("删除失败");
    }
    @RequestMapping("/close")
    @ResponseBody
    public Json close(String configId) {
        if(service.closeById(configId)){
            return  Json.success("删除成功");
        }
        return  Json.error("删除失败");
    }

    /**
     * 新增大v tab配置
     * @param vo
     * @return
     */
    @RequestMapping("/saveChannelConfig")
    @ResponseBody
    public Json saveChannelConfig(ChannelConfigModifyVO vo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Json retJson = new Json(true, OPERATE_SUCCESS_MSG, null);
        try {
            if(!AppVersionUtil.isVersionLegal(vo.getVersionLimit()) || !AppVersionUtil.isVersionLegal(vo.getVersionMaxLimit())) {
                throw new RuntimeException("版本号格式必须为 x.x.x");
            }
            String id = service.insert(vo);
            retJson.setSuccess(Boolean.TRUE);
            retJson.setMsg(id);
        } catch (Exception e) {
            log.error("配置信息保存失败，ChannelConfig:{}.", vo, e);
            retJson.setSuccess(Boolean.FALSE);
            retJson.setMsg(e.getMessage());
        }
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        log.info("用户id为："+userBean.getId()+"用户名称为："+userBean.getUserName()+"在"+sdf.format(new Date())+"进行了新增配置操作");
        return retJson;
    }

//    /**
//     * 保存tab配置与大v渠道关联关系
//     * @param po
//     * @return
//     */
//    @RequestMapping("/saveChannelConfigLink")
//    @ResponseBody
//    public Json saveChannelConfigLink(ChannelConfigPO po) {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Json retJson = new Json(true, OPERATE_SUCCESS_MSG, null);
//        try {
//            if(StringUtils.isEmpty(po.getChannelCode())) {
//                throw new RuntimeException("关联注册渠道不能为空");
//            }
//            //判断渠道必须未ALL_1或者ZC开头
//            String[] channelCodeArr = po.getChannelCode().split(",");
//            for(String channelCode : channelCodeArr) {
//                if(!channelCode.equals(RedisKeyConstant.ALL_1) && !channelCode.startsWith("ZC")) {
//                    throw new RuntimeException("注册渠道【" + channelCode +"】不合法，只能为ALL_1或者ZCxxx");
//                }
//            }
//            //判断传入的渠道ZC码是否包含ALL_1
//            if (po.getChannelCode().contains(RedisKeyConstant.ALL_1)) {
//                if (channelCodeArr.length > 1) {
//                    throw new RuntimeException("ALL_1只能单独配置");
//                }
//            }
//            Boolean saveCardConfigResult = service.saveLink(po);
//
//            if (!saveCardConfigResult) {
//                throw new RuntimeException("配置信息和渠道的关联关系保存失败");
//            }
//        } catch (Exception e) {
//            log.error("配置信息和渠道的关联关系保存失败，ChannelConfig:{}", JsonUtil.beanToJson(po), e);
//            retJson.setSuccess(Boolean.FALSE);
//            retJson.setMsg(e.getMessage());
//        }
//        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
//        log.info("用户id为："+userBean.getId()+"用户名称为："+userBean.getUserName()+"在"+sdf.format(new Date())+"进行了保存渠道配置关联关系操作");
//        return retJson;
//    }

    @RequestMapping("/edit")
    public String editConfig(HttpServletRequest request,
                             String configId) {
        TVChannelConfig channelConfigPO = service.selectById(configId);
        request.setAttribute("channelConfigPO", channelConfigPO);
        return String.format("webpage/vchannelconfig/editConfig");
    }

    /**
     * 修改大v tab配置
     * @param vo
     * @return
     */
    @RequestMapping("/updateById")
    @ResponseBody
    public Json updateById(TVChannelConfig vo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Json retJson = new Json(true, OPERATE_SUCCESS_MSG, null);
        try {
            Boolean result = service.updateById(vo);
            if (!result) {
                throw new RuntimeException("更新配置信息失败");
            }
        } catch (Exception e) {
            log.error("更新配置信息失败，ChannelConfig:{}", vo, e);
            retJson.setSuccess(Boolean.FALSE);
            retJson.setMsg(e.getMessage());
        }
        UserBean userBean = (UserBean) request.getSession().getAttribute("userBean");
        log.info("用户id为："+userBean.getId()+"用户名称为："+userBean.getUserName()+"在"+sdf.format(new Date())+"进行了更新配置信息关系操作");
        return retJson;
    }


    @RequestMapping("/selectConfigList")
    @ResponseBody
    public List<ChannelConfigPO> selectConfigList() {
        try {
            return service.selectConfigList();
        } catch (Exception e) {
            log.error("查询配置下拉列表error：", e);
        }
        return null;
    }
}