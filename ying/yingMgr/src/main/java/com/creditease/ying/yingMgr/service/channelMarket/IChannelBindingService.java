package com.creditease.ying.yingMgr.service.channelMarket;

import com.creditease.ying.common.model.page.DataGrid;

import java.util.List;

/**
 * 渠道绑定服务接口
 * 支持多种类型数据的渠道绑定功能
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IChannelBindingService {

    /**
     * 获取目标数据列表
     * 
     * @param name 名称
     * @param type 类型
     * @param status 状态
     * @param page 页码
     * @param rows 每页行数
     * @return 数据网格
     */
    DataGrid getTargetDataList(String name, String type, String status, int page, int rows);

    /**
     * 获取背景图列表
     * 
     * @param cardName 背景图名称
     * @param useFor 用途
     * @param onlineStatus 上线状态
     * @param page 页码
     * @param rows 每页行数
     * @return 数据网格
     */
    DataGrid getBackgroundPicList(String cardName, String useFor, String onlineStatus, int page, int rows);

    /**
     * 获取渠道列表
     * 
     * @param channelName 渠道名称
     * @param page 页码
     * @param rows 每页行数
     * @return 数据网格
     */
    DataGrid getChannelList(String channelName, int page, int rows);

    /**
     * 绑定渠道
     * 
     * @param targetIds 目标数据ID列表
     * @param channelIds 渠道ID列表
     * @param targetType 目标类型 (BACKGROUND_PIC, HOTSPOT)
     * @param creator 创建人
     */
    void bindChannels(List<Integer> targetIds, List<Integer> channelIds, String targetType, String creator);

    /**
     * 获取绑定关系
     * 
     * @param targetId 目标数据ID
     * @param page 页码
     * @param rows 每页行数
     * @return 数据网格
     */
    DataGrid getBindingRelations(Integer targetId, int page, int rows);

    /**
     * 解绑渠道
     * 
     * @param bindingId 绑定关系ID
     */
    void unbindChannel(Integer bindingId);

    /**
     * 保存目标数据
     * 
     * @param id ID（更新时使用）
     * @param name 名称
     * @param type 类型
     * @param description 描述
     * @param status 状态
     * @param creator 创建人
     */
    void saveTargetData(Integer id, String name, String type, String description, Integer status, String creator);

    /**
     * 删除目标数据
     * 
     * @param ids ID列表
     */
    void deleteTargetData(List<Integer> ids);
}
