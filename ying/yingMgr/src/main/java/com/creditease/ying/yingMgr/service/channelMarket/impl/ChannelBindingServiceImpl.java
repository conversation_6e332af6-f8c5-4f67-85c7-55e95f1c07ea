package com.creditease.ying.yingMgr.service.channelMarket.impl;

import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.yingMgr.dao.channelMarket.IChannelBindingDao;
import com.creditease.ying.yingMgr.model.channelMarket.ChannelBinding;
import com.creditease.ying.yingMgr.model.channelMarket.TargetData;
import com.creditease.ying.yingMgr.service.channelMarket.IChannelBindingService;
import com.creditease.homepage.service.mgr.ICardBgPicService;
import com.creditease.homepage.bean.GetCardBgPicListBean;
import com.creditease.homepage.model.CardBgPicPo;
import com.creditease.ying.withchannel.service.IChannelMarketService;
import com.creditease.ying.withchannel.bean.ChannelMarketPo;
import com.creditease.ying.withchannel.dao.IChannelMarketDao;
import com.creditease.homepage.bean.GetCardChannelListBean;
import com.creditease.homepage.service.mgr.HotspotLivingService;
import com.creditease.homepage.vo.fincir.HotspotLivingCardConfigVo;
import com.creditease.ying.yingMgr.enums.TargetTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

/**
 * 渠道绑定服务实现
 * 支持多种类型数据的渠道绑定功能
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
@Slf4j
public class ChannelBindingServiceImpl implements IChannelBindingService {

    @Autowired
    private IChannelBindingDao channelBindingDao;

    @Autowired
    private ICardBgPicService cardBgPicService;

    @Autowired
    private IChannelMarketService channelMarketService;

    @Autowired
    private IChannelMarketDao channelMarketDao;

    @Autowired
    private HotspotLivingService hotspotLivingService;

    @Override
    public DataGrid getTargetDataList(String name, String type, String status, int page, int rows) {
        try {
            // 先计算分页参数并保存到局部变量，避免被重置
            final int currentPage = page;
            final int pageSize = rows;
            final int pageStart = (currentPage - 1) * pageSize;
            
            log.info("查询目标数据列表 | name: {}, type: {}, status: {}, page: {}, rows: {}, pageStart: {}", 
                    name, type, status, currentPage, pageSize, pageStart);
            
            // 先查询总数，避免分页参数被影响
            int total = channelBindingDao.countTargetData(name, type, status);
            
            // 再查询分页数据
            List<TargetData> list = channelBindingDao.selectTargetDataList(name, type, status, pageStart, pageSize);
            
            log.info("目标数据查询结果 | 总数: {}, 当前页数据量: {}", total, list != null ? list.size() : 0);
            
            // 创建返回对象时先设置总数，再设置数据，避免分页参数重置问题
            DataGrid dataGrid = new DataGrid();
            dataGrid.setTotal((long) total);
            dataGrid.setRows(list);
            
            return dataGrid;
        } catch (Exception e) {
            log.error("获取目标数据列表失败", e);
            DataGrid dataGrid = new DataGrid();
            dataGrid.setTotal(0L);
            dataGrid.setRows(new java.util.ArrayList<>());
            return dataGrid;
        }
    }

    @Override
    public DataGrid getBackgroundPicList(String cardName, String useFor, String onlineStatus, int page, int rows) {
        try {
            // 先计算分页参数并保存到局部变量，避免被重置
            final int currentPage = page;
            final int pageSize = rows;
            final int pageStart = (currentPage - 1) * pageSize;
            
            log.info("查询背景图列表 | cardName: {}, useFor: {}, onlineStatus: {}, page: {}, rows: {}, pageStart: {}", 
                    cardName, useFor, onlineStatus, currentPage, pageSize, pageStart);
            
            GetCardBgPicListBean queryBean = new GetCardBgPicListBean();
            queryBean.setCardName(cardName);
            queryBean.setUseFor(useFor);
            
            // 转换 onlineStatus 字符串为 Integer
            if (onlineStatus != null && !onlineStatus.isEmpty()) {
                try {
                    queryBean.setOnlineStatus(Integer.parseInt(onlineStatus));
                } catch (NumberFormatException e) {
                    log.warn("onlineStatus 格式错误: {}", onlineStatus);
                    queryBean.setOnlineStatus(1); // 默认值
                }
            } else {
                queryBean.setOnlineStatus(1); // 默认值
            }
            
            queryBean.setPage(currentPage);
            queryBean.setRows(pageSize);
            
            DataGrid result = cardBgPicService.getPageDataGrid(queryBean);
            log.info("背景图查询结果 | 总数: {}, 当前页数据量: {}", 
                    result.getTotal(), result.getRows() != null ? result.getRows().size() : 0);
            
            return result;
        } catch (Exception e) {
            log.error("获取背景图列表失败", e);
            DataGrid dataGrid = new DataGrid();
            dataGrid.setTotal(0L);
            dataGrid.setRows(new java.util.ArrayList<>());
            return dataGrid;
        }
    }

    @Override
    public DataGrid getChannelList(String channelName, int page, int rows) {
        try {
            // 先计算分页参数并保存到局部变量，避免被重置
            final int currentPage = page;
            final int pageSize = rows;
            final int pageStart = (currentPage - 1) * pageSize;
            
            log.info("查询渠道列表 | channelName: {}, page: {}, rows: {}, pageStart: {}", 
                    channelName, currentPage, pageSize, pageStart);
            
            // 使用现有的渠道市场服务
            GetCardChannelListBean queryBean = new GetCardChannelListBean();
            queryBean.setChannelName(channelName);
            queryBean.setPage(currentPage);
            queryBean.setRows(pageSize);
            
            DataGrid result = channelMarketService.selectList(queryBean);
            log.info("渠道列表查询结果 | 总数: {}, 当前页数据量: {}", 
                    result.getTotal(), result.getRows() != null ? result.getRows().size() : 0);
            
            return result;
        } catch (Exception e) {
            log.error("获取渠道列表失败", e);
            DataGrid dataGrid = new DataGrid();
            dataGrid.setTotal(0L);
            dataGrid.setRows(new java.util.ArrayList<>());
            return dataGrid;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindChannels(List<Integer> targetIds, List<Integer> channelIds, String targetType, String creator) {
        try {
            // 1. 验证目标类型是否有效
            TargetTypeEnum targetTypeEnum = TargetTypeEnum.getByCode(targetType);
            if (targetTypeEnum == null) {
                throw new RuntimeException("不支持的目标类型: " + targetType + "，支持的类型: " + String.join(", ", TargetTypeEnum.getAllCodes()));
            }
            
            // 2. 验证目标数据和渠道是否存在
            for (Integer targetId : targetIds) {
                if (TargetTypeEnum.BACKGROUND_PIC.equals(targetTypeEnum)) {
                    // 验证背景图是否存在且有效
                    CardBgPicPo bgPic = cardBgPicService.getCardBgPicById(targetId);
                    if (bgPic == null) {
                        throw new RuntimeException("背景图不存在，ID: " + targetId);
                    }
                    
                    Date now = new Date();
                    if (bgPic.getOnlineTime() != null && bgPic.getOnlineTime().after(now)) {
                        throw new RuntimeException("背景图未上线，ID: " + targetId);
                    }
                    
                    if (bgPic.getOfflineTime() != null && bgPic.getOfflineTime().before(now)) {
                        throw new RuntimeException("背景图已下线，ID: " + targetId);
                    }
                    
                    log.info("背景图验证通过，ID: {}, 名称: {}", targetId, bgPic.getName());
                } else if (TargetTypeEnum.HOTSPOT.equals(targetTypeEnum)) {
                    // 验证热点卡片是否存在且有效
                    HotspotLivingCardConfigVo hotspotCard = hotspotLivingService.getCardById(targetId);
                    if (hotspotCard == null) {
                        throw new RuntimeException("热点卡片不存在，ID: " + targetId);
                    }
                    
                    Date now = new Date();
                    if (hotspotCard.getOnlineTime() != null && hotspotCard.getOnlineTime().after(now)) {
                        throw new RuntimeException("热点卡片未上线，ID: " + targetId);
                    }
                    
                    if (hotspotCard.getOfflineTime() != null && hotspotCard.getOfflineTime().before(now)) {
                        throw new RuntimeException("热点卡片已下线，ID: " + targetId);
                    }
                    
                    log.info("热点卡片验证通过，ID: {}, 名称: {}", targetId, hotspotCard.getName());
                } else {
                    // 其他类型暂时只做基本验证
                    log.info("目标数据验证通过，类型: {}, ID: {}", targetTypeEnum.getName(), targetId);
                }
            }
            
                // 验证渠道是否存在
                for (Integer channelId : channelIds) {
                    // 验证渠道存在性
                    ChannelMarketPo channel = channelMarketDao.selectByUniqueKey(channelId);
                    if (channel == null) {
                        throw new RuntimeException("渠道不存在，ID: " + channelId);
                    }
                    log.info("渠道验证通过，ID: {}, 名称: {}", channelId, channel.getChannelName());
                }
            
            // 2. 无感知更新：增量添加和删除
            Date now = new Date();
            int totalAdded = 0;
            int totalDeleted = 0;
            
            for (Integer targetId : targetIds) {
                // 获取该背景图当前的绑定关系
                List<ChannelBinding> currentBindings = channelBindingDao.selectByTargetId(targetId);
                Set<Integer> currentChannelIds = currentBindings.stream()
                    .map(ChannelBinding::getChannelId)
                    .collect(Collectors.toSet());
                
                // 计算需要添加和删除的渠道
                Set<Integer> newChannelIds = new HashSet<>(channelIds);
                Set<Integer> toAdd = new HashSet<>(newChannelIds);
                toAdd.removeAll(currentChannelIds); // 需要添加的渠道
                
                Set<Integer> toDelete = new HashSet<>(currentChannelIds);
                toDelete.removeAll(newChannelIds); // 需要删除的渠道
                
                log.info("目标数据 {} 绑定关系更新：当前={}, 新选择={}, 需添加={}, 需删除={}", 
                    targetId, currentChannelIds, newChannelIds, toAdd, toDelete);
                
                // 删除不再需要的绑定关系
                if (!toDelete.isEmpty()) {
                    for (Integer channelId : toDelete) {
                        int deleted = channelBindingDao.deleteByTargetIdAndChannelId(targetId, channelId);
                        totalDeleted += deleted;
                    }
                    log.info("目标数据 {} 删除了 {} 个绑定关系", targetId, toDelete.size());
                }
                
                // 添加新的绑定关系
                if (!toAdd.isEmpty()) {
                    List<ChannelBinding> newBindings = new ArrayList<>();
                    String targetName = getTargetName(targetId, targetType);
                    
                    for (Integer channelId : toAdd) {
                        // 获取渠道信息
                        ChannelMarketPo channel = channelMarketDao.selectByUniqueKey(channelId);
                        if (channel != null) {
                            ChannelBinding binding = ChannelBinding.builder()
                                .targetId(targetId)
                                .targetType(targetType)
                                .targetName(targetName)
                                .channelId(channelId)
                                .channelName(channel.getChannelName())
                                .status(1)
                                .creator(creator)
                                .createTime(now)
                                .updateTime(now)
                                .build();
                            newBindings.add(binding);
                        }
                    }
                    
                    if (!newBindings.isEmpty()) {
                        int inserted = channelBindingDao.insertChannelBindings(newBindings);
                        totalAdded += inserted;
                        log.info("目标数据 {} 添加了 {} 个新的绑定关系", targetId, newBindings.size());
                    }
                }
            }
            
            log.info("绑定关系更新完成：添加了 {} 个，删除了 {} 个", totalAdded, totalDeleted);
            
            log.info("渠道绑定成功 | targetIds: {}, channelIds: {}, creator: {}", 
                    targetIds, channelIds, creator);
        } catch (Exception e) {
            log.error("渠道绑定失败", e);
            throw e;
        }
    }

    @Override
    public DataGrid getBindingRelations(Integer targetId, int page, int rows) {
        try {
            // 先计算分页参数并保存到局部变量，避免被重置
            final int currentPage = page;
            final int pageSize = rows;
            final int pageStart = (currentPage - 1) * pageSize;
            
            log.info("查询绑定关系 | targetId: {}, page: {}, rows: {}, pageStart: {}", targetId, currentPage, pageSize, pageStart);
            
            // 先查询总数，避免分页参数被影响
            int total = channelBindingDao.countBindingRelations(targetId);
            
            // 再查询分页数据
            List<ChannelBinding> list = channelBindingDao.selectBindingRelations(targetId, pageStart, pageSize);
            
            log.info("绑定关系查询结果 | 总数: {}, 当前页数据量: {}", total, list != null ? list.size() : 0);
            
            // 创建返回对象时先设置总数，再设置数据，避免分页参数重置问题
            DataGrid dataGrid = new DataGrid();
            dataGrid.setTotal((long) total);
            dataGrid.setRows(list);
            
            return dataGrid;
        } catch (Exception e) {
            log.error("获取绑定关系失败", e);
            DataGrid dataGrid = new DataGrid();
            dataGrid.setTotal(0L);
            dataGrid.setRows(new java.util.ArrayList<>());
            return dataGrid;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindChannel(Integer bindingId) {
        try {
            int result = channelBindingDao.deleteChannelBinding(bindingId);
            if (result == 0) {
                throw new RuntimeException("解绑失败，绑定关系不存在");
            }
            log.info("渠道解绑成功 | bindingId: {}", bindingId);
        } catch (Exception e) {
            log.error("解绑渠道失败", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTargetData(Integer id, String name, String type, String description, Integer status, String creator) {
        try {
            Date now = new Date();
            if (id == null) {
                // 新增
                TargetData targetData = TargetData.builder()
                    .name(name)
                    .type(type)
                    .description(description)
                    .status(status)
                    .creator(creator)
                    .createTime(now)
                    .updateTime(now)
                    .build();
                channelBindingDao.insertTargetData(targetData);
            } else {
                // 更新
                TargetData targetData = TargetData.builder()
                    .id(id)
                    .name(name)
                    .type(type)
                    .description(description)
                    .status(status)
                    .updateTime(now)
                    .build();
                channelBindingDao.updateTargetData(targetData);
            }
            log.info("保存目标数据成功 | id: {}, name: {}, type: {}", id, name, type);
        } catch (Exception e) {
            log.error("保存目标数据失败", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTargetData(List<Integer> ids) {
        try {
            int result = channelBindingDao.deleteTargetData(ids);
            if (result == 0) {
                throw new RuntimeException("删除失败，数据不存在");
            }
            log.info("删除目标数据成功 | ids: {}", ids);
        } catch (Exception e) {
            log.error("删除目标数据失败", e);
            throw e;
        }
    }
    
    /**
     * 根据目标类型和目标ID获取目标名称
     */
    private String getTargetName(Integer targetId, String targetType) {
        try {
            TargetTypeEnum targetTypeEnum = TargetTypeEnum.getByCode(targetType);
            if (targetTypeEnum == null) {
                return targetType + "_" + targetId;
            }
            
            switch (targetTypeEnum) {
                case BACKGROUND_PIC:
                    CardBgPicPo bgPic = cardBgPicService.getCardBgPicById(targetId);
                    return bgPic != null ? bgPic.getName() : targetTypeEnum.getName() + "_" + targetId;
                case HOTSPOT:
                    HotspotLivingCardConfigVo hotspotCard = hotspotLivingService.getCardById(targetId);
                    return hotspotCard != null ? hotspotCard.getName() : targetTypeEnum.getName() + "_" + targetId;
                default:
                    return targetTypeEnum.getName() + "_" + targetId;
            }
        } catch (Exception e) {
            log.warn("获取目标名称失败，使用默认名称: targetId={}, targetType={}", targetId, targetType, e);
            return targetType + "_" + targetId;
        }
    }
}
