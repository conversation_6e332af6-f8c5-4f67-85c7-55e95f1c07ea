/*
*
* TVChannelConfigMapper.java
* <AUTHOR>
* @date 2025-09-17
*/
package com.creditease.ying.yingMgr.service.vchannelconfig.dao;

import java.util.List;

import com.creditease.ying.yingMgr.service.vchannelconfig.bean.ChannelConfigPO;
import com.creditease.ying.yingMgr.service.vchannelconfig.bean.TVChannelConfig;

public interface TVChannelConfigMapper {
    /**
     *
     * @mbg.generated 2025-09-17
     */
    int deleteByPrimaryKey(Integer configId);

    /**
     *
     * @mbg.generated 2025-09-17
     */
    int insert(TVChannelConfig record);

    /**
     *
     * @mbg.generated 2025-09-17
     */
    int insertSelective(TVChannelConfig record);

    /**
     *
     * @mbg.generated 2025-09-17
     */
    TVChannelConfig selectByPrimaryKey(Integer configId);
    List<TVChannelConfig> selectPage(ChannelConfigPO listBean);

    /**
     *
     * @mbg.generated 2025-09-17
     */
    int updateByPrimaryKeySelective(TVChannelConfig record);

    /**
     *
     * @mbg.generated 2025-09-17
     */
    int updateByPrimaryKey(TVChannelConfig record);
}