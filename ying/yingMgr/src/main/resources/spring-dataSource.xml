<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
http://www.springframework.org/schema/context
http://www.springframework.org/schema/context/spring-context-3.2.xsd
http://www.springframework.org/schema/tx
http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
http://www.springframework.org/schema/aop
http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
">
	<!-- 自动扫描(自动注入) -->
	<!-- 只扫描yingMgr依赖的服务 -->
	<context:component-scan base-package="com.creditease.ying.api.taglib" />
    <context:component-scan base-package="com.creditease.ying.api.yrd" />
    <context:component-scan base-package="com.creditease.ying.api.jyb" />
	<context:component-scan base-package="com.creditease.ying.api.push"/>
	<context:component-scan base-package="com.creditease.ying.memberredeemapply">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.thirdchannelconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.common">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.yingMgr">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.channel">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.ticket">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.market">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.member">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.p2puser">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.message">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.yrb">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<!-- 新增子包时 需要在此配置文件开启包扫描 否则无法将bean注入容器 -->
	<context:component-scan base-package="com.creditease.ying.newYrb" />
	<context:component-scan base-package="com.creditease.ying.moduletips" />
	<context:component-scan base-package="com.creditease.ying.treasure">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.product">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.order">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.sysconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.push">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.version">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.user">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	 <context:component-scan base-package="com.creditease.ying.fund">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
    </context:component-scan>
    <context:component-scan base-package="com.creditease.ying.api.insurance">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
	<context:component-scan base-package="com.creditease.ying.appAudit">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.insuranceNew">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
    <context:component-scan base-package="com.creditease.ying.api.fund">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
	<context:component-scan base-package="com.creditease.ying.platform">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.owmFinance">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.fortuneActivity">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.activity">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
    <context:component-scan base-package="com.creditease.ying.activityCenter" />
	<context:component-scan base-package="com.creditease.ying.bank">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.appinit">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.carousel">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.sendPrize">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.carouselWeb">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.community">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.recharge">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.withdraw">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.share">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.device">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.apptheme">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.search">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.point">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.maintain">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.shake">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.zscoupons">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
    <context:component-scan base-package="com.creditease.ying.watch">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
	<context:component-scan base-package="com.creditease.ying.appad">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
    <context:component-scan base-package="com.creditease.ying.fincir">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
    <context:component-scan base-package="com.creditease.ying.shareConfig">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
    <context:component-scan base-package="com.creditease.ying.kaconfig">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
	<context:component-scan base-package="com.creditease.ying.lock">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.wallet">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.util">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.gatedLaunch">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.coupon">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.homepage.service.mgr">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.homepage.define">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.taskgame">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.temperatureconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.yingApi.aop">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.fundsupermoneyconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.fundsupermoneyincome">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.fundsupermoneyorders">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.fundsupermoneyawards">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.fundsupermoneyasset">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.tmpenrolllist">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.growthclubrecharges">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.widget">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.widgetgroup">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.mgr">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.exclusiveFinanceProduct">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.traderecord">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.customerserviceconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.customerservicetipsconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.rypackconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.zhimaconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.lgsLimiteConfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.membercategory">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.memberinfo">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.membermemberprivilege">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.memberorder">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.memberuserprivilege">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.memberuserinfo">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.memberredeemcode">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.memberproductredeemcode">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.memberproduct">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.memberprivilege">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.scrm">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

	<context:component-scan base-package="com.creditease.ying.newYrb">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.api.reward">
	<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.api.fcard">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.weixin">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.scrm">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.members">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>


	<!-- 	<context:component-scan base-package="com.creditease.ying.notice">
            <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
        </context:component-scan> -->

	<!--<context:component-scan base-package="com.creditease.ying.wallet">-->
		<!--<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>-->
	<!--</context:component-scan>-->
	<context:component-scan base-package="com.creditease.ying.pointbiz">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.pointNew">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.advice">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.healthcoin" />

	<context:component-scan base-package="com.creditease.ying.taglib">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.api.finance" />
	<context:component-scan base-package="com.creditease.ying.api.cfp" />
	<context:component-scan base-package="com.creditease.ying.api.fcard" />
	<context:component-scan base-package="com.creditease.ying.api.reward" />
	<context:component-scan base-package="com.creditease.ying.cfpStatisticData" />
    <context:component-scan base-package="com.creditease.ying.api.robot" />
	<context:component-scan base-package="com.creditease.ying.robot" />
	<context:component-scan base-package="com.creditease.ying.taglib"/>
	<context:component-scan base-package="com.creditease.ying.withchannel"/>
	<context:component-scan base-package="com.creditease.ying.api.yingapi"/>
	<context:component-scan base-package="com.creditease.ying.api.baseservice" />
	<context:component-scan base-package="com.creditease.ying.api.support" />
	<context:component-scan base-package="com.creditease.ying.yingMgr.service"/>
	<context:component-scan base-package="com.creditease.ying.api.marketplatform">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.loadingpage">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.profitshowconfig">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />

	</context:component-scan><context:component-scan base-package="com.creditease.ying.headerCard">
	<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.lifeprivilegeposition">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.fcardconf">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.tabdic">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<context:component-scan base-package="com.creditease.ying.healthTask">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<!-- 配置数据源 -->
	<bean name="dataSource1" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <property name="url" value="${mysql.db.url}" />
		<property name="username" value="${mysql.db.username}" />
		<property name="password" value="${mysql.db.password}" />

		<!-- 初始化连接大小 -->
		<property name="initialSize" value="1" />
		<!-- 连接池最大使用连接数量 -->
		<property name="maxActive" value="20" />
		<!-- 连接池最小空闲 -->
		<property name="minIdle" value="1" />
		<!-- 获取连接最大等待时间 -->
		<property name="maxWait" value="60000" />
		<!-- <property name="poolPreparedStatements" value="true" /> -->
		<!--<property name="maxPoolPreparedStatementPerConnectionSize" value="33" /> -->
		<property name="validationQuery" value="SELECT 1" />
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="25200000" />
		<!-- 打开removeAbandoned功能 -->
		<property name="removeAbandoned" value="true" />
		<!-- 1800秒，也就是30分钟 -->
		<property name="removeAbandonedTimeout" value="180" />
		<!-- 关闭abanded连接时输出错误日志 -->
		<property name="logAbandoned" value="true" />
		<!-- 监控数据库 -->
		<!-- <property name="filters" value="stat" /> -->
		<property name="filters" value="mergeStat" />
	</bean>

	<!-- 配置数据源 -->
	<bean name="dataSource2" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
		<property name="url" value="${mysql.db2.url}" />
		<property name="username" value="${mysql.db2.username}" />
		<property name="password" value="${mysql.db2.password}" />

		<!-- 初始化连接大小 -->
		<property name="initialSize" value="1" />
		<!-- 连接池最大使用连接数量 -->
		<property name="maxActive" value="10" />
		<!-- 连接池最小空闲 -->
		<property name="minIdle" value="1" />
		<!-- 获取连接最大等待时间 -->
		<property name="maxWait" value="60000" />
		<!-- <property name="poolPreparedStatements" value="true" /> -->
		<!--<property name="maxPoolPreparedStatementPerConnectionSize" value="33" /> -->
		<property name="validationQuery" value="SELECT 1" />
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="25200000" />
		<!-- 打开removeAbandoned功能 -->
		<property name="removeAbandoned" value="true" />
		<!-- 1800秒，也就是30分钟 -->
		<property name="removeAbandonedTimeout" value="1800" />
		<!-- 关闭abanded连接时输出错误日志 -->
		<property name="logAbandoned" value="true" />
		<!-- 监控数据库 -->
		<!-- <property name="filters" value="stat" /> -->
		<property name="filters" value="mergeStat" />
	</bean>

	<!-- myBatis文件 -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="dataSource" ref="dataSource1" />
		<!-- 自动扫描entity目录, 省掉Configuration.xml里的手工配置 -->
		<property name="mapperLocations">
			<list>
				<value>classpath*:com/creditease/ying/**/mapping/**/*.xml</value>
				<value>classpath*:com/creditease/homepage/**/mapping/**/*.xml</value>
				<value>classpath*:com/creditease/**/mapping/**/*.xml</value>
				<value>classpath*:com/creditease/ying/dao/mapping/**/*.xml</value>
				<value>classpath*:mapper/**/*.xml</value>
			</list>
		</property>
		<property name="configLocation" value="classpath:mybatis-config.xml"/>
	</bean>
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="com.creditease.ying.common.dao.mybatis;com.creditease.homepage.mybatis;
		com.creditease.ying.dao;com.creditease.ying.watch.mybatis;com.creditease.ying.fund.mapping;
		com.creditease.ying.taglib.dao;com.creditease.ying.yingMgr.dao;com.creditease.ying.yingMgr.assetsconfig;com.creditease.ying.yingMgr.todoitems;com.creditease.ying.yingMgr.fundh5.mapping;com.creditease.ying.yingMgr.life.mapping;
		com.creditease.ying.exclusiveFinanceProduct.dao.mapper;com.creditease.ying.withchannel.dao;com.creditease.ying.appad.dao;
		com.creditease.ying.yingMgr.service.nxsyamc.dao;com.creditease.ying.yingMgr.service.logindata.mapper;com.creditease.ying.yingMgr.service.vchannelconfig.dao;com.creditease.ying.fincir.mapper;
		com.creditease.ying.insurance.mapper;com.creditease.ying.appAudit.dao; com.creditease.ying.healthTask.dao;com.creditease.ying.logindata.mapper" />
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
	</bean>
	<bean id="TFundGroupConfigDao" class="org.mybatis.spring.mapper.MapperFactoryBean">
		<property name="mapperInterface" value="com.creditease.ying.fund.dao.TFundGroupConfigDao"/>
		<property name="sqlSessionFactory" ref="sqlSessionFactory" />
	</bean>

	<!-- 配置事务管理器 -->
	<bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource1" />
	</bean>

	<!-- 注解方式配置事物 -->
	<tx:annotation-driven transaction-manager="transactionManager" />

	<!-- 配置druid监控spring jdbc -->
	<bean id="druid-stat-interceptor" class="com.alibaba.druid.support.spring.stat.DruidStatInterceptor" />
	<bean id="druid-stat-pointcut" class="org.springframework.aop.support.JdkRegexpMethodPointcut" scope="prototype">
		<property name="patterns">
			<list>
				<value>com.creditease.ying.yingMgr.service.*</value>
			</list>
		</property>
	</bean>
	<bean id="dataSourceAnnotationAspect" class="com.creditease.ying.common.aspect.DataSourceAnnotationAspect" />
	<!-- 自动数据源设定 -->
	<aop:config>
		<aop:advisor advice-ref="druid-stat-interceptor" pointcut-ref="druid-stat-pointcut" />
		<aop:aspect ref="dataSourceAnnotationAspect">
			<aop:around method="setDataSource" pointcut="execution(* com.creditease.ying.yingMgr.*.*(..))" />
			<!-- <aop:around method="setDataSource" pointcut="execution(* com.creditease.ying.*.dao.mybatis.impl.*.*(..))" /> -->
		</aop:aspect>
	</aop:config>


</beans>
