<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.creditease.ying.yingMgr.dao.channelMarket.IChannelBindingDao">

    <!-- 目标数据结果映射 -->
    <resultMap id="TargetDataResultMap" type="com.creditease.ying.yingMgr.model.channelMarket.TargetData">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 渠道信息结果映射（基于t_channel_market表，只使用现有字段） -->
    <resultMap id="ChannelInfoResultMap" type="com.creditease.ying.yingMgr.model.channelMarket.ChannelInfo">
        <id column="id" property="id"/>
        <result column="channel_name" property="channelName"/>
        <result column="channel_param" property="channelParam"/>
        <result column="market_id" property="marketId"/>
        <result column="creater" property="creater"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 渠道绑定结果映射 -->
    <resultMap id="ChannelBindingResultMap" type="com.creditease.ying.yingMgr.model.channelMarket.ChannelBinding">
        <id column="id" property="id"/>
        <result column="target_id" property="targetId"/>
        <result column="target_type" property="targetType"/>
        <result column="target_name" property="targetName"/>
        <result column="channel_id" property="channelId"/>
        <result column="channel_name" property="channelName"/>
        <result column="status" property="status"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- BaseResultMap 别名，指向 ChannelBindingResultMap -->
    <resultMap id="BaseResultMap" type="com.creditease.ying.yingMgr.model.channelMarket.ChannelBinding">
        <id column="id" property="id"/>
        <result column="target_id" property="targetId"/>
        <result column="target_type" property="targetType"/>
        <result column="target_name" property="targetName"/>
        <result column="channel_id" property="channelId"/>
        <result column="channel_name" property="channelName"/>
        <result column="status" property="status"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

        <!-- 获取目标数据列表（使用背景图表，只查询有效的背景图） -->
        <select id="selectTargetDataList" resultMap="TargetDataResultMap">
            SELECT 
                id,
                name,
                'BACKGROUND_PIC' as type,
                '' as code,
                remark as description,
                status,
                priority as sort_order,
                '' as creator,
                create_time,
                update_time,
                remark
            FROM t_card_bg_pic
            <where>
                status = 1
                AND (online_time IS NULL OR online_time &lt;= NOW())
                AND (offline_time IS NULL OR offline_time &gt;= NOW())
                <if test="name != null and name != ''">
                    AND name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="type != null and type != ''">
                    AND use_for = #{type}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
            </where>
            ORDER BY priority ASC, create_time DESC
            LIMIT #{pageStart}, #{rows}
        </select>

        <!-- 统计目标数据总数（使用背景图表，只统计有效的背景图） -->
        <select id="countTargetData" resultType="int">
            SELECT COUNT(*) FROM t_card_bg_pic
            <where>
                status = 1
                AND (online_time IS NULL OR online_time &lt;= NOW())
                AND (offline_time IS NULL OR offline_time &gt;= NOW())
                <if test="name != null and name != ''">
                    AND name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="type != null and type != ''">
                    AND use_for = #{type}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
            </where>
        </select>

    <!-- 获取渠道列表（基于t_channel_market表，只使用现有字段） -->
    <select id="selectChannelList" resultMap="ChannelInfoResultMap">
        SELECT 
            id,
            channel_name,
            channel_param,
            market_id,
            creater,
            create_time,
            update_time
        FROM t_channel_market
        <where>
            <if test="channelName != null and channelName != ''">
                AND channel_name LIKE CONCAT('%', #{channelName}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{pageStart}, #{rows}
    </select>

    <!-- 统计渠道总数（基于t_channel_market表） -->
    <select id="countChannel" resultType="int">
        SELECT COUNT(*) FROM t_channel_market
        <where>
            <if test="channelName != null and channelName != ''">
                AND channel_name LIKE CONCAT('%', #{channelName}, '%')
            </if>
        </where>
    </select>

    <!-- 插入渠道绑定关系 -->
    <insert id="insertChannelBindings" parameterType="java.util.List">
        INSERT INTO t_channel_binding (
            target_id, target_type, target_name,
            channel_id, channel_name,
            status, creator, create_time, remark
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.targetId}, #{item.targetType}, #{item.targetName},
                #{item.channelId}, #{item.channelName},
                #{item.status}, #{item.creator}, #{item.createTime}, #{item.remark}
            )
        </foreach>
    </insert>

    <!-- 获取绑定关系列表 -->
    <select id="selectBindingRelations" resultMap="ChannelBindingResultMap">
        SELECT * FROM t_channel_binding
        WHERE target_id = #{targetId}
        ORDER BY create_time DESC
        LIMIT #{pageStart}, #{rows}
    </select>

    <!-- 统计绑定关系总数 -->
    <select id="countBindingRelations" resultType="int">
        SELECT COUNT(*) FROM t_channel_binding
        WHERE target_id = #{targetId}
    </select>

    <!-- 删除绑定关系 -->
    <delete id="deleteChannelBinding">
        DELETE FROM t_channel_binding WHERE id = #{bindingId}
    </delete>

    <!-- 插入目标数据 -->
    <insert id="insertTargetData" parameterType="com.creditease.ying.yingMgr.model.channelMarket.TargetData" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_target_data (
            name, type, code, description, status, sort_order, creator, create_time, remark
        ) VALUES (
            #{name}, #{type}, #{code}, #{description}, #{status}, #{sortOrder}, #{creator}, #{createTime}, #{remark}
        )
    </insert>

    <!-- 更新目标数据 -->
    <update id="updateTargetData" parameterType="com.creditease.ying.yingMgr.model.channelMarket.TargetData">
        UPDATE t_target_data
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="code != null">code = #{code},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除目标数据 -->
    <delete id="deleteTargetData">
        DELETE FROM t_target_data WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据ID获取目标数据（使用背景图表） -->
    <select id="selectTargetDataById" resultMap="TargetDataResultMap">
        SELECT 
            id,
            name,
            'BACKGROUND_PIC' as type,
            '' as code,
            remark as description,
            status,
            priority as sort_order,
            '' as creator,
            create_time,
            update_time,
            remark
        FROM t_card_bg_pic WHERE id = #{id}
    </select>

    <!-- 检查绑定关系是否存在 -->
    <select id="checkBindingExists" resultType="int">
        SELECT COUNT(*) FROM t_channel_binding
        WHERE target_id = #{targetId} AND channel_id = #{channelId}
    </select>

    <!-- 根据目标数据ID删除所有绑定关系 -->
    <delete id="deleteByTargetId">
        DELETE FROM t_channel_binding
        WHERE target_id = #{targetId}
    </delete>

    <!-- 根据目标数据ID查询所有绑定关系 -->
    <select id="selectByTargetId" resultMap="BaseResultMap">
        SELECT 
            id, target_id, target_type, target_name, channel_id, channel_name, 
            status, creator, 
            create_time, update_time, remark
        FROM t_channel_binding
        WHERE target_id = #{targetId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据目标数据ID和渠道ID删除特定绑定关系 -->
    <delete id="deleteByTargetIdAndChannelId">
        DELETE FROM t_channel_binding
        WHERE target_id = #{targetId} AND channel_id = #{channelId}
    </delete>

</mapper>
