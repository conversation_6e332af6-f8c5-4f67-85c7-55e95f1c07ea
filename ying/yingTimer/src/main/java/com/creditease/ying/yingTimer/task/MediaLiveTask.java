package com.creditease.ying.yingTimer.task;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.creditease.ying.fincir.bean.MediaLiveBean;
import com.creditease.ying.fincir.dao.MediaLiveDao;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.hutool.core.util.StrUtil.endWith;

/**
 * 直播定时任务
 *
 * <AUTHOR>
 * @date 2023/4/6 13:48
 */
@Slf4j
@Component
public class MediaLiveTask {

    @Autowired
    MediaLiveDao mediaLiveDao;
    @Autowired
    OkHttpClient okHttpProxyClient;

    /**
     * 定时任务:修改直播推流开始时间
     */
    @XxlJob("updatePushFlowTimeTask")
    public void updatePushFlowTimeTask() {
        log("updatePushFlowTimeTask-开始");
        try {
            // 查询没有推流开始时间,并且前后一小时的直播数据.
            List<MediaLiveBean> mediaLiveBeans = mediaLiveDao.selectNotStartedLiveList();
            if (mediaLiveBeans == null || mediaLiveBeans.isEmpty()) {
                log("updatePushFlowTimeTask-没有需要处理的数据");
                return;
            }
            log(String.format("updatePushFlowTimeTask-从数据库中查询数据条数:%s", mediaLiveBeans.size()));
            Map<String, MediaLiveBean> mediaLiveBeanMap = mediaLiveBeans.stream().filter(e -> StrUtil.isNotBlank(e.getMediaUrl()))
                                                                        // 只处理http、https协议并且.m3u8和.flv结尾的推流地址
                                                                        .filter(e -> (endWith(e.getMediaUrl(), ".m3u8")||endWith(e.getMediaUrl(), ".flv")) && StrUtil.startWith(e.getMediaUrl(), "http"))
                                                                        .collect(Collectors.toMap(MediaLiveBean::getMediaUrl,
                                                                                                  e -> e,
                                                                                                  (e, e2) -> e.getStartTime().before(e2.getStartTime()) ? e : e2));
            log(String.format("updatePushFlowTimeTask-经过过滤后数据条数:%s", mediaLiveBeanMap.size()));
            mediaLiveBeanMap.forEach((mediaUrl, mediaLiveBean) -> {
                try {
                    final Request request = new Request.Builder()
                            .url(mediaUrl)
                            .get()
                            .build();
                    try (okhttp3.Response response = okHttpProxyClient.newCall(request).execute()) {
                        int code = response.code();
                        log(String.format("updatePushFlowTimeTask-mediaUrl[%s] ==> response status :[%s]", mediaUrl, code));
                        if (code == 200) {
                            mediaLiveDao.updatePushFlowTimeById(new MediaLiveBean().setId(mediaLiveBean.getId()).setPushFlowTime(new Date()));
                        }
                    }
                } catch (IOException e) {
                    log(String.format("updatePushFlowTimeTask-失败-直播id=%s,mediaUrl=%s,异常:%s", mediaLiveBean.getId(), mediaUrl, ExceptionUtil.stacktraceToString(e)));
                }
            });
        } catch (Exception e) {
            log("updatePushFlowTimeTask-失败-定时任务失败.异常:" + ExceptionUtil.stacktraceToString(e));
        }
        log("updatePushFlowTimeTask-结束");
    }

    private void log(String msg) {
        log.info(msg);
        XxlJobHelper.log(msg);
    }

    public static void main(String[] args) throws IOException {
        OkHttpClient okHttpClient = new OkHttpClient();
        final Request request = new Request.Builder()
                .url("https://bl.qxshares.com/yrcf/yrcfapplive.m3u8")
                .get()
                .build();
        Call call = okHttpClient.newCall(request);
        int code = call.execute().code();
        System.out.println(code);
    }

}
