package com.creditease.ying.yingTimer.task.httpclient;

import com.creditease.ying.api.yrd.aop.CommonLoggingInterceptor;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetSocketAddress;
import java.net.Proxy;

import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * 可以访问外网的httpClient
 */
@Configuration
public class OkHttpClientConfiguration {

    @Value("${proxy.outer.net.ip}")
    private String outNetProxyIp;
    @Value("${proxy.outer.net.http.port}")
    private int outNetProxyPort;

    @Bean
    public OkHttpClient okHttpProxyClient() {
        return new OkHttpClient.Builder()
                .proxy(createProxy())
                .addInterceptor(new CommonLoggingInterceptor())
                .connectTimeout(15, SECONDS)    // 连接超时 15秒
                .readTimeout(20, SECONDS)       // 读取超时 20秒
                .writeTimeout(10, SECONDS)      // 写入超时 10秒
                .retryOnConnectionFailure(true) // 连接失败时重试
                .build();
    }

    private Proxy createProxy() {
        return new Proxy(Proxy.Type.HTTP, new InetSocketAddress(outNetProxyIp, outNetProxyPort));
    }
}
