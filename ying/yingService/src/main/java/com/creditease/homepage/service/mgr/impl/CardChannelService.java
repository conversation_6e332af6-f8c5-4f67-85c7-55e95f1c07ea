package com.creditease.homepage.service.mgr.impl;

import com.creditease.homepage.HomepageConstants;
import com.creditease.homepage.bean.GetCardListBean;
import com.creditease.homepage.bean.GetCardListVO;
import com.creditease.homepage.enums.CardType;
import com.creditease.homepage.enums.SpecialRestrictEnum;
import com.creditease.homepage.model.CardChannel;
import com.creditease.homepage.model.CardConditionPo;
import com.creditease.homepage.model.CardConfigPo;
import com.creditease.homepage.mybatis.ICardChannelDao;
import com.creditease.homepage.mybatis.ICardConfigDao;
import com.creditease.homepage.service.mgr.ICardConfigService;
import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.common.util.BeanUtil;
import com.creditease.ying.common.util.JsonUtils;
import com.creditease.ying.taglib.utils.TagUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/12/14.
 */
@Slf4j
@Service
public class CardChannelService {

    @Autowired
    ICardChannelDao cardChannelDao;

    @Autowired
    private ICardConfigDao cardConfigDao;

    @Autowired
    ICardConfigService cardConfigService;

    public boolean deleteByCondition(CardChannel cardChannel) throws Exception{
        return cardChannelDao.deleteByConditions(cardChannel) > 0;
    }

    public long countByCondition(CardChannel cardChannel) throws Exception {
        return cardChannelDao.countByCondition(cardChannel);
    }

    public DataGrid getPageDataGrid(GetCardListBean getCardListBean) throws Exception  {
        DataGrid dg = new DataGrid();

        getCardListBean.setApp(HomepageConstants.AppEnum.FUTURE_FORTUNE_APP.getCode());

        // 修复：使用新的统计方法统计已绑定渠道的卡片总数
        Long total = cardConfigDao.getCardChannelPageCount(getCardListBean);
        List<CardConfigPo>  list = cardConfigService.selectCardChannelList(getCardListBean);
        // 处理成页面对象
        List<GetCardListVO> rows = this.getCardInfoList(list, getCardListBean);
        dg.setTotal(total == null ? 0L : total);
        dg.setRows(rows);
        return dg;
    }

    public List<GetCardListVO> getCardInfoList(List<CardConfigPo> cardConfigPos, GetCardListBean getCardListBean) {
        List<GetCardListVO> cardListVOList = Lists.newArrayList();
        for (CardConfigPo cardConfigPo : cardConfigPos) {

            GetCardListVO getCardListVO = new GetCardListVO();
            // 修复：直接使用SQL聚合后的cardChannelId和channelName
            getCardListVO.setCardChannelId(cardConfigPo.getCardChannelId());
            getCardListVO.setChannelName(cardConfigPo.getChannelName());

            BeanUtil.copyProperties(getCardListVO, cardConfigPo);
            int iCardType = cardConfigPo.getCardType();
            String cardType = CardType.getCardType(iCardType);
            getCardListVO.setCardType(cardType);
            getCardListVO.setiCardType(cardConfigPo.getCardType());
            getCardListVO.setSpecialRestrictEnumName(SpecialRestrictEnum.getDescriptinByName(cardConfigPo.getSpecialRestrictEnumName()));
            Integer cardId = cardConfigPo.getId();
            CardConditionPo conditionQuery = new CardConditionPo();
            conditionQuery.setCardId(cardId);
            getCardListVO.setCondition1("选中");
            getCardListVO.setSpecialUserTagNames(TagUtils.getTagNames(getCardListVO.getSpecialUserTags()));
            cardListVOList.add(getCardListVO);
        }
        return cardListVOList;
    }

    public boolean update(CardChannel cardChannel) throws Exception  {
        log.info(" update | param : {}", JsonUtils.convertObjToJsonString(cardChannel));

        if(cardChannelDao.countByCondition(cardChannel) > 0){
            return cardChannelDao.updateByPrimaryKeySelective(cardChannel) > 0;
        }
        log.error(" update | 关联渠道记录不存在！");
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean insertCardChannel(List<Integer> cardIdList, List<Integer> channelIds, String creator) throws Exception  {
        GetCardListBean queryBean = new GetCardListBean();
        queryBean.setCardIds(cardIdList);
        List<CardConfigPo>  cardList = cardConfigDao.selectList(queryBean);
        log.info("config | cardInfoList : {} ", JsonUtils.convertObjToJsonString(cardIdList));

        // 1. 先删除这些卡片的旧渠道关系
        for(Integer cardId : cardIdList) {
            CardChannel deleteCondition = new CardChannel();
            deleteCondition.setCardId(cardId);
            int deletedCount = cardChannelDao.deleteByConditions(deleteCondition);
            log.info("删除卡片ID: {} 的旧渠道关系，删除数量: {}", cardId, deletedCount);
        }

        // 2. 组装新的card_channel数据入库
        List<CardChannel> list = new ArrayList<>();
        for(Integer channelId : channelIds) {
            for(CardConfigPo po : cardList) {
                if(HomepageConstants.AppEnum.YIREN_FORTUNE_APP.getCode() == po.getApp()){
                    continue;
                }
                CardChannel cardChannel = new CardChannel();
                cardChannel.setCreater(creator);
                cardChannel.setCardId(po.getId());
                cardChannel.setCardDetail(po.getCardDetail());
                cardChannel.setChannelMarketId(channelId);
                list.add(cardChannel);
            }
        }
        
        // 3. 批量插入新的渠道关系
        if(list.isEmpty()) {
            log.warn("没有需要插入的渠道关系数据");
            return true;
        }
        
        boolean insertResult = cardChannelDao.insertBatch(list) > 0;
        log.info("插入渠道关系结果: {}, 插入数量: {}", insertResult, list.size());
        return insertResult;
    }

}