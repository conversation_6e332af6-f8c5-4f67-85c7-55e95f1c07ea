package com.creditease.homepage.mybatis;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.creditease.homepage.bean.GetCardListBean;
import com.creditease.homepage.model.CardConfigPo;


public interface ICardConfigDao {

    CardConfigPo selectCardChannelConfigById(@Param(value = "cardId")int cardId, @Param(value = "cardChannelId")int cardChannelId);

    List<CardConfigPo> selectCardChannelList(GetCardListBean cardListBean);

    List<CardConfigPo> selectList(GetCardListBean cardListBean);

    List<CardConfigPo> selectListDing(GetCardListBean cardListBean);
    long getPageCount(GetCardListBean getCardListBean);

    CardConfigPo selectById(int id);

    CardConfigPo selectByType(int type);

    int deleteById(int id);

    int insertSelective(CardConfigPo po);

    int updateByIdSelective(CardConfigPo po);

    List<CardConfigPo> selectByTypeAndCondition(int cardType, int conditionType);

    long getCardChannelPageCount(GetCardListBean getCardListBean);

}
