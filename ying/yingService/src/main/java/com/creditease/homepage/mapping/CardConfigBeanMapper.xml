<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.creditease.homepage.mybatis.ICardConfigDao">
    <resultMap id="BaseResultMap" type="com.creditease.homepage.model.CardConfigPo">
        <result column="ID" property="id" jdbcType="INTEGER"/>
        <result column="card_channel_id" property="cardChannelId" jdbcType="INTEGER"/>
        <result column="channel_name" property="channelName" jdbcType="VARCHAR"/>
        <result column="APP" property="app" jdbcType="INTEGER"/>
        <result column="MODULE" property="module" jdbcType="INTEGER"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="TITLE" property="title" jdbcType="VARCHAR"/>
        <result column="CARD_TEMPLATE_TYPE" property="cardTemplateType" jdbcType="INTEGER"/>
        <result column="CARD_TYPE" property="cardType" jdbcType="INTEGER"/>
        <result column="PRIORITY" property="priority" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <result column="SPECIAL_USER_TAGS" property="specialUserTags" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="ONLINE_TIME" property="onlineTime" jdbcType="TIMESTAMP"/>
        <result column="OFFLINE_TIME" property="offlineTime" jdbcType="TIMESTAMP"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="VERSION_LIMIT" property="versionLimit" jdbcType="VARCHAR"/>
        <result column="VERSION_MAX_LIMIT" property="versionMaxLimit" jdbcType="VARCHAR" />
        <result column="CARD_DETAIL" property="cardDetail" jdbcType="VARCHAR"/>
        <result column="ACTION" property="action" jdbcType="VARCHAR"/>
        <result column="CALLBACK_TYPE" property="callbackType" jdbcType="INTEGER"/>
        <result column="METHOD_TYPE" property="methodType" jdbcType="INTEGER"/>
        <result column="URL" property="url" jdbcType="VARCHAR"/>
        <result column="SPECIAL_RESTRICT_ENUM_NAME" property="specialRestrictEnumName" jdbcType="VARCHAR"/>
        <result column="SPECIAL_USER_TAGS" property="specialUserTags" jdbcType="VARCHAR"/>
        <result column="SPECIAL_USER_FILE_NAME" property="specialUserFileName" jdbcType="VARCHAR"/>
        <result column="ABTest" property="ABTest" jdbcType="VARCHAR"/>
        <result column="join_audit" property="joinAudit" jdbcType="INTEGER"/>
        <result column="child_join_audit" property="childJoinAudit" jdbcType="INTEGER"/>
        <result column="health_sextag" property="healthSextag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="BaseColumnList">
        ID,APP, MODULE,NAME, TITLE, CALLBACK_TYPE, CARD_TYPE, METHOD_TYPE, URL, CREATE_TIME, UPDATE_TIME,URL,
        REMARK, PRIORITY, STATUS, SPECIAL_RESTRICT_ENUM_NAME, SPECIAL_USER_TAGS, CARD_TEMPLATE_TYPE,
        ONLINE_TIME, OFFLINE_TIME, VERSION_LIMIT, VERSION_MAX_LIMIT, CARD_DETAIL,ACTION, SPECIAL_USER_TAGS,
        SPECIAL_USER_FILE_NAME,ABTest,join_audit,child_join_audit,health_sextag
    </sql>

    <sql id="JoinColumnList">
        b.ID,APP, MODULE,NAME, TITLE, CALLBACK_TYPE, CARD_TYPE, METHOD_TYPE, URL, CREATE_TIME, UPDATE_TIME,URL,
        REMARK, PRIORITY, STATUS, SPECIAL_RESTRICT_ENUM_NAME, SPECIAL_USER_TAGS, CARD_TEMPLATE_TYPE,
        ONLINE_TIME, OFFLINE_TIME, VERSION_LIMIT, VERSION_MAX_LIMIT, CARD_DETAIL,ACTION, SPECIAL_USER_TAGS,
        SPECIAL_USER_FILE_NAME
    </sql>

    <select id="selectCardChannelConfigById" resultMap="BaseResultMap">
       select t.ID, t.APP, t.MODULE, t.NAME, t.TITLE, t.CALLBACK_TYPE, t.CARD_TYPE, t.METHOD_TYPE, t.URL, t.CREATE_TIME,
            t.UPDATE_TIME, t.URL, REMARK, t.PRIORITY, t.STATUS, t.SPECIAL_RESTRICT_ENUM_NAME, t.SPECIAL_USER_TAGS, t.CARD_TEMPLATE_TYPE,
            t.ONLINE_TIME, t.OFFLINE_TIME, t.VERSION_LIMIT, t.VERSION_MAX_LIMIT, t.ACTION, t.SPECIAL_USER_TAGS,
            t.SPECIAL_USER_FILE_NAME, t1.CARD_DETAIL, t1.id as card_channel_id
            from t_card t
            right join t_card_channel t1
            on t.id = t1.card_id
            where  t.id=#{cardId,jdbcType=INTEGER}
            and t1.id=#{cardChannelId,jdbcType=INTEGER}
    </select>

    <select id="selectCardChannelList" resultMap="BaseResultMap" parameterType="com.creditease.homepage.bean.GetCardListBean">
        select t.ID, t.APP, t.MODULE, t.NAME, t.TITLE, t.CALLBACK_TYPE, t.CARD_TYPE, t.METHOD_TYPE, t.URL, t.CREATE_TIME,
        t.UPDATE_TIME, t.URL, REMARK, t.PRIORITY, t.STATUS, t.SPECIAL_RESTRICT_ENUM_NAME, t.SPECIAL_USER_TAGS, t.CARD_TEMPLATE_TYPE,
        t.ONLINE_TIME, t.OFFLINE_TIME, t.VERSION_LIMIT, t.VERSION_MAX_LIMIT, t.ACTION, t.SPECIAL_USER_TAGS,
        t.SPECIAL_USER_FILE_NAME, t1.CARD_DETAIL, 
        MIN(t1.id) as card_channel_id, 
        GROUP_CONCAT(t2.channel_name) as channel_name,
        t.join_audit,t.child_join_audit,t.health_sextag
        from t_card t
        inner join t_card_channel t1
        on t.id = t1.card_id
        left join t_channel_market t2
        on t1.channel_market_id = t2.id
        <where>
            <if test="app != null">AND t.APP = #{app,jdbcType=INTEGER}</if>
            <if test="module != null">AND t.MODULE = #{module,jdbcType=INTEGER}</if>
            <if test="cardType != null">AND t.CARD_TYPE = #{cardType,jdbcType=INTEGER}</if>
            <if test="cardTemplateType != null">AND t.CARD_TEMPLATE_TYPE = #{cardTemplateType,jdbcType=INTEGER}</if>
            <if test="status != null and status.size() > 0">
                AND t.STATUS IN
                <foreach collection="status" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cardName != null and  cardName != ''">AND t.NAME like CONCAT('%',#{cardName,jdbcType=VARCHAR},'%')</if>
            <if test="online != null and online == true">
                AND NOW() BETWEEN t.ONLINE_TIME AND t.OFFLINE_TIME
            </if>
            <if test="cardId != null">
                AND t.ID = #{cardId}
            </if>
            <if test="joinAudit != null and joinAudit> -1">
                <choose>
                    <when test="joinAudit == 0">
                        AND t.child_join_audit = #{joinAudit}
                    </when>
                    <otherwise>
                        AND t.child_join_audit > 0
                    </otherwise>
                </choose>
            </if>
            <if test="healthSextag != null and healthSextag> -1">
                AND t.health_sextag = #{healthSextag}
            </if>
        </where>
        GROUP BY t.ID, t.PRIORITY
        ORDER BY t.PRIORITY
        <if test="pageStart >= 0 and rows > 0">
            LIMIT #{pageStart},#{rows}
        </if>
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="com.creditease.homepage.bean.GetCardListBean">
        select
        <include refid="BaseColumnList"/>
        from t_card
        <where>
        	<if test="app != null">AND APP = #{app,jdbcType=INTEGER}</if>
        	<if test="module != null">AND MODULE = #{module,jdbcType=INTEGER}</if>
            <if test="cardType != null">AND CARD_TYPE = #{cardType,jdbcType=INTEGER}</if>
            <if test="cardTemplateType != null">AND CARD_TEMPLATE_TYPE = #{cardTemplateType,jdbcType=INTEGER}</if>
            <if test="status != null and status.size() > 0">
                AND STATUS IN
                <foreach collection="status" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cardName != null and  cardName != ''">AND NAME like CONCAT('%',#{cardName,jdbcType=VARCHAR},'%')</if>
            <if test="cardIds != null and cardIds.size() > 0">
                AND ID IN
                <foreach collection="cardIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="online != null and online == true">
                AND NOW() BETWEEN ONLINE_TIME AND OFFLINE_TIME
            </if>
            <if test="cardId != null">
                AND ID = #{cardId}
            </if>
        </where>
        ORDER BY PRIORITY
        <if test="pageStart !=null and rows > 0">
            LIMIT #{pageStart},#{rows}
        </if>
    </select>





    <select id="selectListDing" resultMap="BaseResultMap" parameterType="com.creditease.homepage.bean.GetCardListBean">
        select
        <include refid="BaseColumnList"/>
        from t_card
        <where>
            <if test="app != null">AND APP = #{app,jdbcType=INTEGER}</if>
            <if test="module != null">AND MODULE = #{module,jdbcType=INTEGER}</if>
            <if test="cardType != null">AND CARD_TYPE = #{cardType,jdbcType=INTEGER}</if>
            <if test="cardTemplateType != null">AND CARD_TEMPLATE_TYPE = #{cardTemplateType,jdbcType=INTEGER}</if>
            <if test="status != null and status.size() > 0">
                AND STATUS IN
                <foreach collection="status" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cardName != null and  cardName != ''">AND NAME like CONCAT('%',#{cardName,jdbcType=VARCHAR},'%')</if>
            <if test="cardIds != null and cardIds.size() > 0">
                AND ID IN
                <foreach collection="cardIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="online != null and online == true">
                AND NOW() BETWEEN ONLINE_TIME AND OFFLINE_TIME
            </if>
            <if test="cardId != null">
                AND ID = #{cardId}
            </if>


            <if test="userStatus != null and userStatus> 0">
                and id in (SELECT CARD_ID    from t_card_condition where CONDITION_ID =#{userStatus,jdbcType=INTEGER})
            </if>

            <if test="onlineStatus != null and onlineStatus> 0">
                <if test=" onlineStatus == 1">
                    AND NOW() BETWEEN ONLINE_TIME AND OFFLINE_TIME
                </if>
                <if test=" onlineStatus == 2">
                    AND NOW()   &gt; OFFLINE_TIME
                </if>
                <if test=" onlineStatus == 3">
                    AND NOW() 	&lt; ONLINE_TIME
                </if>
            </if>
            <if test="joinAudit != null and joinAudit> -1">
                <choose>
                    <when test="joinAudit == 0">
                        AND child_join_audit = #{joinAudit}
                    </when>
                    <otherwise>
                        AND child_join_audit > 0
                    </otherwise>
                </choose>
            </if>
            <if test="isSpecialAccount">
                AND id NOT IN (SELECT key_id FROM hidden_record_config WHERE key_table='t_card')
            </if>
            <if test="healthSextag != null and healthSextag> -1">
                AND health_sextag = #{healthSextag}
            </if>
        </where>
        ORDER BY PRIORITY
        <if test="pageStart !=null and rows > 0">
            LIMIT #{pageStart},#{rows}
        </if>
    </select>
    <select id="getPageCount" resultType="java.lang.Long" parameterType="com.creditease.homepage.bean.GetCardListBean">
        select count(1)
        from t_card
        <where>
            <if test="app != null">AND APP = #{app,jdbcType=INTEGER}</if>
            <if test="module != null">AND MODULE = #{module,jdbcType=INTEGER}</if>
            <if test="cardType != null">AND CARD_TYPE = #{cardType,jdbcType=INTEGER}</if>
            <if test="cardTemplateType != null">AND CARD_TEMPLATE_TYPE = #{cardTemplateType,jdbcType=INTEGER}</if>
            <if test="status != null and status.size() > 0">
                AND STATUS IN
                <foreach collection="status" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cardName != null and  cardName != ''">AND NAME like CONCAT('%',#{cardName,jdbcType=VARCHAR},'%')</if>
            <if test="cardIds != null and cardIds.size() > 0">
                AND ID IN
                <foreach collection="cardIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="online != null and online == true">
                AND NOW() BETWEEN ONLINE_TIME AND OFFLINE_TIME
            </if>
            <if test="cardId != null">
                AND ID = #{cardId}
            </if>

            <if test="userStatus != null and userStatus> 0">
                and id in (SELECT CARD_ID    from t_card_condition where CONDITION_ID =#{userStatus,jdbcType=INTEGER})
            </if>

            <if test="onlineStatus != null and onlineStatus> 0">
                <if test=" onlineStatus == 1">
                    AND NOW() BETWEEN ONLINE_TIME AND OFFLINE_TIME
                </if>
                <if test=" onlineStatus == 2">
                    AND NOW()   &gt; OFFLINE_TIME
                </if>
                <if test=" onlineStatus == 3">
                    AND NOW() 	&lt; ONLINE_TIME
                </if>
            </if>

            <if test="joinAudit != null and joinAudit> -1">
                <choose>
                    <when test="joinAudit == 0">
                        AND child_join_audit = #{joinAudit}
                    </when>
                    <otherwise>
                        AND child_join_audit > 0
                    </otherwise>
                </choose>
            </if>

            <if test="healthSextag != null and healthSextag> -1">
                AND health_sextag = #{healthSextag}
            </if>
            <if test="isSpecialAccount">
                AND id NOT IN (SELECT key_id FROM hidden_record_config WHERE key_table='t_card')
            </if>

        </where>
    </select>

    <select id="getCardChannelPageCount" resultType="java.lang.Long" parameterType="com.creditease.homepage.bean.GetCardListBean">
        select count(distinct t.ID) from t_card t
        inner join t_card_channel t1
        on t.id = t1.card_id
        <where>
            <if test="app != null">AND t.APP = #{app,jdbcType=INTEGER}</if>
            <if test="module != null">AND t.MODULE = #{module,jdbcType=INTEGER}</if>
            <if test="cardType != null">AND t.CARD_TYPE = #{cardType,jdbcType=INTEGER}</if>
            <if test="cardTemplateType != null">AND t.CARD_TEMPLATE_TYPE = #{cardTemplateType,jdbcType=INTEGER}</if>
            <if test="status != null and status.size() > 0">
                AND t.STATUS IN
                <foreach collection="status" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cardName != null and  cardName != ''">AND t.NAME like CONCAT('%',#{cardName,jdbcType=VARCHAR},'%')</if>
            <if test="online != null and online == true">
                AND NOW() BETWEEN t.ONLINE_TIME AND t.OFFLINE_TIME
            </if>
            <if test="cardId != null">
                AND t.ID = #{cardId}
            </if>
            <if test="joinAudit != null and joinAudit> -1">
                <choose>
                    <when test="joinAudit == 0">
                        AND t.child_join_audit = #{joinAudit}
                    </when>
                    <otherwise>
                        AND t.child_join_audit > 0
                    </otherwise>
                </choose>
            </if>
            <if test="healthSextag != null and healthSextag> -1">
                AND t.health_sextag = #{healthSextag}
            </if>
        </where>
    </select>


    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from t_card
        where ID = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from t_card
        where CARD_TYPE = #{cardType,jdbcType=INTEGER}
            <![CDATA[ and ONLINE_TIME <= now() and OFFLINE_TIME >= now() ]]>
        order by priority
        limit 1
    </select>

    <select id="selectByTypeAndCondition" resultMap="BaseResultMap">
        select
        <include refid="JoinColumnList"/>
        from t_card_condition a join t_card b on a.card_id=b.id
        where
        b.CARD_TYPE = #{0,jdbcType=INTEGER}
        AND a.CONDITION_ID = #{1,jdbcType=INTEGER}
        <![CDATA[ and ONLINE_TIME <= now() and OFFLINE_TIME >= now() ]]>
        order by priority
    </select>

    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from t_card
        where ID = #{id,jdbcType=INTEGER}
    </delete>


    <insert id="insertSelective" parameterType="com.creditease.homepage.model.CardConfigPo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into t_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
        	<if test="app != null">
                APP,
            </if>
            <if test="module != null">
            	MODULE,
            </if>
            <if test="name != null">
                NAME,
            </if>
            <if test="title != null">
                TITLE,
            </if>
            <if test="callbackType != null and callbackType != 0">
                CALLBACK_TYPE,
            </if>
            <if test="cardType != null and cardType != 0">
                CARD_TYPE,
            </if>
            <if test="methodType != null and methodType != 0">
                METHOD_TYPE,
            </if>
            <if test="url != null">
                URL,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="priority != null">
                PRIORITY,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="specialUserTags != null">
                SPECIAL_USER_TAGS,
            </if>
            <if test="cardTemplateType != null and cardTemplateType != 0">
                CARD_TEMPLATE_TYPE,
            </if>
            <if test="onlineTime != null">
                ONLINE_TIME,
            </if>
            <if test="offlineTime != null">
                OFFLINE_TIME,
            </if>
            <if test="cardDetail != null">
                CARD_DETAIL,
            </if>
            <if test="specialRestrictEnumName != null">
                SPECIAL_RESTRICT_ENUM_NAME,
            </if>
            <if test="versionLimit != null">
                VERSION_LIMIT,
            </if>
            <if test="versionMaxLimit != null">
                VERSION_MAX_LIMIT,
            </if>
            <if test="action != null">
                ACTION,
            </if>
            <if test="ABTest != null">
                ABTest,
            </if>
            <if test="joinAudit != null">
                join_audit,
            </if>
            <if test="childJoinAudit != null">
                child_join_audit,
            </if>
            <if test="healthSextag != null">
                health_sextag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="app != null">
                #{app,jdbcType=INTEGER},
            </if>
            <if test="module != null">
                #{module,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="callbackType != null and callbackType != 0">
            #{callbackType,jdbcType=INTEGER},
            </if>
            <if test="cardType != null and cardType != 0">
            #{cardType,jdbcType=INTEGER},
            </if>
            <if test="methodType != null and methodType != 0">
                #{methodType,jdbcType=INTEGER},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="specialUserTags != null">
                #{specialUserTags,jdbcType=VARCHAR},
            </if>
            <if test="cardTemplateType != null">
                #{cardTemplateType,jdbcType=INTEGER},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="offlineTime != null">
                #{offlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cardDetail != null">
                #{cardDetail,jdbcType=VARCHAR},
            </if>
            <if test="specialRestrictEnumName != null">
                #{specialRestrictEnumName,jdbcType=VARCHAR},
            </if>
            <if test="versionLimit != null">
                #{versionLimit,jdbcType=INTEGER},
            </if>
            <if test="versionMaxLimit != null">
                #{versionMaxLimit,jdbcType=INTEGER},
            </if>
            <if test="action != null">
                #{action,jdbcType=VARCHAR},
            </if>
            <if test="ABTest != null">
                #{ABTest,jdbcType=VARCHAR},
            </if>
            <if test="joinAudit != null">
                #{joinAudit,jdbcType=INTEGER},
            </if>

            <if test="childJoinAudit != null">
                #{childJoinAudit,jdbcType=INTEGER},
            </if>
            <if test="healthSextag != null">
                #{healthSextag,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByIdSelective" parameterType="com.creditease.homepage.model.CardConfigPo">
        update t_card
        <set>
        	 <if test="app != null">
                NAME = #{app,jdbcType=INTEGER},
            </if>
            <if test="module!=null">
                MODULE = #{module,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                TITLE = #{title,jdbcType=VARCHAR},
            </if>
            <if test="callbackType != null and callbackType != 0">
                CALLBACK_TYPE = #{callbackType,jdbcType=INTEGER},
            </if>
            <if test="cardType != null and cardType != 0">
                CARD_TYPE = #{cardType,jdbcType=INTEGER},
            </if>
            <if test="methodType != null and methodType != 0">
                METHOD_TYPE = #{methodType,jdbcType=INTEGER},
            </if>
            <if test="url != null">
                URL = #{url,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="priority != null">
                PRIORITY = #{priority,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=INTEGER},
            </if>
            <if test="cardTemplateType != null and cardTemplateType != 0">
                CARD_TEMPLATE_TYPE = #{cardTemplateType,jdbcType=INTEGER},
            </if>
            <if test="onlineTime != null">
                ONLINE_TIME = #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="offlineTime != null">
                OFFLINE_TIME = #{offlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cardDetail != null">
                CARD_DETAIL = #{cardDetail,jdbcType=VARCHAR},
            </if>
            <if test="versionLimit != null">
                VERSION_LIMIT = #{versionLimit,jdbcType=INTEGER},
            </if>
            <if test="versionMaxLimit != null">
                VERSION_MAX_LIMIT = #{versionMaxLimit,jdbcType=INTEGER},
            </if>
            <if test="action != null">
                ACTION = #{action,jdbcType=VARCHAR},
            </if>
            <if test="specialRestrictEnumName != null">
                SPECIAL_RESTRICT_ENUM_NAME = #{specialRestrictEnumName},
            </if>
            <if test="specialUserTags != null">
                special_user_tags = #{specialUserTags},
            </if>
            <if test="specialUserFileName != null">
                SPECIAL_USER_FILE_NAME = #{specialUserFileName,jdbcType=VARCHAR},
            </if>
            <if test="ABTest != null">
                ABTest = #{ABTest,jdbcType=VARCHAR},
            </if>
            <if test="joinAudit != null">
                join_audit = #{joinAudit,jdbcType=INTEGER},
            </if>
            <if test="childJoinAudit != null">
                child_join_audit = #{childJoinAudit,jdbcType=INTEGER},
            </if>
            <if test="healthSextag != null">
                health_sextag = #{healthSextag,jdbcType=INTEGER}
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
</mapper>