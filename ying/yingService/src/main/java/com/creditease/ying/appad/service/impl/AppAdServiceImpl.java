package com.creditease.ying.appad.service.impl;

import com.creditease.homepage.bean.TagsDTO;
import com.creditease.ying.api.taglib.TaglibApi;
import com.creditease.ying.api.taglib.bean.UserTag;
import com.creditease.ying.api.yrd.EliteLoanApi;
import com.creditease.ying.api.yrd.YrdFinanceApi;
import com.creditease.ying.appad.bean.AppAdParam;
import com.creditease.ying.appad.bean.dto.MessagePayloadDTO;
import com.creditease.ying.appad.bean.dto.PushMessageDTO;
import com.creditease.ying.appad.bean.po.AppAdComposePO;
import com.creditease.ying.appad.bean.po.AppAdPO;
import com.creditease.ying.appad.bean.po.AppAdPositionPO;
import com.creditease.ying.appad.bean.po.AppAdUserPO;
import com.creditease.ying.appad.bean.vo.AccountAdVO;
import com.creditease.ying.appad.enums.AppAdPositionEnum;
import com.creditease.ying.appad.enums.AppAdStatus;
import com.creditease.ying.appad.service.IAppAdService;
import com.creditease.ying.appad.util.AppAdUtil;
import com.creditease.ying.appinit.bean.InitDataBean;
import com.creditease.ying.common.dao.mybatis.IBaseDao;
import com.creditease.ying.common.model.page.DataGrid;
import com.creditease.ying.common.util.ConstantsForRedis;
import com.creditease.ying.common.util.DateUtil;
import com.creditease.ying.common.util.JsonUtil;
import com.creditease.ying.insurance.service.IInsuranceService;
import com.creditease.ying.p2puser.bean.P2puserBean;
import com.creditease.ying.taglib.utils.TagUtils;
import com.creditease.ying.watch.type.AccountInsuranceStatusEnum;
import com.creditease.ying.withchannel.bean.GetPositionByIds;
import com.creditease.ying.withchannel.enums.WithChannelFeatureType;
import com.creditease.ying.withchannel.service.IFeatureWithChannelService;
import com.google.common.base.*;
import com.google.common.collect.Collections2;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
public class AppAdServiceImpl implements IAppAdService, InitializingBean {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private IBaseDao baseDao;

    @Autowired(required = false)
    private IInsuranceService insuranceService;

    @Autowired
    private TaglibApi taglibApi;

    @Autowired(required = false)
    private YrdFinanceApi yrdFinanceApi;

    @Autowired(required = false)
    private EliteLoanApi eliteLoanApi;

    @Autowired
    private IFeatureWithChannelService appAdChannelService;

    @Autowired
    private JedisCluster jedisCluster;

    private static final String LIVE_ROOM_USER_KEY = "live_room_user:%s";

    private static final String PUB_REDIS_CHANNEL = "live_media_push_channel";

    private byte initGroupAppAdPosition = 4;

    private String clientTypeCommon = "0";

    private volatile Map<String, String> appPositionConfigMap;

    private Splitter.MapSplitter mapSplitter = Splitter.on("|").trimResults().withKeyValueSeparator(":");

    private List<AppAdPositionPO> appAdPositionPOSDefault = Lists.newLinkedList();

    private int initDataBeanSelectRows = 100;
    private int initDataBeanSelectPage = 1;

    @Override
    public int saveAppAd(AppAdPO appAd) {
        appAd.setAdDesc(StringEscapeUtils.unescapeHtml(appAd.getAdDesc()));
        appAd.setTitle(StringEscapeUtils.unescapeHtml(appAd.getTitle()));
        checkedAppAdPO(appAd);
        Date curDate = new Date();
        appAd.setUpdateTime(curDate);

        if (appAd.getId() == null || appAd.getId() <= 0) {
            appAd.setCreateTime(curDate);
            boolean bo = baseDao.insert("appAdMapper.insertSelective", appAd);
            logger.info("appAd save result : {} {}", bo, JsonUtil.beanToJson(appAd));
            //保存组合标签
            int count = saveAppAdComposeList(appAd);
            logger.info("appAd AppAdComposeList result: {} ", count);
            delAppAdRedisKey(appAd);
            pushWebSocketMessage(appAd);
            return 1;
        }

        long rowsCount = baseDao.get("appAdMapper.countAppAdsSelected", new AppAdParam(appAd.getId()));
        if (rowsCount <= 0) {
            logger.error("app推荐位信息不存在，appAd={}。", appAd);
            return 0;
        }

        boolean updateFlag = baseDao.update("appAdMapper.updateByPrimaryKeySelective", appAd);
        if (updateFlag) {
            delAppAdRedisKey(appAd);
            pushWebSocketMessage(appAd);
            //保存组合标签
            int count = updateAppAdComposeList(appAd);
        }

        return 1;
    }

    private void pushWebSocketMessage(AppAdPO appAd) {
        //发送浮球信息到App客户端

        if (AppAdPositionEnum.APP_AD_POSITION_73.getPosition() != appAd.getShowPosition()) {
            return;
        }

        //如果不是上线的不处理
        if (Objects.isNull(appAd.getOnlineTime()) || Objects.isNull(appAd.getOfflineTime())) {
            return;
        }

        if (StringUtils.isBlank(appAd.getAdKey())) {
            PushMessageDTO pushMessage = new PushMessageDTO();
            MessagePayloadDTO messagePayloadDTO = new MessagePayloadDTO();
            messagePayloadDTO.setType("floating_ball");
            messagePayloadDTO.setTimestamp(Instant.now().getEpochSecond());
            pushMessage.setPayload(JsonUtil.beanToJson(messagePayloadDTO));
            jedisCluster.publish(PUB_REDIS_CHANNEL, JsonUtil.beanToJson(pushMessage));
            return;
        }
        String adKey = appAd.getAdKey();
        for (String liveId : adKey.split(",")) {
            String liveRoomUserKey = String.format(LIVE_ROOM_USER_KEY, liveId);
            if (!jedisCluster.exists(liveRoomUserKey)) {
                continue;
            }
            Set<String> deviceInfos = jedisCluster.smembers(liveRoomUserKey);
            if (CollectionUtils.isEmpty(deviceInfos)) {
                continue;
            }
            PushMessageDTO pushMessage = new PushMessageDTO();
            MessagePayloadDTO messagePayloadDTO = new MessagePayloadDTO();
            messagePayloadDTO.setType("floating_ball");
            messagePayloadDTO.setTimestamp(Instant.now().getEpochSecond());
            pushMessage.setPayload(JsonUtil.beanToJson(messagePayloadDTO));
            pushMessage.setDeviceInfos(deviceInfos);
            jedisCluster.publish(PUB_REDIS_CHANNEL, JsonUtil.beanToJson(pushMessage));
        }

    }

    /**
     * 保存首页-组合弹窗内容
     *
     * @param appAd
     * @return
     */
    private int saveAppAdComposeList(AppAdPO appAd) {
        logger.info("saveAppAdComposeList save  {}  ", appAd);

        if (appAd == null || appAd.getId() == null || appAd.getShowPosition() == null || appAd.getComposeType() == null) {
            //非首页组合弹窗
            return 0;
        }
        if (appAd.getShowPosition() != 26 || appAd.getComposeType() != 2) {
            //非首页组合弹窗
            return 0;
        }
        AppAdComposePO activity1 = new AppAdComposePO();
        activity1.setDataType(Byte.valueOf("1"));
        activity1.setAdId(appAd.getId());
        activity1.setShowType(AppAdUtil.getShowType(appAd.getActivity1_show_type()));
        if (appAd.getActivity1_goUrl() != null) {
            activity1.setGoUrl(StringUtils.trim(appAd.getActivity1_goUrl().replace("&amp;", "&")));
        }

        activity1.setShowPic(StringUtils.trim(appAd.getActivity1_showPic()));
        activity1.setOrders(AppAdUtil.getOrders(appAd.getActivity1_orders()));
        activity1.setBackgroundColor(appAd.getBackgroundColor());
        baseDao.insert("appAdComposePOMapper.insertSelective", activity1);
        logger.info("saveAppAdComposeList activity1 save  {}  ", activity1);


        AppAdComposePO fix2 = new AppAdComposePO();
        fix2.setDataType(Byte.valueOf("2"));
        fix2.setAdId(appAd.getId());
        fix2.setShowType(AppAdUtil.getShowType(appAd.getFix2_show_type()));
        fix2.setGoUrl(StringUtils.trim(appAd.getFix2_goUrl()));
        if (appAd.getFix2_goUrl() != null) {
            fix2.setGoUrl(StringUtils.trim(appAd.getFix2_goUrl().replace("&amp;", "&")));
        }

        fix2.setOrders(AppAdUtil.getOrders(appAd.getFix2_orders()));
        baseDao.insert("appAdComposePOMapper.insertSelective", fix2);
        logger.info("saveAppAdComposeList fix2 save  {}  ", fix2);


        AppAdComposePO redPacket3 = new AppAdComposePO();
        redPacket3.setDataType(Byte.valueOf("3"));
        redPacket3.setAdId(appAd.getId());
        redPacket3.setShowType(AppAdUtil.getShowType(appAd.getRedPacket3_show_type()));
        if (appAd.getInsureFundType() != null) {
            redPacket3.setInsureFundType(Byte.parseByte(appAd.getInsureFundType() + ""));
        }
        redPacket3.setGoUrl(appAd.getRedPacket3_goUrl());
        if (appAd.getRedPacket3_goUrl() != null) {
            redPacket3.setGoUrl(StringUtils.trim(appAd.getRedPacket3_goUrl().replace("&amp;", "&")));
        }
        redPacket3.setOrders(AppAdUtil.getOrders(appAd.getRedPacket3_orders()));
        baseDao.insert("appAdComposePOMapper.insertSelective", redPacket3);
        logger.info("saveAppAdComposeList redPacket3 save  {}  ", redPacket3);


        AppAdComposePO yrb4 = new AppAdComposePO();
        yrb4.setDataType(Byte.valueOf("4"));
        yrb4.setAdId(appAd.getId());
        yrb4.setShowPic(StringUtils.trim(appAd.getYrb4_showPic()));
        yrb4.setShowType(AppAdUtil.getShowType(appAd.getYrb4_show_type()));
        yrb4.setGoUrl(StringUtils.trim(appAd.getYrb4_goUrl()));
        if (appAd.getYrb4_goUrl() != null) {
            yrb4.setGoUrl(StringUtils.trim(appAd.getYrb4_goUrl().replace("&amp;", "&")));
        }
        yrb4.setOrders(AppAdUtil.getOrders(appAd.getYrb4_orders()));
        baseDao.insert("appAdComposePOMapper.insertSelective", yrb4);
        logger.info("saveAppAdComposeList yrb4 save  {}  ", yrb4);


        AppAdComposePO points5 = new AppAdComposePO();
        points5.setDataType(Byte.valueOf("5"));
        points5.setAdId(appAd.getId());
        points5.setShowType(AppAdUtil.getShowType(appAd.getPoints5_show_type()));
        points5.setShowPic(StringUtils.trim(appAd.getPoints5_showPic()));
        points5.setGoUrl(StringUtils.trim(appAd.getPoints5_goUrl()));
        if (appAd.getPoints5_goUrl() != null) {
            points5.setGoUrl(appAd.getPoints5_goUrl().replace("&amp;", "&"));
        }
        points5.setOrders(AppAdUtil.getOrders(appAd.getPoints5_orders()));
        baseDao.insert("appAdComposePOMapper.insertSelective", points5);
        logger.info("saveAppAdComposeList points5 save  {}  ", points5);


        AppAdComposePO limitTime6 = new AppAdComposePO();
        limitTime6.setDataType(Byte.valueOf("6"));
/**
 *     private String backgroundPic;
 *     private String limitTime6_middleDesc;
 *     private String limitTime6_bottomDesc;
 */
        limitTime6.setBackgroundPic(StringUtils.trim(appAd.getBackgroundPic()));
        limitTime6.setMiddleDesc(appAd.getLimitTime6_middleDesc());
        limitTime6.setBottomDesc(appAd.getLimitTime6_bottomDesc());
        limitTime6.setAdId(appAd.getId());
        limitTime6.setShowType(AppAdUtil.getShowType(appAd.getLimitTime6_show_type()));
        limitTime6.setShowPic(StringUtils.trim(appAd.getLimitTime6_showPic()));
        limitTime6.setGoUrl(StringUtils.trim(appAd.getLimitTime6_goUrl()));
        limitTime6.setButtonDesc(appAd.getLimitTime6_buttonDesc());
        if (appAd.getLimitTime6_goUrl() != null) {
            limitTime6.setGoUrl(StringUtils.trim(appAd.getLimitTime6_goUrl().replace("&amp;", "&")));
        }
        limitTime6.setOrders(AppAdUtil.getOrders(appAd.getLimitTime6_orders()));
        baseDao.insert("appAdComposePOMapper.insertSelective", limitTime6);
        logger.info("saveAppAdComposeList limitTime6 save  {}  ", limitTime6);


        AppAdComposePO activity7 = new AppAdComposePO();
        activity7.setDataType(Byte.valueOf("7"));
        activity7.setAdId(appAd.getId());
        activity7.setShowType(AppAdUtil.getShowType(appAd.getActivity7_show_type()));
        activity7.setShowPic(StringUtils.trim(appAd.getActivity7_showPic()));
        activity7.setGoUrl(StringUtils.trim(appAd.getActivity7_goUrl()));
        if (appAd.getActivity7_goUrl() != null) {
            activity7.setGoUrl(StringUtils.trim(appAd.getActivity7_goUrl().replace("&amp;", "&")));
        }
        activity7.setOrders(AppAdUtil.getOrders(appAd.getActivity7_orders()));
        baseDao.insert("appAdComposePOMapper.insertSelective", activity7);
        logger.info("saveAppAdComposeList activity7 save  {}  ", activity7);


        logger.info("saveAppAdComposeList 全部保存完成  {}  ", appAd);

        return 1;


    }

    /**
     * 修改-组合弹窗内容
     *
     * @param appAd
     * @return
     */
    private int updateAppAdComposeList(AppAdPO appAd) {
        logger.info("updateAppAdComposeList update  {}  ", appAd);

        try {

            if (appAd == null || appAd.getId() == null || appAd.getShowPosition() == null || appAd.getComposeType() == null) {
                //非首页组合弹窗
                return 0;
            }
            if (appAd.getShowPosition() != 26 || appAd.getComposeType() != 2) {
                //非首页组合弹窗
                return 0;
            }
            AppAdComposePO activity1 = new AppAdComposePO();
            activity1.setDataType(Byte.valueOf("1"));
            activity1.setAdId(appAd.getId());
            activity1.setShowType(AppAdUtil.getShowType(appAd.getActivity1_show_type()));
            activity1.setGoUrl(StringUtils.trim(appAd.getActivity1_goUrl()));
            if (appAd.getActivity1_goUrl() != null) {
                activity1.setGoUrl(StringUtils.trim(appAd.getActivity1_goUrl().replace("&amp;", "&")));
            }
            activity1.setShowPic(StringUtils.trim(appAd.getActivity1_showPic()));
            activity1.setOrders(AppAdUtil.getOrders(appAd.getActivity1_orders()));
            activity1.setBackgroundColor(appAd.getBackgroundColor());
            baseDao.insert("appAdComposePOMapper.updateByPrimaryKeySelective", activity1);
            logger.info("updateAppAdComposeList activity1 save  {}  ", activity1);


            AppAdComposePO fix2 = new AppAdComposePO();
            fix2.setDataType(Byte.valueOf("2"));
            fix2.setAdId(appAd.getId());
            fix2.setShowType(AppAdUtil.getShowType(appAd.getFix2_show_type()));
            if (appAd.getFix2_goUrl() != null) {
                fix2.setGoUrl(StringUtils.trim(appAd.getFix2_goUrl().replace("&amp;", "&")));
            }
            fix2.setOrders(AppAdUtil.getOrders(appAd.getFix2_orders()));
            baseDao.insert("appAdComposePOMapper.updateByPrimaryKeySelective", fix2);
            logger.info("updateAppAdComposeList fix2 save  {}  ", fix2);


            AppAdComposePO redPacket3 = new AppAdComposePO();
            redPacket3.setDataType(Byte.valueOf("3"));
            redPacket3.setAdId(appAd.getId());
            redPacket3.setShowType(AppAdUtil.getShowType(appAd.getRedPacket3_show_type()));
            if (appAd.getInsureFundType() != null) {
                redPacket3.setInsureFundType(Byte.parseByte(appAd.getInsureFundType() + ""));
            }

            if (appAd.getRedPacket3_goUrl() != null) {
                redPacket3.setGoUrl(StringUtils.trim(appAd.getRedPacket3_goUrl().replace("&amp;", "&")));
            }
            redPacket3.setOrders(AppAdUtil.getOrders(appAd.getRedPacket3_orders()));
            baseDao.insert("appAdComposePOMapper.updateByPrimaryKeySelective", redPacket3);
            logger.info("updateAppAdComposeList redPacket3 save  {}  ", redPacket3);


            AppAdComposePO yrb4 = new AppAdComposePO();
            yrb4.setDataType(Byte.valueOf("4"));
            yrb4.setAdId(appAd.getId());
            yrb4.setShowPic(StringUtils.trim(appAd.getYrb4_showPic()));
            yrb4.setShowType(AppAdUtil.getShowType(appAd.getYrb4_show_type()));
            yrb4.setGoUrl(appAd.getYrb4_goUrl());
            if (appAd.getYrb4_goUrl() != null) {
                yrb4.setGoUrl(StringUtils.trim(appAd.getYrb4_goUrl().replace("&amp;", "&")));
            }
            yrb4.setOrders(AppAdUtil.getOrders(appAd.getYrb4_orders()));
            baseDao.insert("appAdComposePOMapper.updateByPrimaryKeySelective", yrb4);
            logger.info("updateAppAdComposeList yrb4 save  {}  ", yrb4);


            AppAdComposePO points5 = new AppAdComposePO();
            points5.setDataType(Byte.valueOf("5"));
            points5.setAdId(appAd.getId());
            points5.setShowType(AppAdUtil.getShowType(appAd.getPoints5_show_type()));
            points5.setShowPic(StringUtils.trim(appAd.getPoints5_showPic()));
            points5.setGoUrl(StringUtils.trim(appAd.getPoints5_goUrl()));
            if (appAd.getPoints5_goUrl() != null) {
                points5.setGoUrl(StringUtils.trim(appAd.getPoints5_goUrl().replace("&amp;", "&")));
            }
            points5.setOrders(AppAdUtil.getOrders(appAd.getPoints5_orders()));
            baseDao.insert("appAdComposePOMapper.updateByPrimaryKeySelective", points5);
            logger.info("updateAppAdComposeList points5 save  {}  ", points5);


            AppAdComposePO limitTime6 = new AppAdComposePO();
            limitTime6.setDataType(Byte.valueOf("6"));
            limitTime6.setBackgroundPic(StringUtils.trim(appAd.getBackgroundPic()));
            limitTime6.setMiddleDesc(appAd.getLimitTime6_middleDesc());
            limitTime6.setBottomDesc(appAd.getLimitTime6_bottomDesc());
            limitTime6.setAdId(appAd.getId());
            limitTime6.setShowType(AppAdUtil.getShowType(appAd.getLimitTime6_show_type()));
            limitTime6.setShowPic(StringUtils.trim(appAd.getLimitTime6_showPic()));
            limitTime6.setButtonDesc(appAd.getLimitTime6_buttonDesc());
            limitTime6.setGoUrl(StringUtils.trim(appAd.getLimitTime6_goUrl()));
            if (appAd.getLimitTime6_goUrl() != null) {
                limitTime6.setGoUrl(StringUtils.trim(appAd.getLimitTime6_goUrl().replace("&amp;", "&")));
            }
            limitTime6.setOrders(AppAdUtil.getOrders(appAd.getLimitTime6_orders()));
            baseDao.insert("appAdComposePOMapper.updateByPrimaryKeySelective", limitTime6);
            logger.info("updateAppAdComposeList limitTime6 update  {}  ", limitTime6);


            AppAdComposePO activity7 = new AppAdComposePO();
            activity7.setDataType(Byte.valueOf("7"));
            activity7.setAdId(appAd.getId());
            activity7.setShowType(AppAdUtil.getShowType(appAd.getActivity7_show_type()));
            activity7.setShowPic(StringUtils.trim(appAd.getActivity7_showPic()));
            activity7.setGoUrl(StringUtils.trim(appAd.getActivity7_goUrl()));
            if (appAd.getActivity7_goUrl() != null) {
                activity7.setGoUrl(StringUtils.trim(appAd.getActivity7_goUrl().replace("&amp;", "&")));
            }
            activity7.setOrders(AppAdUtil.getOrders(appAd.getActivity7_orders()));
            AppAdComposePO tempComposePo = baseDao.get("appAdComposePOMapper.selectByDataType", activity7);
            if (tempComposePo == null || tempComposePo.getId() == null) {
                baseDao.insert("appAdComposePOMapper.insertSelective", activity7);
            } else {
                baseDao.insert("appAdComposePOMapper.updateByPrimaryKeySelective", activity7);
            }

            logger.info("updateAppAdComposeList points7 update  {}  ", activity7);


            logger.info("updateAppAdComposeList 全部保存完成  {}  ", appAd);

        } catch (Exception e) {
            logger.error("修改异常", e);
        }
        return 1;


    }

    private void checkedAppAdPO(AppAdPO appAdPO) {
//        if (appAdPO == null || appAdPO.getShowPosition() == null) {
//            throw new IllegalArgumentException("param is invalid!");
//        }
        if (appAdPO.getShowPosition() == null) {
            return;
        }
        if (appAdPO.getShowPosition() == 1 || appAdPO.getShowPosition() == 2 || appAdPO.getShowPosition() == 5) {
            if (StringUtils.isBlank(appAdPO.getShowPic())) {
                throw new RuntimeException("展示图片不能为空!");
            }
        }

        if (appAdPO.getUserStates() != null && appAdPO.getUserStates().contains("6") && appAdPO.getUserStates().length() > 1) {
            throw new RuntimeException("用户态配置不合法，[注册当日]条件只能单选!");
        }

        if (appAdPO.getShowPosition() == 3 || appAdPO.getShowPosition() == 4) {
            List<AppAdPO> existsAppAdPOs = baseDao.getList("appAdMapper.selectByPosiotion", appAdPO);
            for (AppAdPO ad : existsAppAdPOs) {
                if (appAdPO.getId() != null && appAdPO.getId().longValue() == ad.getId()) {
                    continue;
                }
                if (appAdPO.getOrders().intValue() == ad.getOrders()) {
                    throw new RuntimeException(String.format("已经存在相同优先级的条目，id:%s, title:%s", ad.getId(), ad.getTitle()));
                }
            }
        }
        if (StringUtils.isEmpty(appAdPO.getClientTypes())) {
            throw new RuntimeException("客户端类型不能为空！");
        }
        
        // 版本字段相互依赖验证：最低版本和最高版本必须同时填写或同时不填
        boolean hasMinVersion = StringUtils.isNotBlank(appAdPO.getMinVersion());
        boolean hasMaxVersion = StringUtils.isNotBlank(appAdPO.getMaxVersion());
        if (hasMinVersion != hasMaxVersion) {
            throw new RuntimeException("最低版本和最高版本必须同时填写或同时不填！");
        }
    }

    private Collection<UserTag> getUserHadTags(P2puserBean p2puserBean, String allConfigTags) {
        List<UserTag> userHadTags = new ArrayList<>();
        if (p2puserBean != null && !Strings.isNullOrEmpty(allConfigTags)) {
            // 只有用户登录 && 配置有需要通过标签过滤时才查用户标签，减少请求数量
            userHadTags = taglibApi.searchUserTag(p2puserBean.getP2puserId(), allConfigTags);
        }
        return Collections2.filter(userHadTags, new Predicate<UserTag>() {
            @Override
            public boolean apply(UserTag tag) {
                return tag != null && StringUtils.isNotBlank(tag.getValue()) && !"null".equalsIgnoreCase(tag.getValue()) && !"0".equals(tag.getValue());
            }
        });
    }

    private boolean checkUserTagsSatisfy(AppAdStatus status, AppAdPO appAdPO, Collection<UserTag> userHadTags) {
        // 查询用户是否满足标签库设置
        switch (status) {
            case ALL_VISIBLE:
                return true;
            case NOT_VISIBLE:
                return false;
            case PARTIAL_VISIBLE:
                return checkUserTagMatchRequiredTags(appAdPO, userHadTags);
            case PARTIAL_INVISIBLE:
                return !checkUserTagMatchRequiredTags(appAdPO, userHadTags);
        }
        return false;
    }

    private boolean checkUserTagMatchRequiredTags(AppAdPO appAdPO, Collection<UserTag> userHadTags) {
        Set<String> allConfigTagSet = new HashSet<>();
        allConfigTagSet.addAll(TagsDTO.getTagsByJson(appAdPO.getSpecialUserTags()));
        for (final String tag : allConfigTagSet) {
            if (Iterables.any(userHadTags, new Predicate<UserTag>() {
                @Override
                public boolean apply(UserTag userTag) {
                    return tag.equals(userTag.getName());
                }
            })) {
                return true;
            }
        }
        return false;
    }

    // 检查用户状态是否已阅
    private boolean checkUserAppAdStatus(P2puserBean p2puserBean, byte showPosition) {
        AppAdUserPO appAdUser = new AppAdUserPO();
        appAdUser.setP2puserId(Long.parseLong(p2puserBean.getP2puserId()));
        appAdUser.setShowPosition(showPosition);
        AppAdUserPO appAdUser2 = baseDao.get("AppAdUserPOMapper.selectByParam", appAdUser);
        if (appAdUser2 != null) {
            return Boolean.TRUE; // 用户已阅
        }
        return Boolean.FALSE;
    }

    @Override
    public DataGrid<List<AppAdPO>> getAppAds(AppAdParam appAdParam, boolean isSpecialAccount) {
        DataGrid dataGrid = new DataGrid();
        appAdParam.setSpecialAccount(isSpecialAccount);
        long rowsCount = baseDao.get("appAdMapper.countAppAdsSelectedDing", appAdParam);
        if (rowsCount > 0) {
            Map<String, String> featureIdChannelMap = appAdChannelService.getFeatureIdChannelMap(WithChannelFeatureType.APP_AD);

            List<AppAdPO> appAds = baseDao.getList("appAdMapper.selectAppAdsDing", appAdParam);
            logger.info(" appAds {}", JsonUtil.beanToJson(appAds));

            for (AppAdPO appAdPO : appAds) {
                if (isSpecialAccount) {
                    appAdPO.setSpecialUserTagNames(TagUtils.getTagNamesEnglish(appAdPO.getSpecialUserTags()));
                } else {
                    appAdPO.setSpecialUserTagNames(TagUtils.getTagNames(appAdPO.getSpecialUserTags()));
                }
                appAdPO.setChannelName(featureIdChannelMap.get(appAdPO.getId().toString()));
                if (AppAdUtil.COMPOSE.equals(appAdPO.getComposeType())) {
                    AppAdComposePO appAdComposePO = new AppAdComposePO();
                    appAdComposePO.setAdId(appAdPO.getId());
                    appAdComposePO.setDataType(Byte.valueOf("1"));
                    AppAdComposePO composePOData = baseDao.get("appAdComposePOMapper.selectByDataType", appAdComposePO);
                    if (composePOData != null) {
                        appAdPO.setGoUrl(composePOData.getGoUrl());
                        appAdPO.setShowPic(composePOData.getShowPic());
                    } else {
                        appAdPO.setGoUrl("");
                        appAdPO.setShowPic("");
                    }

                }
            }
            dataGrid.setRows(appAds);
        }
        dataGrid.setTotal(rowsCount);
        return dataGrid;
    }

    @Override
    public List<AppAdPositionPO> getPositionsConfig() {

        InitDataBean initDataBean = new InitDataBean();
        initDataBean.setInitGroup(initGroupAppAdPosition);
        initDataBean.setPage(initDataBeanSelectPage);
        initDataBean.setRows(initDataBeanSelectRows);
        final List<InitDataBean> infoBeanList = baseDao.getList("initDataBeanMapper.selectList", initDataBean);
        if (CollectionUtils.isEmpty(infoBeanList)) {
            return appAdPositionPOSDefault;
        }

        final Map<String, String> tempAppPositionConfigMap = Maps.newHashMap();
        List<AppAdPositionPO> appAdPositionPOS = new ArrayList<>(Lists.transform(infoBeanList, new Function<InitDataBean, AppAdPositionPO>() {
            @Override
            public AppAdPositionPO apply(InitDataBean input) {
                tempAppPositionConfigMap.put(input.getInitValue(), input.getInitComment());
                return new AppAdPositionPO(input.getInitValue(), input.getInitComment());
            }
        }));
        appPositionConfigMap = tempAppPositionConfigMap;

        return appAdPositionPOS;
    }

    @Override
    public List<AppAdPO> getAppAdSpecified(List<String> positions, String clientType, String tag, P2puserBean p2puserBean) {
        if (CollectionUtils.isEmpty(positions) || StringUtils.isBlank(clientType)) {
            logger.error("查询广告位参数错误，position={}, clientType={}, tag={}.", positions, clientType, tag);
            return Collections.emptyList();
        }

        List<String> clientTypes = Lists.newArrayList();
        clientTypes.add(clientTypeCommon);
        clientTypes.add(clientType);
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("positions", positions);
        paramMap.put("clientTypes", clientTypes);
        if (positions.contains("7") && p2puserBean != null) {
            AccountInsuranceStatusEnum status = insuranceService.getUserAccountStatus(p2puserBean.getP2puserId());
            switch (status) {
                case Expiration:
                    tag = "已过期";
                    break;
                case NOT_Guarantee:
                    tag = "未保障";
                    break;
                case In_Guarantee:
                    tag = "保障中";
                    break;
            }
        }
        paramMap.put("tag", Strings.nullToEmpty(tag));
        paramMap.put("userId", p2puserBean != null ? p2puserBean.getP2puserId() : null);

        return baseDao.getList("appAdMapper.selectAppAdForSpecifiedPosition", paramMap);
    }

    @Override
    public AppAdPositionPO getPositionSpecified(String positionCode) {
        return new AppAdPositionPO(positionCode, Strings.nullToEmpty(appPositionConfigMap.get(positionCode)));
    }

    @Override
    public AccountAdVO getAccountInsuranceStatus(String userId) {
        AccountAdVO accountAdVO = new AccountAdVO();
        AccountInsuranceStatusEnum status = insuranceService.getUserAccountStatus(userId);
        switch (status) {
            case Expiration:
                accountAdVO.setTitle("已过期");
                break;
            case NOT_Guarantee:
                accountAdVO.setTitle("未保障");
                break;
            case In_Guarantee:
                accountAdVO.setTitle("保障中");
                break;
        }
        accountAdVO.setGoUrl("https://hlwapp.yixinbaoxian.com/build/product/#/9");
        accountAdVO.setStatus(status.getCode());
        return accountAdVO;
    }

    @Override
    public boolean upAppAdUser(long p2puserId, int showPosition, String clientType) {
        AppAdUserPO appAdUser = new AppAdUserPO();
        appAdUser.setP2puserId(p2puserId);
        appAdUser.setShowPosition((byte) showPosition);
        AppAdUserPO appAdUser2 = baseDao.get("AppAdUserPOMapper.selectByParam", appAdUser);
        if (appAdUser2 == null) {
            appAdUser.setClientType(Byte.parseByte(clientType));
            appAdUser.setCreateTime(new Date());
            return baseDao.insert("AppAdUserPOMapper.insert", appAdUser);
        } else {
            return Boolean.FALSE;
        }
    }

    @Override
    public List<AppAdPO> getAdPositionListByIds(GetPositionByIds getPositionByIds) {
        return baseDao.getList("appAdMapper.selectListByIds", getPositionByIds);
    }

    @Override
    public void showPageData(HttpServletRequest request, Long id) {
        AppAdParam appAdParam = new AppAdParam();
        appAdParam.setId(id);
        AppAdPO bean = baseDao.get("appAdMapper.selectById", appAdParam);
        logger.info("update :{}", JsonUtil.beanToJson(bean));
        request.setAttribute("bean", bean);

        List<AppAdComposePO> list = baseDao.getList("appAdComposePOMapper.selectByAdId", id);
        Map<String, String> extInfo = AppAdUtil.getShowPageData(list);
        extInfo.put("onlineTime", DateUtil.formatDate(bean.getOnlineTime()));
        extInfo.put("offlineTime", DateUtil.formatDate(bean.getOfflineTime()));
        logger.info("extInfo :{}", JsonUtil.beanToJson(extInfo));

        request.setAttribute("extInfo", extInfo);


    }

    @Override
    public void afterPropertiesSet() throws Exception {
        appPositionConfigMap = mapSplitter.split(AppAdPositionEnum.APP_POSITIONS_DESC);
        for (Map.Entry<String, String> entry : appPositionConfigMap.entrySet()) {
            appAdPositionPOSDefault.add(new AppAdPositionPO(entry.getKey(), entry.getValue()));
        }
    }

    /**
     * 删除缓存操作
     */
    private void delAppAdRedisKey(AppAdPO appAd) {
        String loginKey = ConstantsForRedis.SUPPORT_QUERY_APP_AD_LOGIN_INFO_LIST_KEY;
        String unLoginKey = ConstantsForRedis.SUPPORT_QUERY_APP_AD_INFO_LIST_KEY;
        logger.info("【删除推广位缓存】key:{},删除结果:{}", loginKey, jedisCluster.del(String.format(loginKey, appAd.getShowPosition())));
        logger.info("【删除推广位缓存】key:{},删除结果:{}", unLoginKey, jedisCluster.del(String.format(unLoginKey, appAd.getShowPosition())));
    }

}
