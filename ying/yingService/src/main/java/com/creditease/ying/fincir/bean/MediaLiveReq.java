package com.creditease.ying.fincir.bean;

import com.creditease.ying.common.util.BeanUtil;
import com.creditease.ying.common.util.HtmlCharacterEscapeUtils;
import com.creditease.ying.common.util.JsonUtil;
import com.creditease.ying.common.util.StringUtil;
import com.google.common.base.Strings;
import lombok.Data;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: HT
 * @Description:
 * @Date:Created in 22:23 2020-03-18
 **/
@Data
public class MediaLiveReq {
    private String id;
    private String articleTitle;    //文章标题
    private String subjectId;//关联的专题id
    private String playerImg;      //播放器封面图
    private String verticalPlayerImg;      //播放器竖版封面图
    private String preheatUrl;      //预热视频
    private String orderlistUrl;      //订单入口地址
    private String imgUrl;      //列表图片
    private String headerImgUrl;      //首页卡片图片
    private String adImgUrl;      //学习页推广图片
    private String contentImg;      //内容简介图
    private String mediaUrl;        //视频地址
    private String mediaUrlForH5;        //视频地址,H5 flv暂时不支持
    private String mediaType; //类型 1横版 2竖版
    private Date startTime;     //直播开始时间
    private Date endTime;       //直播结束时间
    private Integer status;     //状态  -1 已删除 0未发布  1未开播 2正在播 3已结束
    private Integer orderCount; //预约人数
    private Integer leftPoint; //剩余积分
    private BigDecimal readCountScore; //加权倍数
    private String subTitle;
    private String contentDes;
    private String historyRecommend;
    private String guest1Name;
    private String guest1Des;
    private String guest2Name;
    private String guest2Des;
    private String guest3Name;
    private String guest3Des;
    private String guest4Name;
    private String guest4Des;
    private String guest5Name;
    private String guest5Des;
    List<GuestDesBean> guestDesBeanList = new ArrayList<>();
    private String guestStr;
    private String startTimeStr;
    private String endTimeStr;
    private String codeUrl;
    private String codeDes;
    private String paperArticleId;
    private String contentTags;
    /**
     * 控制是否展示在资源位 0 不展示 1 展示
     */
    private Integer displayResourcePosition;

    /**
     * 专栏类型 0 免费 1 收费
     */
    private Integer channelType;

    /**
     * 专栏id
     */
    private Integer channelId;

    /**
     * 是否免费 0 免费 1 收费
     */
    private Integer isFree;

    /**
     * 是否单独售卖 0 否 1 是
     */
    private Integer isSeparateSale;

    /**
     * 单独售卖价格
     */
    private BigDecimal price;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 会员免费 1 健康大会员
     */
    private String freeVipType;

    /**
     * 专栏试看时长（单位：秒）
     */
    private Integer channelPreviewDuration;

    /**
     * 专栏排序
     */
    private Integer channelOrder;

    /**
     * 单独售卖试看时长（单位：秒）
     */
    private Integer separateSalePreviewDuration;

    private String channelListUrl;

    /**
     * 分享状态 1 可分享 0 不可分享
     */
    private Integer shareStatus;

    public MediaLiveBean getLiveBean() {
        if (guestStr != null) {
            guestStr = StringEscapeUtils.unescapeHtml(guestStr);
            guestDesBeanList = JsonUtil.jsonToListBean(guestStr, guestDesBeanList.getClass());
        } else {
            getGuest(guest1Name, guest1Des);
            getGuest(guest2Name, guest2Des);
            getGuest(guest3Name, guest3Des);
            getGuest(guest4Name, guest4Des);
            getGuest(guest5Name, guest5Des);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("guests", guestDesBeanList);
        map.put("subTitle", StringEscapeUtils.unescapeHtml(this.subTitle));
        map.put("contentDes", StringEscapeUtils.unescapeHtml(this.contentDes));
        map.put("historyRecommend", StringEscapeUtils.unescapeHtml(this.historyRecommend));
        map.put("codeUrl",codeUrl == null ? null : codeUrl.trim());
        map.put("codeDes",StringEscapeUtils.unescapeHtml(this.codeDes));
        map.put("paperArticleId",paperArticleId);
        map.put("mediaUrlForH5", mediaUrlForH5);
        String extend = JsonUtil.beanToJson(map);
        MediaLiveBean liveBean = new MediaLiveBean();

        liveBean.setId(id);
        liveBean.setContentTags(contentTags);
        liveBean.setArticleTitle(HtmlCharacterEscapeUtils.Escape(articleTitle));
        liveBean.setSubjectId(subjectId);
        liveBean.setStatus(this.status);
        liveBean.setContentImg(contentImg == null ? null : contentImg.trim());
        liveBean.setEndTime(endTime);
        liveBean.setImgUrl(imgUrl == null ? null : imgUrl.trim());
        liveBean.setHeaderImgUrl(headerImgUrl == null ? null : headerImgUrl.trim());
        liveBean.setAdImgUrl(adImgUrl == null ? null : adImgUrl.trim());
        liveBean.setMediaUrl(mediaUrl == null ? null : mediaUrl.trim());
        if(LiveMediaTypeEnum.codeOf(StringUtils.isEmpty(mediaType) ? null : Integer.parseInt(mediaType)) == null) {
            throw new IllegalArgumentException("mediaType must be 1 or 2");
        }
        liveBean.setMediaType(mediaType);
        liveBean.setOrderCount(orderCount);
        liveBean.setLeftPoint(leftPoint);
        liveBean.setPlayerImg(playerImg == null ? null : playerImg.trim());
        if((LiveMediaTypeEnum.VERTICAL.getCode()+"").equals(mediaType) && (StringUtils.isEmpty(verticalPlayerImg) && StringUtils.isEmpty(preheatUrl))) {
            throw new IllegalArgumentException("请补充竖版封面图片或预热视频");
        }
        if((LiveMediaTypeEnum.HORIZONTAL.getCode()+"").equals(mediaType) && (StringUtils.isEmpty(playerImg) && StringUtils.isEmpty(preheatUrl))) {
            throw new IllegalArgumentException("请补充横版封面图片或预热视频");
        }
        liveBean.setVerticalPlayerImg(verticalPlayerImg == null ? null : verticalPlayerImg.trim());
        liveBean.setPreheatUrl(preheatUrl == null ? null : preheatUrl.trim());
        liveBean.setOrderlistUrl(orderlistUrl == null ? null : orderlistUrl.trim());
        liveBean.setStartTime(startTime);
        liveBean.setExtendInfo(extend);
        liveBean.setReadCountScore(readCountScore);
        liveBean.setDisplayResourcePosition(displayResourcePosition);

        liveBean.setChannelId(channelId);
        liveBean.setIsFree(isFree);
        liveBean.setIsSeparateSale(isSeparateSale);
        liveBean.setSkuId(skuId);
        liveBean.setFreeVipType(freeVipType);
        liveBean.setChannelPreviewDuration(channelPreviewDuration);
        liveBean.setChannelOrder(channelOrder);
        liveBean.setSeparateSalePreviewDuration(separateSalePreviewDuration);
        liveBean.setChannelListUrl(channelListUrl);
        liveBean.setShareStatus(shareStatus);
        return liveBean;
    }

    private void getGuest(String name, String des) {
        if (!Strings.isNullOrEmpty(name) && !Strings.isNullOrEmpty(des)) {
            name = StringEscapeUtils.unescapeHtml(name);
            des = StringEscapeUtils.unescapeHtml(des);
            guestDesBeanList.add(new GuestDesBean(name, des));
        }
    }

}
