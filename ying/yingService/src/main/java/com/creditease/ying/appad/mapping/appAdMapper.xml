<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="appAdMapper">
    <resultMap id="BaseResultMap" type="com.creditease.ying.appad.bean.po.AppAdPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="ad_desc" property="adDesc" jdbcType="VARCHAR"/>
        <result column="show_position" property="showPosition" jdbcType="TINYINT"/>
        <result column="client_type" property="clientType" jdbcType="TINYINT"/>
        <result column="tag" property="tag" jdbcType="VARCHAR"/>
        <result column="min_version" property="minVersion" jdbcType="VARCHAR"/>
        <result column="max_version" property="maxVersion" jdbcType="VARCHAR"/>
        <result column="go_type" property="goType" jdbcType="TINYINT"/>
        <result column="show_pic" property="showPic" jdbcType="VARCHAR"/>
        <result column="go_url" property="goUrl" jdbcType="VARCHAR"/>
        <result column="enable" property="enable" jdbcType="TINYINT"/>
        <result column="orders" property="orders" jdbcType="TINYINT"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="special_user_tags" property="specialUserTags" jdbcType="VARCHAR" />
        <result column="online_time" property="onlineTime" jdbcType="TIMESTAMP"/>
        <result column="offline_time" property="offlineTime" jdbcType="TIMESTAMP"/>
        <result column="user_states" property="userStates" jdbcType="VARCHAR"/>
        <result column="ad_key" property="adKey" jdbcType="VARCHAR"/>
        <result column="compose_type" property="composeType" jdbcType="TINYINT"/>
        <result column="client_types" property="clientTypes" jdbcType="VARCHAR"/>
        <result column="join_audit" property="joinAudit" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, title, ad_desc, show_position, client_type,client_types, tag, min_version, max_version, go_type,
        show_pic, go_url, orders, enable, operator, update_time, create_time,
        special_user_tags,online_time,offline_time,user_states,ad_key,compose_type,join_audit
    </sql>

    <sql id="Join_Column_List">
        distinct t1.id, t1.title, t1.ad_desc, t1.show_position, t1.client_type, t1.client_types,t1.tag, t1.min_version, t1.max_version, t1.go_type,
        t1.show_pic, t1.go_url, t1.orders, t1.enable, t1.operator, t1.update_time, t1.create_time, t1.special_user_tags,
        t1.online_time, t1.offline_time, t1.user_states,t1.ad_key,compose_type,join_audit
    </sql>


    <sql id="whereSelected">
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="title != null">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="enable != null">
                AND enable = #{enable}
            </if>
            <if test="showPosition != null">
                AND show_position = #{showPosition}
            </if>
            <if test="joinAudit != null and joinAudit> -1">
                AND join_audit = #{joinAudit}
            </if>
        </where>
    </sql>

    <select id="countAppAdsSelected" resultType="long" parameterType="com.creditease.ying.appad.bean.AppAdParam">
        SELECT count(distinct t1.id)
        FROM t_app_ad t1
        <if test="onlyChannel">
            join t_app_feature_with_channel t2 on t1.id = t2.feature_id
        </if>
        <where>
            <if test="id != null">
                AND t1.id = #{id}
            </if>
            <if test="title != null">
                AND t1.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="enable != null">
                AND t1.enable = #{enable}
            </if>
            <if test="showPosition != null">
                AND t1.show_position = #{showPosition}
            </if>
            <if test="joinAudit != null and joinAudit> -1">
                AND t1.join_audit = #{joinAudit}
            </if>
        </where>
    </select>



    <select id="countAppAdsSelectedDing" resultType="long" parameterType="com.creditease.ying.appad.bean.AppAdParam">
        SELECT count(distinct t1.id)
        FROM t_app_ad t1
        <if test="onlyChannel">
            join t_app_feature_with_channel t2 on t1.id = t2.feature_id
        </if>
        <where>
            <if test="id != null">
                AND t1.id = #{id}
            </if>
            <if test="title != null">
                AND t1.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="enable != null">
                AND t1.enable = #{enable}
            </if>
            <if test="showPosition != null">
                AND t1.show_position = #{showPosition}
            </if>

            <if test="userStatus != null and userStatus> 0">
                and user_states  like concat('%', #{userStatus}, '%')
            </if>

            <if test="onlineStatus != null and onlineStatus> 0">
                <if test=" onlineStatus == 1">
                    AND NOW() BETWEEN ONLINE_TIME AND OFFLINE_TIME
                </if>
                <if test=" onlineStatus == 2">
                    AND NOW()   &gt; OFFLINE_TIME
                </if>
                <if test=" onlineStatus == 3">
                    AND NOW() 	&lt; ONLINE_TIME  OR  ONLINE_TIME is null
                </if>
            </if>
            <if test="isSpecialAccount">
                AND id NOT IN (SELECT key_id FROM hidden_record_config WHERE key_table='t_app_ad')
            </if>
            <if test="joinAudit != null and joinAudit> -1">
                AND t1.join_audit = #{joinAudit}
            </if>

        </where>
    </select>

    <select id="selectAppAds" resultMap="BaseResultMap" parameterType="com.creditease.ying.appad.bean.AppAdParam">
        SELECT
        <include refid="Join_Column_List"/>
        FROM t_app_ad t1
        <if test="onlyChannel">
            join t_app_feature_with_channel t2 on t1.id = t2.feature_id
        </if>
        <include refid="whereSelected"/>
        <if test="sort != null and sort != '' ">
            ORDER BY ${sort} ${order}
        </if>
        limit #{pageStart},#{rows}
    </select>



    <select id="selectAppAdsDing" resultMap="BaseResultMap" parameterType="com.creditease.ying.appad.bean.AppAdParam">
        SELECT
        <include refid="Join_Column_List"/>
        FROM t_app_ad t1
        <if test="onlyChannel">
            join t_app_feature_with_channel t2 on t1.id = t2.feature_id
        </if>
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="title != null">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="enable != null">
                AND enable = #{enable}
            </if>
            <if test="showPosition != null">
                AND show_position = #{showPosition}
            </if>

            <if test="userStatus != null and userStatus> 0">
                and user_states  like concat('%', #{userStatus}, '%')
            </if>

            <if test="onlineStatus != null and onlineStatus> 0">
                <if test=" onlineStatus == 1">
                    AND NOW() BETWEEN ONLINE_TIME AND OFFLINE_TIME
                </if>
                <if test=" onlineStatus == 2">
                    AND NOW()   &gt; OFFLINE_TIME
                </if>
                <if test=" onlineStatus == 3">
                    AND NOW() 	&lt; ONLINE_TIME OR  ONLINE_TIME is null
                </if>
            </if>
            <if test="isSpecialAccount">
                AND id NOT IN (SELECT key_id FROM hidden_record_config WHERE key_table='t_app_ad')
            </if>
            <if test="joinAudit != null and joinAudit> -1">
                AND t1.join_audit = #{joinAudit}
            </if>

        </where>
        <if test="sort != null and sort != '' ">
            ORDER BY ${sort} ${order}
        </if>
        limit #{pageStart},#{rows}
    </select>

    <select id="selectById" resultMap="BaseResultMap" parameterType="com.creditease.ying.appad.bean.AppAdParam">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_app_ad
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
        </where>
     limit 1

    </select>

    <select id="selectByPosiotion" resultMap="BaseResultMap" parameterType="com.creditease.ying.appad.bean.AppAdParam">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_app_ad
        <where>
            <if test="showPosition != null">
                AND show_position = #{showPosition}
            </if>
        </where>
    </select>


    <insert id="insertSelective" parameterType="com.creditease.ying.appad.bean.po.AppAdPO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into t_app_ad
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">
                title,
            </if>
            <if test="adDesc != null">
                ad_desc,
            </if>
            <if test="showPosition != null">
                show_position,
            </if>
            <if test="clientType != null">
                client_type,
            </if>
            <if test="clientTypes != null">
                client_types,
            </if>
            <if test="tag != null">
                tag,
            </if>
            <if test="minVersion != null">
                min_version,
            </if>
            <if test="maxVersion != null">
                max_version,
            </if>
            <if test="goType != null">
                go_type,
            </if>
            <if test="showPic != null">
                show_pic,
            </if>
            <if test="goUrl != null">
                go_url,
            </if>
            <if test="orders != null">
                orders,
            </if>
            <if test="enable != null">
                enable,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
             <if test="onlineTime != null">
                online_time,
            </if>
             <if test="offlineTime != null">
                offline_time,
            </if>
            <if test="userStates != null">
                user_states,
            </if>
            <if test="adKey != null">
                ad_key,
            </if>
            <if test="composeType != null">
                compose_type,
            </if>
            <if test="joinAudit != null">
                join_audit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="adDesc != null">
                #{adDesc,jdbcType=VARCHAR},
            </if>
            <if test="showPosition != null">
                #{showPosition,jdbcType=TINYINT},
            </if>
            <if test="clientType != null">
                #{clientType,jdbcType=TINYINT},
            </if>
            <if test="clientTypes != null">
                #{clientTypes,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=VARCHAR},
            </if>
            <if test="minVersion != null">
                #{minVersion,jdbcType=VARCHAR},
            </if>
            <if test="maxVersion != null">
                #{maxVersion,jdbcType=VARCHAR},
            </if>
            <if test="goType != null">
                #{goType,jdbcType=TINYINT},
            </if>
            <if test="showPic != null">
                #{showPic,jdbcType=VARCHAR},
            </if>
            <if test="goUrl != null">
                #{goUrl,jdbcType=VARCHAR},
            </if>
            <if test="orders != null">
                #{orders,jdbcType=TINYINT},
            </if>
            <if test="enable != null">
                #{enable,jdbcType=TINYINT},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="offlineTime != null">
                #{offlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userStates != null">
                #{userStates,jdbcType=VARCHAR},
            </if>
            <if test="adKey != null">
                #{adKey,jdbcType=VARCHAR},
            </if>

            <if test="composeType != null">
                #{composeType,jdbcType=TINYINT},
            </if>
            <if test="joinAudit != null">
                #{joinAudit,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.creditease.ying.appad.bean.po.AppAdPO">
        update t_app_ad
        <set>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="adDesc != null">
                ad_desc = #{adDesc,jdbcType=VARCHAR},
            </if>
            <if test="showPosition != null">
                show_position = #{showPosition,jdbcType=TINYINT},
            </if>
            <if test="clientType != null">
                client_type = #{clientType,jdbcType=TINYINT},
            </if>
            <if test="clientTypes != null">
                client_types = #{clientTypes,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                tag = #{tag,jdbcType=VARCHAR},
            </if>
            <if test="minVersion != null">
                min_version = #{minVersion,jdbcType=VARCHAR},
            </if>
            <if test="maxVersion != null">
                max_version = #{maxVersion,jdbcType=VARCHAR},
            </if>
            <if test="goType != null">
                go_type = #{goType,jdbcType=TINYINT},
            </if>
            <if test="showPic != null">
                show_pic = #{showPic,jdbcType=VARCHAR},
            </if>
            <if test="goUrl != null">
                go_url = #{goUrl,jdbcType=VARCHAR},
            </if>
            <if test="orders != null">
                orders = #{orders,jdbcType=TINYINT},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=TINYINT},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="specialUserTags != null">
                special_user_tags = #{specialUserTags,jdbcType=VARCHAR},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="offlineTime != null">
                offline_time = #{offlineTime,jdbcType=TIMESTAMP},
            </if>
             <if test="userStates != null">
                user_states = #{userStates,jdbcType=VARCHAR},
            </if>
            <if test="adKey != null">
                ad_key = #{adKey,jdbcType=VARCHAR},
            </if>
            <if test="composeType != null">
                compose_type = #{composeType,jdbcType=VARCHAR},
            </if>
            <if test="joinAudit != null">
                join_audit = #{joinAudit,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAppAdForSpecifiedPosition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_app_ad
        WHERE
        show_position IN
        <foreach item="item" index="index" collection="positions" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        AND client_type in
        <foreach item="item" index="index" collection="clientTypes" open="(" separator="," close=")">
            #{item,jdbcType=TINYINT}
        </foreach>
        <!-- 登录状态下，返回全部可见/部分可见/部分不可见的数据；非登录状态下仅返回全部可见的数据 -->
        <choose>
            <when test="userId != null">
                AND enable IN (1, 2, 3)
            </when>
            <otherwise>
                AND enable = 1
            </otherwise>
        </choose>
        <if test="tag != null and tag != ''">
            AND tag = #{tag,jdbcType=VARCHAR}
        </if>
        ORDER BY orders ASC
    </select>

    <select id="selectListByIds" resultMap="BaseResultMap" parameterType="com.creditease.ying.withchannel.bean.GetPositionByIds">
        select
        <include refid="Base_Column_List"/>
        from t_app_ad
        <where>
            <if test="ids != null and ids.size() > 0">
                AND ID IN
                <foreach collection="ids" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="ids == null or ids.size() == 0">
                1 = 2
            </if>
        </where>
    </select>
</mapper>