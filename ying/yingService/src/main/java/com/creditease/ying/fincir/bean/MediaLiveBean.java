package com.creditease.ying.fincir.bean;

import com.creditease.ying.common.model.page.PageHelper;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.*;


@Data
@Accessors(chain = true)
public class MediaLiveBean extends PageHelper implements java.io.Serializable {
    private String id;
    private String articleTitle;    //文章标题
    private String subjectId;//关联的专题
    private String playerImg;      //播放器封面图
    private String verticalPlayerImg;      //播放器竖版封面图
    private String preheatUrl;      //预热直播地址
    private String orderlistUrl;      //订单入口地址
    private String imgUrl;      //列表图片
    private String headerImgUrl;      //首页卡片图片
    private String adImgUrl;      //学习页推广图片
    private String contentImg;      //内容简介图
    private String mediaUrl;        //视频地址
    private String mediaUrlForH5;        //视频地址,H5 flv暂时不支持
    private String mediaType;        //视频类型：1横版 2竖版
    private Date startTime;     //直播开始时间
    private Date endTime;       //直播结束时间
    private Integer status;     //状态  -1 已删除 0未发布  1直播中 2未开始 3已结束
    private Integer orderCount; //预约人数
    private Integer leftPoint; //本场直播剩余积分
    private String extendInfo;
    private Date startTimeBegin;  //直播开始时间范围查询的起始时间
    private Date startTimeEnd;   //直播开始时间范围查询的结束时间
    private BigDecimal readCountScore;   //加权倍数
    private String isHaveTask;  //是否配置了福利活动
    private Integer duration;//直播时长
    private String contentTags;
    private Date pushFlowTime;
    private Integer displayResourcePosition;

    /**
     * 专栏id
     */
    private Integer channelId;

    /**
     * 是否免费 0 免费 1 收费
     */
    private Integer isFree;

    /**
     * 是否单独售卖 0 否 1 是
     */
    private Integer isSeparateSale;

    /**
     * skuId
     */
    private String skuId;
    /**
     * 会员免费 1 健康大会员
     */
    private String freeVipType;

    /**
     * 专栏试看时长（单位：秒）
     */
    private Integer channelPreviewDuration;

    /**
     * 专栏排序
     */
    private Integer channelOrder;

    /**
     * 单独售卖试看时长（单位：秒）
     */
    private Integer separateSalePreviewDuration;

    /**
     * 专栏列表页地址
     */
    private String channelListUrl;

    /**
     * 分享状态 1 可分享 0 不可分享
     */
    private Integer shareStatus;
}
