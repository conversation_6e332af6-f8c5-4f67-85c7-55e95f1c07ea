package com.creditease.ying.appad.bean.po;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Getter
@Setter
@ToString
public class AppAdPO {
    private Long id;
    private String title;
    private String adDesc;
    private Integer showPosition;
    private String clientTypes;
    /**
     * 安易盈H5支持多选需求该字段标注过期，替换字段clientTypes
     */
    @Deprecated
    private Integer clientType;
    private String tag;
    private String minVersion;
    private String maxVersion;
    private Integer goType;
    private String showPic;
    private String goUrl;
    private Integer enable;
    private String operator;
    private Integer orders = 0;
    private Date updateTime;
    private Date createTime;
    private Date onlineTime;
    private Date offlineTime;

    private String userStates;

    private String specialUserTags;

    private String specialUserTagNames;
    private String channelName;
    private String adKey;
    /**
     * 1非组合
     * 2组合
     */
    private Integer composeType;
    private Integer activity1_show_type;
    private String activity1_goUrl;
    private String activity1_showPic;
    private Integer activity1_orders = 0;
    /**
     * 背景色dataType=1才有:#000000
     */
    private String backgroundColor;



    private Integer fix2_show_type = 0;
    private String fix2_goUrl;
    private Integer fix2_orders = 0;


    private Integer redPacket3_show_type = 0;
    /**
     * 0保险+基金;1保险;2基金
     */
    private Integer insureFundType;
    private String redPacket3_goUrl;
    private Integer redPacket3_orders = 0;



    private Integer yrb4_show_type;
    private String yrb4_showPic;
    private String yrb4_goUrl;
    private Integer yrb4_orders;


    private Integer points5_show_type;
    private String points5_showPic;
    private String points5_goUrl;
    private Integer points5_orders;



    private Integer limitTime6_show_type;
    private String limitTime6_goUrl;
    private String limitTime6_showPic;
    private String limitTime6_buttonDesc;



    private Integer limitTime6_orders;


    /**
     * 背景图dataType=6才有:url
     * limitTime6_middleDesc
     * limitTime6_bottomDesc
     * backgroundPic
     */
    private String backgroundPic;
    private String limitTime6_middleDesc;
    private String limitTime6_bottomDesc;


    /**
     * 是否展示
     */
    private Integer activity7_show_type;
    /**
     * 展示图片
     */

    private String activity7_showPic;
    /**
     * 图片跳转
     */
    private String activity7_goUrl;
    /**
     * 排序
     */
    private Integer activity7_orders;

    private Integer joinAudit;

}
