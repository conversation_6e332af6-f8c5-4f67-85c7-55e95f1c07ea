<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="LiveBeanMapper">
    <resultMap id="BaseResultMap" type="com.creditease.ying.fincir.bean.MediaLiveBean">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="article_title" property="articleTitle" jdbcType="VARCHAR"/>
        <result column="player_img" property="playerImg" jdbcType="VARCHAR"/>
        <result column="vertical_player_img" property="verticalPlayerImg" jdbcType="VARCHAR"/>
        <result column="preheat_url" property="preheatUrl" jdbcType="VARCHAR"/>
        <result column="img_url" property="imgUrl" jdbcType="VARCHAR"/>
        <result column="header_img_url" property="headerImgUrl" jdbcType="VARCHAR"/>
        <result column="ad_img_url" property="adImgUrl" jdbcType="VARCHAR"/>
        <result column="content_img" property="contentImg" jdbcType="VARCHAR"/>
        <result column="media_url" property="mediaUrl" jdbcType="VARCHAR"/>
        <result column="media_type" property="mediaType" jdbcType="CHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="order_count" property="orderCount" jdbcType="INTEGER"/>
        <result column="left_point" property="leftPoint" jdbcType="INTEGER"/>
        <result column="extend_info" property="extendInfo" jdbcType="VARCHAR"/>
        <result column="read_count_score" property="readCountScore" jdbcType="DECIMAL"/>
        <result column="orderlist_url" property="orderlistUrl" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="INTEGER"/>
        <result column="push_flow_time" property="pushFlowTime" jdbcType="TIMESTAMP"/>
        <result column="display_resource_position" property="displayResourcePosition" jdbcType="INTEGER"/>

        <result column="channel_id" jdbcType="INTEGER" property="channelId" />
        <result column="is_free" jdbcType="INTEGER" property="isFree" />
        <result column="is_separate_sale" jdbcType="INTEGER" property="isSeparateSale" />
        <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
        <result column="free_vip_type" jdbcType="VARCHAR" property="freeVipType" />
        <result column="channel_preview_duration" jdbcType="INTEGER" property="channelPreviewDuration" />
        <result column="channel_order" jdbcType="INTEGER" property="channelOrder" />
        <result column="separate_sale_preview_duration" jdbcType="INTEGER" property="separateSalePreviewDuration" />
        <result column="channel_list_url" jdbcType="VARCHAR" property="channelListUrl" />
        <result column="share_status" jdbcType="INTEGER" property="shareStatus" />
    </resultMap>

    <resultMap id="LiveAndDataResultMap" type="com.creditease.ying.fincir.bean.MediaLiveAndDataBean">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="article_title" property="articleTitle" jdbcType="VARCHAR"/>
        <result column="player_img" property="playerImg" jdbcType="VARCHAR"/>
        <result column="vertical_player_img" property="verticalPlayerImg" jdbcType="VARCHAR"/>
        <result column="preheat_url" property="preheatUrl" jdbcType="VARCHAR"/>
        <result column="img_url" property="imgUrl" jdbcType="VARCHAR"/>
        <result column="header_img_url" property="headerImgUrl" jdbcType="VARCHAR"/>
        <result column="ad_img_url" property="adImgUrl" jdbcType="VARCHAR"/>
        <result column="content_img" property="contentImg" jdbcType="VARCHAR"/>
        <result column="media_url" property="mediaUrl" jdbcType="VARCHAR"/>
        <result column="media_type" property="mediaType" jdbcType="CHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="order_count" property="orderCount" jdbcType="INTEGER"/>
        <result column="left_point" property="leftPoint" jdbcType="INTEGER"/>
        <result column="extend_info" property="extendInfo" jdbcType="VARCHAR"/>
        <result column="read_count" property="readCount" jdbcType="INTEGER"/>
        <result column="read_count_score" property="readCountScore" jdbcType="DECIMAL"/>
        <result column="orderlist_url" property="orderlistUrl" jdbcType="VARCHAR"/>
        <result column="push_flow_time" property="pushFlowTime" jdbcType="TIMESTAMP"/>

        <result column="channel_id" jdbcType="INTEGER" property="channelId" />
        <result column="is_free" jdbcType="INTEGER" property="isFree" />
        <result column="is_separate_sale" jdbcType="INTEGER" property="isSeparateSale" />
        <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
        <result column="free_vip_type" jdbcType="VARCHAR" property="freeVipType" />
        <result column="channel_preview_duration" jdbcType="INTEGER" property="channelPreviewDuration" />
        <result column="channel_order" jdbcType="INTEGER" property="channelOrder" />
        <result column="separate_sale_preview_duration" jdbcType="INTEGER" property="separateSalePreviewDuration" />
        <result column="channel_list_url" jdbcType="VARCHAR" property="channelListUrl" />
        <result column="share_status" jdbcType="INTEGER" property="shareStatus" />
    </resultMap>

    <sql id="Base_Column_List">
        id,read_count_score,id,article_title,player_img,vertical_player_img,preheat_url,img_url,header_img_url,ad_img_url,content_img,img_url,media_url,media_type,start_time,end_time,order_count,left_point,status,extend_info,orderlist_url, duration,push_flow_time,
    channel_id, is_free, is_separate_sale, sku_id, free_vip_type, channel_preview_duration,
    channel_order, separate_sale_preview_duration,channel_list_url,display_resource_position,share_status
    </sql>

    <sql id="Live_Extend_Column_List">
        td.read_count,read_count_score,live.id,live.article_title,live.player_img,live.vertical_player_img,live.preheat_url,live.img_url,live.header_img_url,live.ad_img_url,live.content_img,live.img_url,live.media_url,live.media_type,live.start_time,live.end_time,live.order_count,live.left_point,live.status,live.extend_info,orderlist_url,push_flow_time,
     channel_id, is_free, is_separate_sale, sku_id, free_vip_type, channel_preview_duration,
     channel_order, separate_sale_preview_duration,channel_list_url,share_status
    </sql>

    <select id="getLiveInfoBySkuId" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_fincir_media_live
        where sku_id = #{skuId,jdbcType=VARCHAR}
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_fincir_media_live
        where ID = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectLiveAndDataByPrimaryKey" resultMap="LiveAndDataResultMap" parameterType="String">
        select
        <include refid="Live_Extend_Column_List"/>
        from t_fincir_media_live live left join t_fincir_media_data td on live.id = td.target_id
        where live.id = #{id,jdbcType=VARCHAR}
    </select>

    <insert id="addLiveInfo" parameterType="map">
        INSERT into t_fincir_media_live
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="id != null and id.length() >0">
                id,
            </if>
            <if test="articleTitle != null and articleTitle.length() > 0">
                article_title,
            </if>
            <if test="playerImg != null and playerImg.length() >0">
                player_img,
            </if>
            <if test="verticalPlayerImg != null and verticalPlayerImg.length() >0">
                vertical_player_img,
            </if>
            <if test="preheatUrl != null and preheatUrl.length() >0">
                preheat_url,
            </if>
            <if test="contentImg != null and contentImg.length() >0">
                content_img,
            </if>

            <if test="imgUrl != null and imgUrl.length() >0">
                img_url,
            </if>
            <if test="headerImgUrl != null and headerImgUrl.length() >0">
                header_img_url,
            </if>
            <if test="adImgUrl != null and adImgUrl.length() >0">
                ad_img_url,
            </if>

            <if test="mediaUrl != null and mediaUrl.length() >0">
                media_url,
            </if>
            <if test="mediaType != null and mediaType.length() >0">
                media_type,
            </if>
            <if test="startTime != null">
                start_time,
            </if>

            <if test="endTime != null">
                end_time,
            </if>
            <if test="extendInfo != null and extendInfo.length() > 0">
                extend_info,
            </if>
            <if test="leftPoint != null">
                left_point,
            </if>
            <if test="readCountScore != null">
                read_count_score,
            </if>
            <if test="orderlistUrl != null and orderlistUrl.length() >0">
                orderlist_url,
            </if>

            <if test="channelId != null">
                channel_id,
            </if>
            <if test="isFree != null">
                is_free,
            </if>
            <if test="isSeparateSale != null">
                is_separate_sale,
            </if>
            <if test="skuId != null">
                sku_id,
            </if>
            <if test="freeVipType != null">
                free_vip_type,
            </if>
            <if test="channelPreviewDuration != null">
                channel_preview_duration,
            </if>
            <if test="channelOrder != null">
                channel_order,
            </if>
            <if test="separateSalePreviewDuration != null">
                separate_sale_preview_duration,
            </if>
            <if test="channelListUrl != null">
                channel_list_url,
            </if>
            <if test="displayResourcePosition != null">
                display_resource_position,
            </if>

            status,order_count
        </trim>
        <trim prefix="values(" suffix=")" suffixOverrides=",">
            <if test="id != null and id.length() >0">
                #{id},
            </if>
            <if test="articleTitle != null and articleTitle.length() > 0">
                #{articleTitle},
            </if>
            <if test="playerImg != null and playerImg.length() >0">
                #{playerImg},
            </if>
            <if test="verticalPlayerImg != null and verticalPlayerImg.length() >0">
                #{verticalPlayerImg},
            </if>
            <if test="preheatUrl != null and preheatUrl.length() >0">
                #{preheatUrl},
            </if>
            <if test="contentImg != null and contentImg.length() >0">
                #{contentImg},
            </if>

            <if test="imgUrl != null and imgUrl.length() >0">
                #{imgUrl},
            </if>

            <if test="headerImgUrl != null and headerImgUrl.length() >0">
                #{headerImgUrl},
            </if>
            <if test="adImgUrl != null and adImgUrl.length() >0">
                #{adImgUrl},
            </if>

            <if test="mediaUrl != null and mediaUrl.length() >0">
                #{mediaUrl},
            </if>
            <if test="mediaType != null and mediaType.length() >0">
                #{mediaType},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>

            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="extendInfo != null and extendInfo.length() > 0">
                #{extendInfo},
            </if>
            <if test="leftPoint != null">
                #{leftPoint},
            </if>
            <if test="readCountScore != null">
                #{readCountScore},
            </if>
            <if test="orderlistUrl != null and orderlistUrl.length() >0">
                #{orderlistUrl},
            </if>

            <if test="channelId != null">
                #{channelId,jdbcType=INTEGER},
            </if>
            <if test="isFree != null">
                #{isFree,jdbcType=INTEGER},
            </if>
            <if test="isSeparateSale != null">
                #{isSeparateSale,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=VARCHAR},
            </if>
            <if test="freeVipType != null">
                #{freeVipType,jdbcType=VARCHAR},
            </if>
            <if test="channelPreviewDuration != null">
                #{channelPreviewDuration,jdbcType=INTEGER},
            </if>
            <if test="channelOrder != null">
                #{channelOrder,jdbcType=INTEGER},
            </if>
            <if test="separateSalePreviewDuration != null">
                #{separateSalePreviewDuration,jdbcType=INTEGER},
            </if>
            <if test="channelListUrl != null">
                #{channelListUrl,jdbcType=VARCHAR},
            </if>
            <if test="displayResourcePosition != null">
                #{displayResourcePosition,jdbcType=INTEGER},
            </if>
            0,0
        </trim>
    </insert>

    <update id="updateLiveInfo" parameterType="map">

        update t_fincir_media_live
        <set>

            <if test="articleTitle != null and articleTitle.length() > 0">
                article_title = #{articleTitle},
            </if>
            <if test="playerImg != null">
                player_img = #{playerImg},
            </if>
            <if test="verticalPlayerImg != null">
                vertical_player_img = #{verticalPlayerImg},
            </if>
            <if test="preheatUrl != null">
                preheat_url = #{preheatUrl},
            </if>
            <if test="contentImg != null">
                content_img = #{contentImg},
            </if>

            <if test="imgUrl != null and imgUrl.length() >0">
                img_url = #{imgUrl},
            </if>

            <if test="headerImgUrl != null and headerImgUrl.length() >0">
                header_img_url = #{headerImgUrl},
            </if>
            <if test="adImgUrl != null and adImgUrl.length() >0">
                ad_img_url = #{adImgUrl},
            </if>

            <if test="mediaUrl != null and mediaUrl.length() >0">
                media_url = #{mediaUrl},
            </if>
            <if test="mediaType != null and mediaType.length() >0">
                media_type = #{mediaType},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>

            <if test="endTime != null">
                end_time = #{endTime},
            </if>

            <if test="status != null">
                status = #{status},
            </if>

            <if test="orderCount != null">
                order_count = #{orderCount},
            </if>

            <if test="leftPoint != null">
                left_point = #{leftPoint},
            </if>

            <if test="extendInfo != null">
                extend_info = #{extendInfo},
            </if>
            <if test="readCountScore != null">
                read_count_score = #{readCountScore},
            </if>
            <if test="orderlistUrl != null">
                orderlist_url = #{orderlistUrl},
            </if>
            <if test="pushFlowTime != null">
                push_flow_time = #{pushFlowTime},
            </if>

                channel_id = #{channelId,jdbcType=INTEGER},
                is_free = #{isFree,jdbcType=INTEGER},
            <if test="isSeparateSale != null">
                is_separate_sale = #{isSeparateSale,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=VARCHAR},
            </if>
                free_vip_type = #{freeVipType,jdbcType=VARCHAR},
                channel_preview_duration = #{channelPreviewDuration,jdbcType=INTEGER},
            <if test="channelOrder != null">
                channel_order = #{channelOrder,jdbcType=INTEGER},
            </if>
            <if test="separateSalePreviewDuration != null">
                separate_sale_preview_duration = #{separateSalePreviewDuration,jdbcType=INTEGER},
            </if>
            <if test="channelListUrl != null">
                channel_list_url = #{channelListUrl,jdbcType=VARCHAR},
            </if>
            <if test="shareStatus != null">
                share_status = #{shareStatus,jdbcType=INTEGER},
            </if>
            <if test="displayResourcePosition != null">
                display_resource_position = #{displayResourcePosition},
            </if>
        </set>
        where id = #{id}


    </update>

    <update id="updateDuration" parameterType="map">
        update t_fincir_media_live
        set duration = #{duration}
        where id = #{id}
    </update>
    <update id="updateChannelOrder" parameterType="map">
        update t_fincir_media_live
        set channel_order = channel_order + 1
        where channel_id = #{channelId}
    </update>

    <update id="updateChannelId" parameterType="map">
        update t_fincir_media_live
        set channel_id = null
        where id = #{id}
    </update>

    <update id="clearChannelId" parameterType="map">
        update t_fincir_media_live
        set channel_id = null, is_free = 0, channel_preview_duration = null
        where channel_id = #{channelId}
    </update>


    <!-- 查询所有数据 带阅读数的 -->
    <select id="getLiveListWithData" parameterType="map" resultMap="LiveAndDataResultMap">
        select
        <include refid="Live_Extend_Column_List"/>
        from t_fincir_media_live live left join t_fincir_media_data td on live.id = td.target_id
        <where>
            <include refid="sqlForPage"/>
            <if test="id != null and id.length() >0">
                and live.id = #{id}
            </if>
            <if test="articleTitle != null and articleTitle.length() >0">
                and live.article_title like concat('%',#{articleTitle},'%')
            </if>

        </where>
        order by live.start_time desc
        limit #{pageStart},#{rows}
    </select>


    <!-- 查询所有数据 -->
    <select id="geliveList" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_fincir_media_live
        <where>
            <include refid="sqlForPage"/>
        </where>
        order by start_time desc
        limit #{pageStart},#{rows}
    </select>

    <!-- 分页查询条件 -->
    <sql id="sqlForPage">
      status >= 0
    </sql>

    <select id="selectCount" resultType="Integer" parameterType="map">
        select count(1) from t_fincir_media_live
        <where>
            <include refid="sqlForPage"/>
            <if test="id != null and id.length() >0">
                and id = #{id}
            </if>
            <if test="articleTitle != null and articleTitle.length() >0">
                and article_title like concat('%',#{articleTitle},'%')
            </if>
        </where>
    </select>
    <select id="selectCountByChannelId" resultType="Integer">
        select count(1) from t_fincir_media_live
        <where>
            channel_id = #{channelId} and status > 0
        </where>
    </select>

    <select id="selectLiveListByChannelId" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from t_fincir_media_live
        <where>
            channel_id = #{channelId} and status > 0
            <if test=" id!= null and id.length() >0">
                and (id = #{id} or article_title like concat('%',#{id},'%'))
            </if>
        </where>
        ORDER BY
        CASE
            WHEN start_time &lt;= NOW() AND (end_time IS NULL OR end_time >= NOW()) THEN 0
            WHEN start_time > NOW() THEN 1
            ELSE 2
            END,
        CASE
            WHEN start_time &lt;= NOW() AND (end_time IS NULL OR end_time >= NOW()) THEN start_time END DESC,  -- 直播中按开播时间倒序
        CASE
            WHEN start_time > NOW() THEN start_time END ASC,  -- 即将开播按开播时间正序
        CASE
            WHEN start_time &lt;= NOW() AND (end_time IS NOT NULL AND end_time &lt; NOW()) THEN start_time END DESC  -- 已结束按开播时间倒序
    </select>

    <select id="getJoinActivityUsers" parameterType="string" resultType="com.creditease.ying.fincir.bean.UserActivityBean">
        select id as id,user_id as userId,mobile_no as mobileNo,activity_id as activityId,create_time as createTime
        from t_fincir_user_activity
        where activity_id = #{id}
    </select>

    <select id="getLiveListByParam" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_fincir_media_live
        <where>
            <if test="startTimeBegin != null">
                start_time BETWEEN #{startTimeBegin,jdbcType=TIMESTAMP} and DATE_ADD(#{startTimeBegin,jdbcType=TIMESTAMP},INTERVAL 5 MINUTE )
            </if>
        </where>
        order by start_time desc
    </select>

    <select id="selectNotStartedLiveList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_fincir_media_live
        <![CDATA[
            where start_time >= #{start}
            and start_time <= #{end}
            and push_flow_time is null
        ]]>
    </select>

    <select id="findRecentlyLiveMediaId"  resultType="java.lang.String">
        select
            id
        from
            t_fincir_media_live
        where
        status >0  and  start_time &lt;= now()  and  end_time  > now()- interval 15 minute ;
    </select>

    <update id="updatePushFlowTimeById" parameterType="map">
        update t_fincir_media_live
        set push_flow_time = #{pushFlowTime}
        where id = #{id}
    </update>

</mapper>
