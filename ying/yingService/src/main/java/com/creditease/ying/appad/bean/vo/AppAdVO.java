package com.creditease.ying.appad.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @createDate 01/03/2017 17:10
 */
@ApiModel(value = "推广位信息")
public class AppAdVO implements Serializable {

    @ApiModelProperty(value = "唯一标示")
    private String id;
    @ApiModelProperty(value = "推广位标题")
    private String title;
    @ApiModelProperty(value = "推广位描述信息")
    private String adDesc;
    @ApiModelProperty(value = "推广位打开方式，1: APP内跳转，2: app外跳转，3：Native打开")
    private Integer goType;
    @ApiModelProperty(value = "推广位展示图片")
    private String showPic;
    @ApiModelProperty(value = "推广位备注（*表示红点，文字表示文字，null或空字符串表示右上角不展示）")
    private String tag;
    @ApiModelProperty(value = "最低版本")
    private String minVersion;
    @ApiModelProperty(value = "最高版本")
    private String maxVersion;
    @ApiModelProperty(value = "推广位跳转地址")
    private String goUrl;
    @ApiModelProperty(value = "推广位展示位置")
    private String showPosition;
    @ApiModelProperty(value = "是否浮窗")
    private boolean needFloatWindow;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAdDesc() {
        return adDesc;
    }

    public void setAdDesc(String adDesc) {
        this.adDesc = adDesc;
    }

    public Integer getGoType() {
        return goType;
    }

    public void setGoType(Integer goType) {
        this.goType = goType;
    }

    public String getShowPic() {
        return showPic;
    }

    public void setShowPic(String showPic) {
        this.showPic = showPic;
    }

    public String getGoUrl() {
        return goUrl;
    }

    public void setGoUrl(String goUrl) {
        this.goUrl = goUrl;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getShowPosition() {
        return showPosition;
    }

    public void setShowPosition(String showPosition) {
        this.showPosition = showPosition;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getMinVersion() {
        return minVersion;
    }

    public void setMinVersion(String minVersion) {
        this.minVersion = minVersion;
    }

    public String getMaxVersion() {
        return maxVersion;
    }

    public void setMaxVersion(String maxVersion) {
        this.maxVersion = maxVersion;
    }

    public boolean isNeedFloatWindow() {
        return needFloatWindow;
    }

    public void setNeedFloatWindow(boolean needFloatWindow) {
        this.needFloatWindow = needFloatWindow;
    }
}
