package com.yrjk.life.api.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.yrjk.life.core.model.welfare.LedgerAccountPageResp;
import com.yrjk.life.core.model.welfare.platform.common.WelfarePlatformBaseResp;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yrjk.life.core.service.impl.AbstractReconciliationProcessor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收入流水对账数据处理器
 *
 * <AUTHOR>
 * @date 2025-11-05
 */
@Slf4j
@Component
public class LedgerIncomeAccountingJob extends AbstractReconciliationProcessor {

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /**
     * XXL-JOB 任务入口
     */
    @XxlJob("ledgerIncomeAccountingJobHandler")
    public void executeJob() {
        super.process();
    }

    @Override
    protected LedgerAccountParams initParams() {
        JobParam jobParam = parseJobParam();
        String batchDate = LocalDate.now().format(DATE_FORMAT);
        return new LedgerAccountParams(jobParam.getStartDate(), jobParam.getEndDate(), batchDate, jobParam.getStoreIds());
    }

    @Override
    protected PageQueryResult queryPageData(LocalDate startDate, LocalDate endDate, String storeIds,
                                            Integer pageNum, Integer pageSize) {
        WelfarePlatformBaseResp<LedgerAccountPageResp> response =
                welfareWrapper.queryPendingIncome(startDate,endDate, storeIds, pageNum, pageSize);

        if (response == null || response.getContext() == null) {
            return null;
        }

        LedgerAccountPageResp pageResp = response.getContext();
        return new PageQueryResult(pageResp.getRecords(), pageResp.getHasNext());
    }


    private JobParam parseJobParam() {
        String param = XxlJobHelper.getJobParam();
        LocalDate defaultDate = LocalDate.now().minusDays(1);
        LocalDate startDate = defaultDate;
        LocalDate endDate = defaultDate;
        String storeIds = null;

        if (StringUtils.isNotBlank(param)) {
            Map<String, String> kv = Arrays.stream(param.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .map(item -> item.split("=", 2))
                    .filter(arr -> arr.length == 2)
                    .collect(Collectors.toMap(arr -> arr[0].trim(), arr -> arr[1].trim(), (a, b) -> b));

            if (kv.containsKey("startDate")) {
                startDate = LocalDate.parse(kv.get("startDate"), DATE_FORMAT);
            }
            if (kv.containsKey("endDate")) {
                endDate = LocalDate.parse(kv.get("endDate"), DATE_FORMAT);
            }
            if (endDate.isBefore(startDate)) {
                throw new IllegalArgumentException("endDate must be >= startDate");
            }
            storeIds = kv.get("storeIds");
        }

        log.info("使用任务参数 - 开始日期:{}, 结束日期:{}, 店铺:{}", startDate, endDate, storeIds);
        return new JobParam(startDate, endDate, storeIds);
    }

    @Getter
    private static class JobParam {
        private final LocalDate startDate;
        private final LocalDate endDate;
        private final String storeIds;

        private JobParam(LocalDate startDate, LocalDate endDate, String storeIds) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.storeIds = storeIds;
        }
    }
}