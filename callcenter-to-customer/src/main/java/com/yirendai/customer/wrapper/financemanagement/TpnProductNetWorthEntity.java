package com.yirendai.customer.wrapper.financemanagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TpnProductNetWorthEntity {
    /**
     * 净值日期
     */
    @ApiModelProperty(value = "净值日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime netWorthDate;

    /**
     * 单位净值
     */
    @ApiModelProperty(value = "单位净值")
    private String unitNetWorth;

    /**
     * 累计净值
     */
    @ApiModelProperty(value = "累计净值")
    private String cumulativeNetWorth;
}
