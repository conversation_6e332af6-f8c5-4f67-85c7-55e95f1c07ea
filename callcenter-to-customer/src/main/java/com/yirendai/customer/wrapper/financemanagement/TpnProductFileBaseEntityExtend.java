package com.yirendai.customer.wrapper.financemanagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TpnProductFileBaseEntityExtend {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long pkid;

    /**
     * 一级产品编号
     */
    @ApiModelProperty(value = "一级产品编号")
    private String productId;

    /**
     * 附件类型 1:基金合同 2:披露报告 3:产品介绍 4:其它
     */
    @ApiModelProperty(value = "附件类型 1:基金合同 2:披露报告 3:产品介绍 4:其它")
    private String type;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String name;

    /**
     * 文件path
     */
    @ApiModelProperty(value = "文件path")
    private String path;

    /**
     * file_id
     */
    @ApiModelProperty(value = "file_id")
    private String fileId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String operator;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

    /**
     * 系统状态
     */
    @ApiModelProperty(value = "系统状态")
    private String sysState;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 附件显示名称
     */
    @ApiModelProperty(value = "附件显示名称")
    private String attachmentName;
}
