package com.yirendai.customer.wrapper.financemanagement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yirendai.app.fortune.common.model.DataModel;
import com.yirendai.customer.constant.enums.FinanceProductBusinessError;
import com.yirendai.customer.constant.exception.BusinessException;
import com.yirendai.customer.vo.req.FinanceProductDetailReq;
import com.yirendai.customer.vo.res.FinanceProductDetailRes;
import com.yirendai.customer.wrapper.WrapperUtils;
import com.yirendai.workbench.util.JsonUtilExt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 资管接口封装
 */
@Slf4j
@Service
public class FinanceManagementWrapper {
    @Autowired
    private WrapperUtils wrapperUtils;

    @Value("${finance-management.base-url:http://easy-go.easy-go.paas.test}")
    private String financeManagementBaseUrl;

    /**
     * 获取私募证券二级产品详情
     * @param pkid 二级产品主键ID
     * @param productSaleNameDesc 产品销售代号
     */
    public R<TwoPrivateSecurityFundStandardEntityExtend> getPrivateFundDetail(Long pkid, String productSaleNameDesc) {
        if (pkid == null && StringUtils.isBlank(productSaleNameDesc)) {
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        if (pkid != null) {
            params.put("pkid", pkid);
        } else {
            params.put("productSaleNameDesc",  productSaleNameDesc);
        }
        String url = financeManagementBaseUrl + "/tpnProductBase/anon/twoPrivateSecurityDetail";
        String paramJson = JSON.toJSONString(params);
        log.info("查询私募证劵二级产品详情开始, url:{}, params:{}", url, paramJson);
        try {
            ResponseEntity<String> response = wrapperUtils.postJson(url, paramJson);
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("查询私募证劵二级产品详情返回码异常, status:{}, body:{}", response.getStatusCode(), response.getBody());
                throw new BusinessException(FinanceProductBusinessError.GET_FINANCE_PRODUCT_DETAIL_FAIL);
            }
            String body = response.getBody();
            log.info("查询私募证券二级产品详情成功, url:{}, body:{}", url, body);
            return JSON.parseObject(body, new TypeReference<R<TwoPrivateSecurityFundStandardEntityExtend>>() {});
        } catch (Exception e) {
            log.error("查询私募证劵二级产品详情异常, url:{}, params:{}", url, paramJson, e);
            throw new BusinessException(FinanceProductBusinessError.GET_FINANCE_PRODUCT_DETAIL_FAIL);
        }
    }


    public FinanceProductDetailRes convertToFinanceProductDetailReq(TwoPrivateSecurityFundStandardEntityExtend entityExtend) {
        LocalDate netWorthDate = Optional.ofNullable(entityExtend.getNetWorthDate()).map(t -> Instant.ofEpochMilli(t)
                        .atZone(ZoneId.systemDefault()).toLocalDate()).orElse(null);

        LocalDate subscriptionOpenDay = Optional.ofNullable(entityExtend.getSubscriptionOpenDay()).map(t -> Instant.ofEpochMilli(t)
                .atZone(ZoneId.systemDefault()).toLocalDate()).orElse(null);

        LocalDate cutoffTime = Optional.ofNullable(entityExtend.getCutoffTime()).map(t -> Instant.ofEpochMilli(t)
                .atZone(ZoneId.systemDefault()).toLocalDate()).orElse(null);

        LocalDate redemptionOpenDay = Optional.ofNullable(entityExtend.getRedemptionOpenDay()).map(t -> Instant.ofEpochMilli(t)
                .atZone(ZoneId.systemDefault()).toLocalDate()).orElse(null);

        LocalDate redemptionPeriodStart = Optional.ofNullable(entityExtend.getRedemptionPeriodStart()).map(t -> Instant.ofEpochMilli(t)
                .atZone(ZoneId.systemDefault()).toLocalDate()).orElse(null);

        LocalDate redemptionPeriodEnd = Optional.ofNullable(entityExtend.getRedemptionPeriodEnd()).map(t -> Instant.ofEpochMilli(t)
                .atZone(ZoneId.systemDefault()).toLocalDate()).orElse(null);

        return FinanceProductDetailRes.builder()
                .productSaleNameDesc(entityExtend.getProductSaleNameDesc())
                .netWorthDate(netWorthDate)
                .subscriptionOpenDay(subscriptionOpenDay)
                .cutoffTime(cutoffTime)
                .redemptionOpenDay(redemptionOpenDay)
                .redemptionPeriodStart(redemptionPeriodStart)
                .redemptionPeriodEnd(redemptionPeriodEnd)
                .investmentStrategy(entityExtend.getInvestmentStrategy())
                .productFeature(entityExtend.getProductFeature())
                .recommend(entityExtend.getRecommend())
                .riskDesc(entityExtend.getRiskDesc())
                .shareLockUpPeriod(entityExtend.getShareLockUpPeriod())
                .threshold(entityExtend.getThreshold())
                .build();
    }



}
