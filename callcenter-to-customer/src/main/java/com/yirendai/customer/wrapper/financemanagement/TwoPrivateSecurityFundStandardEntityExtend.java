package com.yirendai.customer.wrapper.financemanagement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TwoPrivateSecurityFundStandardEntityExtend {
    @ApiModelProperty(value = "最新净值日期, 时间戳")
    private Long netWorthDate;

    @ApiModelProperty(value = "单位净值")
    private String unitNetWorth;

    @ApiModelProperty(value = "近1年收益率")
    private String pastYearReturn;

    @ApiModelProperty(value = "净值走势")
    private List<TpnProductNetWorthEntity> netWorthEntityList;

    @ApiModelProperty(value = "成立以来最大回撤")
    private String maxDrawdown;

    @ApiModelProperty(value = "申购开放日, 时间戳")
    private Long subscriptionOpenDay;

    @ApiModelProperty(value = "打款截止时间.申购开放日-2")
    private Long cutoffTime;

    @ApiModelProperty(value = "赎回开放日")
    private Long redemptionOpenDay;

    @ApiModelProperty(value = "赎回窗口期开始时间")
    private Long redemptionPeriodStart;

    @ApiModelProperty(value = "赎回窗口期结束时间")
    private Long redemptionPeriodEnd;

    @ApiModelProperty(value = "文件集合")
    private List<TpnProductFileBaseEntityExtend> productFileBaseList;

    @ApiModelProperty(value = "主键")
    private Long pkid;

    @ApiModelProperty(value = "一级产品编号")
    private String productId;

    @ApiModelProperty(value = "基金管理人ID。机构管理-合作方")
    private Long fundManagerId;

    @ApiModelProperty(value = "基金管理人")
    private String fundManager;

    @ApiModelProperty(value = "托管行ID")
    private Long custodianBankId;

    @ApiModelProperty(value = "托管行")
    private String custodianBank;

    @ApiModelProperty(value = "产品销售名称")
    private String productSaleName;

    @ApiModelProperty(value = "产品销售代号")
    private String productSaleNameDesc;

    @ApiModelProperty(value = "销售渠道")
    private String saleChannel;

    @ApiModelProperty(value = "投资策略")
    private String investmentStrategy;

    @ApiModelProperty(value = "产品发布状态 1:预热 2:发布 3:结束")
    private String productStatus;

    @ApiModelProperty(value = "门槛")
    private String threshold;

    @ApiModelProperty(value = "份额锁定期")
    private String shareLockUpPeriod;

    @ApiModelProperty(value = "佣金折算系数")
    private String commissionFactor;

    @ApiModelProperty(value = "业绩折算系数")
    private String performanceFactor;

    @ApiModelProperty(value = "产品特点")
    private String productFeature;

    @ApiModelProperty(value = "推荐语")
    private String recommend;

    @ApiModelProperty(value = "认购渠道 1:“JC”微信小程序 2:FD私募通APP、3:“TL投资管理”微信小程序、4:妥私募APP")
    private String subscriptionChannel;

    @ApiModelProperty(value = "打款截止时间描述")
    private String cutoffTimeDesc;

    @ApiModelProperty(value = "赎回窗口期描述")
    private String redemptionPeriodDesc;

    @ApiModelProperty(value = "风险提示")
    private String riskDesc;
}
