package com.yirendai.customer.constant.enums;

import com.yirendai.customer.constant.exception.BusinessException;
import org.springblade.core.tool.api.IResultCode;

public interface IBusinessError extends IResultCode {

    /** 抛出业务异常 */
    default void throwBusinessException() {
        throw new BusinessException(this.getCode(), this.getMessage());
    }

    /** 抛出业务异常（带附加消息） */
    default void throwBusinessException(String appendMsg) {
        throw new BusinessException(this, appendMsg);
    }

    /** 抛出业务异常（带异常原因） */
    default void throwBusinessException(Throwable throwable) {
        throw new BusinessException(this, throwable);
    }

    /** 抛出业务异常（带附加消息和异常原因） */
    default void throwBusinessException(String appendMsg, Throwable throwable) {
        throw new BusinessException(this, appendMsg, throwable);
    }
}
