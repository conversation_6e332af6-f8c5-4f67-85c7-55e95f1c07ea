package com.yirendai.customer.constant.enums;


/**
 * 分享链接业务异常枚举
 *
 * <AUTHOR>
 */
public enum ShareLinkBusinessError implements IBusinessError {

    /**
     * 链接ID不能为空
     */
    LINK_ID_IS_NULL(5001, "链接ID不能为空"),

    /**
     * 分享链接不存在
     */
    SHARE_LINK_NOT_EXIST(5002, "分享链接不存在或已被删除"),

    /**
     * 用户token错误
     */
    USER_TOKEN_ERROR(5003, "用户信息错误"),

    ;

    private final int code;
    private final String message;

    ShareLinkBusinessError(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}