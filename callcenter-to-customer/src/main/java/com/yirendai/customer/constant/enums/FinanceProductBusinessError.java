package com.yirendai.customer.constant.enums;

public enum FinanceProductBusinessError implements IBusinessError {
    /**
     * 获取私募证券二级产品详情失败
     */
    GET_FINANCE_PRODUCT_DETAIL_FAIL(6001, "链接ID不能为空"),;

    private final int code;
    private final String message;

    FinanceProductBusinessError(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public int getCode() {
        return code;
    }
}
