package com.yirendai.customer.controller;

import com.yirendai.customer.vo.req.FinanceProductDetailReq;
import com.yirendai.customer.vo.res.FinanceProductDetailRes;
import com.yirendai.customer.wrapper.financemanagement.FinanceManagementWrapper;
import com.yirendai.customer.wrapper.financemanagement.TwoPrivateSecurityFundStandardEntityExtend;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/finance-product")
@Api(value = "资管产品", tags = "资管产品")
@CrossOrigin(
        origins = "*",
        allowedHeaders = "*",
        methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.OPTIONS},
        maxAge = 3600
)
public class FinanceProductController {

    @Autowired
    private FinanceManagementWrapper financeManagementWrapper;

    @RequestMapping(value = "/detail", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation(value = "获取私募产品详情", notes = "获取私募产品详情")
    public R<FinanceProductDetailRes> detail(@RequestBody FinanceProductDetailReq request) {
        TwoPrivateSecurityFundStandardEntityExtend result = financeManagementWrapper.getPrivateFundDetail(request.getPkid(), request.getProductSaleNameDesc()).getData();
        return R.data(financeManagementWrapper.convertToFinanceProductDetailReq(result));
    }
}
