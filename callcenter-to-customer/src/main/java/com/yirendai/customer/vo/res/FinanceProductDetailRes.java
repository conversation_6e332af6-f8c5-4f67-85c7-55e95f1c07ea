package com.yirendai.customer.vo.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 私募证券二级产品详情Response
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FinanceProductDetailRes {

    @ApiModelProperty(value = "产品销售代号")
    private String productSaleNameDesc;

    @ApiModelProperty(value = "最新净值日期")
    @JsonFormat(pattern = "MM-dd", timezone = "GMT+8")
    private LocalDate netWorthDate;

    @ApiModelProperty(value = "申购开放日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate subscriptionOpenDay;

    @ApiModelProperty(value = "打款截止时间.申购开放日-2")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate cutoffTime;

    @ApiModelProperty(value = "赎回开放日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate redemptionOpenDay;

    @ApiModelProperty(value = "赎回窗口期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate redemptionPeriodStart;

    @ApiModelProperty(value = "赎回窗口期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate redemptionPeriodEnd;

    @ApiModelProperty(value = "投资策略")
    private String investmentStrategy;

    @ApiModelProperty(value = "产品特点")
    private String productFeature;

    @ApiModelProperty(value = "推荐语")
    private String recommend;

    @ApiModelProperty(value = "风险提示")
    private String riskDesc;

    @ApiModelProperty(value = "份额锁定期")
    private String shareLockUpPeriod;

    @ApiModelProperty(value = "门槛")
    private String threshold;
}
