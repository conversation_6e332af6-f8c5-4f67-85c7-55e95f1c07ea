package com.yrjk.life.core.model.welfare;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yrjk.life.core.model.ledger.LedgerAccountingRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 收入流水分页响应
 */
@Data
public class LedgerAccountPageResp implements Serializable {
    
    private String queryDate;
    private List<LedgerAccountingRecord> records;
    private Integer currentCount;
    private Integer totalCount;
    private Integer totalPages;
    private Integer currentPage;
    private Integer pageSize;
    private Boolean hasNext;

}