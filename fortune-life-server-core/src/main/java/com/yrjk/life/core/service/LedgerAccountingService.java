// LedgerAccountingService.java
package com.yrjk.life.core.service;

import com.yrjk.life.core.model.ledger.LedgerAccountingRecord;
import com.yrjk.life.core.model.ledger.LedgerBatchRequest;
import com.yrjk.life.core.model.ledger.LedgerBatchResponse;

import java.util.List;

/**
 * Ledger账务接口服务
 * <AUTHOR>
 * @date 2025-11-05
 */
public interface LedgerAccountingService {
    
    /**
     * 批量记账 - 生成文件并提交
     * @param systemFlag 系统标识
     * @param batchDate 批次日期
     * @param records 记账记录列表
     * @return 响应结果
     */
    LedgerBatchResponse batchAccounting(String systemFlag, String batchDate, List<LedgerAccountingRecord> records);
    
    /**
     * 生成批量记账文件
     * @param records 记账记录列表
     * @param filePath 文件路径
     * @return 文件名
     */
    String generateBatchFile(List<LedgerAccountingRecord> records, String filePath);
    
    /**
     * 提交批量记账请求
     * @param request 请求报文
     * @return 响应报文
     */
    LedgerBatchResponse submitBatchRequest(LedgerBatchRequest request);
    
    /**
     * 处理错误文件
     * @param errorFilePath 错误文件路径
     * @return 错误记录列表
     */
    List<LedgerAccountingRecord> processErrorFile(String errorFilePath);
}