package com.yrjk.life.core.service.impl;

import com.yrjk.life.core.client.LedgerWebServiceClient;
import com.yrjk.life.core.exception.BusinessException;
import com.yrjk.life.core.model.ledger.LedgerAccountingRecord;
import com.yrjk.life.core.model.ledger.LedgerBatchRequest;
import com.yrjk.life.core.model.ledger.LedgerBatchResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Ledger账务接口服务实现 - 流式批量记账
 *
 * <AUTHOR>
 * @date 2025-11-05
 */
@Slf4j
@Service
public class LedgerAccountingServiceImpl {

    /** 字段分隔符 ASCII码2 */
    private static final String FIELD_SEPARATOR = "\u0002";

    /** 文件编码 GBK */
    private static final String FILE_ENCODING = "GBK";

    /** 错误码 */
    private static final String ERROR_CODE_PARAM = "LE001";
    private static final String ERROR_CODE_FILE = "LE002";
    private static final String ERROR_CODE_INVOKE = "LE003";

    /** Redis Key 前缀 */
    private static final String REDIS_KEY_PREFIX = "ledger:batchNo:";

    @Value("${ledger.nas.basePath:/data/ledger}")
    private String nasBasePath;

    @Value("${ledger.system.flag:YXH}")
    private String systemFlag;

    /** 批次号长度 */
    private static final int BATCH_NO_LENGTH = 6;

    @Resource
    private LedgerWebServiceClient ledgerWebServiceClient;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 创建批次文件写入器（流式写入）
     *
     * @param systemFlag 系统标识
     * @param batchDate 批次日期 yyyy-MM-dd
     * @return 批次文件写入器
     */
    public BatchFileWriter createBatchFileWriter(String systemFlag, String batchDate) {
        log.info("创建批次文件写入器, systemFlag={}, batchDate={}", systemFlag, batchDate);

        try {
            // 校验参数
            validateBatchDate(batchDate);

            // 生成批次号
            String batchNo = generateBatchNo(systemFlag, batchDate);

            // 构建文件路径
            String dateDir = batchDate.replaceAll("-", "");
            String fileDir = nasBasePath + "/" + systemFlag + "/" + dateDir + "/";
            String fileName = systemFlag + dateDir + batchNo + ".i";
            String fullFilePath = fileDir + fileName;

            // 创建目录
            File file = new File(fullFilePath);
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                log.info("创建目录: {}, 结果: {}", parentDir.getAbsolutePath(), created);
            }

            // 创建文件写入器
            BatchFileWriter writer = new BatchFileWriter(
                    fullFilePath,
                    systemFlag,
                    batchDate,
                    batchNo,
                    fileDir,
                    fileName
            );

            log.info("批次文件写入器创建成功, batchNo={}, filePath={}", batchNo, fullFilePath);
            return writer;

        } catch (Exception e) {
            log.error("创建批次文件写入器失败", e);
            throw new BusinessException(ERROR_CODE_FILE, "创建批次文件写入器失败: " + e.getMessage());
        }
    }

    /**
     * 提交批量记账请求（基于已生成的文件）
     *
     * @param writer 批次文件写入器
     * @return 批量记账响应
     */
    public LedgerBatchResponse submitBatchFileRequest(BatchFileWriter writer) {
        log.info("提交批量记账请求, batchNo={}, totalCount={}", writer.getBatchNo(), writer.getTotalCount());
        try {
            // 1. 生成trigger文件
            String triggerFilePath = writer.getFileDir() + writer.getFileName() + ".trigger";
            generateTriggerFile(triggerFilePath);
            // 2. 构建请求报文
            LedgerBatchRequest request = new LedgerBatchRequest();
            request.setSystemFlag(this.systemFlag);
            request.setContSystemFlag(writer.getSystemFlag());
            request.setBatchDate(writer.getBatchDate());
            request.setBatchNo(writer.getBatchNo());
            request.setFileDir(writer.getFileDir());
            request.setFileName(writer.getFileName());
            request.setTotalCnt(writer.getTotalCount());
            // 3. 调用WebService接口
            LedgerBatchResponse response = ledgerWebServiceClient.invokeBatchAccounting(request);

            log.info("批量记账请求提交完成, batchNo={}, rspCode={}, rspMsg={}",
                    writer.getBatchNo(), response.getRspCode(), response.getRspMsg());
            return response;

        } catch (Exception e) {
            log.error("提交批量记账请求失败, batchNo={}", writer.getBatchNo(), e);
            throw new BusinessException(ERROR_CODE_INVOKE, "提交批量记账请求失败: " + e.getMessage());
        }
    }

    /**
     * 生成trigger文件
     */
    private void generateTriggerFile(String triggerFilePath) {
        try {
            File triggerFile = new File(triggerFilePath);
            File parentDir = triggerFile.getParentFile();

            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }

            boolean created = triggerFile.createNewFile();
            log.info("trigger文件生成{}, path={}", created ? "成功" : "已存在", triggerFilePath);

        } catch (IOException e) {
            log.error("生成trigger文件失败, path={}", triggerFilePath, e);
            throw new BusinessException(ERROR_CODE_FILE, "生成trigger文件失败: " + e.getMessage());
        }
    }

    /**
     * 生成批次号（格式: HHmmss）
     */
    /**
     * 生成批次号（基于Redis自增，保证唯一性）
     *
     * 格式: 6位数字，从 000001 开始，每天重置
     * Redis Key: ledger:batchNo:{systemFlag}:{date}
     *
     * 示例:
     * - 第1个批次: 000001
     * - 第2个批次: 000002
     * - ...
     * - 第999999个批次: 999999
     *
     * @param systemFlag 系统标识
     * @param batchDate 批次日期 yyyy-MM-dd
     * @return 6位批次号
     */
    private String generateBatchNo(String systemFlag, String batchDate) {
        try {
            // 构建Redis Key: ledger:batchNo:YYX:2025-11-05
            String redisKey = REDIS_KEY_PREFIX + systemFlag + ":" + batchDate;

            // Redis 自增
            Long sequence = redisTemplate.opsForValue().increment(redisKey, 1);

            if (sequence == null) {
                throw new BusinessException(ERROR_CODE_INVOKE, "Redis自增失败，返回null");
            }

            // 设置过期时间（3天，避免Redis堆积）
            redisTemplate.expire(redisKey, 3, TimeUnit.DAYS);

            // 格式化为6位数字（左补0）
            String batchNo = String.format("%0" + BATCH_NO_LENGTH + "d", sequence);

            // 防止溢出（超过6位）
            if (batchNo.length() > BATCH_NO_LENGTH) {
                log.error("批次号溢出, sequence={}, batchNo={}", sequence, batchNo);
                throw new BusinessException(ERROR_CODE_INVOKE, "批次号已达到最大值，请检查系统配置");
            }

            log.debug("生成批次号成功, redisKey={}, sequence={}, batchNo={}", redisKey, sequence, batchNo);

            return batchNo;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成批次号异常", e);
            // 降级方案：使用时间戳
            return generateBatchNoFallback();
        }
    }

    /**
     * 批次号生成降级方案（当Redis不可用时）
     * 使用时间戳: HHmmss
     *
     * @return 6位批次号
     */
    private String generateBatchNoFallback() {
        log.warn("使用批次号降级方案（时间戳）");
        String timestamp = LocalDate.now()
                .atStartOfDay()
                .plusHours(java.time.LocalTime.now().getHour())
                .plusMinutes(java.time.LocalTime.now().getMinute())
                .plusSeconds(java.time.LocalTime.now().getSecond())
                .format(DateTimeFormatter.ofPattern("HHmmss"));
        return timestamp;
    }


    /**
     * 校验批次日期
     */
    private void validateBatchDate(String batchDate) {
        if (StringUtils.isBlank(batchDate)) {
            throw new BusinessException(ERROR_CODE_PARAM, "批次日期不能为空");
        }

        // 校验日期格式 yyyy-MM-dd
        if (!batchDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
            throw new BusinessException(ERROR_CODE_PARAM,
                    "批次日期格式错误，应为yyyy-MM-dd，实际: " + batchDate);
        }
    }

    /**
     * 空值处理
     */
    private String nvl(Object obj) {
        return obj == null ? "" : obj.toString().trim();
    }

    /**
     * 批次文件写入器（支持流式追加写入）
     * 使用 try-with-resources 自动管理资源
     */
    public static class BatchFileWriter implements AutoCloseable {

        private final String filePath;
        private final String systemFlag;
        private final String batchDate;
        private final String batchNo;
        private final String fileDir;
        private final String fileName;

        private BufferedWriter writer;
        private int totalCount = 0;
        private boolean headerWritten = false;
        private boolean closed = false;

        public BatchFileWriter(String filePath, String systemFlag, String batchDate,
                               String batchNo, String fileDir, String fileName) throws IOException {
            this.filePath = filePath;
            this.systemFlag = systemFlag;
            this.batchDate = batchDate;
            this.batchNo = batchNo;
            this.fileDir = fileDir;
            this.fileName = fileName;

            // 创建文件写入流（GBK编码）
            this.writer = new BufferedWriter(
                    new OutputStreamWriter(
                            new FileOutputStream(filePath),
                            Charset.forName("GBK")
                    )
            );
        }

        /**
         * 写入文件头（只写一次）
         */
        private void writeHeader() throws IOException {
            if (headerWritten) {
                return;
            }

            StringBuilder sb = new StringBuilder();
            sb.append(systemFlag).append(FIELD_SEPARATOR);
            sb.append(batchDate).append(FIELD_SEPARATOR);
            sb.append(batchNo).append(FIELD_SEPARATOR);
            sb.append("{totalCount}").append(FIELD_SEPARATOR); // 占位符，完成时替换

            writer.write(sb.toString());
            writer.newLine();
            writer.flush();

            headerWritten = true;
        }

        /**
         * 批量写入记录
         *
         * @param records 记账记录列表
         * @throws IOException IO异常
         */
        public void writeRecords(List<LedgerAccountingRecord> records) throws IOException {
            if (closed) {
                throw new IOException("文件写入器已关闭");
            }

            if (records == null || records.isEmpty()) {
                return;
            }

            // 第一次写入时，先写文件头
            if (!headerWritten) {
                writeHeader();
            }

            // 批量写入记录
            for (LedgerAccountingRecord record : records) {
                writeRecordInternal(record);
            }

            writer.flush();
        }

        /**
         * 写入单条记录
         *
         * @param record 记账记录
         * @throws IOException IO异常
         */
        public void writeRecord(LedgerAccountingRecord record) throws IOException {
            if (closed) {
                throw new IOException("文件写入器已关闭");
            }

            // 第一次写入时，先写文件头
            if (!headerWritten) {
                writeHeader();
            }

            writeRecordInternal(record);
            writer.flush();
        }

        /**
         * 内部写入记录方法
         */
        private void writeRecordInternal(LedgerAccountingRecord record) throws IOException {
            // 设置批次信息
            record.setContSystemFlag(systemFlag);
            record.setBatchNo(batchNo);
            // 构建并写入记录
            String line = buildFileBody(record);
            writer.write(line);
            writer.newLine();
            totalCount++;
        }

        /**
         * 完成写入并更新文件头的总笔数
         *
         * @throws IOException IO异常
         */
        public void complete() throws IOException {
            if (closed) {
                return;
            }

            // 关闭写入流
            if (writer != null) {
                writer.close();
                writer = null;
            }

            // 更新文件头中的总笔数
            updateTotalCountInHeader();

            closed = true;
        }

        /**
         * 更新文件头中的总笔数（替换占位符）
         */
        private void updateTotalCountInHeader() throws IOException {
            // 读取文件内容
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(new FileInputStream(filePath), Charset.forName("GBK")))) {

                String line;
                boolean firstLine = true;
                while ((line = reader.readLine()) != null) {
                    if (firstLine) {
                        // 替换占位符为实际总笔数
                        line = line.replace("{totalCount}", String.valueOf(totalCount));
                        firstLine = false;
                    }
                    content.append(line).append(System.lineSeparator());
                }
            }

            // 写回文件
            try (BufferedWriter writer = new BufferedWriter(
                    new OutputStreamWriter(new FileOutputStream(filePath), Charset.forName("GBK")))) {
                writer.write(content.toString());
            }
        }

        /**
         * 构建文件体（按照Ledger文档定义的25个字段）
         */
        private String buildFileBody(LedgerAccountingRecord record) {
            StringBuilder sb = new StringBuilder();

            // 1. 版本号
            sb.append(nvl(record.getVersNo())).append(FIELD_SEPARATOR);
            // 2. 系统标识
            sb.append(nvl(record.getContSystemFlag())).append(FIELD_SEPARATOR);
            // 3. 批次日期
            sb.append(nvl(record.getBatchDate())).append(FIELD_SEPARATOR);
            // 4. 批次号
            sb.append(nvl(record.getBatchNo())).append(FIELD_SEPARATOR);
            // 5. 业务日期
            sb.append(nvl(record.getBusiDate())).append(FIELD_SEPARATOR);
            // 6. 业务流水号
            sb.append(nvl(record.getBusiSerseqNo())).append(FIELD_SEPARATOR);
            // 7. 冲账标识
            sb.append(nvl(record.getCancelFlag())).append(FIELD_SEPARATOR);
            // 8. 订单号
            sb.append(nvl(record.getOrderNum())).append(FIELD_SEPARATOR);
            // 9. 交易码
            sb.append(nvl(record.getTranCode())).append(FIELD_SEPARATOR);
            // 10. 金额组数
            sb.append(nvl(record.getAmtGrp())).append(FIELD_SEPARATOR);
            // 11. 金额
            sb.append(nvl(record.getAmt())).append(FIELD_SEPARATOR);
            // 12. 参数值
            sb.append(nvl(record.getItem())).append(FIELD_SEPARATOR);
            // 13. 合同号1
            sb.append(nvl(record.getContractno1())).append(FIELD_SEPARATOR);
            // 14. 合同号2
            sb.append(nvl(record.getContractno2())).append(FIELD_SEPARATOR);
            // 15. 客户ID1
            sb.append(nvl(record.getCustomerId1())).append(FIELD_SEPARATOR);
            // 16. 客户ID2
            sb.append(nvl(record.getCustomerId2())).append(FIELD_SEPARATOR);
            // 17. 产品分类编码
            sb.append(nvl(record.getProductClass())).append(FIELD_SEPARATOR);
            // 18. 机构编码
            sb.append(nvl(record.getBrcCode())).append(FIELD_SEPARATOR);
            // 19. 冲销原业务日期
            sb.append(nvl(record.getReserve1())).append(FIELD_SEPARATOR);
            // 20. 冲销原业务流水号
            sb.append(nvl(record.getReserve2())).append(FIELD_SEPARATOR);
            // 21. 金额1
            sb.append(nvl(record.getAmount1())).append(FIELD_SEPARATOR);
            // 22. 金额2
            sb.append(nvl(record.getAmount2())).append(FIELD_SEPARATOR);
            // 23. 出借端产品分类编码
            sb.append(nvl(record.getDProductClass())).append(FIELD_SEPARATOR);
            // 24. 拆单标示
            sb.append(nvl(record.getOrdSplitFlag())).append(FIELD_SEPARATOR);
            // 25. 快付通标示
            sb.append(nvl(record.getIsEntAcctFlag())).append(FIELD_SEPARATOR);

            return sb.toString();
        }

        /**
         * 空值处理
         */
        private String nvl(Object obj) {
            return obj == null ? "" : obj.toString().trim();
        }

        @Override
        public void close() throws IOException {
            if (!closed && writer != null) {
                complete();
            }
        }

        // ==================== Getters ====================

        public String getSystemFlag() {
            return systemFlag;
        }

        public String getBatchDate() {
            return batchDate;
        }

        public String getBatchNo() {
            return batchNo;
        }

        public String getFileDir() {
            return fileDir;
        }

        public String getFileName() {
            return fileName;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public String getFilePath() {
            return filePath;
        }
    }
}