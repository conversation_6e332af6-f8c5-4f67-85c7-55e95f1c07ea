// LedgerAccountingRecord.java - 单笔记账记录
package com.yrjk.life.core.model.ledger;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Ledger记账记录 - 对应文件体
 */
@Data
public class LedgerAccountingRecord implements Serializable {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("版本号")
    private String versNo;

    @ApiModelProperty("系统标识")
    private String contSystemFlag;

    @ApiModelProperty("批次日期")
    private LocalDate batchDate;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("业务日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate busiDate;

    @ApiModelProperty("业务流水号")
    private String busiSerseqNo;

    @ApiModelProperty("冲账标识")
    private String cancelFlag;

    @ApiModelProperty("订单号")
    private String orderNum;

    @ApiModelProperty("交易码")
    private String tranCode;

    @ApiModelProperty("金额组数")
    private Integer amtGrp;

    @ApiModelProperty("金额串")
    private String amt;

    @ApiModelProperty("参数值")
    private String item;

    @ApiModelProperty("合同号1")
    private String contractno1;

    @ApiModelProperty("合同号2")
    private String contractno2;

    @ApiModelProperty("客户ID1")
    private String customerId1;

    @ApiModelProperty("客户ID2")
    private String customerId2;

    @ApiModelProperty("产品分类编码")
    private String productClass;

    @ApiModelProperty("机构编码")
    private String brcCode;

    @ApiModelProperty("冲销原业务日期")
    private String reserve1;

    @ApiModelProperty("冲销原业务流水号")
    private String reserve2;

    @ApiModelProperty("金额1")
    private BigDecimal amount1;

    @ApiModelProperty("金额2")
    private BigDecimal amount2;

    @ApiModelProperty("出借端产品分类编码")
    private String dProductClass;

    @ApiModelProperty("拆单标示")
    private String ordSplitFlag;

    @ApiModelProperty("快付通标示")
    private String isEntAcctFlag;

    @ApiModelProperty("业务ID")
    private String businessId;

    @ApiModelProperty("业务类型")
    private Integer businessType;
}