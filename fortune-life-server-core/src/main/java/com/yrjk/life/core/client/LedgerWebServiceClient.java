package com.yrjk.life.core.client;

import com.yrjk.life.core.exception.BusinessException;
import com.yrjk.life.core.model.ledger.LedgerBatchRequest;
import com.yrjk.life.core.model.ledger.LedgerBatchResponse;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.concurrent.TimeUnit;

/**
 * Ledger WebService客户端 - OkHttp实现
 * 服务名: tpbnsmag
 * 交易码: 333003
 * 编码: GBK
 * 接口形式: KEY-VALUE报文格式
 * <AUTHOR>
 * @date 2025-11-05
 */
@Slf4j
@Component
public class LedgerWebServiceClient {

    /** 报文编码 GBK */
    private static final String ENCODING = "GBK";

    /** 成功响应码 */
    private static final String SUCCESS_CODE = "000000";

    /** 错误码: 接口调用失败 */
    private static final String ERROR_CODE_INVOKE = "LE003";

    /** 报文头固定长度部分 */
    private static final String HEADER_PREFIX = "00000040771001RQ1################################";

    /** 服务名 */
    private static final String SERVICE_NAME = "tpbnsmag";

    @Value("${ledger.webservice.url}")
    private String webServiceUrl;

    @Value("${ledger.webservice.connectTimeout:10000}")
    private Integer connectTimeout;

    @Value("${ledger.webservice.readTimeout:30000}")
    private Integer readTimeout;

    @Value("${ledger.webservice.writeTimeout:10000}")
    private Integer writeTimeout;

    private OkHttpClient httpClient;

    /**
     * 初始化OkHttp客户端
     */
    @PostConstruct
    public void init() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                .writeTimeout(writeTimeout, TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true)
                .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
                .addInterceptor(new LoggingInterceptor())
                .build();

        log.info("====== LedgerWebServiceClient初始化完成 ======");
        log.info("URL: {}", webServiceUrl);
        log.info("连接超时: {}ms", connectTimeout);
        log.info("读取超时: {}ms", readTimeout);
        log.info("写入超时: {}ms", writeTimeout);
        log.info("==========================================");
    }

    /**
     * 调用批量记账接口
     *
     * @param request 批量记账请求
     * @return 批量记账响应
     */
    public LedgerBatchResponse invokeBatchAccounting(LedgerBatchRequest request) {
        log.info("========== 开始调用Ledger批量记账接口 ==========");
        log.info("系统标识: {}", request.getContSystemFlag());
        log.info("批次日期: {}", request.getBatchDate());
        log.info("批次号: {}", request.getBatchNo());
        log.info("文件名: {}", request.getFileName());
        log.info("总笔数: {}", request.getTotalCnt());

        long startTime = System.currentTimeMillis();

        try {
            // 1. 校验配置
            validateConfig();

            // 3. 构建SOAP请求报文
            String soapRequest = buildSoapRequest(request);
            log.debug("SOAP请求报文: {}", soapRequest);

            // 4. 发送HTTP请求
            String soapResponse = sendSoapRequest(soapRequest);
            log.debug("SOAP响应报文: {}", soapResponse);

            // 5. 解析响应报文
            LedgerBatchResponse response = parseSoapResponse(soapResponse);

            long elapsed = System.currentTimeMillis() - startTime;
            log.info("========== Ledger批量记账接口调用完成 ==========");
            log.info("批次号: {}", response.getBatchNo());
            log.info("响应码: {}", response.getRspCode());
            log.info("响应消息: {}", response.getRspMsg());
            log.info("耗时: {}ms", elapsed);
            log.info("结果: {}", response.isSuccess() ? "成功" : "失败");
            log.info("===========================================");

            return response;

        } catch (BusinessException e) {
            log.error("调用Ledger批量记账接口失败(业务异常), batchNo={}, errorCode={}, errorMsg={}",
                    request.getBatchNo(), e.getCode(), e.getErrorMsg(), e);
            throw e;
        } catch (Exception e) {
            log.error("调用Ledger批量记账接口失败(系统异常), batchNo={}", request.getBatchNo(), e);
            throw new BusinessException(ERROR_CODE_INVOKE, "调用Ledger接口失败: " + e.getMessage());
        }
    }

    /**
     * 构建SOAP请求报文
     * 报文格式: 报文头(40字节) + 服务名 + 空格 + KEY-VALUE内容
     * 示例: 00000040771001RQ1################################tpbnsmag <ContSystemFlag>YXH</ContSystemFlag>...
     */
    private String buildSoapRequest(LedgerBatchRequest request) {
        StringBuilder sb = new StringBuilder();

        // 1. 报文头 (40字节固定长度)
        sb.append(HEADER_PREFIX);

        // 2. 服务名
        sb.append(SERVICE_NAME).append(" ");

        // 3. KEY-VALUE内容
        // 系统标识 - 传核心该报文必输
        if (StringUtils.isNotBlank(request.getSystemFlag())) {
            sb.append("<SystemFlag>").append(request.getSystemFlag()).append("</SystemFlag>");
        }

        // 系统标识 - 分配给各个系统的系统标示
        sb.append("<ContSystemFlag>").append(request.getContSystemFlag()).append("</ContSystemFlag>");

        // 批次日期
        sb.append("<BatchDate>").append(request.getBatchDate()).append("</BatchDate>");

        // 批次号
        sb.append("<BatchNo>").append(request.getBatchNo()).append("</BatchNo>");

        // 文件路径
        sb.append("<FileDir>").append(request.getFileDir()).append("</FileDir>");

        // 文件名
        sb.append("<FileName>").append(request.getFileName()).append("</FileName>");

        // 总笔数
        sb.append("<TotalCnt>").append(request.getTotalCnt()).append("</TotalCnt>");

        // MAC校验码 (可选)
        if (StringUtils.isNotBlank(request.getMac())) {
            sb.append("<Mac>").append(request.getMac()).append("</Mac>");
        }

        return sb.toString();
    }

    /**
     * 发送SOAP请求
     */
    private String sendSoapRequest(String soapRequest) throws IOException {
        // 使用GBK编码构建请求体
        byte[] requestBytes = soapRequest.getBytes(Charset.forName(ENCODING));

        RequestBody requestBody = RequestBody.create(
                requestBytes,
                MediaType.parse("text/xml; charset=" + ENCODING)
        );

        Request httpRequest = new Request.Builder()
                .url(webServiceUrl)
                .post(requestBody)
                .addHeader("Content-Type", "text/xml; charset=" + ENCODING)
                .addHeader("SOAPAction", "")
                .addHeader("User-Agent", "LedgerWebServiceClient/1.0")
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                String errorMsg = String.format("HTTP请求失败, code=%d, message=%s",
                        response.code(), response.message());
                log.error(errorMsg);
                throw new IOException(errorMsg);
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new IOException("响应体为空");
            }

            // 使用GBK编码读取响应
            byte[] responseBytes = responseBody.bytes();
            return new String(responseBytes, Charset.forName(ENCODING));
        }
    }

    /**
     * 解析SOAP响应报文
     * 响应格式: KEY-VALUE格式
     * 示例: <ContSystemFlag>YXH</ContSystemFlag><BatchDate>2016-10-01</BatchDate>...
     */
    private LedgerBatchResponse parseSoapResponse(String soapResponse) {
        if (StringUtils.isBlank(soapResponse)) {
            throw new BusinessException(ERROR_CODE_INVOKE, "响应报文为空");
        }

        LedgerBatchResponse response = new LedgerBatchResponse();

        // 解析各个字段
        response.setContSystemFlag(extractXmlValue(soapResponse, "ContSystemFlag"));
        response.setBatchDate(extractXmlValue(soapResponse, "BatchDate"));
        response.setBatchNo(extractXmlValue(soapResponse, "BatchNo"));
        response.setRspCode(extractXmlValue(soapResponse, "RspCode"));
        response.setRspMsg(extractXmlValue(soapResponse, "RspMsg"));
        response.setMac(extractXmlValue(soapResponse, "Mac"));

        // 校验必填字段
        if (StringUtils.isBlank(response.getRspCode())) {
            throw new BusinessException(ERROR_CODE_INVOKE, "响应报文缺少RspCode字段");
        }

        if (StringUtils.isBlank(response.getRspMsg())) {
            response.setRspMsg("无响应消息");
        }

        return response;
    }

    /**
     * 从XML中提取标签值
     *
     * @param xml XML内容
     * @param tagName 标签名
     * @return 标签值
     */
    private String extractXmlValue(String xml, String tagName) {
        String startTag = "<" + tagName + ">";
        String endTag = "</" + tagName + ">";

        int startIndex = xml.indexOf(startTag);
        int endIndex = xml.indexOf(endTag);

        if (startIndex >= 0 && endIndex > startIndex) {
            return xml.substring(startIndex + startTag.length(), endIndex).trim();
        }

        return null;
    }

    /**
     * 校验配置
     */
    private void validateConfig() {
        if (StringUtils.isBlank(webServiceUrl)) {
            throw new BusinessException(ERROR_CODE_INVOKE, "Ledger WebService URL未配置");
        }

        if (httpClient == null) {
            throw new BusinessException(ERROR_CODE_INVOKE, "OkHttpClient未初始化");
        }
    }

    /**
     * OkHttp日志拦截器
     */
    private static class LoggingInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();

            long startTime = System.currentTimeMillis();
            log.debug("发送HTTP请求: {} {}", request.method(), request.url());

            Response response = chain.proceed(request);

            long elapsed = System.currentTimeMillis() - startTime;
            log.debug("收到HTTP响应: {} {} ({}ms)",
                    response.code(), response.message(), elapsed);

            return response;
        }
    }
}