package com.yrjk.life.core.service.impl;

import com.yrjk.life.core.model.ledger.LedgerAccountingRecord;
import com.yrjk.life.core.model.ledger.LedgerBatchResponse;
import com.yrjk.life.core.service.impl.LedgerAccountingServiceImpl.BatchFileWriter;
import com.xxl.job.core.context.XxlJobHelper;
import com.yrjk.life.core.wrapper.WelfareWrapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 对账系统批量记账抽象处理器
 *
 * <AUTHOR>
 * @date 2025-11-05
 */
@Slf4j
public abstract class AbstractReconciliationProcessor {

    /**
     * 系统标识
     */
    protected static final String SYSTEM_FLAG = "YYX";

    /**
     * 分页大小
     */
    protected static final int PAGE_SIZE = 500;

    @Resource
    protected WelfareWrapper welfareWrapper;

    @Resource
    protected LedgerAccountingServiceImpl ledgerAccountingService;

    /**
     * 模板方法：定义对账数据推送处理流程
     */
    public final void process() {
        BatchFileWriter writer = null;
        List<Long> recordIds = new ArrayList<>();
        int totalRecords = 0;
        try {
            // 1. 初始化参数
            LedgerAccountParams params = initParams();
            log.info("对账数据推送开始 - 开始日期:{}, 结束日期:{}, 批次日期:{}, 店铺:{}, 系统:{}", params.getStartDate(), params.getEndDate(), params.getBatchDate(), params.getStoreIds(), SYSTEM_FLAG);

            // 2. 创建批次文件写入器
            writer = ledgerAccountingService.createBatchFileWriter(SYSTEM_FLAG, params.getBatchDate());
            log.info("创建批次文件 - 批次号:{}", writer.getBatchNo());
            // 3. 分页查询并流式写入
            int currentPage = 0;
            boolean hasNext = true;

            while (hasNext) {
                // 3.1 查询一页数据（钩子方法）
                PageQueryResult pageResult = queryPageData(params.getStartDate(), params.getEndDate(), params.getStoreIds(), currentPage, PAGE_SIZE);
                if (pageResult == null || pageResult.getRecords() == null) {
                    break;
                }
                List<LedgerAccountingRecord> records = pageResult.getRecords();

                if (!records.isEmpty()) {
                    writer.writeRecords(records);
                    records.stream().map(LedgerAccountingRecord::getId).forEach(recordIds::add);
                    totalRecords += records.size();
                }

                hasNext = pageResult.isHasNext();
                currentPage++;
            }

            log.info("数据查询完成 - 总页数:{}, 总记录数:{}", currentPage, totalRecords);
            // 4. 完成文件写入
            writer.complete();
            // 5. 无数据直接返回
            if (totalRecords == 0) {
                log.warn("无数据需要推送");
                XxlJobHelper.handleSuccess("执行成功：无数据");
                return;
            }
            // 6. 提交批量记账
            LedgerBatchResponse batchResponse = ledgerAccountingService.submitBatchFileRequest(writer);
            Integer pushStatus = 1;
            String errorMessage = "";
            if (!batchResponse.isSuccess()) {
                pushStatus = 2;
                errorMessage = batchResponse.getRspMsg();
                log.error("记账失败 - 错误码:{}, 错误信息:{}",
                        batchResponse.getRspCode(), batchResponse.getRspMsg());
                XxlJobHelper.handleFail(String.format("记账失败：%s", batchResponse.getRspMsg()));
            }

            log.info("记账成功 - 批次号:{}", batchResponse.getBatchNo());

            // 7. 回调更新批次号
            boolean updateSuccess = welfareWrapper.updateBatchNo(recordIds, batchResponse.getBatchNo(), batchResponse.getBatchDate(), pushStatus, errorMessage);

            if (!updateSuccess) {
                log.error("批次号更新失败");
            }
            log.info("对账数据推送完成 - 批次号:{}, 记录数:{}", batchResponse.getBatchNo(), totalRecords);
        } catch (Exception e) {
            log.error("对账数据推送异常 - 已处理:{}条, 异常:", totalRecords, e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (Exception e) {
                    log.error("关闭文件写入器失败", e);
                }
            }
        }
    }

    // ==================== 抽象方法（子类必须实现） ====================

    /**
     * 初始化参数（钩子方法 - 子类提供）
     *
     * @return 对账参数对象
     */
    protected abstract LedgerAccountParams initParams();


    /**
     * 分页查询对账数据（钩子方法）
     */
    protected abstract PageQueryResult queryPageData(LocalDate startDate, LocalDate endDate, String storeIds, Integer pageNum, Integer pageSize);


    /**
     * 对账参数对象
     */
    @Getter
    protected static class LedgerAccountParams {
        private final LocalDate startDate;
        private final LocalDate endDate;
        private final String batchDate;
        private final String storeIds;

        public LedgerAccountParams(LocalDate startDate, LocalDate endDate, String batchDate, String storeIds) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.batchDate = batchDate;
            this.storeIds = storeIds;
        }
    }

    /**
     * 分页查询结果包装
     */
    @Getter
    protected static class PageQueryResult {
        private final List<LedgerAccountingRecord> records;
        private final boolean hasNext;

        public PageQueryResult(List<LedgerAccountingRecord> records, boolean hasNext) {
            this.records = records;
            this.hasNext = hasNext;
        }

    }
}