package com.yrjk.life.core.model.ledger;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.io.Serializable;

/**
 * Ledger批量记账响应报文
 * 对应交易码: 333003
 * 服务名: tpbnsmag
 * 
 * <AUTHOR>
 * @date 2025-11-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LedgerBatchResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 系统标识
     * 类型: char(20)
     * 必填: M
     * 说明: 分配给各个系统的系统标示，比如YXH
     * 样例: YXH
     */
    private String contSystemFlag;
    
    /**
     * 批次日期
     * 类型: char(10)
     * 必填: M
     * 格式: yyyy-MM-dd
     * 样例: 2016-10-01
     */
    private String batchDate;
    
    /**
     * 批次号
     * 类型: char(6)
     * 必填: M
     * 说明: 6位数字批次号
     * 样例: 100001
     */
    private String batchNo;
    
    /**
     * 响应码
     * 类型: char(6)
     * 必填: M
     * 说明: 000000表示成功，其他表示失败
     * 样例: 000000
     */
    private String rspCode;
    
    /**
     * 响应信息
     * 类型: char(80)
     * 必填: M
     * 说明: 响应的详细描述信息
     * 样例: 成功
     */
    private String rspMsg;
    
    /**
     * MAC校验码
     * 类型: char(16)
     * 必填: O (可选)
     * 说明: 用于报文完整性校验
     */
    private String mac;
    
    // ==================== 业务方法 ====================
    
    /**
     * 判断是否成功
     * @return true-成功, false-失败
     */
    public boolean isSuccess() {
        return "000000".equals(rspCode);
    }
    
    /**
     * 判断是否失败
     * @return true-失败, false-成功
     */
    public boolean isFailed() {
        return !isSuccess();
    }
    
    /**
     * 获取批次标识
     * @return 批次日期+批次号
     */
    public String getBatchKey() {
        return batchDate + "_" + batchNo;
    }
    
    /**
     * 创建成功响应
     * @param contSystemFlag 系统标识
     * @param batchDate 批次日期
     * @param batchNo 批次号
     * @return 成功响应对象
     */
    public static LedgerBatchResponse success(String contSystemFlag, String batchDate, String batchNo) {
        return LedgerBatchResponse.builder()
                .contSystemFlag(contSystemFlag)
                .batchDate(batchDate)
                .batchNo(batchNo)
                .rspCode("000000")
                .rspMsg("成功")
                .build();
    }
    
    /**
     * 创建失败响应
     * @param contSystemFlag 系统标识
     * @param batchDate 批次日期
     * @param batchNo 批次号
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     * @return 失败响应对象
     */
    public static LedgerBatchResponse failure(String contSystemFlag, String batchDate, 
                                              String batchNo, String errorCode, String errorMsg) {
        return LedgerBatchResponse.builder()
                .contSystemFlag(contSystemFlag)
                .batchDate(batchDate)
                .batchNo(batchNo)
                .rspCode(errorCode)
                .rspMsg(errorMsg)
                .build();
    }
    
    /**
     * 从KEY-VALUE格式报文解析响应
     * 报文格式示例: <ContSystemFlag>YXH</ContSystemFlag><BatchDate>2016-10-01</BatchDate>...
     * @param xmlContent XML格式的响应内容
     * @return LedgerBatchResponse对象
     */
    public static LedgerBatchResponse parseFromXml(String xmlContent) {
        if (xmlContent == null || xmlContent.isEmpty()) {
            return null;
        }
        
        LedgerBatchResponse response = new LedgerBatchResponse();
        response.setContSystemFlag(extractXmlValue(xmlContent, "ContSystemFlag"));
        response.setBatchDate(extractXmlValue(xmlContent, "BatchDate"));
        response.setBatchNo(extractXmlValue(xmlContent, "BatchNo"));
        response.setRspCode(extractXmlValue(xmlContent, "RspCode"));
        response.setRspMsg(extractXmlValue(xmlContent, "RspMsg"));
        response.setMac(extractXmlValue(xmlContent, "Mac"));
        
        return response;
    }
    
    /**
     * 转换为KEY-VALUE格式报文
     * @return XML格式的响应字符串
     */
    public String toXml() {
        StringBuilder sb = new StringBuilder();
        sb.append("<ContSystemFlag>").append(nvl(contSystemFlag)).append("</ContSystemFlag>");
        sb.append("<BatchDate>").append(nvl(batchDate)).append("</BatchDate>");
        sb.append("<BatchNo>").append(nvl(batchNo)).append("</BatchNo>");
        sb.append("<RspCode>").append(nvl(rspCode)).append("</RspCode>");
        sb.append("<RspMsg>").append(nvl(rspMsg)).append("</RspMsg>");
        if (mac != null && !mac.isEmpty()) {
            sb.append("<Mac>").append(mac).append("</Mac>");
        }
        return sb.toString();
    }
    
    /**
     * 从XML中提取标签值
     * @param xml XML内容
     * @param tagName 标签名
     * @return 标签值
     */
    private static String extractXmlValue(String xml, String tagName) {
        String startTag = "<" + tagName + ">";
        String endTag = "</" + tagName + ">";
        
        int startIndex = xml.indexOf(startTag);
        int endIndex = xml.indexOf(endTag);
        
        if (startIndex >= 0 && endIndex > startIndex) {
            return xml.substring(startIndex + startTag.length(), endIndex).trim();
        }
        
        return null;
    }
    
    /**
     * 空值处理
     * @param obj 对象
     * @return 非空字符串
     */
    private String nvl(Object obj) {
        return obj == null ? "" : obj.toString();
    }
    
    @Override
    public String toString() {
        return "LedgerBatchResponse{" +
                "contSystemFlag='" + contSystemFlag + '\'' +
                ", batchDate='" + batchDate + '\'' +
                ", batchNo='" + batchNo + '\'' +
                ", rspCode='" + rspCode + '\'' +
                ", rspMsg='" + rspMsg + '\'' +
                ", mac='" + mac + '\'' +
                ", isSuccess=" + isSuccess() +
                '}';
    }
}