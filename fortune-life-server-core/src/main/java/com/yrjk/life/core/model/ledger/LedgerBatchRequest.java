// LedgerBatchRequest.java - 批量记账请求报文
package com.yrjk.life.core.model.ledger;

import lombok.Data;
import java.io.Serializable;

/**
 * Ledger批量记账请求报文
 * 交易码: 333003
 */
@Data
public class LedgerBatchRequest implements Serializable {
    
    /**
     * 系统标识 - 传核心该报文必输
     */
    private String systemFlag;
    
    /**
     * 系统标识 - 分配给各个系统的系统标示，如YXH
     */
    private String contSystemFlag;
    
    /**
     * 批次日期 格式: yyyy-MM-dd
     */
    private String batchDate;
    
    /**
     * 批次号 6位
     */
    private String batchNo;
    
    /**
     * 文件路径 - 绝对路径
     */
    private String fileDir;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 总笔数
     */
    private Integer totalCnt;
    
    /**
     * MAC校验码
     */
    private String mac;
}