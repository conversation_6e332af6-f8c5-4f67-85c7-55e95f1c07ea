package com.yrjk.life.core.wrapper;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Strings;
import com.yrjk.life.core.common.Constants;
import com.yrjk.life.core.common.DataModel;
import com.yrjk.life.core.component.properties.LifeServerProperties;
import com.yrjk.life.core.enums.*;
import com.yrjk.life.core.exception.BusinessAssert;
import com.yrjk.life.core.exception.BusinessException;
import com.yrjk.life.core.model.mall.MallProductListVo;
import com.yrjk.life.core.model.mall.MallProductVo;
import com.yrjk.life.core.model.mall.ProductRankVo;
import com.yrjk.life.core.model.welfare.*;
import com.yrjk.life.core.model.welfare.platform.common.WelfarePlatformBaseResp;
import com.yrjk.life.core.model.welfare.platform.product.*;
import com.yrjk.life.core.service.mall.MallProductService;
import com.yrjk.life.core.utils.DateUtils;
import com.yrjk.life.core.utils.HttpClientUtil;
import com.yrjk.life.core.utils.JsonUtil;
import com.yrjk.life.core.utils.MyTypeToken;
import com.yrjk.life.core.utils.ReturnUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.creditease.sbc.open.jwt.channel.ChannelUser;
import cn.creditease.sbc.open.jwt.util.AESUtil;
import cn.creditease.sbc.open.jwt.util.JwtGenerate;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc
 * @date 2021-12-09
 */
@Slf4j
@Component
public class WelfareWrapper {
    @Autowired
    private LifeServerProperties lifeServerProperties;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    private static final String SUCCESS = "K-000000";
    private static final String WELFARE_PLATFORM = "1";
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 根据条件筛选商品(POST)
     * <p>
     * 接口文档: http://it.creditease.corp/pages/viewpage.action?pageId=88978358
     *
     * @param pageNum
     * @param pageSize
     * @param goodsInfoNos  sku编码 查询
     * @param likeGoodsName 模糊条件-商品名称
     * @param addedFlag 上下架状态: 1-上架 / 0-下架 {@link AddedFlagEnum}
     * @return
     */
    public Page<EsGoodsInfoVO> goodsConditionQuery(Integer pageNum, Integer pageSize, List<String> goodsInfoNos, String likeGoodsName, AddedFlagEnum addedFlag, List<String> goodsInfoIds) {
        pageNum = pageNum > 0 ? pageNum - 1 : pageNum;
        Map<String, Object> params = new HashMap<>();
        if (addedFlag != null) {
            params.put("addedFlag", addedFlag.getType());// 上下架状态: 1-上架 / 0-下架
        }
        params.put("pageNum", pageNum); // 福利平台当前页,从0开始
        params.put("pageSize", pageSize);
        params.put("storeId", lifeServerProperties.getStoreId().toString());
        if(CollectionUtils.isNotEmpty(goodsInfoNos)) {
            params.put("goodsInfoNos", goodsInfoNos);
        }
        if(CollectionUtils.isNotEmpty(goodsInfoIds)) {
            params.put("goodsInfoIds", goodsInfoIds);
        }
        params.put("priceFlag", 2); // 0: 无限制 1：现金商品 2：积分商品 3：积分 + 现金
        if(StringUtils.isNotEmpty(likeGoodsName)) {
            params.put("likeGoodsName", likeGoodsName);
        }
        Map<String, String> header = this.getJwtToken();
        String jsonStr = JsonUtil.beanToJson(params);
        String url = String.format("%s/open/goods/condition/query", lifeServerProperties.getWelfarePlatform());
        log.info("根据条件查询商品 start，url={},params={},header={}", url, params, header);
        String respStr = HttpClientUtil.postJson(url, jsonStr, header);
        log.info("根据条件查询商品 end，url={},params={},result = {}", url, params, respStr);

        WelfarePlatformBaseResp<EsGoodsInfoResponse> resp = JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<EsGoodsInfoResponse>>() {
        });
        if (null != resp && SUCCESS.equals(resp.getCode())) {
            Page page = new Page(pageNum, pageSize);
            if (resp.getContext() == null) {
                // 返回数据这种情况: {"code":"K-000000","message":null,"errorData":null,"context":null}
                return page.setTotal(0).setRecords(Collections.emptyList());
            }
            MicroServicePage<EsGoodsInfoVO> esGoodsInfoPage = resp.getContext().getEsGoodsInfoPage();
            return page.setRecords(esGoodsInfoPage.getContent()).setTotal(esGoodsInfoPage.getTotal());
        } else {
            log.info("根据条件查询商品 返回失败");
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
    }



    public WelfareChannelLoginRespVo getWelfareUserTokenInfo(Long storeId, String mobileNo,String userId,String welfareChannelCode) {
        Map<String, String> params = new HashMap<>();
        params.put("storeId", storeId.toString());
        params.put("customerAccount", this.getAesEncryptStr(mobileNo));
        params.put("channelUserId", userId);
        params.put("channelCode", lifeServerProperties.getRegisterWelfareChannelCode());
        if(StringUtils.isNotEmpty(welfareChannelCode)){
            params.put("channelCode", welfareChannelCode);
        }
        String url = lifeServerProperties.getWelfarePlatform() + "/open/channel/login";
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String,String> header= this.getJwtToken();
        log.info("获取用户token请求 start，url={},params={},header={}", url, params,header);
        String respStr = HttpClientUtil.postJson(url, jsonStr,header);
        log.info("获取用户token请求 end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<WelfareChannelLoginRespVo> resp = (WelfarePlatformBaseResp<WelfareChannelLoginRespVo>) JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<WelfareChannelLoginRespVo>>(){});
        if(null!=resp && SUCCESS.equals(resp.getCode())) {
            return resp.getContext();
        } else {
            log.info("获取用户token请求返回失败");
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
    }

    /**
     * 变更福利平台用户手机号
     * @param storeId
     * @param customerId
     * @param oldMobile
     * @param newMobile
     * @return  返回String为错误信息，如果为空则表示成功。
     */
    public String changeWelfareMobile(Long storeId, String customerId, String oldMobile, String newMobile) {
        Map<String, String> params = new HashMap<>();
        params.put("storeId", storeId.toString());
        params.put("oldAccount", this.getAesEncryptStr(oldMobile));
        params.put("newAccount", this.getAesEncryptStr(newMobile));
        params.put("customerId", customerId);
        String url = lifeServerProperties.getWelfarePlatform() + "/open/customer/updateAccount";
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String, String> header = this.getJwtToken();
        log.info("变更用户手机号请求 start，url={},params={},header={}", url, params, header);
        String respStr = HttpClientUtil.postJson(url, jsonStr, header);
        log.info("变更用户手机号请求 end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<String> resp = JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<String>>() {
        });
        if (null != resp && SUCCESS.equals(resp.getCode())) {
            return resp.getContext();
        } else {
            log.error("变更用户福利平台手机号失败");
            throw new BusinessException(ErrorEnum.WELFARE_CHANGE_MOBILE_ERROR);
        }
    }

    /**
     * 查询福利平台排行榜
     *
     * @param pageSize
     * @param pageNo
     * @param opType
     * @return
     */
    public DataModel<ProductRankVo> getWelfarePlatformRank(Integer pageSize, Integer pageNo, String opType){
        ProductRankVo productRankVo = new ProductRankVo();
        String rankKey = MallProductService.WELFARE_PALTFORM_RANK_KEY + pageNo + ":" + pageSize;
        String rank = redisTemplate.opsForValue().get(rankKey);
        if(StringUtils.isNotBlank(rank)){
            log.info("福利平台商品排行榜在缓存中存在，rank={}",rank);
            List<MallProductVo> welfarePlatforRank = JsonUtil.jsonArrayToListBean(rank,MallProductVo.class);
            if(OpTypeEnum.ALL_NEW.getCode().equals(opType) && CollectionUtils.isNotEmpty(welfarePlatforRank) && welfarePlatforRank.size() > 3){
                welfarePlatforRank = welfarePlatforRank.subList(0,3);
            }
            productRankVo.setBestSalesList(welfarePlatforRank);
            productRankVo.setBestSalesUrl(lifeServerProperties.getGoodRankH5Url());

            return ReturnUtil.successResult(productRankVo);
        }

        log.info("查询福利商城排行榜");
        String suffixUrl = "/open/goods/rank";
        String url = lifeServerProperties.getWelfarePlatform() + suffixUrl;
        Map<String,Long> param = new HashMap<>();
        param.put("storeId",lifeServerProperties.getStoreId());
        String jsonParam = JsonUtil.beanToJson(param);
        Map<String,String> token = getJwtToken();
        String resp = HttpClientUtil.postJson(url,jsonParam,token);
        log.info("福利平台排行榜接口返回:{}",resp);
        WelfarePlatformBaseResp<WelfareProductDataVo> productList = JsonUtil.jsonToBean(resp, new MyTypeToken<WelfarePlatformBaseResp<WelfareProductDataVo>>() {});
        BusinessAssert.isNotNull(productList,WelfarePlatformError.DATA_IS_NULL);

        if(!SUCCESS.equals(productList.getCode())){
            return ReturnUtil.exception(productList.getCode(),productList.getMessage());
        }

        WelfareProductDataVo productVos = productList.getContext();
        if(productVos != null && CollectionUtils.isNotEmpty(productVos.getGoodsInfoList())){
            List<MallProductVo> bestSalesList = productVos.getGoodsInfoList().stream()
                    .sorted((p1,p2) -> {
                        if(p1.getGoodsInfoRank() == null || p2.getGoodsInfoRank() == null){
                            return 0;
                        }
                        return p2.getGoodsInfoRank().compareTo(p1.getGoodsInfoRank());
                    })
                    .map(product ->{
                        MallProductVo vo = new MallProductVo();
                        vo.setStoreId(lifeServerProperties.getStoreId());
                        vo.setProductDetailUrl(product.getGoodsInfoUrl());
                        vo.setProductName(product.getGoodsInfoName());
                        vo.setProductSubIdStr(product.getSkuId());
                        vo.setImageUrl(product.getGoodsInfoImg());
                        vo.setPointPrice(product.getMarketPrice().doubleValue());
                        vo.setMallProductType(WELFARE_PLATFORM);

                        return vo;
                    }).collect(Collectors.toList());

            redisTemplate.opsForValue().set(rankKey,JsonUtil.beanToJson(bestSalesList),lifeServerProperties.getExpireTime(), TimeUnit.SECONDS);

            productRankVo.setBestSalesUrl(lifeServerProperties.getGoodRankH5Url());
            if(OpTypeEnum.ALL_NEW.getCode().equals(opType) && CollectionUtils.isNotEmpty(bestSalesList) && bestSalesList.size() > 3){
                bestSalesList = bestSalesList.subList(0,3);
            }
            productRankVo.setBestSalesList(Collections.unmodifiableList(bestSalesList));

            return ReturnUtil.successResult(productRankVo);
        }

        return ReturnUtil.successResult();
    }

    /**
     *
     * @param pageSize
     * @param pageNo
     * @param sortFlag,如下
     * 0: 销量倒序->时间倒序->市场价倒序
     * 1:上架时间倒序->销量倒序->市场价倒序
     * 2:市场价倒序->销量倒序
     * 3:市场价升序->销量倒序
     * 4:销量倒序->市场价倒序
     * 5:评论数倒序->销量倒序->市场价倒序
     * 6:好评倒序->销量倒序->市场价倒序
     * 7:收藏倒序->销量倒序->市场价倒序
     * 10:排序号倒序->创建时间倒序
     * 11:修改时间倒序->销量倒序->市场价倒序
     * @param channelCode 福利平台渠道id
     * @return
     */
    public DataModel<MallProductListVo> queryWelfarePlatformProducts(Integer pageSize, Integer pageNo, Integer sortFlag, String channelCode,String pointCondition) {
        String welfareProductKey = MallProductService.WELFARE_PALTFORM_PRODUCT_LIST_KEY + sortFlag + ":" + pageNo + ":" + pageSize + ":" + channelCode;
        PointConditionEnum conditionEnum = PointConditionEnum.getByPointCondition(pointCondition);
        if(Objects.nonNull(conditionEnum) && PointConditionEnum.ALL != conditionEnum){
            welfareProductKey = welfareProductKey +":" + conditionEnum.getPointCondition();
        }
        String pointProductList = redisTemplate.opsForValue().get(welfareProductKey);
        if(StringUtils.isNotBlank(pointProductList)){
            log.info("福利平台商城商品列表数据：{}",pointProductList);
            MallProductListVo mallProductListVo = JsonUtil.jsonToBean(pointProductList,MallProductListVo.class);
            if(mallProductListVo != null){
                return ReturnUtil.successResult(mallProductListVo);
            }
        }

        String resp = getWelfarePlatformProducts(pageSize, pageNo, sortFlag, channelCode, false,conditionEnum.getStartPoint(),conditionEnum.getEndPoint());
        if(StringUtils.isBlank(resp)){
            return ReturnUtil.exception(WelfarePlatformError.DATA_IS_NULL);
        }
        WelfarePlatformBaseResp<WelfareProductDataVo> productList = JsonUtil.jsonToBean(resp, new MyTypeToken<WelfarePlatformBaseResp<WelfareProductDataVo>>() {});

        BusinessAssert.isNotNull(productList,WelfarePlatformError.DATA_IS_NULL);

        if(!SUCCESS.equals(productList.getCode())){
            return ReturnUtil.exception(productList.getCode(),productList.getMessage());
        }
        if(productList.getContext() != null && CollectionUtils.isNotEmpty(productList.getContext().getGoodsInfoList())){
            MallProductListVo products = buildWelfarePlatformProduct(productList.getContext().getGoodsInfoList());
            redisTemplate.opsForValue().set(welfareProductKey,JsonUtil.beanToJson(products),lifeServerProperties.getExpireTime(),TimeUnit.SECONDS);

            return ReturnUtil.successResult(products);
        }

        return ReturnUtil.successResult();
    }

    public MallProductListVo queryWelfarePlatformProducts(List<String> channelCodeList,
        Integer startPoint, Integer endPoint, String goodsInfoName, Integer pageSize, Integer pageNo , WelfareProductSortTypeEnum sortType) {
        String resp = getWelfarePlatformProducts(pageSize,pageNo,sortType.getSortFlag(),channelCodeList,false,
            startPoint,endPoint,goodsInfoName);
        if(StringUtils.isNotBlank(resp)){
            WelfarePlatformBaseResp<WelfareProductDataVo> productList = JsonUtil.jsonToBean(resp, new MyTypeToken<WelfarePlatformBaseResp<WelfareProductDataVo>>() {});
            BusinessAssert.isNotNull(productList,WelfarePlatformError.DATA_IS_NULL);
            BusinessAssert.isNotNull(productList.getContext(),WelfarePlatformError.DATA_IS_NULL);
            BusinessAssert.isNotNull(productList.getContext().getGoodsInfoList(),WelfarePlatformError.DATA_IS_NULL);
            return buildWelfarePlatformProduct(productList.getContext().getGoodsInfoList());
        }
        return null;
    }

    public MallProductListVo buildWelfarePlatformProduct(List<WelfareProductVo> vos){
        MallProductListVo products = new MallProductListVo();
        products.setProductList(getMallProductVos(vos));
        return products;
    }

    /**
     * 封装福利平台商品详情信息
     * @param vos 商品列表
     * @param storeId 福利平台店铺ID
     * @param channelCode 渠道编码
     * @return 福利平台商品详情列表
     */
    public MallProductListVo buildWelfarePlatformProductList(List<WelfareProductVo> vos, Long storeId, String channelCode){
        MallProductListVo products = new MallProductListVo();
        products.setProductList(getMallProductVoList(vos, storeId, channelCode));
        return products;
    }

    public List<MallProductVo> getMallProductVos(List<WelfareProductVo> vos) {
        return vos.stream().map(product -> {
            MallProductVo vo = new MallProductVo();
            vo.setProductName(product.getGoodsInfoName());
            vo.setImageUrl(product.getGoodsInfoImg());
            vo.setProductSubIdStr(product.getSkuId());
            // pointPrice现金价格 -> 对应福利平台的市场价
            if(product.getMarketPrice() != null && product.getMarketPrice().compareTo(new BigDecimal("0")) >= 0){
                if(product.getMarketPrice().compareTo(new BigDecimal("0")) == 0){
                    vo.setPointPrice(Double.valueOf("0.00"));
                }else{
                    vo.setPointPrice(product.getMarketPrice().doubleValue());
                }
            }
            //2022-04-20 产品要求商品详情页URL拼接channel_code参数
            String productDetailUrl = product.getGoodsInfoUrl();
            if (StringUtils.isNotBlank(productDetailUrl) && productDetailUrl.contains("?")) {
                productDetailUrl += lifeServerProperties.getGoodUrlChannelCodeParam();
            }
            vo.setProductDetailUrl(productDetailUrl);
            vo.setStoreId(lifeServerProperties.getStoreId());
            vo.setMallProductType(WELFARE_PLATFORM);
            vo.setPointNum("");//默认值
            if(product.getBuyPoint() != null && product.getBuyPoint().compareTo(0L) > 0){//积分大于0显示
                vo.setPointNum(String.valueOf(product.getBuyPoint()));
            }
            // 积分划线价
            vo.setLinePoint(product.getLinePoint());
            vo.setGoodsInfoNo(product.getGoodsInfoNo());
            // 划线价
            vo.setLinePrice(product.getLinePrice());
            vo.setLabelIdStr(product.getLabelIdStr());
            // 库存
            if (product.getStock() != null) {
                vo.setExistAmount(product.getStock().intValue());
            } else {
                vo.setExistAmount(0);
            }
            //销量
            vo.setGoodsSalesNum(Objects.nonNull(product.getGoodsSalesNum()) ? product.getGoodsSalesNum() : 0L);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 封装福利平台商品详情信息
     * @param vos 商品列表
     * @param storeId 福利平台店铺id
     * @param channelCode 渠道编码
     * @return 福利平台商品详情列表
     */
    public List<MallProductVo> getMallProductVoList(List<WelfareProductVo> vos, Long storeId, String channelCode) {
        return vos.stream().map(product -> {
            MallProductVo vo = new MallProductVo();
            vo.setProductName(product.getGoodsInfoName());
            vo.setImageUrl(product.getGoodsInfoImg());
            vo.setProductSubIdStr(product.getSkuId());
            // pointPrice现金价格 -> 对应福利平台的市场价
            if(product.getMarketPrice() != null && product.getMarketPrice().compareTo(new BigDecimal("0")) >= 0){
                if(product.getMarketPrice().compareTo(new BigDecimal("0")) == 0){
                    vo.setPointPrice(Double.valueOf("0.00"));
                }else{
                    vo.setPointPrice(product.getMarketPrice().doubleValue());
                }
            }
            //2022-04-20 产品要求商品详情页URL拼接channel_code参数
            StringBuilder productDetailUrl = new StringBuilder();
            productDetailUrl.append(product.getGoodsInfoUrl());
            if (StringUtils.isNotBlank(product.getGoodsInfoUrl()) && product.getGoodsInfoUrl().contains("?")) {
                productDetailUrl.append(channelCode);
            }
            vo.setProductDetailUrl(productDetailUrl.toString());
            vo.setStoreId(storeId);
            vo.setMallProductType(WELFARE_PLATFORM);
            //默认值
            vo.setPointNum("");
            //积分大于0显示
            if(product.getBuyPoint() != null && product.getBuyPoint().compareTo(0L) > 0){
                vo.setPointNum(String.valueOf(product.getBuyPoint()));
            }
            // 积分划线价
            vo.setLinePoint(product.getLinePoint());
            vo.setGoodsInfoNo(product.getGoodsInfoNo());
            // 划线价
            vo.setLinePrice(product.getLinePrice());
            vo.setLabelIdStr(product.getLabelIdStr());
            // 库存
            if (product.getStock() != null) {
                vo.setExistAmount(product.getStock().intValue());
            } else {
                vo.setExistAmount(0);
            }
            //销量
            vo.setGoodsSalesNum(Objects.nonNull(product.getGoodsSalesNum()) ? product.getGoodsSalesNum() : 0L);
            // 商品类型
            vo.setGoodsType(product.getGoodsType());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 调用福利平台查询商品列表
     * @param pageSize
     * @param pageNo
     * @param sortFlag
     * @param channelCode
     * @param refreshConfig，false-福利商城接口调用，true-后台缓存刷新接口调用
     * @return
     */
    public String getWelfarePlatformProducts(Integer pageSize, Integer pageNo, Integer sortFlag, String channelCode, boolean refreshConfig) {
        return this.getWelfarePlatformProducts(pageSize, pageNo, sortFlag, channelCode, refreshConfig, null, null);
    }
    public String getWelfarePlatformProducts(Integer pageSize, Integer pageNo, Integer sortFlag, String channelCode, boolean refreshConfig, Integer startPoint, Integer endPoint) {
        //该接口统一参数格式，统一用channelCodeList 该处进行转换  避免调用逻辑修改
        channelCode = Strings.isNullOrEmpty(channelCode) ? lifeServerProperties.getWelfareChannelCode() : channelCode;
        return getWelfarePlatformProducts(pageSize,pageNo,sortFlag,Collections.singletonList(channelCode),refreshConfig,startPoint,endPoint,null);
    }
    public String getWelfarePlatformProducts(Integer pageSize, Integer pageNo, Integer sortFlag, List<String> channelCodeList
        , boolean refreshConfig, Integer startPoint, Integer endPoint,String goodsInfoName) {
        String suffixUrl = "/open/goods/customList";
        String url = lifeServerProperties.getWelfarePlatform() + suffixUrl;
        WelfareProductListReq req = new WelfareProductListReq();
        req.setPageNum(pageNo > 0 ? pageNo - 1 : pageNo);
        req.setPageSize(pageSize);
        req.setStoreId(lifeServerProperties.getStoreId());

        req.setSortFlag(Objects.isNull(sortFlag) ? WelfareProductSortTypeEnum.SORT50.getSortFlag() : sortFlag);
        if(Objects.nonNull(startPoint)){
            req.setStartPoint(startPoint);
        }
        if(Objects.nonNull(endPoint)){
            req.setEndPoint(endPoint);
        }
        req.setChannelCodeList(channelCodeList);
        if(StringUtils.isNotBlank(goodsInfoName)){
            req.setGoodsInfoName(goodsInfoName);
        }
        String resp = HttpClientUtil.postJson(url,JsonUtil.beanToJson(req),getJwtToken());
        log.info("福利平台商品列表数据:{},request:{}",resp,JsonUtil.beanToJson(req));
        return resp;
    }

    /**
     * 查询福利平台指定店铺+指定Tab下的商品列表
     * @param pageSize 分页大小
     * @param pageNo 分页数
     * @param sortFlag 排序规则
     * @param channelCodeList 指定Tab
     * @param startPoint 积分下限
     * @param endPoint 积分上限
     * @param goodsInfoName 搜索关键字
     * @param storeId 店铺id
     * @param skuIdList 商品skuId列表
     * @return 商品列表
     */
    public String getWelfarePlatformProductsWithStoreId(Integer pageSize, Integer pageNo, Integer sortFlag, List<String> channelCodeList,
        Integer startPoint, Integer endPoint, String goodsInfoName, Long storeId, List<String> skuIdList) {
        String suffixUrl = "/open/goods/customList";
        String url = lifeServerProperties.getWelfarePlatform() + suffixUrl;
        WelfareProductListReq req = new WelfareProductListReq();
        req.setPageNum(pageNo > 0 ? pageNo - 1 : pageNo);
        req.setPageSize(pageSize);
        req.setStoreId(storeId);
        req.setSortFlag(sortFlag);
        if(Objects.nonNull(startPoint)){
            req.setStartPoint(startPoint);
        }
        if(Objects.nonNull(endPoint)){
            req.setEndPoint(endPoint);
        }
        req.setChannelCodeList(channelCodeList);
        if(StringUtils.isNotBlank(goodsInfoName)){
            req.setGoodsInfoName(goodsInfoName);
        }
        if (CollUtil.isNotEmpty(skuIdList)) {
            req.setGoodsInfoIdList(skuIdList);
        }
        String resp = HttpClientUtil.postJson(url, JsonUtil.beanToJson(req), getJwtToken());
        log.info("福利平台商品列表数据:{},request:{}", resp, JsonUtil.beanToJson(req));
        return resp;
    }

    /**
     * 默认查询宜人店铺的订单
     *
     * @param orderId
     * @return
     */
    public SimpleWelfareOrderInfoVo getOrderInfo(String orderId) {
        return getOrderInfo(orderId , lifeServerProperties.getStoreId().toString());
    }

    public SimpleWelfareOrderInfoVo getOrderInfo(final String orderId , final String storeId) {
        String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/info";
        Map<String, String> params = new HashMap<>();
        params.put("storeId", storeId);
        params.put("tid", orderId);
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String,String> header= this.getJwtToken();
        log.info("getOrderInfo start，url={},params={}", url, params);
        String respStr = HttpClientUtil.postJson(url, jsonStr,header);
        log.info("getOrderInfo end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>> resp = (WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>)
            JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>>(){});
        if (resp == null || !SUCCESS.equals(resp.getCode())) {
            log.info("getOrderInfo请求返回失败");
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
        if (CollectionUtils.isEmpty(resp.getContext())) {
            log.info("getOrderInfo请求返回失败");
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_RESPONSE_ERROR);
        }
        return resp.getContext().get(0);
    }


    /**
     * 查询宜人店铺下指定用户的所有订单
     *
     * @param customerId
     * @return
     */
    public List<SimpleWelfareOrderInfoVo> getOrderInfoByCustomerId(String customerId) {
        String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/customer/info";
        Map<String, String> params = new HashMap<>();
        params.put("storeId", lifeServerProperties.getStoreId().toString());
        params.put("customerId", customerId);
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String, String> header = this.getJwtToken();
        log.info("getOrderInfoByCustomerId start，url={},params={}", url, params);
        String respStr = HttpClientUtil.postJson(url, jsonStr, header);
        log.info("getOrderInfoByCustomerId end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>> resp = JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>>() {
        });
        if (resp == null || !SUCCESS.equals(resp.getCode())) {
            log.info("getOrderInfoByCustomerId请求返回失败");
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
        if (CollectionUtils.isEmpty(resp.getContext())) {
            log.info("getOrderInfoByCustomerId请求返回失败");
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_RESPONSE_ERROR);
        }
        return resp.getContext();
    }

    public List<SimpleWelfareOrderInfoVo> getCurrentDayOrderList(String customerId,String startModified,String endModified,Integer pageNo,Integer pageSize) {
        String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/increment";
        Map<String, String> params = new HashMap<>();
        params.put("storeId", lifeServerProperties.getStoreId().toString());
        params.put("pageNo", pageNo.toString());
        params.put("pageSize", pageSize.toString());
        params.put("orderCreateFrom", startModified);
        params.put("orderCreateTo", endModified);
        if(!StringUtils.isEmpty(customerId)){
            params.put("customerId", customerId);
        }
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String,String> header= this.getJwtToken();
        log.info("getCurrentDayOrderList start，url={},params={}", url, params);
        String respStr = HttpClientUtil.postJson(url, jsonStr,header);
        log.info("getCurrentDayOrderList end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>> resp = (WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>)
                JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>>(){});
        if ( null!=resp && SUCCESS.equals(resp.getCode()) ) {
            return resp.getContext();
        }
        return null;
    }


    public Map<String, String> getJwtToken() {
        ChannelUser channelUser = ChannelUser.builder()
                .channelId(lifeServerProperties.getChannelId())
                .prefix(lifeServerProperties.getPrefix())
                .serialNum(lifeServerProperties.getSerialNum())
                .build();
        String jwtToken = JwtGenerate.createJWT(channelUser, lifeServerProperties.getJwt());
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", jwtToken);
        return header;
    }

    public List<SimpleWelfareOrderInfoVo> getAllNewOrderInfoByTheDayBefore(LocalDate yesterday ,int pageNum ,int pageSize) {
        String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/less";
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", lifeServerProperties.getStoreId());
        params.put("startCreated", yesterday.format(DateTimeFormatter.ofPattern(DateUtils.FORMAT_YYYY_MM_DD)) );
        params.put("endCreated", yesterday.format(DateTimeFormatter.ofPattern(DateUtils.FORMAT_YYYY_MM_DD)) );
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String,String> header= this.getJwtToken();
        log.info("getAllNewOrderInfoByTheDayBefore start，url={},params={}", url, params);
        String respStr = HttpClientUtil.postJson(url, jsonStr,header);
        log.info("getAllNewOrderInfoByTheDayBefore end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>> resp = (WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>)
            JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>>(){});
        if ( null!=resp && SUCCESS.equals(resp.getCode()) ) {
            return resp.getContext();
        } else {
            log.info("getAllNewOrderInfoByTheDayBefore 请求返回失败");
            return null;
        }
    }


    public List<SimpleWelfareOrderInfoVo> getTradeListByLabelId(String customerId , List<String> labelIds ,int pageNum ,int pageSize) {
        String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/list/byLabel";
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", lifeServerProperties.getStoreId());
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("labelIds", labelIds);
        params.put("customerId", customerId);
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String,String> header= this.getJwtToken();
        log.info("getTradeListByLabelId start，url={},params={}", url, params);
        String respStr = HttpClientUtil.postJson(url, jsonStr,header);
        log.info("getTradeListByLabelId end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>> resp = (WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>)
            JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>>(){});
        if ( null!=resp && SUCCESS.equals(resp.getCode()) ) {
            return resp.getContext();
        } else {
            log.info("getTradeListByLabelId 请求返回失败");
            return null;
        }
    }

    public List<SimpleWelfareOrderInfoVo> getTradeListByCustomerAndStoreId(String customerId, String skuNameLike,
        int pageNum, int pageSize, Long storeId, String skuId, String payState) {
        String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/list/byCustomer";
        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum > 0 ? pageNum -1 : 0);
        params.put("pageSize", Math.min(pageSize, 100));
        params.put("storeId", storeId);
        params.put("customerId", customerId);
        if(StringUtils.isNotBlank(skuNameLike)){
            params.put("skuName", skuNameLike);
        }
        if (StringUtils.isNotBlank(skuId)) {
            params.put("skuId", skuId);
        }
        if (StringUtils.isNotBlank(payState)) {
            params.put("payState", payState);
        }
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String,String> header= this.getJwtToken();
        log.info("getTradeListByCustomer start，url={},params={}", url, params);
        String respStr = HttpClientUtil.postJson(url, jsonStr,header);
        log.info("getTradeListByCustomer end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>> resp = (WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>)
                JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<List<SimpleWelfareOrderInfoVo>>>(){});
        if ( null!=resp && SUCCESS.equals(resp.getCode()) ) {
            return resp.getContext();
        } else {
            log.info("getTradeListByCustomer 请求返回失败");
            return null;
        }
    }


    /**
     * 查询用户名下订单(返回结果包含分页信息)
     */
    public IPage<SimpleWelfareOrderInfoVo> getTradePageByCustomerAndStoreId(OrderPageByCustomerReq req)  {
        String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/page/byCustomer";
        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", req.getPageNum() > 0 ? req.getPageNum() - 1 : req.getPageNum()); // 福利平台当前页,从0开始
        params.put("pageSize", req.getPageSize());
        params.put("storeId", req.getStoreId());
        params.put("customerId", req.getCustomerId());
        if(StringUtils.isNotBlank(req.getSkuName())) {
            params.put("skuName", req.getSkuName());
        }
        if (StringUtils.isNotBlank(req.getSkuId())) {
            params.put("skuId", req.getSkuId());
        }
        if (StringUtils.isNotBlank(req.getPayState())) {
            params.put("payState", req.getPayState());
        }
        if (StringUtils.isNotBlank(req.getOrderCreateTo())) {
            params.put("orderCreateTo", req.getOrderCreateTo());
        }
        if (StringUtils.isNotBlank(req.getOrderCreateFrom())) {
            params.put("orderCreateFrom", req.getOrderCreateFrom());
        }
        try {
            String jsonStr = JsonUtil.beanToJson(params);
            Map<String,String> header= this.getJwtToken();
            log.info("查询福利平台用户名下订单开始, url:{}, params:{}", url, params);
            String respStr = HttpClientUtil.postJson(url, jsonStr,header);
            log.info("查询福利平台用户名下订单结束, url:{}, params:{}, result:{}", url, params, respStr);
            WelfarePlatformBaseResp<MicroServicePage<SimpleWelfareOrderInfoVo>> resp =
            JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<MicroServicePage<SimpleWelfareOrderInfoVo>>>() {});
            if (resp == null || !SUCCESS.equals(resp.getCode())) {
                log.warn("查询福利平台用户名下订单异常, 返回结果:{}", JSON.toJSONString(resp));
                throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
            }
            Page page = new Page(req.getPageNum(), req.getPageSize());
            MicroServicePage<SimpleWelfareOrderInfoVo> result = resp.getContext();
            if (result == null) {
                return page.setTotal(0).setRecords(Collections.emptyList());
            }
            return page.setRecords(result.getContent()).setTotal(result.getTotal());
        } catch (Exception e) {
            log.error("查询福利平台用户名下订单异常:", e);
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
    }

    private String getAesEncryptStr(String sStr){
        String encryptStr =null;
        try {
            encryptStr = AESUtil.encrypt(sStr, lifeServerProperties.getJwt());
        } catch (Exception e) {
            log.error("getAesEncryptStr error" , e);
        }
        return encryptStr;
    }

    /**
     * 通知福利平台同步收货地址
     * @param mobile 手机号
     * @param storeId 店铺id
     * @param userId 用户id
     * @param syncStoreId 从syncStoreId这个店铺同步收货地址到storeId
     */
    public void syncAddress(String mobile, Long storeId, Long userId, String syncStoreId) {
        Map<String, String> params = new HashMap<>();
        params.put("phone", this.getAesEncryptStr(mobile));
        params.put("thirdPlatformUserId", String.valueOf(userId));
        params.put("currentStoreId", String.valueOf(storeId));
        params.put("needSynStoreId", syncStoreId);
        String url = lifeServerProperties.getWelfarePlatform() + "/open/customer/synDeliveryAddressByOtherStore";
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String, String> header = this.getJwtToken();
        log.info("通知福利平台同步收货地址请求 start, url={}, params={}, header={}", url, params, header);
        String respStr = HttpClientUtil.postJson(url, jsonStr, header);
        log.info("通知福利平台同步收货地址请求 end, url={}, params={}, result = {}", url, params, respStr);
    }

    /**
     * 校验渠道id和店铺id是否匹配
     * @param channelIdList 批量渠道id
     * @param storeId 店铺id
     * @return 不匹配的渠道id列表
     */
    public List<String> checkChannelIdAndStoreId(List<String> channelIdList, Long storeId) {
        Map<String, Object> params = new HashMap<>();
        params.put("channelIdList", channelIdList);
        params.put("storeId", storeId);
        String url = lifeServerProperties.getWelfarePlatform() + "/open/goods/checkChannelIdsAndStoreId";
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String, String> header = this.getJwtToken();
        log.info("校验渠道id和店铺id是否匹配请求 start, url={}, params={}, header={}", url, params, header);
        String respStr = HttpClientUtil.postJson(url, jsonStr, header);
        log.info("校验渠道id和店铺id是否匹配请求 end, url={}, params={}, result = {}", url, params, respStr);
        WelfarePlatformBaseResp<CheckChannelIdAndStoreIdVo> resp =
                JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<CheckChannelIdAndStoreIdVo>>(){});
        if (Objects.nonNull(resp) && SUCCESS.equals(resp.getCode())) {
            return Objects.isNull(resp.getContext()) ? null : resp.getContext().getNotMatchChannelIdList();
        } else {
            throw new BusinessException(ErrorEnum.MAGIC_CUBE_CHANNEL_ID_CHECK_ERR);
        }
    }

    /**
     * 单个商品加入购物车
     */
    public DataModel addPurchase(String customerId, String goodsInfoId, Long goodsNum, Boolean verifyStock, String inviteeId, String terminalSource) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("customerId", customerId);
            params.put("goodsInfoId", goodsInfoId);
            params.put("goodsNum", goodsNum);
            params.put("verifyStock", verifyStock);
            params.put("inviteeId", inviteeId);
            params.put("terminalSource", terminalSource);
            String url = lifeServerProperties.getWelfarePlatform() + "/open/goods/addPurchase";
            String jsonStr = JsonUtil.beanToJson(params);
            Map<String, String> header = this.getJwtToken();
            log.info("单个商品加入购物车请求 start, url={}, params={}, header={}", url, params, header);
            String respStr = HttpClientUtil.postJson(url, jsonStr, header);
            log.info("单个商品加入购物车请求 end, url={}, params={}, result = {}", url, params, respStr);
            WelfarePlatformBaseResp resp = JSON.parseObject(respStr, WelfarePlatformBaseResp.class);
            if (Objects.nonNull(resp) && SUCCESS.equals(resp.getCode())) {
                return ReturnUtil.successResult();
            }
            DataModel dataModel = new DataModel<>();
            dataModel.setResult(Constants.FAILURE);
            dataModel.setErrorCode(ErrorEnum.WELFARE_ADD_PURCHASE_ERROR.getErrorCode());
            dataModel.setMsg(Objects.isNull(resp) || StringUtils.isBlank(resp.getMessage()) ?
                    ErrorEnum.WELFARE_ADD_PURCHASE_ERROR.getErrorMsg() : resp.getMessage());
            return dataModel;
        } catch (Exception e) {
            log.error("调用福利平台接口加入购物车发生异常，异常原因为", e);
            return ReturnUtil.fromErrorCode(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
    }

    public boolean revokedYrUid(Set<String> revokePhoneList) {
        List<String> list = revokePhoneList.stream().filter(StringUtils::isNotBlank).map(this::getAesEncryptStr).collect(Collectors.toList());
        String url = lifeServerProperties.getWelfarePlatform() + "/open/customer/revokeThirdPlatformUid";
        Map<String, Object> params = new HashMap<>();
        params.put("phoneNoList", list);
        params.put("thirdPlatform", "YRCF");
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String,String> header= this.getJwtToken();
        log.info("revokeThirdPlatformUid start，url={},params={}", url, params);
        String respStr = HttpClientUtil.postJson(url, jsonStr,header);
        log.info("revokeThirdPlatformUid end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp resp = JsonUtil.jsonToBean(respStr, WelfarePlatformBaseResp.class);
        if (null!=resp && SUCCESS.equals(resp.getCode()) ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 通过手机号获取福利平台用户信息
     * @param mobile
     * @param storeId
     * @return
     */
    public WelfareCustomerInfoVo getWelfareCustomerInfo(String mobile ,Long storeId) {
        Map<String, String> params = new HashMap<>();
        params.put("storeId", String.valueOf(storeId));
        params.put("account", this.getAesEncryptStr(mobile));
        String url = lifeServerProperties.getWelfarePlatform() + "/open/customer/getDetailInfo";
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String, String> header = this.getJwtToken();
        log.info("通过手机号获取福利平台用户信息 start，url={},params={},header={}", url, params, header);
        String respStr = HttpClientUtil.postJson(url, jsonStr, header);
        log.info("通过手机号获取福利平台用户信息 end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<WelfareCustomerInfoVo> resp =JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<WelfareCustomerInfoVo>>() {
        });
        if (null != resp && SUCCESS.equals(resp.getCode())) {
            return resp.getContext();
        } else {
            return null;
        }
    }

    public List<GoodsBaseInfo> getSkuInfo(List<String> skuIdList , Long storeId) {
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", storeId);
        params.put("skuIdList",
            skuIdList.stream().filter(org.springframework.util.StringUtils::hasText)
                .distinct().collect(Collectors.toList()));
        String url = lifeServerProperties.getWelfarePlatform() + "/open/goods/sku/info";
        String jsonStr = JsonUtil.beanToJson(params);
        Map<String, String> header = this.getJwtToken();
        log.info("获取福利平台商品信息 start，url={},params={},header={}", url, params, header);
        String respStr = HttpClientUtil.postJson(url, jsonStr, header);
        log.info("获取福利平台商品信息 end，url={},params={},result = {}", url, params, respStr);
        WelfarePlatformBaseResp<GoodsInfoListResponse> resp =JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<GoodsInfoListResponse>>() {
        });
        if (null != resp && SUCCESS.equals(resp.getCode())) {
            return resp.getContext().getGoodsInfoList();
        } else {
            return null;
        }
    }

    /**
     * 统计用户在指定福利平台店铺中各个状态的订单数量
     * @param customerId 用户id
     * @param storeId 店铺id
     * @return 订单统计信息
     */
    public WelfareOrderCountVO getWelfareTradeCountInfo(String customerId, Long storeId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("customerId", customerId);
            params.put("storeId", storeId);
            String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/todo";
            String jsonStr = JsonUtil.beanToJson(params);
            Map<String, String> header = this.getJwtToken();
            log.info("统计用户在指定福利平台店铺中各个状态的订单数量 start,url={},params={},header={}", url, params, header);
            String respStr = HttpClientUtil.postJson(url, jsonStr, header);
            log.info("统计用户在指定福利平台店铺中各个状态的订单数量 end,url={},params={},result = {}", url, params, respStr);
            if (StringUtils.isBlank(respStr)) {
                throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR, "调用福利平台接口查询结果为空");
            }
            WelfarePlatformBaseResp<WelfareOrderCountVO> resp = JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<WelfareOrderCountVO>>(){});
            if (Objects.nonNull(resp) && SUCCESS.equals(resp.getCode())) {
                return resp.getContext();
            } else {
                throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR.getErrorCode(), "调用福利平台接口查询失败");
            }
        } catch (Exception e) {
            log.error("统计用户在指定福利平台店铺中各个状态的订单数量发生异常，异常原因为", e);
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
    }

    public List<GoodsCateVo> getGoodsCateByNameList(List<String> cateNameList) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("cateNameList", cateNameList);
            String url = lifeServerProperties.getWelfarePlatform() + "/open/goods/queryByCateNameAndPoint";
            String jsonStr = JsonUtil.beanToJson(params);
            Map<String, String> header = this.getJwtToken();
            log.info("根据商品分类名称获取商品分类信息, url={}, params={}, header={}", url, params, header);
            String respStr = HttpClientUtil.postJson(url, jsonStr, header);
            log.info("根据商品分类名称获取商品分类信息end, url={}, params={}, result={}", url, params, respStr);
            if (StringUtils.isBlank(respStr)) {
                throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR, "调用福利平台接口查询结果为空");
            }
            WelfarePlatformBaseResp<GoodsCateListRes> resp = JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<GoodsCateListRes>>(){});
            if (Objects.isNull(resp) || !SUCCESS.equals(resp.getCode())) {
                throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR.getErrorCode(), "调用福利平台接口查询失败");
            }
            return resp.getContext().getGoodsCateVOList();
        } catch (Exception  e) {
            log.error("根据商品分类名称获取商品分类信息发生异常，异常原因为", e);
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
    }

    public List<GoodsCateVo> getAllGoodsCate() {
        try {
            String url = lifeServerProperties.getWelfarePlatform() + "/open/goods/allGoodsCate";
            Map<String, String> header = this.getJwtToken();
            log.info("获取所有商品分类信息, url={}, header={}", url, header);
            String respStr = HttpClientUtil.get(url, null, header);
            log.info("获取所有商品分类信息, url={}, header={}, result={}", url, header, respStr);
            if (StringUtils.isBlank(respStr)) {
                throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR, "调用福利平台接口查询结果为空");
            }
            WelfarePlatformBaseResp<GoodsCateListRes> resp = JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<GoodsCateListRes>>(){});
            if (Objects.isNull(resp) || !SUCCESS.equals(resp.getCode())) {
                throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR.getErrorCode(), "调用福利平台接口查询失败");
            }
            return resp.getContext().getGoodsCateVOList();
        } catch (Exception e) {
            log.error("获取所有商品分类信息发生异常，异常原因为", e);
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
    }

    public Map<String, OrderRestrictedSalesActivityRecordVo> getTradeRestrictedSalesRecordList(List<String> tidList, Long storeId) {
        if (CollectionUtils.isEmpty(tidList) || Objects.isNull(storeId)) {
            return new HashMap<>();
        }
        tidList = tidList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tidList)) {
            return new HashMap<>();
        }

        try {
            Map<String, Object> param = new HashMap<>();
            param.put("tidList", tidList);
            param.put("storeId", storeId);
            String jsonStr = JsonUtil.beanToJson(param);
            Map<String, String> header = this.getJwtToken();
            String url = lifeServerProperties.getWelfarePlatform() + "/open/trade/getRestrictedSalesRecordList";
            log.info("getRestrictedSalesRecordList start，url={},params={}", url, param);
            String respStr = HttpClientUtil.postJson(url, jsonStr, header);
            log.info("getRestrictedSalesRecordList end，url={},params={},result = {}", url, param, respStr);
            WelfarePlatformBaseResp<OrderRestrictedSalesRecordResponse> resp =
                    JsonUtil.jsonToBean(respStr, new MyTypeToken<WelfarePlatformBaseResp<OrderRestrictedSalesRecordResponse>>() {});
            if (Objects.nonNull(resp) && SUCCESS.equals(resp.getCode()) && Objects.nonNull(resp.getContext())
                    && CollectionUtils.isNotEmpty(resp.getContext().getRestrictedSalesActivityRecordList())) {
                return resp.getContext().getRestrictedSalesActivityRecordList().stream()
                        .collect(Collectors.toMap(OrderRestrictedSalesActivityRecordVo::getTid, v -> v));
            }
        } catch (Exception e) {
            log.error("查询多个订单的限购记录发生异常，异常原因为", e);
        }
        return new HashMap<>();
    }


    /**
     * 根据订单ID和物流单号查询实时物流信息
     */
    public List<WelfareRealTimeLogisticsInfo> getRealTimeLogisticsInfo(String tid, String deliveryNo) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("tid", tid);
            params.put("deliveryNo", deliveryNo);
            String jsonStr = JsonUtil.beanToJson(params);
            Map<String, String> header = this.getJwtToken();
            String url = lifeServerProperties.getWelfarePlatform() + "/open/logistics/real-time-delivery-info";
            log.info("获取实时物流信息开始, url:{}, params:{}", url, params);
            String respStr = HttpClientUtil.postJson(url, jsonStr, header);
            log.info("获取实时物流信息结束, url:{}, params:{}, result:{}", url, params, respStr);
            WelfarePlatformBaseResp<List<WelfareRealTimeLogisticsInfo>> response = JsonUtil.jsonToBean(respStr,
                    new MyTypeToken<WelfarePlatformBaseResp<List<WelfareRealTimeLogisticsInfo>>>() {});
            if (Objects.isNull(response) || !SUCCESS.equals(response.getCode())) {
                throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR.getErrorCode(), "调用福利平台接口查询失败");
            }
            return response.getContext();
        } catch (Exception e) {
            log.error("调用福利平台获取实时物流信息接口异常:", e);
            throw new BusinessException(ErrorEnum.WELFARE_HTTP_REQ_ERROR);
        }
    }

    /**
     * 分页查询待推送的收入流水
     *
     * @param storeIds 店铺ID列表
     * @param pageNum 页码（从0开始）
     * @param pageSize 每页大小
     * @return 收入流水分页响应
     */
    public WelfarePlatformBaseResp<LedgerAccountPageResp> queryPendingIncome(
            LocalDate startDate, LocalDate endDate, String storeIds, Integer pageNum, Integer pageSize) {

        log.info("========== 查询待推送收入流水 ==========");
        log.info("查询开始日期: {}, 结束日期: {}, 店铺ID: {}, 页码: {}, 每页: {}", startDate, endDate, storeIds, pageNum, pageSize);
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("startDate", startDate.format(formatter));
            params.put("endDate", endDate.format(formatter));
            params.put("storeIds", storeIds);
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);

            String url = lifeServerProperties.getWelfarePlatform() + "/open/ledgerAccounting/pending/income";
            String jsonStr = JsonUtil.beanToJson(params);
            Map<String, String> header = this.getJwtToken();

            log.info("查询收入流水请求 start，url={}, params={}", url, jsonStr);
            String respStr = HttpClientUtil.postJson(url, jsonStr, header);
            log.info("查询收入流水请求 end，result={}", respStr);

            // 解析响应
            WelfarePlatformBaseResp<LedgerAccountPageResp> resp = JsonUtil.jsonToBean(
                    respStr,
                    new MyTypeToken<WelfarePlatformBaseResp<LedgerAccountPageResp>>() {}
            );

            if (resp == null || !SUCCESS.equals(resp.getCode())) {
                log.error("查询收入流水失败, code={}, message={}",
                        resp != null ? resp.getCode() : "null",
                        resp != null ? resp.getMessage() : "响应为空");
                throw new BusinessException("LE_WF001", "查询收入流水失败");
            }

            log.info("查询收入流水成功, 当前页记录数: {}, 总记录数: {}",
                    resp.getContext().getCurrentCount(),
                    resp.getContext().getTotalCount());

            return resp;

        } catch (Exception e) {
            log.error("查询收入流水异常", e);
            throw new BusinessException("LE_WF001", "查询收入流水异常: " + e.getMessage());
        }
    }

    /**
     * 分页查询待推送的退款流水
     *
     * @param storeIds 店铺ID列表
     * @param pageNum 页码（从0开始）
     * @param pageSize 每页大小
     * @return 退款流水分页响应
     */
    public WelfarePlatformBaseResp<LedgerAccountPageResp> queryPendingRefund(
            LocalDate startDate,LocalDate endDate, String storeIds, Integer pageNum, Integer pageSize) {

        log.info("========== 查询待推送退款流水 ==========");
        log.info("查询开始日期: {}, 结束日期: {}, 店铺ID: {}, 页码: {}, 每页: {}", startDate, endDate, storeIds, pageNum, pageSize);

        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("startDate", startDate.format(formatter));
            params.put("endDate", endDate.format(formatter));
            params.put("storeIds", storeIds);
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);

            String url = lifeServerProperties.getWelfarePlatform() + "/open/ledgerAccounting/pending/refund";
            String jsonStr = JsonUtil.beanToJson(params);
            Map<String, String> header = this.getJwtToken();

            log.info("查询退款流水请求 start，url={}, params={}", url, jsonStr);
            String respStr = HttpClientUtil.postJson(url, jsonStr, header);
            log.info("查询退款流水请求 end，result={}", respStr);

            // 解析响应
            WelfarePlatformBaseResp<LedgerAccountPageResp> resp = JsonUtil.jsonToBean(
                    respStr,
                    new MyTypeToken<WelfarePlatformBaseResp<LedgerAccountPageResp>>() {}
            );

            if (resp == null || !SUCCESS.equals(resp.getCode())) {
                log.error("查询退款流水失败, code={}, message={}",
                        resp != null ? resp.getCode() : "null",
                        resp != null ? resp.getMessage() : "响应为空");
                throw new BusinessException("LE_WF002", "查询退款流水失败");
            }

            log.info("查询退款流水成功, 当前页记录数: {}, 总记录数: {}",
                    resp.getContext().getCurrentCount(),
                    resp.getContext().getTotalCount());

            return resp;

        } catch (Exception e) {
            log.error("查询退款流水异常", e);
            throw new BusinessException("LE_WF002", "查询退款流水异常: " + e.getMessage());
        }
    }

    /**
     * 回调福利平台更新批次号
     *
     * @param recordsIds 推送记录id
     * @param batchNo 批次号
     * @param batchDate 批次日期
     * @return 是否成功
     */
    public boolean updateBatchNo(List<Long> recordsIds, String batchNo, String batchDate,Integer pushStatus,String errorMsg) {

        log.info("========== 回调福利平台更新批次号 ==========");
        log.info("对账记录ID数量: {}, 批次号: {}, 批次日期: {}", recordsIds.size(), batchNo, batchDate);

        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("ids", recordsIds);
            params.put("batchNo", batchNo);
            params.put("pushStatus", pushStatus);
            params.put("errorMsg", errorMsg);
            String url = lifeServerProperties.getWelfarePlatform() + "/open/ledgerAccounting/push-callback";
            String jsonStr = JsonUtil.beanToJson(params);
            Map<String, String> header = this.getJwtToken();

            log.info("更新批次号请求 start，url={}, params={}", url, jsonStr);
            String respStr = HttpClientUtil.postJson(url, jsonStr, header);
            log.info("更新批次号请求 end，result={}", respStr);

            // 解析响应
            WelfarePlatformBaseResp resp = JsonUtil.jsonToBean(respStr, WelfarePlatformBaseResp.class);

            if (resp == null || !SUCCESS.equals(resp.getCode())) {
                log.error("更新批次号失败, code={}, message={}",
                        resp != null ? resp.getCode() : "null",
                        resp != null ? resp.getMessage() : "响应为空");
                return false;
            }

            log.info("更新批次号成功");
            return true;

        } catch (Exception e) {
            log.error("更新批次号异常", e);
            return false;
        }
    }
}
