package com.yirendai.workbench.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@RefreshScope
public class CallcenterProperty {

    @Value("${callCenter.exportExcelDir:/files/task/}")
    private String exportExcelDir;

    @Value("${callCenter.apiDocUrl:https://yrcc.yrcf.com}")
    private String apiDocUrl;

    @Value("${callCenter.importFileDir:/files/task/}")
    private String importFileDir;

    @Value("${saas.menucode.unassignUserList:公海客户saas}")
    private String unassignUserMenuCode;
    @Value("${saas.menucode.myUserList:我的客户saas}")
    private String myCustomerMenuCode;

    @Value("${saas.menucode.callrecord:通讯记录saas}")
    private String callrecordMenuCode;

    @Value("${saas.menucode.callStatistics:diaoxia<PERSON><PERSON>wutongji}")
    private String saasCallStatisticsMenuCode;

    @Value("${saas.menucode.callStatistics:kefuhuawutongjisaas}")
    private String saasCusServiceCallStatisticsMenuCode;

    @Value("${yumeng.menucode.sms}")
    private String yumengSmsMenuCode; //短信记录

    @Value("${yumeng.menucode.callrecord}")
    private String yumengCallrecordMenuCode; //通讯记录

    @Value("${yumeng.menucode.referral}")
    private String yumengReferralMenuCode; //转介绍列表

    @Value("${yumeng.menucode.listLock}")
    private String yumengListLockMenuCode; //名单锁列表

    @Value("${yumeng.menucode.manageUser}")
    private String yumengManageUserMenuCode; //管户列表

    @Value("${yumeng.menucode.customerList}")
    private String yumengCustomerListMenuCode; //客户列表

    @Value("${yumeng.menucode.highSeasList}")
    private String yumengHighSeasListMenuCode; //客户列表

    @Value("${yumeng.menucode.salesProcess}")
    private String yumengSalesProcessMenuCode; //销售流程管理列表

    //呼叫统计
    @Value("${yumeng.menucode.callStatistics}")
    private String yumengCallStatisticsMenuCode;

    @Value("${yumeng.menucode.bmdInsuranceList:bermuda-application-form-information-management}")
    private String bmdInsuranceListMenuCode;

    @Value("${yumeng.menucode.CusServiceCallStatistics:kefuhuawutongji}")
    private String yumengCusServiceCallStatisticsMenuCode;

    @Value("${yumeng.menucode.myWorkOrder:wodegongdan}")
    private String yumengMyWorkOrder;
    @Value("${yumeng.menucode.serviceSummary:fuwuxiaojietongji}")
    private String yumengServiceSummary;

    @Value("${saas.menucode.myWorkOrder:wodegongdansaas}")
    private String saasMyWorkOrder;
    @Value("${saas.menucode.serviceSummary:fuwuxiaojietongjisaas}")
    private String saasServiceSummary;

    @Value("${saas.menucode.wechatContact:weixinjilusaas}")
    private String saasWechatContact;

    @Value("${yumeng.menucode.wechatContact:weixinjilu}")
    private String yumengWechatContact;

    @Value("${yumeng.menucode.newQwContact:ceTelephone_qwChat}")
    private String yumengNewQwContact;

    @Value("${yumeng.menucode.file2Url:bermuda_file2url}")
    private String bermudaFile2UrlMenuCode;

    @Value("${yumeng.menucode.file2UrlSaas:bermuda_file2urlsaas}")
    private String saasBermudaFile2UrlMenuCode;
}
