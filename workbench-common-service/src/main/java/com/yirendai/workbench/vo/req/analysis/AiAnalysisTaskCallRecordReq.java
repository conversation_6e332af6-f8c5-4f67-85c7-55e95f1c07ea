package com.yirendai.workbench.vo.req.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.support.Query;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * ai分析任务通话记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AiAnalysisTaskCallRecord对象", description = "ai分析任务通话记录")
public class AiAnalysisTaskCallRecordReq extends Query implements Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "ai分析任务Id ai_analysis_task表ID")
    private Long analysisTaskId;

    @ApiModelProperty(value = "记录类型 0-通讯记录分析，1-客户分析")
    private Integer recordType;

    //客户姓名
    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户ID")
    private String customerId;


    @ApiModelProperty(value = "员工编号 ")
    private String plannerNo;

    @ApiModelProperty(value = "员工姓名")
    private String agentName;

    @ApiModelProperty(value = "电话唯一标识")
    private String uuid;

    @ApiModelProperty(value = "1呼入，2呼出")
    private Integer callType;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "部门名称")
    private String agentDept;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;


    @ApiModelProperty(value = "AI分析状态 0未开始，1进行中，2已完成,3失败")
    private List<Integer> status;

    @ApiModelProperty(value = "AI分析完成时间")
    private LocalDateTime analysisTime;

    @ApiModelProperty(value = "是否已删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "场景表ID ai_analysis_config_scene表ID" ,hidden = true)
    private List<Long> configSceneIds;

    @ApiModelProperty(value = "场景结果项表ID  ai_analysis_config_scene_result表ID",hidden = true)
    private List<Long> configSceneResultIds;

    @ApiModelProperty(value = "搜索场景和结果ID" )
    private List<String> configIds;

}
