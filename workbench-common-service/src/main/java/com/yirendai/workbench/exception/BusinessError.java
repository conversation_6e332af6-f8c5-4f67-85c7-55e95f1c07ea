package com.yirendai.workbench.exception;

import org.springblade.core.tool.api.IResultCode;

public enum BusinessError implements IResultCode {
    /**
     * xxx错误
     */
    DEMO_ERROR(1, "xxx错误"),
    /**
     * 必填参数为空
     */
    PARAM_NULL_ERROR(2, "必填参数为空"),
    /**
     * fpc用户已经存在
     */
    FPC_USER_EXIST(3, "FPC工号已存在，请检查后重试"),

    FPC_ASSIGNED(4, "该客户D类跟进场景已分派过FPC，不可重复创建，请于列表中查询"),
    USER_NOT_EXIST(5, "当前客户ID信息有误，请检查后再试，如为外部用户可使用人工分派FPC"),
    DATA_NOT_EXIST(6, "记录不存在"),
    DATA_IS_INVALID(7, "记录已经无效"),
    GET_STAFF_ERROR(8, "获取优选顾问失败"),
    NO_FPC_RECEPTION_DATETIME(9, "所选时段未生成，请检查后再试"),
    UPDATE_ASSIGN_COUNT_ERROR(10, "联动修改派单计数表失败，请联系开发人员"),
    PARAM_ERROR(11, "参数错误"),

    FPC_ASSIGN_ERROR(30, "该FPC尚未生成日程，无法分派FPC"),

    NO_PROCESSED_CONTENT(31, "处理后的内容为空"),

    EXCEED_FILE_MAX_LINES(37, "文件超过最大行数"),

    NOT_LOGIN(95, "未登录或登录过期"),

    PLANNER_USER_BIND_NOT_EXIST(40, "绑定关系不存在"),

    CALL_TAGLIB_INTERFACE_EXCEPTION(41, "标签服务接口调用异常"),
    DEPT_ERROR(42, "场景小组不合法"),
    TAGLIB_ERROR(43, "标签不存在"),
    SCENE_ERROR(44, "场景编码已存在"),
    STRATEGY_ERROR(45, "策略编码已存在"),
    DEPT_ID_ERROR(46, "小组id异常，应该是数字"),
    DEPT_WEIGHT_ERROR(47, "小组权重异常，应该是数字"),
    TAG_NAME_NULL(48, "标签名为空"),
    STRATEGY_NOT_EXIST(45, "策略编码不存在"),
    GROUP_NOT_EXIST(409, "未获取到小组信息"),

    ERROR_FILE_PARSING(38, "文件解析失败"),
    ERROR_FILE_IMPORT(39, "文件导入失败"),
    LIST_LOCK_USER_NOT_EXIST(40, "未查询到客户ID，请检查后重新申请"),
    USER_NOT_EXIST_IN_DB(41, "客户当前未在库，不可申请"),
    USER_ALREADY_LIST_LOCK_LOCKED(42, "名单锁锁定，不可申请"),
    USER_ALREADY_UNLOCKED(43, "客户未被锁定"),
    USER_MANAGER_USER_LOCKED(44, "客户被管户锁定"),
    USER_MANAGER_USER_OWN_OTHER_GROUP(45, "客户当前归属其他组别，不可申请管户"),
    USER_MANAGER_USER_OWN_SAME_GROUP_MONTH_LIMIT(46, "客户本月被管户次数已达到上限，不可申请"),
    USER_NOT_MANAGER_USER_LOCKED(47, "客户非管户锁定"),
    CRM_USER(48, "用户已存在，用户类型为CRM"),
    DEVELOP_PROTECTION_PERIOD(49, "用户已存在(开发保护期内)"),
    USER_LOCKED(50, "用户已锁定（管户/名单锁"),
    INTRODUCER_USER_NOT_EXIST(51, "未查询到介绍人ID，请检查后重试"),
    REFERRAL_USER_REGISTER(52, "用户已注册，申请失败"),
    REFERRAL_USER_EXIST(53, "用户已存在，申请失败"),
    NOT_GROUP_ID(54, "不是小组id"),
    USER_ALLOCATION_LOCKED(55, "当前客户被名单锁/管户锁定，不可分配"),
    USER_OUTBOUND_LOCKED(56, "当前客户被名单锁/管户锁定，不可出库"),
    USER_NOT_EXIST_IN_BOUND(57, "客户当前未在库，不可分配"),
    USER_ALREADY_ALLOCATION_PLANNER(58, "客户已分配优选顾问,请在变更优选顾问处操作"),
    USER_NOT_INT_DEVELOP_CYCLE(59, "客户未在开发期内"),
    USER_GROUP_NAME_NOT_EQUAL(60, "客户所属组别不一致"),
    USER_NO_ALLOCATION_PLANNER(61, "客户未分配优选顾问,请在指定优选顾问处操作"),
    EXCHANGE_PANNER_NO_AUTH(62, "无当前用户优选顾问变更的权限"),
    USER_EXCHANGE_PANNER_LOCKED(63, "当前客户被名单锁/管户锁定，变更失败"),
    USER_EXCHANGE_PANNER_ERROR(64, "变更后优选顾问不能为当前优选顾问"),
    PLANNER_NOT_EXIST(65, "优选顾问不存在"),
    CAN_NOT_CHANGE_PLANNER_MONTH(66, "抱歉，本月您已变更过优选顾问，不能再次操作"),
    CAN_NOT_CHANGE_PLANNER(67, "抱歉，您暂时不能变更优选顾问"),
    CAN_NOT_CHANGE_PLANNER_TRY_THREE_DAYS_LATER(68, "当前无法变更优选顾问，请三天后重试"),
    PLANNER_NOT_RIGHT(69, "当前优选顾问姓名和工号不匹配，请重试"),
    SAME_PLANNER(70, "当前优选顾问和指定的为同一人，无需变更"),
    CAN_NOT_CHANGE_ASSIGN_PLANNER(71, "抱歉，您暂时不能变更为指定优选顾问"),
    PHONE_CAN_NOT_FIND_USER_ID(72, "当前手机号未查询到客户ID"),
    USER_ALREADY_MANAGE_USER_LOCKED(73, "客户已管户，不可申请"),
    LOCK_REASON_TOO_LONG(74, "锁定原因不能超过200个字符"),
    RECORD_TOO_MANY(75, "记录超过1万条"),
    LOCK_DAY_PATTERN_ERROR(76, "锁定天数必须是1到5位数字"),
    SCENE_NOT_EXIST(77, "场景信息不存在"),
    DELAY_END_DATE_MUST_GREATER_THAN_ORIGINAL_DEVELOP_END_DATE(78, "延长时间需大于原开发期结束时间"),
    PHONE_AND_RELATION_NOT_ALLOW_REPEAT(79, "手机号和关系不允许同时重复"),

    USER_NOT_EXIST_INBOUND(80, "客户当前未在库"),
    EXCEED_AGENT_LIMIT(81, "超过当前租户坐席数量上限：%s"),


    /**
     * 上传文件为空
     */
    FILE_IS_NULL(83, "上传文件为空"),
    /**
     * 保存文件为空
     */
    SAVE_FILE_ERROR(84, "保存文件为空"),
    /**
     * 任务类型有误
     */
    TASK_TYPE_ERROR(85, "任务类型有误"),

    /**
     * 任务不存在
     */
    TASK_IS_NLL(86, "任务不存在"),

    /**
     * 任务删除状态有误
     */
    TASK_NOT_DELETED(87, "任务已开始执行，不能删除"),

    NOT_FIND_USER(88, "未找到需要更新的用户"),
    USER_NOT_NORMAL_STATUS(89, "请选择正常的账号进行停用"),
    BATCH_STOP_USER_ERROR(90, "批量停用用户账号失败"),
    SUPER_ADMIN_NOT_ALLOW_OPERATION(91, "【超级管理员】账号不可分配角色/停用/删除"),
    ONLY_STOP_USER_CAN_DELETE(92, "仅停用的账号能删除"),

    TENANT_NOT_EXIST(93, "租户信息不存在"),

    PARAM_INVALID(94, "任务参数异常，无法继续执行"),

    REFERRAL_IS_NOT_EXIST(95, "转介绍信息不存在"),

    REFERRAL_IS_NOT_VALID_OR_NOT_VIRTUAL_ID(96, "已失效或不是虚拟id"),
    USER_DEPT_ERROR(95, "当前员工无归属组别，操作失败"),

    GROUP_DEPT_ERROR(96, "部门信息不存在"),

    USER_DEPT_COUNT_ERROR(97, "当前员工关联多个组别，操作失败"),

    REFERRAL_INFO_EXIST(98, "用户已存在转介绍记录，申请失败"),

    USER_NOT_BIND_REFERRAL_INTRODUCER(99, "介绍人与申请人无绑定关系，申请失败"),
    USER_HAS_CANCEL_INFO(100, "用户存在注销记录，申请失败"),
    USER_REFERRAL_SUBMIT_TIME_OUT(101, "已超过提交申请时效，申请失败"),

    PDF_SIGN_KEY_NOT_EXIST(98, "签名key不存在"),

    STRATEGY_USER_ADDITION_CONFIG_EXIST(99, "该用户已存在当前略策配置"),

    STRATEGY_USER_ADDITION_CONFIG_NOT_EXIST(100, "该用户不存在当前略策配置"),

    STRATEGY_USER_NOT_EXIST(101, "当前客户未在库"),

    CUSTOM_TEMPLATE_NOT_EXIST(102, "自定义模板不存在"),
    CUSTOM_TEMPLATE_NAME_EXIST(103, "模版名称已存在，不可重复添加"),
    AI_PLANNER_CHAT_CONTACT_NOT_EXIST(104, "AI语料信息不存在"),
    AI_SERVICE_SUMMARY_GENERATING(105, "AI服务小结生成中，请稍后再试"),
    AI_WORK_ORDER_GENERATING(106, "AI工单生成中，请稍后再试"),
    STRATEGY_TASK_IS_NOT_EXIST(107, "策略任务不存在"),

    CONFIG_DATA_ERROR(108, "配置数据异常"),
    SCENE_EXIST_CONFIG(109, "当前应用场景已存在配置数据"),

    WHITE_LIST_USER_EXIST(110, "客户已加入白名单，不可重复申请"),

    CUSTOMER_NOT_REGISTER(111, "未查询宜人注册信息，请检查后重试"),
    PHONE_AND_CUSTOMER_ID_IS_NULL(112, "请输入查询的客户ID或手机号"),


    AI_WORK_ORDER_COUNT_ERROR(107, "次数超过3次，请手动补充信息"),

    REFERRAL_USER_OUTBOUND_LOCKED(107, "当前客户被名单锁/管户锁定，申请失败"),

    PDF_CONVERT_TO_IMAGE_FAILED(113, "PDF转图片失败"),

    FILE_SHARE_LINK_SCENE_INVALID(114, "文件转链接使用场景非法"),

    FILE_SHARE_LINK_TYPE_INVALID(115, "文件转链接文件类型非法"),

    FILE_SHARE_LINK_ORIGIN_FILE_NOT_EXIST(116, "文件转链接原始文件不存在"),

    GET_WELFARE_ORDER_FAILED(117, "获取福利平台订单信息失败"),

    GET_WELFARE_ORDER_REAL_TIME_LOGISTICS_FAILED(118, "获取福利平台订单实时物流信息失败"),
    SALE_PROCESS_NODE_IS_NOT_EXIST(119, "销售流程节点不存在"),

    SALE_PROCESS_EDGE_NOT_EXIST(120, "销售流程边数据不存在"),

    NO_PERMISSION(121, "当前操作无权限"),

    SALE_PROCESS_NODE_CANNOT_RECONNECT(122, "下级节点不能回连"),

    SALE_PROCESS_START_NODE_NOT_END(123, "结束节点不能为开始节点"),

    SALE_PROCESS_START_NODE_ONLY_ONE(124, "当前配置中已存在一个开始节点"),
    SALE_PROCESS_COPY_FAIL(125, "复制销售流程数据失败"),
    SALE_PROCESS_UPDATE_NODE_TEXT_ERROR(126, "更新流程node_text失败"),
    SALE_PROCESS_IS_PROCESSING(127, "该用户的流程正在处理中，请稍后再试"),
    SALE_PROCESS_IS_NOT_EXIST(128, "销售流程记录不存在"),
    MANUAL_FINISH_PROCESS_ERROR(129, "手动结束流程失败"),
    MANUAL_FINISH_PROCESS_STATUS_ERROR(130, "完成状态必须为1（已赢单）或2（已败单）"),
    SALE_PROCESS_IS_END(131, "销售流程已经结束"),
    MANUAL_ADVANCE_CONDITION_FAIL(132, "手动推进失败"),

    SALE_PROCESS_NODE_NOT_CONNECTED(133, "流程节点未正确连接"),
    SALE_PROCESS_NO_START_NODE(134, "流程中没有开始节点"),
    SALE_PROCESS_NO_END_NODE(135, "流程中没有结束节点"),
    SALE_PROCESS_MULTIPLE_START_NODES(136, "流程中存在多个开始节点"),
    SALE_PROCESS_ISOLATED_NODE(137, "流程中存在孤立节点"),
    SALE_PROCESS_NO_PATH_TO_END(138, "流程没有从开始节点到结束节点的路径");
    ;

    private int code;
    private String message;

    BusinessError(int code, String msg) {
        this.code = code;
        this.message = msg;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void throwBusinessException() {
        throw new BusinessException(this.code, this.message);
    }
}
