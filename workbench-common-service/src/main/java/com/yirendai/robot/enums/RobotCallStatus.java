package com.yirendai.robot.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

public enum RobotCallStatus {
    UNCALLED(0, "未拨打"),
    CALLING(1, "拨打中"),
    CALLED(2, "已接通"),
    UNCALLED_FAIL(3, "未接通"),
    OVER_LIMIT(4, "超频拦截");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    RobotCallStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RobotCallStatus getByCode(Integer code) {
        for (RobotCallStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
