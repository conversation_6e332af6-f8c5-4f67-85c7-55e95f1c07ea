package com.yirendai.robot.enums;

public enum RobotCallResult {
    UNKNOWN(0, "无"),
    NO_ANSWER(1, "无人接听"),
    SHUT_DOWN(2, "关机"),
    STOP_SERVICE(3, "停机"),
    NO_ANSWER_NUMBER(4, "空号"),
    REFUSE_CALL(5, "用户拒接"),
    LINE_BUSY(6, "线路忙"),
    INTERCEPT_LINE(7, "线路拦截"),
    SYSTEM_INTERCEPT(8, "系统拦截"),
    RECOVERY_ON_TIMER_EXPIRE( 9, "超时重试"),
    USER_BUSY(10, "用户忙"),
    NORMAL_CLEARING( 11, "正常结束"),
    OVER_LIMIT(12, "超频拦截");
    private Integer code;
    private String message;
    RobotCallResult(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    public Integer getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }

    public static RobotCallResult getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        RobotCallResult[] results = RobotCallResult.values();
        for (RobotCallResult result : results) {
            if (code.equals(result.getCode())) {
                return result;
            }
        }

        return null;
    }
}
