/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 外呼通话任务客户号码查询入参
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@ApiModel(value = "外呼通话任务客户号码查询入参", description = "外呼通话任务客户号码查询入参")
public class RobotCallTasksCustomerQueryReqVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "被叫号码")
    private String calledNumber;

    @ApiModelProperty(value = "机器人ID")
    private Long robotId;

    @ApiModelProperty(value = "任务ID，关联到ai_outbound_call_tasks表")
    private Long taskId;

    @ApiModelProperty(value = "呼叫状态（0:未拨打, 1:拨打中, 2:已接通, 3:未接通, 4:超频拦截）")
    private Integer callStatus;

    @ApiModelProperty(value = "呼叫结果（未接通原因）（0:无, 1:无人接听, 2:关机, 3:停机, 4:空号, 5:用户拒接, 6:线路忙, 7:线路拦截, 8:系统拦截, 12:超频拦截）")
    private Integer callResult;

    @ApiModelProperty(value = "意向名称")
    private String intentionDes;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "客户ID")
    private String customerId;

    @ApiModelProperty(value = "用户task_customer id")
    private String id;

    @ApiModelProperty(value = "机器人变量列表" ,hidden = true)
    private List<RobotAttributeVariableVO> robotAttributeVariableList;


}
