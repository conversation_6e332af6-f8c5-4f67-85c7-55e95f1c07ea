package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 编辑频次规则入参
 */
@Data
@ApiModel(value = "编辑频次规则入参", description = "编辑频次规则入参")
public class RobotCallFrequencyRuleUpdateReq extends RobotCallFrequencyRuleCreateReq {

    @ApiModelProperty(value = "规则ID", required = true)
    @NotNull(message = "规则ID不能为空")
    private Long id;
}
