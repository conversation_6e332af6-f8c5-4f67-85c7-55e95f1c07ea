package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.support.Query;

/**
 * 频次规则分页查询入参
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "频次规则查询入参", description = "频次规则查询入参")
public class RobotCallFrequencyRuleQueryReq extends Query {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机器人名称模糊搜索")
    private String robotName;

    @ApiModelProperty(value = "规则状态 1-启用 0-停用")
    private Integer status;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private String tenantId;
}
