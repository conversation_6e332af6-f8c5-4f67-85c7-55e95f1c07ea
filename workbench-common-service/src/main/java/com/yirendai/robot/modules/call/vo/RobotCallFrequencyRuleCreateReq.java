package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Max;
import java.util.List;

/**
 * 新建频次规则入参
 */
@Data
@ApiModel(value = "新建频次规则入参", description = "新建频次规则入参")
public class RobotCallFrequencyRuleCreateReq {

    @ApiModelProperty(value = "规则名称（不传则默认为：一个自然月内，最多可拨打x次）", required = false)
    private String ruleName;

    @ApiModelProperty(value = "机器人ID集合", required = true)
    @NotEmpty(message = "请选择机器人")
    private List<Long> robotIds;

    @ApiModelProperty(value = "接通次数限制", required = true)
    @NotNull(message = "请输入频次限制")
    @Positive(message = "频次限制必须大于0")
    @Max(value = 999_999_999, message = "频次限制不能超过9位数")
    private Integer limitCount;

    @ApiModelProperty(value = "规则状态 1-启用 0-停用")
    private Integer status = 1;

    @ApiModelProperty(value = "备注")
    private String remark;
}
