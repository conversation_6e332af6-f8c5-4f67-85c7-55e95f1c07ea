package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 规则状态切换入参
 */
@Data
@ApiModel(value = "频次规则状态入参", description = "频次规则状态入参")
public class RobotCallFrequencyRuleStatusReq {

    @ApiModelProperty(value = "规则ID", required = true)
    @NotNull(message = "规则ID不能为空")
    private Long id;

    @ApiModelProperty(value = "规则状态 1-启用 0-停用", required = true)
    @NotNull(message = "规则状态不能为空")
    private Integer status;
}
