<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>BladeX-Biz</artifactId>
        <groupId>org.springblade</groupId>
        <version>2.9.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>planner-workbench</artifactId>
    <packaging>jar</packaging>
    <description>宜人理财师智能工作台</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <mybatis-plus-boot-starter.version>3.4.2</mybatis-plus-boot-starter.version>
        <okhttp.version>4.10.0</okhttp.version>
        <javacv.version>1.5.9</javacv.version>
        <bytedeco.classifier.macos-x86_64>macosx-x86_64</bytedeco.classifier.macos-x86_64>
        <bytedeco.classifier.macos-arm64>macosx-arm64</bytedeco.classifier.macos-arm64>
        <bytedeco.classifier.linux-x86_64>linux-x86_64</bytedeco.classifier.linux-x86_64>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yirendai.app</groupId>
            <artifactId>fortune-app-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-tenant</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-api-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>workbench-common-service</artifactId>
            <version>2.9.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-db</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>druid-spring-boot-starter</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>undertow-core</artifactId>
                    <groupId>io.undertow</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-undertow</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>undertow-servlet</artifactId>
                    <groupId>io.undertow</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>undertow-websockets-jsr</artifactId>
                    <groupId>io.undertow</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-auto</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-system-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-aggregation-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-dict-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-datascope</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-scope-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-excel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>easyexcel</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.18</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>3.0.2</version>
        </dependency>

        <!-- iText PDF 依赖 - 用于更好的中文支持 -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>kernel</artifactId>
            <version>7.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>forms</artifactId>
            <version>7.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>7.2.5</version>
            <type>pom</type>
        </dependency>
        <!-- zip4j - 用于创建带密码保护的ZIP文件 -->
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>2.11.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.22</version>
        </dependency>

        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.12.0</version>
        </dependency>

        <dependency>
            <groupId>cn.creditease.sbc</groupId>
            <artifactId>open-platform-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.2</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-gson</artifactId>
            <version>0.11.2</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>1.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
            <version>${okhttp.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv</artifactId>
            <version>1.5.9</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.5.0</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.commonmark</groupId>
            <artifactId>commonmark</artifactId>
            <version>0.17.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mybatis-plus-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.directwebremoting</groupId>
            <artifactId>dwr</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.vesoft</groupId>
            <artifactId>client</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-sleuth-zipkin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-http</artifactId>
        </dependency>
        <!-- MapStruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version> <!-- 请根据需要选择合适的版本 -->
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.3.2</version>
        </dependency>
        <!-- Spring Data MongoDB -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jaudiotagger</groupId>
            <artifactId>jaudiotagger</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-parsers-standard-package</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.18.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>
        <!-- Spring Data MongoDB -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.bytedeco</groupId>-->
        <!--            <artifactId>javacv-platform</artifactId>-->
        <!--            <version>1.5.9</version>-->
        <!--        </dependency>-->
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <deploy.type>dev</deploy.type>
            </properties>
            <dependencies>
                <!-- macosx-x86_64 -->
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>javacpp</artifactId>
                    <version>${javacv.version}</version>
                    <classifier>${bytedeco.classifier.macos-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>ffmpeg</artifactId>
                    <version>6.0-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.macos-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>openblas</artifactId>
                    <version>0.3.23-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.macos-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>opencv</artifactId>
                    <version>4.7.0-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.macos-x86_64}</classifier>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>dev2</id>
            <properties>
                <deploy.type>dev</deploy.type>
            </properties>
            <dependencies>
                <!-- macosx-arm64 -->
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>javacpp</artifactId>
                    <version>${javacv.version}</version>
                    <classifier>${bytedeco.classifier.macos-arm64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>ffmpeg</artifactId>
                    <version>6.0-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.macos-arm64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>openblas</artifactId>
                    <version>0.3.23-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.macos-arm64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>opencv</artifactId>
                    <version>4.7.0-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.macos-arm64}</classifier>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <deploy.type>test</deploy.type>
            </properties>
            <dependencies>
                <!-- linux-x86_64 -->
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>javacpp</artifactId>
                    <version>${javacv.version}</version>
                    <classifier>${bytedeco.classifier.linux-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>ffmpeg</artifactId>
                    <version>6.0-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.linux-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>openblas</artifactId>
                    <version>0.3.23-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.linux-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>opencv</artifactId>
                    <version>4.7.0-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.linux-x86_64}</classifier>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <deploy.type>prd</deploy.type>
            </properties>
            <dependencies>
                <!-- linux-x86_64 -->
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>javacpp</artifactId>
                    <version>${javacv.version}</version>
                    <classifier>${bytedeco.classifier.linux-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>ffmpeg</artifactId>
                    <version>6.0-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.linux-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>openblas</artifactId>
                    <version>0.3.23-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.linux-x86_64}</classifier>
                </dependency>
                <dependency>
                    <groupId>org.bytedeco</groupId>
                    <artifactId>opencv</artifactId>
                    <version>4.7.0-${javacv.version}</version>
                    <classifier>${bytedeco.classifier.linux-x86_64}</classifier>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.yirendai.WorkbenchAdminApplication</mainClass> <!-- 替换为你的实际主类 -->
                </configuration>
            </plugin>
        </plugins>
        <finalName>planner-workbench</finalName>
    </build>
</project>