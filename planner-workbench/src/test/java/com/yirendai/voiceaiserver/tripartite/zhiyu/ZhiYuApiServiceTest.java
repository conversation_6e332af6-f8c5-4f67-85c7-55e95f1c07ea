package com.yirendai.voiceaiserver.tripartite.zhiyu;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.HitTestingResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class ZhiYuApiServiceTest {


    private ZhiYuApiService zhiYuApiService;

    @BeforeEach
    public void setUp() {
        RestTemplate restTemplate = new RestTemplate();
        zhiYuApiService = new ZhiYuApiService( );
        zhiYuApiService.restTemplate = restTemplate;
    }


    @Test
    public void testHitTesting_ReturnsExpectedResponse() {
        // Arrange
        String query = "2024-12-03日新闻";
        R<HitTestingResponse> response = zhiYuApiService.hitTesting(query);
        log.info("res={}",response);
    }
}
