package com.yirendai.workbench.util;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class AesUtilTest {

    @Test
    public void testEncryptDecrypt() throws Exception {
        String key = "ads@SAc";
        String data = "{\"code\":\"0\",\"success\":true,\"message\":null,\"time\":1747119495751,\"data\":{\"customPhone\":[{\"phone\":\"153****5802\",\"aesPhone\":\"cRrna0hWsHdZsI4YTZ0G/g==\",\"defaultPhone\":true},{\"phone\":\"185****5267\",\"aesPhone\":\"lj/07dpF4qm2ehmgdjjaOw==\",\"defaultPhone\":false},{\"phone\":\"134****0502\",\"aesPhone\":\"CadKN0L5wvE3LSWJd4ij/w==\",\"defaultPhone\":false}],\"name\":\"韩颖\",\"customerId\":354019},\"url\":null}";
        String encValue = KaishiAESUtil.encrypt(data, key);

        String decData = KaishiAESUtil.decrypt(encValue, key);
        Assert.assertEquals(data, decData);
    }
}
