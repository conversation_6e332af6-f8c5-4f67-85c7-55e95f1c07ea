package com.yirendai.workbench.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yirendai.workbench.wrapper.dto.ChannelPagedResponse;
import org.junit.jupiter.api.Test;
import org.springblade.core.tool.api.R;

import java.util.Map;

public class JsonUtilExtTest {
    @Test
    public void parseOrderResp(){
        String resp = "{\"code\":0,\"data\":{\"current\":1,\"records\":[{\"orderId\":\"1234567890\",\"goodsName\":\"商品0\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.6\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567891\",\"goodsName\":\"商品1\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567892\",\"goodsName\":\"商品2\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567893\",\"goodsName\":\"商品3\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567894\",\"goodsName\":\"商品4\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567895\",\"goodsName\":\"商品5\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567896\",\"goodsName\":\"商品6\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567897\",\"goodsName\":\"商品7\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567898\",\"goodsName\":\"商品8\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"},{\"orderId\":\"1234567899\",\"goodsName\":\"商品9\",\"actualPayAmount\":100,\"orderTime\":\"2025-05-19T17:39:37.601\",\"payTime\":\"2025-05-19T17:39:37.601\",\"remark\":\"备注\"}],\"size\":10,\"total\":100},\"msg\":null,\"success\":false}";
        R<ChannelPagedResponse<Map<String, Object>>> result = JsonUtilExt.jsonToBean(resp,
                new TypeReference<R<ChannelPagedResponse<Map<String, Object>>>>() {});
        System.out.println(result.getCode());
        System.out.println(result.getData().getRecords());
    }
}
