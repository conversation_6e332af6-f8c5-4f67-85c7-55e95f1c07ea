package com.yirendai.workbench.model.callcenter;

import com.yirendai.workbench.model.SaasImportUserEncryptData;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.KaishiAESUtil;
import com.yirendai.workbench.util.Md5Util;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class SaasImportUserEncryptDataTest {

    @Test
    public void testEnc(){
        String key = "hD5nL9qR2y";
        SaasImportUserEncryptData encryptData = new SaasImportUserEncryptData();
        String userData = "[{\"phone\":\"13655228810\",\"email\":\"<EMAIL>\",\"idNo\":\"110100200110219870\",\"gender\":\"1\",\"weChat\":\"wx001\",\"remark\":\"备注信息\",\"customerName\":\"赵小小\"},{\"phone\":\"13655226611\",\"email\":\"<EMAIL>\",\"idNo\":\"110100200110219871\",\"gender\":\"2\",\"weChat\":\"wx002\",\"remark\":\"备注信息，随便写点什么\",\"customerName\":\"刁冰冰\"}]";
        encryptData.setEncryptData(KaishiAESUtil.encrypt(userData, key));
        encryptData.setRequestTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        encryptData.setSign(Md5Util.digest(encryptData.getEncryptData() + encryptData.getRequestTime()));
        System.out.println(JsonUtilExt.beanToJson(encryptData));
    }
}
