package com.yirendai.workbench.model.callcenter;

import com.yirendai.workbench.enums.callcenter.CustomField;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class CustomFieldQueryTest {

    private CustomFieldQuery customFieldQuery;

    @BeforeEach
    void setUp() {
        customFieldQuery = new CustomFieldQuery();

    }

    @Test
    void testInitQueryParam_InputParamIsBlank() {
        // 输入参数为空
        customFieldQuery.initQueryParam("");
        customFieldQuery.setFieldType(CustomField.FieldType.STRING);
        assertNull(customFieldQuery.getFieldQueryParam());
    }

    @Test
    void testInitQueryParam_InputParamIsValidJsonArray() {
        // 输入参数为有效的 JSON 数组
        String inputParam = "[\"value1\", \"value2\"]";
        customFieldQuery.setFieldType(CustomField.FieldType.MULTI_SELECTOR);

        customFieldQuery.initQueryParam(inputParam);
        List params = (List)customFieldQuery.getFieldQueryParam();
        assertEquals("value1", params.get(0));
        assertEquals("value2", params.get(1));
    }

    @Test
    void testInitQueryParam_InputParamIsValidJsonArrayNumber() {
        // 输入参数为有效的 JSON 数组
        String inputParam = "[1, 2]";
        customFieldQuery.setFieldType(CustomField.FieldType.NUMBER);

        customFieldQuery.initQueryParam(inputParam);
        List params = (List)customFieldQuery.getFieldQueryParam();
        assertEquals(new BigDecimal("1"), params.get(0));
        assertEquals(new BigDecimal("2"), params.get(1));
    }

    @Test
    void testInitQueryParam_InputParamIsValidJsonArrayDateTime() {
        // 输入参数为有效的 JSON 数组
        String inputParam = "[\"2012-05-11 12:11:09\", \"2012-06-11 12:21:09\"]";
        customFieldQuery.setFieldType(CustomField.FieldType.DATETIME);

        customFieldQuery.initQueryParam(inputParam);
        List params = (List)customFieldQuery.getFieldQueryParam();

        LocalDateTime dateTime1 = LocalDateTime.parse("2012-05-11 12:11:09", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime dateTime2 = LocalDateTime.parse("2012-06-11 12:21:09", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        assertEquals(dateTime1, params.get(0));
        assertEquals(dateTime2, params.get(1));

    }

    @Test
    void testInitQueryParam_InputParamIsValidJsonObject() {
        // 输入参数为有效的 JSON 对象
        String inputParam = "value";
        customFieldQuery.setFieldType(CustomField.FieldType.SINGLE_SELECTOR);

        customFieldQuery.initQueryParam(inputParam);

        assertEquals("value", customFieldQuery.getFieldQueryParam());
    }

    @Test
    void testInitQueryParam_InputParamIsInvalidJson() {
        // 输入参数为无效的 JSON 字符串
        String inputParam = "invalidJson";
        customFieldQuery.setFieldType(CustomField.FieldType.STRING);

        customFieldQuery.initQueryParam(inputParam);

        assertEquals("invalidJson", customFieldQuery.getFieldQueryParam());
    }
}