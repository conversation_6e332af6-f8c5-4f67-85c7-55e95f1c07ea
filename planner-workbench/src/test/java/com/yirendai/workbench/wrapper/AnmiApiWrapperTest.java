package com.yirendai.workbench.wrapper;

import com.google.common.collect.Maps;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.anmi.AnMiAESUtils;
import com.yirendai.workbench.wrapper.dto.anmi.AnmiEncryptRequest;
import com.yirendai.workbench.wrapper.dto.anmi.AnmiSendSmsRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Map;
import java.util.SortedMap;

import static com.yirendai.workbench.util.anmi.AnMiAESUtils.*;

@Slf4j
public class AnmiApiWrapperTest {

    String accessKeyId = "ef3b1d5d388e41d29802787d7dec0249";
    String accessKeySecret = "ec004ccca6d9420a96c7c01e48bf6c7f";
    @Test
    public void testSendSms() throws Exception {
        AnmiSendSmsRequest sendSmsRequest = AnmiSendSmsRequest.builder()
            .solt(0)
            .imei("860690072081679")
            .content("你好，这是通过安米发送的短信")
            .targetNumber("1")
            .uuid("sms_uuid_123456")
            .build();

        String url ="http://yr-work-phone-manage-web.yrgzsjgl.paas.test/palm/api/open/v2/push/send/sms";
        String params = JsonUtilExt.beanToJson(sendSmsRequest);
        log.info("指定设备发短信, url:{} param：{}", url, params);

        String encryptContent = AnMiAESUtils.encrypt(params, accessKeyId);

        AnmiEncryptRequest encryptRequest = new AnmiEncryptRequest(encryptContent);
        Map<String, String> headerParams = initHeaderParams(encryptRequest, "");
        String requestBody = JsonUtilExt.beanToJson(encryptRequest);
        //打印请求参数，url， header
        log.info("请求参数：{}", requestBody);
        log.info("请求url：{}", url);
        StringBuilder headerStr = new StringBuilder("请求header：\n");
        headerParams.forEach((key, value) -> headerStr.append(key).append(":").append(value).append("\n"));
        log.info(headerStr.toString());

    }

    private Map<String, String> initHeaderParams(Object request, String tenantId) throws Exception {
        SortedMap contentMap = beanToMap(request);
        String content = getMd5Content(contentMap, accessKeySecret);
        SortedMap map = getCommnMap(accessKeyId);
        String sign = getSign(content, map, accessKeySecret);
        Map<String, String> headerParams = Maps.newHashMap();
        headerParams.put(X_APP_AUTHORIZATION, X_APP_AUTHORIZATION_HEAD + accessKeyId);
        headerParams.put(X_APP_KEY, accessKeyId);
        headerParams.put(X_APP_TIMESTAMP, String.valueOf( map.get(X_APP_TIMESTAMP)));
        headerParams.put(X_APP_NONCE, String.valueOf( map.get(X_APP_NONCE)));
        headerParams.put(CONTENT_MD5, content);
        headerParams.put(SIGN, sign);
        return headerParams;
    }
}
