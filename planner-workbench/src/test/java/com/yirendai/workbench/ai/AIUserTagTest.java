package com.yirendai.workbench.ai;

import javax.annotation.Resource;

import com.yirendai.WorkbenchAdminApplication;
import com.yirendai.voiceaiserver.service.IAiUserTagCategoryService;
import com.yirendai.workbench.entity.AiUserTagCategory;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class AIUserTagTest {
//    @Resource
//    private IAiUserTagCategoryService aiUserTagCategoryService;
//
//    @Test
//    public void savetagCategory() {
//        AiUserTagCategory aiUserTagCategory = new AiUserTagCategory();
//        aiUserTagCategory.setCategoryName("普通标签");
//        aiUserTagCategory.setMatchDes("");
//        aiUserTagCategory.setUserType(1);
//        aiUserTagCategory.setParentId(0L);
//        aiUserTagCategory.setMultiSelect(1);
//        aiUserTagCategory.setIsDeleted(0);
//        aiUserTagCategoryService.save(aiUserTagCategory);
//
//
//    }
}

