package com.yirendai.workbench.service.callcenter;

import com.yirendai.workbench.entity.callcenter.CallcenterCustomFields;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class FieldDefinitionSortTest {

    @Test
    public void testSortMap() {
        List<CallcenterCustomFields> fieldDefinitions = new ArrayList<>();
        CallcenterCustomFields callcenterCustomFields1 = new CallcenterCustomFields();
        callcenterCustomFields1.setSortOrder(10).setFieldName("f1");
        fieldDefinitions.add(callcenterCustomFields1);
        CallcenterCustomFields callcenterCustomFields2 = new CallcenterCustomFields();
        callcenterCustomFields2.setSortOrder(8).setFieldName("f2");
        fieldDefinitions.add(callcenterCustomFields2);
        CallcenterCustomFields callcenterCustomFields3 = new CallcenterCustomFields();
        callcenterCustomFields3.setSortOrder(20).setFieldName("f3");
        fieldDefinitions.add(callcenterCustomFields3);
        CallcenterCustomFields callcenterCustomFields4 = new CallcenterCustomFields();
        callcenterCustomFields4.setSortOrder(13).setFieldName("f4");
        fieldDefinitions.add(callcenterCustomFields4);

        Map<String, CallcenterCustomFields> map = fieldDefinitions.stream()
                .sorted(Comparator.comparing(CallcenterCustomFields::getSortOrder))
                .collect(Collectors.toMap(CallcenterCustomFields::getFieldName,
                        Function.identity(),
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new));

        map.forEach((k, v) -> {
            System.out.println(k + ":" + v.getSortOrder());
        });
    }
}
