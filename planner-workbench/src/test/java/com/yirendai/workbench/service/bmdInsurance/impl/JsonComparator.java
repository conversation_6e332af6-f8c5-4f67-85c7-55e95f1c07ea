package com.yirendai.workbench.service.bmdInsurance.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class JsonComparator {

    /**
     * 比较两个 JSONObject 是否完全一致
     */
    public static boolean isEqual(JSONObject json1, JSONObject json2) {
        if (json1 == null || json2 == null) {
            return json1 == json2;
        }

        if (json1.size() != json2.size()) {
            return false;
        }

        for (String key : json1.keySet()) {
            if (!json2.containsKey(key)) {
                return false;
            }

            Object val1 = json1.get(key);
            Object val2 = json2.get(key);

            if (!compareValues(val1, val2)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 递归比较两个值是否一致
     */
    private static boolean compareValues(Object val1, Object val2) {
        if (val1 == null || val2 == null) {
            return val1 == val2;
        }

        // 如果是 JSONObject，递归比较
        if (val1 instanceof JSONObject && val2 instanceof JSONObject) {
            return isEqual((JSONObject) val1, (JSONObject) val2);
        }

        // 如果是 JSONArray，比较每个元素
        if (val1 instanceof JSONArray && val2 instanceof JSONArray) {
            return compareArrays((JSONArray) val1, (JSONArray) val2);
        }

        // 其他情况直接 equals 比较
        return val1.equals(val2);
    }

    /**
     * 比较两个 JSONArray 是否一致
     */
    private static boolean compareArrays(JSONArray arr1, JSONArray arr2) {
        if (arr1.size() != arr2.size()) {
            return false;
        }

        for (int i = 0; i < arr1.size(); i++) {
            Object item1 = arr1.get(i);
            Object item2 = arr2.get(i);

            if (!compareValues(item1, item2)) {
                return false;
            }
        }

        return true;
    }

    // ================== 示例测试 ==================

    public static void main(String[] args) {
        String jsonStr1 = "{\n" +
                "  \"name\": \"张三\",\n" +
                "  \"age\": 25,\n" +
                "  \"address\": {\n" +
                "    \"city\": \"北京\",\n" +
                "    \"street\": \"长安街\"\n" +
                "  },\n" +
                "  \"email\": \"<EMAIL>\",\n" +
                "  \"travelInfoList\": [\n" +
                "    {\"city\": \"上海\", \"country\": \"中国\"},\n" +
                "    {\"city\": \"纽约\", \"country\": \"美国\"}\n" +
                "  ]\n" +
                "}";

        String jsonStr2 = "{\n" +
                "  \"name\": \"张三\",\n" +
                "  \"age\": 25,\n" +
                "  \"address\": {\n" +
                "    \"city\": \"北京\",\n" +
                "    \"street\": \"长安街\"\n" +
                "  },\n" +
                "  \"email\": \"<EMAIL>\",\n" +
                "  \"travelInfoList\": [\n" +
                "    {\"city\": \"上海\", \"country\": \"中国\"},\n" +
                "    {\"city\": \"纽约\", \"country\": \"美国\"}\n" +
                "  ]\n" +
                "}";

        JSONObject obj1 = JSON.parseObject(jsonStr1);
        JSONObject obj2 = JSON.parseObject(jsonStr2);

        boolean result = isEqual(obj1, obj2);
        System.out.println("两个 JSON 是否一致: " + result);
    }
}
