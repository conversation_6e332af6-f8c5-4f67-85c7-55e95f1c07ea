package com.yirendai.workbench.service.callcenter;

import com.yirendai.workbench.service.callcenter.impl.CallcenterWechatCustomerMappingServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基于真实客户备注数据的客户ID提取功能单元测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("真实客户备注数据ID提取测试")
class RealDataCustomerIdExtractionTest {

    @InjectMocks
    private CallcenterWechatCustomerMappingServiceImpl service;

    private Method extractAllCustomerIdsFromRemarkMethod;

    @BeforeEach
    void setUp() throws Exception {
        extractAllCustomerIdsFromRemarkMethod = CallcenterWechatCustomerMappingServiceImpl.class
                .getDeclaredMethod("extractAllCustomerIdsFromRemark", String.class);
        extractAllCustomerIdsFromRemarkMethod.setAccessible(true);
    }

    private List<String> extractCustomerId(String remark) throws Exception {
        return (List<String>) extractAllCustomerIdsFromRemarkMethod.invoke(service, remark);
    }


    @ParameterizedTest
    @DisplayName("测试多客户ID提取的真实数据")
    @CsvSource({
            // 备注, 期望的客户ID（用分号分隔多个ID）
            "'N林杏贤254542理44余谊廷250729', '254542;250729'",
            "'刘宏20009559师56', '20009559'", // 年龄56被排除
            "'张朝霞9514356理15105297695', '9514356'", // 手机号被排除
            "'田文龙71男13901034641', ''", // 手机号被排除
            "'刘巧灵28291831 理13631562267', '28291831'", // 手机号被排除
            "'李金飞18719274044理71678770', '71678770'", // 手机号被排除
            "'袁亚丽13714929634理70464546', '70464546'", // 手机号被排除
            "'姜阳69273259理13504989989', '69273259'", // 手机号被排除
            "'A1,李明全代李华程文秀，85822600', '85822600'",
            "'刘久玲15601230926理81029321', '81029321'", // 手机号被排除
            "'杨铁春20338682师49', '20338682'", // 年龄49被排除
            "'张朝霞9514356理15105297695', '9514356'", // 手机号被排除
            "'xj洪加宽31539464师66岁', '31539464'", // 年龄66被排除
            "'刘宏20009559师56', '20009559'", // 年龄56被排除
            "'李康应10349470 师50', '10349470'", // 年龄50被排除
            "'N常迪1000207017理另1005662089', '1000207017;1005662089'",
            "'客户滕建全48岁3809647  8.28', '3809647'", // 年龄48被排除
            "'李伟杰15086816269理27179884', '27179884'", // 手机号被排除
            "'国文10346158师58💰 10*5+5*10', '10346158'", // 年龄58被排除
            "'余云飞13016373理18768206030', '13016373'", // 后面的手机号被排除
            "'赵顺娥18636413568理1000160767', '1000160767'", // 手机号被排除
            "'陈群65158123师49', '65158123'", // 年龄49被排除
            "'李子秀64997704女58师', '64997704'", // 年龄58被排除
            "'朱振华58670978师58', '58670978'", // 年龄58被排除
            "'N陈曦1509683理另2681570', '1509683;2681570'",
            "'周康如5527542师70', '5527542'", // 年龄70被排除
            "'宜人女52何莉14886619', '14886619'", // 年龄52被排除
            "'宜人女61袁玉芬14568291', '14568291'", // 年龄61被排除
            "'宜人女54伍本翠14116851', '14116851'", // 年龄54被排除
            "'宜人55男张子成11575737固守', '11575737'", // 年龄55被排除
            "'毛超美5091060师65', '5091060'", // 年龄65被排除
            "'宜人女28韩雪1010113623', '1010113623'", // 年龄28被排除
            "'宜人女34蒋笑楠1010004941', '1010004941'", // 年龄34被排除
            "'N林杏贤254542理44余谊廷250729', '254542;250729'", // 年龄44被排除
            "'宜人女41杨艳丽1010004017', '1010004017'", // 年龄41被排除
            "'陈斐 ，男35，84392558', '84392558'", // 年龄35被排除
            "'宜人女38刘运嘉10020915', '10020915'", // 年龄38被排除
            "'54李会萍11723106', '11723106'", // 年龄54被排除
            "'宜人女45刘辉1000714473', '1000714473'", // 年龄45被排除
            "'42岁牛福英1000238845', '1000238845'", // 年龄42被排除
            "'宜人男32 于小祥1000205553', '1000205553'", // 年龄32被排除
            "'梁玉珍45女46531968', '46531968'", // 年龄45被排除
            "'宜人女49毕永智1000176201王先生负', '1000176201'", // 年龄49被排除
            "'宜人女38黄圣婴1000250371', '1000250371'", // 年龄38被排除
            "'宜人女36张红1000245173', '1000245173'", // 年龄36被排除
            "'宜人女35左倩1000209243', '1000209243'", // 年龄35被排除
            "'ok低赵璐13605382女A师', '13605382'",
            "'宜人女42赵小燕1000205035', '1000205035'", // 年龄42被排除
            "'89059756 蔡少娟', '89059756'",
            "'邵彩霞12431246师59', '12431246'", // 年龄59被排除
            "'792董婷婷44岁2765370', '2765370'", // 年龄44被排除
            "'王婷婷33女27549154赵颖2w一年', '27549154'", // 年龄33被排除
            "'李世葵50姐7220361生日9.26', '7220361'", // 年龄50被排除
            "'陶珏女32女 83862463', '83862463'", // 年龄32被排除
            "'任意斌43男5506461', '5506461'", // 年龄43被排除
            "'李洪艳43姐29496319保险', '29496319'", // 年龄43被排除
            "'谢雅莉52女1000160679', '1000160679'", // 年龄52被排除
            "'王翠玉49姐6999049', '6999049'", // 年龄49被排除
            "'秦晓会35女1000216751', '1000216751'", // 年龄35被排除
            "'陶桂平48女1000049269流转进来的', '1000049269'", // 年龄48被排除
            "'黄中基42姐88624981', '88624981'", // 年龄42被排除
            "'袁昌斌9304190师49', '9304190'", // 年龄49被排除
            "'刘维霞53 女 1000229159秦皇岛', '1000229159'", // 年龄53被排除
            "'吉文红53姐34963834d', '34963834'", // 年龄53被排除
            "'宜人女44赵轶芳12614326', '12614326'", // 年龄44被排除
            "'宜人女43张小芬12454995', '12454995'", // 年龄43被排除
            "'宜人女56王涓12296374固收', '12296374'", // 年龄56被排除
            "'张力梅13614517558理64930028', '64930028'", // 手机号被排除
            "'宜人男37储晓铭11322141太平人寿', '11322141'", // 年龄37被排除
            "'宜人女30孙菲1001129959', '1001129959'", // 年龄30被排除
            "'宜人男48徐方东1000725045', '1000725045'", // 年龄48被排除
            "'Xj顾惠东69347427师B～首笔', '69347427'",
            "'宜人女53刘惠芳1000711581', '1000711581'", // 年龄53被排除
            "'A在库濮珉74900223上海', '74900223'",
            "'宜人女36白亚辉1000552387', '1000552387'", // 年龄36被排除
            "'宜人女34崔旭冉1000229027', '1000229027'" // 年龄34被排除
    })
    void testRealCustomerRemarkDataMultipleIds(String remark, String expectedIds) throws Exception {
        List<String> actualIds = extractCustomerId(remark);

        if (expectedIds.isEmpty()) {
            assertTrue(actualIds.isEmpty(),
                    String.format("备注: '%s' 应该没有客户ID，但实际提取出: %s", remark, actualIds));
        } else {
            List<String> expectedIdList = Arrays.asList(expectedIds.split(";"));
            assertEquals(expectedIdList.size(), actualIds.size(),
                    String.format("备注: '%s' 应该提取出 %d 个客户ID，但实际提取出 %d 个: %s",
                            remark, expectedIdList.size(), actualIds.size(), actualIds));

            // 验证每个期望的ID都被提取到了
            for (String expectedId : expectedIdList) {
                assertTrue(actualIds.contains(expectedId),
                        String.format("备注: '%s' 应该包含客户ID: '%s'，但实际提取出: %s",
                                remark, expectedId, actualIds));
            }

            // 验证提取的ID顺序（LinkedHashSet保持插入顺序）
            for (int i = 0; i < expectedIdList.size(); i++) {
                assertEquals(expectedIdList.get(i), actualIds.get(i),
                        String.format("备注: '%s' 第 %d 个客户ID顺序错误，期望: %s，实际: %s",
                                remark, i+1, expectedIdList.get(i), actualIds.get(i)));
            }
        }
    }

    @ParameterizedTest
    @DisplayName("测试应该被排除的年龄数据")
    @ValueSource(strings = {
            "田文龙71男13901034641", // 71是年龄，手机号也应该被排除，但可能提取到其他数字
            "高娅玲 51女", // 51是年龄，没有其他有效客户ID
            "A唐平  33 大额类固收", // 33是年龄，没有其他有效客户ID
            "宋小勇  先生  40 常州市", // 40是年龄，没有其他有效客户ID
            "张三，25岁", // 25是年龄
            "李四 30 男", // 30是年龄
            "王五，年龄45", // 45是年龄
            "赵六 60岁", // 60是年龄
            "50岁老人" // 50是年龄
    })
    void testAgeExclusionInRealData(String remark) throws Exception {
        List<String> extractedIds = extractCustomerId(remark);

        // 验证提取的ID不是明显的年龄数字
        for (String id : extractedIds) {
            try {
                int numId = Integer.parseInt(id);
                assertTrue(numId > 120 || id.length() >= 7,
                        String.format("从备注 '%s' 中提取的ID '%s' 不应该是年龄", remark, id));
            } catch (NumberFormatException e) {
                // 非数字ID，跳过年龄检查
            }
        }
    }

    @ParameterizedTest
    @DisplayName("测试手机号码排除逻辑")
    @ValueSource(strings = {
            "联系电话13812345678",
            "手机：15987654321",
            "电话号码18612345678",
            "田文龙71男13901034641", // 包含手机号
            "刘久玲15601230926理81029321", // 包含手机号，应该提取81029321
            "张力梅13614517558理64930028", // 包含手机号，应该提取64930028
            "高娃81258606理15247537391", // 包含手机号，应该提取81258606
            "李金飞18719274044理71678770" // 包含手机号，应该提取71678770
    })
    void testPhoneNumberExclusionInRealData(String remark) throws Exception {
        List<String> extractedIds = extractCustomerId(remark);

        // 验证提取的ID不是手机号码（11位且以1开头）
        for (String id : extractedIds) {
            assertFalse(id.length() == 11 && id.startsWith("1"),
                    String.format("从备注 '%s' 中提取的ID '%s' 不应该是手机号码", remark, id));
        }
    }


    @Test
    @DisplayName("测试多客户ID去重")
    void testMultipleCustomerIdDeduplication() throws Exception {
        // 测试重复客户ID的去重
        List<String> result = extractCustomerId("客户1234567，备注1234567，重要1234567");
        assertEquals(1, result.size());
        assertEquals("1234567", result.get(0));
    }


}