package com.yirendai.workbench.service.bmdInsurance.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yirendai.workbench.util.JsonChineseTranslatorHelper;
import org.junit.jupiter.api.Test;

import java.util.LinkedHashMap;
import java.util.Map;

import static com.yirendai.workbench.util.JsonChineseTranslatorHelper.*;

public class JsonChineseTranslatorHelperTest {
    @Test
    public void test() {
//            String jsonStr = "{\"commercialInterestsList\":[{\"cityAndCountry\":\"tbr城市和国家\",\"companyName\":\"tbr公司名称\",\"companyOfficial\":\"tbr公司官网\",\"investmentRatio\":\"12\",\"marketValue\":134}]}";
//            String jsonStr = "{\"healthStatus\":{\"bloodTransfusion\":true,\"deathRecordsList\":[{\"age\":51,\"deathAgeAndReason\":\"或死亡年龄和死因BBR\",\"familyMember\":\"家庭成员BBR\"}],\"deformity\":false,\"diseaseVOList\":[{\"age\":21,\"ageOfDeath\":41,\"ageOfLiving\":31,\"disease\":\"患病情况BBR\",\"relation\":\"与该家庭成员的关系BBR\"}],\"drinking\":true,\"drinkingDose\":10,\"drinkingRate\":\"Monthly\",\"drugsRecordList\":[{\"dose\":\"数额BBR\",\"frequency\":\"使用频率BBR\",\"name\":\"产品BBR\",\"time\":\"24/06/2025\"}],\"hasFamilyMemberDisease\":true,\"height\":\"111\",\"hivTreatment\":false,\"hospitalization\":true,\"medicalCheck\":false,\"takingDrugs\":true,\"takingDrugsOrAlcohol\":true,\"takingMedicine\":true,\"treatment\":false,\"unCompletedCheck\":true,\"unTreatedSymptom\":false,\"virusCheck\":true,\"weight\":\"222\",\"weightChange\":\"Gain\"},\"medicalRecords\":{\"cityInfo\":{\"address\":\"详细地址BBR\",\"city\":\"城市BBR\",\"country\":\"国家BBR\",\"postalCode\":\"邮政编码BBR\",\"stateProvince\":\"州/省BBR\"},\"hospitalName\":\"BBR最近一次就诊的医院名称\",\"lastVisitDate\":\"2025-06-25 10:19:07\",\"lastVisitReason\":\"上次医疗咨询的原因\\nBBR\",\"treatmentResult\":\"治疗或开具的处方药物以及完成的任何检查的结果\\nBBR\"}}";
            String jsonStr = "{\"detailInfo\":{\"futureTravelInfoList\":[{\"city\":\"bbr城市\",\"country\":\"bbr国家\",\"visitRate\":\"bbr访视频率\",\"visitTime\":\"bbr访问时长\"}],\"futureTravelOutsideInfo\":{\"changeReason\":\"bbr变更居住地原因\",\"city\":\"bbr计划居住城市\",\"country\":\"bbr你是否计划在今后两年内居住在你目前居住国以外的地方?\n\",\"travelTime\":\"bbr计划居住时间\"},\"hasAccident\":true,\"hasAviationActivity\":true,\"hasDangerousDriving\":true,\"hasDangerousHobby\":true,\"hasFutureTravel\":true,\"hasFutureTravelOutside\":true,\"hasHighPosition\":true,\"hasPendingCriminal\":true,\"hasTobaccoOrNicotine\":true,\"hasTravel\":true,\"highPositionInfoList\":[{\"duty\":\"bbr责任详情\",\"jobTime\":\"bbr任职时间\",\"position\":\"bbr职务/职级\",\"relation\":\"bbr与该人物的关系\"}],\"pendingCriminalDetail\":\"bbr您目前是否面临任何待审指控？或者,您是否曾经被指控、定罪过任何刑事犯罪？又或者，您当前是否处于缓刑、假释或法定释放状态?\\n\",\"tobaccoInfoList\":[{\"amount\":\"bbr数额\",\"frequency\":\"bbr使用频率\",\"lastUseDate\":\"22/06/2025\",\"product\":\"bbr产品\"}],\"travelInfoList\":[{\"city\":\"bbr城市\",\"country\":\"bbr国家\",\"visitRate\":\"bbr访问频率\",\"visitTime\":\"bbr访问时长\"}]}}";
            System.out.println("原始 JSON:\n" + jsonStr);
            JSONObject originalJson = JSON.parseObject(jsonStr);

            // 1. 收集中文字段
            Map<String, Object> chineseFields = new LinkedHashMap<>();
            collectChineseFields(originalJson, chineseFields, "");

            System.out.println("收集到的中文字段:");
            System.out.println(JSON.toJSONString(chineseFields));

            // 2. 调用翻译服务
            Map<String, String> translations = callTranslationService(chineseFields);

            System.out.println("\n翻译结果:");
            System.out.println(JSON.toJSONString(translations));

            // 3. 更新原始 JSON
            updateJsonWithTranslations(originalJson, translations);

            System.out.println("\n更新后的完整 JSON:");
            System.out.println(originalJson.toJSONString());

            boolean isEqual = JsonComparator.isEqual(JSON.parseObject(jsonStr), JSON.parseObject(originalJson.toJSONString()));
            System.out.println("Is equal to original? " + isEqual);
    }
}
