package com.yirendai.workbench.service.callcenter;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.yirendai.workbench.entity.callcenter.CallcenterCustomFields;
import com.yirendai.workbench.service.importExcel.easyExcel.ImportExcelHelper;
import com.yirendai.workbench.service.importExcel.easyExcel.DynamicImportResult;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ExcelDynamicFieldTest {
    @Test
    public void importDynamicExcel() {
        File localFile = new File("/Users/<USER>/Downloads/用户明细.xlsx");
        byte[] content = new byte[(int) localFile.length()];
        try {
            FileInputStream in = new FileInputStream(localFile);
            in.read(content);
            in.close();
        } catch (Exception e) {}

        DynamicImportResult importList = ImportExcelHelper.getImportResult(localFile, 0, 1, false, null);
        List data = importList.getData();
        for (Object list : data) {
            System.out.println(list);
        }
        System.out.println(importList.getHeaders());
    }

    @Test
    public void writeDynamicExcel(){
        try (ExcelWriter excelWriter = EasyExcel.write(new File("/Users/<USER>/Downloads/out.xlsx")).build();){
            List<List<String>> customerList = new ArrayList<>();

            Map<String, CallcenterCustomFields> fieldNameDefinitionMap = initFieldNameDefinitionMap();
            List<List<String>> excelHeaders = initExcelHeaders(fieldNameDefinitionMap);
            List<LinkedHashMap> products = mockData();

            Page<LinkedHashMap> pageResult = new PageImpl<>(products, PageRequest.of(1, 100), 1000);
            for (LinkedHashMap customerDb : pageResult.getContent()) {
                List<String> customerToExcel = new ArrayList<>();
                fieldNameDefinitionMap.forEach((fieldName, fieldDefine) -> {
                    customerToExcel.add(customerDb.get(fieldName).toString());
                });
                customerList.add(customerToExcel);
            }
            WriteSheet  writeSheet = EasyExcel.writerSheet(0, "我的客户")
                    .head(excelHeaders)
                    .excludeColumnFieldNames(Collections.singleton("id")).build();
            excelWriter.write(customerList, writeSheet);
        }
    }

    private Map<String, CallcenterCustomFields>  initFieldNameDefinitionMap(){
        List<CallcenterCustomFields> fieldDefinitions = new ArrayList<>();
        for (int i = 0; i < 4; i++){
            fieldDefinitions.add(new CallcenterCustomFields());
        }

        int[] order = new int[] {3, 5, 0, 2};
        boolean[] required = new boolean[] {false, false, true, true};
        for (int i = 0; i < 4; i++){
            fieldDefinitions.get(i).setFieldName("fieldName" + i);
            fieldDefinitions.get(i).setFieldLabel("fieldLabel" + i);
            fieldDefinitions.get(i).setSortOrder(order[i]);
            fieldDefinitions.get(i).setIsRequired(required[i]);
        }

        return fieldDefinitions.stream()
                .sorted(Comparator.comparing(CallcenterCustomFields::getSortOrder))
                .collect(Collectors.toMap(CallcenterCustomFields::getFieldName, Function.identity(),
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new));
    }

    private List<List<String>> initExcelHeaders(Map<String, CallcenterCustomFields> fieldNameDefinitionMap){
        List<List<String>> excelHeaders = new ArrayList<>();
        fieldNameDefinitionMap.forEach((fieldName, field) -> {
            List<String> header = new ArrayList<>();
            String prefix = "";
            if (field.getIsRequired()){
                prefix = "*";
            }
            header.add(prefix + field.getFieldLabel());
            excelHeaders.add(header);
        });
        return excelHeaders;
    }

    private List<LinkedHashMap> mockData(){
        List<LinkedHashMap> customerList = new ArrayList<>();
        for (int i = 0; i < 100; i++){
            LinkedHashMap customer = new LinkedHashMap();
            customer.put("id", i);
            customer.put("fieldName0", "fieldValue0" + i);
            customer.put("fieldName1", "fieldValue1" + i);
            customer.put("fieldName2", "fieldValue2" + i);
            customer.put("fieldName3", "fieldValue3" + i);

            customerList.add(customer);
        }
        return customerList;
    }

}
