<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.SkillGroupDetailMapper">
  <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.SkillGroupDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_person" jdbcType="VARCHAR" property="modifyPerson" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <insert id="insertBatch" parameterType="java.util.List">
    INSERT INTO skill_group_detail (
    group_id,
    employee_id,
    employee_name,
    del_flag,
    creator,
    create_time
    ) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.groupId},
      #{item.employeeId},
      #{item.employeeName},
      #{item.delFlag},
      #{item.creator},
      #{item.createTime}
      )
    </foreach>
  </insert>

  <update id="batchDeleteByEmployeeIds">
    UPDATE skill_group_detail
    SET del_flag = 1, modify_time = NOW()
    WHERE group_id = #{groupId}
    AND employee_id IN
    <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
      #{employeeId}
    </foreach>
  </update>

</mapper>