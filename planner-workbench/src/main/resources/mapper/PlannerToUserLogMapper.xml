<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.PlannerToUserLogMapper">
  <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.PlannerToUserLog">
	<id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="planner_id" jdbcType="INTEGER" property="plannerId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
	<result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
  </resultMap>
  <sql id="Base_Column_List">
    user_id, planner_id, status,remark,update_time,channel,id,user_type
  </sql>
  <select id="getBindBeanLog"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from planner_to_user_log
    WHERE status="0" AND user_id= #{userId}
    LIMIT 1
  </select>
</mapper>