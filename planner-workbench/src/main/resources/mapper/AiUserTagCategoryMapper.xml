<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiUserTagCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="aiUserTagCategoryResultMap" type="com.yirendai.workbench.entity.AiUserTagCategory">
        <id column="id" property="id"/>
        <result column="category_name" property="categoryName"/>
        <result column="match_des" property="matchDes"/>
        <result column="user_type" property="userType"/>
        <result column="parent_id" property="parentId"/>
        <result column="top_parent_id" property="topParentId"/>
        <result column="color_value" property="colorValue"/>
        <result column="multi_select" property="multiSelect"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>
    <select id="selectByParentId" resultMap="aiUserTagCategoryResultMap">
        select
        *
        from ai_user_tag_category
        where
        parent_id = #{parentId,jdbcType=NUMERIC}
        <if test="categoryName !=null and categoryName !=''">
            and category_name like concat('%',#{categoryName},'%')
        </if>
        <if test="userType !=null  ">
            and user_type = #{userType}
        </if>
    </select>


</mapper>
