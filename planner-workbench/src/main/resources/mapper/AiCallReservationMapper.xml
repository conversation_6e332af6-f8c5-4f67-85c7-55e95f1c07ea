<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiCallReservationMapper">

    <resultMap id="AiCallReservationBaseResultMap" type="com.yirendai.workbench.entity.AiCallReservation">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="chatContactId" column="chat_contact_id" jdbcType="BIGINT"/>
        <result property="plannerNo" column="planner_no" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="processStatus" column="process_status" jdbcType="TINYINT"/>
        <result property="reservationTime" column="reservation_time" jdbcType="TIMESTAMP"/>
        <result property="reservationReason" column="reservation_reason" jdbcType="VARCHAR"/>
        <result property="confidenceScore" column="confidence_score" jdbcType="DECIMAL"/>
        <result property="nextChatStarter" column="next_chat_starter" jdbcType="VARCHAR"/>
        <result property="feedbackStatus" column="feedback_status" jdbcType="TINYINT"/>
        <result property="feedbackDetails" column="feedback_details" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="BIGINT"/>
        <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,chat_contact_id,planner_no,user_id,process_status,
        reservation_time,reservation_reason,confidence_score,
        next_chat_starter,feedback_status,feedback_details,
        tenant_id,create_user,create_dept,create_time,
        update_user,update_time,status,is_deleted
    </sql>

</mapper>