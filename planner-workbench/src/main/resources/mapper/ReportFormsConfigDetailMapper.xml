<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.ReportFormsConfigDetailMapper">
  <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.ReportFormsConfigDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="view_id" jdbcType="VARCHAR" property="viewId" />
    <result column="view_name" jdbcType="VARCHAR" property="viewName" />
    <result column="filter_field" jdbcType="VARCHAR" property="filterField" />
    <result column="show_fields" jdbcType="VARCHAR" property="showFields" />
    <result column="variable" jdbcType="VARCHAR" property="variable" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_person" jdbcType="VARCHAR" property="modifyPerson" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project_id, project_name, view_id, view_name, filter_field,show_fields, variable,
    del_flag, creator, create_time, modify_person, modify_time
  </sql>

</mapper>