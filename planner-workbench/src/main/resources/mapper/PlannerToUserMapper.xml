<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.PlannerToUserMapper">
  <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.PlannerToUser">
	<id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="planner_id" jdbcType="INTEGER" property="plannerId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
	<result column="channel" jdbcType="VARCHAR" property="channel" />
  </resultMap>
  <sql id="Base_Column_List">
    user_id, planner_id, update_time,channel,id
  </sql>
  <select id="selectCountAll"  resultType="java.lang.Integer">
    select count(1) from planner_to_user where CHANNEL="yrcf";
  </select>
  <select id="selectListByPage"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from planner_to_user 
    WHERE CHANNEL="yrcf"
    ORDER BY id ASC
	limit #{pageNo},#{pageSize}
  </select>
</mapper>