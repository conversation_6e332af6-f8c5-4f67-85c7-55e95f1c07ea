<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationWechatSummaryMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiConversationWechatSummary">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="plannerId" column="planner_id" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="wechatContent" column="wechat_content" jdbcType="VARCHAR"/>
            <result property="summaryContentPlanner" column="summary_content_planner" jdbcType="VARCHAR"/>
            <result property="summaryContentUser" column="summary_content_user" jdbcType="VARCHAR"/>
            <result property="userContentValid" column="user_content_valid" jdbcType="TINYINT"/>
            <result property="confidenceScore" column="confidence_score" jdbcType="DECIMAL"/>
            <result property="feedbackStatus" column="feedback_status" jdbcType="TINYINT"/>
            <result property="feedbackDetails" column="feedback_details" jdbcType="VARCHAR"/>
            <result property="feedbackUserId" column="feedback_user_id" jdbcType="VARCHAR"/>
            <result property="feedbackUserName" column="feedback_user_name" jdbcType="VARCHAR"/>
            <result property="feedbackTime" column="feedback_time" jdbcType="TIMESTAMP"/>
            <result property="intervalTag" column="interval_tag" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,planner_id,user_id,wechat_content,
        summary_content_planner,summary_content_user,user_content_valid,
        confidence_score,feedback_status,feedback_details,
        feedback_user_id,feedback_user_name,feedback_time,
        interval_tag,tenant_id,create_time,
        update_time,status,is_deleted
    </sql>
</mapper>
