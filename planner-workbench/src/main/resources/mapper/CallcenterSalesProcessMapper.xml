<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterSalesProcessMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CallcenterSalesProcess">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="product_type" property="productType" jdbcType="INTEGER"/>
        <result column="process_name" property="processName" jdbcType="VARCHAR"/>
        <result column="process_no" property="processNo" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="process_status" property="processStatus" jdbcType="INTEGER"/>
        <result column="node_text" property="nodeText" jdbcType="LONGVARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="pause_time" property="pauseTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="default_process" property="defaultProcess" jdbcType="INTEGER"/>
        <result column="node_cal_type" property="nodeCalType" jdbcType="INTEGER"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, product_type, process_name, process_no, version_no, process_status,
        node_text, publish_time, pause_time, create_user, create_time,
        update_user, update_time, default_process,node_cal_type,tenant_id
    </sql>

</mapper>
