<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.PlannerUserBindMapper">
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.PlannerUserBind">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="planner_id" jdbcType="VARCHAR" property="plannerId" />
        <result column="planner_name" jdbcType="VARCHAR" property="plannerName" />
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="resource" jdbcType="VARCHAR" property="resource" />
        <result column="org_id" jdbcType="INTEGER" property="orgId" />
        <result column="remark2" jdbcType="VARCHAR" property="remark2" />
        <result column="group_name" jdbcType="VARCHAR" property="groupName" />
        <result column="last_call_time" jdbcType="TIMESTAMP" property="lastCallTime" />
        <result column="last_communication_time" jdbcType="TIMESTAMP" property="lastCommunicationTime" />
        <result column="visit_parent" jdbcType="VARCHAR" property="visitParent" />
        <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
        <result column="is_sale" jdbcType="VARCHAR" property="isSale" />
        <result column="appointment_time" jdbcType="TIMESTAMP" property="appointmentTime" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="allocate_type" jdbcType="INTEGER" property="allocateType" />
    </resultMap>

    <resultMap id="PageResultMap" type="com.yirendai.workbench.entity.PlannerCustomerList">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="planner_id" jdbcType="VARCHAR" property="plannerId" />
        <result column="planner_name" jdbcType="VARCHAR" property="plannerName" />
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="resource" jdbcType="VARCHAR" property="resource" />
        <result column="org_id" jdbcType="INTEGER" property="orgId" />
        <result column="remark2" jdbcType="VARCHAR" property="remark2" />
        <result column="group_name" jdbcType="VARCHAR" property="groupName" />
        <result column="last_call_time" jdbcType="TIMESTAMP" property="lastCallTime" />
        <result column="last_communication_time" jdbcType="TIMESTAMP" property="lastCommunicationTime" />
        <result column="visit_parent" jdbcType="VARCHAR" property="visitParent" />
        <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
        <result column="is_sale" jdbcType="VARCHAR" property="isSale" />
        <result column="appointment_time" jdbcType="TIMESTAMP" property="appointmentTime" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="allocate_type" jdbcType="INTEGER" property="allocateType" />
        <result column="user_phone" jdbcType="VARCHAR" property="phone" />
        <result column="scene_id" jdbcType="BIGINT" property="sceneId" />
        <result column="follow_up_status" jdbcType="TIMESTAMP" property="flowUpStatus" />
        <result column="develop_start_time" jdbcType="VARCHAR" property="developStartTime" />
        <result column="develop_end_time" jdbcType="VARCHAR" property="developEndTime" />
        <result column="scene_no" jdbcType="VARCHAR" property="sceneNo" />
        <result column="user_type" jdbcType="INTEGER" property="userType" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,user_name, planner_id,planner_name, bind_time,end_time,create_time,update_time,remark,resource,org_id,remark2,group_name,
last_call_time,last_communication_time,visit_parent,visit_type,is_sale,appointment_time,tenant_id,allocate_type
    </sql>

    <sql id="Page_Column_List">
        pub.id,pub.user_id,pub.user_name,pub.planner_id,pub.planner_name,pub.bind_time,pub.end_time,pub.create_time,pub.update_time,pub.remark,pub.resource,pub.org_id,pub.remark2,pub.group_name,
        pub.last_call_time,pub.last_communication_time,pub.visit_parent,pub.visit_type,pub.is_sale,pub.appointment_time,pub.tenant_id,pub.allocate_type,
        cu.user_phone,cu.follow_up_status,cu.develop_start_time,cu.develop_end_time,cu.scene_id,cu.user_type,cu.scene_no
    </sql>


    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="com.yirendai.workbench.entity.PlannerUserBind">
        insert into planner_user_bind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="plannerId != null">
                planner_id,
            </if>
            <if test="plannerName != null">
                planner_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="bindTime != null">
                bind_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="remark2 != null">
                remark2,
            </if>
            <if test="resource != null">
                resource,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="plannerId != null">
                #{plannerId,jdbcType=INTEGER},
            </if>
            <if test="plannerName != null">
                #{plannerName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bindTime != null">
                #{bindTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="remark2 != null">
                #{remark2,jdbcType=VARCHAR},
            </if>
            <if test="resource != null">
                #{resource,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=INTEGER},
            </if>
            <if test="groupName != null">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getByOrgIdAndResource" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM planner_user_bind
        WHERE resource=#{resource,jdbcType=VARCHAR} AND org_id=#{orgId,jdbcType=INTEGER}

    </select>

    <select id="selectCountAll"  resultType="java.lang.Integer">
        select count(1) from planner_user_bind where resource=2 and id &lt; 197510
    </select>

    <select id="selectListByPage"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from planner_user_bind
        WHERE resource=2 and id &lt; 197510
        ORDER BY id ASC
        limit #{pageNo},#{pageSize}
    </select>

    <select id="searchCustomerList" resultMap="PageResultMap">
        select * from (
        select
        <include refid="Page_Column_List"/> from planner_user_bind pub
        inner join callcenter_user cu  on pub.user_id =cu.user_id
        <where>
            <if test="userId != null">
                and pub.user_id=#{userId,jdbcType=BIGINT}
            </if>
            <if test="plannerId != null">
                and pub.planner_id=#{plannerId,jdbcType=INTEGER}
            </if>
            <if test="plannerName != null">
                and pub.planner_name like concat('%',#{plannerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userName != null">
                and pub.user_name like concat('%',#{userName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userType != null">
                and cu.user_type=#{userType,jdbcType=VARCHAR}
            </if>
            <if test="lastCommunicationTimeFrom != null">
                and pub.last_communication_time &gt;= #{lastCommunicationTimeFrom,jdbcType=TIMESTAMP}
            </if>
            <if test="lastCommunicationTimeTo != null">
                and pub.last_communication_time &lt;= #{lastCommunicationTimeTo,jdbcType=TIMESTAMP}
            </if>
            <if test="appointmentTimeFrom != null">
                and pub.appointment_time &gt;= #{appointmentTimeFrom,jdbcType=TIMESTAMP}
            </if>
            <if test="appointmentTimeTo != null">
                and pub.appointment_time &lt;= #{appointmentTimeTo,jdbcType=TIMESTAMP}
            </if>
            <if test="visitParent != null">
                and pub.visit_parent=#{visitParent,jdbcType=VARCHAR}
            </if>
            <if test="visitType != null">
                and pub.visit_type=#{visitType,jdbcType=VARCHAR}
            </if>
            <if test="isSale != null">
                and pub.is_sale like concat('%',#{isSale,jdbcType=VARCHAR},'%')
            </if>
            <if test="plannerIds != null and  plannerIds.size>0 ">
                and pub.planner_id in
                <foreach collection="plannerIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deptPlannerIds != null and  deptPlannerIds.size>0">
                and pub.planner_id in
                <foreach collection="deptPlannerIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="tenantId != null">
                and pub.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="phone != null">
                and cu.user_phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="flowUpStatus != null">
                and cu.follow_up_status = #{flowUpStatus,jdbcType=TINYINT}
            </if>
            <if test="sceneNo != null">
                and cu.scene_no = #{sceneNo,jdbcType=VARCHAR}
            </if>
        </where>
        union all
        select
        <include refid="Page_Column_List"/> from planner_user_bind pub
        inner join callcenter_user cu  on pub.user_id=cu.virtual_user_id
        <where>
            <if test="userId != null">
                and pub.user_id=#{userId,jdbcType=BIGINT}
            </if>
            <if test="plannerId != null">
                and pub.planner_id=#{plannerId,jdbcType=INTEGER}
            </if>
            <if test="plannerName != null">
                and pub.planner_name like concat('%',#{plannerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userName != null">
                and pub.user_name like concat('%',#{userName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userType != null">
                and cu.user_type=#{userType,jdbcType=VARCHAR}
            </if>
            <if test="lastCommunicationTimeFrom != null">
                and pub.last_communication_time &gt;= #{lastCommunicationTimeFrom,jdbcType=TIMESTAMP}
            </if>
            <if test="lastCommunicationTimeTo != null">
                and pub.last_communication_time &lt;= #{lastCommunicationTimeTo,jdbcType=TIMESTAMP}
            </if>
            <if test="appointmentTimeFrom != null">
                and pub.appointment_time &gt;= #{appointmentTimeFrom,jdbcType=TIMESTAMP}
            </if>
            <if test="appointmentTimeTo != null">
                and pub.appointment_time &lt;= #{appointmentTimeTo,jdbcType=TIMESTAMP}
            </if>
            <if test="visitParent != null">
                and pub.visit_parent=#{visitParent,jdbcType=VARCHAR}
            </if>
            <if test="visitType != null">
                and pub.visit_type=#{visitType,jdbcType=VARCHAR}
            </if>
            <if test="isSale != null">
                and pub.is_sale like concat('%',#{isSale,jdbcType=VARCHAR},'%')
            </if>
            <if test="plannerIds != null and  plannerIds.size>0 ">
                and pub.planner_id in
                <foreach collection="plannerIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deptPlannerIds != null and  deptPlannerIds.size>0">
                and pub.planner_id in
                <foreach collection="deptPlannerIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="tenantId != null">
                and pub.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="phone != null">
                and cu.user_phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="flowUpStatus != null">
                and cu.follow_up_status = #{flowUpStatus,jdbcType=TINYINT}
            </if>
            <if test="sceneNo != null">
                and cu.scene_no = #{sceneNo,jdbcType=VARCHAR}
            </if>
        </where>
        ) as temp
        <choose>
            <when test="sortType != null">
                <choose>
                    <!-- 1 = 上次沟通时间 -->
                    <when test="sortType == 1">
                        <choose>
                            <!-- 升序：NULL 最上面 -->
                            <when test="sortOrder != null and (sortOrder == 'ASC' or sortOrder == 'asc')">
                                ORDER BY (temp.last_communication_time IS NULL) DESC,
                                temp.last_communication_time ASC
                            </when>
                            <!-- 降序：NULL 最下面 -->
                            <otherwise>
                                ORDER BY (temp.last_communication_time IS NULL) ASC,
                                temp.last_communication_time DESC
                            </otherwise>
                        </choose>
                    </when>
                    <!-- 2 = 上次接通时间 -->
                    <when test="sortType == 2">
                        <choose>
                            <!-- 升序：NULL 最上面 -->
                            <when test="sortOrder != null and (sortOrder == 'ASC' or sortOrder == 'asc')">
                                ORDER BY (temp.last_call_time IS NULL) DESC,
                                temp.last_call_time ASC
                            </when>
                            <!-- 降序：NULL 最下面 -->
                            <otherwise>
                                ORDER BY (temp.last_call_time IS NULL) ASC,
                                temp.last_call_time DESC
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        ORDER BY temp.bind_time DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY temp.bind_time DESC
            </otherwise>
        </choose>
        limit #{offset}, #{pageSize}
    </select>

    <select id="countCustomerList" resultType="int">
        select count(*) from
        (select pub.id from planner_user_bind pub
        inner join callcenter_user cu on pub.user_id =cu.user_id
        <where>
            <if test="userId != null">
                and pub.user_id=#{userId,jdbcType=BIGINT}
            </if>
            <if test="plannerId != null">
                and pub.planner_id=#{plannerId,jdbcType=INTEGER}
            </if>
            <if test="plannerName != null">
                and pub.planner_name like concat('%',#{plannerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userName != null">
                and pub.user_name like concat('%',#{userName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userType != null">
                and cu.user_type=#{userType,jdbcType=VARCHAR}
            </if>
            <if test="lastCommunicationTimeFrom != null">
                and pub.last_communication_time &gt;= #{lastCommunicationTimeFrom,jdbcType=TIMESTAMP}
            </if>
            <if test="lastCommunicationTimeTo != null">
                and pub.last_communication_time &lt;= #{lastCommunicationTimeTo,jdbcType=TIMESTAMP}
            </if>
            <if test="appointmentTimeFrom != null">
                and pub.appointment_time &gt;= #{appointmentTimeFrom,jdbcType=TIMESTAMP}
            </if>
            <if test="appointmentTimeTo != null">
                and pub.appointment_time &lt;= #{appointmentTimeTo,jdbcType=TIMESTAMP}
            </if>
            <if test="visitParent != null">
                and pub.visit_parent=#{visitParent,jdbcType=VARCHAR}
            </if>
            <if test="visitType != null">
                and pub.visit_type=#{visitType,jdbcType=VARCHAR}
            </if>
            <if test="isSale != null">
                and pub.is_sale like concat('%',#{isSale,jdbcType=VARCHAR},'%')
            </if>
            <if test="plannerIds != null and  plannerIds.size>0">
                and pub.planner_id in
                <foreach collection="plannerIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deptPlannerIds != null and  deptPlannerIds.size>0">
                and pub.planner_id in
                <foreach collection="deptPlannerIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="tenantId != null">
                and pub.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="phone != null">
                and cu.user_phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="flowUpStatus != null">
                and cu.follow_up_status = #{flowUpStatus,jdbcType=TINYINT}
            </if>
        </where>
        union all
        select pub.id from planner_user_bind pub
        inner join callcenter_user cu on pub.user_id =cu.virtual_user_id
        <where>
            <if test="userId != null">
                and pub.user_id=#{userId,jdbcType=BIGINT}
            </if>
            <if test="plannerId != null">
                and pub.planner_id=#{plannerId,jdbcType=INTEGER}
            </if>
            <if test="plannerName != null">
                and pub.planner_name like concat('%',#{plannerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userName != null">
                and pub.user_name like concat('%',#{userName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userType != null">
                and cu.user_type=#{userType,jdbcType=VARCHAR}
            </if>
            <if test="lastCommunicationTimeFrom != null">
                and pub.last_communication_time &gt;= #{lastCommunicationTimeFrom,jdbcType=TIMESTAMP}
            </if>
            <if test="lastCommunicationTimeTo != null">
                and pub.last_communication_time &lt;= #{lastCommunicationTimeTo,jdbcType=TIMESTAMP}
            </if>
            <if test="appointmentTimeFrom != null">
                and pub.appointment_time &gt;= #{appointmentTimeFrom,jdbcType=TIMESTAMP}
            </if>
            <if test="appointmentTimeTo != null">
                and pub.appointment_time &lt;= #{appointmentTimeTo,jdbcType=TIMESTAMP}
            </if>
            <if test="visitParent != null">
                and pub.visit_parent=#{visitParent,jdbcType=VARCHAR}
            </if>
            <if test="visitType != null">
                and pub.visit_type=#{visitType,jdbcType=VARCHAR}
            </if>
            <if test="isSale != null">
                and pub.is_sale like concat('%',#{isSale,jdbcType=VARCHAR},'%')
            </if>
            <if test="plannerIds != null and  plannerIds.size>0">
                and pub.planner_id in
                <foreach collection="plannerIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deptPlannerIds != null and  deptPlannerIds.size>0">
                and pub.planner_id in
                <foreach collection="deptPlannerIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="tenantId != null">
                and pub.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="phone != null">
                and cu.user_phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="flowUpStatus != null">
                and cu.follow_up_status = #{flowUpStatus,jdbcType=TINYINT}
            </if>
        </where>
        ) as temp
    </select>



    <select id="getCrmGroups" resultType="java.lang.Integer">
        SELECT id
        FROM (
                 SELECT *, (@row_number := @row_number + 1) AS row_num
                 FROM planner_user_bind, (SELECT @row_number := 0) AS init where resource = 2
                 ORDER BY id asc
             ) AS numbered_rows
        WHERE row_num % 100 = 1 order by row_num asc;
    </select>

    <update id="updateLastCallAndCommunicationTime">
        UPDATE planner_user_bind
        <set>
            last_communication_time = #{lastCommunicationTime},
            <if test="lastCallTime != null">
                last_call_time = #{lastCallTime},
            </if>
        </set>
        WHERE user_id = #{userId}
    </update>

    <select id="findBindBakDataInfo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM planner_user_bind_0510bak
        WHERE user_id=#{userId,jdbcType=VARCHAR}
    </select>

</mapper>
