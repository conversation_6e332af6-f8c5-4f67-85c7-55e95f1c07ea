<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiHelperMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="aiHelperMessageResultMap" type="com.yirendai.workbench.entity.AiHelperMessage">
        <id column="id" property="id"/>
        <result column="account" property="account"/>
        <result column="user_id" property="userId"/>
        <result column="conversation_id" property="conversationId"/>
        <result column="message_id" property="messageId"/>
        <result column="question" property="question"/>
        <result column="answer" property="answer"/>
        <result column="rating" property="rating"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="selectAiHelperMessagePage" resultMap="aiHelperMessageResultMap">
        select * from ai_helper_message where is_deleted = 0
    </select>

    <update id="updateRatingByMessageIdAndUserId" >
        update ai_helper_message set rating = #{rating} where message_id = #{messageId} and user_id = #{userId}
    </update>

    <update id="deleteByCconversationIdAndUserId">
        update ai_helper_message
        set is_deleted = 1
        where user_id = #{userId}
        <if test="conversationId != null and conversationId !=''">
            and conversation_id = #{conversationId}
        </if>
    </update>
</mapper>
