<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AdsCsgBigFixedAchievementDfMapper">

    <select id="getPlannerAchievements" resultType="com.yirendai.workbench.entity.AdsCsgBigFixedAchievementDf">
        select staff_id, max(staff_name) as staff_name, sum(invest_amount) as invest_amount
        from ads_csg_big_fixed_achievement_df
        where invest_amount is not null and staff_id is not null and staff_name is not null and staff_name != ''
        group by staff_id
    </select>

    <select id="getPlannerUsers" resultType="com.yirendai.workbench.entity.AdsCsgBigFixedAchievementDf">
        select staff_id, user_id, min(STR_TO_DATE(order_time, '%Y-%m-%d %H:%i:%s')) as order_time
        from ads_csg_big_fixed_achievement_df
        where
            order_time is not null and order_time != ''
            and user_id is not null and user_id != ''
            and staff_id is not null
        group by staff_id, user_id
    </select>

</mapper>
