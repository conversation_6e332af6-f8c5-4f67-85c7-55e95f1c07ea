<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationFlowResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiConversationFlowResult">
        <id column="id" property="id" />
        <result column="source_type" property="sourceType" />
        <result column="source_id" property="sourceId" />
        <result column="chat_id" property="chatId" />
        <result column="source_data" property="sourceData" />
        <result column="tenant_id" property="tenantId" />
        <result column="planner_no" property="plannerNo" />
        <result column="user_id" property="userId" />
        <result column="flow_type" property="flowType" />
        <result column="intent_code" property="intentCode" />
        <result column="node_code" property="nodeCode" />
        <result column="node_name" property="nodeName" />
        <result column="node_level" property="nodeLevel" />
        <result column="is_reached" property="isReached" />
        <result column="confidence_score" property="confidenceScore" />
        <result column="matched_keywords" property="matchedKeywords" />
        <result column="model" property="model" />
        <result column="temperature" property="temperature" />
        <result column="task_id" property="taskId" />
        <result column="chat_time" property="chatTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, source_type, source_id, chat_id, source_data, tenant_id, planner_no, user_id, flow_type, intent_code, node_code,
        node_name, node_level, is_reached, confidence_score,
        matched_keywords, model, temperature, task_id, chat_time, create_time, update_time, is_deleted
    </sql>

    <!-- 根据来源类型和来源ID查询结果 -->
    <select id="selectBySource" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_conversation_flow_result
        WHERE source_type = #{sourceType}
          AND source_id = #{sourceId}
          AND tenant_id = #{tenantId}
          AND is_deleted = 0
        ORDER BY node_level ASC, node_code ASC
    </select>

    <!-- 批量插入结果 -->
    <insert id="batchInsert">
        INSERT INTO ai_conversation_flow_result (
            source_type, source_id, chat_id, source_data, tenant_id, planner_no, user_id, flow_type, intent_code,
            node_code, node_name, node_level, is_reached, confidence_score,
            matched_keywords, model, temperature, task_id, chat_time,
            create_time, update_time, is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.sourceType}, #{item.sourceId}, #{item.chatId}, #{item.sourceData}, #{item.tenantId},
                #{item.plannerNo}, #{item.userId}, #{item.flowType}, #{item.intentCode}, 
                #{item.nodeCode}, #{item.nodeName}, #{item.nodeLevel}, #{item.isReached}, 
                #{item.confidenceScore}, #{item.matchedKeywords}, #{item.model}, #{item.temperature},
                #{item.taskId}, #{item.chatTime}, #{item.createTime}, #{item.updateTime}, #{item.isDeleted}
            )
        </foreach>
    </insert>

    <select id="selectReachedResult" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_conversation_flow_result
        WHERE is_deleted = 0
          AND is_reached = 1
          AND user_id = #{userId}
          AND node_name = #{nodeName}
        <if test="startTime != null">
          AND chat_time &gt;= #{startTime}
        </if>
        ORDER BY chat_time ASC
        LIMIT 1
    </select>

</mapper>
