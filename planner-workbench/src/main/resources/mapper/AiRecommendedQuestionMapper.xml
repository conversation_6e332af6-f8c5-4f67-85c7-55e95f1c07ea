<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiRecommendedQuestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="aiRecommendedQuestionResultMap" type="com.yirendai.workbench.entity.AiRecommendedQuestion">
        <id column="id" property="id"/>
        <result column="text_content" property="textContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_approved" property="isApproved"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="approver_userId" property="approverUserid"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <select id="selectRecommendedQuestions" resultType="java.lang.String">
        select
            text_content
        from ai_recommended_question where is_deleted = 0
        and tenant_id = #{tenantId}
        order by sort asc
    </select>

</mapper>
