<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationProcessTaskMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiConversationProcessTask">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="taskData" column="task_data" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="TINYINT"/>
        <result property="retryCount" column="retry_count" jdbcType="INTEGER"/>
        <result property="maxRetry" column="max_retry" jdbcType="INTEGER"/>
        <result property="taskStatus" column="task_status" jdbcType="TINYINT"/>
        <result property="lastRetryTime" column="last_retry_time" jdbcType="TIMESTAMP"/>
        <result property="lastFailReason" column="last_fail_reason" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="BIGINT"/>
        <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,task_id,task_data,task_type,retry_count,
        max_retry,task_status,last_retry_time,
        last_fail_reason,tenant_id,create_user,
        create_dept,create_time,
        update_user,update_time,status,
        is_deleted
    </sql>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO ai_conversation_process_task (
        task_id, task_data, task_type, retry_count, max_retry,
        task_status, last_retry_time, last_fail_reason, tenant_id,
        create_user, create_dept, create_time, update_user, update_time, status, is_deleted
        ) VALUES
        <foreach collection="list" item="task" separator=",">
            (
            #{task.taskId}, #{task.taskData}, #{task.taskType}, #{task.retryCount}, #{task.maxRetry},
            #{task.taskStatus}, #{task.lastRetryTime}, #{task.lastFailReason}, #{task.tenantId},
            #{task.createUser}, #{task.createDept}, #{task.createTime}, #{task.updateUser}, #{task.updateTime},
            #{task.status}, #{task.isDeleted}
            )
        </foreach>
    </insert>

</mapper>
