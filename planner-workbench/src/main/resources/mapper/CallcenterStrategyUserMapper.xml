<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterStrategyUserMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CallcenterStrategyUser">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result column="planner_id" jdbcType="VARCHAR" property="plannerId" />
        <result column="planner_name" jdbcType="VARCHAR" property="plannerName" />
        <result column="customer_user_id" jdbcType="VARCHAR" property="customerUserId" />
        <result column="customer_user_name" jdbcType="VARCHAR" property="customerUserName" />
        <result column="customer_user_type" jdbcType="INTEGER" property="customerUserType" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="strategy_config_id" jdbcType="INTEGER" property="strategyConfigId" />
        <result column="strategy_config_name" jdbcType="VARCHAR" property="strategyConfigName" />
        <result column="strategy_type_id" jdbcType="INTEGER" property="strategyTypeId" />
        <result column="strategy_trigger_time" jdbcType="TIMESTAMP" property="strategyTriggerTime" />
        <result column="communication_status" jdbcType="INTEGER" property="communicationStatus" />
        <result column="tag_type" jdbcType="INTEGER" property="tagType" />
        <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    </resultMap>

    <select id="searchStrategyUserList" resultMap="BaseResultMap" parameterType="com.yirendai.workbench.vo.req.callcenter.StrategyUserReq">
        select * from callcenter_strategy_user
        <where>
            <if test="customerUserType != null">
                and customer_user_type = #{customerUserType,jdbcType=INTEGER}
            </if>
            <if test="strategyConfigName != null">
                and strategy_config_name LIKE concat('%',#{strategyConfigName,jdbcType=VARCHAR},'%')
            </if>
            <if test="communicationStatus != null">
                and communication_status = #{communicationStatus,jdbcType=VARCHAR}
            </if>
            <if test="strategyTriggerTime != null">
                and strategy_trigger_time >= #{strategyTriggerTime,jdbcType=TIMESTAMP}
            </if>
            <if test="customerUserId != null">
                and customer_user_id = #{customerUserId,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY id DESC
        limit #{offset}, #{pageSize}
    </select>

    <select id="countStrategyUserList" resultType="int" parameterType="com.yirendai.workbench.vo.req.callcenter.StrategyUserReq">
        select  count(*) from callcenter_strategy_user
        <where>
            <if test="customerUserType != null">
                and customer_user_type = #{customerUserType,jdbcType=INTEGER}
            </if>
            <if test="strategyConfigName != null">
                and strategy_config_name LIKE concat('%',#{strategyConfigName,jdbcType=VARCHAR},'%')
            </if>
            <if test="communicationStatus != null">
                and communication_status = #{communicationStatus,jdbcType=VARCHAR}
            </if>
            <if test="strategyTriggerTime != null">
                and strategy_trigger_time >= #{strategyTriggerTime,jdbcType=TIMESTAMP}
            </if>
            <if test="customerUserId != null">
                and customer_user_id = #{customerUserId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getTypeIds" resultType="java.lang.Long" parameterType="java.util.Map">
        select DISTINCT strategy_type_id  from callcenter_strategy_user
        <where>
            <if test="plannerId != null">
                planner_id = #{plannerId,jdbcType=INTEGER}
            </if>
            <if test="strategyTriggerTime != null">
                and strategy_trigger_time >= #{strategyTriggerTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

</mapper>
