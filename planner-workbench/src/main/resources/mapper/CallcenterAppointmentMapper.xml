<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterAppointmentMapper">

    <select id="selectListByPlannerId" resultType="com.yirendai.workbench.vo.res.AppointmentListRes">
        SELECT
            a.connect_time connectTime, a.user_id userId, b.user_name userName, b.RESOURCE source
        from callcenter_appointment a left join planner_user_bind b
        on a.planner_id = b.planner_id
        and b.user_id = a.user_id
        where a.connect_status = 0
        and a.planner_id = #{plannerId}
        and a.connect_time &gt;= #{startTime}
        and a.connect_time &lt;= #{endTime}
    </select>
</mapper>
