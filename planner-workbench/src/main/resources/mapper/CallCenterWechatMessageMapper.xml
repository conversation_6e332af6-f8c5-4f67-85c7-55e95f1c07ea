<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallCenterWechatMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CallCenterWechatMessage">
        <id column="id" property="id" />
        <result column="wechat_id" property="wechatId" />
        <result column="type" property="type" />
        <result column="is_send" property="isSend" />
        <result column="sender" property="sender" />
        <result column="recipient" property="recipient" />
        <result column="content" property="content" />
        <result column="voice_content" property="voiceContent" />
        <result column="source_type" property="sourceType" />
        <result column="send_time" property="sendTime" />
        <result column="resource_url" property="resourceUrl" />
        <result column="nas_url" property="nasUrl" />
        <result column="pay_type" property="payType" />
        <result column="updated_time" property="updatedTime" />
        <result column="user_no" property="userNo" />
        <result column="request_params" property="requestParams" />
        <result column="response_body" property="responseBody" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, wechat_id, type, is_send, sender, recipient, content, voice_content, source_type,
        send_time, resource_url, nas_url, pay_type, updated_time, user_no, request_params,
        response_body, tenant_id, create_time, update_time
    </sql>


</mapper>