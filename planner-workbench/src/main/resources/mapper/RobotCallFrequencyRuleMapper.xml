<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRuleMapper">

    <resultMap id="RuleResultMap" type="com.yirendai.robot.modules.call.vo.RobotCallFrequencyRulePageVO">
        <id column="id" property="id"/>
        <result column="rule_name" property="ruleName"/>
        <result column="limit_count" property="limitCount"/>
        <result column="status" property="status"/>
        <result column="enabled_time" property="enabledTime"/>
        <result column="remark" property="remark"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="pageRuleResultList" resultMap="RuleResultMap">
        select distinct
            r.id,
            r.rule_name,
            r.limit_count,
            r.status,
            r.enabled_time,
            r.remark,
            r.create_user_name,
            r.create_time,
            r.update_user_name,
            r.update_time
        from ai_robot_call_frequency_rule r
        <if test="req != null and req.robotName != null and req.robotName != ''">
            inner join ai_robot_call_frequency_rule_robot rr on r.id = rr.rule_id
            inner join ai_robot robot on rr.robot_id = robot.id
        </if>
        <where>
            <if test="req != null and req.robotName != null and req.robotName != ''">
                and robot.robot_name like concat('%', #{req.robotName}, '%')
            </if>
            <if test="req != null and req.status != null">
                and r.status = #{req.status}
            </if>
            <if test="req != null and req.tenantId != null and req.tenantId != ''">
                and r.tenant_id = #{req.tenantId}
            </if>
        </where>
        order by r.status desc, r.create_time desc
    </select>

</mapper>
