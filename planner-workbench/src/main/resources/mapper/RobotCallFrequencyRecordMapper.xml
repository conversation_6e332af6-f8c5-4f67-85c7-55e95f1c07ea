<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRecordMapper">

    <select id="countByRobotIdsAndPhoneInPeriod" resultType="int">
        select count(*)
        from ai_robot_call_frequency_record
        where robot_id in
        <foreach collection="robotIds" item="robotId" open="(" separator="," close=")">
            #{robotId}
        </foreach>
          and phone = #{phone}
          and connect_time between #{startTime} and #{endTime}
    </select>

</mapper>
