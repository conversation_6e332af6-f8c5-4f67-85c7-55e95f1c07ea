<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.voiceaiserver.dao.AiComplianceBackResultDao">

    <resultMap type="com.yirendai.workbench.entity.AiComplianceBackResult" id="AiComplianceBackResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="plannerId" column="planner_id" jdbcType="VARCHAR"/>
        <result property="plannerName" column="planner_name" jdbcType="VARCHAR"/>
        <result property="valid" column="valid" jdbcType="INTEGER"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="personalConfirmationCompleted" column="personal_confirmation_completed" jdbcType="INTEGER"/>
        <result property="personalConfirmationReason" column="personal_confirmation_reason" jdbcType="VARCHAR"/>
        <result property="productAmountConfirmationCompleted" column="product_amount_confirmation_completed" jdbcType="INTEGER"/>
        <result property="productAmountConfirmationReason" column="product_amount_confirmation_reason" jdbcType="VARCHAR"/>
        <result property="documentSigningConfirmationCompleted" column="document_signing_confirmation_completed" jdbcType="INTEGER"/>
        <result property="documentSigningConfirmationReason" column="document_signing_confirmation_reason" jdbcType="VARCHAR"/>
        <result property="productServiceRiskAwarenessCompleted" column="product_service_risk_awareness_completed" jdbcType="INTEGER"/>
        <result property="productServiceRiskAwarenessReason" column="product_service_risk_awareness_reason" jdbcType="VARCHAR"/>
        <result property="autonomousDecisionRiskBearingCompleted" column="autonomous_decision_risk_bearing_completed" jdbcType="INTEGER"/>
        <result property="autonomousDecisionRiskBearingReason" column="autonomous_decision_risk_bearing_reason" jdbcType="VARCHAR"/>
        <result property="riskCompatibilityAcceptanceCompleted" column="risk_compatibility_acceptance_completed" jdbcType="INTEGER"/>
        <result property="riskCompatibilityAcceptanceReason" column="risk_compatibility_acceptance_reason" jdbcType="VARCHAR"/>
        <result property="noAdditionalPromotionCompleted" column="no_additional_promotion_completed" jdbcType="INTEGER"/>
        <result property="noAdditionalPromotionReason" column="no_additional_promotion_reason" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into yrcf_crm_155.ai_compliance_back_result(uuid, user_id, planner_id, valid, description, personal_confirmation_completed, personal_confirmation_reason, product_amount_confirmation_completed, product_amount_confirmation_reason, document_signing_confirmation_completed, document_signing_confirmation_reason, product_service_risk_awareness_completed, product_service_risk_awareness_reason, autonomous_decision_risk_bearing_completed, autonomous_decision_risk_bearing_reason, risk_compatibility_acceptance_completed, risk_compatibility_acceptance_reason, no_additional_promotion_completed, no_additional_promotion_reason, create_time, update_time, is_deleted, tenant_id)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.uuid}, #{entity.userId}, #{entity.plannerId}, #{entity.valid}, #{entity.description}, #{entity.personalConfirmationCompleted}, #{entity.personalConfirmationReason}, #{entity.productAmountConfirmationCompleted}, #{entity.productAmountConfirmationReason}, #{entity.documentSigningConfirmationCompleted}, #{entity.documentSigningConfirmationReason}, #{entity.productServiceRiskAwarenessCompleted}, #{entity.productServiceRiskAwarenessReason}, #{entity.autonomousDecisionRiskBearingCompleted}, #{entity.autonomousDecisionRiskBearingReason}, #{entity.riskCompatibilityAcceptanceCompleted}, #{entity.riskCompatibilityAcceptanceReason}, #{entity.noAdditionalPromotionCompleted}, #{entity.noAdditionalPromotionReason}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.tenantId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into yrcf_crm_155.ai_compliance_back_result(uuid, user_id, planner_id, valid, description, personal_confirmation_completed, personal_confirmation_reason, product_amount_confirmation_completed, product_amount_confirmation_reason, document_signing_confirmation_completed, document_signing_confirmation_reason, product_service_risk_awareness_completed, product_service_risk_awareness_reason, autonomous_decision_risk_bearing_completed, autonomous_decision_risk_bearing_reason, risk_compatibility_acceptance_completed, risk_compatibility_acceptance_reason, no_additional_promotion_completed, no_additional_promotion_reason, create_time, update_time, is_deleted, tenant_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.uuid}, #{entity.userId}, #{entity.plannerId}, #{entity.valid}, #{entity.description}, #{entity.personalConfirmationCompleted}, #{entity.personalConfirmationReason}, #{entity.productAmountConfirmationCompleted}, #{entity.productAmountConfirmationReason}, #{entity.documentSigningConfirmationCompleted}, #{entity.documentSigningConfirmationReason}, #{entity.productServiceRiskAwarenessCompleted}, #{entity.productServiceRiskAwarenessReason}, #{entity.autonomousDecisionRiskBearingCompleted}, #{entity.autonomousDecisionRiskBearingReason}, #{entity.riskCompatibilityAcceptanceCompleted}, #{entity.riskCompatibilityAcceptanceReason}, #{entity.noAdditionalPromotionCompleted}, #{entity.noAdditionalPromotionReason}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.tenantId})
        </foreach>
        on duplicate key update
uuid = values(uuid) , user_id = values(user_id) , planner_id = values(planner_id) , valid = values(valid) , description = values(description) , personal_confirmation_completed = values(personal_confirmation_completed) , personal_confirmation_reason = values(personal_confirmation_reason) , product_amount_confirmation_completed = values(product_amount_confirmation_completed) , product_amount_confirmation_reason = values(product_amount_confirmation_reason) , document_signing_confirmation_completed = values(document_signing_confirmation_completed) , document_signing_confirmation_reason = values(document_signing_confirmation_reason) , product_service_risk_awareness_completed = values(product_service_risk_awareness_completed) , product_service_risk_awareness_reason = values(product_service_risk_awareness_reason) , autonomous_decision_risk_bearing_completed = values(autonomous_decision_risk_bearing_completed) , autonomous_decision_risk_bearing_reason = values(autonomous_decision_risk_bearing_reason) , risk_compatibility_acceptance_completed = values(risk_compatibility_acceptance_completed) , risk_compatibility_acceptance_reason = values(risk_compatibility_acceptance_reason) , no_additional_promotion_completed = values(no_additional_promotion_completed) , no_additional_promotion_reason = values(no_additional_promotion_reason) , create_time = values(create_time) , update_time = values(update_time) , is_deleted = values(is_deleted) , tenant_id = values(tenant_id)     </insert>

</mapper>

