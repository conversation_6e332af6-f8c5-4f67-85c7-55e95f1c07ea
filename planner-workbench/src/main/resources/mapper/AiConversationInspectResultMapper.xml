<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationInspectResultMapper">

    <resultMap id="AiConversationInspectResultBaseResultMap" type="com.yirendai.workbench.entity.AiConversationInspectResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="chatContactId" column="chat_contact_id" jdbcType="BIGINT"/>
            <result property="plannerNo" column="planner_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="violation" column="violation" jdbcType="TINYINT"/>
            <result property="intentStatus" column="intent_status" jdbcType="TINYINT"/>
            <result property="sensitiveWordSize" column="sensitive_word_size" jdbcType="INTEGER"/>
            <result property="sensitiveWordResult" column="sensitive_word_result" jdbcType="OTHER"/>
            <result property="inspectIntentSize" column="inspect_intent_size" jdbcType="INTEGER"/>
            <result property="inspectIntentText" column="inspect_intent_text" jdbcType="OTHER"/>
            <result property="feedbackStatus" column="feedback_status" jdbcType="TINYINT"/>
            <result property="feedbackDetails" column="feedback_details" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,chat_contact_id,planner_no,
        user_id,violation,intent_status,
        sensitive_word_size,sensitive_word_result,inspect_intent_size,
        inspect_intent_text,feedback_status,feedback_details,
        tenant_id,create_user,create_dept,
        create_time,update_user,update_time,
        status,is_deleted
    </sql>
</mapper>
