<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.callcenter.CallcenterCustomTemplateFieldsMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.callcenter.CallcenterCustomTemplateFields">
            <id property="id" column="id" />
            <result property="customTemplateId" column="custom_template_id" />
            <result property="customFieldsId" column="custom_fields_id" />
            <result property="isRequired" column="is_required" />
            <result property="sortOrder" column="sort_order" />
            <result property="canSearch" column="can_search" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,custom_template_id,custom_fields_id,is_required,sort_order,
        can_search,create_time,update_time
    </sql>
</mapper>