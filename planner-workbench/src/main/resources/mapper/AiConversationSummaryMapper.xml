<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationSummaryMapper">

    <resultMap type="com.yirendai.workbench.entity.AiConversationSummary" id="AiConversationSummaryMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="chatContactId" column="chat_contact_id" jdbcType="BIGINT"/>
        <result property="plannerNo" column="planner_no" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="summaryContentPlanner" column="summary_content_planner" jdbcType="VARCHAR"/>
        <result property="summaryContentUser" column="summary_content_user" jdbcType="VARCHAR"/>
        <result property="userContentValid" column="user_content_valid" jdbcType="INTEGER"/>
        <result property="confidenceScore" column="confidence_score" jdbcType="VARCHAR"/>
        <result property="feedbackStatus" column="feedback_status" jdbcType="INTEGER"/>
        <result property="feedbackDetails" column="feedback_details" jdbcType="VARCHAR"/>
        <result property="feedbackUserId" column="feedback_user_id" jdbcType="VARCHAR"/>
        <result property="feedbackUserName" column="feedback_user_name" jdbcType="VARCHAR"/>
        <result property="feedbackTime" column="feedback_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="BIGINT"/>
        <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

</mapper>

