<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiPlannerTopUserMapper">

    <select id="getLastSyncTimeByPlannerNo" resultType="java.time.LocalDateTime">
        select max(create_time)
        from ai_planner_top_user
        where
            planner_no = #{plannerNo}
          and tenant_id = #{tenantId}
          and create_time is not null
    </select>

    <select id="getGroups" resultType="com.yirendai.voiceaiserver.vo.db.AvayaGroupVO">
        select min(id) - 1 as minId, max(id) as maxId, count(1) as groupSize
        from ai_planner_top_user
        group by ceil(id/10000)
        order by groupSize desc
    </select>

    <select id="getUserRealName" parameterType="list" resultType="com.yirendai.workbench.entity.AiPlannerTopUser">
        SELECT user_id, MAX(user_name) AS user_name
        FROM ai_planner_top_user
        WHERE user_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND user_name IS NOT NULL AND user_name != 'null' AND LENGTH(TRIM(user_name)) != 0
        GROUP BY user_id
    </select>

</mapper>
