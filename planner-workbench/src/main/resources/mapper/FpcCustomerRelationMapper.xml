<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.FpcCustomerRelationMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.FpcCustomerRelation">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="sign_date" jdbcType="DATE" property="signDate" />
        <result column="sign_time" jdbcType="VARCHAR" property="signTime" />
        <result column="first_insurance_amount" jdbcType="DECIMAL" property="firstInsuranceAmount" />
        <result column="years" jdbcType="INTEGER" property="years" />
        <result column="insurance_company" jdbcType="INTEGER" property="insuranceCompany" />
        <result column="product" jdbcType="INTEGER" property="product" />
        <result column="precautions" jdbcType="VARCHAR" property="precautions" />
        <result column="assign_scene" jdbcType="INTEGER" property="assignScene" />
        <result column="member_no" jdbcType="INTEGER" property="memberNo" />
        <result column="member_name" jdbcType="VARCHAR" property="memberName" />
        <result column="member_city" jdbcType="INTEGER" property="memberCity" />
        <result column="assign_time" jdbcType="TIMESTAMP" property="assignTime" />
        <result column="assign_type" jdbcType="INTEGER" property="assignType" />
        <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
        <result column="staff_id" jdbcType="INTEGER" property="staffId" />
        <result column="staff_city" jdbcType="VARCHAR" property="staffCity" />
        <result column="staff_group" jdbcType="VARCHAR" property="staffGroup" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, user_name, user_id, user_phone, type, sign_date, sign_time, first_insurance_amount,
        years, insurance_company, product, precautions, assign_scene, member_no, member_name,
        member_city, assign_time, assign_type, staff_name, staff_id, staff_city, staff_group,
        status, create_time, update_time
    </sql>

    <select id="selectListBySignDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fpc_customer_relation
        where sign_date between #{begin} and #{end} and status = 1
    </select>

    <select id="selectSignDateList" resultType="java.lang.String">
        select
        DISTINCT(sign_date)
        from fpc_customer_relation
        where sign_date between #{begin} and #{end} and status = 1
    </select>

</mapper>
