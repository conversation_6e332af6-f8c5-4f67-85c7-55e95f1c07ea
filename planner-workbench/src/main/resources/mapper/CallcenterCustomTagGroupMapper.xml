<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.callcenter.CallcenterCustomTagGroupMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.callcenter.CallcenterCustomTagGroup">
            <id property="id" column="id" />
            <result property="tagGroupName" column="tag_group_name" />
            <result property="groupColor" column="group_color" />
            <result property="multiCheck" column="multi_check" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="tenantId" column="tenant_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tag_group_name,group_color,multi_check,create_time,update_time,
        tenant_id
    </sql>
</mapper>
