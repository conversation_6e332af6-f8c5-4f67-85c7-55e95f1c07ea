<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiCustomerWorkOrderMapper">

    <resultMap type="com.yirendai.workbench.entity.AiCustomerWorkOrder" id="AiCustomerWorkOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="chatContactId" column="chat_contact_id" jdbcType="INTEGER"/>
        <result property="callUuid" column="call_uuid" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="segmentConfig" column="segment_config" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id" jdbcType="INTEGER"/>
        <result property="result" column="result" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

</mapper>

