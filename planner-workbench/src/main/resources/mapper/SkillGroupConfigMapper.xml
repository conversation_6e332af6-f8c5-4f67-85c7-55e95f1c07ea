<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.SkillGroupConfigMapper">
  <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.SkillGroupConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="group_type" jdbcType="TINYINT" property="groupType" />
    <result column="use_tel_no" jdbcType="VARCHAR" property="useTelNo" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_person" jdbcType="VARCHAR" property="modifyPerson" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, group_name, group_type, use_tel_no, del_flag, creator, create_time, modify_person, 
    modify_time
  </sql>

</mapper>