<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationTagsProcessMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiConversationTagsProcess">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="chatContactId" column="chat_contact_id" jdbcType="BIGINT"/>
            <result property="processStatus" column="process_status" jdbcType="TINYINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,chat_contact_id,process_status,
        tenant_id,create_time,update_time,
        is_deleted
    </sql>
</mapper>
