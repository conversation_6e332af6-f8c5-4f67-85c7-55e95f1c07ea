<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AdsQwBfixedOrderDetailDfMapper">

    <select id="getPlannerAchievements" resultType="com.yirendai.workbench.entity.AdsQwBfixedOrderDetailDf">
        select staff_id, max(staff_name) as staff_name, sum(pay_amt) as pay_amt
        from ads_qw_bfixed_order_detail_df
        where pay_amt is not null and staff_id is not null and staff_id != '' and staff_name is not null and staff_name != ''
        group by staff_id
    </select>

    <select id="getPlannerUsers" resultType="com.yirendai.workbench.entity.AdsQwBfixedOrderDetailDf">
        select staff_id, user_id, min(STR_TO_DATE(pay_date, '%Y-%m-%d %H:%i:%s')) as pay_date
        from ads_qw_bfixed_order_detail_df
        where
            pay_date is not null and pay_date != ''
            and user_id is not null and user_id != ''
            and staff_id is not null and staff_id != ''
        group by staff_id, user_id
    </select>

</mapper>
