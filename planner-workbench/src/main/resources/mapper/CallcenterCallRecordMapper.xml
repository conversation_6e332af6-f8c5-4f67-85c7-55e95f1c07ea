<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterCallRecordMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CallcenterCallRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="systemType" column="system_type" jdbcType="INTEGER"/>
            <result property="callType" column="call_type" jdbcType="INTEGER"/>
            <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
            <result property="agentUserId" column="agent_user_id" jdbcType="BIGINT"/>
            <result property="callPlannerId" column="call_planner_id" jdbcType="VARCHAR"/>
            <result property="user" column="user" jdbcType="VARCHAR"/>
            <result property="agentName" column="agent_name" jdbcType="VARCHAR"/>
            <result property="customerUid" column="customer_uid" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="answered" column="answered" jdbcType="INTEGER"/>
            <result property="startStamp" column="start_stamp" jdbcType="TIMESTAMP"/>
            <result property="endStamp" column="end_stamp" jdbcType="TIMESTAMP"/>
            <result property="ring" column="ring" jdbcType="BIGINT"/>
            <result property="answerStamp" column="answer_stamp" jdbcType="TIMESTAMP"/>
            <result property="duration" column="duration" jdbcType="BIGINT"/>
            <result property="hangupBy" column="hangup_by" jdbcType="INTEGER"/>
            <result property="hangupCause" column="hangup_cause" jdbcType="VARCHAR"/>
            <result property="direction" column="direction" jdbcType="VARCHAR"/>
            <result property="recordUrl" column="record_url" jdbcType="VARCHAR"/>
            <result property="billsec" column="billsec" jdbcType="BIGINT"/>
            <result property="plannerId" column="planner_id" jdbcType="VARCHAR"/>
            <result property="callUuid" column="call_uuid" jdbcType="VARCHAR"/>
            <result property="coreUuid" column="core_uuid" jdbcType="VARCHAR"/>
            <result property="bridgeUuid" column="bridge_uuid" jdbcType="VARCHAR"/>
            <result property="callerIdName" column="caller_id_name" jdbcType="VARCHAR"/>
            <result property="callerIdNumber" column="caller_id_number" jdbcType="VARCHAR"/>
            <result property="destinationNumber" column="destination_number" jdbcType="VARCHAR"/>
            <result property="hangupDisposition" column="hangup_disposition" jdbcType="VARCHAR"/>
            <result property="gwIp" column="gw_ip" jdbcType="VARCHAR"/>
            <result property="gwName" column="gw_name" jdbcType="VARCHAR"/>
            <result property="callTalking10" column="call_talking10" jdbcType="INTEGER"/>
            <result property="sessionType" column="session_type" jdbcType="INTEGER"/>
            <result property="bridgeHangupCause" column="bridge_hangup_cause" jdbcType="VARCHAR"/>
            <result property="busData" column="bus_data" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="hangupPhrase" column="hangup_phrase" jdbcType="VARCHAR"/>
            <result property="waitsec" column="waitsec" jdbcType="BIGINT"/>
            <result property="bridgeStamp" column="bridge_stamp" jdbcType="TIMESTAMP"/>
            <result property="agentAnswered" column="agent_answered" jdbcType="INTEGER"/>
            <result property="staffStartStamp" column="staff_start_stamp" jdbcType="TIMESTAMP"/>
            <result property="dataUpdateTime" column="data_update_time" jdbcType="TIMESTAMP"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="csBillsec" column="cs_billsec" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,uuid,system_type,call_type,
        agent_id,agent_user_id,call_planner_id,
        user,agent_name,customer_uid,
        customer_name,phone,answered,
        start_stamp,end_stamp,ring,
        answer_stamp,duration,hangup_by,
        hangup_cause,direction,record_url,
        billsec,latest_planner_no,call_uuid,
        core_uuid,bridge_uuid,caller_id_name,
        caller_id_number,destination_number,hangup_disposition,
        gw_ip,gw_name,call_talking10,
        session_type,bridge_hangup_cause,bus_data,
        created_at,updated_at,hangup_phrase,
        waitsec,bridge_stamp,agent_answered,
        staff_start_stamp,data_update_time,tenant_id,cs_billsec
    </sql>


    <select id="selectRecordByPage" resultType="com.yirendai.workbench.entity.CallcenterCallRecord">
        SELECT * FROM callcenter_call_record
        <where>
            <if test="request.code != null and request.code != ''">
                AND call_planner_no = #{request.code}
            </if>
            <if test="request.realName != null and request.realName != ''">
                AND agent_name = #{request.realName}
            </if>
            <if test="request.startStampBegin != null and request.startStampBegin != ''">
                AND start_stamp &gt;= #{request.startStampBegin}
            </if>
            <if test="request.startStampEnd != null and request.startStampEnd != ''">
                AND start_stamp &lt;= #{request.startStampEnd}
            </if>
            <if test="request.customerUid != null and request.customerUid != ''">
                AND customer_uid = #{request.customerUid}
            </if>
            <if test="request.phone != null and request.phone != ''">
                AND phone = #{request.phone}
            </if>
            <if test="request.ringTimeBegin != null">
                AND ring &gt;= #{request.ringTimeBegin}
            </if>
            <if test="request.ringTimeEnd != null">
                AND ring &lt;= #{request.ringTimeEnd}
            </if>
            <if test="request.billsecBegin != null">
                AND billsec &gt;= #{request.billsecBegin}
            </if>
            <if test="request.billsecEnd != null">
                AND billsec &lt;= #{request.billsecEnd}
            </if>
            <if test="request.uuid != null and request.uuid != ''">
                AND uuid = #{request.uuid}
            </if>
            <if test="codeList != null and codeList.size() > 0">
                AND call_planner_no IN
                <foreach item="item" index="index" collection="codeList" open="(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where> ORDER BY created_at DESC LIMIT #{request.pageSize} OFFSET #{offset}
    </select>

    <select id="countRecords" resultType="java.lang.Long">
        SELECT COUNT(*) FROM callcenter_call_record
        <where>
            <if test="request.code != null and request.code != ''">
                AND call_planner_no = #{request.code}
            </if>
            <if test="request.realName != null and request.realName != ''">
                AND agent_name = #{request.realName}
            </if>
            <if test="request.startStampBegin != null and request.startStampBegin != ''">
                AND start_stamp &gt;= #{request.startStampBegin}
            </if>
            <if test="request.startStampEnd != null and request.startStampEnd != ''">
                AND start_stamp &lt;= #{request.startStampEnd}
            </if>
            <if test="request.customerUid != null and request.customerUid != ''">
                AND customer_uid = #{request.customerUid}
            </if>
            <if test="request.phone != null and request.phone != ''">
                AND phone = #{request.phone}
            </if>
            <if test="request.ringTimeBegin != null">
                AND ring &gt;= #{request.ringTimeBegin}
            </if>
            <if test="request.ringTimeEnd != null">
                AND ring &lt;= #{request.ringTimeEnd}
            </if>
            <if test="request.billsecBegin != null">
                AND billsec &gt;= #{request.billsecBegin}
            </if>
            <if test="request.billsecEnd != null">
                AND billsec &lt;= #{request.billsecEnd}
            </if>
            <if test="request.uuid != null and request.uuid != ''">
                AND uuid = #{request.uuid}
            </if>
            <if test="codeList != null and codeList.size() > 0">
                AND call_planner_no IN
                <foreach item="item" index="index" collection="codeList" open="(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCallCenterPlannerList" resultType="com.yirendai.workbench.entity.CallcenterCallRecord">
        select call_planner_id, max(agent_name) as agent_name
        from callcenter_call_record
        where
              system_type = 2 and planner_id is not null and length(trim(planner_id)) != 0
              and agent_name is not null and length(trim(agent_name)) != 0
        group by planner_id
    </select>

    <select id="countTotalCallDuration" resultType="java.lang.Long">
        SELECT SUM(cs_billsec) FROM callcenter_call_record
        <where>
            <choose>
                <when test="userId != null and userId != '' and virtualId != null and virtualId != ''">
                    AND (customer_uid = #{userId} or customer_uid = #{virtualId})
                </when>
                <when test="userId != null and userId != ''">
                    AND customer_uid = #{userId}
                </when>
                <when test="virtualId != null and virtualId != ''">
                    AND customer_uid = #{virtualId}
                </when>
            </choose>
            <if test="startTime != null">
                AND created_at &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND created_at &lt;= #{endTime}
            </if>
            <if test="plannerId != null and plannerId != ''">
                AND planner_id = #{plannerId}
            </if>
        </where>
    </select>

    <select id="existAvailCall" resultType="boolean">
        SELECT count(*) >0 FROM callcenter_call_record
        <where>
            <choose>
                <when test="userId != null and userId != '' and virtualId != null and virtualId != ''">
                    AND (customer_uid = #{userId} or customer_uid = #{virtualId})
                </when>
                <when test="userId != null and userId != ''">
                    AND customer_uid = #{userId}
                </when>
                <when test="virtualId != null and virtualId != ''">
                    AND customer_uid = #{virtualId}
                </when>
            </choose>
            <if test="startTime != null">
                AND created_at &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND created_at &lt;= #{endTime}
            </if>
            <if test="plannerId != null and plannerId != ''">
                AND planner_id = #{plannerId}
            </if>
            and call_type in  (2, 4)
            and cs_billsec > 0
        </where>
        limit 1
    </select>

    <select id="getCallDurationAndTimes"
            resultType="com.yirendai.workbench.vo.res.callcenter.CallDurationTimes">
        SELECT COUNT(*) AS callTimes, SUM(billsec) as callDuration FROM callcenter_call_record
        WHERE planner_id = #{plannerId}
          AND start_stamp &gt;= #{startTime}
          AND start_stamp &lt;= #{endTime}
    </select>
    <select id="getRecordInfo"
            resultType="com.yirendai.workbench.vo.res.callcenter.HouyuRecordInfoVo">
        select customer_uid userId, max(start_stamp ) as lastCommunicationTime, max(cs_answer_stamp) lastCallTime
        from callcenter_call_record
        where customer_uid in
        <foreach item="item" index="index" collection="userIdList" open="(" separator=", " close=")">
            #{item}
        </foreach>
        group by customer_uid
    </select>
    <select id="findByUUIDList" resultType="com.yirendai.workbench.entity.AiAnalysisTaskCallRecord">
        SELECT
            record.id as callRecordId,
            record.planner_id as plannerNo,
            record.agent_name as agentName,
            record.uuid as uuid,
            record.call_type as callType,
            record.phone as phone,
            record.agent_dept as agentDept,
            record.tenant_id as tenantId,
            record.record_url as recordUrl,
            contact.id as chatContactId
        FROM callcenter_call_record record
        left join ai_planner_chat_contact contact on record.uuid = contact.bus_id
        WHERE record.uuid IN
        <foreach item="item" index="index" collection="uuidGroup" open="(" separator=", " close=")">
        #{item}
        </foreach>
        <if test="scopePlannerIds!=null and scopePlannerIds.size()>0">
            AND record.planner_id IN
            <foreach item="item" index="index" collection="scopePlannerIds" open="(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        AND record.tenant_id = #{tenantId}

    </select>
</mapper>
