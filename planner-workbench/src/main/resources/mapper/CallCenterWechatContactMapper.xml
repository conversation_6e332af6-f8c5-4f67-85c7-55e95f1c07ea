<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallCenterWechatContactMapper">

    <!-- 微信联系人及最新消息结果映射 -->
    <resultMap id="WechatContactWithMessageResultMap" type="com.yirendai.workbench.vo.res.callcenter.WechatContactListRes">
        <result column="customer_wechat_id" property="customerWechatId" />
        <result column="customer_wechat_no" property="customerWechatNo" />
        <result column="customer_wechat_img" property="customerWechatImg" />
        <result column="customer_wechat_nickname" property="customerWechatNickname" />
        <result column="customer_remark" property="customerRemark" />
        <result column="message_type" property="messageType" />
        <result column="message_content" property="messageContent" />
        <result column="last_message_time" property="lastMessageTime" />
    </resultMap>

    <select id="pageWechatContactWithLatestMessage" resultMap="WechatContactWithMessageResultMap">
        SELECT DISTINCT
        c.customer_wechat_id,
        c.customer_wechat_no,
        c.customer_wechat_img,
        c.customer_wechat_nickname,
        c.customer_remark,
        m1.type as message_type,
        m1.content as message_content,
        m1.send_time as last_message_time
        FROM callcenter_wechat_contact c
        INNER JOIN callcenter_wechat_message m1 ON
        m1.tenant_id = #{tenantId}
        AND m1.user_no = c.planner_no
        AND (
        (m1.sender = c.customer_wechat_id AND m1.recipient = c.planner_wechat_id)
        OR (m1.sender = c.planner_wechat_id AND m1.recipient = c.customer_wechat_id)
        )
        INNER JOIN (
        -- 预聚合每个员工-客户组合的最新消息时间
        SELECT
        user_no,
        CASE
        WHEN sender = recipient THEN sender -- 处理特殊情况
        WHEN sender != #{plannerWechatId} THEN sender
        ELSE recipient
        END as contact_wechat_id,
        MAX(send_time) as max_send_time
        FROM callcenter_wechat_message
        WHERE tenant_id = #{tenantId}
        AND user_no = #{plannerNo}
        AND (sender =  #{plannerWechatId} OR recipient =  #{plannerWechatId})
        GROUP BY user_no,
        CASE
        WHEN sender = recipient THEN sender
        WHEN sender !=  #{plannerWechatId} THEN sender
        ELSE recipient
        END
        ) latest ON
        latest.user_no = m1.user_no
        AND latest.contact_wechat_id = c.customer_wechat_id
        AND latest.max_send_time = m1.send_time
        WHERE c.tenant_id = #{tenantId}
        AND c.planner_wechat_id = #{plannerWechatId}
        AND c.planner_no = #{plannerNo}
        <if test="customerWechat != null and customerWechat != ''">
            AND (c.customer_wechat_no LIKE CONCAT('%', #{customerWechat}, '%')
            OR c.customer_wechat_nickname LIKE CONCAT('%', #{customerWechat}, '%'))
        </if>
        ORDER BY m1.send_time DESC
    </select>

</mapper>
