<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiAnalysisTaskSceneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiAnalysisTaskScene">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="scene_id" property="sceneId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, scene_id
    </sql>

</mapper>
