<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.FpcReceptionDateTimeMapper">
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.FpcReceptionDateTime">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="member_no" jdbcType="INTEGER" property="memberNo" />
        <result column="member_name" jdbcType="VARCHAR" property="memberName" />
        <result column="member_city" jdbcType="INTEGER" property="memberCity" />
        <result column="reception_date" jdbcType="DATE" property="receptionDate" />
        <result column="booked_date_count" jdbcType="INTEGER" property="bookedDateCount" />
        <result column="reception_time" jdbcType="VARCHAR" property="receptionTime" />
        <result column="booked_time_count" jdbcType="INTEGER" property="bookedTimeCount" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, member_no, member_name, member_city, reception_date, booked_date_count, reception_time,
        booked_time_count, status, create_time, update_time
    </sql>

    <update id="updateDateCount">
        update fpc_reception_date_time
        set booked_date_count = booked_date_count + 1,
        update_time = now()
        where member_no = #{memberNo,jdbcType=INTEGER}
        and
        reception_date = #{receptionDate,jdbcType=DATE}
    </update>
    <update id="updateTimeCount">
        update fpc_reception_date_time
        set booked_time_count = booked_time_count + 1,
        update_time = now()
        where member_no = #{memberNo,jdbcType=INTEGER}
        and reception_date = #{receptionDate,jdbcType=DATE}
        and reception_time = #{receptionTime,jdbcType=VARCHAR}
    </update>

    <update id="updateInitDateAndTimeCount" parameterType="java.util.List">
        update fpc_reception_date_time
        set booked_time_count = 0,booked_date_count = 0,
        update_time = now()
        where reception_date IN
        <foreach collection="signDateList" item="signDate" index="index" open="(" close=")" separator=",">
            #{signDate}
        </foreach>
    </update>

</mapper>
