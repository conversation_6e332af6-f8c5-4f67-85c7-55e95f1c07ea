<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.bmdInsurance.BmdInsuranceApplyDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.bmdInsurance.BmdInsuranceApplyDetailDto">
        <id column="id" property="id"/>
        <result column="apply_record_id" property="applyRecordId"/>
        <result column="form_type" property="formType"/>
        <result column="form_data" property="formData"/>
        <result column="translated_data" property="translatedData"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, apply_record_id, form_type, form_data, translated_data, create_time, update_time
    </sql>

    <!-- 根据申请记录ID和表单类型更新记录 -->
    <update id="updateByRecordIdAndFormType">
        update bmd_insurance_apply_detail
        <set>
            <if test="formData != null and formData != ''">
                form_data = #{formData},
            </if>
            <if test="translatedData != null and translatedData != ''">
                translated_data = #{translatedData},
            </if>
            update_time = now()
        </set>
        where apply_record_id = #{applyRecordId} and form_type = #{formType}
    </update>

    <!-- 批量更新记录 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update bmd_insurance_apply_detail
            <set>
                <if test="item.formData != null and item.formData != ''">
                    form_data = #{item.formData},
                </if>
                <if test="item.translatedData != null and item.translatedData != ''">
                    translated_data = #{item.translatedData},
                </if>
                update_time = now()
            </set>
            where id = #{item.id}
        </foreach>
    </update>

</mapper> 