<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiHelperOpeningRemarksMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="aiHelperOpeningRemarksResultMap" type="com.yirendai.workbench.entity.AiHelperOpeningRemarks">
        <id column="id" property="id"/>
        <result column="text_content" property="textContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="usage_count" property="usageCount"/>
        <result column="is_approved" property="isApproved"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="approver_userId" property="approverUserid"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>



    <select id="selectAiHelperOpeningRemarksPage" resultMap="aiHelperOpeningRemarksResultMap">
        select * from ai_helper_opening_remarks where is_deleted = 0
    </select>

    <select id="selectOneByTenantId" resultMap="aiHelperOpeningRemarksResultMap">
        SELECT * FROM ai_helper_opening_remarks
                 WHERE is_deleted = 0
                 and tenant_id = #{tenantId}
                 ORDER BY RAND() LIMIT 1
    </select>

    <update id="updateUsageCount">
        update ai_helper_opening_remarks set usage_count = usage_count + 1 where id = #{id}
    </update>
</mapper>
