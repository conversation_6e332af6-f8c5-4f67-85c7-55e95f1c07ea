<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.robot.modules.call.mapper.RobotCallFrequencyOperationMapper">

    <resultMap id="OperationMap" type="com.yirendai.robot.modules.call.vo.RobotCallFrequencyOperationVO">
        <result column="operation_type" property="operationType"/>
        <result column="operator_name" property="operatorName"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="pageRobotFrequencyByRule" resultMap="OperationMap">
        select operation_type,
               operator_name,
               remark,
               create_time
        from ai_robot_call_frequency_operation
        where rule_id = #{ruleId}
        order by create_time desc
    </select>

</mapper>
