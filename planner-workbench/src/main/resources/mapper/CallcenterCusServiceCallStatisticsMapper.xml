<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterCusServiceCallStatisticsMapper">

    <select id="getCallInStatistics" resultType="com.yirendai.workbench.entity.CallcenterCusServiceCallStatistics">
        SELECT tenant_id,
               planner_id,
               COUNT(*)                                     call_in_calls,
               COUNT(CASE WHEN cs_answered = 2 THEN 1 END)  AS call_in_connected_calls,  -- 接通量
               COUNT(DISTINCT customer_uid)                 call_in_customer,
               COALESCE(SUM(cs_billsec),0)                                 call_in_cs_billsec,
               CASE
                   WHEN COUNT(CASE WHEN cs_answered = 2 THEN 1 END) > 0 THEN
                       ROUND(COALESCE(SUM(cs_billsec),0) / COUNT(CASE WHEN cs_answered = 2 THEN 1 END))
                   ELSE 0
                   END                                   AS call_in_average_duration, -- 平均单通时长（秒）
               COUNT(CASE WHEN cs_billsec > 300 THEN 1 END) AS gt_five_minutes,
               COUNT(CASE WHEN satisfaction_level = 'satisfied' THEN 1 END) call_in_satisfaction
        FROM callcenter_call_record
        WHERE call_type = 1
          <if test="type != null and type == 2">
            and contact_way = 1
          </if>
          and start_stamp
            &gt; #{startTime}
          AND start_stamp &lt;= #{endTime}
        GROUP BY tenant_id,
                 planner_id
    </select>
    <select id="getCallBackStatistics" resultType="com.yirendai.workbench.entity.CallcenterCusServiceCallStatistics">
        SELECT tenant_id,
               planner_id,
               COUNT(*)                                                           call_back_calls,              -- 通话量
               COUNT(CASE WHEN cs_answered = 2 THEN 1 END)                     AS call_back_connected_calls,    -- 接通量
               COUNT(DISTINCT customer_uid)                                       call_back_customer,           -- 客户量
               COUNT(DISTINCT CASE WHEN cs_answered = 2 THEN customer_uid END) AS call_back_customer_connected, -- 接通客户数
               COALESCE(SUM(cs_duration),0)                                                   call_back_cs_duration,        -- 回访呼出时长
               COALESCE(SUM(cs_billsec),0)                                                call_back_cs_billsec,         -- 回访通话时长
               CASE
                   WHEN COUNT(CASE WHEN cs_answered = 2 THEN 1 END) > 0 THEN
                       ROUND(COALESCE(SUM(cs_billsec),0) / COUNT(CASE WHEN cs_answered = 2 THEN 1 END))
                   ELSE 0
                   END                                                         AS average_call_duration,        -- 平均单通时长（秒）
               COUNT(CASE WHEN cs_billsec > 300 THEN 1 END)                    AS gt_five_minutes
        FROM callcenter_call_record
        WHERE call_type = 3
        <if test="type != null and type == 2">
            and contact_way in (1, 2)
        </if>
          and start_stamp
            &gt; #{startTime}
          AND start_stamp &lt;= #{endTime}
        GROUP BY tenant_id,
                 planner_id
    </select>

    <select id="getCusServiceCallStatistics" resultType="com.yirendai.workbench.entity.CallcenterCusServiceCallStatistics">
        SELECT sum(call_in_calls)             call_in_calls,
               sum(call_in_customer)          call_in_customer,
               SUM(call_in_cs_billsec)        call_in_cs_billsec,
               SUM(call_in_satisfaction)       call_in_satisfaction,
               sum(call_back_calls)           call_back_calls,
               sum(call_back_connected_calls) call_back_connected_calls,
               sum(call_back_customer)        call_back_customer,
               SUM(call_back_cs_billsec)      call_back_cs_billsec
        FROM callcenter_cus_service_call_statistics
        WHERE statistics_date &gt;= #{startTime}
          AND statistics_date &lt;= #{endTime}
          and tenant_id = #{tenantId}
          and planner_id = #{plannerId}
          and type = 2
    </select>
</mapper>
