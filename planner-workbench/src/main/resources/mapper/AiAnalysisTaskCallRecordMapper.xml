<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiAnalysisTaskCallRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiAnalysisTaskCallRecord">
        <id column="id" property="id" />
        <result column="analysis_task_id" property="analysisTaskId" />
        <result column="analysis_task_data_id" property="analysisTaskDataId" />
        <result column="scene_id" property="sceneId" />
        <result column="call_record_id" property="callRecordId" />
        <result column="chat_contact_id" property="chatContactId" />
        <result column="planner_no" property="plannerNo" />
        <result column="agent_name" property="agentName" />
        <result column="uuid" property="uuid" />
        <result column="call_type" property="callType" />
        <result column="phone" property="phone" />
        <result column="agent_dept" property="agentDept" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="analysis_time" property="analysisTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="record_url" property="recordUrl" />
        <result column="record_type" property="recordType" />
        <result column="customer_id" property="customerId" />
        <result column="data_source_type" property="dataSourceType" />
        <result column="data_source_id" property="dataSourceId" />
        <result column="customer_base_data" property="customerBaseData" />
        <result column="customer_business_data" property="customerBusinessData" />
        <result column="data_source_types" property="dataSourceTypes" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, analysis_task_id, analysis_task_data_id, scene_id, call_record_id, chat_contact_id, planner_no, agent_name, uuid, call_type, phone, agent_dept, tenant_id, create_user, create_user_name, create_time, update_user, update_time, status, analysis_time, is_deleted, record_url, record_type, customer_id, data_source_type, data_source_id, customer_base_data, customer_business_data, data_source_types
    </sql>
    <select id="pageList" resultType="com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskCallRecordVO">
        select
        *
        from ai_analysis_task_call_record
        <where>
            <if test="req.analysisTaskId != null">
                and analysis_task_id = #{req.analysisTaskId}
            </if>
            <if test="req.plannerNo != null and req.plannerNo !=''">
                and planner_no = #{req.plannerNo}
            </if>
            <if test="req.agentName != null  and req.agentName !=''">
                and agent_name like concat('%',#{req.agentName},'%')
            </if>
            <if test="req.uuid != null  and req.uuid !=''">
                and uuid = #{req.uuid}
            </if>
            <if test="req.callType != null">
                and call_type = #{req.callType}
            </if>
            <if test="req.phone != null and req.phone !=''">
                and phone = #{req.phone}
            </if>
            <if test="req.agentDept != null  and req.agentDept !=''">
                and agent_dept = #{req.agentDept}
            </if>
            <if test="req.tenantId != null  and req.tenantId !=''">
                and tenant_id = #{req.tenantId}
            </if>
            <if test="req.status != null and req.status.size()>0">
                and status in
                <foreach item="item" collection="req.status" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="req.analysisTime != null">
                and analysis_time = #{req.analysisTime}
            </if>
            <if test="req.configSceneIds != null and req.configSceneIds.size()>0 ">
                and id in
                (select
                analysis_task_call_record_id
                from ai_analysis_task_result result
                where result.analysis_task_call_record_id = ai_analysis_task_call_record.id
                and result.status =2
                and result.scene_id in
                <foreach item="item" collection="req.configSceneIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                and result.scene_result_id >0
                )
            </if>
            <if test="req.configSceneResultIds != null and req.configSceneResultIds.size()>0 ">
                and id in
                (select
                analysis_task_call_record_id
                from ai_analysis_task_result result
                where result.analysis_task_call_record_id = ai_analysis_task_call_record.id
                and result.scene_result_id in
                <foreach item="item" collection="req.configSceneResultIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                )
            </if>
            and is_deleted = 0
        </where>
    </select>
    <select id="selectTop100ByTaskId" resultType="com.yirendai.workbench.entity.AiAnalysisTaskCallRecord">
        select
        *
        from ai_analysis_task_call_record
        where analysis_task_id = #{taskId}
        and is_deleted = 0
        and status in (0,1)
        order by id
        limit 100
    </select>
    <select id="selectCountByTaskId" resultType="java.lang.Integer">
        select
        count(1)
        from ai_analysis_task_call_record
        where analysis_task_id = #{taskId}
        and is_deleted = 0
    </select>

</mapper>
