<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiAnalysisTaskDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiAnalysisTaskData">
        <id column="id" property="id"/>
        <result column="analysis_task_id" property="analysisTaskId"/>
        <result column="scene_ids" property="sceneIds"/>
        <result column="data_source_type" property="dataSourceType"/>
        <result column="data_source_id" property="dataSourceId"/>
        <result column="customer_id" property="customerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="planner_no" property="plannerNo"/>
        <result column="content" property="content"/>
        <result column="result_name" property="resultName"/>
        <result column="result_des" property="resultDes"/>
        <result column="total_sub_task_count" property="totalSubTaskCount"/>
        <result column="finished_sub_task_count" property="finishedSubTaskCount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="analysis_time" property="analysisTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, analysis_task_id, scene_ids, data_source_type, data_source_id, customer_id, customer_name, planner_no, content, result_name, result_des, total_sub_task_count, finished_sub_task_count, tenant_id, create_user, create_user_name, create_time, update_user, update_time, status, analysis_time, is_deleted
    </sql>

    <select id="selectTop100ByTaskId" resultType="com.yirendai.workbench.entity.AiAnalysisTaskData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_analysis_task_data
        WHERE analysis_task_id = #{analysisTaskId}
        AND is_deleted = 0
        <if test="statusList != null and statusList.size() > 0">
            AND status IN
            <foreach item="item" index="index" collection="statusList" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY id
        LIMIT 100
    </select>

    <select id="countByTaskId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ai_analysis_task_data
        WHERE analysis_task_id = #{analysisTaskId}
          AND is_deleted = 0
    </select>

    <select id="countDistinctCustomerByTaskId" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT customer_id)
        FROM ai_analysis_task_data
        WHERE analysis_task_id = #{analysisTaskId}
          AND is_deleted = 0
    </select>

</mapper>
