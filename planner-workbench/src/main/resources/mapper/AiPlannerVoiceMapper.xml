<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiPlannerVoiceMapper">
    <select id="selectListByConditions" resultType="com.yirendai.voiceaiserver.vo.response.AiPlannerVoiceListRes">
        SELECT
        voice.*,
        staff.member_name name,
        staff.groups_name groupsName
        FROM
        ai_planner_voice voice
        left join
        cs_staff_info    staff
        on
        voice.staff_id = staff.member_external_number
        where voice.status != 0
        <if test="account != null and account != ''">
            and voice.account = #{account}
        </if>
        <if test="status != null and status != ''">
            and voice.status = #{status}
        </if>
    </select>
</mapper>
