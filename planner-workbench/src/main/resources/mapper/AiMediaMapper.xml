<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiMediaMapper">
    <resultMap id="AiMediaPO" type="com.yirendai.voiceaiserver.model.po.AiChatPO"/>

    <select id="getHandleList" resultMap="AiMediaPO">
        SELECT id, origin_content, processed_content
        FROM ai_planner_chat_contact
        where origin_type = #{type}
          AND origin_content is not null AND origin_content != ''
          AND (processed_content = '' OR processed_content is null)
        LIMIT #{limit}
    </select>

    <select id="getHandleCount" resultType="long">
        SELECT count(1)
        FROM ai_planner_chat_contact
        where origin_type = #{type}
          AND origin_content is not null AND origin_content != ''
          AND (processed_content = '' OR processed_content is null)
    </select>

</mapper>
