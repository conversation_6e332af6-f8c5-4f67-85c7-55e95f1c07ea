<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterCallStatisticsMapper">

    <select id="callStatistics" resultType="com.yirendai.workbench.entity.CallcenterCallStatistics">
        SELECT
        tenant_id,
        planner_id,
        COUNT(*)                     total_outbound_calls,
        COUNT(CASE WHEN cs_answered = 2 THEN 1 END) AS connected_calls,
        COUNT(CASE WHEN cs_answered != 2 OR cs_answered IS NULL THEN 1 END) AS unconnected_calls, -- 未接通量
        ROUND(
        (COUNT(CASE WHEN cs_answered = 2 THEN 1 END) / COUNT(*)) * 100
        ) AS connection_rate, -- 接通率（单位：%）
        COUNT(DISTINCT customer_uid) customer_called,
        COUNT(DISTINCT CASE WHEN cs_answered = 2 THEN customer_uid END) AS customer_connected, -- 接通客户数
        COALESCE(SUM(cs_duration),0)             total_cs_duration,
        COALESCE(SUM(cs_billsec),0)              total_cs_billsec,
        COALESCE(SUM(cs_ring) ,0)                total_cs_ring,
        CASE
        WHEN COUNT(CASE WHEN cs_answered = 2 THEN 1 END) > 0 THEN
        ROUND(COALESCE(SUM(cs_billsec),0) / COUNT(CASE WHEN cs_answered = 2 THEN 1 END))
        ELSE 0
        END AS average_call_duration -- 平均单通时长（秒）
        FROM
        callcenter_call_record
        WHERE
        call_type = 2
        and start_stamp &gt; #{startTime}
        AND start_stamp &lt;= #{endTime}
        GROUP BY
        tenant_id,
        planner_id
    </select>
</mapper>
