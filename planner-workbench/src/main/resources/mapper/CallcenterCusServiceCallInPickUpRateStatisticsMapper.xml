<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterCusServiceCallInPickUpRateStatisticsMapper">
    <select id="getCallInPickUpRateStatistics"
            resultType="com.yirendai.workbench.entity.CallcenterCusServiceCallInPickUpRateStatistics">
        SELECT tenant_id,
               DATE_FORMAT(start_stamp, '%Y-%m-%d') AS statistics_date, -- 日期部分
               DATE_FORMAT(start_stamp, '%H')       AS statistics_hour, -- 小时部分
               CASE WHEN MINUTE (start_stamp)  &lt;
        30 THEN '00-30' ELSE '30-60'
        END
        AS statistics_minute, -- 半小时部分
        COUNT(*) AS call_in_count,
        SUM(CASE WHEN cs_answered = 2 THEN 1 ELSE 0 END) AS connected_count,
        SUM(CASE WHEN cs_answered = 1 THEN 1 ELSE 0 END) AS abandoned_count,
        ROUND(
        IF(SUM(CASE WHEN cs_answered = 1 THEN 1 ELSE 0 END) = 0, 0,
        SUM(CASE WHEN cs_answered = 1 THEN ring ELSE 0 END) /
        SUM(CASE WHEN cs_answered = 1 THEN 1 ELSE 0 END)
        ), 0
        ) AS avg_abandon_duration,
        ROUND(
        IF(COUNT(*) = 0, 0,
        SUM(CASE WHEN cs_answered = 2 THEN 1 ELSE 0 END) / COUNT(*) * 100
        ), 2
        ) AS connect_rate
        FROM
        callcenter_call_record
        WHERE
        call_type = 1
        and contact_way = 1
        AND start_stamp >= NOW() - INTERVAL 4 HOUR
        GROUP BY
        tenant_id,
        statistics_date, statistics_hour, statistics_minute
        order by
        tenant_id,
        statistics_date, statistics_hour, statistics_minute
    </select>
    <select id="getCallInVolume" resultType="com.yirendai.workbench.vo.res.callcenter.HourStatisticsRes">
        SELECT DATE_FORMAT(start_stamp, '%H') AS statisticsHour, -- 小时部分
               COUNT(*)                       AS callInCount
        FROM
            callcenter_call_record
        WHERE
            tenant_id = #{tenantId}
          AND call_type = 1
          and contact_way = 1
          AND start_stamp &gt;= #{startTime}
          AND start_stamp &lt;= #{endTime}
        GROUP BY
            statisticsHour
        ORDER BY
            statisticsHour
    </select>
    <select id="getCallInConnectedCount"
            resultType="com.yirendai.workbench.vo.res.callcenter.CallInConnectedStatisticsRes">
        SELECT COUNT(*) AS count,
        SUM(CASE WHEN cs_answered = 2 THEN 1 ELSE 0 END) AS connected_count,
        COUNT(CASE WHEN satisfaction_level = 'satisfied' THEN 1 END) call_in_satisfaction,
        COUNT(CASE WHEN satisfaction_level IS NOT NULL THEN 1 END) all_satisfaction

        FROM
            callcenter_call_record
        WHERE
            tenant_id = #{tenantId}
          AND call_type = 1
          and contact_way = 1
          AND start_stamp &gt;= #{startTime}
          AND start_stamp &lt;= #{endTime}
    </select>
</mapper>
