<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AdsCsgSmallFixedAchievementDfMapper">

    <select id="getPlannerAchievements" resultType="com.yirendai.workbench.entity.AdsCsgSmallFixedAchievementDf">
        select staff_id, max(staff_name) as staff_name, sum(fin_amount) as fin_amount
        from ads_csg_small_fixed_achievement_df
        where fin_amount is not null and staff_id is not null and staff_name is not null and staff_name != ''
        group by staff_id
    </select>

    <select id="getPlannerUsers" resultType="com.yirendai.workbench.entity.AdsCsgSmallFixedAchievementDf">
        select staff_id, user_id, min(STR_TO_DATE(pay_time, '%Y-%m-%d %H:%i:%s')) as pay_time
        from ads_csg_small_fixed_achievement_df
        where
            pay_time is not null and pay_time != ''
            and user_id is not null and user_id != ''
            and staff_id is not null
        group by staff_id, user_id
    </select>

    <select id="get001ByAmount" resultType="decimal">
        select sum(amount) as totalAmount from (
            select user_id, sum(pay_amt) as amount
            from ads_qw_bfixed_order_detail_df
            where user_id=#{userId} and pay_date >= #{developStartTime} and pay_date &lt;= #{developEndTime}
            union all
            select user_id, sum(invest_amount) as amount
            from ads_csg_big_fixed_achievement_df
            where user_id=#{userId} and receive_payment_time  >= #{developStartTime} and receive_payment_time &lt;= #{developEndTime}
            union all
            select user_id, sum(pay_amt) as amount
            from ads_qw_sfixed_order_detail_df
            where user_id=#{userId} and pay_date >= #{developStartTime}  and pay_date &lt;= #{developEndTime}
            union all
            select user_id, sum(fin_amount) as amount
            from ads_csg_small_fixed_achievement_df
            where user_id=#{userId} and pay_time >= #{developStartTime} and pay_time &lt;= #{developEndTime}
        ) t
    </select>

</mapper>
