<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.callcenter.CallcenterChannelApiMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.callcenter.CallcenterChannelApi">
            <id property="id" column="id" />
            <result property="menu" column="menu" />
            <result property="subMenu" column="sub_menu" />
            <result property="url" column="url" />
            <result property="isInner" column="is_inner" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="tenantId" column="tenant_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,menu,sub_menu,url,is_inner,create_time,
        update_time,tenant_id
    </sql>
</mapper>
