<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterUserMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.callcenter.CallcenterUser">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="virtualUserId" column="virtual_user_id" />
            <result property="userName" column="user_name" />
            <result property="userPhone" column="user_phone" />
            <result property="virtualUser" column="virtual_user" />
            <result property="source" column="source" />
            <result property="tenantId" column="tenant_id" />
            <result property="createUser" column="create_user" />
            <result property="createTime" column="create_time" />
            <result property="updateUser" column="update_user" />
            <result property="updateTime" column="update_time" />
            <result property="userType" column="user_type" />
            <result property="userStatus" column="user_status" />
            <result property="sceneId" column="scene_id" />
            <result property="groupId" column="group_id" />
            <result property="developCycle" column="develop_cycle" />
            <result property="developStartTime" column="develop_start_time" />
            <result property="extensionDevelopCycle" column="extension_develop_cycle" />
            <result property="developEndTime" column="develop_end_time" />
            <result property="followUpStatus" column="follow_up_status" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,virtual_user_id,user_name,user_phone,virtual_user,
        source,tenant_id,create_user,create_time,update_user,
        update_time,user_type,user_status,scene_id,group_id,
        develop_cycle,develop_start_time,extension_develop_cycle,develop_end_time,follow_up_status
    </sql>

    <select id="getUnAllocateUnLockUserList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM callcenter_user u
        where
        group_id = #{groupId} and user_status = 1 and user_type=1 and tenant_id = #{tenantId}
        <if test="offsetId != null">
            and id > #{offsetId}
        </if>
        and not exists(select * from callcenter_user_lock l where l.user_id=u.user_id and lock_status=1)
        ORDER BY u.id
        LIMIT #{size}

    </select>

    <select id="getUnFollowUserList" resultType="com.yirendai.workbench.entity.callcenter.UserDevelopInfo">
        select u.id, u.user_id, u.virtual_user_id, u.develop_start_time, u.develop_end_time, u.tenant_id, b.planner_id
        from callcenter_user u join planner_user_bind b on b.user_id=u.user_id
        where user_status=2 and follow_up_status=1
        <if test="tenantId != null">
            and u.tenant_id=#{tenantId}
        </if>
        <if test="offsetId != null">
            and u.id > #{offsetId}
        </if>
        order by u.id  LIMIT #{size}
    </select>

    <select id="getAllocatedNoCrmList" resultType="com.yirendai.workbench.entity.callcenter.UserDevelopInfo">
        select u.id, u.user_id, u.virtual_user_id, u.develop_start_time, u.develop_end_time, u.tenant_id, b.planner_id
        from callcenter_user u join planner_user_bind b on b.user_id=u.user_id
        where
        user_status = 2 and user_type=1 and u.tenant_id = #{tenantId} and u.develop_end_time >= #{developStartDate} and u.develop_end_time &lt;= #{developEndDate}
        <if test="offsetId != null">
            and u.id > #{offsetId}
        </if>
        ORDER BY u.id  LIMIT #{size}
    </select>

    <update id="batchSetCustomerToFollowUp">
        <foreach collection="list" item="item" separator=";">
            UPDATE callcenter_user
            SET follow_up_status = #{item.followUpStatus}
            WHERE tenant_id = #{item.tenantId}
            <if test="item.userId != null">
                and user_id = #{item.userId})
            </if>
            <if test="item.virtualUserId != null">
                and virtual_user_id = #{item.virtualUserId})
            </if>
        </foreach>
    </update>

    <select id="findNeedChangeDeptUser" resultMap="BaseResultMap">
        select distinct(id)
        from (select cu.id
              from
                  planner_user_bind pub left join callcenter_user cu on pub.user_id = cu.user_id
              where pub.planner_id = #{plannerId}
                and pub.tenant_id = #{tenantId}
                and cu.group_id != #{groupId}
              union all
              select
                  cu.id
              from
                  planner_user_bind pub left join callcenter_user cu on  pub.user_id = cu.virtual_user_id
              where
                  pub.planner_id = #{plannerId}
                and pub.tenant_id = #{tenantId}
                and cu.group_id != #{groupId}
              ) as temp  where id>#{minId} order by id asc limit #{pageSize}
    </select>
</mapper>
