<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiAnalysisTaskResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiAnalysisTaskResult">
        <id column="id" property="id" />
        <result column="analysis_task_id" property="analysisTaskId" />
        <result column="analysis_task_data_id" property="analysisTaskDataId" />
        <result column="analysis_task_call_record_id" property="analysisTaskCallRecordId" />
        <result column="chat_contact_id" property="chatContactId" />
        <result column="scene_name" property="sceneName" />
        <result column="scene_id" property="sceneId" />
        <result column="result_name" property="resultName" />
        <result column="scene_result_id" property="sceneResultId" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="analysis_time" property="analysisTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="final_status" property="finalStatus" />
        <result column="data_source_types" property="dataSourceTypes" />
        <result column="match_reasons" property="matchReasons" />
        <result column="msg_times" property="msgTimes" />
        <result column="sub_task_id" property="subTaskId" />
        <result column="result_des" property="resultDes" />
        <result column="customer_id" property="customerId" />
    </resultMap>

    <select id="selectSceneCount"
            resultType="com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskSceneCountVO">
        select scene_id id,
               scene_name sceneName,
               count(1) resultCount
        from ai_analysis_task_result
        where analysis_task_id = #{taskId}
        and status = 2
        and is_deleted = 0
        group by scene_id
    </select>
    <select id="selectSceneResultCount"
            resultType="com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskSceneResultCountVO">
        select scene_result_id id,
               result_name resultName,
               count(1) resultCount
        from ai_analysis_task_result
        where analysis_task_id = #{taskId}
        and scene_id = #{sceneId}
        and status = 2
        and is_deleted = 0
        group by scene_result_id
    </select>
    <select id="listByCallRecordIdAndConditions"
            resultType="com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskResultVO">
        select id,
               analysis_task_id analysisTaskId,
               analysis_task_data_id analysisTaskDataId,
               analysis_task_call_record_id analysisTaskCallRecordId ,
               chat_contact_id chatContactId,
               scene_name sceneName,
               scene_id sceneId,
               result_name resultName,
               scene_result_id sceneResultId,
               tenant_id tenantId,
               create_user createUser,
               create_user_name createUserName,
               create_time createTime,
               update_user updateUser,
                update_time updateTime,
                status,
                analysis_time analysisTime,
                is_deleted isDeleted,
                final_status finalStatus,
                result_des resultDes,
                data_source_types dataSourceTypes,
                match_reasons matchReasons,
                msg_times msgTimes,
                sub_task_id subTaskId,
                customer_id customerId
        from ai_analysis_task_result
        where analysis_task_call_record_id = #{callRecordId}
        and status = 2
        and is_deleted = 0
        <if test="sceneIds != null and sceneIds.size() > 0">
            <foreach item="item" index="index" collection="sceneIds" open="and scene_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="resultIds != null and resultIds.size() > 0">
            <foreach item="item" index="index" collection="resultIds" open="and scene_result_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="listByDataIds" resultType="com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskResultVO">
        select id,
               analysis_task_id analysisTaskId,
               analysis_task_data_id analysisTaskDataId,
               analysis_task_call_record_id analysisTaskCallRecordId ,
               chat_contact_id chatContactId,
               scene_name sceneName,
               scene_id sceneId,
               result_name resultName,
               scene_result_id sceneResultId,
               tenant_id tenantId,
               create_user createUser,
               create_user_name createUserName,
               create_time createTime,
               update_user updateUser,
               update_time updateTime,
               status,
               analysis_time analysisTime,
               is_deleted isDeleted,
               final_status finalStatus,
               result_des resultDes,
               data_source_types dataSourceTypes,
               match_reasons matchReasons,
               msg_times msgTimes,
               sub_task_id subTaskId
        from ai_analysis_task_result
        where status = 2
          and is_deleted = 0
        <if test="dataIds != null and dataIds.size() > 0">
            <foreach item="item" index="index" collection="dataIds" open="and analysis_task_data_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="listByCallRecordId" resultType="com.yirendai.workbench.entity.AiAnalysisTaskResult">
        select * from
                     ai_analysis_task_result
        where analysis_task_call_record_id = #{callRecordId}
        <if test="statusList != null and statusList.size() > 0">
            <foreach item="item" index="index" collection="statusList" open="and status in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by id
    </select>

</mapper>
