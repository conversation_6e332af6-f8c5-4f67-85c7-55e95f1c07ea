<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterForbiddenMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CallcenterForbidden">
            <id property="id" column="id" />
            <result property="customId" column="custom_id" />
            <result property="phone" column="phone" />
            <result property="tenantId" column="tenant_id" />
            <result property="importType" column="import_type" />
            <result property="importUserCode" column="import_user_code" />
            <result property="forbidReason" column="forbid_reason" />
            <result property="forbiddenDuration" column="forbidden_duration" />
            <result property="forbiddenSince" column="forbidden_since" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,custom_id,phone,tenant_id,import_type,import_user_code,
        forbid_reason,forbidden_duration,forbidden_since,create_time,update_time
    </sql>
</mapper>
