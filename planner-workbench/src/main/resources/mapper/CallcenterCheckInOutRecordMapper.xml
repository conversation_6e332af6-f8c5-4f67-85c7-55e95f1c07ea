<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterCheckInOutRecordMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CallcenterCheckInOutRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
            <result property="agentuser" column="agentUser" jdbcType="VARCHAR"/>
            <result property="action" column="action" jdbcType="VARCHAR"/>
            <result property="subAction" column="sub_action" jdbcType="VARCHAR"/>
            <result property="actDate" column="act_date" jdbcType="DATE"/>
            <result property="actTime" column="act_time" jdbcType="TIME"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,agent_id,agentUser,action,subAction,
        act_date,act_time
    </sql>

  <select id="selectLatestRecordsByAgentIds" resultMap="BaseResultMap">
    SELECT *
    FROM callcenter_check_in_out_record
    WHERE agent_id IN
    <foreach item="id" collection="agentIdList" open="(" separator="," close=")">
      #{id}
    </foreach>
    AND id IN (
    SELECT MAX(id)
    FROM callcenter_check_in_out_record
    WHERE agent_id IN
    <foreach item="id" collection="agentIdList" open="(" separator="," close=")">
      #{id}
    </foreach>
    GROUP BY agent_id
    )
  </select>

</mapper>
