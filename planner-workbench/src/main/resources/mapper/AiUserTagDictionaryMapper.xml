<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiUserTagDictionaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="aiUserTagDictionaryResultMap" type="com.yirendai.workbench.entity.AiUserTagDictionary">
        <id column="id" property="id"/>
        <result column="tag_name" property="tagName"/>
        <result column="match_des" property="matchDes"/>
        <result column="user_type" property="userType"/>
        <result column="user_tag_category_id" property="userTagCategoryId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tag_type" property="tagType"/>
        <result column="match_type" property="matchType"/>
        <result column="node_code" property="nodeCode"/>
        <result column="node_group" property="nodeGroup"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="keywords" property="keywords"/>
    </resultMap>


    <select id="findAllCategoriesWithTags" parameterType="java.lang.String"
            resultType="com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags">
        SELECT
        c.category_name AS category,
        c.multi_select AS canMultiSelect,
        GROUP_CONCAT(d.tag_name ORDER BY d.id SEPARATOR ', ') AS tagList
        FROM
        ai_user_tag_category c
        JOIN
        ai_user_tag_dictionary d ON c.id = d.user_tag_category_id
        WHERE
        c.is_deleted = 0
        AND d.is_deleted = 0
        AND c.tenant_id = #{tenantId}
        AND d.tag_type = 'GENERAL'
        GROUP BY
        c.id
        ORDER BY
        c.id
    </select>


</mapper>
