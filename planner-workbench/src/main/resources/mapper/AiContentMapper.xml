<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiContentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="aiContentResultMap" type="com.yirendai.workbench.entity.AiContent">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="text_content" property="textContent"/>
        <result column="url" property="url"/>
        <result column="release_time" property="releaseTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="segment_id" property="segmentId"/>
        <result column="original_content" property="originalContent"/>
    </resultMap>

    <select id="countBySegmentId" resultType="int">
        select count(*)
        from ai_content
        where segment_id = #{segmentId,jdbcType=VARCHAR}
    </select>


</mapper>
