<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiCustomerTagMapper">

    <resultMap id="AiCustomerTagBaseResultMap" type="com.yirendai.workbench.entity.AiCustomerTag">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="userUnionId" column="user_union_id" jdbcType="VARCHAR"/>
        <result property="userPhone" column="user_phone" jdbcType="VARCHAR"/>
        <result property="tagId" column="tag_id" jdbcType="BIGINT"/>
        <result property="tagName" column="tag_name" jdbcType="VARCHAR"/>
        <result property="tagCategoryId" column="tag_category_id" jdbcType="BIGINT"/>
        <result property="tagCategoryName" column="tag_category_name" jdbcType="VARCHAR"/>
        <result property="topTagCategoryId" column="top_tag_category_id" jdbcType="BIGINT"/>
        <result property="topTagCategoryName" column="top_tag_category_name" jdbcType="VARCHAR"/>
        <result property="matchStatus" column="match_status" jdbcType="INTEGER"/>
        <result property="weight" column="weight" jdbcType="INTEGER"/>
        <result property="colorValue" column="color_value" jdbcType="VARCHAR"/>
        <result property="feedbackStatus" column="feedback_status" jdbcType="INTEGER"/>
        <result property="feedbackDetails" column="feedback_details" jdbcType="VARCHAR"/>
        <result property="feedbackUserId" column="feedback_user_id" jdbcType="VARCHAR"/>
        <result property="feedbackUserName" column="feedback_user_name" jdbcType="VARCHAR"/>
        <result property="feedbackTime" column="feedback_time" jdbcType="TIMESTAMP"/>
        <result property="sourceType" column="source_type" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="AiCustomerTagBase_Column_List">
        id,user_id,user_name,user_union_id,user_phone,tag_id,tag_name,tag_category_id,tag_category_name,top_tag_category_id,top_tag_category_name,match_status,weight,feedback_status,feedback_details,feedback_user_id,feedback_user_name,feedback_time,sourceType,tenant_id,create_user,create_time,update_user,update_time,status,is_deleted
    </sql>
</mapper>
