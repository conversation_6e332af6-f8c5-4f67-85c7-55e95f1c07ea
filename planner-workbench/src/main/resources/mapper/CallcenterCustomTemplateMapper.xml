<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.callcenter.CallcenterCustomTemplateMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.callcenter.CallcenterCustomTemplate">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="tenantId" column="tenant_id" />
            <result property="creatorName" column="creator_name" />
            <result property="updateName" column="update_name" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,tenant_id,
        creator_name,update_name,create_time,
        update_time
    </sql>
</mapper>