<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.callcenter.CallcenterRecordChannelCallbackMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.callcenter.CallcenterRecordChannelCallback">
            <id property="id" column="id" />
            <result property="uuid" column="uuid" />
            <result property="tenantId" column="tenant_id" />
            <result property="hasSend" column="has_send" />
            <result property="retryTimes" column="retry_times" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,uuid,tenant_id,has_send,retry_times,create_time,
        update_time
    </sql>
</mapper>
