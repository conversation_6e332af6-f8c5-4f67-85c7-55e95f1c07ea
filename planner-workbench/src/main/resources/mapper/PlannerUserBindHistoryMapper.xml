<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.PlannerUserBindHistoryMapper">
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.PlannerUserBindHistory">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="bind_id" jdbcType="INTEGER" property="bindId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="planner_id" jdbcType="VARCHAR" property="plannerId" />
        <result column="planner_name" jdbcType="VARCHAR" property="plannerName" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="resource" jdbcType="VARCHAR" property="resource" />
        <result column="org_id" jdbcType="INTEGER" property="orgId" />
        <result column="bind_status" jdbcType="INTEGER" property="bindStatus" />
        <result column="remark2" jdbcType="VARCHAR" property="remark2" />
        <result column="group_name" jdbcType="VARCHAR" property="groupName" />
        <result column="allocate_type" jdbcType="INTEGER" property="allocateType" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,bind_id,user_name, planner_id,planner_name, bind_time,end_time,create_time,update_time,remark,resource,org_id,bind_status,remark2,group_name,allocate_type
    </sql>
    <select id="selectCountAll"  resultType="java.lang.Integer">
        select count(1) from planner_user_bind_history where bind_status=1 and resource=2 and remark is NULL
    </select>

</mapper>
