<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterRetrieveRuleMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CallcenterRetrieveRule">
            <id property="id" column="id" />
            <result property="ruleType" column="rule_type" />
            <result property="canRetrieve" column="can_retrieve" />
            <result property="isLimited" column="is_limited" />
            <result property="limitByPerson" column="limit_by_person" />
            <result property="tenantId" column="tenant_id" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,rule_type,can_retrieve,is_limited,limit_by_person,tenant_id,
        create_time,update_time
    </sql>
</mapper>
