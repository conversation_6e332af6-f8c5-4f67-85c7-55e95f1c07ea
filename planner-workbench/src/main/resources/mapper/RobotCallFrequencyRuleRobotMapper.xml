<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRuleRobotMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.robot.modules.call.entity.RobotCallFrequencyRuleRobot">
        <id column="id" property="id"/>
        <result column="rule_id" property="ruleId"/>
        <result column="robot_id" property="robotId"/>
        <result column="robot_name" property="robotName"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <insert id="insertBatch">
        INSERT INTO ai_robot_call_frequency_rule_robot (rule_id, robot_id, robot_name, tenant_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.ruleId}, #{item.robotId}, #{item.robotName}, #{item.tenantId}, #{item.createTime})
        </foreach>
    </insert>

    <delete id="deleteByRuleId">
        DELETE FROM ai_robot_call_frequency_rule_robot WHERE rule_id = #{ruleId}
    </delete>

    <delete id="deleteByRuleIdAndRobotIds">
        DELETE FROM ai_robot_call_frequency_rule_robot
        WHERE rule_id = #{ruleId}
          AND robot_id IN
          <foreach collection="robotIds" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
    </delete>

    <select id="selectRobotCallFrequencyByRuleIds" resultMap="BaseResultMap">
        SELECT id, rule_id, robot_id, robot_name, tenant_id, create_time
        FROM ai_robot_call_frequency_rule_robot
        WHERE rule_id IN
        <foreach collection="ruleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectRobotCallFrequencyByRobotId" resultMap="BaseResultMap">
        SELECT id, rule_id, robot_id, robot_name, tenant_id, create_time
        FROM ai_robot_call_frequency_rule_robot
        WHERE robot_id = #{robotId}
        LIMIT 1
    </select>

    <select id="selectRobotIdsByRuleId" resultType="java.lang.Long">
        SELECT robot_id
        FROM ai_robot_call_frequency_rule_robot
        WHERE rule_id = #{ruleId}
    </select>

    <delete id="deleteByRobotId">
        DELETE FROM ai_robot_call_frequency_rule_robot WHERE robot_id = #{robotId}
    </delete>

    <select id="selectRuleIdsByRobotId" resultType="java.lang.Long">
        SELECT rule_id
        FROM ai_robot_call_frequency_rule_robot
        WHERE robot_id = #{robotId}
    </select>

</mapper>
