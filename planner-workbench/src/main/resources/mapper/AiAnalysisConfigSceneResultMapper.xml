<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiAnalysisConfigSceneResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiAnalysisConfigSceneResult">
        <id column="id" property="id" />
        <result column="scene_id" property="sceneId" />
        <result column="result_name" property="resultName" />
        <result column="prompt" property="prompt" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scene_id, result_name, prompt, tenant_id, create_user, create_time, update_user, update_time, is_deleted
    </sql>
    <select id="selectBySceneId"
            resultType="com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneResultVO">
        select
            id, scene_id as sceneId, result_name as resultName, prompt
        from ai_analysis_config_scene_result
        where scene_id = #{sceneId}
        and is_deleted = 0
        order by id
    </select>
    <select id="selectCountBySceneResultName" resultType="java.lang.Integer">
        select
            count(1)
        from ai_analysis_config_scene_result
        where scene_id = #{sceneId}
        and result_name = #{resultName}
        and is_deleted = 0
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
    </select>

</mapper>
