<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiAnalysisConfigSceneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiAnalysisConfigScene">
        <id column="id" property="id" />
        <result column="scene_name" property="sceneName" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scene_name, tenant_id, create_user, create_time, update_user, update_time, is_deleted
    </sql>
    <select id="listByUserId" resultType="com.yirendai.workbench.entity.AiAnalysisConfigScene">
        select *
        from ai_analysis_config_scene
        where tenant_id = #{tenantId}
          and create_user = #{userId}
          and is_deleted = 0
        order by id  desc
    </select>
    <select id="getByIdAndUser" resultType="com.yirendai.workbench.entity.AiAnalysisConfigScene">
        select *
        from ai_analysis_config_scene
        where id = #{id}
          and create_user = #{userId}
          and is_deleted = 0
    </select>
    <select id="selectCountBySceneName" resultType="java.lang.Integer">
        select count(1)
        from ai_analysis_config_scene
        where scene_name = #{sceneName}
          and tenant_id = #{tenantId}
          and create_user = #{userId}
          and is_deleted = 0
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
    </select>

</mapper>
