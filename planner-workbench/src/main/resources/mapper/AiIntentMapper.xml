<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiIntentMapper">
    <resultMap id="AiChatPO" type="com.yirendai.voiceaiserver.model.po.AiChatPO"/>

    <select id="getHandleList" resultMap="AiChatPO">
        SELECT id,
               origin_content,
               processed_content,
               origin_type
        FROM ai_planner_chat_contact
        where origin_content != ''
          AND (direction = 1 OR origin_type = 'meeting_voice_call' or origin_type = 'avaya')
          AND (msg_tag = '' OR msg_tag is null)
        LIMIT #{limit}
    </select>

    <select id="getHandleCount" resultType="long">
        SELECT count(1)
        FROM ai_planner_chat_contact
        where origin_content != ''
          AND (direction = 1 OR origin_type = 'meeting_voice_call' or origin_type = 'avaya')
          AND (msg_tag = '' OR msg_tag is null)
    </select>

</mapper>
