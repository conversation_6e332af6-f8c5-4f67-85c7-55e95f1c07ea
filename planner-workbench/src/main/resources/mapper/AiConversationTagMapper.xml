<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationTagMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiConversationTag">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="chatContactId" column="chat_contact_id" jdbcType="BIGINT"/>
            <result property="plannerNo" column="planner_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="tagId" column="tag_id" jdbcType="BIGINT"/>
            <result property="processId" column="process_id" jdbcType="BIGINT"/>
            <result property="tagCategoryId" column="tag_category_id" jdbcType="BIGINT"/>
            <result property="tagName" column="tag_name" jdbcType="VARCHAR"/>
            <result property="tagCategoryName" column="tag_category_name" jdbcType="VARCHAR"/>
            <result property="matchStatus" column="match_status" jdbcType="TINYINT"/>
            <result property="confidenceScore" column="confidence_score" jdbcType="DECIMAL"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,chat_contact_id,planner_no,user_id,
        tag_id,process_id,tag_category_id,
        tag_name,tag_category_name,match_status,
        confidence_score,tenant_id,create_user,
        create_time,update_user,update_time,
        status,is_deleted
    </sql>

    <select id="selectChatContacts" parameterType="map" resultType="com.yirendai.voiceaiserver.vo.response.AiConversationTagExportDto">
        SELECT
        apcc.id AS 'id',
        apcc.user_id AS 'userId',
        apcc.planner_no AS 'plannerNo',
        apcc.processed_content AS 'aiContent',
        GROUP_CONCAT(CONCAT(act.tag_category_name, ':', act.tag_name) SEPARATOR ', ') AS 'tagAndCategory',
        MIN(act.create_time) AS 'createTime',
        MAX(act.update_time) AS 'updateTime',
        apcc.origin_content AS 'audioPath'
        FROM ai_planner_chat_contact apcc
        INNER JOIN ai_conversation_tag act ON act.chat_contact_id = apcc.id
        WHERE apcc.origin_type = 'avaya' AND processed_content IS NOT NULL
        <if test="matchStatus != null">
            AND act.match_status = #{matchStatus}
        </if>
        GROUP BY apcc.id, apcc.processed_content;
    </select>

</mapper>
