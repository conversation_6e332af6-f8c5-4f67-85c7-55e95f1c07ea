<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationInspectDetailMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiConversationInspectDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="chatContactId" column="chat_contact_id" jdbcType="BIGINT"/>
            <result property="plannerNo" column="planner_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="inspectResultId" column="inspect_result_id" jdbcType="BIGINT"/>
            <result property="inspectType" column="inspect_type" jdbcType="TINYINT"/>
            <result property="sensitiveWord" column="sensitive_word" jdbcType="VARCHAR"/>
            <result property="startIndex" column="start_index" jdbcType="INTEGER"/>
            <result property="inspectIntentId" column="inspect_intent_id" jdbcType="INTEGER"/>
            <result property="inspectIntentText" column="inspect_intent_text" jdbcType="VARCHAR"/>
            <result property="confidenceScore" column="confidence_score" jdbcType="DECIMAL"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="createDept" column="create_dept" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,chat_contact_id,planner_no,
        user_id,inspect_result_id,inspect_type,
        sensitive_word,start_index,inspect_intent_id,
        inspect_intent_text,confidence_score,tenant_id,
        create_user,create_dept,create_time,
        update_user,update_time,status,
        is_deleted
    </sql>
</mapper>
