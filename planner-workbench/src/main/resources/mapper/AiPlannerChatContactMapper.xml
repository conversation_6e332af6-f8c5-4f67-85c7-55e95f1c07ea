<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiPlannerChatContactMapper">

    <select id="getPerMaxBusId" resultType="com.yirendai.workbench.entity.AiPlannerChatContact">
        select user_id, max(CONVERT(bus_id, UNSIGNED)) as bus_id
        from ai_planner_chat_contact
        where planner_no = #{plannerNo} and origin_type != 'avaya' and origin_type != 'call_center' and origin_type != 'houyu'
        group by user_id
    </select>

    <select id="getMaxMsgTime" resultType="java.time.LocalDateTime">
        select max(msg_time)
        from ai_planner_chat_contact
        where
            origin_type = 'avaya'
          and planner_no = #{plannerNo}
          and user_id = #{userId}
    </select>

    <select id="getGroups" resultType="com.yirendai.voiceaiserver.vo.db.AvayaGroupVO">
        select min(id) - 1 as minId, max(id) as maxId, count(1) as groupSize
        from ai_planner_chat_contact
        where origin_type = 'avaya' and (processing_content is null or processing_content = '')
        group by ceil(id/1000) order by groupSize desc
    </select>

    <select id="getGroupsModel" resultType="com.yirendai.voiceaiserver.vo.db.AvayaGroupVO">
        select min(id) - 1 as minId, max(id) as maxId, count(1) as groupSize
        from ai_planner_chat_contact
        where origin_type = 'avaya'
          and (processed_content is null or LENGTH(TRIM(processed_content)) = 0)
          and nas_path is not null and LENGTH(TRIM(nas_path)) != 0
        group by ceil(id/10000) order by groupSize desc, minId desc
    </select>

    <select id="getAllMaxMsgTime" resultType="java.time.LocalDateTime">
        select max(msg_time) from ai_planner_chat_contact where origin_type = 'avaya' and tenant_id = #{tenantId}
    </select>

    <select id="getWeChatTimeWithMonth" resultType="java.time.LocalDate">
        select date(msg_time) as `day`
        from ai_planner_chat_contact
        where
            msg_time &gt;= #{startTime} and msg_time &lt; #{endTime}
            and planner_no = #{plannerNo} and user_id = #{userId} and tenant_id = #{tenantId}
            and origin_type != 'avaya' and origin_type != 'call_center' and origin_type != 'houyu'
        group by date(msg_time) order by `day` asc
    </select>

    <select id="getIdRangeWithinTimePeriod" resultType="java.lang.Long">
        SELECT id
        FROM
            (SELECT id, (@row_number := @row_number + 1) AS row_num
            FROM ai_planner_chat_contact, (SELECT @row_number := 0) AS init
            <where>
                <if test="endId != null">
                    id &lt; #{endId}
                </if>
                and msg_time &gt;= #{startTime} and msg_time &lt; #{endTime}
                and origin_type = 'avaya'
                and nas_path is not null and TRIM(nas_path) != ''
                and (processed_content is null or TRIM(processed_content) = '')
            </where>
            ORDER BY id desc limit #{size}) AS numbered_rows
        WHERE row_num % 100 = 1 order by row_num asc;
    </select>
    <select id="getChatContactIdByUuid" resultType="java.lang.Long">
        select id from ai_planner_chat_contact where bus_id = #{uuid}
    </select>
    <select id="getListByIdList" resultType="com.yirendai.voiceaiserver.vo.response.AiHelperCallRecordVO">
        select
            record.call_planner_id as plannerNo,
            record.agent_name as agentName,
            record.uuid as uuid,
            record.call_type as callType,
            record.agent_dept as agentDept,
            record.start_stamp as startStamp,
            record.end_stamp as endStamp,
            record.customer_name as customerName,
            record.customer_uid as customerUid,
            contact.processed_content as processedContent
        from callcenter_call_record record
        left join ai_planner_chat_contact contact on record.uuid = contact.bus_id
        where record.id in
        <foreach item="item" collection="callRecordIds" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>

    <select id="getLatestMsgCountByPlannerNo" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT user_id)
        FROM ai_planner_chat_contact
        WHERE planner_no = #{plannerNo}
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="nonQwMsgTypeList != null and nonQwMsgTypeList.size() > 0">
                AND origin_type NOT IN
                <foreach collection="nonQwMsgTypeList" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
    </select>

    <select id="getLatestMsgByPlannerNoWithPage" resultType="com.yirendai.workbench.entity.AiPlannerChatContact">
        SELECT a.*
        FROM ai_planner_chat_contact a
        RIGHT JOIN (
            SELECT user_id, MAX(msg_time) AS max_msg_time
            FROM ai_planner_chat_contact
            WHERE planner_no = #{plannerNo}
                <if test="userId != null and userId != ''">
                    AND user_id = #{userId}
                </if>
                <if test="nonQwMsgTypeList != null and nonQwMsgTypeList.size() > 0">
                    AND origin_type NOT IN
                    <foreach collection="nonQwMsgTypeList" item="type" open="(" separator="," close=")">
                        #{type}
                    </foreach>
                </if>
            GROUP BY user_id
            ORDER BY max_msg_time DESC LIMIT #{offset}, #{limit}
            ) b ON a.user_id = b.user_id AND a.msg_time = b.max_msg_time
        WHERE a.planner_no = #{plannerNo}
            <if test="userId != null and userId != ''">
                AND a.user_id = #{userId}
            </if>
            <if test="nonQwMsgTypeList != null and nonQwMsgTypeList.size() > 0">
                AND origin_type NOT IN
                <foreach collection="nonQwMsgTypeList" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
        ORDER BY a.msg_time DESC
    </select>
</mapper>
