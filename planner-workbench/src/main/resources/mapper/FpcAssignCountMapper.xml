<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.FpcAssignCountMapper">
  <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.FpcAssignCount">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="member_no" jdbcType="INTEGER" property="memberNo" />
    <result column="assign_count" jdbcType="INTEGER" property="assignCount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="city" jdbcType="INTEGER" property="city" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="init_count" jdbcType="INTEGER" property="initCount" />
    <result column="system_count" jdbcType="INTEGER" property="systemCount" />
    <result column="artificial_count" jdbcType="INTEGER" property="artificialCount" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="init_method" jdbcType="INTEGER" property="initMethod" />
  </resultMap>
  <sql id="Base_Column_List">
    id, member_no, assign_count,status, city, create_time, update_time,init_count,system_count,artificial_count,start_time,init_method
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fpc_assign_count
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectFpcAssignCountList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fpc_assign_count
    where status=1
  </select>

  <select id="selectAllList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fpc_assign_count
  </select>

  <delete id="delById" parameterType="java.lang.Integer">
    DELETE FROM fpc_assign_count WHERE id = #{id,jdbcType=INTEGER}
  </delete>

  <select id="selectListByMemberNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fpc_assign_count
    where status=1 AND member_no = #{memberNo,jdbcType=INTEGER}
  </select>

  <update id="fpcAssignCountUpate" parameterType="com.yirendai.workbench.entity.FpcAssignCount">
    update fpc_assign_count SET
    assign_count = #{assignCount,jdbcType=INTEGER},
    init_count = #{initCount,jdbcType=INTEGER},
    system_count = #{systemCount,jdbcType=INTEGER},
    artificial_count = #{artificialCount,jdbcType=INTEGER},
    init_method = #{initMethod,jdbcType=INTEGER},
    start_time = #{startTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="delFpcAssignCount" parameterType="com.yirendai.workbench.entity.FpcAssignCount">
    update fpc_assign_count SET status = 0
    where member_no = #{memberNo,jdbcType=INTEGER}
  </update>

  <select id="getMaxCount" resultType="java.lang.Integer">
    select MAX(assign_count)
    from fpc_assign_count
    where status=1
  </select>

  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.yirendai.workbench.entity.FpcAssignCount">
    insert into fpc_assign_count
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="memberNo != null">
        member_no,
      </if>
      <if test="assignCount != null">
        assign_count,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="initCount != null">
        init_count,
      </if>
      <if test="systemCount != null">
        system_count,
      </if>
      <if test="artificialCount != null">
        artificial_count,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="initMethod != null">
        init_method,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="city != null">
        #{city,jdbcType=INTEGER},
      </if>
      <if test="memberNo != null">
        #{memberNo,jdbcType=INTEGER},
      </if>
      <if test="assignCount != null">
        #{assignCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="initCount != null">
        #{initCount,jdbcType=INTEGER},
      </if>
      <if test="systemCount != null">
        #{systemCount,jdbcType=INTEGER},
      </if>
      <if test="artificialCount != null">
        #{artificialCount,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="initMethod != null">
        #{initMethod,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

</mapper>