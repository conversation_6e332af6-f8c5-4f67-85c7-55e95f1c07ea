<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AdsCsgDOrderDetailDfMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AdsCsgDOrderDetailDf">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="etlTime" column="etl_time" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="policyCode" column="policy_code" jdbcType="VARCHAR"/>
            <result property="policyStatusDesc" column="policy_status_desc" jdbcType="VARCHAR"/>
            <result property="standardPremiumUsd" column="standard_premium_usd" jdbcType="DECIMAL"/>
            <result property="dAmtUsd" column="d_amt_usd" jdbcType="DECIMAL"/>
            <result property="paymentYears" column="payment_years" jdbcType="VARCHAR"/>
            <result property="productType2Desc" column="product_type2_desc" jdbcType="VARCHAR"/>
            <result property="holdDate" column="hold_date" jdbcType="VARCHAR"/>
            <result property="initialPaymentDate" column="initial_payment_date" jdbcType="VARCHAR"/>
            <result property="underwritingPassDate" column="underwriting_pass_date" jdbcType="VARCHAR"/>
            <result property="saleName" column="sale_name" jdbcType="VARCHAR"/>
            <result property="staffId" column="staff_id" jdbcType="VARCHAR"/>
            <result property="staffName" column="staff_name" jdbcType="VARCHAR"/>
            <result property="staffGroup" column="staff_group" jdbcType="VARCHAR"/>
            <result property="staffDept" column="staff_dept" jdbcType="VARCHAR"/>
            <result property="staffDeptLeader" column="staff_dept_leader" jdbcType="VARCHAR"/>
            <result property="insuranceCompanyName" column="insurance_company_name" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="holderName" column="holder_name" jdbcType="VARCHAR"/>
            <result property="insuredName" column="insured_name" jdbcType="VARCHAR"/>
            <result property="coolingPeriodEnd" column="cooling_period_end" jdbcType="VARCHAR"/>
            <result property="policySendFlag" column="policy_send_flag" jdbcType="VARCHAR"/>
            <result property="dataDate" column="data_date" jdbcType="VARCHAR"/>
            <result property="commissionInsertTime" column="commission_insert_time" jdbcType="TIMESTAMP"/>
            <result property="csMonthId" column="cs_month_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,etl_time,user_id,
        policy_code,policy_status_desc,standard_premium_usd,
        d_amt_usd,payment_years,product_type2_desc,
        hold_date,initial_payment_date,underwriting_pass_date,
        sale_name,staff_id,staff_name,
        staff_group,staff_dept,staff_dept_leader,
        insurance_company_name,product_name,holder_name,
        insured_name,cooling_period_end,policy_send_flag,
        data_date,commission_insert_time,cs_month_id
    </sql>

    <select id="getPlannerAchievements" resultType="com.yirendai.workbench.entity.AdsCsgDOrderDetailDf">
        select staff_id, max(staff_name) as staff_name, sum(d_amt_usd) as d_amt_usd
        from ads_csg_d_order_detail_df
        where
            d_amt_usd is not null and underwriting_pass_date is not null
          and staff_id is not null and staff_id != ''
          and staff_name is not null and staff_name != ''
        group by staff_id
    </select>

    <select id="getPlannerUsers" resultType="com.yirendai.workbench.entity.AdsCsgDOrderDetailDf">
        select staff_id, user_id, min(STR_TO_DATE(underwriting_pass_date, '%Y-%m-%d %H:%i:%s')) as underwriting_pass_date
        from ads_csg_d_order_detail_df
        where
            underwriting_pass_date is not null
          and user_id is not null and user_id != ''
          and staff_id is not null and staff_id != ''
        group by staff_id, user_id
    </select>

    <select id="selectInsuranceTotalAmount" resultType="com.yirendai.workbench.entity.InsuranceTypeAmount">
        select user_id, sum(standard_premium_usd) as amount, 'D' as type
        from ads_csg_d_order_detail_df
        where user_id = #{userId}  and hold_date >= #{payDateStart} and hold_date &lt;= #{payDateEnd}
        union all
        select user_id, sum(long_ins_first_amt) as amount, 'B' as type
        from ads_qw_long_ins_order_detail_df where user_id=#{userId} and pay_date>= #{payDateStart} and pay_date &lt;= #{payDateEnd}
        union all
        select user_id, sum(shounian_amount) as amount, 'B' as type
        from ads_changxian_order_detail_df  where user_id=#{userId} and pay_date>= #{payDateStart} and pay_date &lt;= #{payDateEnd}
    </select>
</mapper>
