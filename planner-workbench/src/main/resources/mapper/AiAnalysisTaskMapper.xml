<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiAnalysisTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.AiAnalysisTask">
        <id column="id" property="id" />
        <result column="task_name" property="taskName" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
        <result column="analysis_type" property="analysisType" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="analysis_content" property="analysisContent" />
        <result column="source_page" property="sourcePage" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_name, tenant_id, create_user, create_user_name, create_time, update_user, update_time, status, is_deleted, analysis_type, start_time, end_time, analysis_content
    </sql>
    <select id="selectByIdAndUserId" resultType="com.yirendai.workbench.entity.AiAnalysisTask">
        select
        *
        from ai_analysis_task
        where id = #{req.id}
        and tenant_id = #{req.tenantId}
        and create_user = #{req.createUser}
        and is_deleted = 0
    </select>
    <select id="aiAnalysisTaskPageList" resultType="com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskVO">
        select
        id, task_name, tenant_id, create_user, create_user_name, create_time, update_user, update_time, status, is_deleted, analysis_type, start_time, end_time, analysis_content, source_page
        from ai_analysis_task
        where tenant_id = #{req.tenantId}
        and create_user = #{req.createUser}
        and is_deleted = 0
        <if test="req.taskName != null and req.taskName != ''">
            and task_name like concat('%', #{req.taskName},'%')
        </if>
        <if test="req.status != null and req.status.size() >0">
          <foreach item="status" index="index" collection="req.status" separator="," open="and status in (" close=")">
              #{status}
          </foreach>
        </if>
        <if test="req.createTimeBeg != null">
            and create_time >= #{req.createTimeBeg}
        </if>
        <if test="req.createTimeEnd != null">
            <![CDATA[ and create_time <= #{req.createTimeEnd} ]]>
        </if>
        order by id desc
    </select>
    <select id="selectTenantId" resultType="java.lang.String">
        select
        tenant_id
        from ai_analysis_task
        where is_deleted = 0
        and status in (0,1)
        group by tenant_id

    </select>
    <select id="selectByTenantIdGroupByUser" resultType="com.yirendai.workbench.entity.AiAnalysisTask">
        select
        *
        from ai_analysis_task a
        where a.tenant_id = #{tenantId}
        and a.is_deleted = 0
        and a.analysis_type = 0
        and a.record_type = 0
        and a.status in (0,1)
        and a.id in (
            select min(b.id)
            from ai_analysis_task b
            where b.tenant_id = a.tenant_id
            and b.is_deleted = 0
            and b.status in (0,1)
            group by b.create_user
        )
    </select>


</mapper>
