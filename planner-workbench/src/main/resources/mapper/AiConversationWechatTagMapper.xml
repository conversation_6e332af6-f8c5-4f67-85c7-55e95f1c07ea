<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.AiConversationWechatTagMapper">

    <resultMap id="AiConversationWechatTagBaseResultMap" type="com.yirendai.workbench.entity.AiConversationWechatTag">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="wechatSummaryId" column="wechat_summary_id" jdbcType="BIGINT"/>
            <result property="plannerNo" column="planner_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="intervalTag" column="interval_tag" jdbcType="VARCHAR"/>
            <result property="tagId" column="tag_id" jdbcType="BIGINT"/>
            <result property="tagCategoryId" column="tag_category_id" jdbcType="BIGINT"/>
            <result property="tagName" column="tag_name" jdbcType="VARCHAR"/>
            <result property="tagCategoryName" column="tag_category_name" jdbcType="VARCHAR"/>
            <result property="matchStatus" column="match_status" jdbcType="TINYINT"/>
            <result property="confidenceScore" column="confidence_score" jdbcType="DECIMAL"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wechat_summary_id,planner_no,
        user_id,interval_tag,tag_id,
        tag_category_id,tag_name,tag_category_name,
        match_status,confidence_score,tenant_id,
        create_time,update_time,status,
        is_deleted
    </sql>
</mapper>
