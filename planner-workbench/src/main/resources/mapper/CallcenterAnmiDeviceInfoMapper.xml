<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.mapper.CallcenterAnmiDeviceInfoMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CallcenterAnmiDeviceInfo">
            <id property="id" column="id" />
            <result property="deviceId" column="device_id" />
            <result property="imei" column="imei" />
            <result property="orgName" column="org_name" />
            <result property="userName" column="user_name" />
            <result property="status" column="status" />
            <result property="sim1Action" column="sim1_action" />
            <result property="sim1Number" column="sim1_number" />
            <result property="sim2Action" column="sim2_action" />
            <result property="sim2Number" column="sim2_number" />
            <result property="userNo" column="user_no" />
            <result property="orgId" column="org_id" />
            <result property="version" column="version" />
            <result property="updatedTime" column="updated_time" />
            <result property="tenantId" column="tenant_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,device_id,imei,org_name,user_name,status,
        sim1_action,sim1_number,sim2_action,sim2_number,user_no,version,
        org_id,updated_time,tenant_id
    </sql>

    <insert id="batchInsertOrUpdateDeviceInfo" parameterType="java.util.List">
        INSERT INTO callcenter_anmi_device_info (
        id, device_id, imei, org_name, user_name, status,
        sim1_action, sim1_number, sim2_action, sim2_number, user_no,version,
        org_id, updated_time, tenant_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.deviceId}, #{item.imei}, #{item.orgName}, #{item.userName}, #{item.status},
            #{item.sim1Action}, #{item.sim1Number}, #{item.sim2Action}, #{item.sim2Number}, #{item.userNo},
            #{item.version}, #{item.orgId}, #{item.updatedTime}, #{item.tenantId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        device_id = VALUES(device_id),
        org_name = VALUES(org_name),
        user_name = VALUES(user_name),
        status = VALUES(status),
        sim1_action = VALUES(sim1_action),
        sim1_number = VALUES(sim1_number),
        sim2_action = VALUES(sim2_action),
        sim2_number = VALUES(sim2_number),
        version = VALUES(version),
        user_no = VALUES(user_no),
        org_id = VALUES(org_id),
        updated_time = VALUES(updated_time)
    </insert>

    <select id="getDeviceInfoByImei" resultMap="BaseResultMap">
        SELECT *
        FROM callcenter_anmi_device_info
        WHERE imei = #{imei} order by id desc limit 1
    </select>

    <select id="getDeviceInfoByUserNo" resultMap="BaseResultMap">
        SELECT *
        FROM callcenter_anmi_device_info
        WHERE user_no = #{userNo} order by id desc limit 1
    </select>
</mapper>
