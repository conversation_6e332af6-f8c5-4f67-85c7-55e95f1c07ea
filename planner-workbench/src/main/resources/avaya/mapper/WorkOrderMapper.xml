<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.avaya.mapper.WorkOrderMapper">

    <select id="getPage" resultType="com.yirendai.workbench.vo.res.WorkOrderListRes">
        SELECT
        work_order_id,
        user_tel,
        call_dir,
        work_order_type,
        work_order_sta,
        work_order_sec,
        priority,
        close_time,
        creator,
        work_order_state,
        cbg.groups_type AS groupType,
        cbg.groups_name AS groupName,
        cbs.STAFF_NAME AS staffName,
        create_time AS createTime,
        submit_time AS submitTime,
        ( SELECT
        seq.CUS_NAME
        FROM
        cx_bas_voice_seq seq
        WHERE
        seq.VOICE_ID=order_voiceid) AS custName
        FROM
        work_order queryby
        LEFT JOIN
        ( SELECT
        cbs1.STAFF_ID,
        cbs1.STAFF_NAME
        FROM
        cx_bas_staff cbs1
        WHERE
        cbs1.DELFLAG='n' )cbs
        ON
        creator=cbs.STAFF_ID
        LEFT JOIN
        ( SELECT
        cbgs1.STAFF_ID,
        cbgs1.GROUPS_ID
        FROM
        cx_bas_groups_staff cbgs1
        WHERE
        cbgs1.DELFLAG='n') cbgs
        ON
        cbgs.STAFF_ID=creator
        LEFT JOIN
        ( SELECT
        cbg1.groups_id,
        cbg1.groups_name,
        cbg1.groups_type
        FROM
        cx_bas_groups cbg1
        WHERE
        cbg1.DELFLAG='n') cbg
        ON
        cbgs.GROUPS_ID=cbg.groups_id
        WHERE
        1=1
        <if test="identityCard != null and identityCard !=''">
            AND identity_card=#{identityCard}
        </if>
        <if test="userTel != null and userTel !=''">
            AND user_tel=#{userTel}
        </if>
    </select>
</mapper>
