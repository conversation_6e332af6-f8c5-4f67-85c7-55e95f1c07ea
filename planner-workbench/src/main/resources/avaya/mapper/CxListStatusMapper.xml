<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.avaya.mapper.CxListStatusMapper">
    <resultMap id="BaseResultMap" type="com.yirendai.workbench.entity.CxListStatus">
        <result column="status_id" jdbcType="VARCHAR" property="statusId" />
        <result column="strategy_id" jdbcType="VARCHAR" property="strategyId" />
        <result column="getUser" jdbcType="VARCHAR" property="getUser" />
        <result column="getTime" jdbcType="TIMESTAMP" property="getTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="key_point_cd" jdbcType="VARCHAR" property="keyPointCd" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
    </resultMap>

    <select id="selectCountAll" resultType="java.lang.Long">
    SELECT count(*)
    FROM cx_list_status
    WHERE LIST_STATUS !="回退"
    AND GETUSER !=""
    AND END_TIME>now()
    AND end_time!='9999-12-31'
    AND end_time!='9999-12-01'
    </select>

    <select id="selectMaxId" resultType="java.lang.Long">
     SELECT max(status_id)
     FROM cx_list_status
     WHERE LIST_STATUS !="回退"
     AND GETUSER !=""
     AND END_TIME>now()
     AND end_time!='9999-12-31'
     AND end_time!='9999-12-01'
    </select>

    <select id="selectListByPage" resultMap="BaseResultMap">
        SELECT status_id,strategy_id,getUser,getTime,end_time,key_point_cd,user_name
        FROM cx_list_status
        WHERE LIST_STATUS !="回退"
        AND GETUSER !=""
        AND END_TIME>now()
        AND end_time!='9999-12-31'
        AND end_time!='9999-12-01'
        ORDER by status_id ASC
        limit #{page},#{size}
    </select>



</mapper>
