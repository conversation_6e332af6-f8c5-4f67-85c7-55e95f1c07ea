<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="LOG_PATH" source="logging.file.path"/>
    <springProperty scope="context" name="appName" source="spring.application.name"/>

    <!-- 引用的第三方类库或框架一律设置为INFO级别 减少日志输出 -->
    <logger name="org.springframework.beans.factory" level="INFO"/>
    <!-- 只禁用特定的 springfox 组件 -->
    <logger name="springfox.documentation.spring.web" level="WARN"/>
    <logger name="springfox.documentation.spring.web.plugins" level="WARN"/>
    <logger name="springfox.documentation.swagger" level="WARN"/>

    <!-- 禁用 MybatisConfiguration 的日志 -->
    <logger name="com.baomidou.mybatisplus.core.MybatisConfiguration" level="WARN"/>
    <!-- 保留 SQL 相关的日志 -->
    <logger name="com.baomidou.mybatisplus.core.executor" level="DEBUG"/>
    <logger name="com.baomidou.mybatisplus.extension.plugins" level="DEBUG"/>

    <logger name="org.springframework.core.env" level="INFO"/>
    <logger name="com.yirendai.workbench.mapper" level="DEBUG"/>
    <logger name="com.yirendai.workbench.blade.mapper" level="DEBUG"/>
    <logger name="com.yirendai.workbench.hycall.mapper" level="DEBUG"/>
    <logger name="com.yirendai.workbench.readonly.mapper" level="DEBUG"/>
    <logger name="com.yirendai.workbench" level="INFO"/>
    <logger name="org.springframework.data.mongodb.core.MongoTemplate" level="DEBUG"/>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <!--
        日志输出格式：
            %d表示日期时间，
            %thread表示线程名，
            %-5level：级别从左显示5个字符宽度
            %logger{50} 表示logger名字最长50个字符，否则按照句点分割。
            %msg：日志消息，
            %n是换行符
        -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}|[${appName},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}]|%thread|%-5level|%class{200}:(%line\)|%msg%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/archive/${appName}.%d{yyyy-MM-dd}.log.gz
            </fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}|[${appName},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}]|%thread|%-5level|%class{200}:(%line\)|%msg%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="asyncFile" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <appender-ref ref="file"/>
    </appender>

    <root level="info">
        <appender-ref ref="stdout"/>
        <appender-ref ref="asyncFile"/>
    </root>
</configuration>
