#服务器端口
server:
  port: 8200

#数据源配置
spring:
  datasource:
    crm:
      jdbc-url: ${blade.datasource.planner-workbench.url}
      username: ${blade.datasource.planner-workbench.username}
      password: ${blade.datasource.planner-workbench.password}
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: select 1
    avaya:
      jdbc-url: ${blade.datasource.avaya.url}
      username: ${blade.datasource.avaya.username}
      password: ${blade.datasource.avaya.password}
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: select 1
    owmuc:
      jdbc-url: ${blade.datasource.owm_uc.url}
      username: ${blade.datasource.owm_uc.username}
      password: ${blade.datasource.owm_uc.password}
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: select 1
    linkwechat:
      jdbc-url: ${blade.datasource.linkwechat.url}
      username: ${blade.datasource.linkwechat.username}
      password: ${blade.datasource.linkwechat.password}
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: select 1
    blade:
      jdbc-url: ${blade.datasource.blade.url}
      username: ${blade.datasource.blade.username}
      password: ${blade.datasource.blade.password}
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: select 1
    hycall:
      jdbc-url: ${blade.datasource.hycall.url}
      username: ${blade.datasource.hycall.username}
      password: ${blade.datasource.hycall.password}
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: oracle.jdbc.OracleDriver
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: select 1 from dual
    crm-readonly:
      jdbc-url: ${blade.datasource.crm-readonly.url}
      username: ${blade.datasource.crm-readonly.username}
      password: ${blade.datasource.crm-readonly.password}
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      minimum-idle: 3
      maximum-pool-size: 10
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: select 1
      read-only: true
  data:
    mongodb:
      uri: mongodb://yrcc_saas:LCyhxK0o38FVlCjmHR&<EMAIL>:27017/yrcc_saas
      # 连接池关键参数
      auto-index-creation: false
      # 连接池配置
      client:
        min-pool-size: 10
        max-pool-size: 100
        max-idle-time: 30000  # 毫秒
        max-life-time: 3600000 # 毫秒
        wait-queue-timeout: 5000 # 毫秒
        wait-queue-size: 100
        connect-timeout: 5000 # 毫秒
        socket-timeout: 30000 # 毫秒
        server-selection-timeout: 30000 # 毫秒
application:
  nasDir: "/data/yx_nas/appsource"
  appSourceUrl: "http://appsource.155.test.yirendai.com/"
rocketmq:
  name-server: *************:9876
  producer:
    group: planner_workbench_ai_test
  consumer:
    group: planner_workbench_ai_test

http-config:
  proxyHost: squid.caiwu.corp
  proxyPort: 3128

  # 开启增强功能
  knife4j:
    enable: true
    setting:
      enable-swagger-models: true
