<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.readonly.mapper.CallRecordReadOnlyMapper">

    <select id="getCallOfSpecificTimes" resultType="com.yirendai.workbench.readonly.entity.SaleProcessCallRecordInfo">

        SELECT  uuid, start_stamp   FROM callcenter_call_record
        WHERE  customer_uid = #{customerId}
        <if test="fromTime != null">
            AND start_stamp &gt;= #{fromTime}
        </if>
        AND start_stamp &lt;= #{toTime}
        AND call_type in (2, 4)
        AND tenant_id = #{tenantId}
        ORDER BY start_stamp ASC
        LIMIT 1 OFFSET #{specificTimes}

    </select>

    <select id="getFirstAnsweredCall" resultType="com.yirendai.workbench.readonly.entity.SaleProcessCallRecordInfo">

        SELECT  uuid, start_stamp   FROM callcenter_call_record
        WHERE  customer_uid = #{customerId}
        <if test="fromTime != null">
            AND start_stamp &gt;= #{fromTime}
        </if>
        AND start_stamp &lt;= #{toTime}
        AND tenant_id = #{tenantId}
        AND call_type in (2, 4)
        AND cs_answered = 2
        ORDER BY start_stamp ASC
        LIMIT 1

    </select>

    <select id="getFirstOverBillSecCall"  resultType="com.yirendai.workbench.readonly.entity.SaleProcessCallRecordInfo">
        SELECT  uuid,  start_stamp
        FROM (
            SELECT  uuid, start_stamp, @running_total := @running_total + cs_billsec AS total_billsec
            FROM
            callcenter_call_record,
            (SELECT @running_total := 0) AS init
            WHERE
                customer_uid = #{customerId}
                <if test="fromTime != null">
                    AND start_stamp &gt;= #{fromTime}
                </if>
                AND start_stamp &lt;= #{toTime}
                AND tenant_id = #{tenantId}
                AND call_type in (2, 4)
                AND cs_answered = 2
            ORDER BY      start_stamp
        ) AS call_records
        WHERE total_billsec >= #{totalBillsec}
        LIMIT 1
    </select>

    <select id="getFPCRelation" resultType="com.yirendai.workbench.readonly.entity.SaleProcessFpcInfo">
        select assign_time, staff_id, staff_name from fpc_customer_relation where user_id=#{customerId} and assign_time &gt;= #{sinceTime}
    </select>
</mapper>
