<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessRecordReadonlyMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.vo.res.CallcenterSaleProcessRecordRes">
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="planner_id" jdbcType="VARCHAR" property="plannerId" />
        <result column="planner_name" jdbcType="VARCHAR" property="plannerName" />
        <result column="group_name" jdbcType="VARCHAR" property="groupName" />
        <result column="sale_process_id" jdbcType="INTEGER" property="saleProcessId" />
        <result column="progress_value" jdbcType="DECIMAL" property="progressValue" />
        <result column="current_node_id" jdbcType="INTEGER" property="currentNodeId" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    </resultMap>

    <resultMap id="BaseResultCustomerMap" type="com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerSaleProcessRecordResp">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result column="sale_process_id" jdbcType="INTEGER" property="saleProcessId" />
        <result column="product_type" jdbcType="INTEGER" property="productType" />
        <result column="process_no" jdbcType="VARCHAR" property="processNo" />
        <result column="process_name" jdbcType="VARCHAR" property="processName" />
        <result column="current_node_id" jdbcType="INTEGER" property="currentNodeId" />
        <result column="progress_value" jdbcType="DECIMAL" property="progressValue" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
    </resultMap>


    <select id="countSaleProcessRecordListPage" resultType="int">
        select count(*) from callcenter_sale_process_record cspr JOIN planner_user_bind pub
        ON  cspr.user_id = pub.user_id
        where cspr.process_no = #{processNo}
        <if test="plannerIds != null and  plannerIds.size>0 ">
            and pub.planner_id in
            <foreach collection="plannerIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="getSaleProcessRecordListPage" resultMap="BaseResultMap">
        select cspr.user_id, planner_id,planner_name,group_name,sale_process_id,progress_value,current_node_id,cspr.start_time,cspr.end_time
        from callcenter_sale_process_record cspr JOIN planner_user_bind pub
        ON  cspr.user_id = pub.user_id
        where cspr.process_no = #{processNo}
        <if test="plannerIds != null and  plannerIds.size>0 ">
            and pub.planner_id in
            <foreach collection="plannerIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY cspr.create_time DESC
        <if test="pageStart != null and  pageSize!=null ">
            LIMIT #{pageStart},#{pageSize}
        </if>

    </select>

    <select id="countCustomerSaleProcessRecordListPage" resultType="int">
        select count(*) from callcenter_sale_process_record cspr JOIN callcenter_sales_process csp
        ON  cspr.sale_process_id = csp.id
        where cspr.user_id = #{userId} and csp.process_status=2
        <if test="status != null ">
            and cspr.status = #{status,jdbcType=INTEGER}
        </if>
        <if test="processName != null ">
            and csp.process_name LIKE concat('%',#{processName,jdbcType=VARCHAR},'%')
        </if>
        <if test="productType != null ">
            and csp.product_type = #{productType,jdbcType=INTEGER}
        </if>
    </select>


    <select id="getCustomerSaleProcessRecordListPage" resultMap="BaseResultCustomerMap">
        select cspr.id,cspr.sale_process_id,cspr.current_node_id,cspr.process_no,csp.process_name,csp.product_type,cspr.progress_value,cspr.start_time,cspr.update_time,cspr.status
        from callcenter_sale_process_record cspr JOIN callcenter_sales_process csp
        ON  cspr.sale_process_id = csp.id
        where cspr.user_id = #{userId} and csp.process_status=2
        <if test="status != null ">
            and cspr.status = #{status,jdbcType=INTEGER}
        </if>
        <if test="processName != null ">
            and csp.process_name LIKE concat('%',#{processName,jdbcType=VARCHAR},'%')
        </if>
        <if test="productType != null ">
            and csp.product_type = #{productType,jdbcType=INTEGER}
        </if>
        ORDER BY status ASC , update_time DESC
        <if test="pageStart != null and  pageSize!=null ">
            LIMIT #{pageStart},#{pageSize}
        </if>
    </select>



</mapper>
