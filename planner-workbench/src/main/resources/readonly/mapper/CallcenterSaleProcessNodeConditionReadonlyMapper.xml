<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessNodeConditionReadonlyMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeCondition">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="node_id" property="nodeId" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="progress_value" property="progressValue" jdbcType="DECIMAL"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="conditions" property="conditions" jdbcType="VARCHAR"/>
        <result column="visible" property="visible" jdbcType="INTEGER"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="NodeConditionNumbInfoMap" type="com.yirendai.workbench.vo.res.callcenter.CallcenterNodeConditionNumbInfo">
        <result column="nodeId" property="nodeId" jdbcType="VARCHAR"/>
        <result column="conditionNumb" property="conditionNumb" jdbcType="INTEGER"/>
    </resultMap>


    <select id="selectListByIds" resultMap="BaseResultMap">
        SELECT *
        FROM callcenter_sale_process_node_condition
        WHERE is_deleted =0 and
        id IN
        <foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectListByNodeId" resultMap="BaseResultMap">
        SELECT *
        FROM callcenter_sale_process_node_condition
        WHERE node_id = #{nodeId} and is_deleted=0
    </select>

    <select id="countNumberByNodeIds" resultMap="NodeConditionNumbInfoMap">
        SELECT node_id AS nodeId,count(1) as conditionNumb
        FROM callcenter_sale_process_node_condition
        WHERE is_deleted =0 and node_id in
        <foreach collection="nodeIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY node_id;

    </select>


</mapper>
