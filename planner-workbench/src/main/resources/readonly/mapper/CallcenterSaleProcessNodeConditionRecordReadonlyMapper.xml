<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessNodeConditionRecordReadonlyMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeConditionRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="sale_process_id" property="saleProcessId" jdbcType="INTEGER"/>
        <result column="node_id" property="nodeId" jdbcType="VARCHAR"/>
        <result column="condition_id" property="conditionId" jdbcType="INTEGER"/>
        <result column="extra_info" property="extraInfo" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="finish_type" property="finishType" jdbcType="INTEGER"/>
        <result column="finish_time" property="finishTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <resultMap id="NodeConditionNumbInfoMap" type="com.yirendai.workbench.vo.res.callcenter.CallcenterNodeConditionNumbInfo">
        <result column="nodeId" property="nodeId" jdbcType="VARCHAR"/>
        <result column="conditionNumb" property="conditionNumb" jdbcType="INTEGER"/>
    </resultMap>

    <select id="countNumberByNodeIds" resultMap="NodeConditionNumbInfoMap">
        SELECT node_id AS nodeId,count(1) as conditionNumb
        FROM callcenter_sale_process_node_condition_record
        WHERE user_id = #{userId} and status=1
        AND node_id in
        <foreach collection="nodeIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY node_id;
    </select>

    <select id="selectListByMap" resultMap="BaseResultMap">
        SELECT *
        FROM callcenter_sale_process_node_condition_record
        WHERE
        <if test="userId != null ">
            user_id = #{userId}
        </if>
        <if test="status != null ">
            AND status = #{status}
        </if>
        <if test="nodeId != null ">
            AND node_id = #{nodeId}
        </if>
    </select>

</mapper>
