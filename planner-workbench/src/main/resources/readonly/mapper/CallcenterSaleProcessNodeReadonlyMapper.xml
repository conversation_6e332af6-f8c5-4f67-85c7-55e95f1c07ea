<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessNodeReadonlyMapper">

    <resultMap id="BaseResultMap" type="com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="sale_process_id" property="saleProcessId" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="progress_value" property="progressValue" jdbcType="DECIMAL"/>
        <result column="clearance_rule" property="clearanceRule" jdbcType="INTEGER"/>
        <result column="allow_manual" property="allowManual" jdbcType="INTEGER"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectBeanById" resultType="com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode">
        SELECT *
        FROM callcenter_sale_process_node
        WHERE id = #{id}
    </select>

    <select id="selectListByIds" resultMap="BaseResultMap">
        SELECT *
        FROM callcenter_sale_process_node
        WHERE
        id IN
        <foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
