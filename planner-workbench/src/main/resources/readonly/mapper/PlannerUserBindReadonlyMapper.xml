<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.readonly.mapper.PlannerUserBindReadonlyMapper">
    <!-- 查询未完成指定流程编号的用户绑定记录 -->
    <select id="selectUncompletedByProcessNo" resultType="com.yirendai.workbench.readonly.entity.PlannerUserBindReadonly">
        SELECT p.*
        FROM planner_user_bind p
        WHERE p.id > #{startId}
          AND NOT EXISTS (
            SELECT 1
            FROM callcenter_sale_process_record r
            WHERE r.user_id = p.user_id
              AND r.process_no = #{processNo}
              AND r.status != 0
                AND r.is_deleted = 0
        )
        ORDER BY p.id ASC
            LIMIT #{limit}
    </select>
</mapper>
