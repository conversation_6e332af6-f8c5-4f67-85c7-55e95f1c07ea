spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://nacos.paas.idc
        username: user_architecture_team
        password: AvBTIzMW
        namespace: NAMESPACE_RELEASE_PRO_FORTUNE_SERVICE
      config:
        server-addr: http://nacos.paas.idc
        username: user_architecture_team
        password: AvBTIzMW
        group: GROUP_PLANNER_WORKBENCH
        namespace: NAME_RELEASE_PRO_ARCHITECTURE
