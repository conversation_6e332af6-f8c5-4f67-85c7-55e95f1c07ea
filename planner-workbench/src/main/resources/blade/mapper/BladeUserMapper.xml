<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.blade.mapper.BladeUserMapper">
    <select id="listDeptChild" resultType="com.yirendai.workbench.entity.BladeUser">
        select * from blade_user where is_deleted = 0
        <if test="plannerName != null">
            and real_name like concat('%',#{plannerName},'%')
        </if>
        <if test="deptIdList!=null and deptIdList.size>0">
            and id in (
            SELECT
            user_id
            FROM
            blade_user_dept
            WHERE
            dept_id IN
            <foreach collection="deptIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="getByUserId" resultType="com.yirendai.workbench.entity.BladeUser">
        select * from blade_user where id= #{id}
    </select>

    <select id="findUserDeptChangeUser" resultType="com.yirendai.workbench.entity.BladeUser">
        select
            bu.*
        from
            blade_user bu
                inner join blade_user_dept bup on
                bu.id = bup.user_id
        where
            bu.tenant_id = #{tenantId}
          and bu.is_deleted = 0
          and bu.account_status = 1
          and bup.create_time >= DATE_SUB(NOW(), interval 30 MINUTE)
    </select>
</mapper>
