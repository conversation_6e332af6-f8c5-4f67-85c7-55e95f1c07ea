<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.workbench.blade.mapper.BladeDeptMapper">

    <select id="getUserGroupDeptInfo" resultType="org.springblade.system.entity.Dept">
        select dpt.*
        from blade_dept dpt join blade_user_dept udpt on dpt.id=udpt.dept_id and dpt.dept_category=#{deptCategory}
        where udpt.user_id=#{userId};
    </select>
</mapper>
