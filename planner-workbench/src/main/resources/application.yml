#mybatis-plus配置
mybatis-plus:
  global-config:
    enable-sql-runner: true
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  configuration:
    map-underscore-to-camel-case: true
    lazy-loading-enabled: false
    use-generated-keys: true
    jdbc-type-for-null: NULL

#swagger扫描路径配置
swagger:
  base-packages:
    - org.springblade
    - com.yirendai.workbench.controller
spring:
  redis:
    lettuce:
      cluster:
        refresh:
          adaptive: true
          period: 300s
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 150MB

application:
  nasDir: "/data/yx_nas/appsource"
  appSourceUrl: "http://appsource.167.test.yirendai.com/"


logging:
  file:
    path: ./logs
  level:
    com:
      yirendai:
        voiceaiserver:
          mapper: DEBUG
        robot: DEBUG

management:
  endpoints:
    web:
      exposure:
        include: health,info # 仅开放必要的健康检查（health）和信息（info）端点
        exclude: env,beans,configprops,mappings,metrics,shutdown,heapdump  # 关闭所有敏感端点（确保无遗漏）