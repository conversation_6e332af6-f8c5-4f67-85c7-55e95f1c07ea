{"nodes": {"greeting": {"id": "greeting", "description": "初始问候阶段", "successIntents": ["product_interest", "ask_detail"], "failureIntents": ["refuse", "hangup"], "maxRetries": 20, "successPath": "product_intro", "failurePath": "exit", "timeout": 20}, "product_intro": {"id": "product_intro", "description": "产品介绍阶段", "successIntents": ["ask_procedure", "confirm_advantage"], "failureIntents": ["doubt", "not_interested"], "maxRetries": 30, "successPath": "guide_login", "failurePath": "handle_objection", "timeout": 30}, "guide_login": {"id": "guide_login", "description": "引导登录阶段", "successIntents": ["app_login", "wechat_login"], "failureIntents": ["refuse_login", "unknown_app"], "maxRetries": 20, "successPath": "post_login", "failurePath": "alternative_login", "timeout": 25}, "alternative_login": {"id": "alternative_login", "description": "替代登录方式阶段", "successIntents": ["sms_link"], "failureIntents": ["refuse_sms"], "maxRetries": 10, "successPath": "post_login", "failurePath": "exit", "timeout": 20}, "post_login": {"id": "post_login", "description": "登录后操作阶段", "successIntents": ["enter_h5"], "failureIntents": ["login_failed"], "maxRetries": 20, "successPath": "browse_and_explain", "failurePath": "handle_login_issue", "timeout": 30}, "browse_and_explain": {"id": "browse_and_explain", "description": "浏览与讲解阶段", "successIntents": ["proceed_to_fill"], "failureIntents": ["confusion", "hesitation"], "maxRetries": 30, "successPath": "fill_info", "failurePath": "reassure", "timeout": 40}, "fill_info": {"id": "fill_info", "description": "信息填写阶段", "successIntents": ["submit"], "failureIntents": ["incorrect_info"], "maxRetries": 20, "successPath": "payment", "failurePath": "correct_info", "timeout": 35}, "payment": {"id": "payment", "description": "支付阶段", "successIntents": ["payment_success"], "failureIntents": ["payment_failed"], "maxRetries": 10, "successPath": "post_payment", "failurePath": "payment_assistance", "timeout": 60}, "post_payment": {"id": "post_payment", "description": "支付后阶段", "successIntents": ["confirmation"], "failureIntents": ["question"], "maxRetries": 10, "successPath": "exit", "failurePath": "answer_questions", "timeout": 30}, "handle_objection": {"id": "handle_objection", "description": "处理异议阶段", "successIntents": ["addressed"], "failureIntents": ["persistent_refusal"], "maxRetries": 20, "successPath": "product_intro", "failurePath": "exit", "timeout": 25}, "reassure": {"id": "reassure", "description": "安抚与鼓励阶段", "successIntents": ["proceed"], "failureIntents": ["still_hesitant"], "maxRetries": 20, "successPath": "browse_and_explain", "failurePath": "exit", "timeout": 30}, "correct_info": {"id": "correct_info", "description": "信息修正阶段", "successIntents": ["corrected"], "failureIntents": ["give_up"], "maxRetries": 20, "successPath": "fill_info", "failurePath": "exit", "timeout": 30}, "payment_assistance": {"id": "payment_assistance", "description": "支付协助阶段", "successIntents": ["retry_payment"], "failureIntents": ["abort_payment"], "maxRetries": 10, "successPath": "payment", "failurePath": "exit", "timeout": 45}, "answer_questions": {"id": "answer_questions", "description": "回答问题阶段", "successIntents": ["satisfied"], "failureIntents": ["more_questions"], "maxRetries": 30, "successPath": "exit", "failurePath": "continue_answering", "timeout": 30}, "continue_answering": {"id": "continue_answering", "description": "继续回答问题阶段", "successIntents": ["satisfied"], "failureIntents": ["still_confused"], "maxRetries": 20, "successPath": "exit", "failurePath": "reassure", "timeout": 30}, "exit": {"id": "exit", "description": "退出流程阶段", "successIntents": [], "failureIntents": [], "maxRetries": 0, "successPath": null, "failurePath": null, "timeout": null}}}