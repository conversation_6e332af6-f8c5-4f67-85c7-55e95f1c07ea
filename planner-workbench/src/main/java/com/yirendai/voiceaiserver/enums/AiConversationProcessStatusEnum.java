package com.yirendai.voiceaiserver.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum AiConversationProcessStatusEnum {
    /**
     * 低优先级初始化任务
     */
    LOW_PRIORITY_INIT(-2, "低优先级初始化任务"),
    /**
     * 仅初始化等待xxlJob调用
     */
    INIT_WAITING(-1, "仅初始化等待调用"),
    /**
     * 未开始
     */
    NOT_START(0, "未开始"),
    /**
     * 进行中
     */
    PROCESSING(1, "进行中"),
    /**
     * 已完成
     */
    FINISHED(2, "已完成"),
    /**
     * 已取消
     */
    CANCELLED(3, "已取消"),
    /**
     * 已失败
     */
    FAILED(4, "已失败"),
    /**
     * 暂无配置无法执行
     */
    NO_CONFIG(5, "暂无配置无法执行");

    private final Integer code;
    private final String desc;

    AiConversationProcessStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AiConversationProcessStatusEnum getByCode(Integer code) {
        for (AiConversationProcessStatusEnum value : AiConversationProcessStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
