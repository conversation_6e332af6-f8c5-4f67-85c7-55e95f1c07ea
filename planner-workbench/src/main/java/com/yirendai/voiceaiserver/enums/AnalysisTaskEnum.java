package com.yirendai.voiceaiserver.enums;

public enum AnalysisTaskEnum {

    NOT_STARTED(0, "未开始"),
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完成");

    private final Integer code;
    private final String message;

    AnalysisTaskEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据code获取对应的枚举
     * @param code 状态码
     * @return 枚举值
     */
    public static AnalysisTaskEnum fromCode(Integer code) {
        for (AnalysisTaskEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}
