package com.yirendai.voiceaiserver.enums;

import lombok.Getter;

/**
 * 主意图枚举
 * <AUTHOR>
 * @since 2025-01-03
 */
@Getter
public enum PrimaryIntentEnum {
    
    D_CLASS(3, "D_CLASS", "D类产品", "港保产品推荐沟通"),
    B_CLASS(2,"B_CLASS", "B类产品", "非港保保险产品沟通"),
    CLASS_001(1,"001_CLASS", "001类产品", "固收理财产品沟通"),
    NONE(0, "NONE", "无意图", "无匹配产品意图");

    private final int id;
    private final String code;
    private final String name;
    private final String description;
    
    PrimaryIntentEnum(Integer id, String code, String name, String description) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public static PrimaryIntentEnum getByCode(String code) {
        for (PrimaryIntentEnum value : PrimaryIntentEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return NONE;
    }

    public static PrimaryIntentEnum getById(Integer id) {
        for (PrimaryIntentEnum value : PrimaryIntentEnum.values()) {
            if (value.getId() == id) {
                return value;
            }
        }
        return NONE;
    }
    
    public static boolean isValidIntent(String code) {
        return getByCode(code) != NONE;
    }
}

