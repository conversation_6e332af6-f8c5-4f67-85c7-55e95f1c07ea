package com.yirendai.voiceaiserver.enums;

import com.yirendai.voiceaiserver.vo.response.AssetTypeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@ApiModel(value = "资产类型")
public enum AssetTypeEnum {
    @ApiModelProperty(value = "D类")
    D(1, "D类"),

    @ApiModelProperty(value = "B类")
    B(2, "B类"),

    @ApiModelProperty(value = "大额001")
    BIG(3, "大额001"),

    @ApiModelProperty(value = "小额001")
    SMALL(4, "小额001"),
    ;

    private final Integer code;
    private final String desc;

    AssetTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static final List<AssetTypeVO> allList = new ArrayList<>();

    public static final List<Integer> allCodeList = new ArrayList<>();

    static {
        for (AssetTypeEnum type : AssetTypeEnum.values()) {
            allCodeList.add(type.getCode());
            allList.add(AssetTypeVO.builder().code(type.getCode()).desc(type.getDesc()).build());
        }
    }
}
