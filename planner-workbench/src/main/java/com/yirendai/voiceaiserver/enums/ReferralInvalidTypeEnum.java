package com.yirendai.voiceaiserver.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Getter;

@Getter
public enum ReferralInvalidTypeEnum {
    MGM_ASSIGN(1, "mgm指定"),
    MANUAL(2, "手动"),
    AUTO(3, "自动"),
    ;

    private final Integer code;
    private final String desc;

    ReferralInvalidTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Integer> getAll() {
        return Arrays.stream(values()).map(ReferralInvalidTypeEnum::getCode).collect(Collectors.toList());
    }

}
