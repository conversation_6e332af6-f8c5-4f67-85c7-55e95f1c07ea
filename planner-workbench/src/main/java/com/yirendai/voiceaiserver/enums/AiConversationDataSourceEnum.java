package com.yirendai.voiceaiserver.enums;

import com.yirendai.workbench.entity.AiConversationChatSummary;
import com.yirendai.workbench.entity.AiConversationWechatSummary;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据来源类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AiConversationDataSourceEnum {
    /**
     * 呼叫中心语音数据
     */
    CALL_CENTER("0", "呼叫中心", AiPlannerChatContact.class),
    /**
     * 企微对话数据
     */
    QI_WECHAT("1", "企微对话", AiConversationWechatSummary.class),

    WECHAT("2", "微信对话", AiConversationChatSummary.class),
    ;

    private final String code;
    private final String name;
    private final Class<?> dataClass;

    /**
     * 根据代码获取枚举
     */
    public static AiConversationDataSourceEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AiConversationDataSourceEnum value : AiConversationDataSourceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}