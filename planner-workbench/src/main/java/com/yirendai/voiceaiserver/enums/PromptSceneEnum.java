package com.yirendai.voiceaiserver.enums;

import java.util.ArrayList;
import java.util.List;

import com.yirendai.voiceaiserver.vo.response.PromptSceneVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * 提示词配置使用场景Enum
 */
@Getter
@ApiModel(value = "场景类型")
public enum PromptSceneEnum {
    @ApiModelProperty(value = "单条avaya聊天内容小结")
    SUMMARY_SIGNAL_AVAYA_CONTENT(1, "单条avaya聊天内容小结"),

    @ApiModelProperty(value = "语音转文字")
    VOICE_TO_TEXT(2, "语音转文字"),

    @ApiModelProperty(value = "降噪")
    DENOISE(3, "降噪"),

    @ApiModelProperty(value = "声纹检测")
    VOICE_SEARCH(4, "声纹检测"),

    @ApiModelProperty(value = "用户标签打标")
    USER_TAG(5, "用户标签打标"),

    @ApiModelProperty(value = "下一次通话时间建义提示")
    NEXT_CALL_TIME(6, "下一次通话时间建义提示"),

    @ApiModelProperty(value = "内容稽查（违规分类)")
    CONTENT_CHECK(7, "内容稽查（违规分类)"),

    @ApiModelProperty(value = "新呼叫中心通话音转文")
    CALL_CENTER_TRANSLATE(8, "新呼叫中心通话音转文"),

    @ApiModelProperty(value = "企微对话小结")
    WECHAT_CONVERSATION_SUMMARY(9, "企微对话小结"),

    @ApiModelProperty(value = "企微对话标签")
    WECHAT_CONVERSATION_TAG(10, "企微对话标签"),

    @ApiModelProperty(value = "机器人通话小结")
    ROBOT_CALL_SUMMARY(14, "机器人通话小结"),

    @ApiModelProperty(value = "机器人通话意向分析")
    ROBOT_CALL_INTENTION(15, "机器人通话意向分析"),

    @ApiModelProperty(value = "AI合规回访结果分析")
    AI_COMPLIANCE_BACK_RESULT(16, "AI合规回访结果分析"),

    @ApiModelProperty(value = "AI内容任务分析")
    AI_TASK_CONTENT_ANALYSIS(17, "AI任务内容分析"),

    @ApiModelProperty(value = "AI客服工单")
    AI_SERVICE_WORK_ORDER(18, "AI客服工单"),

    @ApiModelProperty(value = "汇总单条avaya聊天内容小结")
    SUM_SUMMARY_SIGNAL_AVAYA_CONTENT(101, "汇总单条avaya聊天内容小结"),

    @ApiModelProperty(value = "汇总下一次通话时间建义提示")
    SUM_NEXT_CALL_TIME(106, "汇总下一次通话时间建义提示"),

    @ApiModelProperty(value = "汇总企微对话小结")
    SUM_WECHAT_CONVERSATION_SUMMARY(109, "汇总企微对话小结"),

    @ApiModelProperty(value = "机器人通话小结汇总")
    SUM_ROBOT_CALL_SUMMARY(114, "机器人通话小结汇总"),


    @ApiModelProperty(value = "AI内容任务分析汇总")
    AI_TASK_CONTENT_ANALYSIS_SUMMARY(117, "AI内容任务分析汇总"),

    @ApiModelProperty(value = "AI客服工单汇总")
    AI_SERVICE_WORK_ORDER_SUMMARY(118, "AI客服工单汇总"),


    @ApiModelProperty(value = "AI助手")
    AI_HELPER(201, "AI助手补充提示词"),

    @ApiModelProperty(value = "AI助手补充提示词实时转语音场景使用")
    AI_HELPER_TTS(211, "AI助手补充提示词实时转语音场景使用"),


    @ApiModelProperty(value = "AI新闻")
    AI_NEWS(202, "AI新闻"),


    @ApiModelProperty(value = "AI新闻格式化")
    AI_NEWS_FORMAT(203, "AI新闻格式化"),

    @ApiModelProperty(value = "厚予-语音转文字")
    HOUYU_VOICE_TO_TEXT(11, "厚予-语音转文字"),

    @ApiModelProperty(value = "厚予-降噪")
    HOUYU_DENOISE(12, "厚予-降噪"),

    @ApiModelProperty(value = "厚予-声纹检测")
    HOUYU_VOICE_SEARCH(13, "厚予-声纹检测"),
	
    VISIT_SUMMARY(204, "拜访总结"),
    SUM_VISIT_SUMMARY(205, "汇总单条拜访总结"),

    @ApiModelProperty(value = "港险主意图检测")
    HONG_KONG_INSURANCE_INTENT(19, "港险主意图检测"),
    
    @ApiModelProperty(value = "港险流程节点识别")
    HONG_KONG_INSURANCE_NODES(20, "港险流程节点识别"),

    @ApiModelProperty(value = "AI客户维度内容任务分析")
    AI_CUSTOMER_TASK_CONTENT_ANALYSIS(21, "AI客户维度内容任务分析"),


        ;


    private final Integer code;

    private final String desc;

    PromptSceneEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static final List<PromptSceneVo> allList = new ArrayList<>();

    static {
        for (PromptSceneEnum type : PromptSceneEnum.values()) {
            allList.add(PromptSceneVo.builder().code(type.getCode()).desc(type.getDesc()).build());
        }
    }
}
