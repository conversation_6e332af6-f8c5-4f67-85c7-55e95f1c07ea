package com.yirendai.voiceaiserver.enums;

import lombok.Getter;

/**
 * 理财师声音状态枚举类
 */
@Getter
public enum VoiceStatusEnum {
    UNREVIEWED(0, "未审核"),
    UNDER_REVIEW(1, "审核中"),
    APPROVED(2, "审核通过"),
    REJECTED(3, "审核拒绝"),
    ;

    /**
     * 状态码
     */
    Integer code;

    /**
     * 状态名称
     */
    String name;

    VoiceStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
