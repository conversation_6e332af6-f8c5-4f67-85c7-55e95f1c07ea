package com.yirendai.voiceaiserver.enums;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@ApiModel(value = "AI大模型类型")
public enum AiModelType {
    /**
     *  默认为prompt表中配置的模型，支持
     *  qwen-plus / 7b
     *  qwen-max /72b
     * 【理财师总结模型】yiren-summary / ft::Qwen2-7B-Instruct:1-bgto
     * 【理财师打标模型】yiren-label / ft::Qwen2-7B-Instruct:1-rpoo
     * 【理财师意图分类模型】yiren-intention / ft:Qwen2-0.5B-extend-vocabulary:1-zxqc
     */
    COMMON_MODEL(0, "通用模型"),
    LARGE_MODEL(1, "大参数固定模型"),
    SUMMARY_DISTILLATION_MODEL(2, "小结蒸馏模型"),
    TAG_DISTILLATION_MODEL(3, "标签蒸馏模型"),
    ;
    private int code;
    private String desc;

    AiModelType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
