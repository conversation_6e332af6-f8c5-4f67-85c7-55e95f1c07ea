package com.yirendai.voiceaiserver.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum AiConversationTagTypeEnum {
    /**
     * 匹配标签
     */
    MATCH(0, "匹配标签"),
    /**
     * 推荐新增标签
     */
    RECOMMEND(1, "推荐新增标签"),
    ;

    private final Integer code;
    private final String desc;

    AiConversationTagTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AiConversationTagTypeEnum getByCode(Integer code) {
        for (AiConversationTagTypeEnum value : AiConversationTagTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
