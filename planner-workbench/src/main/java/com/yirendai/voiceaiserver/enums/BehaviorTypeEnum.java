package com.yirendai.voiceaiserver.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum BehaviorTypeEnum {
    LIKE(1, "点赞"),
    DOWN(2, "踩"),
    STAR(3, "收藏"),
    CANCEL_STAR(4, "取消收藏"),
    ;

    private final Integer code;
    private final String desc;

    BehaviorTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Integer> getAll() {
        return Arrays.stream(values()).map(BehaviorTypeEnum::getCode).collect(Collectors.toList());
    }

    public static List<Integer> getLikeOrDown() {
        return Arrays.asList(LIKE.getCode(), DOWN.getCode());
    }
}
