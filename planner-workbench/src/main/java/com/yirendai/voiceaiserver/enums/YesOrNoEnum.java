package com.yirendai.voiceaiserver.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否枚举
 */
@Getter
@AllArgsConstructor
public enum YesOrNoEnum {


    NO(0,"否"),

    YES(1,"是"),;

    final Integer key;

    final String value;

    public static YesOrNoEnum getByKey(Integer key){
        if (key == null){
            return null;
        }

        for(YesOrNoEnum e : YesOrNoEnum.values()){
            if(e.getKey().equals(key)){
                return e;
            }
        }

        return null;
    }
}
