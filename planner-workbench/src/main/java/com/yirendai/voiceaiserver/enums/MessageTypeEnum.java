package com.yirendai.voiceaiserver.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 信息类型Enum
 */
@Getter
public enum MessageTypeEnum {
    NEWS(1, "新闻"),
    ACTIVITY_RULE(2, "活动规则"),
    ;

    private final Integer code;
    private final String desc;

    MessageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Integer> getAll() {
        return Arrays.stream(values()).map(MessageTypeEnum::getCode).collect(Collectors.toList());
    }
}
