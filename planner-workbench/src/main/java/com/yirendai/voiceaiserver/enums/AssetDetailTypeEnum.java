package com.yirendai.voiceaiserver.enums;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

@Getter
@ApiModel(value = "资产详细类型")
public enum AssetDetailTypeEnum {
    @ApiModelProperty(value = "D类")
    D(1, "D类"),

    @ApiModelProperty(value = "新客长险")
    QW_LONG(2, "新客长险"),

    @ApiModelProperty(value = "新客大额001")
    QW_BIG(3, "新客大额001"),

    @ApiModelProperty(value = "新客小额001")
    QW_SMALL(4, "新客小额001"),

    @ApiModelProperty(value = "老客长险")
    CSG_LONG(5, "老客长险"),

    @ApiModelProperty(value = "老客大额001")
    CSG_BIG(6, "老客大额001"),

    @ApiModelProperty(value = "老客小额001")
    CSG_SMALL(7, "老客小额001"),
    ;

    private final Integer code;
    private final String desc;

    AssetDetailTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
