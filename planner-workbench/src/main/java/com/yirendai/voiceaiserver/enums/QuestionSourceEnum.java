package com.yirendai.voiceaiserver.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 问题来源Enum
 */
@Getter
public enum QuestionSourceEnum {
    MATCHING_LIST(1, "匹配列表"),
    SEARCH_BUTTON(2, "搜索按钮"),
    ;

    private final Integer code;
    private final String desc;

    QuestionSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Integer> getAll() {
        return Arrays.stream(values()).map(QuestionSourceEnum::getCode).collect(Collectors.toList());
    }
}
