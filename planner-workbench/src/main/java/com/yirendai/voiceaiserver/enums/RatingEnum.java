package com.yirendai.voiceaiserver.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点赞，点踩
 */
@Getter
@AllArgsConstructor
public enum RatingEnum {

    LIKE("like", "点赞"),
    DISLIKE("dislike", "点踩"),
    NONE("0", "撤销");

    private final String value;
    private final String description;

    public static RatingEnum getByValue(String rating) {
        for (RatingEnum ratingEnum : RatingEnum.values()) {
            if (ratingEnum.getValue().equals(rating)) {
                return ratingEnum;
            }
        }
        return null;
    }
}
