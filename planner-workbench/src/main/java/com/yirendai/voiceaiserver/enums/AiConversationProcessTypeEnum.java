package com.yirendai.voiceaiserver.enums;

import com.yirendai.robot.modules.session.entity.RobotSession;
import com.yirendai.voiceaiserver.mq.send.TopicEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.task.conversation.sub.*;
import com.yirendai.workbench.entity.*;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum AiConversationProcessTypeEnum {
    /**
     * 内容小结
     */
    CONTENT_SUMMARY(0, "内容小结", ContentSummaryProcess.class, AiPlannerChatContact.class, TopicEnum.SUMMARY),
    /**
     * 标签分类
     */
    TAG_CATEGORY(1, "标签分类", TagCategoryProcess.class, AiPlannerChatContact.class, TopicEnum.USER_TAG),
    /**
     * 内容稽查
     */
    CONTENT_INSPECT(2, "内容稽查", ContentInspectProcess.class, AiPlannerChatContact.class, TopicEnum.CONVERSATION_INSPECT),
    /**
     * 通话预约
     */
    CALL_RESERVATION(3, "通话预约", CallReservationProcess.class, AiPlannerChatContact.class, TopicEnum.CALL_RESERVATION),
    /**
     * 企微小结
     */
    CONTENT_WECHAT_SUMMARY(4, "企微小结", ContentWechatSummaryProcess.class, AiConversationWechatSummary.class, TopicEnum.CONTENT_WECHAT_SUMMARY_TAG),
    /**
     * 企微标签
     */
    CONTENT_WECHAT_TAG(5, "企微标签", ContentWechatTagProcess.class, AiConversationWechatSummary.class, TopicEnum.CONTENT_WECHAT_SUMMARY_TAG),
    VISIT_SUMMARY(6, "拜访总结", VisitSummaryProcess.class, CallcenterVisitRecord.class, TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC),
    /**
     * 机器人通话用户意向
     */
    ROBOT_CALL_USER_INTENTION(7, "机器人通话用户意向", RobotCallUserIntentionProcess.class, RobotSession.class, TopicEnum.ROBOT_CALL_USER_ANALYSIS),
    /**
     * 机器人通话用户总结
     */
    ROBOT_CALL_USER_SUMMARY(8, "机器人通话用户总结", RobotCallUserSummaryProcess.class, RobotSession.class, TopicEnum.ROBOT_CALL_USER_ANALYSIS),

    /**
     * 通话合规回访结果
     */
    AI_CALL_USER_COMPLIANCE_RESULT(9, "AI通话用户合规回访结果", CallUserComplianceResultProcess.class, AiPlannerChatContact.class, TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC),

    /**
     * 通话内容分析（通用）
     */
    AI_TASK_CONTENT_ANALYSIS(10, "通话任务内容分析", ContentAnalysisTaskProcess.class, AiAnalysisTaskResult.class, TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC),

    /**
     * 客服工单
     */
    AI_SERVICE_WORK_ORDER(11, "AI客服工单", ServiceWorkOrderProcess.class, AiCustomerWorkOrder.class, TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC),

    /**
     * 港险主意图检测
     */
    HONG_KONG_INSURANCE_INTENT(12, "港险主意图检测", HongKongInsuranceIntentProcess.class, HongKongInsuranceIntentProcess.class, TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC),
    
    /**
     * 港险流程节点识别
     */
    HONG_KONG_INSURANCE_NODES(13, "港险流程节点识别", HongKongInsuranceTagsProcess.class, HongKongInsuranceTagsProcess.class, TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC),
    /**
     * 客户数据分析
     */
    CUSTOMER_DATA_ANALYSIS(14, "客户数据分析", CustomerDataAnalysisProcess.class, AiAnalysisTaskCallRecord.class, TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC),
    ;

    private final Integer code;
    private final String desc;
    // 数据处理类
    private final Class<? extends AbstractAiConversationProcess<?>> conversationProcess;
    // 数据来源类
    private final Class<?> dataClass;
    //mq topic
    private final TopicEnum mqTopic;

    AiConversationProcessTypeEnum(Integer code, String desc, Class<? extends AbstractAiConversationProcess<?>> conversationProcess, Class<?> dataClass, TopicEnum mqTopic) {
        this.code = code;
        this.desc = desc;
        this.conversationProcess = conversationProcess;
        this.dataClass = dataClass;
        this.mqTopic = mqTopic;
    }

    public static AiConversationProcessTypeEnum getByCode(Integer code) {
        for (AiConversationProcessTypeEnum value : AiConversationProcessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid AiConversationProcessTypeEnum code: " + code);
    }
}
