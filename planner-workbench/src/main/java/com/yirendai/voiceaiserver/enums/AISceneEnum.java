package com.yirendai.voiceaiserver.enums;

/**
 * AI助手使用场景
 */
public enum AISceneEnum {
    DEFAULT(201, "默认场景"),
    REAL_TIME_CALL_TRANSCRIPTION(211, "通话实时转语音场景"),
    /**
     * 分析通话记录
     */
    ANALYSIS_CALL_RECORD(212, "分析通话记录"),
    ;

    private final Integer key;
    private final String description;

    AISceneEnum(Integer key, String description) {
        this.key = key;
        this.description = description;
    }

    public Integer getKey() {
        return key;
    }

    public String getDescription() {
        return description;
    }

    public static AISceneEnum fromKey(Integer key) {
        for (AISceneEnum scene : AISceneEnum.values()) {
            if (scene.getKey().equals(key)) {
                return scene;
            }
        }
        throw new IllegalArgumentException("Unknown AI Scene key: " + key);
    }
}
