package com.yirendai.voiceaiserver.enums;

import lombok.Getter;
import java.util.ArrayList;
import java.util.List;

/**
 * 企微消息类型Enum
 */
@Getter
public enum QwMsgTypeEnum {
    TEXT("text", "文本"),
    IMAGE("image", "图片"),
    LINK("link", "跳转链接"),
    EMOTION("emotion", "表情包"),
    VOICE("voice", "语音"),
    WEAPP("weapp", "小程序"),
    REVOKE("revoke", "撤回"),
    EXTERNAL_REDPACKET("external_redpacket", "红包"),
    FILE("file", "文件"),
    MEETING_VOICE_CALL("meeting_voice_call", "语音电话"),
    SPHFEED("sphfeed", "视频号"),
    VIDEO("video", "视频"),
    MIXED("mixed", "混合"),
    CHATRECORD("chatrecord", "聊天记录"),
    CARD("card", "名片"),
    LOCATION("location", "位置"),
    MEETING("meeting", "会议"),
    REDPACKET("redpacket", "红包"),
    AVAYA("avaya", "avaya语音"),
    CALL_CENTER("call_center", "新呼叫中心通话"),
    HOUYU("houyu", "厚予语音"),
    ;

    /**
     * 消息类型
     */
    String type;

    /**
     * 类型名称
     */
    String name;

    QwMsgTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static final List<String> nonQwMsgTypeList = new ArrayList<>();

    static {
        nonQwMsgTypeList.add(AVAYA.getType());
        nonQwMsgTypeList.add(CALL_CENTER.getType());
        nonQwMsgTypeList.add(HOUYU.getType());
    }
}
