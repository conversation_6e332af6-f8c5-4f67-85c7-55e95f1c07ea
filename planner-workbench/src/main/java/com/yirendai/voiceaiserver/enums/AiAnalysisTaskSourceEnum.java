package com.yirendai.voiceaiserver.enums;

import com.yirendai.workbench.entity.AiPlannerChatContact;
import lombok.Getter;

@Getter
public enum AiAnalysisTaskSourceEnum {
    //    @ApiModelProperty(value = "任务来源,0-会话任务分析，1-通讯记录，2-公海列表，3-客户列表")
    CONTENT_ANALYSIS_TASK(0, "会话任务分析"),
    CONTENT_COMMUNICATION_TASK(1, "通讯记录"),
    CONTENT_PUBLIC_POOL_TASK(2, "公海列表"),
    CONTENT_CUSTOMER_LIST_TASK(3, "客户列表"),
    CONTENT_CUSTOMER_MY(4, "我的客户"),
    ;



    private final int code;
    private final String name;

    AiAnalysisTaskSourceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(int code) {
        for (AiAnalysisTaskSourceEnum c : AiAnalysisTaskSourceEnum.values()) {
            if (c.getCode() == code) {
                return c.name;
            }
        }
        return "未知";
    }

    public static int getCode(String name) {
        for (AiAnalysisTaskSourceEnum c : AiAnalysisTaskSourceEnum.values()) {
            if (c.getName().equals(name)) {
                return c.code;
            }
        }
        return -1;
    }

}
