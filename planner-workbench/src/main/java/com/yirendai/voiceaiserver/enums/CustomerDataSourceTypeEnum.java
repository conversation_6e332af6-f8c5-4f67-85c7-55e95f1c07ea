package com.yirendai.voiceaiserver.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户数据源类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Getter
@AllArgsConstructor
public enum CustomerDataSourceTypeEnum {

    /**
     * 通话记录
     */
    CALL_RECORD("1", "通话记录", "ai_planner_chat_contact"),

    /**
     * 短信记录
     */
    SMS_RECORD("2", "短信记录", "ai_robot_tenant_sms_send_log"),

    /**
     * 微信记录
     */
    WECHAT_RECORD("3", "微信记录", "ai_conversation_chat_summary"),

    /**
     * 企微记录
     */
    ENTERPRISE_WECHAT_RECORD("4", "企微记录", "ai_conversation_wechat_summary"),

    /**
     * 客户基本信息
     */
    CUSTOMER_BASIC_INFO("5", "客户基本信息", "ai_analysis_task_call_record"),

    /**
     * 客户业务数据
     */
    CUSTOMER_BUSINESS_DATA("6", "客户业务数据", "ai_analysis_task_call_record");

    /**
     * 数据源类型代码
     */
    private final String code;

    /**
     * 数据源类型名称
     */
    private final String name;

    /**
     * 对应的数据表名
     */
    private final String tableName;

    /**
     * 根据代码获取枚举
     */
    public static CustomerDataSourceTypeEnum getByCode(String code) {
        for (CustomerDataSourceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否是客户数据类型（5、6、7存储在ai_analysis_task_call_record表）
     */
    public boolean isCustomerDataType() {
        return CUSTOMER_BASIC_INFO.equals(this) 
            || CUSTOMER_BUSINESS_DATA.equals(this);
    }

    /**
     * 判断代码是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
