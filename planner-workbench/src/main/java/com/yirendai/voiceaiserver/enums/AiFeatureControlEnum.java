package com.yirendai.voiceaiserver.enums;

/**
 * AI功能模块枚举
 */
public enum AiFeatureControlEnum {
    AI_ASSISTANT_QA_FLOAT("AI助手浮层挂件+问答浮层"),
    AI_USER_TAGS_FLOOR("AI用户标签模块（原页面增加楼层）"),
    CALL_BUTTON_FLOAT_SUMMARY("呼叫按钮悬浮/点击出现上次小结内容-前情提要"),
    CALL_AFTER_SUMMARY_POPUP("呼叫后-手工小结提交后弹窗：ai小结生成Loading+ai小结内容"),
    TELESALES_RECORD_LISTEN_SUMMARY("沟通记录-电销记录-调听弹窗-增加本次通话AI小结内容"),
    WECHAT_RECORD_QUERY_PAGE("沟通记录-企微记录分页签-独立查询页（含内容&日小结）"),
    COMMUNICATION_RECORD_LISTEN_SUMMARY_WARNING("通讯记录-调听弹窗-增加AI小结内容&AI预警内容-后续迭代"),
    COMMUNICATION_RECORD_LIST_WARNING_FILTER("通讯记录列表-增加AI预警列&筛选-后续迭代"),
    WECHAT_MENU_QUERY_PAGE("企微菜单-独立查询页（含客户筛选&内容&日小结）"),
    SMART_CALENDAR_NEWS_MODULE("智能日历-今日要闻模块（接口-标题+跳转链接）"),
    NEW_QW_MSG_MENU_QUERY_PAGE("新企微菜单-已绑定企微员工列表&企微好友&消息内容"),
    ;

    private final String featureName;

    AiFeatureControlEnum(String featureName) {
        this.featureName = featureName;
    }

    public String getFeatureName() {
        return featureName;
    }

    public String getCode() {
        return name();
    }

    public static AiFeatureControlEnum fromCode(String code) {
        for (AiFeatureControlEnum feature : values()) {
            if (feature.getCode().equals(code)) {
                return feature;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public static AiFeatureControlEnum fromFeatureName(String featureName) {
        for (AiFeatureControlEnum feature : values()) {
            if (feature.getFeatureName().equals(featureName)) {
                return feature;
            }
        }
        throw new IllegalArgumentException("Invalid feature name: " + featureName);
    }
}
