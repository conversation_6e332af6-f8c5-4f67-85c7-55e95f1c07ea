package com.yirendai.voiceaiserver.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * redis集群配置参数
 */
@Data
@Configuration
public class RedisClusterProperties {
    @Value("${spring.redis.cluster.nodes:n}")
    private String nodes;

    @Value("${spring.redis.cluster.password:p}")
    private String password;

    /**
     * 最大重试次数
     */
    @Value("${spring.redis.cluster.maxAttempts:3}")
    private int maxAttempts;
    /**
     * 最大连接数, 默认8个
     */
    @Value("${spring.redis.cluster.maxTotal:8}")
    private int maxTotal = 8;
    /**
     * 获取连接时的最大等待时间(毫秒)
     * 如果超时就抛异常; 若值为负数意为阻塞不确定的时间, 默认-1
     */
    @Value("${spring.redis.cluster.maxWaitMillis:-1}")
    private long maxWaitMillis;
    /**
     * 最大空闲连接数, 负数意为无限制, 默认8
     */
    @Value("${spring.redis.cluster.maxIdle:8}")
    private int maxIdle;
    /**
     * 最小空闲连接数, 默认0
     */
    @Value("${spring.redis.cluster.minIdle:0}")
    private int minIdle;
    /**
     * 逐出连接的最小空闲时间(毫秒), 默认30分钟, 即1800000毫秒
     */
    @Value("${spring.redis.cluster.minEvictableIdleTimeMillis:1800000}")
    private long minEvictableIdleTimeMillis;
    /**
     * 逐出扫描的时间间隔(毫秒), 如果为负数, 则不运行逐出线程, 默认-1
     */
    @Value("${spring.redis.cluster.timeBetweenEvictionRunsMillis:-1}")
    private long timeBetweenEvictionRunsMillis;
    /**
     * 连接Redis Server超时时间
     */
    @Value("${spring.redis.cluster.connectionTimeout:10000}")
    private int connectionTimeout;
    /**
     * 等待Response超时时间
     */
    @Value("${spring.redis.cluster.soTimeout:6000}")
    private int soTimeout;
    /**
     * 连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
     */
    private boolean blockWhenExhausted = true;
    /**
     * 每次逐出检查时逐出的最大数目, 如果为负数就是1/abs(n), 默认3
     */
    @Value("${spring.redis.cluster.numTestsPerEvictionRun:3}")
    private int numTestsPerEvictionRun = 3;
    /**
     * 在空闲时检查有效性, 默认false
     */
    @Value("${spring.redis.cluster.testWhileIdle:false}")
    private boolean testWhileIdle;

    /**
     * 在获取连接的时候检查有效性, 默认false
     */
    @Value("${spring.redis.cluster.testOnBorrow:false}")
    private boolean testOnBorrow;
}

