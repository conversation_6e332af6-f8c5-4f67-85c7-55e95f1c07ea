package com.yirendai.voiceaiserver.config;

import com.google.common.collect.Sets;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.annotations.ApiIgnore;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * <AUTHOR>
 * @desc
 * @date 2021-10-27
 */
@Configuration
@ComponentScan(value = "com.yirendai.voiceaiserver.controller")
public class SwaggerVoiceConfig {

    @Value("${swagger.show:true}")
    private boolean swaggerShow;

    @Bean
    public Docket defaultApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(swaggerShow)
                .apiInfo(apiInfo())
                .ignoredParameterTypes(ApiIgnore.class)
                .protocols(Sets.newHashSet("http"))
                .pathMapping("/")
                .select()
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.regex(".*"))
                .build()
                .groupName("all");
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("理财师AI助手 API")
                .description("接口说明")
                .version("1.0")
                .build();
    }
}

