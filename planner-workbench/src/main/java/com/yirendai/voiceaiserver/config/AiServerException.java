package com.yirendai.voiceaiserver.config;

import com.yirendai.voiceaiserver.common.IResultCode;
import lombok.Getter;

public class AiServerException extends RuntimeException{

    @Getter
    private Integer code;

    public AiServerException(IResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
    }

    public AiServerException(Integer code, String message) {
        super(message);
        this.code = code;
    }
}
