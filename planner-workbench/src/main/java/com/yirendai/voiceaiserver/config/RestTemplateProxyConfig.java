package com.yirendai.voiceaiserver.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @time 2024/1/2 19:40
 **/
@Configuration
@Slf4j
public class RestTemplateProxyConfig {

    @Value("${http.proxy.url}")
    private String proxyUrl;

    @Value("${http.proxy.port}")
    private int proxyPort;

    @Bean
    public RestTemplate restTemplateProxy(ClientHttpRequestFactory httpRequestFactoryProxy) {
        return new RestTemplate(httpRequestFactoryProxy);
    }

    @Bean
    public ClientHttpRequestFactory httpRequestFactoryProxy() {
        return new HttpComponentsClientHttpRequestFactory(httpClientProxy());
    }

    @Bean
    public HttpClient httpClientProxy() {
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", SSLConnectionSocketFactory.getSocketFactory()).build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        //设置整个连接池最大连接数
        connectionManager.setMaxTotal(400);

        //路由是对maxTotal的细分
        connectionManager.setDefaultMaxPerRoute(100);
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(3000000)  //返回数据的超时时间
                .setConnectTimeout(3000000) //连接上服务器的超时时间
                .setConnectionRequestTimeout(1000) //从连接池中获取连接的超时时间
                .build();
        HttpHost proxy = new HttpHost(proxyUrl, proxyPort);
        return HttpClientBuilder.create().setDefaultRequestConfig(requestConfig).setConnectionManager(connectionManager)
                .setProxy(proxy).build();
    }
}
