package com.yirendai.voiceaiserver.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = "com.yirendai.voiceaiserver.mapper.owmuc", sqlSessionFactoryRef = "OwmucSqlSessionFactory")
public class OwmucDataSourceConfig {

    @Bean(name = "OwmucDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.owmuc")
    public DataSource getDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "OwmucSqlSessionFactory")
    public SqlSessionFactory getOwmucSqlSessionFactory(@Qualifier("OwmucDataSource") DataSource dataSource,
                                                       @Qualifier("mybatisPlusPageInterceptor") MybatisPlusInterceptor mybatisPlusInterceptor) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:owmuc/mapper/*.xml"));
        bean.setPlugins(new Interceptor[]{mybatisPlusInterceptor});
        return bean.getObject();
    }

    @Bean(name = "OwmucSqlSessionTemplate")
    public SqlSessionTemplate getOwmucSqlSessionTemplate(@Qualifier("OwmucSqlSessionFactory") SqlSessionFactory sessionFactory) {
        return new SqlSessionTemplate(sessionFactory);
    }
}
