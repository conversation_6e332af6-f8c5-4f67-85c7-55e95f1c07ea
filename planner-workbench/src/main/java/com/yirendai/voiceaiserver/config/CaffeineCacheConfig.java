package com.yirendai.voiceaiserver.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.yirendai.robot.modules.session.service.chat.entity.DialogSession;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
public class CaffeineCacheConfig {

    @Bean
    public Cache<String, List<AiTagCategoryWithTags>> aiTagCategoryCache() {
        return Caffeine.newBuilder()
                .expireAfterWrite(60, TimeUnit.MINUTES)
                .maximumSize(10)
                .build();
    }

    @Bean
    public Cache<String, Boolean> webHookErrorCache() {
        return Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                .maximumSize(50)
                .build();
    }

    @Bean
    public Cache<String, DialogSession> aiDialogSessionCache() {
        return Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                .maximumSize(10)
                .build();
    }
}

