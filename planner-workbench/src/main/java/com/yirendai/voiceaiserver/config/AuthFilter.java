package com.yirendai.voiceaiserver.config;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.directwebremoting.util.DelegatingServletInputStream;
import org.springblade.core.secure.utils.AuthUtil;

import javax.servlet.*;
import javax.servlet.FilterConfig;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;

public class AuthFilter implements Filter {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {}

    @Override
    public void destroy() {}

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (request instanceof HttpServletRequest) {
            // 包装原始请求
            HttpServletRequest httpRequest = (HttpServletRequest) request;

            // 处理 Content-Type 为 application/json 的请求
            if ("application/json".equalsIgnoreCase(httpRequest.getContentType())) {
                // 包装原始请求
                JsonHttpServletRequestWrapper wrappedRequest = new JsonHttpServletRequestWrapper(httpRequest);
                // 继续过滤链，使用包装后的请求对象
                chain.doFilter(wrappedRequest, response);
            } else {
                CustomHttpServletRequestWrapper wrappedRequest = new CustomHttpServletRequestWrapper(httpRequest);
                String account = AuthUtil.getUserAccount();
                if (StrUtil.isNotBlank(account)) {
                    wrappedRequest.addParameter("account", account);
                }
                chain.doFilter(wrappedRequest, response);
            }
        } else {
            chain.doFilter(request, response);
        }
    }

    private static class CustomHttpServletRequestWrapper extends HttpServletRequestWrapper {

        private final Map<String, String[]> additionalParams;

        public CustomHttpServletRequestWrapper(HttpServletRequest request) {
            super(request);
            this.additionalParams = new HashMap<>();
        }

        public void addParameter(String name, String value) {
            additionalParams.put(name, new String[]{value});
        }

        @Override
        public String getParameter(String name) {
            // 优先从新增参数中获取值，如果没有则从原始请求中获取
            if (additionalParams.containsKey(name)) {
                return additionalParams.get(name)[0];
            }
            return super.getParameter(name);
        }

        @Override
        public Map<String, String[]> getParameterMap() {
            Map<String, String[]> paramMap = new HashMap<>(super.getParameterMap());
            paramMap.putAll(additionalParams);
            return Collections.unmodifiableMap(paramMap);
        }

        @Override
        public Enumeration<String> getParameterNames() {
            Set<String> paramNames = new HashSet<>(super.getParameterMap().keySet());
            paramNames.addAll(additionalParams.keySet());
            return Collections.enumeration(paramNames);
        }

        @Override
        public String[] getParameterValues(String name) {
            if (additionalParams.containsKey(name)) {
                return additionalParams.get(name);
            }
            return super.getParameterValues(name);
        }
    }

    private class JsonHttpServletRequestWrapper extends HttpServletRequestWrapper {

        private final String modifiedBody;

        public JsonHttpServletRequestWrapper(HttpServletRequest request) throws IOException {
            super(request);

            // 读取原始JSON数据
            StringBuilder sb = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
            }

            // 解析JSON并进行修改
            Map<String, Object> jsonMap = objectMapper.readValue(sb.toString(), HashMap.class);

            // 在这里添加或修改参数
            String account = AuthUtil.getUserAccount();
            if (StrUtil.isNotBlank(account)) {
                jsonMap.put("account", account);
            }

            // 将修改后的JSON转换回字符串
            modifiedBody = objectMapper.writeValueAsString(jsonMap);
        }

        @Override
        public ServletInputStream getInputStream() throws IOException {
            return new DelegatingServletInputStream(new ByteArrayInputStream(modifiedBody.getBytes()));
        }

        @Override
        public BufferedReader getReader() throws IOException {
            return new BufferedReader(new InputStreamReader(this.getInputStream()));
        }
    }
}
