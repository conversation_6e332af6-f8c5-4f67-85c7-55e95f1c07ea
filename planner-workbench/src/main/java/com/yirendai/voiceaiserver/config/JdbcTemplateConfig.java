package com.yirendai.voiceaiserver.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * <AUTHOR>
 * @time 2024/6/18 13:52
 **/
@Configuration
public class JdbcTemplateConfig {

    @Value("${blade.datasource.voice.username}")
    private String voiceUserName;
    @Value("${blade.datasource.voice.password}")
    private String voicePassword;
    @Value("${blade.datasource.voice.url}")
    private String voiceUrl;

    @Bean
    public JdbcTemplate voiceJbcTemplate() {
        String driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        return new JdbcTemplate(
                DataSourceBuilder.create().driverClassName(driver).username(voiceUserName).password(voicePassword).url(voiceUrl)
                        .build());
    }
}
