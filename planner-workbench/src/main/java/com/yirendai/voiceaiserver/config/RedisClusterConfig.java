package com.yirendai.voiceaiserver.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

import java.util.HashSet;
import java.util.Set;

/**
 * Redis集群配置
 */
@Slf4j
@Configuration
public class RedisClusterConfig {

    @Autowired
    protected RedisClusterProperties properties;

    @Bean
    public RedissonClient createRedissonClient() {
        Config config = new Config();
        String[] nodeUrlArr = properties.getNodes().split(",");
        for (int i = 0; i < nodeUrlArr.length; i++) {
            nodeUrlArr[i] = "redis://" + nodeUrlArr[i];
        }
        config.useClusterServers()
                .addNodeAddress(nodeUrlArr)
                .setPassword(properties.getPassword());
        RedissonClient redissonClient = Redisson.create(config);
        return redissonClient;
    }

    @Bean
    @ConditionalOnMissingBean(JedisCluster.class)
    public JedisCluster jedisCluster() {
        // 封装集群节点配置
        Set<HostAndPort> jedisClusterNode = new HashSet<>();
        String[] nodeUrlArr = properties.getNodes().split(",");
        for (String nodeUrl : nodeUrlArr) {
            String[] nodeConfigArr = nodeUrl.split(":");
            jedisClusterNode.add(new HostAndPort(nodeConfigArr[0].trim(), Integer.parseInt(nodeConfigArr[1].trim())));
        }
        // 封装连接池配置
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxIdle(properties.getMaxIdle());
        poolConfig.setMinIdle(properties.getMinIdle());
        poolConfig.setMaxTotal(properties.getMaxTotal());
        poolConfig.setMaxWaitMillis(properties.getMaxWaitMillis());
        poolConfig.setMinEvictableIdleTimeMillis(properties.getMinEvictableIdleTimeMillis());
        poolConfig.setTimeBetweenEvictionRunsMillis(properties.getTimeBetweenEvictionRunsMillis());
        poolConfig.setBlockWhenExhausted(properties.isBlockWhenExhausted());
        poolConfig.setNumTestsPerEvictionRun(properties.getNumTestsPerEvictionRun());
        poolConfig.setTestWhileIdle(properties.isTestWhileIdle());

        // 初始化对象
        return new JedisCluster(jedisClusterNode, properties.getConnectionTimeout(), properties.getSoTimeout(),
            properties.getMaxAttempts(), properties.getPassword(), poolConfig);
    }

}
