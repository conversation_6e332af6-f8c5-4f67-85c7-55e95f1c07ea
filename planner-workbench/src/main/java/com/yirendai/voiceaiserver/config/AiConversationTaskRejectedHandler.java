package com.yirendai.voiceaiserver.config;

import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.task.AiRetryableTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Component
public class AiConversationTaskRejectedHandler implements RejectedExecutionHandler {

    // 负责将任务持久化的服务
    @Resource
    private AiConversationProcessTaskService aiConversationProcessTaskService;

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        if (r instanceof AiRetryableTask) {
            log.warn("Ai-Conversation-Process-Task {} rejected from {}", r.toString(), executor.toString());
            AiRetryableTask task = (AiRetryableTask) r;
            aiConversationProcessTaskService.saveTask(task, AiConversationProcessStatusEnum.FAILED, "Ai-Conversation-Process-Task rejected from " + executor.toString());
        } else {
            // 其他类型的任务处理
            throw new RejectedExecutionException("Ai-Conversation-Process-Task " + r.toString() + " rejected from " + executor.toString());
        }
    }
}

