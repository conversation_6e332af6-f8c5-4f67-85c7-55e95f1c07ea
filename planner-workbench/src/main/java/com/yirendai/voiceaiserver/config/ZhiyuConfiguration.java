package com.yirendai.voiceaiserver.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 智语配置文件
 */
@Configuration
@RefreshScope
@ConfigurationProperties("zhiyu")
@Getter
@Setter
public class ZhiyuConfiguration {

	/**
	 * AI CS  基础 URL
	 */
	private String aiCsUrl ;

	/**
	 * API 密钥
	 */
	private String aiCsKey;

	/**
	 * 财联社知识库 基础 URL
	 */
	private String clsUrl ;

	/**
	 * 财联社知识库 密钥
	 */
	private String clsKey;

	/**
	 * 财联社知识库 dataset
	 */
	private String clsDataset;


	/**
	 * API 密钥  （实时转语音场景）
	 */
	private String aiCsKeyTts;

	/**
	 * 实时转语音url
	 */
	private String ttsOnlineUrl;

	/**
	 * 实时转语音key
	 */
	private String ttsOnlineKey;


}
