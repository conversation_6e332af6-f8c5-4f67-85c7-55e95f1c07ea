package com.yirendai.voiceaiserver.config;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolationException;

@RestControllerAdvice(basePackages = {"com.yirendai.voiceaiserver.controller"})
@Slf4j
public class ControllerAdvice {

    @ExceptionHandler(AiServerException.class)
    public R aiServerExceptionHandler(AiServerException cve) {
        log.error("AiServerException ", cve);
        return R.fail(cve.getCode(), cve.getMessage());
    }
    @ExceptionHandler(ConstraintViolationException.class)
    public R constraintViolationExceptionHandler(ConstraintViolationException cve) {
        log.error("constraintViolationException ", cve);
        return R.fail(ResultCode.PARAM_INVALID.getCode(), ResultCode.PARAM_INVALID.getMessage());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException msrpe) {
        log.error("missingServletRequestParameterException ", msrpe);
        return R.fail(ResultCode.PARAM_INVALID.getCode(), ResultCode.PARAM_INVALID.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException mnve) {

        log.error("methodArgumentNotValidException ", mnve);
        return R.fail(ResultCode.FAILURE.getCode(), mnve.getBindingResult().getFieldError().getDefaultMessage());
    }
    @ExceptionHandler(BindException.class)
    public R methodArgumentNotValidExceptionHandler(BindException mnve) {
        log.error("BindException ", mnve);
        return R.fail(ResultCode.FAILURE.getCode(), mnve.getBindingResult().getFieldError().getDefaultMessage());
    }

    @ExceptionHandler(Exception.class)
    public R exceptionHandler(Exception ex) {
        log.error("Exception ", ex);
        return R.fail(ResultCode.FAILURE);
    }
}
