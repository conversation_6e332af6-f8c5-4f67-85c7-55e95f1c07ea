package com.yirendai.voiceaiserver.common;

/**
 * 结果码
 * <AUTHOR>
 * @time 2024/1/2 17:49
 **/
public enum ResultCode implements IResultCode {
    SUCCESS(200, "操作成功"),
    FAILURE(400, "业务异常"),
    PARAM_INVALID(1000, "参数不合法"),
    HANDING(1001, "正在处理中，请稍后再试"),
    NO_EDIT(1002, "不可编辑"),
    NO_CUSTOMER(1003, "无客户信息或投资信息，无法生成沟通话术"),
    NO_CONFIG(1004, "未配置新闻或活动信息，无法生成沟通话术"),
    STATUS_ERROR(1005, "存在审核中或审核通过/拒绝的录音，暂不支持录音"),
    NO_LOGIN(1006, "用户未登录"),
    NO_EXIST(1007, "数据不存在"),
    VOICE_REGISTER_FAIL(1008, "声纹注册失败"),
    VOICE_DENOICE_FAIL(1009, "降噪失败"),
    VOICE_SEARCH_FAIL(1010, "声纹检测失败"),
    EXIST(1011, "数据已存在"),
    NO_AI_PROMPT_CONFIG(1012, "无AI提示配置信息或不完整"),
    AI_REPLY_PARSE_ERROR(1013, "AI回复结果无法被解析"),
    CHAT_CONTENT_EMPTY(1014, "对话信息为空"),
    DATA_OVER_LIMIT(1015, "数据量超过单次限制"),
    DB_DATA_ERROR(1016, "数据库存在异常数据"),
    AI_RESULT_ERROR(1017, "AI调用结果异常"),
    NO_AI_TAG_CATEGORY(1018, "无AI标签分类信息"),
    SERIALIZE_ERROR(1019, "序列化失败"),
    SHARDING_ERROR(1020, "分片处理失败"),
    NO_USER_TAG(1021, "未找到该用户标签"),
    NO_CONVERSATION_SUMMARY(1022, "对话小结不存在"),
    CONVERSATION_SUMMARY_HANDING(1023, "对话小结处理中"),
    CONVERSATION_SUMMARY_FAIL(1024, "对话小结处理失败"),
    CONVERSATION_ASR_WAITING(1025, "对话音转文处理中"),
    INVALID_CALL(1026, "无效通话"),
    WECHAT_MESSAGE_SUMMARY_ERROR(1027, "企微消息小结任务入库异常"),
    NO_AI_CONVERSATION_WECHAT_SUMMARY(1028, "无AI企微对话小结信息"),
    USER_NOT_EXIST(1029, "用户不存在"),
    USER_NO_QW(1030, "当前客户暂未添加企微，请引导顾客添加。"),
    NO_PERMISSION(1031, "当前操作无权限"),
    CHANNEL_UNSUPPORTED_QUERY_CUSTOMER_INFO(1032, "当前渠道暂不支持查询客户信息"),
    FILE_PARSE_ERROR(1033, "文件解析异常"),
    AI_REPLY_INVALID_JSON(1034, "AI回复结果为无效JSON"),
    NO_AI_CONVERSATION_SOURCE(1035, "无AI对话来源信息"),
    CHAT_PROCESS_CONFIG_ERROR(1036, "对话处理过程配置错误"),
    INTENTION_NOT_FOUND(1037, "机器人意向配置不存在"),
    ROBOT_SUMMARY_QUERY_ERROR(1038, "机器人小结获取异常"),
    INIT_DATA_ERROR(1039, "初始化数据失败"),
    DIALOG_CONTENT_ANALYSIS_SOURCE_EMPTY(1040, "对话任务内容分析数据源为空"),
    DIALOG_CONTENT_ANALYSIS_SCENE_NOT_EXIST(1041, "对话任务内容分析场景不存在"),
    DIALOG_CONTENT_ANALYSIS_RESULT_EMPTY(1042, "对话任务内容分析结果为空"),
    WORK_ORDER_ANALYSIS_SOURCE_EMPTY(1043, "工单数据分析数据源为空"),
    WORK_ORDER_ANALYSIS_RESULT_EMPTY(1044, "工单数据分析结果为空"),
    CHANNEL_UNSUPPORTED_QUERY_QW_INFO(1045, "当前渠道暂不支持查询企微信息"),
    AI_INTENT_NOT_FOUND(1046, "未找到主意图识别结果"),
    AI_INTENT_NOT_MATCH(1047, "主意图识别结果不匹配"),
    ;

    final int code;

    final String message;

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    private ResultCode(final int code, final String message) {
        this.code = code;
        this.message = message;
    }
}
