package com.yirendai.voiceaiserver.biz.asr;

import com.yirendai.voiceaiserver.enums.MsgSendDirectionEnum;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ws.ASRWebsocketRecognizer;
import com.yirendai.workbench.entity.AiPlannerChatContactSub;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class MP3ChannelSplitter {

    // 核心分离方法
    public void splitChannels(File mp3File, ASRWebsocketRecognizer rcgLeft, ASRWebsocketRecognizer rcgRight, String chunkSize, int chunkInterval)
            throws UnsupportedAudioFileException, IOException {

        // 1. 读取MP3文件
        AudioInputStream mp3Stream = AudioSystem.getAudioInputStream(mp3File);
        AudioFormat sourceFormat = mp3Stream.getFormat();

        // 2. 验证音频格式
        if (sourceFormat.getChannels() != 2) {
            throw new UnsupportedAudioFileException("非立体声音频");
        }

        // 3. 转换为PCM格式
        AudioFormat pcmFormat = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                sourceFormat.getSampleRate(),
                16,                    // 16-bit
                sourceFormat.getChannels(),
                sourceFormat.getChannels() * 2, // 每样本字节数（2字节/通道）
                sourceFormat.getSampleRate(),
                false                  // 小端序
        );

        AudioInputStream pcmStream = AudioSystem.getAudioInputStream(pcmFormat, mp3Stream);

        // 5. 处理音频数据
        byte[] frameBuffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = pcmStream.read(frameBuffer)) != -1) {
            try {
                processFrame(frameBuffer, bytesRead, rcgLeft, rcgRight, chunkSize, chunkInterval);
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                log.info("InterruptedException: " + e.getMessage());
            }
        }

        // 7. 清理资源
        pcmStream.close();
        mp3Stream.close();
    }

    // 处理单个音频帧
    private void processFrame(byte[] frame, int length, ASRWebsocketRecognizer rcgLeft, ASRWebsocketRecognizer rcgRight, String chunkSize, int chunkInterval) throws InterruptedException, IOException {
        // 解析 chunk_size
        String[] chunkSizeParts = chunkSize.split(",");
        int encoderChunkSize = Integer.parseInt(chunkSizeParts[0]);
        int decoderChunkSize = Integer.parseInt(chunkSizeParts[1]);
        int encoderBlockSize = Integer.parseInt(chunkSizeParts[2]);

        // 计算每个块的字节数
        int sampleRate = 16000; // 假设采样率为 16000 Hz
        int bytesPerSample = 2; // 16-bit PCM，每样本 2 字节
        int bytesPerChannel = sampleRate * bytesPerSample / 1000 * encoderChunkSize; // 每个块的字节数

        // 用于累积左声道和右声道的字节
        ByteArrayOutputStream leftBuffer = new ByteArrayOutputStream();
        ByteArrayOutputStream rightBuffer = new ByteArrayOutputStream();

        for (int i = 0; i < length; i += 4) { // 立体声16bit每帧4字节
            if (i + 3 >= length) break; // 防止越界

            // 提取左声道（前2字节）
            byte[] leftChunk = new byte[]{frame[i], frame[i + 1]};
            // 提取右声道（后2字节）
            byte[] rightChunk = new byte[]{frame[i + 2], frame[i + 3]};

            // 累积左声道和右声道的字节
            leftBuffer.write(leftChunk);
            rightBuffer.write(rightChunk);
        }

        // 获取累积的左声道和右声道的字节
        byte[] leftBytes = leftBuffer.toByteArray();
        byte[] rightBytes = rightBuffer.toByteArray();

        // 如果累积的字节数达到 chunk_size，则发送
        if (leftBytes.length >= bytesPerChannel) {
            rcgLeft.feedChunk(Arrays.copyOf(leftBytes, bytesPerChannel));
            leftBuffer.reset(); // 重置缓冲区
        }
        if (rightBytes.length >= bytesPerChannel) {
            rcgRight.feedChunk(Arrays.copyOf(rightBytes, bytesPerChannel));
            rightBuffer.reset(); // 重置缓冲区
        }
    }

    public static void main(String[] args) {
        try {
            //ttsOnlineUrl: ws://aigc-api.aigc.paas.corp/v1/ws/audio/translations
            //  #  实时转语音key
            //  ttsOnlineKey: sk-itQdczJ3KcYNsn4OaO8F6M2MTfb887nGYay8E6oZjOSSAz9Q
            MP3ChannelSplitter splitter = new MP3ChannelSplitter();
            Integer leftDirection = MsgSendDirectionEnum.FROM_CRM.getCode();
            Integer rightDirection = MsgSendDirectionEnum.FROM_CUS.getCode();
            String url = "ws://aigc-api.aigc.paas.corp/v1/ws/audio/translations";
            String key = "sk-itQdczJ3KcYNsn4OaO8F6M2MTfb887nGYay8E6oZjOSSAz9Q";
            // 假设 rcgLeft 和 rcgRight 已经正确初始化
            List<AiPlannerChatContactSub> synchronizedList = Collections.synchronizedList(new ArrayList<AiPlannerChatContactSub>());
            ASRWebsocketRecognizer rcgLeft = new ASRWebsocketRecognizer(url, key, "example-left.wav", leftDirection, null, synchronizedList, "张三", "李四");
            ASRWebsocketRecognizer rcgRight = new ASRWebsocketRecognizer(url, key, "example-right.wav", rightDirection, null, synchronizedList, "张三", "李四");

            splitter.splitChannels(
                    new File("/Users/<USER>/2025年/1月/1/yrcall_1869687765910835200.mp3"),
                    rcgLeft,
                    rcgRight,
                    "5,10,5", // chunk_size
                    10        // chunk_interval
            );
            System.out.println("声道分离并实时音转文完成");
            Thread.sleep(10000L);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}