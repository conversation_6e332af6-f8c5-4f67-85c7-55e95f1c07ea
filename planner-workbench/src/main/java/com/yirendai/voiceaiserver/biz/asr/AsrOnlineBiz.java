package com.yirendai.voiceaiserver.biz.asr;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.config.ZhiyuConfiguration;
import com.yirendai.voiceaiserver.enums.MsgSendDirectionEnum;
import com.yirendai.voiceaiserver.enums.YesOrNoEnum;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ws.ASRWebsocketRecognizer;
import com.yirendai.voiceaiserver.vo.response.AsrOnlineVO;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.AiPlannerChatContactSub;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.mapper.AiPlannerChatContactSubMapper;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.jetbrains.annotations.NotNull;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.sound.sampled.*;
import java.io.File;
import java.io.IOException;
import java.io.SequenceInputStream;
import java.nio.ShortBuffer;
import java.util.*;

/**
 * 在线音转文
 */
@Component
@Slf4j
public class AsrOnlineBiz {

    @Resource
    CallcenterCallRecordService callcenterCallRecordService;

    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;

    @Autowired
    @Qualifier("asrOnlineThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    AiPlannerChatContactSubMapper aiPlannerChatContactSubMapper;
    @Autowired
    private ZhiyuConfiguration zhiyuConfiguration;


    int chunkSize = 1920;


    @Value("${zhiyu.mp3Interval:3000}")
    private Long mp3Interval;
    @Autowired
    private RealtimeMp3SplitToFunASR realtimeMp3SplitToFunASR;

    @Autowired
    private MP3ChannelSplitter mp3ChannelSplitter;

    /**
     * 通话实时音转文
     * @param uuid
     * @param ringTime
     * @return
     */
    public SseEmitter asrOnline(String uuid, Long ringTime) {
        SseEmitter sseEmitter = new SseEmitter(60 * 1000 * 60 * 6L);
        threadPoolTaskExecutor.execute(() -> {
            translate(sseEmitter, uuid, ringTime);
        });
        return sseEmitter;
    }

    /**
     * 发送异常信息
     * @param sseEmitter
     * @param msg
     */
    public void seeSendError(SseEmitter sseEmitter, String msg) {
        AsrOnlineVO asrOnlineVO = new AsrOnlineVO();
        asrOnlineVO.setStatus(YesOrNoEnum.NO.getKey());
        asrOnlineVO.setMsg(msg);
        try {
            sseEmitter.send(asrOnlineVO);
            sseEmitter.complete();
        } catch (IOException e) {
            log.error("seeSendError {} {}", e.getMessage(), msg, e);
        }

    }

    /**
     * 音转文
     * @param sseEmitter
     * @param uuid
     * @param ringTime
     */
    public void translate(SseEmitter sseEmitter, String uuid, Long ringTime) {
        ASRWebsocketRecognizer rcgLeft = null;
        ASRWebsocketRecognizer rcgRight = null;
        List<AiPlannerChatContactSub> synchronizedList = Collections.synchronizedList(new ArrayList<AiPlannerChatContactSub>());
        AiPlannerChatContact chatContact = null;
        File file = null;
        try {
            log.info("查询新呼叫中心消息id={}开始", uuid);
            LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CallcenterCallRecord::getUuid, uuid).eq(CallcenterCallRecord::getSystemType, 2);
            CallcenterCallRecord record = callcenterCallRecordService.getOne(wrapper);
            if (Objects.isNull(record) || StrUtil.isBlank(record.getCallPlannerId()) || StrUtil.isBlank(record.getCustomerUid()) || StrUtil.isBlank(record.getPhone())) {
                log.info("当前消息uuid={}部分数据缺失，无法同步消息", uuid);
                seeSendError(sseEmitter, "当前消息部分数据缺失，无法同步");
                return;
            }
            LambdaQueryWrapper<AiPlannerChatContact> chatContactWrapper = new LambdaQueryWrapper<>();
            chatContactWrapper.eq(AiPlannerChatContact::getBusId, record.getUuid());
            chatContact = iAiPlannerChatContactService.getOne(chatContactWrapper);
            if (Objects.isNull(chatContact)) {
                log.info("当前消息uuid={}未查询到对话记录", uuid);
                seeSendError(sseEmitter, "当前消息未查询到对话记录");
                return;
            }
            log.info("查询新呼叫中心消息id={}结束", uuid);
            file = new File(chatContact.getNasPath());

            if (!file.exists()) {
                log.info("未获取到音频文件uuid={}", uuid);
                seeSendError(sseEmitter, "未获取到音频文件");
                return;
            }
            /**
             * 员工姓名
             */
            String userName =record.getAgentName();
            /**
             * 客户姓名
             */
            String customerName = record.getCustomerName();

            Integer leftDirection = MsgSendDirectionEnum.FROM_CRM.getCode();
            Integer rightDirection = MsgSendDirectionEnum.FROM_CUS.getCode();
            if (MsgSendDirectionEnum.FROM_CUS.getCode().equals(chatContact.getDirection())) {
                leftDirection = MsgSendDirectionEnum.FROM_CUS.getCode();
                rightDirection = MsgSendDirectionEnum.FROM_CRM.getCode();
            }
            String url = zhiyuConfiguration.getTtsOnlineUrl();
            String key = zhiyuConfiguration.getTtsOnlineKey();
            rcgLeft = new ASRWebsocketRecognizer(url,key,"example-left" + uuid + ".mp3", leftDirection, sseEmitter, synchronizedList, userName, customerName);
            rcgRight = new ASRWebsocketRecognizer(url,key,"example-right" + uuid + ".mp3", rightDirection, sseEmitter, synchronizedList,userName, customerName);
            //realtimeMp3SplitToFunASR.watchFileChanges(chatContact.getNasPath(),rcgLeft,rcgRight);
            realtimeMp3SplitToFunASR.readAndProcessFile(chatContact.getNasPath(),rcgLeft,rcgRight);
            Thread.sleep(100000L);
        } catch (Exception e) {
            log.error("同步新呼叫中心消息id={}发生异常，异常原因为{}", uuid, e.getMessage(), e);
            seeSendError(sseEmitter, "同步新呼叫中心消息异常");
        } finally {
//            try {
//                if (Objects.nonNull(rcgLeft)) {
//                    rcgLeft.close(8000);
//                }
//                if (Objects.nonNull(rcgRight)) {
//                    rcgRight.close(8000);
//                }
//                if (CollectionUtil.isNotEmpty(synchronizedList)) {
//                    for (AiPlannerChatContactSub item : synchronizedList) {
//                        if (chatContact != null) {
//                            item.setChatContactId(chatContact.getId());
//                        }
//                        aiPlannerChatContactSubMapper.insert(item);
//                    }
//                }
//                sseEmitter.complete();
//            } catch (Exception e) {
//                log.error("关闭websocket发生异常，异常原因为", e);
//            }

        }

    }

    private AsrBytes handleTranslate(File file, ASRWebsocketRecognizer rcgRight, ASRWebsocketRecognizer rcgLeft, String uuid, int fileNo, AsrBytes asrBytes) {
//        File wavFile = new File(file.getAbsolutePath() + ".wav");
//        try {
//            FileUtil.convertAmrToWav(file, wavFile);
//        } catch (Exception e) {
//            log.error("转换文件为wav格式发生异常，异常原因为", e);
//            throw new AiServerException(ResultCode.FAILURE.getCode(), "转换文件为wav格式发生异常");
//        }
//        file = wavFile;
        String leftFileName = getLeftFileName(file, uuid, fileNo);
        String rightFileName = getRightFileName(file, uuid, fileNo);
        String leftChannelOutputFile = file.getParent() + "/left-" + uuid + "_" + fileNo + ".wav";
        String rightChannelOutputFile = file.getParent() + "/right-" + uuid + "_" + fileNo + ".wav";
        this.splitAudioByChannel(file.getAbsolutePath(), leftChannelOutputFile, rightChannelOutputFile);
        File left = new File(leftChannelOutputFile);
        File right = new File(rightChannelOutputFile);

        log.info("uuid={}开始合并音频文件100001 == fileNo", uuid);

        String leftFileName1 = getLeftFileName(file, uuid, (fileNo - 1));
        String rightFileName1 = getRightFileName(file, uuid, (fileNo - 1));
        File leftFile1 = new File(leftFileName1);
        File rightFile1 = new File(rightFileName1);
        if (100001 == fileNo) {
            audioMerge(leftFileName, leftChannelOutputFile);
            audioMerge(rightFileName, rightChannelOutputFile);
        }
        else {
            log.info("uuid={}开始合并音频文件", uuid);
            audioMerge(leftFileName,leftFileName1, leftChannelOutputFile);
            audioMerge(rightFileName,rightFileName1, rightChannelOutputFile);
        }

        try {
            try {
                if (asrBytes == null) {
                    asrBytes = new AsrBytes();
                }
                byte[] leftFile = ASRWebsocketRecognizer.readWavFile(leftFileName,asrBytes,0);
                byte[] rightFile = ASRWebsocketRecognizer.readWavFile(rightFileName,asrBytes,1);

                byte[] beforeLeftBytes = feedChunk(leftFile, rcgLeft, asrBytes.getLeftFile(), false);
                byte[] beforeRightBytes = feedChunk(rightFile, rcgRight, asrBytes.getRightFile(), false);
                if (beforeLeftBytes != null && beforeLeftBytes.length > 0) {
                    asrBytes.setLeftFile(Arrays.copyOf(beforeLeftBytes, beforeLeftBytes.length));
                }
                if (beforeRightBytes != null && beforeRightBytes.length > 0) {
                    asrBytes.setRightFile(Arrays.copyOf(beforeRightBytes, beforeRightBytes.length));
                }

            } catch (IOException e) {
                throw new RuntimeException(e);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } finally {
            left.delete();
            right.delete();
            if (Objects.nonNull(leftFile1)) {
                leftFile1.delete();
            }
            if (Objects.nonNull(rightFile1)) {
                rightFile1.delete();
            }
        }
        return asrBytes;
    }

    @NotNull
    private static String getRightFileName(File file, String uuid, int fileNo) {
        String rightFileName = file.getParent() + "/right-all-" + uuid + "_" + fileNo + ".wav";
        return rightFileName;
    }

    @NotNull
    private static String getLeftFileName(File file, String uuid, int fileNo) {
        String leftFileName = file.getParent() + "/left-all-" + uuid + "_" + fileNo + ".wav";
        return leftFileName;
    }

    /**
     * @param send 是否强制发送，否则的话大小等于chunkSize才发送
     */
    public byte[] feedChunk(byte[] audioBytes, ASRWebsocketRecognizer rcg, byte[] beforeBytes, boolean send) throws Exception {
        if (beforeBytes != null && beforeBytes.length > 0) {
            byte[] newBytes = new byte[ beforeBytes.length + audioBytes.length ];
            System.arraycopy(beforeBytes, 0, newBytes, 0, beforeBytes.length);
            System.arraycopy(audioBytes, 0, newBytes, beforeBytes.length, audioBytes.length);
            audioBytes = newBytes;
            log.info("feedChunk 加上一次没有发送的长度 {}  ", beforeBytes.length);
        }
//        int chunkSize = 320;
        for (int i = 0; i < audioBytes.length; i += chunkSize) {
            int end = Math.min(i + chunkSize, audioBytes.length);
            byte[] chunk = Arrays.copyOfRange(audioBytes, i, end);
            if (chunk.length == chunkSize) {
                rcg.feedChunk(chunk );
            }
            else {
                if (send) {
                    rcg.feedChunk(chunk );
                }
                else {
                    log.info("feedChunk 长度不够暂不发送 {}", chunk.length);
                    return chunk;
                }
            }

        }
        return null;

    }

    /**
     * 按照切割信息切割文件
     * @param inputAudioPath 原文件路径
     * @param outputAudioPath 输出文件路径
     * @param startTime 开始时间，微秒
     * @param endTime 结束时间，微秒
     * @return 截取到微秒
     */
    public long splitAndConcatenateAudio(String inputAudioPath, String outputAudioPath, Long startTime, Long endTime) throws IOException {
        //
        long timestamp = 0l;
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputAudioPath)) {
            grabber.start();
            try (FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputAudioPath, grabber.getAudioChannels())) {
                recorder.setSampleRate(grabber.getSampleRate());
                recorder.setAudioCodec(grabber.getAudioCodec());
                recorder.start();

                Frame frame;
                if (startTime >= grabber.getLengthInTime()) {
                    return timestamp;
                }
                grabber.setTimestamp(startTime); // 跳过开始的几微秒
                while ((frame = grabber.grabFrame()) != null) {
                    //log.info("按照切割信息切割文件 startTime={} endTime={} grabberTimestamp={}", startTime, endTime, grabber.getTimestamp());
                    recorder.record(frame);
                    timestamp= grabber.getTimestamp();
                    if (timestamp >= endTime) {

                        break; // 停止录制
                    }
                }
                recorder.stop();
                recorder.release();
                grabber.stop();
            }
        } catch (Exception e) {
            log.error("splitAndConcatenateAudio error {}", e.getMessage(), e);
            return 0;
        }
        return timestamp;
    }

    /**
     * 根据左右声道拆分音频文件
     * @param inputFile 文件绝对路径
     * @param leftChannelOutputFile 左声道文件路径
     * @param rightChannelOutputFile 右声道文件路径
     */
    public void splitAudioByChannel(String inputFile, String leftChannelOutputFile, String rightChannelOutputFile) {
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile); FFmpegFrameRecorder leftRecorder = new FFmpegFrameRecorder(leftChannelOutputFile, 1); FFmpegFrameRecorder rightRecorder = new FFmpegFrameRecorder(rightChannelOutputFile, 1)) {

            grabber.start();
            leftRecorder.setAudioCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_PCM_S16LE);
            leftRecorder.setFormat("wav");
            leftRecorder.setSampleRate(16000);
            rightRecorder.setAudioCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_PCM_S16LE);
            rightRecorder.setFormat("wav");
            rightRecorder.setSampleRate(16000);
            leftRecorder.start();
            rightRecorder.start();

            Frame frame;
            while ((frame = grabber.grabSamples()) != null) {
                if (frame.samples != null && frame.samples.length > 0) {
                    ShortBuffer buffer = (ShortBuffer) frame.samples[ 0 ];

                    int availableSamples = buffer.remaining();
                    if (availableSamples > 0) {
                        short[] samples = new short[ availableSamples ];
                        buffer.get(samples);

                        short[] leftChannelSamples = new short[ samples.length / 2 ];
                        short[] rightChannelSamples = new short[ samples.length / 2 ];

                        for (int i = 0; i < samples.length / 2; i++) {
                            leftChannelSamples[ i ] = samples[ 2 * i ];
                            rightChannelSamples[ i ] = samples[ 2 * i + 1 ];
                        }

                        ShortBuffer leftBuffer = ShortBuffer.wrap(leftChannelSamples);
                        ShortBuffer rightBuffer = ShortBuffer.wrap(rightChannelSamples);

                        leftRecorder.recordSamples(grabber.getSampleRate(), 1, leftBuffer);
                        rightRecorder.recordSamples(grabber.getSampleRate(), 1, rightBuffer);
                    }
                }
            }

            grabber.stop();
            leftRecorder.stop();
            rightRecorder.stop();

        } catch (Exception e) {
            log.error("音频通道分离失败，文件路径 {}, 失败原因为", inputFile, e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "音频通道分离失败" + e.getMessage());
        }
    }

    public void audioMerge(String outputFile, String... inputFiles) {

        try {
            // 存储所有音频输入流的数组
            AudioInputStream[] audioInputStreams = new AudioInputStream[ inputFiles.length ];
            // 打开所有输入文件并读取音频输入流
            for (int i = 0; i < inputFiles.length; i++) {
                File file = new File(inputFiles[ i ]);
                audioInputStreams[ i ] = AudioSystem.getAudioInputStream(file);
            }

            // 获取第一个音频输入流的音频格式，假设所有输入文件具有相同的格式
            AudioFormat audioFormat = audioInputStreams[ 0 ].getFormat();

            // 创建SequenceInputStream来合并所有音频输入流
            SequenceInputStream sequenceInputStream = new SequenceInputStream(Collections.enumeration(Arrays.asList(audioInputStreams)));

            // 创建一个新的AudioInputStream，具有相同的音频格式和总帧长度（这里需要计算或估计）
            // 注意：这里为了简化，我们直接使用了第一个音频输入流的帧长度作为估计值，
            // 但在实际应用中，你可能需要计算所有音频输入流的总帧长度。
            // 由于我们不知道合并后的确切帧长度，这里传递-1作为帧长度参数，
            // AudioSystem.write()方法会根据输入流的实际内容来确定文件大小。
            AudioInputStream combinedAudioInputStream = new AudioInputStream(sequenceInputStream, audioFormat, -1  // 传递-1表示帧长度未知，将由AudioSystem.write()方法计算
            );

            // 将合并后的音频数据写入到输出文件中
            File outputFileObj = new File(outputFile);
            AudioSystem.write(combinedAudioInputStream, AudioFileFormat.Type.WAVE, outputFileObj);

            // 关闭所有资源
            for (AudioInputStream audioInputStream : audioInputStreams) {
                audioInputStream.close();
            }
            combinedAudioInputStream.close();

            System.out.println("音频文件成功拼接为: " + outputFileObj.getAbsolutePath());

        } catch (UnsupportedAudioFileException | IOException e) {
            e.printStackTrace();
        }


    }
//    public static void accumulateAudioFile(String outputFile,String inputFile) {
//        try {
//            // 读取输入音频文件
//            File inputAudioFile = new File(inputFile);
//            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(inputAudioFile);
//
//                // 如果不是第一次累加，则直接追加音频数据到文件中（跳过头部信息）
//                try (FileOutputStream fos = new FileOutputStream(outputFile, true)) {
//                    byte[] buffer = new byte[1024];
//                    int bytesRead;
//                    while ((bytesRead = audioInputStream.read(buffer)) != -1) {
//                        fos.write(buffer, 0, bytesRead);
//                    }
//                }
//            // 关闭音频输入流
//            audioInputStream.close();
//
//        } catch (UnsupportedAudioFileException | IOException   e) {
//            e.printStackTrace();
//        }
//    }

    public IPage<AiPlannerChatContactSub> asrOnlinelist( AiPlannerChatContactSub contactSub, Query query) {
        AiPlannerChatContact contact = iAiPlannerChatContactService.getById(contactSub.getChatContactId());
        if(contact != null && contact.getProcessedContent() != null){
            IPage<AiPlannerChatContactSub> page = new Page<>();
            try{
                LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(CallcenterCallRecord::getUuid, contact.getBusId()).eq(CallcenterCallRecord::getSystemType, 2);
                CallcenterCallRecord record = callcenterCallRecordService.getOne(wrapper);
                String userName = "";
                String customerName = "";
                if(record != null){
                    userName = record.getAgentName();
                    customerName = record.getCustomerName();
                }


                String[] split = contact.getProcessedContent().split("\n");
                page.setTotal(split.length);
                if(split.length > 0){
                    List<AiPlannerChatContactSub> list = new ArrayList<>();
                    for (int i = 0; i < split.length; i++) {
                        AiPlannerChatContactSub aiPlannerChatContactSub = new AiPlannerChatContactSub();
                        String[] text = split[i].split(":");
                        if(text.length > 1){
                            aiPlannerChatContactSub.setText(text[1]);
                            aiPlannerChatContactSub.setMode("offline");
                            if("客户".equals(text[0])){
                                aiPlannerChatContactSub.setDirection(1);
                                aiPlannerChatContactSub.setUserName(customerName);
                            }else{
                                aiPlannerChatContactSub.setDirection(0);
                                aiPlannerChatContactSub.setUserName(userName);
                            }
                            list.add(aiPlannerChatContactSub);
                        }
                    }
                    page.setRecords(list);
                }
            }catch (Exception e){
                log.error("【语音识别】获取处理结果失败", e);
            }
            return page;
        }else{
            LambdaQueryWrapper<AiPlannerChatContactSub> wrapper =	new LambdaQueryWrapper<>();
            wrapper.eq(AiPlannerChatContactSub::getChatContactId, contactSub.getChatContactId());
            wrapper.orderByAsc(AiPlannerChatContactSub::getCreateTime);
            IPage<AiPlannerChatContactSub> pages = aiPlannerChatContactSubMapper.selectPage( Condition.getPage(query), wrapper);
            return pages;
        }


    }
}
