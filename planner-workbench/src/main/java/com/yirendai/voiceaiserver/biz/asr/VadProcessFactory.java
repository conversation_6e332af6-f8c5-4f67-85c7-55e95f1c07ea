package com.yirendai.voiceaiserver.biz.asr;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.vo.response.AsrOnlineVO;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.mapper.AiPlannerChatContactSubMapper;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
@Component
@RefreshScope
@Slf4j
public class VadProcessFactory {
    @Resource
    private CallcenterCallRecordService callcenterCallRecordService;

    @Resource
    private IAiPlannerChatContactService iAiPlannerChatContactService;

    @Resource
    private AiPlannerChatContactSubMapper aiPlannerChatContactSubMapper;

    @Value("${yr_asr.model.url}")
    private String url = "http://aigc-api.aigc.paas.corp";

    @Value("${yr_asr.model.key}")
    private String key = "sk-itQdczJ3KcYNsn4OaO8F6M2MTfb887nGYay8E6oZjOSSAz9Q";

    @Value("${yr_asr.path:/yrCallVoice/asr/}")
    private String tempDir = "/Users/<USER>/IdeaProjects/fortune_activity_admin/";

    @Value("${yr_asr.audio.rate}")
    private final int AUDIO_RATE = 8000;

    @Value("${yr_asr.timeout.short}")
    private final long TIMEOUT_0_MS = 2200;

    @Value("${yr_asr.timeout.long}")
    private final long TIMEOUT_MS = 10000;

    @Value("${yr_asr.vad.segment}")
    private final double VAD_SEGMENT_DURATION = 0.2;

    @Value("${yr_asr.vad.asr}")
    private final double MIN_ASR_DURATION = 0.4;

    @Value("${yr_asr.model.name}")
    private String model = "funasr";

    @Value("${yr_asr.model.temperature}")
    private float temperature = 0.2f;

    @Value("${yr_asr.max_time}")
    private int maxDurationMs = 3 * 60 * 60 * 1000;

    @Value("${yr_asr.vad.fast_slice_time}")
    private double timeFastSliceAsrTime = 3.6;

    @Value("${yr_asr.vad.fast_slice_multi}")
    private double timeFastSliceAsrMulti = 1.2;

    @Value("${yr_asr.vad.clear_inactive_time}")
    private double timeClearInactiveTime = 5.0;

    @Value("${yr_asr.vad.max_asr_time}")
    private double maxAsrTime = 8.0;

    @Autowired
    @Qualifier("asrOnlineChannelThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private JedisCluster jedisCluster;

    private final String FILE_PREFIX = "yr_asr_";
    private long totalPcmBytesWritten = 0;
    private final ReentrantLock fileLock = new ReentrantLock();

    public VadAsrProcessor createMp3VadAsrProcessor(String uuid) {
        VadAsrProcessor mp3VadAsrProcessor = new VadAsrProcessor();
        mp3VadAsrProcessor.setCallcenterCallRecordService(callcenterCallRecordService);
        mp3VadAsrProcessor.setIAiPlannerChatContactService(iAiPlannerChatContactService);
        mp3VadAsrProcessor.setAiPlannerChatContactSubMapper(aiPlannerChatContactSubMapper);
        mp3VadAsrProcessor.setUrl(url);
        mp3VadAsrProcessor.setKey(key);
        mp3VadAsrProcessor.setTempDir(tempDir);
        mp3VadAsrProcessor.setAUDIO_RATE(AUDIO_RATE);
        mp3VadAsrProcessor.setTIMEOUT_0_MS(TIMEOUT_0_MS);
        mp3VadAsrProcessor.setTIMEOUT_MS(TIMEOUT_MS);
        mp3VadAsrProcessor.setVAD_SEGMENT_DURATION(VAD_SEGMENT_DURATION);
        mp3VadAsrProcessor.setMIN_ASR_DURATION(MIN_ASR_DURATION);
        mp3VadAsrProcessor.setModel(model);
        mp3VadAsrProcessor.setTemperature(temperature);
        mp3VadAsrProcessor.setMaxDurationMs(maxDurationMs);
        mp3VadAsrProcessor.setThreadPoolTaskExecutor(threadPoolTaskExecutor);
        mp3VadAsrProcessor.setTimeFastSliceAsrTime(timeFastSliceAsrTime);
        mp3VadAsrProcessor.setTimeFastSliceAsrMulti(timeFastSliceAsrMulti);
        mp3VadAsrProcessor.setTimeClearInactiveTime(timeClearInactiveTime);
        mp3VadAsrProcessor.setMaxAsrTime(maxAsrTime);

        if (mp3VadAsrProcessor.isDebugTest()) {
            tempDir = "/Users/<USER>/IdeaProjects/fortune_activity_admin/278576/";
        }else {
            LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CallcenterCallRecord::getUuid, uuid).eq(CallcenterCallRecord::getSystemType, 2);
            CallcenterCallRecord record = callcenterCallRecordService.getOne(wrapper);
            if (record == null) {
                log.error("音转文监控查询新呼叫中心消息id={}, type=2未查询到数据", uuid);
                throw new AiServerException(ResultCode.NO_EXIST);
            }
            tempDir = tempDir + record.getTenantId() + "/";
        }

        // Initialize file paths and store in Redis
        String tenantPath = tempDir + uuid + "/";
        try {
            Files.createDirectories(Paths.get(tenantPath));
            log.info("音转文id={}, 目录创建成功: {}", uuid, tenantPath);
        } catch (IOException e) {
            log.error("音转文id={}, 目录创建失败: {}", uuid, tenantPath, e);
            return null;
        }

        String leftFile = tenantPath + FILE_PREFIX + uuid + "_left.wav";
        String rightFile = tenantPath + FILE_PREFIX + uuid + "_right.wav";
        Map<String, String> fileMap = new HashMap<>();
        fileMap.put("left_file", leftFile);
        fileMap.put("right_file", rightFile);
        mp3VadAsrProcessor.setLeftTempMp3File(leftFile);
        mp3VadAsrProcessor.setRightTempMp3File(rightFile);
        jedisCluster.setex("asr:files:" + uuid, maxDurationMs / 1000, JSON.toJSONString(fileMap));
        return mp3VadAsrProcessor;
    }

    public void processPcmAudio(String uuid, int channelType, byte[] pcmData) {
        if (channelType != 0 && channelType != 1) {
            log.error("音转文id={}, 无效的声道类型: {}", uuid, channelType);
            return;
        }

        // 从 Redis 获取文件路径
        String fileKey = "asr:files:" + uuid;
        String s = jedisCluster.get(fileKey);
        Map<String, String> fileMap = JSON.parseObject(s, Map.class);
        if (fileMap == null || fileMap.isEmpty()) {
            log.warn("音转文id={}, 未找到文件路径缓存", uuid);
            return;
        }
        String filePath = channelType == 0 ? fileMap.get("left_file") : fileMap.get("right_file");
        if (filePath == null) {
            log.warn("音转文id={}, 未找到声道 {} 的文件路径", uuid, channelType == 0 ? "左" : "右");
            return;
        }

        // 追加 PCM 数据到 WAV 文件
        try {
            appendToWavFile(filePath, pcmData, uuid);
        } catch (IOException e) {
            log.error("音转文id={}, 追加 PCM 数据到文件 {} 失败", uuid, filePath, e);
        }
    }

    public void removeMp3VadAsrProcessor(String uuid) {
        try {
            // Delete audio files
            String fileKey = "asr:files:" + uuid;
            String s = jedisCluster.get(fileKey);
            Map<String, String> fileMap = JSON.parseObject(s, Map.class);
            if (fileMap == null || fileMap.isEmpty()) {
                log.info("音转文id={}, 删除时未找到文件路径缓存", uuid);
                return;
            }
            String leftFile = fileMap.get("left_file");
            String rightFile = fileMap.get("right_file");

            if (leftFile != null) {
                try {
                    Files.deleteIfExists(Paths.get(leftFile));
                    log.info("音转文id={}, 删除左声道文件: {}", uuid, leftFile);
                } catch (IOException e) {
                    log.error("音转文id={}, 删除左声道文件失败: {}", uuid, leftFile, e);
                }
            }
            if (rightFile != null) {
                try {
                    Files.deleteIfExists(Paths.get(rightFile));
                    log.info("音转文id={}, 删除右声道文件: {}", uuid, rightFile);
                } catch (IOException e) {
                    log.error("音转文id={}, 删除右声道文件失败: {}", uuid, rightFile, e);
                }
            }

            // Clean up Redis
            jedisCluster.del(fileKey);
        } catch (Exception e) {
            log.error("音转文id={}, 删除声道文件失败", uuid, e);
        }
    }

    private void appendToWavFile(String filePath, byte[] pcmData, String uuid) throws IOException {
        File file = new File(filePath);
        fileLock.lock();
        try {
            boolean isNewFile = !file.exists() || file.length() < 44;
            if (isNewFile) {
                Files.createDirectories(file.toPath().getParent());
                Files.createFile(file.toPath());
                totalPcmBytesWritten = 0;
                log.info("音转文id={}, 创建新 WAV 文件: {}", uuid, filePath);
            }

            try (RandomAccessFile raf = new RandomAccessFile(file, "rw")) {
                if (isNewFile) {
                    writeWavHeader(raf);
                } else {
                    raf.seek(40);
                    int subchunk2Size = readLittleEndianInt(raf);
                    long fileSize = file.length();
                    if (subchunk2Size > fileSize - 44 || subchunk2Size < 0) {
                        log.warn("音转文id={}, 无效的 Subchunk2Size: {}, 文件大小: {}, 调整头信息", uuid, subchunk2Size, fileSize);
                        // 调整头信息而非重置
                        raf.seek(4);
                        // 更新 ChunkSize
                        writeLittleEndianInt(raf, (int) (fileSize + pcmData.length - 8));
                        raf.seek(40);
                        // 更新 Subchunk2Size
                        writeLittleEndianInt(raf, (int) (fileSize - 44));
                        // 同步当前写入量
                        totalPcmBytesWritten = (int) (fileSize - 44);
                    } else {
                        totalPcmBytesWritten = subchunk2Size;
                    }
                }

                long seekOffset = 44 + totalPcmBytesWritten;
                if (seekOffset < 0) {
                    log.error("音转文id={}, 无效的 seek 偏移: {}, 调整头信息", uuid, seekOffset);
                    raf.seek(4);
                    // 初始化 ChunkSize
                    writeLittleEndianInt(raf, (int) (44 + pcmData.length - 8));
                    raf.seek(40);
                    // 初始化 Subchunk2Size
                    writeLittleEndianInt(raf, 0);
                    totalPcmBytesWritten = 0;
                    seekOffset = 44;
                }

                raf.seek(seekOffset);
                raf.write(pcmData);
                totalPcmBytesWritten += pcmData.length;

                // 更新 WAV 头
                raf.seek(4);
                writeLittleEndianInt(raf, (int) (totalPcmBytesWritten + 36));
                raf.seek(40);
                writeLittleEndianInt(raf, (int) totalPcmBytesWritten);
                // 强制同步
                raf.getFD().sync();

                // 验证更新
                raf.seek(40);
                int updatedSubchunk2Size = readLittleEndianInt(raf);
                if (updatedSubchunk2Size != totalPcmBytesWritten) {
                    log.warn("音转文id={}, Subchunk2Size 更新失败，预期: {}, 实际: {}, 尝试修复", uuid, totalPcmBytesWritten, updatedSubchunk2Size);
                    raf.seek(40);
                    writeLittleEndianInt(raf, (int) totalPcmBytesWritten);
                    raf.getFD().sync();
                }

                long actualSize = file.length();
                long expectedSize = 44 + totalPcmBytesWritten;
                if (actualSize != expectedSize) {
                    log.warn("音转文id={}, 文件大小异常，预期: {} 字节，实际: {} 字节", uuid, expectedSize, actualSize);
                } else {
                    log.info("音转文id={}, 成功追加 {} 字节，文件大小: {} 字节", uuid, pcmData.length, actualSize);
                }
            }
        } catch (IOException e) {
            log.error("音转文id={}, 追加 PCM 数据到文件 {} 失败", uuid, filePath, e);
            throw e;
        } finally {
            fileLock.unlock();
        }
    }

    private int readLittleEndianInt(RandomAccessFile raf) throws IOException {
        int b1 = raf.read() & 0xFF;
        int b2 = raf.read() & 0xFF;
        int b3 = raf.read() & 0xFF;
        int b4 = raf.read() & 0xFF;
        return (b4 << 24) | (b3 << 16) | (b2 << 8) | b1;
    }

    private void writeLittleEndianInt(RandomAccessFile raf, int value) throws IOException {
        raf.write(value & 0xFF);
        raf.write((value >> 8) & 0xFF);
        raf.write((value >> 16) & 0xFF);
        raf.write((value >> 24) & 0xFF);
    }

    private void writeLittleEndianShort(RandomAccessFile raf, short value) throws IOException {
        raf.write(value & 0xFF);
        raf.write((value >> 8) & 0xFF);
    }

    private void writeWavHeader(RandomAccessFile raf) throws IOException {
        raf.setLength(0);
        raf.writeBytes("RIFF");
        writeLittleEndianInt(raf, 36);
        raf.writeBytes("WAVE");
        raf.writeBytes("fmt ");
        writeLittleEndianInt(raf, 16);
        writeLittleEndianShort(raf, (short) 1);
        writeLittleEndianShort(raf, (short) 1);
        writeLittleEndianInt(raf, (int) AUDIO_RATE);
        writeLittleEndianInt(raf, (int) (AUDIO_RATE * 1 * 16 / 8));
        writeLittleEndianShort(raf, (short) (1 * 16 / 8));
        writeLittleEndianShort(raf, (short) 16);
        raf.writeBytes("data");
        writeLittleEndianInt(raf, 0);
    }
}