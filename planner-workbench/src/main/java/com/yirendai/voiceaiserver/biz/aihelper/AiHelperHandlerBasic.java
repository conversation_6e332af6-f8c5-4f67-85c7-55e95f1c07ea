package com.yirendai.voiceaiserver.biz.aihelper;

import cn.hutool.json.JSONUtil;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.config.ZhiyuConfiguration;
import com.yirendai.voiceaiserver.enums.ResponseModeEnum;
import com.yirendai.voiceaiserver.listener.AiHelperEventSourceListener;
import com.yirendai.voiceaiserver.service.AiHelperService;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ZhiYuApiService;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.EventResponse;
import com.yirendai.voiceaiserver.util.HttpUtil;
import com.yirendai.voiceaiserver.vo.request.AiChatMessageReq;
import com.yirendai.voiceaiserver.vo.request.AiHelperMessageReq;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSourceListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * 小盟工具处理基类
 */
@Slf4j
public abstract class AiHelperHandlerBasic {

    @Resource
    protected AiPromptRecordService aiPromptRecordService;

    @Autowired
    @Lazy
    protected AiHelperService aiHelperService;
    @Autowired
    protected ZhiyuConfiguration zhiyuConfiguration;

    @Autowired
    private ZhiYuApiService zhiYuApiService;

    @Autowired
    private ExecutorService commonThreadPoolExecutor;

    /**
     * 发起会话
     *
     * @param chatMessagesReq
     * @return
     */
    public SseEmitter chatMessageStream(AiHelperMessageReq chatMessagesReq) {

        SseEmitter sseEmitter = new SseEmitter(60000L*5);
        sseEmitter.onTimeout(() -> {
            log.info("AiHelper see  sseEmitter timeout");
        });
        sseEmitter.onError(throwable -> {
            log.error("AiHelper see  sseEmitter error {}", throwable.getMessage(), throwable);
        });
        try {
            AiChatMessageReq aiChatMessageReq = getAiChatMessageReq(chatMessagesReq, sseEmitter);
            this.stream(aiChatMessageReq, sseEmitter, chatMessagesReq);
        } catch (Exception e) {
            log.warn("AiHelper see  sseEmitter failed {}", e.getMessage(), e);
        }

        return sseEmitter;
    }


    /**
     * 停止响应
     *
     * @param taskId
     * @param userId
     * @return
     */
    public R<String> stop(String taskId, String userId) {
        String aiCsKey = getKey();
        return zhiYuApiService.stopChatMessage(taskId, userId, aiCsKey);
    }


    /**
     * 获取Aichat请求对象
     *
     * @param chatMessagesReq
     * @return
     */
    protected AiChatMessageReq getAiChatMessageReq(AiHelperMessageReq chatMessagesReq, SseEmitter sseEmitter) throws Exception {
        AiChatMessageReq aiChatMessageReq = new AiChatMessageReq();
        String query = getPrompt(chatMessagesReq, sseEmitter);
        aiChatMessageReq.setQuery(query);
        aiChatMessageReq.setInputs(getInputs(chatMessagesReq, sseEmitter));
        aiChatMessageReq.setResponse_mode(ResponseModeEnum.STREAMING.getValue());
        aiChatMessageReq.setConversation_id(chatMessagesReq.getConversationId());
        aiChatMessageReq.setUser(chatMessagesReq.getUserId());
        return aiChatMessageReq;
    }

    /**
     * 获取预制提示词
     *
     * @return
     */
    protected abstract String getPrompt();

    /**
     * 组装整体提示词
     */
    protected String getPrompt(AiHelperMessageReq chatMessagesReq, SseEmitter sseEmitter) throws Exception {
        return chatMessagesReq.getQuery() + "  ," + getPrompt();
    }

    /**
     * 获取inputs
     */
    protected Map<String, Object> getInputs(AiHelperMessageReq chatMessagesReq, SseEmitter sseEmitter) throws Exception{
        Map<String, Object> map = new HashMap<>();
        map.put("tenantId", chatMessagesReq.getTenantId());
        map.put("scene", chatMessagesReq.getScene());
        return map;
    }

    /**
     * 获取模型调用key
     *
     * @return
     */
    protected abstract String getKey();

    /**
     * 获取模型调用接口
     *
     * @return
     */
    protected String getUrl() {
        return zhiyuConfiguration.getAiCsUrl() + "/chat-messages";
    }

    protected void stream(AiChatMessageReq aiChatMessageReq, SseEmitter sseEmitter, AiHelperMessageReq chatMessagesReq) {
        String aiCsKey = getKey();
        String url = getUrl();
        HttpUtil.stream(aiCsKey, url, JSONUtil.toJsonStr(aiChatMessageReq), getEventSourceListener(sseEmitter, chatMessagesReq));
    }

    /**
     * 获取EventSourceListener
     *
     * @param sseEmitter
     * @param aiHelperMessageReq
     * @return
     */
    protected EventSourceListener getEventSourceListener(SseEmitter sseEmitter, AiHelperMessageReq aiHelperMessageReq) {
        return new AiHelperEventSourceListener(sseEmitter, aiHelperMessageReq, aiHelperService);
    }

    protected EventResponse chatMessagesRes(AiHelperMessageReq chatMessagesReq, String res){
        EventResponse chatMessagesRes = new EventResponse();
        chatMessagesRes.setEvent("message");
        chatMessagesRes.setConversation_id(chatMessagesReq.getConversationId());
        chatMessagesRes.setAnswer(res);
        return chatMessagesRes;
    }
    /**
     * 一秒后把SseEmitter  关闭
     * 使用线程池
     */
    protected void delayClose(SseEmitter sseEmitter, long time) {
        commonThreadPoolExecutor.execute(() -> {
            try {
                Thread.sleep(time);
                sseEmitter.complete();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
    }
}
