package com.yirendai.voiceaiserver.biz.asr;

import javax.sound.sampled.*;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;

public class MP3ChannelSplitterCopy {

    // 核心分离方法
    public void splitChannels(File mp3File, File leftOutput, File rightOutput)
            throws UnsupportedAudioFileException, IOException {

        // 1. 读取MP3文件
        AudioInputStream mp3Stream = AudioSystem.getAudioInputStream(mp3File);
        AudioFormat sourceFormat = mp3Stream.getFormat();

        // 2. 验证音频格式
        if (sourceFormat.getChannels() != 2) {
            throw new UnsupportedAudioFileException("非立体声音频");
        }

        // 3. 转换为PCM格式
        AudioFormat pcmFormat = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                sourceFormat.getSampleRate(),
                16,                    // 16-bit
                sourceFormat.getChannels(),
                sourceFormat.getChannels() * 2, // 每样本字节数（2字节/通道）
                sourceFormat.getSampleRate(),
                false                  // 小端序
        );

        AudioInputStream pcmStream = AudioSystem.getAudioInputStream(pcmFormat, mp3Stream);

        // 4. 准备输出流
        ByteArrayOutputStream leftBuffer = new ByteArrayOutputStream();
        ByteArrayOutputStream rightBuffer = new ByteArrayOutputStream();

        // 5. 处理音频数据
        byte[] frameBuffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = pcmStream.read(frameBuffer)) != -1) {
            processFrame(frameBuffer, bytesRead, leftBuffer, rightBuffer);
        }

        // 6. 写入WAV文件
        writeWavFile(leftOutput, leftBuffer.toByteArray(), pcmFormat.getSampleRate());
        writeWavFile(rightOutput, rightBuffer.toByteArray(), pcmFormat.getSampleRate());

        // 7. 清理资源
        pcmStream.close();
        mp3Stream.close();
    }

    // 处理单个音频帧
    private void processFrame(byte[] frame, int length,
                              ByteArrayOutputStream left,
                              ByteArrayOutputStream right) {
        for (int i = 0; i < length; i += 4) { // 立体声16bit每帧4字节
            if (i + 3 >= length) break; // 防止越界

            // 提取左声道（前2字节）
            left.write(frame[i]);
            left.write(frame[i + 1]);

            // 提取右声道（后2字节）
            right.write(frame[i + 2]);
            right.write(frame[i + 3]);
        }
    }

    // 生成WAV文件
    private void writeWavFile(File output, byte[] pcmData, float sampleRate)
            throws IOException {

        AudioFormat format = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                sampleRate,
                16,         // 16-bit
                1,          // 单声道
                2,          // 2字节/帧
                sampleRate,
                false       // 小端序
        );

        try (ByteArrayInputStream bais = new ByteArrayInputStream(pcmData);
             AudioInputStream audioStream = new AudioInputStream(bais, format, pcmData.length / 2)) {
            AudioSystem.write(audioStream, AudioFileFormat.Type.WAVE, output);
        }
    }

    public static void main(String[] args) {
        try {
            MP3ChannelSplitterCopy splitChannels = new MP3ChannelSplitterCopy();
            splitChannels.splitChannels(
                    new File("/Users/<USER>/2025年/1月/1/yrcall_1869687765910835200.mp3"),
                    new File("/Users/<USER>/2025年/1月/1/left.wav"),
                    new File("/Users/<USER>/2025年/1月/1/right.wav")
            );
            System.out.println("声道分离完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}