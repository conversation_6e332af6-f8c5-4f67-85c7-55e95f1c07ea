package com.yirendai.voiceaiserver.biz;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;

import com.yirendai.voiceaiserver.model.contentcheck.MatchResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

/**
 * 内容稽查
 */
@Component
@Slf4j
public class ContentCheckBiz {


    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private AhoCorasick ahoCorasick;

    /**
     * 敏感词加载
     */
    @PostConstruct
    public void loadFile() throws IOException {
        String filePath = "ai/filter_word.txt";

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                resourceLoader.getResource("classpath:" + filePath).getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    ahoCorasick.insert(line);
                }
            }
        } catch (IOException e) {
            log.error("敏感词加载异常");
            throw e;
        }
        ahoCorasick.buildFailPointers();
        log.info("敏感词加载完成");
    }

    /**
     * 敏感词检测
     * @param text
     * @return
     */
    public List<MatchResult> filterWord(String text){
        return ahoCorasick.search(text);
    }


}
