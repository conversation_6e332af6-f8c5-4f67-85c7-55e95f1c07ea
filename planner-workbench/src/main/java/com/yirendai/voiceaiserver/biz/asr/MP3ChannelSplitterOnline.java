package com.yirendai.voiceaiserver.biz.asr;

import javax.sound.sampled.*;
import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.SeekableByteChannel;
import java.nio.file.*;

public class MP3ChannelSplitterOnline {

    // 核心分离方法（实时版本）
    public static void splitChannelsRealtime(File mp3File, File leftOutput, File rightOutput)
            throws UnsupportedAudioFileException, IOException, InterruptedException {

        // 初始化输出文件
        leftOutput.createNewFile();
        rightOutput.createNewFile();

        // 创建文件通道
        try (SeekableByteChannel channel = Files.newByteChannel(mp3File.toPath(), StandardOpenOption.READ)) {
            long lastPosition = 0;

            // 文件变化监听
            try (WatchService watchService = FileSystems.getDefault().newWatchService()) {
                Path path = mp3File.toPath().getParent();
                path.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);

                while (true) {
                    WatchKey key = watchService.take();
                    for (WatchEvent<?> event : key.pollEvents()) {
                        if (event.context().toString().equals(mp3File.getName())) {
                            lastPosition = processNewData(channel, lastPosition, leftOutput, rightOutput);
                        }
                    }
                    key.reset();
                }
            }
        }
    }

    private static long processNewData(SeekableByteChannel channel, long lastPosition, File leftOutput, File rightOutput)
            throws IOException, UnsupportedAudioFileException {

        long currentPosition = channel.size();
        if (currentPosition <= lastPosition) {
            return lastPosition;
        }

        // 读取新增部分
        ByteBuffer buffer = ByteBuffer.allocate((int) (currentPosition - lastPosition));
        channel.position(lastPosition);
        channel.read(buffer);
        lastPosition = currentPosition;

        // 处理新增音频数据
        processAudioData(buffer.array(), leftOutput, rightOutput);

        return lastPosition;
    }

    private static void processAudioData(byte[] audioData, File leftOutput, File rightOutput)
            throws IOException, UnsupportedAudioFileException {

        // 1. 将字节数组转换为AudioInputStream
        AudioInputStream mp3Stream = AudioSystem.getAudioInputStream(new ByteArrayInputStream(audioData));
        AudioFormat sourceFormat = mp3Stream.getFormat();

        // 2. 验证音频格式
        if (sourceFormat.getChannels() != 2) {
            throw new UnsupportedAudioFileException("非立体声音频");
        }

        // 3. 转换为PCM格式
        AudioFormat pcmFormat = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                sourceFormat.getSampleRate(),
                16,                    // 16-bit
                sourceFormat.getChannels(),
                sourceFormat.getChannels() * 2, // 每样本字节数（2字节/通道）
                sourceFormat.getSampleRate(),
                false                  // 小端序
        );

        AudioInputStream pcmStream = AudioSystem.getAudioInputStream(pcmFormat, mp3Stream);

        // 4. 准备输出流
        ByteArrayOutputStream leftBuffer = new ByteArrayOutputStream();
        ByteArrayOutputStream rightBuffer = new ByteArrayOutputStream();

        // 5. 处理音频数据
        byte[] frameBuffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = pcmStream.read(frameBuffer)) != -1) {
            processFrame(frameBuffer, bytesRead, leftBuffer, rightBuffer);
        }

        // 6. 写入WAV文件
        appendToWavFile(leftOutput, leftBuffer.toByteArray(), pcmFormat.getSampleRate());
        appendToWavFile(rightOutput, rightBuffer.toByteArray(), pcmFormat.getSampleRate());

        // 7. 清理资源
        pcmStream.close();
        mp3Stream.close();
    }

    private static void processFrame(byte[] frame, int length,
                                     ByteArrayOutputStream left,
                                     ByteArrayOutputStream right) {
        for (int i = 0; i < length; i += 4) { // 立体声16bit每帧4字节
            if (i + 3 >= length) break; // 防止越界

            // 提取左声道（前2字节）
            left.write(frame[i]);
            left.write(frame[i + 1]);

            // 提取右声道（后2字节）
            right.write(frame[i + 2]);
            right.write(frame[i + 3]);
        }
    }

    private static void appendToWavFile(File output, byte[] pcmData, float sampleRate)
            throws IOException, UnsupportedAudioFileException {

        // 读取现有WAV文件
        AudioFileFormat fileFormat = AudioSystem.getAudioFileFormat(output);
        AudioFormat format = fileFormat.getFormat();

        try (FileInputStream fis = new FileInputStream(output);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

            // 将现有内容复制到新的ByteArrayOutputStream
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }

            // 添加新数据
            AudioInputStream newAudioStream = new AudioInputStream(
                    new ByteArrayInputStream(pcmData),
                    format,
                    pcmData.length / 2 // 假设pcmData为16位，每样本2字节
            );

            // 将新数据添加到baos
            AudioSystem.write(newAudioStream, AudioFileFormat.Type.WAVE, new FileOutputStream(output));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        try {
            splitChannelsRealtime(
                    new File("/Users/<USER>/2025年/1月/1/yrcall_1869687765910835200.mp3"),
                    new File("/Users/<USER>/2025年/1月/1/left.wav"),
                    new File("/Users/<USER>/2025年/1月/1/right.wav")
            );
            System.out.println("声道分离完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
