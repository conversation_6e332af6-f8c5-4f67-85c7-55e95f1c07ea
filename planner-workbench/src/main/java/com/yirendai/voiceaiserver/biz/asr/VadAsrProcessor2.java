package com.yirendai.voiceaiserver.biz.asr;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.enums.MsgSendDirectionEnum;
import com.yirendai.voiceaiserver.enums.YesOrNoEnum;
import com.yirendai.voiceaiserver.vo.response.AsrOnlineVO;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.AiPlannerChatContactSub;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.mapper.AiPlannerChatContactSubMapper;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.Buffer;
import java.nio.ShortBuffer;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@Slf4j
@Setter
@Getter
public class VadAsrProcessor2 {

    private CallcenterCallRecordService callcenterCallRecordService;
    private IAiPlannerChatContactService iAiPlannerChatContactService;
    private AiPlannerChatContactSubMapper aiPlannerChatContactSubMapper;
    private String url = "http://aigc-api.aigc.paas.corp";
    private String key = "sk-itQdczJ3KcYNsn4OaO8F6M2MTfb887nGYay8E6oZjOSSAz9Q";
    private String tempDir = "/Users/<USER>/IdeaProjects/fortune_activity_admin/";
    private int AUDIO_RATE = 8000;
    private long TIMEOUT_0_MS = 2200;
    private long TIMEOUT_MS = 10000;
    private double VAD_SEGMENT_DURATION = 0.2;
    private double MIN_ASR_DURATION = 0.4;
    private String model = "funasr";
    private float temperature = 0.2f;
    private int maxDurationMs = 3 * 60 * 60 * 1000;
    private double timeFastSliceAsrTime = 3.6;
    private double timeFastSliceAsrMulti = 1.2;
    private double timeClearInactiveTime = 5.0;
    private double maxAsrTime = 8.0;
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final OkHttpClient client = new OkHttpClient();
    private final String LEFT_WAV = "temp_left.wav";
    private final String RIGHT_WAV = "temp_right.wav";
    private final String LEFT_CHANNEL = "左声道";
    private final String RIGHT_CHANNEL = "右声道";
    private final String FILE_PREFIX = "yr_asr_";
    private final boolean isDebugTest = true;
    private long MIN_FILE_SIZE = 1026;
    private int MAX_RETRIES = 3;
    private long[] RETRY_INTERVALS_MS = {2500, 5000, 10000};
    private long FAST_RETRY_INTERVAL_MS = 500;
    private String leftTempMp3File = "";
    private String rightTempMp3File = "";

    public void startMonitoring(SseEmitter sseEmitter, String uuid, Long ringTime) {
        AsrContext context = new AsrContext();
        context.setUuid(uuid);
        if (isDebugTest) {
            tempDir = "/Users/<USER>/IdeaProjects/fortune_activity_admin/";
        }
        log.info("音转文监控查询新呼叫中心消息id={}开始", uuid);
        LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CallcenterCallRecord::getUuid, uuid).eq(CallcenterCallRecord::getSystemType, 2);
        CallcenterCallRecord record = callcenterCallRecordService.getOne(wrapper);
        if (record == null) {
            log.error("音转文监控查询新呼叫中心消息id={}, type=2未查询到数据", uuid);
            seeSendError(sseEmitter, "查询新呼叫中心消息id={}, type=2未查询到数据");
            return;
        }
        String filePath = initProcessor(record.getTenantId(), uuid);
        if (filePath == null) {
            log.error("音转文监控初始化文件路径失败id={}", uuid);
            seeSendError(sseEmitter, "音转文监控初始化文件路径失败");
            return;
        }
        LambdaQueryWrapper<AiPlannerChatContact> chatContactWrapper = new LambdaQueryWrapper<>();
        chatContactWrapper.eq(AiPlannerChatContact::getBusId, record.getUuid());
        AiPlannerChatContact chatContact = iAiPlannerChatContactService.getOne(chatContactWrapper);
        if (Objects.isNull(chatContact)) {
            log.info("音转文监控当前消息uuid={}未查询到对话记录", uuid);
            seeSendError(sseEmitter, "音转文监控当前消息未查询到对话记录");
            return;
        }


        String leftMp3File = leftTempMp3File;
        String rightMp3File = rightTempMp3File;
        if (Strings.isNullOrEmpty(leftMp3File) || Strings.isNullOrEmpty(rightMp3File)) {
            log.info("音转文监控未获取到音频文件uuid={}, leftPath={}, rightPath={}", uuid, leftMp3File, rightMp3File);
            seeSendError(sseEmitter, "音转文监控未获取到音频文件");
            return;
        }
        File leftFile = new File(leftMp3File);
        File rightFile = new File(rightMp3File);
        context.setUserName(record.getCustomerName());
        context.setCustomerName(record.getAgentName());
        context.setLeftDirection(MsgSendDirectionEnum.FROM_CRM.getCode());
        context.setRightDirection(MsgSendDirectionEnum.FROM_CUS.getCode());
        if (MsgSendDirectionEnum.FROM_CUS.getCode().equals(chatContact.getDirection())) {
            context.setLeftDirection(MsgSendDirectionEnum.FROM_CUS.getCode());
            context.setRightDirection(MsgSendDirectionEnum.FROM_CRM.getCode());
        }
        context.setChatContactId(chatContact.getId());

        int retryCount = 0;
        while (retryCount < MAX_RETRIES) {
            if (leftFile.length() < MIN_FILE_SIZE || rightFile.length() < MIN_FILE_SIZE) {
                log.warn("MP3 file too small (left: {} bytes, right: {} bytes), retry {}/{} in {} ms",
                        leftFile.length(), rightFile.length(), retryCount + 1, MAX_RETRIES, RETRY_INTERVALS_MS[retryCount]);
                try {
                    Thread.sleep(RETRY_INTERVALS_MS[retryCount]);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Interrupted during file size check retry, uuid={}", uuid, e);
                    seeSendError(sseEmitter, "Interrupted during file size check");
                    return;
                }
                retryCount++;
                continue;
            }
            break;
        }
        if (retryCount >= MAX_RETRIES) {
            log.error("MP3 files remain too small after retries: left={} bytes, right={} bytes, uuid={}",
                    leftFile.length(), rightFile.length(), uuid);
            seeSendError(sseEmitter, "MP3 files too small after retries");
            return;
        }

        if (ringTime != null && ringTime > 0) {
            retryCount = 0;
            for (File mp3File : new File[]{leftFile, rightFile}) {
                String channel = mp3File.equals(leftFile) ? LEFT_CHANNEL : RIGHT_CHANNEL;
                while (retryCount <= MAX_RETRIES) {
                    FFmpegFrameGrabber grabber = null;
                    try {
                        grabber = new FFmpegFrameGrabber(mp3File);
                        grabber.setAudioChannels(1); // Single channel for individual files
                        grabber.setOption("probesize", "1026");
                        grabber.setOption("analyzeduration", "1000000");
                        grabber.start();
                        try {
                            long sampleRate = grabber.getSampleRate();
                            long bitrate = grabber.getAudioBitrate() / 1000;
                            int bytesPerSample = 2;
                            double seconds = ringTime;
                            long samplesToSkip = (long) (seconds * sampleRate);
                            long pcmBytesToSkip = samplesToSkip * bytesPerSample;
                            if (mp3File.equals(leftFile)) {
                                context.setLastLeftPosition(pcmBytesToSkip);
                            } else {
                                context.setLastRightPosition(pcmBytesToSkip);
                            }
                            double bytesPerSecond = (double) (bitrate * 1000) / 8;
                            long mp3BytesToSkip = (long) (seconds * bytesPerSecond);
                            long actualSize = mp3File.length();
                            if (mp3File.equals(leftFile)) {
                                context.setLastLeftFileSize(mp3BytesToSkip);
                                if (mp3BytesToSkip > actualSize) {
                                    log.warn("id={} 音转文监控响铃时间 {} s 左声道文件大小 {} 超出实际大小 {}，重置为 0", uuid, seconds, mp3BytesToSkip, actualSize);
                                    context.setLastLeftFileSize(0L);
                                    context.setLastLeftPosition(0L);
                                } else {
                                    log.info("id={} 音转文监控根据响铃时间 {} s，左声道初始化 lastPosition={} bytes, lastFileSize={} bytes", uuid, seconds, context.getLastLeftPosition(), context.getLastLeftFileSize());
                                }
                            } else {
                                context.setLastRightFileSize(mp3BytesToSkip);
                                if (mp3BytesToSkip > actualSize) {
                                    log.warn("id={} 音转文监控响铃时间 {} s 右声道文件大小 {} 超出实际大小 {}，重置为 0", uuid, seconds, mp3BytesToSkip, actualSize);
                                    context.setLastRightFileSize(0L);
                                    context.setLastRightPosition(0L);
                                } else {
                                    log.info("id={} 音转文监控根据响铃时间 {} s，右声道初始化 lastPosition={} bytes, lastFileSize={} bytes", uuid, seconds, context.getLastRightPosition(), context.getLastRightFileSize());
                                }
                            }
                            break;
                        } finally {
                            grabber.stop();
                        }
                    } catch (Exception e) {
                        log.error("音转文监控计算响铃时间跳过位置失败id={}, ringTime={}, channel={}, retry {}/{}", uuid, ringTime, channel, retryCount + 1, MAX_RETRIES, e);
                        retryCount++;
                        if (retryCount > MAX_RETRIES) {
                            seeSendError(sseEmitter, "音转文监控计算响铃时间跳过位置失败");
                            return;
                        }
                        try {
                            Thread.sleep(RETRY_INTERVALS_MS[Math.min(retryCount - 1, RETRY_INTERVALS_MS.length - 1)]);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            log.error("Interrupted during FFmpeg retry, uuid={}", uuid, ie);
                            seeSendError(sseEmitter, "Interrupted during FFmpeg retry");
                            return;
                        }
                    }
                }
            }
        } else {
            log.info("监控响铃时间为空或无效，id={}, 初始化 lastPosition=0, lastFileSize=0", uuid);
            context.setLastLeftFileSize(0L);
            context.setLastLeftPosition(0L);
            context.setLastRightFileSize(0L);
            context.setLastRightPosition(0L);
        }

        long startTime = System.currentTimeMillis();
        long lastChangeTime = startTime;

        try {
            while (true) {
                try {
                    Long leftSize = leftFile.length();
                    Long rightSize = rightFile.length();
                    Long leftModifiedTime = leftFile.lastModified();
                    Long rightModifiedTime = rightFile.lastModified();

                    boolean fileChanged = !leftSize.equals(context.getLastLeftFileSize()) || !rightSize.equals(context.getLastRightFileSize()) ||
                            !leftModifiedTime.equals(context.getLastLeftModifiedTime()) || !rightModifiedTime.equals(context.getLastRightModifiedTime());

                    if (!fileChanged) {
                        if (System.currentTimeMillis() - lastChangeTime > TIMEOUT_MS) {
                            break;
                        }
                        if (System.currentTimeMillis() - lastChangeTime > TIMEOUT_0_MS && (context.isLastLeftActive() || context.isLastRightActive())) {
                            processRemainingAudio(context, filePath, sseEmitter);
                        }
                    } else {
                        lastChangeTime = System.currentTimeMillis();
                        if (!leftSize.equals(context.getLastLeftFileSize()) || !leftModifiedTime.equals(context.getLastLeftModifiedTime())) {
                            processFileChange(leftFile, context, filePath, sseEmitter, LEFT_CHANNEL, context.getLastLeftPosition(), context.getLeftChannelBuffer(), context::setLastLeftPosition, context::setLastLeftFileSize, context::setLastLeftModifiedTime);
                        }
                        if (!rightSize.equals(context.getLastRightFileSize()) || !rightModifiedTime.equals(context.getLastRightModifiedTime())) {
                            processFileChange(rightFile, context, filePath, sseEmitter, RIGHT_CHANNEL, context.getLastRightPosition(), context.getRightChannelBuffer(), context::setLastRightPosition, context::setLastRightFileSize, context::setLastRightModifiedTime);
                        }
                    }
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    log.info("MP3 监控线程中断, uuid={}", uuid, e);
                    Thread.currentThread().interrupt();
                    break;
                } catch (IllegalStateException e) {
                    log.error("MP3 状态异常失败, uuid={}", uuid, e);
                    seeSendError(sseEmitter, "MP3 文件访问失败: " + e.getMessage());
                    break;
                } catch (Exception e) {
                    log.error("MP3 监控处理失败, uuid={}", uuid, e);
                    seeSendError(sseEmitter, "MP3 监控处理失败: " + e.getMessage());
                    break;
                }
                if (System.currentTimeMillis() - startTime > maxDurationMs) {
                    log.info("已超过最大处理时长 {} 秒，退出监控 (uuid={})", maxDurationMs / 1000, uuid);
                    break;
                }
            }
        } catch (Throwable e) {
            log.error("Mp3 监控错误,id={}", uuid, e);
        } finally {
            if (!filePath.isEmpty()) {
                deleteDirectory(filePath);
            }
            if (!leftMp3File.isEmpty()) {
                deleteDirectory(leftMp3File);
            }
            if (!rightMp3File.isEmpty()) {
                deleteDirectory(rightMp3File);
            }
            try {
                sseEmitter.complete();
            } catch (Exception e) {
                log.error("关闭 SseEmitter 失败, uuid:{}", uuid, e);
            }
        }
    }

    private void processRemainingAudio(AsrContext context, String filePath, SseEmitter sseEmitter) {
        String uuid = context.getUuid();
        for (String channelName : new String[]{LEFT_CHANNEL, RIGHT_CHANNEL}) {
            List<byte[]> segmentsToSave = channelName.equals(LEFT_CHANNEL) ? context.getLeftSegmentsToSave() : context.getRightSegmentsToSave();
            List<byte[]> channelBuffer = channelName.equals(LEFT_CHANNEL) ? context.getLeftChannelBuffer() : context.getRightChannelBuffer();
            String wavFile = channelName.equals(LEFT_CHANNEL) ? LEFT_WAV : RIGHT_WAV;

            if (!segmentsToSave.isEmpty() && (channelName.equals(LEFT_CHANNEL) ? context.isLastLeftActive() : context.isLastRightActive())) {
                if (channelName.equals(LEFT_CHANNEL)) {
                    context.setCurrentLeftSilenceDuration(0.0);
                    context.setLastLeftActive(false);
                } else {
                    context.setCurrentRightSilenceDuration(0.0);
                    context.setLastRightActive(false);
                }
                int totalSavedSamples = segmentsToSave.stream().mapToInt(data -> data.length / 2).sum();
                double savedDuration = totalSavedSamples / (double) AUDIO_RATE;

                String tempFileName = FILE_PREFIX + System.currentTimeMillis() + "_" + wavFile;
                File file = saveToWav(segmentsToSave, tempFileName, filePath);
                if (file != null && file.exists() && file.length() >= 44) {
                    JSONObject jsonObject = asr2Json(file, model, null, null, null, temperature);
                    String transcription = jsonObject != null ? jsonObject.getString("text") : null;
                    if (transcription != null && !transcription.isEmpty()) {
                        log.info("音转文监控id={}, {} 转录结果: {}", uuid, channelName, transcription);
                        if (channelName.equals(LEFT_CHANNEL)) {
                            seeSendMsg(sseEmitter, context, transcription, context.getLeftDirection(), Objects.equals(context.getLeftDirection(), MsgSendDirectionEnum.FROM_CUS.getCode()) ? context.getUserName() : context.getCustomerName());
                        } else {
                            seeSendMsg(sseEmitter, context, transcription, context.getRightDirection(), Objects.equals(context.getLeftDirection(), MsgSendDirectionEnum.FROM_CUS.getCode()) ? context.getCustomerName() : context.getUserName());
                        }
                    }
                    deleteWavFile(tempFileName);
                }
                segmentsToSave.clear();
                channelBuffer.clear();
            } else {
                channelBuffer.clear();
            }
        }
    }

    private void deleteDirectory(String filePath) {
        if (isDebugTest) {
            return;
        }
        Path dirPath = Paths.get(filePath);
        if (Files.exists(dirPath)) {
            try {
                Files.walkFileTree(dirPath, new DeleteDirectoryVisitor());
                log.info("目录删除成功: {}", dirPath);
            } catch (IOException e) {
                log.error("目录删除失败: {}", dirPath, e);
            }
        }
    }

    private static class DeleteDirectoryVisitor extends SimpleFileVisitor<Path> {
        @NotNull
        @Override
        public FileVisitResult visitFile(Path file, @NotNull BasicFileAttributes attrs) throws IOException {
            Files.delete(file);
            return FileVisitResult.CONTINUE;
        }

        @NotNull
        @Override
        public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
            Files.delete(dir);
            return FileVisitResult.CONTINUE;
        }
    }

    private String initProcessor(String tenantId, String uuid) {
        if (tempDir == null || tempDir.isEmpty()) {
            tempDir = "/yrCallVoice/asr/";
        }
        String tenantPath = tempDir + tenantId + "/";
        String filePath = tenantPath + uuid + "/";
        Path dirPath = Paths.get(filePath);
        Path tenantDirPath = Paths.get(tenantPath);
        Path parentDirPath = Paths.get(tempDir);
        if (!Files.exists(parentDirPath)) {
            try {
                Files.createDirectories(parentDirPath);
                log.info("音转文监控id={}, 父目录创建成功: {}", uuid, parentDirPath);
            } catch (IOException e) {
                log.error("音转文监控id={}, 父目录创建失败: {}", uuid, parentDirPath, e);
                return null;
            }
        }
        if (!Files.exists(tenantDirPath)) {
            try {
                Files.createDirectories(tenantDirPath);
                log.info("音转文监控id={}, 租户目录创建成功: {}", uuid, tenantDirPath);
            } catch (IOException e) {
                log.error("音转文监控id={}, 租户目录创建失败: {}", uuid, tenantDirPath, e);
                return null;
            }
        }
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
                log.info("音转文监控id={}, 目录创建成功: {}", uuid, dirPath);
            } catch (IOException e) {
                log.error("音转文监控id={}, 目录创建失败: {}", dirPath, uuid, e);
                return null;
            }
        }
        return filePath;
    }

    private void processFileChange(File mp3File, AsrContext context, String filePath, SseEmitter sseEmitter, String channelName, long lastPosition, List<byte[]> channelBuffer, java.util.function.Consumer<Long> setLastPosition, java.util.function.Consumer<Long> setLastFileSize, java.util.function.Consumer<Long> setLastModifiedTime) {
        String uuid = context.getUuid();
        try {
            Long currentSize = mp3File.length();
            if (currentSize <= (channelName.equals(LEFT_CHANNEL) ? context.getLastLeftFileSize() : context.getLastRightFileSize())) {
                return;
            }
            byte[] pcmData = convertMp3ToPcm(mp3File, lastPosition, uuid);
            if (pcmData.length == 0) {
                return;
            }
            channelBuffer.add(pcmData);
            Future<?> future = threadPoolTaskExecutor.submit(() -> {
                try {
                    processChannel(context, channelName, channelName.equals(LEFT_CHANNEL) ? LEFT_WAV : RIGHT_WAV, filePath, sseEmitter);
                } catch (Exception e) {
                    log.info("音转文监控id={}, processFileChange 处理{}失败", uuid, channelName, e);
                }
            });
            try {
                future.get();
            } catch (Exception e) {
                log.warn("音转文监控id={}, 等待{}处理任务失败", uuid, channelName, e);
            }
            setLastPosition.accept(lastPosition + pcmData.length);
            setLastFileSize.accept(currentSize);
            setLastModifiedTime.accept(mp3File.lastModified());
        } catch (Exception e) {
            log.error("音转文监控id={}, processFileChange 处理{} MP3 文件失败", uuid, channelName, e);
        }
    }

    private byte[] convertMp3ToPcm(File mp3File, Long lastPosition, String uuid) throws Exception {
        synchronized (VadAsrProcessor2.class) {
            int retryCount = 0;
            while (retryCount <= MAX_RETRIES) {
                FFmpegFrameGrabber grabber = null;
                try {
                    grabber = new FFmpegFrameGrabber(mp3File);
                    grabber.setAudioChannels(1);
                    grabber.start();

                    int bytesPerSample = 2;
                    long sampleRate = grabber.getSampleRate();
                    long samplesToSkip = lastPosition / bytesPerSample;
                    long startTimeMicros = (long) (samplesToSkip * 1_000_000.0 / sampleRate);

                    grabber.setTimestamp(startTimeMicros);
                    log.info("音转文监控id={}, 跳转时间戳: {} s (对应字节位置 {})", uuid, startTimeMicros / 1_000_000.0, lastPosition);

                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    Frame frame;
                    long actualSkipped = 0;

                    while ((frame = grabber.grabFrame()) != null) {
                        if (frame.samples == null) {
                            continue;
                        }
                        for (Buffer buffer : frame.samples) {
                            if (buffer instanceof ShortBuffer) {
                                ShortBuffer sb = (ShortBuffer) buffer;
                                if (actualSkipped < samplesToSkip) {
                                    int remainingSkip = (int) (samplesToSkip - actualSkipped);
                                    sb.position(sb.position() + Math.min(remainingSkip, sb.remaining()));
                                    actualSkipped += remainingSkip;
                                    if (sb.remaining() == 0) {
                                        continue;
                                    }
                                }
                                byte[] byteBuffer = new byte[sb.remaining() * bytesPerSample];
                                int pos = 0;
                                while (sb.hasRemaining()) {
                                    short s = sb.get();
                                    byteBuffer[pos++] = (byte) (s & 0xFF);
                                    byteBuffer[pos++] = (byte) (s >>> 8);
                                }
                                baos.write(byteBuffer);
                            }
                        }
                    }
                    return baos.toByteArray();
                } catch (Exception e) {
                    log.error("音转文监控id={}, convertMp3ToPcm 失败, retry {}/{}", uuid, retryCount + 1, MAX_RETRIES, e);
                    retryCount++;
                    if (retryCount > MAX_RETRIES) {
                        throw new Exception("Failed to convert MP3 to PCM after retries", e);
                    }
                    try {
                        Thread.sleep(FAST_RETRY_INTERVAL_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("Interrupted during FFmpeg retry", ie);
                    }
                } finally {
                    if (grabber != null) {
                        try {
                            grabber.stop();
                        } catch (Exception e) {
                            log.error("Failed to stop FFmpegFrameGrabber, uuid={}", uuid, e);
                        }
                    }
                }
            }
            return new byte[0];
        }
    }

    private void processChannel(AsrContext context, String channelName, String wavFile, String filePath, SseEmitter sseEmitter) {
        List<byte[]> buffer = channelName.equals(LEFT_CHANNEL) ? context.getLeftChannelBuffer() : context.getRightChannelBuffer();
        String uuid = context.getUuid();
        byte[] combinedData = concatenateByteArrays(buffer);
        if (combinedData.length == 0) {
            log.info("音转文监控id={}, {} 合并数据为空，跳过处理", uuid, channelName);
            buffer.clear();
            return;
        }
        int totalSamples = combinedData.length / 2;
        double bufferDuration = totalSamples / (double) AUDIO_RATE;
        int vadSegmentSamples = (int) (AUDIO_RATE * VAD_SEGMENT_DURATION);
        int bytesPerSegment = vadSegmentSamples * 2;

        // 检查说话人切换
        String otherChannel = channelName.equals(LEFT_CHANNEL) ? RIGHT_CHANNEL : LEFT_CHANNEL;
        boolean otherChannelActive = otherChannel.equals(LEFT_CHANNEL) ? context.isLastLeftActive() : context.isLastRightActive();
        boolean currentChannelActive = channelName.equals(LEFT_CHANNEL) ? context.isLastLeftActive() : context.isLastRightActive();
        String lastSpeaker = context.getLastSpeaker();

        // 如果另一声道刚开始活跃，且当前声道有未转录的段，触发整段转录
        if (otherChannelActive && !lastSpeaker.equals(channelName) && !context.getPendingSegments(channelName).isEmpty()) {
            log.info("音转文监控id={}, 检测到说话人切换，{} 整段转录", uuid, lastSpeaker);
            transcribePendingSegments(context, lastSpeaker, filePath, sseEmitter, true); // 使用 2pass-offline
            context.getPendingSegments(lastSpeaker).clear();
            context.setLastSpeaker(channelName);
        }

        for (int i = 0; i < combinedData.length; i += bytesPerSegment) {
            List<byte[]> segmentsToSave = channelName.equals(LEFT_CHANNEL) ? context.getLeftSegmentsToSave() : context.getRightSegmentsToSave();
            List<Double> energyHistory = channelName.equals(LEFT_CHANNEL) ? context.getLeftEnergyHistory() : context.getRightEnergyHistory();
            List<Double> zcrHistory = channelName.equals(LEFT_CHANNEL) ? context.getLeftZcrHistory() : context.getRightZcrHistory();
            boolean lastActive = channelName.equals(LEFT_CHANNEL) ? context.isLastLeftActive() : context.isLastRightActive();
            double currentSilenceDuration = channelName.equals(LEFT_CHANNEL) ? context.getCurrentLeftSilenceDuration() : context.getCurrentRightSilenceDuration();
            double noVoiceDuration = channelName.equals(LEFT_CHANNEL) ? context.getLeftNoVoiceDuration() : context.getRightNoVoiceDuration();
            double timeSinceLastAsr = channelName.equals(LEFT_CHANNEL) ? context.getLastLeftAsrTime() : context.getLastRightAsrTime();
            int segmentLength = Math.min(bytesPerSegment, combinedData.length - i);
            byte[] segment = new byte[segmentLength];
            System.arraycopy(combinedData, i, segment, 0, segmentLength);
            short[] samples = new short[segmentLength / 2];
            for (int j = 0; j < samples.length; j++) {
                samples[j] = (short) ((segment[j * 2] & 0xff) | (segment[j * 2 + 1] << 8));
            }
            double energy = 0;
            double maxSample = 0;
            for (short sample : samples) {
                energy += sample * sample;
                maxSample = Math.max(maxSample, Math.abs(sample));
            }
            energy = (energy / samples.length) / (maxSample * maxSample + 1e-6);
            double zcr = 0;
            for (int j = 1; j < samples.length; j++) {
                if ((samples[j - 1] >= 0 && samples[j] < 0) || (samples[j - 1] < 0 && samples[j] >= 0)) {
                    zcr += 1;
                }
            }
            zcr = zcr / samples.length;
            energyHistory.add(energy);
            zcrHistory.add(zcr);
            if (energyHistory.size() > 100) {
                energyHistory.remove(0);
                zcrHistory.remove(0);
            }
            double energyMean = energyHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double zcrMean = zcrHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double energyStd = Math.sqrt(energyHistory.stream().mapToDouble(e -> Math.pow(e - energyMean, 2)).average().orElse(0.0));
            double zcrStd = Math.sqrt(zcrHistory.stream().mapToDouble(z -> Math.pow(z - zcrMean, 2)).average().orElse(0.0));
            double energyThreshold = Math.max(energyMean - 0.05 * energyStd, 0.00005);
            double zcrThreshold = Math.max(zcrMean - 0.05 * zcrStd, 0.0005);
            if (timeSinceLastAsr > timeFastSliceAsrTime) {
                energyThreshold *= timeFastSliceAsrMulti;
                zcrThreshold *= timeFastSliceAsrMulti;
            }
            double segmentDuration = ((double) segmentLength / 2) / AUDIO_RATE;
            boolean vadResult = energy > energyThreshold || zcr > zcrThreshold;
            segmentsToSave.add(segment);
            context.getPendingSegments(channelName).add(segment); // 记录到待转录段
            if (vadResult) {
                if (LEFT_CHANNEL.equals(channelName)) {
                    context.setCurrentLeftSilenceDuration(0.0);
                    context.setLeftNoVoiceDuration(0.0);
                    context.setLastLeftActive(true);
                    context.setLastLeftAsrTime(timeSinceLastAsr + segmentDuration);
                } else {
                    context.setCurrentRightSilenceDuration(0.0);
                    context.setRightNoVoiceDuration(0.0);
                    context.setLastRightActive(true);
                    context.setLastRightAsrTime(timeSinceLastAsr + segmentDuration);
                }
                context.setLastSpeaker(channelName);
            } else {
                if (LEFT_CHANNEL.equals(channelName)) {
                    context.setCurrentLeftSilenceDuration(currentSilenceDuration + segmentDuration);
                    context.setLeftNoVoiceDuration(noVoiceDuration + segmentDuration);
                    currentSilenceDuration = context.getCurrentLeftSilenceDuration();
                    noVoiceDuration = context.getLeftNoVoiceDuration();
                } else {
                    context.setCurrentRightSilenceDuration(currentSilenceDuration + segmentDuration);
                    context.setRightNoVoiceDuration(noVoiceDuration + segmentDuration);
                    currentSilenceDuration = context.getCurrentRightSilenceDuration();
                    noVoiceDuration = context.getRightNoVoiceDuration();
                }
                if (noVoiceDuration >= timeClearInactiveTime && !lastActive) {
                    segmentsToSave.clear();
                    context.getPendingSegments(channelName).clear();
                    if (LEFT_CHANNEL.equals(channelName)) {
                        context.setLeftNoVoiceDuration(0.0);
                    } else {
                        context.setRightNoVoiceDuration(0.0);
                    }
                    continue;
                }
            }
            boolean forceAsr = timeSinceLastAsr >= maxAsrTime;
            if ((currentSilenceDuration >= MIN_ASR_DURATION && lastActive) || forceAsr) {
                try {
                    int totalSavedSamples = segmentsToSave.stream().mapToInt(data -> data.length / 2).sum();
                    double savedDuration = totalSavedSamples / (double) AUDIO_RATE;
                    log.info("音转文监控id={}, {} {}，segmentsToSave 时长: {} 秒，调用 ASR 接口...",
                            uuid, channelName, forceAsr ? "强制转录触发" : "静音触发", savedDuration);
                    if (LEFT_CHANNEL.equals(channelName)) {
                        context.setLastLeftActive(false);
                        context.setCurrentLeftSilenceDuration(0.0);
                        context.setLastLeftAsrTime(0.0);
                    } else {
                        context.setLastRightActive(false);
                        context.setCurrentRightSilenceDuration(0.0);
                        context.setLastRightAsrTime(0.0);
                    }
                    String tempFileName = FILE_PREFIX + System.currentTimeMillis() + "_" + wavFile;
                    File file = saveToWav(segmentsToSave, tempFileName, filePath);
                    if (file != null && file.exists() && file.length() >= 44) {
                        JSONObject jsonObject = asr2Json(file, model, null, null, null, temperature);
                        String transcription = jsonObject != null ? jsonObject.getString("text") : null;
                        if (transcription != null && !transcription.isEmpty()) {
                            log.info("音转文监控id={}, {} online转录结果: {}", uuid, channelName, transcription);
                            if (channelName.equals(LEFT_CHANNEL)) {
                                seeSendMsg(sseEmitter, context, transcription, context.getLeftDirection(),
                                        Objects.equals(context.getLeftDirection(), MsgSendDirectionEnum.FROM_CUS.getCode()) ? context.getUserName() : context.getCustomerName(), "2pass-online");
                            } else {
                                seeSendMsg(sseEmitter, context, transcription, context.getRightDirection(),
                                        Objects.equals(context.getLeftDirection(), MsgSendDirectionEnum.FROM_CUS.getCode()) ? context.getCustomerName() : context.getUserName(), "2pass-online");
                            }
                        }
                        deleteWavFile(tempFileName);
                    }
                } catch (Throwable e) {
                    log.error("音转文监控id={}, {} ASR 转录失败，失败原因为", uuid, channelName, e);
                } finally {
                    segmentsToSave.clear();
                    context.getPendingSegments(channelName).clear();
                }
            }
        }
        buffer.clear();
    }

    private void deleteWavFile(String wavFile) {
        if (isDebugTest) {
            return;
        }
        File file = new File(wavFile);
        if (file.exists()) {
            if (file.delete()) {
                log.info("已删除 WAV 文件: {}", wavFile);
            } else {
                log.warn("无法删除 WAV 文件: {}", wavFile);
            }
        }
    }

    private byte[] concatenateByteArrays(List<byte[]> arrays) {
        int totalLength = arrays.stream().mapToInt(b -> b.length).sum();
        byte[] result = new byte[totalLength];
        int offset = 0;
        for (byte[] array : arrays) {
            System.arraycopy(array, 0, result, offset, array.length);
            offset += array.length;
        }
        return result;
    }

    private File saveToWav(List<byte[]> audioData, String filename, String filePath) {
        try {
            filename = filePath + filename;
            if (audioData.isEmpty()) {
                log.info("音频数据为空，无法保存 WAV 文件: {}", filename);
                return null;
            }
            byte[] combinedData = concatenateByteArrays(audioData);
            if (combinedData.length == 0) {
                log.info("合并后的音频数据为空，无法保存 WAV 文件: {}", filename);
                return null;
            }
            File wavFile = new File(filename);
            if (wavFile.exists()) {
                wavFile.delete();
                log.info("删除旧 WAV 文件: {}", filename);
            }
            AudioFormat format = new AudioFormat(AUDIO_RATE, 16, 1, true, false);
            AudioInputStream ais = new AudioInputStream(
                    new ByteArrayInputStream(combinedData), format, combinedData.length / 2
            );
            AudioSystem.write(ais, AudioFileFormat.Type.WAVE, wavFile);
            ais.close();
            if (wavFile.exists() && wavFile.length() >= 44) {
                return wavFile;
            } else {
                return null;
            }
        } catch (IOException e) {
            log.error("保存 WAV 文件失败: {}，错误: {}", filename, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("保存 WAV 文件时发生未知错误: {}，错误: {}", filename, e.getMessage());
            return null;
        }
    }

    public JSONObject asr2Json(File file, String model, Integer num, String hotwords, String prompt, Float temperature) {
        try {
            if (!file.exists() || file.length() < 44) {
                log.info("音频文件 {} 不存在或无效（大小: {} 字节）", file.getName(), file.length());
                return null;
            }
            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", file.getName(), RequestBody.create(file, MediaType.parse("audio/wav")))
                    .addFormDataPart("version", "v3")
                    .addFormDataPart("response_format", "json")
                    .addFormDataPart("model", model);
            String defaultHotwords = "固收,类固收,小额001,大额001,宜人,大固,小固,指旺,安易盈,新手专享,新手标,优选,宜优选,认购,起投,券码,福卡,晒单," +
                    "宜人币,奖励金,积分,宜信,逛逛,宜享分,续投,起拍,转投,1688会员,399会员,特供,募集,竞拍,长盈宝,AI会员,小金罐,派息,付息,信贷,日日盈,618," +
                    "信美,首购,代金券,债权,旅居,福袋,长险,药诊卡,提额,旅居卡,宜路相伴,退休金,臻享,宜脉传承,翔旅保,臻选,宜脉相承,宝贝存钱罐,宜脉相传,弘康," +
                    "抽积分,智选,会员日,初冬礼遇,传家有道,宜人优选,健康节,创信,宜她保,社群,老师,平台,产品,风险,财报,经济,文件大小,盈利,保值,理财,网贷," +
                    "合规合法,政策,投资,大额,小额,定期,股票,基金,美元,货币,对冲率,特朗普,微信,企微";
            builder.addFormDataPart("hotwords", StrUtil.isBlank(hotwords) ? defaultHotwords : hotwords);
            if (Objects.nonNull(num)) {
                builder.addFormDataPart("oracle_num", String.valueOf(num));
            }
            if (StrUtil.isNotBlank(prompt)) {
                builder.addFormDataPart("prompt", prompt);
            }
            if (Objects.nonNull(temperature)) {
                builder.addFormDataPart("temperature", String.valueOf(temperature));
            }
            Request request = new Request.Builder()
                    .url(url + "/v1/audio/transcriptions")
                    .header("Authorization", key)
                    .post(builder.build())
                    .build();
            try (Response response = client.newCall(request).execute()) {
                int responseCode = response.code();
                String responseBody = response.body() != null ? response.body().string() : "";
                log.info("asr2Json 调用ChatAPI接口进行音转文结束, model={}, fileName={}, responseCode={}, response={}",
                        model, file.getName(), responseCode, responseBody);
                if (responseCode != 200) {
                    log.error("asr2Json ChatAPI 返回错误状态码: {}, 错误信息: {}", responseCode, response.message());
                    return null;
                }
                if (StrUtil.isBlank(responseBody)) {
                    return null;
                }
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                if (jsonObject == null || jsonObject.getInteger("code") != null || StrUtil.isNotBlank(jsonObject.getString("debug_message"))) {
                    log.error("asr2Json 调用 ChatAPI 返回错误响应: {}", jsonObject);
                    return null;
                }
                return jsonObject;
            }
        } catch (Exception e) {
            return null;
        }
    }

    private void seeSendError(SseEmitter sseEmitter, String msg) {
        AsrOnlineVO asrOnlineVO = new AsrOnlineVO();
        asrOnlineVO.setStatus(YesOrNoEnum.NO.getKey());
        asrOnlineVO.setMsg(msg);
        try {
            synchronized (sseEmitter) {
                sseEmitter.send(asrOnlineVO);
            }
            sseEmitter.complete();
        } catch (IOException e) {
            log.error("seeSendError {} {}", e.getMessage(), msg, e);
        }
    }

    private void transcribePendingSegments(AsrContext context, String channelName, String filePath, SseEmitter sseEmitter, boolean useOfflineMode) {
        String uuid = context.getUuid();
        List<byte[]> pendingSegments = context.getPendingSegments(channelName);
        if (pendingSegments.isEmpty()) {
            log.info("音转文监控id={}, {} 无待转录段，跳过", uuid, channelName);
            return;
        }
        try {
            String wavFile = channelName.equals(LEFT_CHANNEL) ? LEFT_WAV : RIGHT_WAV;
            String tempFileName = FILE_PREFIX + System.currentTimeMillis() + "_" + wavFile;
            File file = saveToWav(pendingSegments, tempFileName, filePath);
            if (file != null && file.exists() && file.length() >= 44) {
                JSONObject jsonObject = asr2Json(file, model, null, null, null, temperature);
                String transcription = jsonObject != null ? jsonObject.getString("text") : null;
                if (transcription != null && !transcription.isEmpty()) {
                    log.info("音转文监控id={}, {} 整段转录结果: {}", uuid, channelName, transcription);
                    if (channelName.equals(LEFT_CHANNEL)) {
                        seeSendMsg(sseEmitter, context, transcription, context.getLeftDirection(),
                                Objects.equals(context.getLeftDirection(), MsgSendDirectionEnum.FROM_CUS.getCode()) ? context.getUserName() : context.getCustomerName(), "2pass-offline");
                    } else {
                        seeSendMsg(sseEmitter, context, transcription, context.getRightDirection(),
                                Objects.equals(context.getLeftDirection(), MsgSendDirectionEnum.FROM_CUS.getCode()) ? context.getCustomerName() : context.getUserName(), "2pass-offline");
                    }
                }
                deleteWavFile(tempFileName);
            }
        } catch (Throwable e) {
            log.error("音转文监控id={}, {} 整段转录失败，失败原因为", uuid, channelName, e);
        }
    }

    private void seeSendMsg(SseEmitter sseEmitter, AsrContext context, String text, Integer direction, String userName, String mode) {
        AsrOnlineVO asrOnlineVO = new AsrOnlineVO();
        asrOnlineVO.setStatus(YesOrNoEnum.YES.getKey());
        asrOnlineVO.setText(text);
        asrOnlineVO.setDirection(direction);
        asrOnlineVO.setUserName(userName);
        String uuid = context.getUuid();
        asrOnlineVO.setMode(mode); // 设置 mode 参数
        try {
            synchronized (sseEmitter) {
                sseEmitter.send(asrOnlineVO);
            }
            AiPlannerChatContactSub contactSub = new AiPlannerChatContactSub();
            contactSub.setChatContactId(context.getChatContactId());
            contactSub.setDirection(direction);
            contactSub.setMode(mode); // 记录 mode
            contactSub.setText(text);
            contactSub.setUserName(userName);
            contactSub.setCreateTime(LocalDateTime.now());
            aiPlannerChatContactSubMapper.insert(contactSub);
        } catch (IOException e) {
            log.error("音转文监控id={}, seeSendMsg {} {}", uuid, e.getMessage(), text, e);
        }
    }

    private void seeSendMsg(SseEmitter sseEmitter, AsrContext context, String text, Integer direction, String userName) {
        AsrOnlineVO asrOnlineVO = new AsrOnlineVO();
        asrOnlineVO.setStatus(YesOrNoEnum.YES.getKey());
        asrOnlineVO.setText(text);
        asrOnlineVO.setDirection(direction);
        asrOnlineVO.setUserName(userName);
        String uuid = context.getUuid();
        asrOnlineVO.setMode("2pass-offline");
        try {
            synchronized (sseEmitter) {
                sseEmitter.send(asrOnlineVO);
            }
            AiPlannerChatContactSub contactSub = new AiPlannerChatContactSub();
            contactSub.setChatContactId(context.getChatContactId());
            contactSub.setDirection(direction);
            contactSub.setMode("online");
            contactSub.setText(text);
            contactSub.setUserName(userName);
            contactSub.setCreateTime(LocalDateTime.now());
            aiPlannerChatContactSubMapper.insert(contactSub);
        } catch (IOException e) {
            log.error("音转文监控id={}, seeSendError {} {}", uuid, e.getMessage(), text, e);
        }
    }

    @Data
    private static class AsrContext {
        private List<byte[]> leftChannelBuffer = new ArrayList<>();
        private List<byte[]> rightChannelBuffer = new ArrayList<>();
        private List<byte[]> leftSegmentsToSave = new ArrayList<>();
        private List<byte[]> rightSegmentsToSave = new ArrayList<>();
        private List<Double> leftEnergyHistory = new ArrayList<>();
        private List<Double> rightEnergyHistory = new ArrayList<>();
        private List<Double> leftZcrHistory = new ArrayList<>();
        private List<Double> rightZcrHistory = new ArrayList<>();
        private Long lastLeftFileSize = 0L;
        private Long lastRightFileSize = 0L;
        private Long lastLeftPosition = 0L;
        private Long lastRightPosition = 0L;
        private Long lastLeftModifiedTime = 0L;
        private Long lastRightModifiedTime = 0L;
        private boolean lastLeftActive = false;
        private boolean lastRightActive = false;
        private double currentLeftSilenceDuration = 0.0;
        private double currentRightSilenceDuration = 0.0;
        private Integer leftDirection;
        private Integer rightDirection;
        private String userName;
        private String uuid;
        private String customerName;
        private Long chatContactId;
        private double leftNoVoiceDuration = 0.0;
        private double rightNoVoiceDuration = 0.0;
        private double lastLeftAsrTime = 0.0;
        private double lastRightAsrTime = 0.0;
        private String lastSpeaker = ""; // 跟踪最后说话的声道
        private List<byte[]> leftPendingSegments = new ArrayList<>(); // 左声道待转录段
        private List<byte[]> rightPendingSegments = new ArrayList<>(); // 右声道待转录段

        public List<byte[]> getPendingSegments(String channelName) {
            return channelName.equals("左声道") ? leftPendingSegments : rightPendingSegments;
        }
    }
}