package com.yirendai.voiceaiserver.biz.aihelper;

import com.yirendai.voiceaiserver.enums.AISceneEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 小盟助手工厂
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AiHelperFactory {

    private final AiHelperHandlerAnalysisCallRecord analysisCallRecordHandler;
    private final AiHelperHandlerRealTimeCallTranscription realTimeCallTranscriptionHandler;
    private final AiHelperHandlerDefault defaultHandler;

    // 可扩展缓存映射
    private final Map<Integer, AiHelperHandlerBasic> handlerMap = new ConcurrentHashMap<>();

    /**
     * 初始化映射关系
     */
    @PostConstruct
    public void init() {
        handlerMap.put(AISceneEnum.ANALYSIS_CALL_RECORD.getKey(), analysisCallRecordHandler);
        handlerMap.put(AISceneEnum.REAL_TIME_CALL_TRANSCRIPTION.getKey(), realTimeCallTranscriptionHandler);
        handlerMap.put(AISceneEnum.DEFAULT.getKey(), defaultHandler);
    }

    /**
     * 获取对应的处理器
     *
     * @param scene 场景枚举  {@link  AISceneEnum}
     * @return 对应的处理器实例
     */
    public AiHelperHandlerBasic getHandler(Integer scene) {
        if(scene == null){
            return defaultHandler;
        }
        AiHelperHandlerBasic handler = handlerMap.get(scene);
        if (handler == null) {
            log.warn("未找到对应的AI助手处理器，使用默认处理器。scene: {}", scene);
            return defaultHandler;
        }
        return handler;
    }
}
