package com.yirendai.voiceaiserver.biz;

import java.util.*;
import java.util.stream.Collectors;

import com.yirendai.voiceaiserver.model.contentcheck.MatchResult;
import com.yirendai.voiceaiserver.model.contentcheck.TrieNode;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.stereotype.Component;

@Component
public class AhoCorasick {
    private TrieNode root = new TrieNode();


    public void insert(String word) {
        if(StringUtil.isBlank(word)){
            return;
        }
        TrieNode node = root;
        for (char ch : word.toCharArray()) {
            node = node.getChildren().computeIfAbsent(ch, k -> new TrieNode());
        }
        node.getOutputs().add(word);
    }

    public void buildFailPointers() {
        Queue<TrieNode> queue = new LinkedList<>();
        for (TrieNode child : root.getChildren().values()) {
            child.setFail(root) ;
            queue.add(child);
        }
        while (!queue.isEmpty()) {
            TrieNode currentNode = queue.poll();

            for (Map.Entry<Character, TrieNode> entry : currentNode.getChildren().entrySet()) {
                char ch = entry.getKey();
                TrieNode childNode = entry.getValue();
                TrieNode failNode = currentNode.getFail();
                while (failNode != null && !failNode.getChildren().containsKey(ch)) {
                    failNode = failNode.getFail();
                }
                if (failNode != null) {
                    childNode.setFail(failNode.getChildren().get(ch));
                } else {
                    childNode.setFail(root);
                }
                if (childNode.getFail().getOutputs() != null) {
                    childNode.getOutputs().addAll(childNode.getFail().getOutputs());
                }

                queue.add(childNode);
            }
        }
    }

    public List<MatchResult> search(String text) {
        Set<MatchResult> results = new HashSet<>();
        TrieNode node = root;

        for (int i = 0; i < text.length(); i++) {
            char ch = text.charAt(i);
            while (node != root && !node.getChildren().containsKey(ch)) {
                node = node.getFail();
            }
            node = node.getChildren().getOrDefault(ch, root);

            for (String word : node.getOutputs()) {
                int start = i - word.length() + 1;
                results.add(new MatchResult(start, word));
            }
        }

        return new ArrayList<>(results);
    }

}
