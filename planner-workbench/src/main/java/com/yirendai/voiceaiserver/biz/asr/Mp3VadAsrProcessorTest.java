package com.yirendai.voiceaiserver.biz.asr;

import java.io.*;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import javax.sound.sampled.*;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jaudiotagger.audio.*;
import org.jaudiotagger.audio.mp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Service
public class Mp3VadAsrProcessorTest {

    @Value("${chat-api.url}")
    private String url = "http://paas-chat-api.paas.paas.test";

    @Value("${chat-api.key}")
    private String key = "sk-M7DkKrU1opQ697xQ2YNbGcTZIT2v96izs6O8i0e4UFD115KR";

    private final OkHttpClient client = new OkHttpClient();

    @Value("${yr_asr.path:/yrCallVoice/asr/}")
    private String tempDir = "/Users/<USER>/IdeaProjects/fortune_activity_admin/asr/";

    private String targetMp3File = "/Users/<USER>/PycharmProjects/mere_fusion/other/target.mp3";
//    private String targetMp3File = "";
    private final String LEFT_WAV = "temp_left.wav";
    private final String RIGHT_WAV = "temp_right.wav";

    // 缓冲区和状态
    private final List<byte[]> leftChannelBuffer = new ArrayList<>();
    private final List<byte[]> rightChannelBuffer = new ArrayList<>();
    private long lastFileSize = 0;
    private long lastPosition = 0;
    private long lastModifiedTime = 0;
    private final List<byte[]> leftSegmentsToSave = new ArrayList<>();
    private final List<byte[]> rightSegmentsToSave = new ArrayList<>();
    private final List<Double> leftEnergyHistory = new ArrayList<>();
    private final List<Double> rightEnergyHistory = new ArrayList<>();
    private final List<Double> leftZcrHistory = new ArrayList<>();
    private final List<Double> rightZcrHistory = new ArrayList<>();
    // 音频参数
    @Value("${yr_asr.audio.rate}")
    private final int AUDIO_RATE = 8000;
    @Value("${yr_asr.timeout.short}")
    private final long TIMEOUT_0_MS = 2200;
    // 15秒超时
    @Value("${yr_asr.timeout.long}")
    private final long TIMEOUT_MS = 12000;
    // 每个 VAD 片段 0.2 秒
    @Value("${yr_asr.vad.segment}")
    private final double VAD_SEGMENT_DURATION = 0.2;
    // 最小转录时长 0.4 秒
    @Value("${yr_asr.vad.asr}")
    private final double MIN_ASR_DURATION = 0.4;
    @Value("${yr_asr.model.name}")
    private String model = "funasr";
    @Value("${yr_asr.model.temperature}")
    private float temperature = 0.2f;
    // 当前持续静音时长
    private double currentLeftSilenceDuration = 0.0;
    private double currentRightSilenceDuration = 0.0;

    // 左声道
    private final String LEFT_CHANNEL = "左声道";
    // 右声道
    private final String RIGHT_CHANNEL = "右声道";

    private boolean lastLeftActive = false;
    private boolean lastRightActive = false;

    private final String File_PREFIX = "yr_asr_";

    private String uuid;
    private String filePath;

    public boolean initProcessor(String tenantId, String uuid) {
        // 确保目录路径不为空
        if (tempDir == null || tempDir.isEmpty()) {
            tempDir = "/yrCallVoice/asr/";
        }
        String tenantPath = tempDir + tenantId + "/";
        String filePath = tenantPath + uuid + "/";
        Path dirPath = Paths.get(filePath);
        Path tenantDirPath = Paths.get(tenantPath);
        Path parentDirPath = Paths.get(tempDir);
        // 确保父目录存在
        if (!Files.exists(parentDirPath)) {
            try {
                Files.createDirectories(parentDirPath);
                log.info("父目录创建成功: {}", parentDirPath);
            } catch (IOException e) {
                log.error("父目录创建失败: {}", parentDirPath, e);
                return false;
            }
        }

        if (!Files.exists(tenantDirPath)) {
            try {
                Files.createDirectories(tenantDirPath);
                log.info("租户目录创建成功: {}", tenantDirPath);
            } catch (IOException e) {
                log.error("租户目录创建失败: {}", tenantDirPath, e);
                return false;
            }
        }

        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
                log.info("目录创建成功: {}", dirPath);
            } catch (IOException e) {
                log.error("目录创建失败: {}", dirPath, e);
                return false;
            }
        }
        return true;
    }

    public void startMonitoring() {
        File mp3File = new File(targetMp3File);
        long startTime = System.currentTimeMillis();
        long lastChangeTime = startTime;

        // 等待文件存在，最多15秒
        while (!mp3File.exists()) {
            if (System.currentTimeMillis() - startTime > TIMEOUT_MS) {
                log.debug("MP3 文件 {} 在 {} 秒内未出现，退出监控", targetMp3File, TIMEOUT_MS / 1000);
                return;
            }
            log.debug("MP3 文件 {} 不存在，等待中...", targetMp3File);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.debug("等待文件时中断", e);
                return;
            }
        }

        log.debug("开始监控 MP3 文件: {}", targetMp3File);

        try {
            while (true) {
                try {
                    long currentSize = mp3File.length();
                    long currentModifiedTime = mp3File.lastModified();

                    // 检查10秒内是否有变动
                    if (currentSize == lastFileSize && currentModifiedTime == lastModifiedTime) {
                        if (System.currentTimeMillis() - lastChangeTime > TIMEOUT_MS) {
                            log.debug("MP3 文件 {} 在 {} 秒内无变动，退出监控", targetMp3File, TIMEOUT_MS / 1000);
                            break;
                        }
                        // 如果2.5秒内无变动，则直接将缓存剩余的进行asr
                        if (System.currentTimeMillis() - lastChangeTime > TIMEOUT_0_MS && (lastLeftActive || lastRightActive)) {
                            log.debug("MP3 文件 {} 在 {} 秒内无变动，直接去将缓存剩余的进行asr", targetMp3File, TIMEOUT_0_MS / 1000);
                            // Process remaining audio in caches for both channels
                            for (String channelName : new String[]{LEFT_CHANNEL, RIGHT_CHANNEL}) {
                                List<byte[]> segmentsToSave = channelName.equals(LEFT_CHANNEL) ? leftSegmentsToSave : rightSegmentsToSave;
                                List<byte[]> channelBuffer = channelName.equals(LEFT_CHANNEL) ? leftChannelBuffer : rightChannelBuffer;
                                String wavFile = channelName.equals(LEFT_CHANNEL) ? LEFT_WAV : RIGHT_WAV;

                                if (!segmentsToSave.isEmpty() && (channelName.equals(LEFT_CHANNEL) ? lastLeftActive : lastRightActive)) {
                                    if (channelName.equals(LEFT_CHANNEL)) {
                                        currentLeftSilenceDuration = 0.0;
                                        lastLeftActive = false;
                                    } else {
                                        currentRightSilenceDuration = 0.0;
                                        lastRightActive = false;
                                    }
                                    int totalSavedSamples = segmentsToSave.stream().mapToInt(data -> data.length / 2).sum();
                                    double savedDuration = totalSavedSamples / (double) AUDIO_RATE;
                                    log.debug("{} 剩余缓存时长: {} 秒，调用 ASR 接口...", channelName, savedDuration);

                                    String tempFileName = File_PREFIX + System.currentTimeMillis() + "_" + wavFile;
                                    File file = saveToWav(segmentsToSave, tempFileName);
                                    if (file != null && file.exists() && file.length() >= 44) {
                                        log.debug("{} WAV 文件大小: {} 字节", channelName, file.length());
                                        JSONObject jsonObject = asr2Json(file, model, null, null, null, temperature);
                                        String transcription = jsonObject != null ? jsonObject.getString("text") : null;
                                        if (transcription != null && !transcription.isEmpty()) {
                                            log.debug("{} 转录结果: {}", channelName, transcription);
                                        } else {
                                            log.debug("{} ASR 转录为空或失败", channelName);
                                        }
                                        deleteWavFile(tempFileName);
                                    } else {
                                        log.debug("{} WAV 文件 {} 无效，跳过 ASR", channelName, wavFile);
                                    }
                                    segmentsToSave.clear();
                                    channelBuffer.clear();
                                } else {
                                    log.debug("{} 剩余缓存为空，跳过 ASR", channelName);
                                    channelBuffer.clear();
                                }
                            }
                        }
                    } else {
                        lastChangeTime = System.currentTimeMillis();
                        processFileChange(mp3File);
                        lastFileSize = currentSize;
                        lastModifiedTime = currentModifiedTime;
                    }
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    log.debug("Mp3 监控中断, uuid:{}", uuid, e);
                    break;
                } catch (Exception e) {
                    log.debug("Mp3 监控错误, uuid:{}", uuid, e);
                }
            }
        } catch (Throwable e) {
            log.error("Mp3 监控错误", e);
        } finally {
            if (filePath != null && !filePath.isEmpty()) {
                deleteDirectory(filePath);
            }
        }
    }

    private void deleteDirectory(String filePath) {
        Path dirPath = Paths.get(filePath);
        if (Files.exists(dirPath)) {
            try {
                Files.walkFileTree(dirPath, new DeleteDirectoryVisitor());
                log.info("目录删除成功: {}", dirPath);
            } catch (IOException e) {
                log.error("目录删除失败: {}", dirPath, e);
            }
        }
    }

    private static class DeleteDirectoryVisitor extends SimpleFileVisitor<Path> {
        @NotNull
        @Override
        public FileVisitResult visitFile(Path file, @NotNull BasicFileAttributes attrs) throws IOException {
            Files.delete(file);
            return FileVisitResult.CONTINUE;
        }

        @NotNull
        @Override
        public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
            Files.delete(dir);
            return FileVisitResult.CONTINUE;
        }
    }

    private void processFileChange(File mp3File) {
        try {
            long currentSize = mp3File.length();
            if (currentSize <= lastFileSize) {
                return;
            }

            log.debug("检测到 MP3 文件写入: {} 字节", currentSize);

            byte[] pcmData = convertMp3ToPcm(mp3File);
            if (pcmData.length == 0) {
                log.debug("无法读取新音频数据");
                return;
            }

            byte[][] channels = splitStereoPcm(pcmData);
            byte[] leftChannel = channels[0];
            byte[] rightChannel = channels[1];

            // Store new audio directly into the respective buffer queues
            leftChannelBuffer.add(leftChannel);
            rightChannelBuffer.add(rightChannel);

            // Create a thread pool with 2 threads for concurrent processing
            ExecutorService executor = Executors.newFixedThreadPool(2);
            List<Future<?>> futures = new ArrayList<>();

            // Submit left channel processing task
            futures.add(executor.submit(() -> {
                try {
                    processChannel(leftChannelBuffer, leftSegmentsToSave, leftEnergyHistory, leftZcrHistory, LEFT_WAV, LEFT_CHANNEL);
                } catch (Exception e) {
                    log.debug("处理左声道失败", e);
                }
            }));

            // Submit right channel processing task
            futures.add(executor.submit(() -> {
                try {
                    processChannel(rightChannelBuffer, rightSegmentsToSave, rightEnergyHistory, rightZcrHistory, RIGHT_WAV, RIGHT_CHANNEL);
                } catch (Exception e) {
                    log.debug("处理右声道失败", e);
                }
            }));

            // Wait for both tasks to complete
            for (Future<?> future : futures) {
                try {
                    future.get(); // Blocks until the task completes
                } catch (Exception e) {
                    log.debug("等待声道处理任务失败", e);
                }
            }

            // 关闭线程池
            executor.shutdown();

            // 更新截取位置
            lastPosition += pcmData.length;
            lastFileSize = currentSize;
        } catch (Exception e) {
            log.debug("处理 MP3 文件失败", e);
        }
    }

    private byte[] convertMp3ToPcm(File mp3File) throws Exception {
        AudioFile audioFile = AudioFileIO.read(mp3File);
        MP3AudioHeader audioHeader = (MP3AudioHeader) audioFile.getAudioHeader();
        int sampleRate = audioHeader.getSampleRateAsNumber();
        int channels = "Stereo".equals(audioHeader.getChannels()) ? 2 : 1;
        log.info("输入音频: 采样率={} Hz, 声道数={}", sampleRate, channels);

        // 计算起始时间（基于 lastPosition）
        double startTime = lastPosition / (double) (AUDIO_RATE * channels * 2);
        log.info("从位置 {} 开始转换，起始时间: {} 秒", lastPosition, startTime);

        // 动态截取到文件末尾
        ProcessBuilder pb = new ProcessBuilder(
                "ffmpeg", "-i", mp3File.getAbsolutePath(), "-f", "s16le", "-ar", String.valueOf(AUDIO_RATE),
                "-ac", String.valueOf(channels), "-vn", "-ss", String.valueOf(startTime), "-"
        );
        Process process = pb.start();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ByteArrayOutputStream errorBaos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;

        // 读取 FFmpeg 输出
        InputStream is = process.getInputStream();
        while ((bytesRead = is.read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        process.waitFor();
        byte[] pcmData = baos.toByteArray();
        log.info("PCM 数据大小: {} 字节", pcmData.length);
        return pcmData;
    }

    private byte[][] splitStereoPcm(byte[] pcmData) {
        int sampleSize = 2;
        int numSamples = pcmData.length / (sampleSize * 2);
        if (numSamples <= 0) {
            log.debug("输入音频数据不足，可能是单声道或无效数据，复制左声道数据到右声道");
            byte[] channelData = new byte[pcmData.length];
            System.arraycopy(pcmData, 0, channelData, 0, pcmData.length);
            return new byte[][]{pcmData, channelData};
        }
        byte[] leftChannel = new byte[numSamples * sampleSize];
        byte[] rightChannel = new byte[numSamples * sampleSize];

        for (int i = 0; i < numSamples; i++) {
            leftChannel[i * sampleSize] = pcmData[i * sampleSize * 2];
            leftChannel[i * sampleSize + 1] = pcmData[i * sampleSize * 2 + 1];
            rightChannel[i * sampleSize] = pcmData[i * sampleSize * 2 + 2];
            rightChannel[i * sampleSize + 1] = pcmData[i * sampleSize * 2 + 3];
        }

        return new byte[][]{leftChannel, rightChannel};
    }


    private void processChannel(List<byte[]> buffer, List<byte[]> segmentsToSave, List<Double> energyHistory, List<Double> zcrHistory, String wavFile, String channelName) {
        // Combine all buffered data
        byte[] combinedData = concatenateByteArrays(buffer);
        if (combinedData.length == 0) {
            log.debug("{} 合并数据为空，跳过处理", channelName);
            buffer.clear();
            return;
        }

        int totalSamples = combinedData.length / 2;
        double bufferDuration = totalSamples / (double) AUDIO_RATE;
        log.debug("{} 缓冲区时长: {} 秒", channelName, bufferDuration);

        // Split into 0.2-second segments for VAD
        // 1600 samples * 2 bytes
        // 计算单片段样本
        int vadSegmentSamples = (int) (AUDIO_RATE * VAD_SEGMENT_DURATION);
        int bytesPerSegment = vadSegmentSamples * 2;
        for (int i = 0; i < combinedData.length; i += bytesPerSegment) {
            int segmentLength = Math.min(bytesPerSegment, combinedData.length - i);
            byte[] segment = new byte[segmentLength];
            System.arraycopy(combinedData, i, segment, 0, segmentLength);

            // Convert to samples for VAD
            short[] samples = new short[segmentLength / 2];
            for (int j = 0; j < samples.length; j++) {
                samples[j] = (short) ((segment[j * 2] & 0xff) | (segment[j * 2 + 1] << 8));
            }

            // Calculate energy and ZCR
            double energy = 0;
            double maxSample = 0;
            for (short sample : samples) {
                energy += sample * sample;
                maxSample = Math.max(maxSample, Math.abs(sample));
            }
            energy = (energy / samples.length) / (maxSample * maxSample + 1e-6);

            double zcr = 0;
            for (int j = 1; j < samples.length; j++) {
                if ((samples[j - 1] >= 0 && samples[j] < 0) || (samples[j - 1] < 0 && samples[j] >= 0)) {
                    zcr += 1;
                }
            }
            zcr = zcr / samples.length;

            // Update history
            energyHistory.add(energy);
            zcrHistory.add(zcr);
            if (energyHistory.size() > 100) {
                energyHistory.remove(0);
                zcrHistory.remove(0);
            }

            // Calculate dynamic thresholds
            double energyMean = energyHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double zcrMean = zcrHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double energyStd = Math.sqrt(energyHistory.stream().mapToDouble(e -> Math.pow(e - energyMean, 2)).average().orElse(0.0));
            double zcrStd = Math.sqrt(zcrHistory.stream().mapToDouble(z -> Math.pow(z - zcrMean, 2)).average().orElse(0.0));

            double energyThreshold = Math.max(energyMean - 0.05 * energyStd, 0.00005);
            double zcrThreshold = Math.max(zcrMean - 0.05 * zcrStd, 0.0005);

            double segmentDuration = ((double) segmentLength / 2) / AUDIO_RATE;
            log.debug("{} 片段 {} 时长: {} 秒, 能量值: {}, ZCR: {}, 能量阈值: {}, ZCR 阈值: {}", channelName, i / bytesPerSegment, segmentDuration, energy, zcr, energyThreshold, zcrThreshold);

            boolean vadResult = energy > energyThreshold || zcr > zcrThreshold;

            segmentsToSave.add(segment);
            if (vadResult) {
                if (LEFT_CHANNEL.equals(channelName)) {
                    currentLeftSilenceDuration = 0.0;
                    lastLeftActive = true;
                } else {
                    currentRightSilenceDuration = 0.0;
                    lastRightActive = true;
                }
                log.debug("{} 片段 {} 检测到语音活动: {}", channelName, i / bytesPerSegment, new Date());
            } else {
                if (LEFT_CHANNEL.equals(channelName)) {
                    currentLeftSilenceDuration += segmentDuration;
                    log.debug("左声道 片段 {} 静音中...，累计静音时长 {}", i / bytesPerSegment, currentLeftSilenceDuration);
                } else {
                    currentRightSilenceDuration += segmentDuration;
                    log.debug("右声道 片段 {} 静音中...，累计静音时长 {}", i / bytesPerSegment, currentRightSilenceDuration);
                }

                // Check if silence duration triggers ASR
                if (((LEFT_CHANNEL.equals(channelName) && currentLeftSilenceDuration >= MIN_ASR_DURATION) ||
                        (RIGHT_CHANNEL.equals(channelName) && currentRightSilenceDuration >= MIN_ASR_DURATION))
                        && (channelName.equals(LEFT_CHANNEL) ? lastLeftActive : lastRightActive)) {
                    int totalSavedSamples = segmentsToSave.stream().mapToInt(data -> data.length / 2).sum();
                    double savedDuration = totalSavedSamples / (double) AUDIO_RATE;
                    log.debug("{} 静音触发，segmentsToSave 时长: {} 秒，调用 ASR 接口...", channelName, savedDuration);

                    if (LEFT_CHANNEL.equals(channelName)) {
                        lastLeftActive = false;
                        currentLeftSilenceDuration = 0.0;
                    } else {
                        lastRightActive = false;
                        currentRightSilenceDuration = 0.0;
                    }

                    String tempFileName = File_PREFIX + System.currentTimeMillis() + "_" + wavFile;
                    File file = saveToWav(segmentsToSave, tempFileName);
                    if (file != null && file.exists() && file.length() >= 44) {
                        log.debug(channelName + " WAV 文件大小: {} 字节", file.length());
                        JSONObject jsonObject = asr2Json(file, model, null, null, null, temperature);
                        String transcription = jsonObject != null ? jsonObject.getString("text") : null;
                        if (transcription != null && !transcription.isEmpty()) {
                            log.info("{} psC转录结果: {}", channelName, transcription);
                        } else {
                            log.debug("{} ASR 转录为空或失败", channelName);
                        }
                        deleteWavFile(tempFileName);
                    } else {
                        log.debug("{} WAV 文件 {} 无效，跳过 ASR", channelName, wavFile);
                    }
                    segmentsToSave.clear();
                }
            }
        }

        // Clear buffer after processing
        buffer.clear();
    }

    private void deleteWavFile(String wavFile) {
        File file = new File(wavFile);
        if (file.exists()) {
            if (file.delete()) {
                log.debug("删除 WAV 文件: {}", wavFile);
            } else {
                log.debug("无法删除 WAV 文件: {}", wavFile);
            }
        }
    }

    private byte[] concatenateByteArrays(List<byte[]> arrays) {
        int totalLength = arrays.stream().mapToInt(b -> b.length).sum();
        byte[] result = new byte[totalLength];
        int offset = 0;
        for (byte[] array : arrays) {
            System.arraycopy(array, 0, result, offset, array.length);
            offset += array.length;
        }
        return result;
    }

    private File saveToWav(List<byte[]> audioData, String filename) {
        try {
            filename = filePath + filename;
            if (audioData.isEmpty()) {
                log.debug("音频数据为空，无法保存 WAV 文件: {}", filename);
                return null;
            }
            byte[] combinedData = concatenateByteArrays(audioData);
            if (combinedData.length == 0) {
                log.debug("合并后的音频数据为空，无法保存 WAV 文件: {}", filename);
                return null;
            }
            // 删除旧 WAV 文件
            File wavFile = new File(filename);
            if (wavFile.exists()) {
                wavFile.delete();
                log.debug("删除旧 WAV 文件: {}", filename);
            }
            AudioFormat format = new AudioFormat(AUDIO_RATE, 16, 1, true, false);
            AudioInputStream ais = new AudioInputStream(
                    new ByteArrayInputStream(combinedData), format, combinedData.length / 2
            );
            AudioSystem.write(ais, AudioFileFormat.Type.WAVE, wavFile);
            ais.close();
            if (wavFile.exists() && wavFile.length() >= 44) {
                return wavFile;
            } else {
                log.debug("WAV 文件 {} 保存失败或无效（大小: {} 字节）", filename, wavFile.length());
                return null;
            }
        } catch (IOException e) {
            log.error("保存 WAV 文件失败: {}，错误: {}", filename, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("保存 WAV 文件时发生未知错误: {}，错误: {}", filename, e.getMessage());
            return null;
        }
    }

    public JSONObject asr2Json(File file, String model, Integer num, String hotwords, String prompt, Float temperature) {
        try {
            if (!file.exists() || file.length() < 44) {
                log.debug("音频文件 {} 不存在或无效（大小: {} 字节）", file.getName(), file.length());
                return null;
            }

            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", file.getName(), RequestBody.create(file, MediaType.parse("audio/wav")))
                    .addFormDataPart("version", "v3")
                    .addFormDataPart("response_format", "json")
                    .addFormDataPart("model", model);

            String defaultHotwords = "固收,类固收,小额001,大额001,宜人,大固,小固,指旺,安易盈,新手专享,新手标,优选,宜优选,认购,起投,券码,福卡,晒单," +
                    "宜人币,奖励金,积分,宜信,逛逛,宜享分,续投,起拍,转投,1688会员,399会员,特供,募集,竞拍,长盈宝,AI会员,小金罐,派息,付息,信贷,日日盈,618," +
                    "信美,首购,代金券,债权,旅居,福袋,长险,药诊卡,提额,旅居卡,宜路相伴,退休金,臻享,宜脉传承,翔旅保,臻选,宜脉相承,宝贝存钱罐,宜脉相传,弘康," +
                    "抽积分,智选,会员日,初冬礼遇,传家有道,宜人优选,健康节,创信,宜她保,社群,老师,平台,产品,风险,财报,经济,文件大小,盈利,保值,理财,网贷," +
                    "合规合法,政策,投资,大额,小额,定期,股票,基金,美元,货币,对冲率,特朗普,微信,企微";
            builder.addFormDataPart("hotwords", StrUtil.isBlank(hotwords) ? defaultHotwords : hotwords);

            if (Objects.nonNull(num)) {
                builder.addFormDataPart("oracle_num", String.valueOf(num));
            }
            if (StrUtil.isNotBlank(prompt)) {
                builder.addFormDataPart("prompt", prompt);
            }
            if (Objects.nonNull(temperature)) {
                builder.addFormDataPart("temperature", String.valueOf(temperature));
            }

            Request request = new Request.Builder()
                    .url(url + "/v1/audio/transcriptions")
                    .header("Authorization", key)
                    .post(builder.build())
                    .build();

            log.debug("asr2Json 调用ChatAPI接口进行音转文开始, model={}, fileName={}", model, file.getName());

            try (Response response = client.newCall(request).execute()) {
                int responseCode = response.code();
                String responseBody = response.body() != null ? response.body().string() : "";
                log.debug("asr2Json 调用ChatAPI接口进行音转文结束, model={}, fileName={}, responseCode={}, response={}",
                        model, file.getName(), responseCode, responseBody);

                if (responseCode != 200) {
                    log.error("asr2Json ChatAPI 返回错误状态码: {}, 错误信息: {}", responseCode, response.message());
                    return null;
                }

                if (StrUtil.isBlank(responseBody)) {
                    log.debug("asr2Json ChatAPI 返回空响应");
                    return null;
                }

                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                if (jsonObject == null || jsonObject.getInteger("code") != null || StrUtil.isNotBlank(jsonObject.getString("debug_message"))) {
                    log.error("asr2Json 调用 ChatAPI 返回错误响应: {}", jsonObject);
                    return null;
                }

                return jsonObject;
            }
        } catch (Exception e) {
            log.debug("asr2Json 调用ChatAPI接口进行音转文发生异常, 异常原因为: {}", e.getMessage(), e);
            return null;
        }
    }

    public static void main(String[] args) {
        try {
            Mp3VadAsrProcessorTest mp3VadAsrProcessor = new Mp3VadAsrProcessorTest();
            mp3VadAsrProcessor.initProcessor("278576", "1869687765910835200");
            mp3VadAsrProcessor.startMonitoring();
        } catch (Exception e) {
            log.debug("启动监控失败", e);
        }
        // 转录结果
    }
}