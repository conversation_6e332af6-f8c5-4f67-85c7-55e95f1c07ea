package com.yirendai.voiceaiserver.biz.asr;

import javax.sound.sampled.*;
import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.*;

public class FixedMP3Splitter {
    private static final int BUFFER_SIZE = 8192;
    private static final int SAMPLE_RATE = 44100;
    private volatile boolean running = true;

    // 改进的实时处理方法
    public void processRealtimeAudio(Path inputMP3,
                                     Path leftWav,
                                     Path rightWav) throws Exception {

        // 初始化WAV文件头
        initWavFile(leftWav);
        initWavFile(rightWav);

        try (FileChannel inChannel = FileChannel.open(inputMP3,
                StandardOpenOption.READ, StandardOpenOption.WRITE)) {

            try (RandomAccessFile leftRAF = new RandomAccessFile(leftWav.toFile(), "rw");
                 RandomAccessFile rightRAF = new RandomAccessFile(rightWav.toFile(), "rw")) {

                long lastPos = 0;
                ByteBuffer buffer = ByteBuffer.allocate(BUFFER_SIZE);

                while (running) {
                    long currentSize = inChannel.size();
                    if (currentSize > lastPos) {
                        inChannel.position(lastPos);
                        int bytesRead = inChannel.read(buffer);
                        if (bytesRead > 0) {
                            processAudioChunk(buffer.array(), bytesRead, leftRAF, rightRAF);
                            lastPos += bytesRead;
                            buffer.clear();
                        }
                    }
                    Thread.sleep(300);
                }

                // 最终更新文件头
                updateWavHeaders(leftRAF);
                updateWavHeaders(rightRAF);
            }
        }
    }

    // 初始化WAV文件头
    private void initWavFile(Path path) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(path.toFile(), "rw")) {
            byte[] header = createWavHeader(0);
            raf.write(header);
        }
    }

    // 创建WAV文件头
    private byte[] createWavHeader(int dataLength) {
        ByteArrayOutputStream header = new ByteArrayOutputStream();
        // WAV头规范‌:ml-citation{ref="7,8" data="citationList"}
        try {
            header.write("RIFF".getBytes()); // ChunkID
            header.write(intToByteArray(36 + dataLength)); // ChunkSize
            header.write("WAVE".getBytes()); // Format
            header.write("fmt ".getBytes()); // Subchunk1ID
            header.write(intToByteArray(16)); // Subchunk1Size
            header.write(shortToByteArray((short) 1)); // AudioFormat(PCM)
            header.write(shortToByteArray((short) 1)); // NumChannels(单声道)
            header.write(intToByteArray(SAMPLE_RATE)); // SampleRate
            header.write(intToByteArray(SAMPLE_RATE * 2)); // ByteRate
            header.write(shortToByteArray((short) 2)); // BlockAlign
            header.write(shortToByteArray((short) 16)); // BitsPerSample
            header.write("data".getBytes()); // Subchunk2ID
            header.write(intToByteArray(dataLength)); // Subchunk2Size
        } catch (IOException e) {
            e.printStackTrace();
        }
        return header.toByteArray();
    }

    // 更新WAV头信息
    private void updateWavHeaders(RandomAccessFile raf) throws IOException {
        long fileLength = raf.length();
        raf.seek(4); // 更新ChunkSize
        raf.write(intToByteArray((int)(fileLength - 8)));
        raf.seek(40); // 更新DataSize
        raf.write(intToByteArray((int)(fileLength - 44)));
    }

    // PCM数据处理与声道分离
    private void processAudioChunk(byte[] mp3Data,
                                   int length,
                                   RandomAccessFile leftOut,
                                   RandomAccessFile rightOut) throws Exception {

        try (AudioInputStream mp3Stream = AudioSystem.getAudioInputStream(
                new ByteArrayInputStream(mp3Data, 0, length))) {

            AudioFormat targetFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,
                    SAMPLE_RATE,
                    16,
                    2,
                    4,
                    SAMPLE_RATE,
                    false
            );

            try (AudioInputStream pcmStream = AudioSystem.getAudioInputStream(targetFormat, mp3Stream)) {
                byte[] pcmBuffer = new byte[4096];
                int bytesRead;

                while ((bytesRead = pcmStream.read(pcmBuffer)) != -1) {
                    for (int i=0; i<bytesRead; i+=4) {
                        if (i+3 >= bytesRead) break;

                        // 写入左声道
                        leftOut.write(pcmBuffer, i, 2);
                        // 写入右声道
                        rightOut.write(pcmBuffer, i+2, 2);
                    }
                }
            }
        }
    }

    // 字节转换工具方法
    private static byte[] intToByteArray(int value) {
        return new byte[] {
                (byte)(value & 0xff),
                (byte)((value >> 8) & 0xff),
                (byte)((value >> 16) & 0xff),
                (byte)((value >> 24) & 0xff)
        };
    }

    private static byte[] shortToByteArray(short value) {
        return new byte[] {
                (byte)(value & 0xff),
                (byte)((value >> 8) & 0xff)
        };
    }

    public static void main(String[] args) throws Exception {
        FixedMP3Splitter splitter = new FixedMP3Splitter();
        splitter.processRealtimeAudio(
                Paths.get("/Users/<USER>/2025年/1月/1/yrcall_1869687765910835200.mp3"),
                Paths.get("/Users/<USER>/2025年/1月/1/left.wav"),
                Paths.get("/Users/<USER>/2025年/1月/1/right.wav")
        );
    }
}