package com.yirendai.voiceaiserver.biz.asr;

import com.yirendai.voiceaiserver.tripartite.zhiyu.ws.ASRWebsocketRecognizer;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.*;
import java.nio.channels.SeekableByteChannel;
import java.nio.file.*;

@Component
@Slf4j
public class RealtimeMp3SplitToFunASR {


    @Autowired
    @Qualifier("asrOnlineThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    private FileOutputStream leftFileStream;
    private FileOutputStream rightFileStream;

    private File leftFile;
    private File rightFile;

    // 2. 文件变化监听
    public void watchFileChanges(String filePath, ASRWebsocketRecognizer rcgLeft, ASRWebsocketRecognizer rcgRight) {
        try (WatchService watchService = FileSystems.getDefault().newWatchService()) {
            Path path = Paths.get(filePath).getParent();
            path.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);
            long lastFilePosition = 0;
            while (true) {
                WatchKey key = watchService.take();
                for (WatchEvent<?> event : key.pollEvents()) {
                    if (event.context().toString().equals(Paths.get(filePath).getFileName().toString())) {
                        lastFilePosition = processNewAudioData(filePath, rcgLeft, rcgRight, lastFilePosition);
                    }
                }
                key.reset();
            }
        } catch (Exception e) {
            log.error("Error in watchFileChanges: filePath={} msg = {} ", filePath, e.getMessage(), e);
        }
    }

    //直接读取文件并处理 (测试使用)

    // 6. 直接读取文件并处理 (测试使用)
    public void readAndProcessFile(String filePath, ASRWebsocketRecognizer rcgLeft, ASRWebsocketRecognizer rcgRight) {
        try (SeekableByteChannel channel = Files.newByteChannel(Paths.get(filePath), StandardOpenOption.READ)) {
            long currentSize = channel.size();
            if (currentSize <= 0) {
                log.info("File is empty or does not exist: {}", filePath);
                return;
            }
            log.info("Start processing file: {}", filePath);
            // 定义分块大小（例如 1 MB）
            int chunkSize = 1024 * 1024; // 1 MB
            ByteBuffer buffer = ByteBuffer.allocate(chunkSize);

            while (channel.position() < currentSize) {
                buffer.clear();
                int bytesRead = channel.read(buffer);
                if (bytesRead == -1) {
                    break;
                }
                buffer.flip(); // 准备读取

                // 启动流处理线程
                byte[] chunk = new byte[bytesRead];
                buffer.get(chunk);
                threadPoolTaskExecutor.submit(() -> {
                    try {
                        processAudioStream(chunk, rcgLeft, rcgRight);
                    } catch (IOException e) {
                        log.error("Error in processNewAudioData: filePath={} msg = {} ", filePath, e.getMessage(), e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("Error in readAndProcessFile: filePath={} msg = {}", filePath, e.getMessage(), e);
        }
    }

    // 3. 处理新增音频数据
    private long processNewAudioData(String filePath, ASRWebsocketRecognizer rcgLeft, ASRWebsocketRecognizer rcgRight, long lastFilePosition) {
        try (SeekableByteChannel channel = Files.newByteChannel(Paths.get(filePath), StandardOpenOption.READ)) {
            long currentSize = channel.size();
            if (currentSize <= lastFilePosition) {
                log.info("No new data in the file: {}", filePath);
                return lastFilePosition;
            }

            // 只读取新增部分
            ByteBuffer buffer = ByteBuffer.allocate((int) (currentSize - lastFilePosition));
            channel.position(lastFilePosition);
            channel.read(buffer);
            lastFilePosition = currentSize;

            // 启动流处理线程
            threadPoolTaskExecutor.submit(() -> {
                try {
                    processAudioStream(buffer.array(), rcgLeft, rcgRight);
                } catch (IOException e) {
                    log.error("Error in processNewAudioData: filePath={} msg = {} ", filePath, e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            log.error("Error in processNewAudioData: filePath={} msg = {} ", filePath, e.getMessage(), e);
        }
        return lastFilePosition;
    }

    // 4. 实时解码并拆分声道
    private void processAudioStream(byte[] mp3Chunk, ASRWebsocketRecognizer rcgLeft, ASRWebsocketRecognizer rcgRight) throws IOException {
        log.info("processAudioStream started mp3Chunk.length={}", mp3Chunk.length);

        Path tempFile = Files.createTempFile("audio", ".mp3");
        Files.write(tempFile, mp3Chunk);
        log.info("Temporary file created: {}", tempFile);

        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(tempFile.toFile())) {
            // 强制配置为立体声
            grabber.setAudioChannels(2);
            grabber.start();

            // 初始化文件输出流
            leftFile = new File("/callVoice/test/left.mp3");
            rightFile = new File("/callVoice/test/right.mp3");

            if (!leftFile.exists()) {
                leftFile.createNewFile();
            }
            if (!rightFile.exists()) {
                rightFile.createNewFile();
            }

            leftFileStream = new FileOutputStream(leftFile, true);
            rightFileStream = new FileOutputStream(rightFile, true);



            Frame frame;
            while ((frame = grabber.grab()) != null) {

                // 验证声道数
                if (frame.audioChannels != 2) {
                    throw new IllegalStateException("非立体声音频，实际声道数: " + frame.audioChannels);
                }

                Buffer[] buffers = frame.samples;

                // 处理交错格式（单 Buffer）
                if (buffers.length == 1) {
                    log.info("处理交错格式（单 Buffer）");
                    if (buffers[0] instanceof ShortBuffer) {
                        ShortBuffer interleavedBuffer = (ShortBuffer) buffers[0];
                        splitInterleavedShortData(interleavedBuffer, rcgLeft, rcgRight);
                    } else if (buffers[0] instanceof FloatBuffer) {
                        FloatBuffer interleavedBuffer = (FloatBuffer) buffers[0];
                        splitInterleavedData(interleavedBuffer, rcgLeft, rcgRight);
                    } else {
                        throw new IllegalStateException("未知音频样本类型: " + buffers[0].getClass().getName());
                    }
                }
                // 处理平面格式（双 Buffer）
                else if (buffers.length == 2) {
                    log.info("处理平面格式（双 Buffer）");
                    if (buffers[0] instanceof ShortBuffer && buffers[1] instanceof ShortBuffer) {
                        ShortBuffer leftBuffer = (ShortBuffer) buffers[0];
                        ShortBuffer rightBuffer = (ShortBuffer) buffers[1];
                        processPlanarShortData(leftBuffer, rightBuffer, rcgLeft, rcgRight);
                    } else if (buffers[0] instanceof FloatBuffer && buffers[1] instanceof FloatBuffer) {
                        FloatBuffer leftBuffer = (FloatBuffer) buffers[0];
                        FloatBuffer rightBuffer = (FloatBuffer) buffers[1];
                        processPlanarData(leftBuffer, rightBuffer, rcgLeft, rcgRight);
                    } else {
                        throw new IllegalStateException("未知音频样本类型: " + buffers[0].getClass().getName() + ", " + buffers[1].getClass().getName());
                    }
                } else {
                    throw new IllegalStateException("未知音频格式，Buffer 数: " + buffers.length);
                }
            }
        } catch (Exception e) {
            log.error("Error in processAudioStream: ", e);
        } finally {
            Files.deleteIfExists(tempFile); // 清理临时文件
            // 关闭文件输出流
            if (leftFileStream != null) {
                leftFileStream.close();
            }
            if (rightFileStream != null) {
                rightFileStream.close();
            }
        }
    }

    // 处理交错格式（单 Buffer，交替存储左右声道） - ShortBuffer
    private void splitInterleavedShortData(
            ShortBuffer interleavedBuffer,
            ASRWebsocketRecognizer rcgLeft,
            ASRWebsocketRecognizer rcgRight
    ) throws IOException, InterruptedException {
        int totalSamples = interleavedBuffer.remaining();
        short[] left = new short[totalSamples / 2];
        short[] right = new short[totalSamples / 2];

        for (int i = 0; i < totalSamples; i += 2) {
            left[i / 2] = interleavedBuffer.get(i);
            right[i / 2] = interleavedBuffer.get(i + 1);
        }
        log.info("splitInterleavedShortData: left.length={} right.length={}", left.length, right.length);
        sendShortToRecognizer(rcgLeft, left,leftFileStream);
        sendShortToRecognizer(rcgRight, right,rightFileStream);
    }

    // 处理交错格式（单 Buffer，交替存储左右声道） - FloatBuffer
    private void splitInterleavedData(
            FloatBuffer interleavedBuffer,
            ASRWebsocketRecognizer rcgLeft,
            ASRWebsocketRecognizer rcgRight
    ) throws IOException, InterruptedException {
        int totalSamples = interleavedBuffer.remaining();
        float[] left = new float[totalSamples / 2];
        float[] right = new float[totalSamples / 2];

        for (int i = 0; i < totalSamples; i += 2) {
            left[i / 2] = interleavedBuffer.get(i);
            right[i / 2] = interleavedBuffer.get(i + 1);
        }
        log.info("splitInterleavedData: left.length={} right.length={}", left.length, right.length);
        sendToRecognizer(rcgLeft, left,leftFileStream);
        sendToRecognizer(rcgRight, right,rightFileStream);
    }

    // 处理平面格式（双 Buffer，各存一个声道） - ShortBuffer
    private void processPlanarShortData(
            ShortBuffer leftBuffer,
            ShortBuffer rightBuffer,
            ASRWebsocketRecognizer rcgLeft,
            ASRWebsocketRecognizer rcgRight
    ) throws IOException, InterruptedException {
        short[] left = new short[leftBuffer.remaining()];
        short[] right = new short[rightBuffer.remaining()];
        leftBuffer.get(left);
        rightBuffer.get(right);
        log.info("processPlanarShortData: left.length={} right.length={}", left.length, right.length);
        sendShortToRecognizer(rcgLeft, left,leftFileStream);
        sendShortToRecognizer(rcgRight, right,rightFileStream);
    }

    // 处理平面格式（双 Buffer，各存一个声道） - FloatBuffer
    private void processPlanarData(
            FloatBuffer leftBuffer,
            FloatBuffer rightBuffer,
            ASRWebsocketRecognizer rcgLeft,
            ASRWebsocketRecognizer rcgRight
    ) throws IOException, InterruptedException {
        float[] left = new float[leftBuffer.remaining()];
        float[] right = new float[rightBuffer.remaining()];
        leftBuffer.get(left);
        rightBuffer.get(right);
        log.info("processPlanarData: left.length={} right.length={}", left.length, right.length);
        sendToRecognizer(rcgLeft, left,leftFileStream);
        sendToRecognizer(rcgRight, right,rightFileStream);
    }

    // 量化并发送到识别器（通用方法） - FloatBuffer
    private void sendToRecognizer(ASRWebsocketRecognizer recognizer, float[] samples,FileOutputStream fileStream ) throws IOException, InterruptedException {
        ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
        ByteBuffer buffer = ByteBuffer.allocate(samples.length * 2)
                .order(ByteOrder.LITTLE_ENDIAN);
        log.info("sendToRecognizer: samples.length={}", samples.length);
        for (float sample : samples) {
            // 限幅处理防止溢出
            float clamped = Math.max(-1.0f, Math.min(1.0f, sample));
            short quantized = (short) (clamped * 32767.0f);
            buffer.putShort(quantized);
        }

        byte[] chunk = buffer.array();
        log.info("sendToRecognizer: chunk.length={}", chunk.length);
        log.info("sendToRecognizer: first 10 bytes: {}", bytesToHex(chunk, 10));
        recognizer.feedChunk(chunk);
        // 将数据写入文件
        if (fileStream != null) {
            log.info("写入文件 {}",chunk.length);
            fileStream.write(chunk);
        }
    }

    // 量化并发送到识别器（通用方法） - ShortBuffer
    private void sendShortToRecognizer(ASRWebsocketRecognizer recognizer, short[] samples,FileOutputStream fileStream) throws IOException, InterruptedException {
        ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
        ByteBuffer buffer = ByteBuffer.allocate(samples.length * 2)
                .order(ByteOrder.LITTLE_ENDIAN);
        log.info("sendShortToRecognizer: samples.length={}", samples.length);
        for (short sample : samples) {
            buffer.putShort(sample);
        }

        byte[] chunk = buffer.array();
        log.info("sendShortToRecognizer: chunk.length={}", chunk.length);
        log.info("sendShortToRecognizer: first 10 bytes: {}", bytesToHex(chunk, 10));
        recognizer.feedChunk(chunk);
        // 将数据写入文件
        if (fileStream != null) {
            log.info("写入文件 {}",chunk.length);
            fileStream.write(chunk);
        }
    }

    // 辅助方法：将字节数组转换为十六进制字符串
    private String bytesToHex(byte[] bytes, int limit) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < Math.min(bytes.length, limit); i++) {
            sb.append(String.format("%02X ", bytes[i]));
        }
        return sb.toString();
    }

}
