package com.yirendai.voiceaiserver.biz.asr;

import com.yirendai.workbench.mapper.AiPlannerChatContactSubMapper;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@RefreshScope
public class MP3VadProcessFactory {
    @Resource
    private CallcenterCallRecordService callcenterCallRecordService;

    @Resource
    private IAiPlannerChatContactService iAiPlannerChatContactService;

    @Resource
    private AiPlannerChatContactSubMapper aiPlannerChatContactSubMapper;

    @Value("${yr_asr.model.url}")
    private String url = "http://aigc-api.aigc.paas.corp";

    @Value("${yr_asr.model.key}")
    private String key = "sk-itQdczJ3KcYNsn4OaO8F6M2MTfb887nGYay8E6oZjOSSAz9Q";

    @Value("${yr_asr.path:/yrCallVoice/asr/}")
    private String tempDir = "/Users/<USER>/IdeaProjects/fortune_activity_admin/";
    // 音频参数
    @Value("${yr_asr.audio.rate}")
    private final int AUDIO_RATE = 8000;
    @Value("${yr_asr.timeout.short}")
    private final long TIMEOUT_0_MS = 2200;
    // 10秒超时
    @Value("${yr_asr.timeout.long}")
    private final long TIMEOUT_MS = 10000;
    // 每个 VAD 片段 0.2 秒
    @Value("${yr_asr.vad.segment}")
    private final double VAD_SEGMENT_DURATION = 0.2;
    // 最小转录时长 0.4 秒
    @Value("${yr_asr.vad.asr}")
    private final double MIN_ASR_DURATION = 0.4;
    @Value("${yr_asr.model.name}")
    private String model = "funasr";
    @Value("${yr_asr.model.temperature}")
    private float temperature = 0.2f;
    @Value("${yr_asr.max_time}")
    private int maxDurationMs = 3 * 60 * 60 * 1000;
    @Value("${yr_asr.vad.fast_slice_time}")
    private double timeFastSliceAsrTime = 3.6;
    @Value("${yr_asr.vad.fast_slice_multi}")
    private double timeFastSliceAsrMulti = 1.2;
    @Value("${yr_asr.vad.clear_inactive_time}")
    private double timeClearInactiveTime = 5.0;
    @Value("${yr_asr.vad.max_asr_time}")
    private double maxAsrTime = 8.0;

    @Autowired
    @Qualifier("asrOnlineChannelThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public Mp3VadAsrProcessor createMp3VadAsrProcessor() {
        Mp3VadAsrProcessor mp3VadAsrProcessor = new Mp3VadAsrProcessor();
        mp3VadAsrProcessor.setCallcenterCallRecordService(callcenterCallRecordService);
        mp3VadAsrProcessor.setIAiPlannerChatContactService(iAiPlannerChatContactService);
        mp3VadAsrProcessor.setAiPlannerChatContactSubMapper(aiPlannerChatContactSubMapper);
        mp3VadAsrProcessor.setUrl(url);
        mp3VadAsrProcessor.setKey(key);
        mp3VadAsrProcessor.setTempDir(tempDir);
        mp3VadAsrProcessor.setAUDIO_RATE(AUDIO_RATE);
        mp3VadAsrProcessor.setTIMEOUT_0_MS(TIMEOUT_0_MS);
        mp3VadAsrProcessor.setTIMEOUT_MS(TIMEOUT_MS);
        mp3VadAsrProcessor.setVAD_SEGMENT_DURATION(VAD_SEGMENT_DURATION);
        mp3VadAsrProcessor.setMIN_ASR_DURATION(MIN_ASR_DURATION);
        mp3VadAsrProcessor.setModel(model);
        mp3VadAsrProcessor.setTemperature(temperature);
        mp3VadAsrProcessor.setMaxDurationMs(maxDurationMs);
        mp3VadAsrProcessor.setThreadPoolTaskExecutor(threadPoolTaskExecutor);
        mp3VadAsrProcessor.setTimeFastSliceAsrTime(timeFastSliceAsrTime);
        mp3VadAsrProcessor.setTimeFastSliceAsrMulti(timeFastSliceAsrMulti);
        mp3VadAsrProcessor.setTimeClearInactiveTime(timeClearInactiveTime);
        mp3VadAsrProcessor.setMaxAsrTime(maxAsrTime);
        return mp3VadAsrProcessor;
    }


}
