package com.yirendai.voiceaiserver.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.enums.CustomerDataSourceTypeEnum;
import com.yirendai.workbench.enums.AnalysisTaskCallRecordStatusEnum;
import com.yirendai.voiceaiserver.enums.AnalysisTaskEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.service.IAiAnalysisTaskDataService;
import com.yirendai.voiceaiserver.util.TaskIdUtil;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.entity.AiAnalysisTask;
import com.yirendai.workbench.entity.AiAnalysisTaskCallRecord;
import com.yirendai.workbench.entity.AiAnalysisTaskData;
import com.yirendai.workbench.entity.AiAnalysisTaskResult;
import com.yirendai.workbench.mapper.AiAnalysisTaskCallRecordMapper;
import com.yirendai.workbench.mapper.AiAnalysisTaskMapper;
import com.yirendai.workbench.mapper.AiAnalysisTaskResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AiAnalysisTaskBiz {
    @Resource
    private AiAnalysisTaskMapper aiAnalysisTaskMapper;

    @Resource
    private AiAnalysisTaskCallRecordMapper aiAnalysisTaskCallRecordMapper;

    @Resource
    private AiAnalysisTaskResultMapper aiAnalysisTaskResultMapper;

    @Autowired
    private AIConversationProcessService aiConversationProcessService;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private WebHookUtil webHookUtil;

    @Autowired
    private IAiAnalysisTaskDataService aiAnalysisTaskDataService;

    public static final String AI_ANALYSIS_TASK_LOCK = "AI:ANALYSIS:TASK:TENANT_LOCK_%s";
    public static final String AI_ANALYSIS_CUSTOMER_TASK_LOCK = "AI:ANALYSIS:CUSTOMER_TASK:TENANT_LOCK_%s";

    public List<String> selectTenantId() {
        return aiAnalysisTaskMapper.selectTenantId();
    }

    /**
     * 执行AI分析任务
     *
     * @param tenantId
     */
    @Async("commonThreadPoolExecutor")
    public void execute(String tenantId) {
        List<AiAnalysisTask> aiAnalysisTaskList = aiAnalysisTaskMapper.selectByTenantIdGroupByUser(tenantId);
        if (CollectionUtils.isEmpty(aiAnalysisTaskList)) {
            log.info("AiAnalysisTaskBiz execute  没有找到任务 tenantId={} ", tenantId);
            return;
        }
        String lockKey = String.format(AI_ANALYSIS_TASK_LOCK, tenantId);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean lockFlag = lock.tryLock(3, 10 * 60, TimeUnit.SECONDS);
            if (!lockFlag) {
                log.info("AiAnalysisTaskBiz execute 执行AI分析任务没有获取锁 tenantId={} ", tenantId);
                return;
            }
            for(AiAnalysisTask aiAnalysisTask : aiAnalysisTaskList){
                log.info("开始执行AI分析任务。 taskId={} ", aiAnalysisTask.getId());

                log.info("执行租户：{} 的执行AI分析任务开始...", tenantId);

                List<AiAnalysisTaskCallRecord> aiAnalysisTaskCallRecordList = aiAnalysisTaskCallRecordMapper.selectTop100ByTaskId(aiAnalysisTask.getId());
                if (CollectionUtils.isEmpty(aiAnalysisTaskCallRecordList)) {
                    log.info("AiAnalysisTaskBiz execute  没有要执行AI分析的通话数据，把任务改成完成。 tenantId={} ", tenantId);
                    updateTaskStatus(aiAnalysisTask.getId(), AnalysisTaskEnum.COMPLETED.getCode());
                    return;
                } else {
                    if (AnalysisTaskEnum.NOT_STARTED.getCode().equals(aiAnalysisTask.getStatus())) {
                        updateTaskStatus(aiAnalysisTask.getId(), AnalysisTaskEnum.IN_PROGRESS.getCode());
                    }
                }


                log.info("AiAnalysisTaskBiz execute  开始执行AI分析任务。 tenantId={}  taskId={}", tenantId, aiAnalysisTask.getId());

                for (AiAnalysisTaskCallRecord aiAnalysisTaskCallRecord : aiAnalysisTaskCallRecordList) {
                    log.info("开始执行AI分析任务。 taskId={} callRecordId={}", aiAnalysisTaskCallRecord.getAnalysisTaskId(), aiAnalysisTaskCallRecord.getId());
                    List<AiAnalysisTaskResult> aiAnalysisTaskResultList = aiAnalysisTaskResultMapper.listByCallRecordId(aiAnalysisTaskCallRecord.getId(), null);
                    int count = aiAnalysisTaskResultList.size();
                    //已完成条数
                    //int finalCount = 0;
                    //成功条数
                    int successCount = 0;
                    // 3失败条数
                    int failedCount = 0;
                    for (AiAnalysisTaskResult aiAnalysisTaskResult : aiAnalysisTaskResultList) {
                        switch (AnalysisTaskCallRecordStatusEnum.fromCode(aiAnalysisTaskResult.getStatus())) {
                            case NOT_STARTED:
                                AiConversationProcessReq requestData = AiConversationProcessReq.builder()
                                        .chatContactId(aiAnalysisTaskResult.getId())
                                        .tenantId(aiAnalysisTask.getTenantId())
                                        .taskType(AiConversationProcessTypeEnum.AI_TASK_CONTENT_ANALYSIS).build();
                                aiConversationProcessService.submitTask(requestData);
                                AiAnalysisTaskResult resultUpdate = new AiAnalysisTaskResult();
                                resultUpdate.setId(aiAnalysisTaskResult.getId());
                                resultUpdate.setStatus(AnalysisTaskCallRecordStatusEnum.IN_PROGRESS.getCode());
                                aiAnalysisTaskResultMapper.updateById(resultUpdate);
                                break;
                            case COMPLETED:
                                successCount++;
                                break;
                            case FAILED:
                                failedCount++;
                                break;
                            default:
                                break;
                        }
//                    if (aiAnalysisTaskResult.getFinalStatus() == 1) {
//                        finalCount++;
//                    }
                    }
                    if (AnalysisTaskCallRecordStatusEnum.IN_PROGRESS.getCode().equals(aiAnalysisTaskCallRecord.getStatus())) {
                        if (count == failedCount+successCount && failedCount > 0) {
                            updateTaskCallRecordStatus(aiAnalysisTaskCallRecord.getId(), AnalysisTaskCallRecordStatusEnum.FAILED.getCode(), LocalDateTime.now());
                        } else if (count == successCount) {
                            updateTaskCallRecordStatus(aiAnalysisTaskCallRecord.getId(), AnalysisTaskCallRecordStatusEnum.COMPLETED.getCode(), LocalDateTime.now());
                        }
                    } else {
                        updateTaskCallRecordStatus(aiAnalysisTaskCallRecord.getId(), AnalysisTaskCallRecordStatusEnum.IN_PROGRESS.getCode(), null);

                    }

                }
            }

        } catch (Exception ex) {
            log.error("AiAnalysisTaskBiz execute  执行AI分析任务发生异常，异常原因为", ex);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_CALL_RECORD_ERROR, "执行AI分析任" + tenantId, "执行AI分析任务发生异常", ex);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    public void updateTaskStatus(Long taskId, Integer status) {
        AiAnalysisTask update = new AiAnalysisTask();
        update.setId(taskId);
        update.setStatus(status);
        update.setUpdateTime(LocalDateTime.now());
        log.info("更新任务状态：{}", update);
        aiAnalysisTaskMapper.updateById(update);
    }

    public void updateTaskCallRecordStatus(Long callRecordId, Integer status, LocalDateTime analysisTime) {
        AiAnalysisTaskCallRecord update = new AiAnalysisTaskCallRecord();
        update.setId(callRecordId);
        update.setStatus(status);
        update.setUpdateTime(LocalDateTime.now());
        update.setAnalysisTime(analysisTime);
        log.info("更新任务通话记录状态：{}", update);
        aiAnalysisTaskCallRecordMapper.updateById(update);
    }

    /**
     * 执行客户分析任务（分析类型为1）
     *
     * @param tenantId 租户ID
     */
    @Async("commonThreadPoolExecutor")
    public void executeCustomer(String tenantId) {
        // 查询分析类型为1（客户ID类型）的任务
        LambdaQueryWrapper<AiAnalysisTask> taskQueryWrapper = new LambdaQueryWrapper<>();
        taskQueryWrapper.eq(AiAnalysisTask::getTenantId, tenantId)
                // 分析类型为1
                .eq(AiAnalysisTask::getAnalysisType, 1)
                .in(AiAnalysisTask::getStatus, AnalysisTaskEnum.NOT_STARTED.getCode(), AnalysisTaskEnum.IN_PROGRESS.getCode())
                .orderByAsc(AiAnalysisTask::getCreateTime);

        List<AiAnalysisTask> aiAnalysisTaskList = aiAnalysisTaskMapper.selectList(taskQueryWrapper);
        if (CollectionUtils.isEmpty(aiAnalysisTaskList)) {
            log.info("AiAnalysisTaskBiz executeCustomer 没有找到客户分析任务 tenantId={}", tenantId);
            return;
        }

        String lockKey = String.format(AI_ANALYSIS_CUSTOMER_TASK_LOCK, tenantId);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean lockFlag = lock.tryLock(3, 10 * 60, TimeUnit.SECONDS);
            if (!lockFlag) {
                log.info("AiAnalysisTaskBiz executeCustomer 执行客户分析任务没有获取锁 tenantId={}", tenantId);
                return;
            }

            for (AiAnalysisTask aiAnalysisTask : aiAnalysisTaskList) {
                log.info("开始执行客户分析任务。taskId={}", aiAnalysisTask.getId());

                // 更新任务状态为进行中
                if (AnalysisTaskEnum.NOT_STARTED.getCode().equals(aiAnalysisTask.getStatus())) {
                    updateTaskStatus(aiAnalysisTask.getId(), AnalysisTaskEnum.IN_PROGRESS.getCode());
                }

                // 1. 查询所有未开始的CallRecord记录
                LambdaQueryWrapper<AiAnalysisTaskCallRecord> callRecordQueryWrapper = new LambdaQueryWrapper<>();
                callRecordQueryWrapper.eq(AiAnalysisTaskCallRecord::getAnalysisTaskId, aiAnalysisTask.getId())
                        .eq(AiAnalysisTaskCallRecord::getStatus, AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode())
                        .eq(AiAnalysisTaskCallRecord::getIsDeleted, 0)
                        .last("LIMIT 100");

                List<AiAnalysisTaskCallRecord> callRecordList = aiAnalysisTaskCallRecordMapper.selectList(callRecordQueryWrapper);

                if (CollectionUtils.isEmpty(callRecordList)) {
                    log.info("AiAnalysisTaskBiz executeCustomer 没有待执行的call_record记录，检查任务完成状态。taskId={}", aiAnalysisTask.getId());
                    // 检查任务是否完成
                    checkAndUpdateTaskStatus(aiAnalysisTask.getId(), tenantId);
                    continue;
                }

                // 2. 为每个CallRecord发起任务
                for (AiAnalysisTaskCallRecord callRecord : callRecordList) {
                    try {
                        // 获取数据源类型
                        String dataSourceTypeCode = callRecord.getDataSourceType();
                        CustomerDataSourceTypeEnum dataSourceType = CustomerDataSourceTypeEnum.getByCode(dataSourceTypeCode);
                        if (dataSourceType == null) {
                            log.warn("无效的数据源类型: callRecordId={}, dataSourceType={}", callRecord.getId(), dataSourceTypeCode);
                            updateTaskCallRecordStatus(callRecord.getId(), AnalysisTaskCallRecordStatusEnum.FAILED.getCode(), null);
                            continue;
                        }

                        // 构建taskId: 14-0_12345 (14=CUSTOMER_DATA_ANALYSIS, 0=数据源类型, 12345=callRecordId)
                        String taskId = TaskIdUtil.generateTaskId(
                                String.valueOf(AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode()),
                                dataSourceTypeCode,
                                callRecord.getId()
                        );

                        // 发起任务
                        AiConversationProcessReq requestData = AiConversationProcessReq.builder()
                                .chatContactId(callRecord.getId())
                                .tenantId(tenantId)
                                .taskId(taskId)
                                .taskType(AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS)
                                .build();

                        aiConversationProcessService.submitTask(requestData);

                        // 更新CallRecord状态为进行中
                        updateTaskCallRecordStatus(callRecord.getId(), AnalysisTaskCallRecordStatusEnum.IN_PROGRESS.getCode(), null);

                        log.info("提交客户分析任务: taskId={}, callRecordId={}, dataSourceType={}",
                                taskId, callRecord.getId(), dataSourceType.getName());

                    } catch (Exception e) {
                        log.error("提交客户分析任务失败: callRecordId={}", callRecord.getId(), e);
                        updateTaskCallRecordStatus(callRecord.getId(), AnalysisTaskCallRecordStatusEnum.FAILED.getCode(), null);
                    }
                }
            }

        } catch (Exception ex) {
            log.error("AiAnalysisTaskBiz executeCustomer 执行客户分析任务发生异常", ex);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_CALL_RECORD_ERROR,
                    "执行客户分析任务" + tenantId, "执行客户分析任务发生异常", ex);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 检查并更新任务状态
     * 根据母表(ai_analysis_task_data)的完成情况判断整个任务是否完成/失败
     *
     * @param taskId   任务ID
     * @param tenantId 租户ID
     */
    private void checkAndUpdateTaskStatus(Long taskId, String tenantId) {
        // 查询该任务下所有的母表记录
        LambdaQueryWrapper<AiAnalysisTaskData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiAnalysisTaskData::getAnalysisTaskId, taskId)
                .eq(AiAnalysisTaskData::getTenantId, tenantId)
                .eq(AiAnalysisTaskData::getIsDeleted, 0);

        List<AiAnalysisTaskData> taskDataList = aiAnalysisTaskDataService.list(queryWrapper);

        if (CollectionUtils.isEmpty(taskDataList)) {
            log.info("任务没有母表数据，标记为完成。taskId={}", taskId);
            updateTaskStatus(taskId, AnalysisTaskEnum.COMPLETED.getCode());
            return;
        }

        boolean allCompleted = taskDataList.stream().allMatch(data ->
                Objects.equals(data.getStatus(), AnalysisTaskCallRecordStatusEnum.COMPLETED.getCode()));

        if (allCompleted) {
            updateTaskStatus(taskId, AnalysisTaskEnum.COMPLETED.getCode());
            log.info("客户分析任务全部完成。taskId={} totalCount={}", taskId, taskDataList.size());
        } else {
            log.info("客户分析任务还未全部完成。taskId={} totalCount={}", taskId, taskDataList.size());
        }
    }
}
