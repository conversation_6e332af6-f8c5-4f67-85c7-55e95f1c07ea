package com.yirendai.voiceaiserver.biz.asr;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.mq.send.MqSendUtil;
import com.yirendai.voiceaiserver.mq.send.TopicEnum;
import com.yirendai.voiceaiserver.vo.response.AsrOnlineVO;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.AiPlannerChatContactSub;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.enums.WebsocketMessageTypeEnum;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.workbench.mapper.AiPlannerChatContactSubMapper;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.vo.res.AppointmentRemindVo;
import com.yirendai.workbench.vo.res.WebSocketMessage;
import com.yirendai.workbench.websocket.WebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@RefreshScope
@Slf4j
public class AsrListener {

    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;

    @Resource
    private AiPlannerChatContactSubMapper aiPlannerChatContactSubMapper;

    @Resource
    private CallcenterCallRecordService callcenterCallRecordService;

    @Resource
    MqSendUtil mqSendUtil;

    public boolean appendAsrResult(String caseId, String asrResult) {
        log.info("AsrListener appendAsrResult caseId: {}, asrResult: {}", caseId, asrResult);
        if (Strings.isNullOrEmpty(caseId) || Strings.isNullOrEmpty(asrResult)) {
            log.warn("AsrListener appendAsrResult fail 1 caseId: {}, asrResult: {}", caseId, asrResult);
            return false;
        }
        String prefix = "yryx_";
        String stringWithoutPrefix = caseId.startsWith(prefix) ? caseId.substring(prefix.length()) : caseId;
        String[] parts = stringWithoutPrefix.split("-", 2);
        if (parts.length != 2) {
            log.warn("AsrListener appendAsrResult fail 2 caseId: {}, asrResult: {}", caseId, asrResult);
            return false;
        }
        String uuid = parts[0];
        String direction = parts[1];
        LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerChatContact::getBusId, uuid);
        AiPlannerChatContact chatContact = aiPlannerChatContactMapper.selectOne(wrapper);
        if (chatContact == null) {
            log.warn("AsrListener appendAsrResult fail 3 caseId: {}, asrResult: {}", caseId, asrResult);
            return false;
        }
        LambdaQueryWrapper<CallcenterCallRecord> recordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        recordLambdaQueryWrapper.eq(CallcenterCallRecord::getUuid, uuid);
        CallcenterCallRecord record = callcenterCallRecordService.getOne(recordLambdaQueryWrapper);
        if (record == null) {
            log.warn("AsrListener appendAsrResult fail 4 caseId: {}, asrResult: {}", caseId, asrResult);
            return false;
        }
        String userName = Objects.equals(direction, "c") ? record.getCustomerName() : record.getAgentName();
        int direct = Objects.equals(direction, "c") ? 1 : 0;
        AiPlannerChatContactSub contactSub = new AiPlannerChatContactSub();
        contactSub.setChatContactId(chatContact.getId());
        contactSub.setDirection(direct);
        contactSub.setMode("2pass-offline");
        contactSub.setText(asrResult);
        contactSub.setUserName(userName);
        contactSub.setCreateTime(LocalDateTime.now());
        aiPlannerChatContactSubMapper.insert(contactSub);
        WebSocketMessage<AsrOnlineVO> webSocketMessage = new WebSocketMessage<>();
        webSocketMessage.setType(WebsocketMessageTypeEnum.ASR_ONLINE.getCode());
        webSocketMessage.setData(AsrOnlineVO.builder().msg("")
                .mode("2pass-offline")
                .text(asrResult)
                .userName(userName)
                .status(1)
                .direction(direct).build());
        mqSendUtil.sendMessage(TopicEnum.WEBSOCKET_ONLINE_TOPIC,
                MqBaseDto.builder().webSocketCode(getSessionKey(record.getTenantId(), chatContact.getPlannerNo()))
                        .webSocketMessage(webSocketMessage).build());
        return true;
    }

    private String getSessionKey(String tenantId, String code) {
        return tenantId + "_" + code;
    }
}
