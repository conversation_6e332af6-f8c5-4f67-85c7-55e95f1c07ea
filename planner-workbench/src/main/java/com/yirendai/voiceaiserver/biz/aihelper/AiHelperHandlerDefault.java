package com.yirendai.voiceaiserver.biz.aihelper;

import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 小盟工具默认场景处理
 * {@link com.yirendai.voiceaiserver.enums.AISceneEnum#DEFAULT}
 */
@Component
public class AiHelperHandlerDefault extends AiHelperHandlerBasic {

    /**
     * 获取预制提示词
     * @return
     */
    @Override
    protected String getPrompt( ) {
        AiPromptRecordVo  latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.AI_HELPER.getCode());

        String dateTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        try{
            return String.format(latestPromptRecordByScene.getPrompt(), dateTime);
        }catch (Exception e){
            return "不要返回思考过程，现在的时间是：" + dateTime;
        }
    }

    /**
     * 获取模型调用key
     *
     * @return
     */
    @Override
    protected String getKey() {
        return zhiyuConfiguration.getAiCsKey();
    }

}
