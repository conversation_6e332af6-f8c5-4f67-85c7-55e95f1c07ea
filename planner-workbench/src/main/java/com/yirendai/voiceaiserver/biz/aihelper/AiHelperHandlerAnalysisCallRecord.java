package com.yirendai.voiceaiserver.biz.aihelper;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.vo.request.AiHelperMessageReq;
import com.yirendai.voiceaiserver.vo.response.AiHelperCallRecordVO;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 小盟工具分析通话记录场景处理
 * {@link com.yirendai.voiceaiserver.enums.AISceneEnum#ANALYSIS_CALL_RECORD}
 */
@Component
@Slf4j
public class AiHelperHandlerAnalysisCallRecord extends  AiHelperHandlerBasic {

    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;


    /**
     * 获取预制提示词
     * @return
     */
    @Override
    protected String getPrompt( ) {
        String dateTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        return " 现在的时间是：" + dateTime;
    }

    /**
     * 组装整体提示词
     */
    protected Map<String, Object> getInputs(AiHelperMessageReq chatMessagesReq, SseEmitter sseEmitter) throws Exception {
        if(CollectionUtil.isEmpty(chatMessagesReq.getCallRecordIds())){
            sseEmitter.send(" "+ JsonUtil.toJson(chatMessagesRes(chatMessagesReq,"请选择要分析的通话数据")));
            log.info("getPrompt 请选择要分析的通话数据 req={}", chatMessagesReq);
            delayClose(sseEmitter,1000);
            throw new AiServerException(ResultCode.FAILURE.getCode(),"请选择要分析的通话数据");
        }
        List<AiHelperCallRecordVO> list = aiPlannerChatContactMapper.getListByIdList(chatMessagesReq.getCallRecordIds());
        if(CollectionUtil.isEmpty(list)){
            sseEmitter.send(" "+ JsonUtil.toJson(chatMessagesRes(chatMessagesReq,"没有查询到要分析的通话数据")));
            log.info("getPrompt 没有查询到要分析的通话数据 req={}", chatMessagesReq);
            delayClose(sseEmitter,1000);
            throw new AiServerException(ResultCode.FAILURE.getCode(),"没有查询到要分析的通话数据");
        }
        // 检查 processedContent 总字符数是否超限
        int totalLength = list.stream()
                .mapToInt(item -> item.getProcessedContent() == null ? 0 : item.getProcessedContent().length())
                .sum();

        if (totalLength > 100000) {
            sseEmitter.send(" " + JsonUtil.toJson(chatMessagesRes(chatMessagesReq, "音转文的文本最多10万字，请缩小数据选中范围")));
            log.warn("getPrompt 音转文的文本超过10万字，totalLength={}, req={}", totalLength, chatMessagesReq);
            delayClose(sseEmitter, 1000);
            throw new AiServerException(ResultCode.FAILURE.getCode(),"音转文的文本最多10万字，请缩小数据选中范围");
        }
        List<String> callRecord = new ArrayList<>();
        StringBuilder promptBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            AiHelperCallRecordVO record = list.get(i);

            promptBuilder.append("电话唯一标识: ").append(record.getUuid()).append("\n");
            promptBuilder.append("员工编号: ").append(record.getPlannerNo()).append("\n");
            promptBuilder.append("员工姓名: ").append(record.getAgentName()).append("\n");
            promptBuilder.append("客户姓名: ").append(record.getCustomerName()).append("\n");
            promptBuilder.append("客户ID: ").append(record.getCustomerUid()).append("\n");
            promptBuilder.append("呼叫类型: ").append(record.getCallType() != null && record.getCallType() == 1 ? "呼入" : "呼出").append("\n");
            promptBuilder.append("部门名称: ").append(record.getAgentDept()).append("\n");
            promptBuilder.append("开始时间: ").append(DateUtil.format(record.getStartStamp(), "yyyy-MM-dd HH:mm:ss")).append("\n");
            promptBuilder.append("结束时间: ").append(DateUtil.format(record.getEndStamp(), "yyyy-MM-dd HH:mm:ss")).append("\n");
            promptBuilder.append("通话内容:\n").append(record.getProcessedContent()).append("\n\n");
            if(promptBuilder.length()>=4000){
                callRecord.add(promptBuilder.toString());
                promptBuilder.setLength(0);
            }
        }
        if(promptBuilder.length()>0){
            callRecord.add(promptBuilder.toString());
        }
        Map<String, Object> getInputs = super.getInputs(chatMessagesReq, sseEmitter);
        getInputs.put("callRecord", JsonUtil.toJson(callRecord));
        return getInputs;
    }






    /**
     * 获取模型调用key
     *
     * @return
     */
    @Override
    protected String getKey() {
        return zhiyuConfiguration.getAiCsKey();
    }

}
