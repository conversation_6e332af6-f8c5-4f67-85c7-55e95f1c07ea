package com.yirendai.voiceaiserver.task.conversation.sub;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationTagTypeEnum;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.mapper.AiConversationWechatSummaryMapper;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.UserBasicInfoFacadeService;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.vo.res.UserBasicInfoResponse;
import groovy.transform.EqualsAndHashCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ContentWechatTagProcess extends AbstractAiConversationProcess<AiConversationWechatSummary> {

    protected ThreadLocal<Set<TagCategoryProcess.TagInfo>> matchTagsList = ThreadLocal.withInitial(HashSet::new);
    protected ThreadLocal<Set<TagCategoryProcess.TagInfo>> recommendTagsList = ThreadLocal.withInitial(HashSet::new);

    @Resource
    private AiConversationWechatSummaryMapper aiConversationWechatSummaryMapper;

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        if (type == AiModelType.COMMON_MODEL) {
            type = AiModelType.TAG_DISTILLATION_MODEL;
        }
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Transactional
    @Override
    public void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        // 处理AI回复内容
        processResponse(processReq, (AiConversationWechatSummary) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        initProcessRecord(req);
    }

    @Override
    protected AiConversationWechatSummary getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        AiConversationWechatSummary aiConversationWechatSummary = processRecord.get();
        if (aiConversationWechatSummary != null) {
            return aiConversationWechatSummary;
        }
        return initProcessRecord(aiConversationProcessReq);
    }

    @Override
    public AiConversationWechatSummary initProcessRecord(AiConversationProcessReq req) {
        Long id = req.getChatContactId();
        // 查询AI小结记录
        AiConversationWechatSummary aiConversationWechatSummary = aiConversationWechatSummaryMapper.selectById(id);
        if (aiConversationWechatSummary == null) {
            log.error("AI企微对话标签任务记录不存在，记录ID: {}", id);
            throw new AiServerException(ResultCode.NO_AI_CONVERSATION_WECHAT_SUMMARY);
        }
        req.setTenantId(aiConversationWechatSummary.getTenantId());
        processRecord.set(aiConversationWechatSummary);
        req.setContent(aiConversationWechatSummary.getWechatContent());
        return aiConversationWechatSummary;
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        List<AiTagCategoryWithTags> allCategoriesWithTags = iAiUserTagDictionaryService.findAllCategoriesWithTags(tenantId);
        if (allCategoriesWithTags.isEmpty()) {
            throw new AiServerException(ResultCode.NO_AI_TAG_CATEGORY);
        }
        return String.format(lastPromptConfig.getPrompt(), content, allCategoriesWithTags);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        ObjectMapper objectMapper = new ObjectMapper();
        aiReplyContent = aiReplyContent.trim();
        try {
            // 处理 matchedTags 数组
            aiReplyContent = cleanJsonString(aiReplyContent);
            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            JsonNode matchedTagsNode = rootNode.get("matchedTags");
            matchTagsList.get().addAll(parseTagInfo(matchedTagsNode));
            // 处理 recommendedNewTags 数组
            JsonNode recommendedNewTagsNode = rootNode.get("recommendedNewTags");
            recommendTagsList.get().addAll(parseTagInfo(recommendedNewTagsNode));
        } catch (JsonProcessingException e) {
            log.error("ai标签结果解析失败:", e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    /**
     * 处理Json解析结果
     */
    private List<TagCategoryProcess.TagInfo> parseTagInfo(JsonNode tagList) {
        List<TagCategoryProcess.TagInfo> tagInfoList = new ArrayList<>();
        if (tagList != null && tagList.isArray()) {
            for (JsonNode tag : tagList) {
                String category = tag.get("category").asText();
                String name = tag.get("name").asText();
                TagCategoryProcess.TagInfo tagInfo = new TagCategoryProcess.TagInfo(category, name);
                tagInfoList.add(tagInfo);
            }
        }
        return tagInfoList;
    }

    @Transactional
    @Override
    public void processResponse(AiConversationProcessReq aiConversationProcessReq, AiConversationWechatSummary aiConversationWechatSummary) {
        Set<TagCategoryProcess.TagInfo> matchTagsListSet = matchTagsList.get();
        String tenantId = aiConversationWechatSummary.getTenantId();
        // 先删除之前的对话标签
        aiConversationWechatTagService.deleteByChatContactId(aiConversationWechatSummary.getId());
        // 查询用户手机号
        String phone = null;
        try {
            SimpleUserInfoDto basicInfoResponse = tenantServiceHolder.getCustomerInfoWrapper(tenantId)
                    .getUserBasicInfoByUserId(aiConversationWechatSummary.getUserId(), tenantId);
            if (basicInfoResponse == null) {
                log.error("AI ContentWechatTagProcess processResponse 用户不存在, {}", aiConversationWechatSummary.getUserId());
            } else {
                phone = basicInfoResponse.getMobileNo();
            }
        } catch (Exception e) {
            log.error("AI ContentWechatTagProcess processResponse 获取用户手机号失败, {}", aiConversationWechatSummary.getUserId(), e);
        }
        for (TagCategoryProcess.TagInfo tagInfo : matchTagsListSet) {
            String category = tagInfo.getCategory();
            String name = tagInfo.getName();
            // 查询标签分类
            AiUserTagCategory aiUserTagCategory = iAiUserTagCategoryService.selectByCategoryName(category, tenantId);
            if (aiUserTagCategory == null) {
                log.error("未找到标签分类: {}", category);
                continue;
            }
            // 查询标签
            AiUserTagDictionary aiUserTagDictionary = iAiUserTagDictionaryService.selectByTagNameAndCategoryId(name, aiUserTagCategory.getId(), tenantId);
            if (aiUserTagDictionary == null) {
                log.error("未找到标签: {}", name);
                continue;
            }
            Long tagId = aiUserTagDictionary.getId();
            Long tagCategoryId = aiUserTagCategory.getId();
            Long topParentId = aiUserTagCategory.getTopParentId();
            String topParentName = "";
            String colorValue = aiUserTagCategory.getColorValue();
            if (topParentId != null && topParentId != 0) {
                AiUserTagCategory topParent = iAiUserTagCategoryService.selectById(topParentId);
                if (topParent != null) {
                    topParentName = topParent.getCategoryName();
                    if (Strings.isNullOrEmpty(colorValue)) {
                        colorValue = topParent.getColorValue();
                    }
                }
            } else if (topParentId != null) {
                // 顶级分类
                topParentId = aiUserTagCategory.getId();
                topParentName = aiUserTagCategory.getCategoryName();
            }
            // 插入对话标签
            aiConversationWechatTagService.insertAiConversationTag(aiConversationWechatSummary, tagCategoryId, tagId, category, name, AiConversationTagTypeEnum.MATCH);
            // 判断是否在用户和标签关联
            LambdaQueryWrapper<AiCustomerTag> customerTagQueryWrapper = new LambdaQueryWrapper<>();
            if (Strings.isNullOrEmpty(phone)) {
                customerTagQueryWrapper.eq(AiCustomerTag::getUserId, aiConversationWechatSummary.getUserId())
                        .eq(AiCustomerTag::getTagName, name)
                        .eq(AiCustomerTag::getTagCategoryName, category)
                        .isNull(AiCustomerTag::getUserPhone)
                        .eq(AiCustomerTag::getTenantId, tenantId);
            } else {
                customerTagQueryWrapper.eq(AiCustomerTag::getUserId, aiConversationWechatSummary.getUserId())
                        .eq(AiCustomerTag::getTagName, name)
                        .eq(AiCustomerTag::getTagCategoryName, category)
                        .eq(AiCustomerTag::getUserPhone, phone)
                        .eq(AiCustomerTag::getTenantId, tenantId);
            }

            AiCustomerTag aiCustomerTag = null;
            try {
                aiCustomerTag = aiCustomerTagMapper.selectOne(customerTagQueryWrapper);
            } catch (Exception e) {
                log.warn("用户标签关联已存在，无法重复插入。错误原因：{}", e.getMessage());
            }
            if (aiCustomerTag == null) {
                aiCustomerTag = new AiCustomerTag();
                aiCustomerTag.setUserId(aiConversationWechatSummary.getUserId());
                aiCustomerTag.setUserPhone(phone);
                aiCustomerTag.setTagId(tagId);
                aiCustomerTag.setTagCategoryId(aiUserTagCategory.getId());
                aiCustomerTag.setTagName(name);
                aiCustomerTag.setTagCategoryName(category);
                aiCustomerTag.setTopTagCategoryId(topParentId);
                aiCustomerTag.setWeight(1);
                aiCustomerTag.setMatchStatus(AiConversationTagTypeEnum.MATCH.getCode());
                aiCustomerTag.setColorValue(colorValue);
                aiCustomerTag.setSourceType(1);
                aiCustomerTag.setTenantId(tenantId);
                aiCustomerTag.setTopTagCategoryName(topParentName);
                aiCustomerTag.setCreateTime(LocalDateTime.now());
                aiCustomerTag.setUpdateTime(LocalDateTime.now());
                try {
                    aiCustomerTagMapper.insert(aiCustomerTag);
                } catch (Exception e) {
                    log.warn("用户标签关联已存在，无法重复插入。错误原因：{}", e.getMessage());
                }
            } else {
                aiCustomerTag.setUpdateTime(LocalDateTime.now());
                Integer weight = aiCustomerTag.getWeight();
                if (weight == null) {
                    weight = 0;
                }
                if (Strings.isNullOrEmpty(aiCustomerTag.getColorValue())) {
                    aiCustomerTag.setColorValue(colorValue);
                }
                if (aiCustomerTag.getSourceType() != null && aiCustomerTag.getSourceType() == 0) {
                    aiCustomerTag.setSourceType(2);
                } else if (aiCustomerTag.getSourceType() == null) {
                    aiCustomerTag.setSourceType(1);
                }
                aiCustomerTag.setWeight(weight + 1);
                aiCustomerTagMapper.updateById(aiCustomerTag);
            }
        }
        Set<TagCategoryProcess.TagInfo> recommendTagsListSet = this.recommendTagsList.get();
        for (TagCategoryProcess.TagInfo tagInfo : recommendTagsListSet) {
            String category = tagInfo.getCategory();
            String name = tagInfo.getName();
            if (Strings.isNullOrEmpty(name)) {
                continue;
            }
            AiUserTagDictionary aiUserTagDictionary = iAiUserTagDictionaryService.selectByTagName(name, tenantId);
            if (aiUserTagDictionary != null) {
                continue;
            }
            // 插入对话标签
            aiConversationWechatTagService.insertAiConversationTag(aiConversationWechatSummary, null, null, category, name, AiConversationTagTypeEnum.RECOMMEND);
            // 判断是否在用户和标签关联
            LambdaQueryWrapper<AiCustomerTag> customerTagQueryWrapper = new LambdaQueryWrapper<>();
            if (Strings.isNullOrEmpty(phone)) {
                customerTagQueryWrapper.eq(AiCustomerTag::getUserId, aiConversationWechatSummary.getUserId())
                        .eq(AiCustomerTag::getTagName, name)
                        .eq(AiCustomerTag::getTagCategoryName, category)
                        .isNull(AiCustomerTag::getUserPhone)
                        .eq(AiCustomerTag::getTenantId, tenantId);
            } else {
                customerTagQueryWrapper.eq(AiCustomerTag::getUserId, aiConversationWechatSummary.getUserId())
                        .eq(AiCustomerTag::getTagName, name)
                        .eq(AiCustomerTag::getTagCategoryName, category)
                        .eq(AiCustomerTag::getUserPhone, phone)
                        .eq(AiCustomerTag::getTenantId, tenantId);
            }
            AiCustomerTag aiCustomerTag = null;
            try {
                aiCustomerTag = aiCustomerTagMapper.selectOne(customerTagQueryWrapper);
            } catch (Exception e) {
                log.warn("用户标签关联已存在，无法重复插入。错误原因：{}", e.getMessage());
            }
            if (aiCustomerTag == null) {
                aiCustomerTag = new AiCustomerTag();
                aiCustomerTag.setUserId(aiConversationWechatSummary.getUserId());
                aiCustomerTag.setUserPhone(phone);
                aiCustomerTag.setTagName(name);
                aiCustomerTag.setMatchStatus(AiConversationTagTypeEnum.RECOMMEND.getCode());
                aiCustomerTag.setTagCategoryName(category);
                aiCustomerTag.setWeight(1);
                aiCustomerTag.setSourceType(1);
                aiCustomerTag.setTenantId(tenantId);
                aiCustomerTag.setCreateTime(LocalDateTime.now());
                aiCustomerTag.setUpdateTime(LocalDateTime.now());
                try {
                    aiCustomerTagMapper.insert(aiCustomerTag);
                } catch (Exception e) {
                    log.warn("用户标签关联已存在，无法重复插入。错误原因：{}", e.getMessage());
                }
            } else {
                aiCustomerTag.setUpdateTime(LocalDateTime.now());
                Integer weight = aiCustomerTag.getWeight();
                if (weight == null) {
                    weight = 0;
                }
                if (aiCustomerTag.getSourceType() != null && aiCustomerTag.getSourceType() == 0) {
                    aiCustomerTag.setSourceType(2);
                } else if (aiCustomerTag.getSourceType() == null) {
                    aiCustomerTag.setSourceType(1);
                }
                aiCustomerTag.setWeight(weight + 1);
                aiCustomerTagMapper.updateById(aiCustomerTag);
            }
        }
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.WECHAT_CONVERSATION_TAG;
    }

    @Data
    @EqualsAndHashCode
    protected static class TagInfo {
        private String category;
        private String name;

        public TagInfo(String category, String name) {
            this.category = category;
            this.name = name;
        }

        @Override
        public String toString() {
            return "TagInfo{category='" + category + "', name='" + name + "'}";
        }
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (matchTagsList.get() != null) {
            matchTagsList.remove();
        }
        if (recommendTagsList.get() != null) {
            recommendTagsList.remove();
        }
    }
}
