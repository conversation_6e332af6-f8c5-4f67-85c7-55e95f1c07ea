package com.yirendai.voiceaiserver.task.conversation.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ServiceWorkOrderFields {

    private List<Field> fields;

    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    @Data
    static class Field {
        private String key;
        private String description;
        private int type;
        private String value;
    }
}
