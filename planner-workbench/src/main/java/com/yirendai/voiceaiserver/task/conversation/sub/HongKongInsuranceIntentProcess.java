package com.yirendai.voiceaiserver.task.conversation.sub;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.*;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.service.IAiConversationFlowResultService;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.service.IAiConversationChatSummaryService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.util.TaskIdUtil;
import com.yirendai.voiceaiserver.service.AiConversationWechatSummaryService;
import com.yirendai.voiceaiserver.service.IAiUserTagDictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 港险主意图识别处理器
 * 只负责识别主意图，不处理标签识别
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class HongKongInsuranceIntentProcess extends AbstractAiConversationProcess<AiConversationTagsProcess> {

    private static final String FLOW_TYPE = "HK_INSURANCE";
    
    @Resource
    protected IAiConversationFlowResultService iAiConversationFlowResultService;
    
    @Resource
    private AiConversationProcessTaskService aiConversationProcessTaskService;
    
    @Resource
    private IAiPlannerChatContactService aiPlannerChatContactService;
    
    @Resource
    private AiConversationWechatSummaryService aiConversationWechatSummaryService;
    
    @Resource
    private IAiUserTagDictionaryService iAiUserTagDictionaryService;

    @Resource
    private IAiConversationChatSummaryService aiConversationChatSummaryService;

    protected ThreadLocal<Set<String>> detectedIntents = ThreadLocal.withInitial(HashSet::new);

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        if (type == AiModelType.COMMON_MODEL) {
            type = AiModelType.TAG_DISTILLATION_MODEL;
        }
        String response = chatService.chatAiContent(question, model, temperature, type, systemPrompt);
        return extractAiReplyContent(response);
    }

    @Transactional
    @Override
    public void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        processResponse(processReq, (AiConversationTagsProcess) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        if (Objects.equals(req.getSourceType(), AiConversationDataSourceEnum.QI_WECHAT.getCode())) {
            // 企微数据源（sourceType = "1"）
            AiConversationWechatSummary aiConversationWechatSummary = aiConversationWechatSummaryService.getById(req.getChatContactId());
            if (aiConversationWechatSummary != null) {
                req.setContent(aiConversationWechatSummary.getWechatContent());
                req.setUserId(aiConversationWechatSummary.getUserId());
                req.setPlannerNo(aiConversationWechatSummary.getPlannerId());
                req.setTenantId(aiConversationWechatSummary.getTenantId());
                
                // 设置chatId：企微数据源取interval_tag
                req.setChatId(aiConversationWechatSummary.getIntervalTag());
                
                String intervalTag = aiConversationWechatSummary.getIntervalTag();
                if (intervalTag != null && !intervalTag.trim().isEmpty()) {
                    try {
                        LocalDate date = LocalDate.parse(intervalTag, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        req.setCallTime(date.atTime(23, 59, 59));
                    } catch (Exception e) {
                        log.warn("解析企微小结时间标签失败，使用创建时间代替: {}", intervalTag, e);
                        req.setCallTime(aiConversationWechatSummary.getCreateTime());
                    }
                } else {
                    req.setCallTime(aiConversationWechatSummary.getCreateTime());
                }
            }
        } else if (Objects.equals(req.getSourceType(), AiConversationDataSourceEnum.WECHAT.getCode())){
            AiConversationChatSummary aiConversationChatSummary = aiConversationChatSummaryService.getById(req.getChatContactId());
            if (aiConversationChatSummary != null) {
                req.setContent(aiConversationChatSummary.getChatContent());
                req.setUserId(aiConversationChatSummary.getCustomerId());
                req.setPlannerNo(aiConversationChatSummary.getPlannerNo());
                req.setTenantId(aiConversationChatSummary.getTenantId());
                LocalDate chatDate = aiConversationChatSummary.getChatDate();
                if (chatDate != null) {
                    req.setChatId(String.valueOf(chatDate));
                    req.setCallTime(chatDate.atTime(23, 59, 59));
                } else {
                    req.setCallTime(aiConversationChatSummary.getCreateTime());
                }
            }
        } else {
            validatePlannerChatContact(req);
        }
    }

    @Override
    protected AiConversationTagsProcess getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        return new AiConversationTagsProcess();
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        return String.format(lastPromptConfig.getPrompt(), content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        ObjectMapper objectMapper = new ObjectMapper();
        aiReplyContent = aiReplyContent.trim();
        Set<String> intents = new HashSet<>();
        if (detectedIntents.get() != null && !detectedIntents.get().isEmpty()) {
            intents.addAll(detectedIntents.get());
        }
        
        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            
            // 支持两种格式：单个意图和多个意图
            if (rootNode.has("intents") && rootNode.get("intents").isArray()) {
                // 多意图格式：{"intents": ["D_CLASS", "B_CLASS"]}
                JsonNode intentsNode = rootNode.get("intents");
                for (JsonNode intentNode : intentsNode) {
                    String intent = intentNode.asText();
                    if (PrimaryIntentEnum.isValidIntent(intent)) {
                        intents.add(intent);
                    }
                }
            } else if (rootNode.has("intent")) {
                // 单意图格式（兼容旧版本）：{"intent": "D_CLASS"}
                String intent = rootNode.get("intent").asText();
                if (PrimaryIntentEnum.isValidIntent(intent)) {
                    intents.add(intent);
                }
            }
            
            // 如果没有识别到有效意图，设置为NONE
            if (intents.isEmpty()) {
                intents.add(PrimaryIntentEnum.NONE.getCode());
            }
            
            detectedIntents.set(intents);
            log.info("港险主意图识别完成，识别到意图: {}", intents);
            
        } catch (JsonProcessingException e) {
            log.error("港险主意图识别结果解析失败:", e);
            intents.add(PrimaryIntentEnum.NONE.getCode());
            detectedIntents.set(intents);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Transactional
    @Override
    public void processResponse(AiConversationProcessReq aiConversationProcessReq, AiConversationTagsProcess aiConversationTagsProcess) {
        String tenantId = aiConversationProcessReq.getTenantId();
        String model = aiPromptRecordService.getLatestPromptRecordByScene(getPromptScene().getCode()).getModel();
        Float temperature = aiPromptRecordService.getLatestPromptRecordByScene(getPromptScene().getCode()).getTemperature();

        Set<String> intentCodes = detectedIntents.get();
        String sourceType = aiConversationProcessReq.getSourceType();
        
        // 获取源数据的理财师ID、用户ID和对话时间
        String plannerNo = aiConversationProcessReq.getPlannerNo();
        String userId = aiConversationProcessReq.getUserId();
        LocalDateTime chatTime = aiConversationProcessReq.getCallTime();
        
        List<AiConversationFlowResult> results = new ArrayList<>();
        Set<String> validIntents = new HashSet<>();
        
        // 为每个识别到的意图创建单独的记录
        for (String intentCode : intentCodes) {
            // 生成对应的子意图任务ID
            String subIntentType = String.valueOf(PrimaryIntentEnum.getByCode(intentCode).getId());
            String intentTaskId = TaskIdUtil.generateSubIntentTaskId(subIntentType,
                String.valueOf(AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode()),
                sourceType, aiConversationProcessReq.getChatContactId());
            
            AiConversationFlowResult result = new AiConversationFlowResult();
            result.setSourceType(sourceType);
            result.setSourceId(aiConversationProcessReq.getChatContactId());
            result.setChatId(aiConversationProcessReq.getChatId());
            result.setTenantId(tenantId);
            result.setPlannerNo(plannerNo);
            result.setUserId(userId);
            result.setFlowType(FLOW_TYPE);
            result.setIntentCode(intentCode);
            result.setNodeCode("INTENT");
            result.setNodeName("主意图识别");
            result.setNodeLevel(0);
            result.setModel(model);
            result.setTemperature(temperature);
            result.setTaskId(intentTaskId);
            result.setChatTime(chatTime);
            result.setCreateTime(LocalDateTime.now());
            result.setUpdateTime(LocalDateTime.now());
            result.setIsDeleted(0);
            
            // 检查是否识别到有效意图
            if (PrimaryIntentEnum.isValidIntent(intentCode)) {
                result.setIsReached(1);
                result.setConfidenceScore(BigDecimal.ONE);
                result.setMatchedKeywords("primary_intent:" + intentCode);
                validIntents.add(intentCode);
            } else {
                result.setIsReached(0);
                result.setConfidenceScore(BigDecimal.ZERO);
                result.setMatchedKeywords("no_intent_detected");
            }
            
            results.add(result);
        }
        
        // 批量保存主意图识别结果
        if (!results.isEmpty()) {
            iAiConversationFlowResultService.batchSaveWithDeduplication(results);
            log.info("港险主意图识别完成，保存了{}个意图结果: {}", results.size(), intentCodes);
        }
        
        // 为每个有效意图创建对应的节点组识别任务
        for (String validIntent : validIntents) {
            try {
                String subIntentType = String.valueOf(PrimaryIntentEnum.getByCode(validIntent).getId());
                
                // 获取该意图下的所有节点组
                Set<String> nodeGroups = getNodeGroupsByIntent(tenantId, validIntent);
                
                // 为每个节点组创建单独的任务
                for (String nodeGroup : nodeGroups) {
                    String nodeTaskId = TaskIdUtil.generateNodeGroupTaskId(subIntentType, nodeGroup,
                        String.valueOf(AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_NODES.getCode()),
                        sourceType, aiConversationProcessReq.getChatContactId());
                    
                    aiConversationProcessTaskService.createProcessTask(
                        nodeTaskId, 
                        aiConversationProcessReq.getChatContactId(),
                        AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_NODES.getCode(),
                        AiConversationProcessStatusEnum.INIT_WAITING.getCode(),
                        tenantId,
                        sourceType
                    );
                    log.info("检测到有效港险意图，已创建节点组识别任务: taskId={}, chatContactId={}, intent={}, subIntentType={}, nodeGroup={}", 
                        nodeTaskId, aiConversationProcessReq.getChatContactId(), validIntent, subIntentType, nodeGroup);
                }
            } catch (Exception e) {
                log.error("创建港险节点组识别任务失败，chatContactId={}, intent={}", 
                    aiConversationProcessReq.getChatContactId(), validIntent, e);
            }
        }
        
        // 如果没有有效意图，清理之前的节点结果
        if (validIntents.isEmpty()) {
            try {
                iAiConversationFlowResultService.clearNodeResultsWhenNoIntent(
                    sourceType, aiConversationProcessReq.getChatContactId(), tenantId, FLOW_TYPE);
                log.info("意图为NONE，已清理之前的节点结果: chatContactId={}, intents={}", 
                    aiConversationProcessReq.getChatContactId(), intentCodes);
            } catch (Exception e) {
                log.error("清理节点结果失败，chatContactId={}, intents={}", 
                    aiConversationProcessReq.getChatContactId(), intentCodes, e);
            }
            log.info("未检测到有效港险意图，不创建节点识别任务: chatContactId={}, intents={}", 
                aiConversationProcessReq.getChatContactId(), intentCodes);
        }
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.HONG_KONG_INSURANCE_INTENT;
    }
    
    /**
     * 获取检测到的主意图代码集合
     * @return 主意图代码集合，如果没有检测到则返回空集合
     */
    public Set<String> getDetectedIntents() {
        Set<String> intents = detectedIntents.get();
        return intents != null ? new HashSet<>(intents) : new HashSet<>();
    }
    
    /**
     * 获取检测到的第一个主意图代码（兼容旧版本）
     * @return 第一个主意图代码，如果没有检测到则返回null
     */
    public String getDetectedIntent() {
        Set<String> intents = detectedIntents.get();
        if (intents != null && !intents.isEmpty()) {
            return intents.iterator().next();
        }
        return null;
    }
    
    /**
     * 检查是否检测到有效的主意图
     * @return true表示检测到有效意图，false表示无意图或检测失败
     */
    public boolean hasValidIntent() {
        Set<String> intents = detectedIntents.get();
        if (intents != null) {
            return intents.stream().anyMatch(PrimaryIntentEnum::isValidIntent);
        }
        return false;
    }

    /**
     * 根据意图获取节点组列表
     */
    private Set<String> getNodeGroupsByIntent(String tenantId, String intentCode) {
        LambdaQueryWrapper<AiUserTagDictionary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUserTagDictionary::getTagType, intentCode)
                .eq(AiUserTagDictionary::getTenantId, tenantId)
                .eq(AiUserTagDictionary::getStatus, 1)
                .eq(AiUserTagDictionary::getIsDeleted, 0)
                .isNotNull(AiUserTagDictionary::getNodeGroup)
                .ne(AiUserTagDictionary::getNodeGroup, "")
                .groupBy(AiUserTagDictionary::getNodeGroup);
        
        List<AiUserTagDictionary> tags = iAiUserTagDictionaryService.list(queryWrapper);
        return tags.stream()
                .map(AiUserTagDictionary::getNodeGroup)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (detectedIntents != null) {
            detectedIntents.remove();
        }
    }
}