package com.yirendai.voiceaiserver.task.conversation.sub;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yirendai.app.fortune.common.util.DateUtil;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.AiCallReservation;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiCallReservationMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CallReservationProcess extends AbstractAiConversationProcess<AiCallReservation> {

    @Resource
    private AiCallReservationMapper aiCallReservationMapper;

    private final ThreadLocal<List<ReservationResult>> reservationResults = ThreadLocal.withInitial(ArrayList::new);

    private final ThreadLocal<ReservationResult> result = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Override
    protected void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.SUM_NEXT_CALL_TIME.getCode());
        String question = buildChunkAfterQuestion(latestPromptRecordByScene, processReq);
        log.info("AI对话处理请求前-预约汇总请求，对话ID{}, 预约汇总长度{}, 问题:{}", processReq.getChatContactId(), processReq.getContent().length(), question);
        String response = chatService.chat(question, latestPromptRecordByScene.getModel(), latestPromptRecordByScene.getTemperature());
        String replyContent = extractAiReplyContent(response);
        parseResponse(replyContent);
        // 处理AI回复内容
        processResponse(processReq, (AiCallReservation) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        validatePlannerChatContact(req);
    }

    @Override
    protected String buildChunkAfterQuestion(AiPromptRecordVo latestPromptRecordByScene, AiConversationProcessReq processReq) {
        return String.format(latestPromptRecordByScene.getPrompt(), DateUtil.formatTo(processReq.getCallTime(), DateUtil.DateFormat.YYYY_MM_DD_HH_MM_SS), reservationResults.get().toString());
    }

    @Override
    protected AiCallReservation getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        // 查询AI通话预约记录
        LambdaQueryWrapper<AiCallReservation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCallReservation::getChatContactId, aiConversationProcessReq.getChatContactId());
        AiCallReservation aiCallReservation = aiCallReservationMapper.selectOne(queryWrapper);
        if (aiCallReservation != null && Objects.equals(aiCallReservation.getProcessStatus(), AiConversationProcessStatusEnum.FINISHED.getCode())) {
            this.finished.set(true);
        } else if (aiCallReservation == null) {
            // 新增AI通话预约记录
            aiCallReservation = new AiCallReservation();
            aiCallReservation.setChatContactId(aiConversationProcessReq.getChatContactId());
            aiCallReservation.setPlannerNo(aiConversationProcessReq.getPlannerNo());
            aiCallReservation.setUserId(aiConversationProcessReq.getUserId());
            aiCallReservation.setProcessStatus(AiConversationProcessStatusEnum.PROCESSING.getCode());
            aiCallReservation.setCreateTime(new Date());
            aiCallReservation.setUpdateTime(new Date());
            aiCallReservation.setTenantId(aiConversationProcessReq.getTenantId());
            aiCallReservation.setIsDeleted(0);
            aiCallReservationMapper.insert(aiCallReservation);
        }
        return aiCallReservation;
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        String curCallTime = DateUtil.formatTo(callTime, DateUtil.DateFormat.YYYY_MM_DD_HH_MM_SS);
        return String.format(lastPromptConfig.getPrompt(), curCallTime, content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            if (aiReplyContent == null) {
                return;
            }
            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            log.info("AI对话预约处理，处理内容: {}", rootNode);
            String time = rootNode.get("time").asText();
            String text = rootNode.get("text").asText();
            String nextChatStarter = rootNode.get("nextChatStarter").asText();
            ReservationResult reservationResult = new ReservationResult(time, text, nextChatStarter);
            reservationResults.get().add(reservationResult);
            result.set(reservationResult);
        } catch (JsonProcessingException e) {
            log.error("ai预约结果解析失败:", e);
            AiCallReservation aiCallReservation = processRecord.get();
            aiCallReservation.setStatus(AiConversationProcessStatusEnum.FAILED.getCode());
            aiCallReservation.setUpdateTime(new Date());
            aiCallReservationMapper.updateById(aiCallReservation);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Override
    protected void processResponse(AiConversationProcessReq req, AiCallReservation aiCallReservation) {
        ReservationResult reservationResult = result.get();
        log.info("AI对话预约处理，处理结果: {}", reservationResult);
        if (reservationResult != null) {
            aiCallReservation.setReservationTime(DateUtil.formatFrom(reservationResult.getTime(), DateUtil.DateFormat.YYYY_MM_DD_HH_MM));
            aiCallReservation.setReservationReason(reservationResult.getText());
            aiCallReservation.setNextChatStarter(reservationResult.getNextChatStarter());
            aiCallReservation.setProcessStatus(AiConversationProcessStatusEnum.FINISHED.getCode());
            aiCallReservation.setUpdateTime(new Date());
            aiCallReservationMapper.updateById(aiCallReservation);
        }
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.NEXT_CALL_TIME;
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        reservationResults.remove();
        result.remove();
    }

    @Data
    @EqualsAndHashCode
    protected static class ReservationResult {
        private String time;
        private String text;
        private String nextChatStarter;

        // 构造函数
        public ReservationResult(String time, String text, String nextChatStarter) {
            this.time = time;
            this.text = text;
            this.nextChatStarter = nextChatStarter;
        }
    }
}
