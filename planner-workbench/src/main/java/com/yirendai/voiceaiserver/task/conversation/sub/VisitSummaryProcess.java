package com.yirendai.voiceaiserver.task.conversation.sub;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.CallcenterVisitRecord;
import com.yirendai.workbench.enums.VisitStatusEnum;
import com.yirendai.workbench.mapper.CallcenterVisitRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @time 2025/2/17 11:22
 **/
@Slf4j
@Component
public class VisitSummaryProcess extends AbstractAiConversationProcess<CallcenterVisitRecord> {

    @Resource
    CallcenterVisitRecordMapper callcenterVisitRecordMapper;

    protected ThreadLocal<List<String>> summaryContentList = ThreadLocal.withInitial(ArrayList::new);

    protected ThreadLocal<String> lastSummaryContent = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Override
    protected void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        // 处理AI回复内容
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(
                PromptSceneEnum.SUM_VISIT_SUMMARY.getCode());
        String question = buildChunkAfterQuestion(latestPromptRecordByScene, processReq);
        log.info("AI拜访小结汇总请求，拜访ID{}, 小结汇总长度{}, 问题:{}", processReq.getChatContactId(),
                processReq.getContent().length(), question);
        String response = chatService.chat(question, latestPromptRecordByScene.getModel(),
                latestPromptRecordByScene.getTemperature());
        String aiReplyContent = extractAiReplyContent(response);
        log.info("AI拜访小结切片汇总回复: {}", aiReplyContent);
        parseResponse(aiReplyContent);
        processResponse(processReq, (CallcenterVisitRecord) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        initProcessRecord(req);
    }

    @Override
    protected CallcenterVisitRecord getOrCreateProcessRecord(AiConversationProcessReq req) {
        CallcenterVisitRecord callcenterVisitRecord = processRecord.get();
        if (callcenterVisitRecord != null) {
            return callcenterVisitRecord;
        }
        return initProcessRecord(req);
    }

    @Override
    public CallcenterVisitRecord initProcessRecord(AiConversationProcessReq req) {
        LambdaQueryWrapper<CallcenterVisitRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallcenterVisitRecord::getId, req.getChatContactId());
        CallcenterVisitRecord callcenterVisitRecord = callcenterVisitRecordMapper.selectOne(queryWrapper);
        callcenterVisitRecord.setStatus(VisitStatusEnum.THREE.getCode());
        callcenterVisitRecord.setUpdateTime(LocalDateTime.now());
        callcenterVisitRecordMapper.updateById(callcenterVisitRecord);
        processRecord.set(callcenterVisitRecord);
        req.setContent(callcenterVisitRecord.getOriContent());
        return callcenterVisitRecord;
    }

    @Override
    protected String buildChunkAfterQuestion(AiPromptRecordVo latestPromptRecordByScene, AiConversationProcessReq processReq) {
        return String.format(latestPromptRecordByScene.getPrompt(), summaryContentList.get().toString());
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime,
            String tenantId) {
        return String.format(lastPromptConfig.getPrompt(), content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        summaryContentList.get().add(aiReplyContent);
        lastSummaryContent.set(aiReplyContent);
    }

    @Override
    protected void processResponse(AiConversationProcessReq req, CallcenterVisitRecord callcenterVisitRecord) {
        String summaryContent = lastSummaryContent.get();
        if (StringUtils.isNotEmpty(summaryContent)) {
            callcenterVisitRecord.setSummary(summaryContent);
            callcenterVisitRecord.setStatus(VisitStatusEnum.FOUR.getCode());
        }else {
            callcenterVisitRecord.setStatus(VisitStatusEnum.FIVE.getCode());
        }
        callcenterVisitRecord.setUpdateTime(LocalDateTime.now());
        callcenterVisitRecordMapper.updateById(callcenterVisitRecord);
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.VISIT_SUMMARY;
    }
}
