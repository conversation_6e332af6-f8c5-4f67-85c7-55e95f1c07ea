package com.yirendai.voiceaiserver.task.conversation.sub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Strings;
import com.yirendai.robot.modules.tenant.service.IRobotTenantSmsSendLogService;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.CustomerDataSourceTypeEnum;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.*;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneResultVO;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.mapper.AiAnalysisTaskCallRecordMapper;
import com.yirendai.workbench.service.IAiConversationChatSummaryService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.params.SetParams;

/**
 * 客户数据分析处理子任务
 * 用于处理客户ID类型的多数据源分析
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Component
public class CustomerDataAnalysisProcess extends AbstractAiConversationProcess<AiAnalysisTaskCallRecord> {

    @Autowired
    private IAiAnalysisTaskResultService aiAnalysisTaskResultService;

    @Resource
    private AiAnalysisTaskCallRecordMapper aiAnalysisTaskCallRecordMapper;

    @Resource
    private IAiPlannerChatContactService aiPlannerChatContactService;

    @Resource
    private IAiConversationChatSummaryService aiConversationChatSummaryService;

    @Resource
    private AiConversationWechatSummaryService aiConversationWechatSummaryService;

    @Resource
    private IAiAnalysisConfigSceneService aiAnalysisConfigSceneService;

    @Resource
    private IAiAnalysisConfigSceneResultService aiAnalysisConfigSceneResultService;

    @Resource
    private IRobotTenantSmsSendLogService robotTenantSmsSendLogService;

    @Resource
    private IAiAnalysisTaskDataService aiAnalysisTaskDataService;

    @Resource
    private IAiAnalysisTaskService aiAnalysisTaskService;

    @Resource
    private JedisCluster jedisCluster;

    /**
     * 解析后的AI分析结果
     */
    protected ThreadLocal<List<CustomerDataAnalysisProcess.IntentResult>> parsedIntentsLocal = ThreadLocal.withInitial(ArrayList::new);

    /**
     * 当前数据源类型
     */
    protected ThreadLocal<String> currentDataSourceTypeLocal = ThreadLocal.withInitial(() -> "");

    /**
     * 分析目的
     */
    protected ThreadLocal<String> purposeThreadLocal = new ThreadLocal<>();

    /**
     * 意向Map
     */
    protected ThreadLocal<Map<String, String>> intentionThreadLocal = new ThreadLocal<>();

    /**
     * 已格式化的意向提示
     */
    protected ThreadLocal<String> formattedIntentThreadLocal = new ThreadLocal<>();

    /**
     * 已格式化的意向名称集合
     */
    protected ThreadLocal<String> formattedIntentKeyThreadLocal = new ThreadLocal<>();

    /**
     * 意向ID Map
     */
    protected ThreadLocal<Map<String, Long>> idIntentionThreadLocal = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        if (type == AiModelType.COMMON_MODEL) {
            type = AiModelType.TAG_DISTILLATION_MODEL;
        }
        String response = chatService.chatAiContent(question, model, temperature, type, systemPrompt);
        return extractAiReplyContent(response);
    }

    @Transactional
    @Override
    public void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        processResponse(processReq, (AiAnalysisTaskCallRecord) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        initProcessRecord(req);
    }

    @Override
    protected AiAnalysisTaskCallRecord getOrCreateProcessRecord(AiConversationProcessReq req) {
        AiAnalysisTaskCallRecord callRecord = processRecord.get();
        if (callRecord != null) {
            return callRecord;
        }
        return initProcessRecord(req);
    }

    @Override
    public AiAnalysisTaskCallRecord initProcessRecord(AiConversationProcessReq req) {
        // req.getChatContactId() 现在存储的是 AiAnalysisTaskData 的 ID
        Long taskCallCenterId = req.getChatContactId();
        AiAnalysisTaskCallRecord callRecord = aiAnalysisTaskCallRecordMapper.selectById(taskCallCenterId);
        if (callRecord == null) {
            log.error("任务数据记录不存在，taskDataId: {}", taskCallCenterId);
            throw new AiServerException(ResultCode.NO_AI_CONVERSATION_WECHAT_SUMMARY);
        }

        processRecord.set(callRecord);
        req.setTenantId(callRecord.getTenantId());

        // 根据数据源类型初始化内容
        String sourceType = callRecord.getDataSourceType();
        CustomerDataSourceTypeEnum dataSourceType = CustomerDataSourceTypeEnum.getByCode(sourceType);
        if (dataSourceType == null) {
            log.error("无效的数据源类型: {}", sourceType);
            throw new AiServerException(ResultCode.DIALOG_CONTENT_ANALYSIS_SOURCE_EMPTY);
        }

        currentDataSourceTypeLocal.set(sourceType);
        initContentByDataSource(req, callRecord, dataSourceType);

        return callRecord;
    }

    /**
     * 根据数据源类型初始化内容（已废弃，保留用于兼容）
     */
    private void initContentByDataSource(AiConversationProcessReq req, AiAnalysisTaskCallRecord callRecord, 
                                        CustomerDataSourceTypeEnum dataSourceType) {
        switch (dataSourceType) {
            case CALL_RECORD:
                initCallRecordContent(req, callRecord);
                break;
            case SMS_RECORD:
                initSmsRecordContent(req, callRecord);
                break;
            case WECHAT_RECORD:
                initWechatRecordContent(req, callRecord);
                break;
            case ENTERPRISE_WECHAT_RECORD:
                initEnterpriseWechatRecordContent(req, callRecord);
                break;
            case CUSTOMER_BASIC_INFO:
            case CUSTOMER_BUSINESS_DATA:
                initCustomerDataContent(req, callRecord);
                break;
            default:
                log.error("未处理的数据源类型: {}", dataSourceType);
                throw new AiServerException(ResultCode.DIALOG_CONTENT_ANALYSIS_SOURCE_EMPTY);
        }
    }

    /**
     * 初始化通话记录内容
     */
    private void initCallRecordContent(AiConversationProcessReq req, AiAnalysisTaskCallRecord callRecord) {
        Long chatContactId = callRecord.getChatContactId();
        AiPlannerChatContact chatContact = aiPlannerChatContactService.getById(chatContactId);
        if (chatContact == null) {
            log.error("通话记录不存在，chatContactId: {}", chatContactId);
            throw new AiServerException(ResultCode.DIALOG_CONTENT_ANALYSIS_SOURCE_EMPTY);
        }
        req.setContent("用户和理财师通话记录: " + chatContact.getProcessedContent());
        req.setUserId(chatContact.getUserId());
        req.setPlannerNo(chatContact.getPlannerNo());
        req.setCallTime(chatContact.getMsgTime());
        req.setSourceType(CustomerDataSourceTypeEnum.CALL_RECORD.getCode());
        req.setTenantId(callRecord.getTenantId());
    }

    /**
     * 初始化短信记录内容
     */
    private void initSmsRecordContent(AiConversationProcessReq req, AiAnalysisTaskCallRecord callRecord) {
        String customerId = callRecord.getCustomerId();
        AiAnalysisTask analysisTask = aiAnalysisTaskService.getById(callRecord.getAnalysisTaskId());
        String content = robotTenantSmsSendLogService.getMsgByCustomerId(customerId, req.getTenantId(), analysisTask.getStartTime(), analysisTask.getEndTime());
        
        if (Strings.isNullOrEmpty(content)) {
            log.warn("未找到客户短信记录，customerId: {}, tenantId: {}", customerId, req.getTenantId());
            req.setContent("用户没有收到短信");
        } else {
            req.setContent("用户收到的短信: " + content);
        }
        req.setCallTime(LocalDateTime.now());
        req.setUserId(customerId);
        req.setPlannerNo(callRecord.getPlannerNo());
        req.setSourceType(CustomerDataSourceTypeEnum.SMS_RECORD.getCode());
        req.setTenantId(callRecord.getTenantId());
    }

    /**
     * 初始化微信记录内容
     */
    private void initWechatRecordContent(AiConversationProcessReq req, AiAnalysisTaskCallRecord callRecord) {
        String customerId = callRecord.getCustomerId();
        // 查询微信聊天小结 - 通过customerId查询
        LambdaQueryWrapper<AiConversationChatSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationChatSummary::getCustomerId, customerId)
                .orderByDesc(AiConversationChatSummary::getCreateTime);
        List<AiConversationChatSummary> chatSummaries = aiConversationChatSummaryService.list(queryWrapper);
        
        if (chatSummaries.isEmpty()) {
            log.warn("未找到微信聊天记录，customerId: {}", customerId);
            req.setContent("");
            return;
        }

        AiConversationChatSummary chatSummary = chatSummaries.get(0);
        req.setContent("用户的微信聊天记录: " + chatSummary.getChatContent());
        req.setUserId(chatSummary.getCustomerId());
        req.setPlannerNo(chatSummary.getPlannerNo());
        req.setCallTime(chatSummary.getCreateTime());
        req.setSourceType(CustomerDataSourceTypeEnum.WECHAT_RECORD.getCode());
        req.setTenantId(chatSummary.getTenantId());
        
        LocalDate chatDate = chatSummary.getChatDate();
        if (chatDate != null) {
            req.setCallTime(chatDate.atTime(23, 59, 59));
        } else {
            req.setCallTime(chatSummary.getCreateTime());
        }
    }

    /**
     * 初始化企微记录内容
     */
    private void initEnterpriseWechatRecordContent(AiConversationProcessReq req, AiAnalysisTaskCallRecord callRecord) {
        String customerId = callRecord.getCustomerId();
        // 查询企微小结 - 通过userId查询
        LambdaQueryWrapper<AiConversationWechatSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationWechatSummary::getUserId, customerId)
                .orderByDesc(AiConversationWechatSummary::getCreateTime);
        List<AiConversationWechatSummary> wechatSummaries = aiConversationWechatSummaryService.list(queryWrapper);
        
        if (wechatSummaries.isEmpty()) {
            log.warn("未找到企微聊天记录，customerId: {}", customerId);
            req.setContent("");
            return;
        }

        AiConversationWechatSummary wechatSummary = wechatSummaries.get(0);
        req.setContent("用户的企业微信聊天记录: " + wechatSummary.getWechatContent());
        req.setUserId(wechatSummary.getUserId());
        req.setPlannerNo(wechatSummary.getPlannerId());
        req.setCallTime(wechatSummary.getCreateTime());
        req.setSourceType(CustomerDataSourceTypeEnum.ENTERPRISE_WECHAT_RECORD.getCode());
        req.setTenantId(wechatSummary.getTenantId());

        String intervalTag = wechatSummary.getIntervalTag();
        if (intervalTag != null && !intervalTag.trim().isEmpty()) {
            try {
                LocalDate date = LocalDate.parse(intervalTag, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                req.setCallTime(date.atTime(23, 59, 59));
            } catch (Exception e) {
                log.warn("解析企微小结时间标签失败，使用创建时间代替: {}", intervalTag, e);
                req.setCallTime(wechatSummary.getCreateTime());
            }
        } else {
            req.setCallTime(wechatSummary.getCreateTime());
        }
    }

    /**
     * 初始化客户数据内容（基本信息、业务数据、订单数据）
     */
    private void initCustomerDataContent(AiConversationProcessReq req, AiAnalysisTaskCallRecord callRecord) {
        // 根据数据源类型获取对应的数据
        String sourceType = req.getSourceType();
        String content = "";
        
        switch (sourceType) {
            case "5":
                content = "用户的基本信息: " + callRecord.getCustomerBaseData();
                break;
            case "6":
                content = "用户的业务信息: " + callRecord.getCustomerBusinessData();
                break;
            default:
                log.warn("未知的客户数据类型: {}", sourceType);
                break;
        }
        
        if (Strings.isNullOrEmpty(content)) {
            log.warn("客户数据内容为空，callRecordId: {}, sourceType: {}", callRecord.getId(), sourceType);
            req.setContent("");
            return;
        }
        req.setContent(content);
        req.setCallTime(callRecord.getCreateTime());
        req.setUserId(callRecord.getCustomerId());
        req.setPlannerNo(callRecord.getPlannerNo());
        req.setTenantId(callRecord.getTenantId());
        req.setSourceType(sourceType);
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        AiAnalysisTaskCallRecord callRecord = processRecord.get();
        Long sceneId = callRecord.getSceneId();
        
        // 查询场景配置
        AiAnalysisConfigScene aiAnalysisConfigScene = aiAnalysisConfigSceneService.getById(sceneId);
        
        // 如果已经初始化过，直接使用
        if (!Strings.isNullOrEmpty(purposeThreadLocal.get())) {
            String formattedIntent = formattedIntentThreadLocal.get();
            String formattedIntentKeys = formattedIntentKeyThreadLocal.get();
            return formatPrompt(lastPromptConfig.getPrompt(),
                    purposeThreadLocal.get(),
                    formattedIntent == null ? "" : formattedIntent,
                    formattedIntentKeys == null ? "" : formattedIntentKeys,
                    content);
        }

        if (aiAnalysisConfigScene == null) {
            log.error("客户数据分析场景不存在，sceneId: {}", sceneId);
            throw new AiServerException(ResultCode.DIALOG_CONTENT_ANALYSIS_SCENE_NOT_EXIST);
        }

        String prompt = aiAnalysisConfigScene.getPrompt();
        purposeThreadLocal.set(prompt);

        // 查询场景结果配置
        List<AiAnalysisConfigSceneResultVO> sceneResults = aiAnalysisConfigSceneResultService.listBySceneId(sceneId);
        Map<String, String> resultMap = new HashMap<>();
        Map<String, Long> idResultMap = new HashMap<>();

        if (sceneResults != null && !sceneResults.isEmpty()) {
            sceneResults.forEach(sceneResult -> {
                resultMap.put(sceneResult.getResultName(), sceneResult.getPrompt());
                idResultMap.put(sceneResult.getResultName(), sceneResult.getId());
            });
        }

        idIntentionThreadLocal.set(idResultMap);
        intentionThreadLocal.set(resultMap);

        // 格式化意向Map为字符串
        String formattedIntentString = resultMap.entrySet().stream()
                .map(entry -> entry.getKey() + "={" + entry.getValue() + "}")
                .collect(Collectors.joining("\n"));
        // 格式化全部key到一个字符串
        String formattedAllKeyString = String.join("\n", resultMap.keySet());

        formattedIntentThreadLocal.set(formattedIntentString);
        formattedIntentKeyThreadLocal.set(formattedAllKeyString);

        return formatPrompt(lastPromptConfig.getPrompt(), prompt, formattedIntentString, formattedAllKeyString, content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        aiReplyContent = aiReplyContent.trim();
        List<IntentResult> intentResults = new ArrayList<>();

        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            JSONObject rootNode = JSON.parseObject(aiReplyContent);

            // 解析多意向结果
            if (rootNode.containsKey("intents") && rootNode.getJSONArray("intents") != null) {
                JSONArray intentsArray = rootNode.getJSONArray("intents");
                for (int i = 0; i < intentsArray.size(); i++) {
                    JSONObject intentObj = intentsArray.getJSONObject(i);
                    String intentName = intentObj.getString("intent_name");
                    String intentDescription = intentObj.getString("intent_description");

                    // 验证intentName是否在配置的场景结果中
                    Map<String, String> intentionMap = intentionThreadLocal.get();
                    if (intentionMap != null && intentionMap.containsKey(intentName)) {
                        IntentResult result = new IntentResult();
                        result.setIntentName(intentName);
                        result.setIntentDescription(intentDescription);
                        intentResults.add(result);
                    } else {
                        log.warn("AI返回的意向名称不在配置的场景结果中: {}", intentName);
                    }
                }
            }

            parsedIntentsLocal.set(intentResults);
            log.info("客户数据分析完成，识别到{}个意向: {}", intentResults.size(),
                    intentResults.stream().map(IntentResult::getIntentName).collect(Collectors.toList()));

        } catch (Exception e) {
            log.error("客户数据分析结果解析失败:", e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Transactional
    @Override
    public void processResponse(AiConversationProcessReq req, AiAnalysisTaskCallRecord callRecord) {
        List<IntentResult> intentResults = parsedIntentsLocal.get();
        if (intentResults.isEmpty()) {
            log.info("未识别到有效意向，callRecordId: {}", callRecord.getId());
            return;
        }

        String dataSourceType = currentDataSourceTypeLocal.get();
        String dataSourceId = String.valueOf(req.getChatContactId());
        LocalDateTime dataSourceTime = req.getCallTime() != null ? req.getCallTime() : LocalDateTime.now();
        Long sceneId = callRecord.getSceneId();
        AiAnalysisConfigScene scene = aiAnalysisConfigSceneService.getById(sceneId);
        String sceneName = "";
        if (scene != null) {
            sceneName = scene.getSceneName();
        }
        // 获取意向ID映射
        Map<String, Long> idIntentionMap = idIntentionThreadLocal.get();

        // 处理每个识别到的意向
        StringBuilder intentNameAll = null;
        StringBuilder intentDesAll = null;
        for (IntentResult intentResult : intentResults) {
            String intentName = intentResult.getIntentName();
            String intentDescription = intentResult.getIntentDescription();

            Long sceneResultId = idIntentionMap.get(intentName);
            if (sceneResultId == null || Strings.isNullOrEmpty(sceneName)) {
                log.warn("未找到对应的场景结果ID: {}", intentName);
                continue;
            }

            String nameWithPrefix = buildScenePrefixedValue(sceneName, intentName);
            String descWithPrefix = buildScenePrefixedValue(sceneName, intentDescription);

            // 使用分布式锁确保数据唯一性
            String lockKey = String.format("yiren:ai:analysis_result:%d:%s:%d:%d",
                callRecord.getAnalysisTaskId(), callRecord.getCustomerId(), sceneId, sceneResultId);
            String lockValue = UUID.randomUUID().toString();
            
            try {
                // 尝试获取锁，30秒超时
                SetParams setParams = SetParams.setParams().nx().ex(30);
                String result = jedisCluster.set(lockKey, lockValue, setParams);
                if (!"OK".equals(result)) {
                    // 获取锁失败，稍等后重试一次
                    Thread.sleep(100);
                    result = jedisCluster.set(lockKey, lockValue, setParams);
                }
                
                if ("OK".equals(result)) {
                    try {
                        // 在锁保护下查询和更新
                        LambdaQueryWrapper<AiAnalysisTaskResult> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(AiAnalysisTaskResult::getAnalysisTaskId, callRecord.getAnalysisTaskId())
                                .eq(AiAnalysisTaskResult::getCustomerId, callRecord.getCustomerId())
                                .eq(AiAnalysisTaskResult::getSceneId, sceneId)
                                .eq(AiAnalysisTaskResult::getSceneResultId, sceneResultId)
                                .eq(AiAnalysisTaskResult::getIsDeleted, 0)
                                .last("limit 1");

                        AiAnalysisTaskResult existingResult = aiAnalysisTaskResultService.getOne(queryWrapper);

                        if (existingResult != null) {
                            // 已存在，更新数据源信息
                            updateExistingResult(existingResult, dataSourceType, dataSourceId, dataSourceTime, descWithPrefix, nameWithPrefix);
                        } else {
                            if (!Strings.isNullOrEmpty(nameWithPrefix)) {
                                if (intentNameAll == null) {
                                    intentNameAll = new StringBuilder(nameWithPrefix);
                                } else {
                                    intentNameAll.append(';').append(nameWithPrefix);
                                }
                            }
                            if (!Strings.isNullOrEmpty(descWithPrefix)) {
                                if (intentDesAll == null) {
                                    intentDesAll = new StringBuilder(descWithPrefix);
                                } else {
                                    intentDesAll.append(';').append(descWithPrefix);
                                }
                            }
                            // 不存在，创建新记录
                            createNewResult(callRecord, nameWithPrefix, sceneResultId, dataSourceType, dataSourceId,
                                    dataSourceTime, descWithPrefix, sceneId, sceneName);
                        }
                    } finally {
                        // 释放锁（使用Lua脚本确保原子性）
                        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
                        jedisCluster.eval(script, Collections.singletonList(lockKey), Collections.singletonList(lockValue));
                    }
                } else {
                    // 无法获取锁，降级处理：直接执行
                    log.warn("无法获取分布式锁，降级处理: {}", lockKey);
                    LambdaQueryWrapper<AiAnalysisTaskResult> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(AiAnalysisTaskResult::getAnalysisTaskId, callRecord.getAnalysisTaskId())
                            .eq(AiAnalysisTaskResult::getCustomerId, callRecord.getCustomerId())
                            .eq(AiAnalysisTaskResult::getSceneId, sceneId)
                            .eq(AiAnalysisTaskResult::getSceneResultId, sceneResultId)
                            .eq(AiAnalysisTaskResult::getIsDeleted, 0)
                            .last("limit 1");

                    AiAnalysisTaskResult existingResult = aiAnalysisTaskResultService.getOne(queryWrapper);

                    if (existingResult != null) {
                        updateExistingResult(existingResult, dataSourceType, dataSourceId, dataSourceTime, descWithPrefix, nameWithPrefix);
                    } else {
                        if (!Strings.isNullOrEmpty(nameWithPrefix)) {
                            if (intentNameAll == null) {
                                intentNameAll = new StringBuilder(nameWithPrefix);
                            } else {
                                intentNameAll.append(';').append(nameWithPrefix);
                            }
                        }
                        if (!Strings.isNullOrEmpty(descWithPrefix)) {
                            if (intentDesAll == null) {
                                intentDesAll = new StringBuilder(descWithPrefix);
                            } else {
                                intentDesAll.append(';').append(descWithPrefix);
                            }
                        }
                        createNewResult(callRecord, nameWithPrefix, sceneResultId, dataSourceType, dataSourceId,
                                dataSourceTime, descWithPrefix, sceneId, sceneName);
                    }
                }
            } catch (Exception e) {
                log.error("分布式锁处理异常: {}", lockKey, e);
                // 异常时也执行业务逻辑
                LambdaQueryWrapper<AiAnalysisTaskResult> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AiAnalysisTaskResult::getAnalysisTaskId, callRecord.getAnalysisTaskId())
                        .eq(AiAnalysisTaskResult::getCustomerId, callRecord.getCustomerId())
                        .eq(AiAnalysisTaskResult::getSceneId, sceneId)
                        .eq(AiAnalysisTaskResult::getSceneResultId, sceneResultId)
                        .eq(AiAnalysisTaskResult::getIsDeleted, 0)
                        .last("limit 1");

                AiAnalysisTaskResult existingResult = aiAnalysisTaskResultService.getOne(queryWrapper);

                if (existingResult != null) {
                    updateExistingResult(existingResult, dataSourceType, dataSourceId, dataSourceTime, descWithPrefix, nameWithPrefix);
                } else {
                    if (!Strings.isNullOrEmpty(nameWithPrefix)) {
                        if (intentNameAll == null) {
                            intentNameAll = new StringBuilder(nameWithPrefix);
                        } else {
                            intentNameAll.append(';').append(nameWithPrefix);
                        }
                    }
                    if (!Strings.isNullOrEmpty(descWithPrefix)) {
                        if (intentDesAll == null) {
                            intentDesAll = new StringBuilder(descWithPrefix);
                        } else {
                            intentDesAll.append(';').append(descWithPrefix);
                        }
                    }
                    createNewResult(callRecord, nameWithPrefix, sceneResultId, dataSourceType, dataSourceId,
                            dataSourceTime, descWithPrefix, sceneId, sceneName);
                }
            }
        }

        // 更新通话记录状态
        Long analysisTaskDataId = callRecord.getAnalysisTaskDataId();
        AiAnalysisTaskData aiAnalysisTaskData = aiAnalysisTaskDataService.getById(analysisTaskDataId);
        if (aiAnalysisTaskData != null) {
            updateFinishedSubTaskCount(aiAnalysisTaskData);
            String resultName = aiAnalysisTaskData.getResultName();
            if (intentNameAll != null && intentNameAll.length() > 0) {
                String value = intentNameAll.toString();
                aiAnalysisTaskData.setResultName(Strings.isNullOrEmpty(resultName) ? value : resultName + ";" + value);
            }
            String resultDes = aiAnalysisTaskData.getResultDes();
            if (intentDesAll != null && intentDesAll.length() > 0) {
                String value = intentDesAll.toString();
                aiAnalysisTaskData.setResultDes(Strings.isNullOrEmpty(resultDes) ? value : resultDes + ";" + value);
            }
            aiAnalysisTaskData.setUpdateTime(LocalDateTime.now());
            aiAnalysisTaskDataService.updateById(aiAnalysisTaskData);
        }
        callRecord.setStatus(2);
        callRecord.setAnalysisTime(LocalDateTime.now());
        aiAnalysisTaskCallRecordMapper.updateById(callRecord);

        log.info("客户数据分析结果保存完成，callRecordId: {}, 意向数量: {}", callRecord.getId(), intentResults.size());
    }

    private String buildScenePrefixedValue(String sceneName, String value) {
        if (Strings.isNullOrEmpty(value)) {
            return value;
        }
        if (Strings.isNullOrEmpty(sceneName)) {
            return value;
        }
        String prefix = sceneName + "-";
        if (value.startsWith(prefix)) {
            return value;
        }
        return prefix + value;
    }

    private void updateFinishedSubTaskCount(AiAnalysisTaskData aiAnalysisTaskData) {
        Integer finishedSubTaskCount = aiAnalysisTaskData.getFinishedSubTaskCount();
        if (finishedSubTaskCount == null || finishedSubTaskCount <= 0) {
            finishedSubTaskCount = 1;
        } else {
            finishedSubTaskCount = finishedSubTaskCount + 1;
        }
        aiAnalysisTaskData.setFinishedSubTaskCount(finishedSubTaskCount);
        if (finishedSubTaskCount >= aiAnalysisTaskData.getTotalSubTaskCount()) {
            aiAnalysisTaskData.setStatus(2);
        } else {
            aiAnalysisTaskData.setStatus(1);
        }
    }

    /**
     * 更新已存在的结果，追加数据源信息
     */
    private void updateExistingResult(AiAnalysisTaskResult existingResult, String dataSourceType,
                                       String dataSourceId, LocalDateTime dataSourceTime, String intentDescription, String intentName) {
        // 解析现有的匹配原因JSON
        List<MatchReason> existingReasons = new ArrayList<>();
        if (existingResult.getMatchReasons() != null && !existingResult.getMatchReasons().isEmpty()) {
            try {
                existingReasons = JSON.parseArray(existingResult.getMatchReasons(), MatchReason.class);
            } catch (Exception e) {
                log.warn("解析已有匹配原因JSON失败，将创建新的: {}", e.getMessage());
            }
        }

        // 添加新的匹配原因
        MatchReason newReason = new MatchReason();
        newReason.setDataSourceType(dataSourceType);
        newReason.setDataSourceId(dataSourceId);
        newReason.setDataSourceTime(dataSourceTime.toString());
        newReason.setMatchDescription(intentDescription);
        existingReasons.add(newReason);

        // 更新结果
        String matchReasonsJson = JSON.toJSONString(existingReasons);
        existingResult.setMatchReasons(matchReasonsJson);

        if (!Strings.isNullOrEmpty(intentName)) {
            existingResult.setResultName(intentName);
        }
        if (!Strings.isNullOrEmpty(intentDescription)) {
            existingResult.setResultDes(intentDescription);
        }

        // 更新数据源类型列表（去重）
        Set<String> dataSourceTypes = new HashSet<>();
        if (existingResult.getDataSourceTypes() != null && !existingResult.getDataSourceTypes().isEmpty()) {
            dataSourceTypes.addAll(Arrays.asList(existingResult.getDataSourceTypes().split(",")));
        }
        dataSourceTypes.add(dataSourceType);
        existingResult.setDataSourceTypes(String.join(",", dataSourceTypes));

        // 更新时间列表
        Set<String> msgTimes = new HashSet<>();
        if (existingResult.getMsgTimes() != null && !existingResult.getMsgTimes().isEmpty()) {
            msgTimes.addAll(Arrays.asList(existingResult.getMsgTimes().split(",")));
        }
        msgTimes.add(dataSourceTime.toString());
        existingResult.setMsgTimes(String.join(",", msgTimes));

        existingResult.setUpdateTime(LocalDateTime.now());
        aiAnalysisTaskResultService.updateById(existingResult);

        log.info("更新已存在的分析结果，resultId: {}, 新增数据源: {}", existingResult.getId(), dataSourceType);
    }

    /**
     * 创建新的分析结果
     */
    private void createNewResult(AiAnalysisTaskCallRecord callRecord, String intentName, Long sceneResultId,
                                  String dataSourceType, String dataSourceId, LocalDateTime dataSourceTime,
                                  String intentDescription, Long sceneId, String sceneName) {
        // 创建匹配原因JSON
        MatchReason matchReason = new MatchReason();
        matchReason.setDataSourceType(dataSourceType);
        matchReason.setDataSourceId(dataSourceId);
        matchReason.setDataSourceTime(dataSourceTime.toString());
        matchReason.setMatchDescription(intentDescription);

        List<MatchReason> matchReasons = new ArrayList<>();
        matchReasons.add(matchReason);
        String matchReasonsJson = JSON.toJSONString(matchReasons);

        // 创建新结果记录
        AiAnalysisTaskResult result = new AiAnalysisTaskResult();
        result.setAnalysisTaskId(callRecord.getAnalysisTaskId());
        result.setAnalysisTaskCallRecordId(callRecord.getId());
        result.setChatContactId(callRecord.getChatContactId());
        result.setSceneId(sceneId);
        result.setSceneName(sceneName);
        result.setSceneResultId(sceneResultId);
        result.setResultName(intentName);
        result.setResultDes(intentDescription);
        result.setDataSourceTypes(dataSourceType);
        result.setMatchReasons(matchReasonsJson);
        result.setMsgTimes(dataSourceTime.toString());
        result.setCustomerId(callRecord.getCustomerId());
        // 已完成
        result.setStatus(2);
        // 完成
        result.setFinalStatus(1);
        result.setAnalysisTime(LocalDateTime.now());
        result.setCreateTime(LocalDateTime.now());
        result.setUpdateTime(LocalDateTime.now());
        result.setCreateUser(callRecord.getCreateUser());
        result.setCreateUserName(callRecord.getCreateUserName());
        result.setUpdateUser(callRecord.getCreateUser());
        result.setTenantId(callRecord.getTenantId());
        result.setIsDeleted(0);

        aiAnalysisTaskResultService.save(result);

        log.info("创建新的分析结果，resultId: {}, 意向: {}, 数据源: {}",
                result.getId(), intentName, dataSourceType);
    }

    @Override
    public void processErrorCallback(AiConversationProcessReq processReq, boolean finished) {
        log.info("客户对话任务内容分析处理失败 processErrorCallback:{}", finished);
        if (finished) {
            AiAnalysisTaskCallRecord analysisTaskCallRecord = processRecord.get();
            Long analysisTaskDataId = analysisTaskCallRecord.getAnalysisTaskDataId();
            AiAnalysisTaskData aiAnalysisTaskData = aiAnalysisTaskDataService.getById(analysisTaskDataId);
            if (aiAnalysisTaskData != null) {
                updateFinishedSubTaskCount(aiAnalysisTaskData);
                aiAnalysisTaskData.setUpdateTime(LocalDateTime.now());
                aiAnalysisTaskDataService.updateById(aiAnalysisTaskData);
            }
        }
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.AI_CUSTOMER_TASK_CONTENT_ANALYSIS;
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (parsedIntentsLocal != null) {
            parsedIntentsLocal.remove();
        }
        if (currentDataSourceTypeLocal != null) {
            currentDataSourceTypeLocal.remove();
        }
        if (purposeThreadLocal != null) {
            purposeThreadLocal.remove();
        }
        if (intentionThreadLocal != null) {
            intentionThreadLocal.remove();
        }
        if (formattedIntentThreadLocal != null) {
            formattedIntentThreadLocal.remove();
        }
        if (formattedIntentKeyThreadLocal != null) {
            formattedIntentKeyThreadLocal.remove();
        }
        if (idIntentionThreadLocal != null) {
            idIntentionThreadLocal.remove();
        }
    }

    private String formatPrompt(String template, Object... args) {
        if (Strings.isNullOrEmpty(template)) {
            return "";
        }
        try {
            return String.format(template, args);
        } catch (IllegalFormatException ex) {
            log.warn("提示词格式化失败 template:{} args:{} error:{}", template, args.length, ex.getMessage());
            StringBuilder builder = new StringBuilder(template);
            if (args != null && args.length > 0) {
                builder.append("\n");
                for (Object arg : args) {
                    builder.append(arg == null ? "" : arg.toString()).append("\n");
                }
            }
            return builder.toString();
        }
    }

    /**
     * 意向识别结果内部类
     */
    @Data
    private static class IntentResult {
        private String intentName;
        private String intentDescription;
    }

    /**
     * 匹配原因内部类（用于JSON序列化）
     */
    @Data
    public static class MatchReason {
        /**
         * 数据源类型：1通话记录，2短信记录，3微信记录，4企微记录，5客户基本信息，6客户业务数据，7客户订单数据
         */
        private String dataSourceType;

        /**
         * 数据源ID
         */
        private String dataSourceId;

        /**
         * 数据源时间
         */
        private String dataSourceTime;

        /**
         * 匹配描述
         */
        private String matchDescription;
    }
}
