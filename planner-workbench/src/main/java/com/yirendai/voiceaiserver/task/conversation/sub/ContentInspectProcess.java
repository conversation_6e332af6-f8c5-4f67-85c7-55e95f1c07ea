package com.yirendai.voiceaiserver.task.conversation.sub;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yirendai.voiceaiserver.biz.ContentCheckBiz;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.model.contentcheck.MatchResult;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.AiConversationInspectDetail;
import com.yirendai.workbench.entity.AiConversationInspectResult;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiConversationInspectDetailMapper;
import com.yirendai.workbench.mapper.AiConversationInspectResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ContentInspectProcess extends AbstractAiConversationProcess<AiConversationInspectResult> {

    @Resource
    private AiConversationInspectResultMapper aiConversationInspectResultMapper;
    @Resource
    private ContentCheckBiz contentCheckBiz;
    @Resource
    private AiConversationInspectDetailMapper aiConversationInspectDetailMapper;

    private final ThreadLocal<Map<Integer, String>> intentHashMap = ThreadLocal.withInitial(HashMap::new);

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Transactional
    @Override
    public void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        processResponse(processReq, (AiConversationInspectResult) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        validatePlannerChatContact(req);
    }

    @Override
    protected AiConversationInspectResult getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        LambdaQueryWrapper<AiConversationInspectResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationInspectResult::getChatContactId, aiConversationProcessReq.getChatContactId());
        AiConversationInspectResult aiConversationInspectResult = aiConversationInspectResultMapper.selectOne(queryWrapper);

        if (aiConversationInspectResult != null) {
            if (Objects.equals(aiConversationInspectResult.getIntentStatus(), AiConversationProcessStatusEnum.FINISHED.getCode())) {
                this.finished.set(true);
            }
        } else {
            // 新增AI通话稽查记录
            aiConversationInspectResult = new AiConversationInspectResult();
            aiConversationInspectResult.setChatContactId(aiConversationProcessReq.getChatContactId());
            aiConversationInspectResult.setPlannerNo(aiConversationProcessReq.getPlannerNo());
            aiConversationInspectResult.setUserId(aiConversationProcessReq.getUserId());
            aiConversationInspectResult.setIntentStatus(AiConversationProcessStatusEnum.NOT_START.getCode());
            aiConversationInspectResult.setCreateTime(LocalDateTime.now());
            aiConversationInspectResult.setUpdateTime(LocalDateTime.now());
            aiConversationInspectResult.setTenantId(aiConversationProcessReq.getTenantId());
            aiConversationInspectResult.setIsDeleted(0);
            aiConversationInspectResultMapper.insert(aiConversationInspectResult);
        }
        return aiConversationInspectResult;
    }

    @Override
    protected void otherPreProcess() {
        AiConversationProcessReq aiConversationProcessReq = req.get();
        AiConversationInspectResult aiConversationInspectResult = processRecord.get();
        // 敏感词检测
        List<MatchResult> matchResults = contentCheckBiz.filterWord(aiConversationProcessReq.getContent());
        if (!matchResults.isEmpty()) {
            // 保存敏感词匹配记录
            for (MatchResult matchResult : matchResults) {
                AiConversationInspectDetail aiConversationInspectDetail = new AiConversationInspectDetail();
                aiConversationInspectDetail.setChatContactId(aiConversationProcessReq.getChatContactId());
                aiConversationInspectDetail.setPlannerNo(aiConversationProcessReq.getPlannerNo());
                aiConversationInspectDetail.setUserId(aiConversationProcessReq.getUserId());
                aiConversationInspectDetail.setInspectResultId(aiConversationInspectResult.getId());
                aiConversationInspectDetail.setInspectType(0);
                aiConversationInspectDetail.setSensitiveWord(matchResult.getWord());
                aiConversationInspectDetail.setStartIndex(matchResult.getStartIndex());
                aiConversationInspectDetail.setCreateTime(LocalDateTime.now());
                aiConversationInspectDetail.setUpdateTime(LocalDateTime.now());
                aiConversationInspectDetail.setTenantId(aiConversationProcessReq.getTenantId());
                aiConversationInspectDetail.setIsDeleted(0);
                aiConversationInspectDetailMapper.insert(aiConversationInspectDetail);
            }

            // 更新AI通话稽查记录
            ObjectMapper objectMapper = new ObjectMapper();
            String matchResultJson;
            try {
                matchResultJson = objectMapper.writeValueAsString(matchResults);
            } catch (JsonProcessingException e) {
                throw new AiServerException(ResultCode.SERIALIZE_ERROR);
            }
            aiConversationInspectResult.setViolation(1);
            aiConversationInspectResult.setSensitiveWordSize(matchResults.size());
            aiConversationInspectResult.setSensitiveWordResult(matchResultJson);
        } else {
            aiConversationInspectResult.setViolation(0);
        }
        aiConversationInspectResult.setIntentStatus(AiConversationProcessStatusEnum.PROCESSING.getCode());
        aiConversationInspectResult.setUpdateTime(LocalDateTime.now());
        aiConversationInspectResultMapper.updateById(aiConversationInspectResult);
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        log.info("AI对话内容稽查处理，处理内容: {}", content);
        return String.format(lastPromptConfig.getPrompt(), content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        AiConversationInspectResult aiConversationInspectResult = processRecord.get();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            if (aiReplyContent == null) {
                return;
            }
            List<Map<String, Object>> list = objectMapper.readValue(aiReplyContent, new TypeReference<List<Map<String, Object>>>(){});
            // 遍历并输出每个对象的信息
            for (Map<String, Object> item : list) {
                Integer no = (Integer) item.get("no");
                String text = (String) item.get("text");
                if (no == 0) {
                    continue;
                }
                intentHashMap.get().put(no, text);
            }
        } catch (JsonProcessingException e) {
            log.error("ai稽查结果解析失败:", e);
            aiConversationInspectResult.setIntentStatus(AiConversationProcessStatusEnum.FAILED.getCode());
            aiConversationInspectResult.setUpdateTime(LocalDateTime.now());
            aiConversationInspectResultMapper.updateById(aiConversationInspectResult);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Transactional
    @Override
    public void processResponse(AiConversationProcessReq aiConversationProcessReq, AiConversationInspectResult aiConversationInspectResult) {
        Map<Integer, String> integerStringMap = intentHashMap.get();
        int violation = aiConversationInspectResult.getViolation();
        // 保存AI通话稽查记录
        ObjectMapper objectMapper = new ObjectMapper();
        ArrayNode arrayNode = objectMapper.createArrayNode();
        if (integerStringMap.isEmpty()) {
            return;
        }
        for (Map.Entry<Integer, String> entry : integerStringMap.entrySet()) {
            ObjectNode objectNode = objectMapper.createObjectNode();
            objectNode.put("no", entry.getKey());
            objectNode.put("text", entry.getValue());
            arrayNode.add(objectNode);
            Integer no = entry.getKey();
            String text = entry.getValue();
            if (no == 0) {
                continue;
            }
            violation = 1;
            AiConversationInspectDetail aiConversationInspectDetail = new AiConversationInspectDetail();
            aiConversationInspectDetail.setChatContactId(aiConversationProcessReq.getChatContactId());
            aiConversationInspectDetail.setPlannerNo(aiConversationProcessReq.getPlannerNo());
            aiConversationInspectDetail.setUserId(aiConversationProcessReq.getUserId());
            aiConversationInspectDetail.setInspectResultId(aiConversationInspectResult.getId());
            aiConversationInspectDetail.setInspectType(1);
            aiConversationInspectDetail.setInspectIntentId(no);
            aiConversationInspectDetail.setInspectIntentText(text);
            aiConversationInspectDetail.setCreateTime(LocalDateTime.now());
            aiConversationInspectDetail.setUpdateTime(LocalDateTime.now());
            aiConversationInspectDetail.setTenantId(aiConversationProcessReq.getTenantId());
            aiConversationInspectDetail.setIsDeleted(0);
            aiConversationInspectDetailMapper.insert(aiConversationInspectDetail);
        }

        String matchResultJson = null;
        try {
            matchResultJson = objectMapper.writeValueAsString(arrayNode);
        } catch (JsonProcessingException e) {
            log.error("AI内容稽查结果序列化失败", e);
        }

        // 更新AI通话稽查记录
        aiConversationInspectResult.setViolation(violation);
        aiConversationInspectResult.setInspectIntentSize(integerStringMap.size());
        aiConversationInspectResult.setInspectIntentText(matchResultJson);
        aiConversationInspectResult.setIntentStatus(AiConversationProcessStatusEnum.FINISHED.getCode());
        aiConversationInspectResult.setUpdateTime(LocalDateTime.now());
        aiConversationInspectResultMapper.updateById(aiConversationInspectResult);
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.CONTENT_CHECK;
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (intentHashMap.get() != null) {
            intentHashMap.remove();
        }
    }
}
