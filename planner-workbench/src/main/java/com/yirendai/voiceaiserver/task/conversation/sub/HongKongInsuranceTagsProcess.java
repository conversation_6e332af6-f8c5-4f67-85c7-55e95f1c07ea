package com.yirendai.voiceaiserver.task.conversation.sub;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationDataSourceEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.IAiConversationFlowResultService;
import com.yirendai.voiceaiserver.service.IAiUserTagCategoryService;
import com.yirendai.voiceaiserver.service.IAiUserTagDictionaryService;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.util.TaskIdUtil;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.service.IAiConversationChatSummaryService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.voiceaiserver.service.AiConversationWechatSummaryService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 港险标签识别处理器
 * 专门负责标签识别，要求已完成主意图识别
 * 
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Component
public class HongKongInsuranceTagsProcess extends AbstractAiConversationProcess<AiConversationTagsProcess> {

    private static final String FLOW_TYPE = "HK_INSURANCE";

    @Resource
    protected IAiUserTagCategoryService iAiUserTagCategoryService;
    @Resource
    protected IAiUserTagDictionaryService iAiUserTagDictionaryService;
    @Resource
    protected IAiConversationFlowResultService iAiConversationFlowResultService;
    @Resource
    private IAiPlannerChatContactService aiPlannerChatContactService;
    @Resource
    private IAiConversationChatSummaryService aiConversationChatSummaryService;
    
    @Resource
    private AiConversationWechatSummaryService aiConversationWechatSummaryService;

    @Value("${third.access.tenantId.yiren}")
    private String tenantIdYiren;

    protected ThreadLocal<Set<NodeResult>> reachedNodes = ThreadLocal.withInitial(HashSet::new);
    protected ThreadLocal<String> intentCode = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        if (type == AiModelType.COMMON_MODEL) {
            type = AiModelType.TAG_DISTILLATION_MODEL;
        }
        String response = chatService.chatAiContent(question, model, temperature, type, systemPrompt);
        return extractAiReplyContent(response);
    }

    @Transactional
    @Override
    public void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        processResponse(processReq, (AiConversationTagsProcess) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        if (Objects.equals(req.getSourceType(), AiConversationDataSourceEnum.QI_WECHAT.getCode())) {
            // 企微数据源（sourceType = "1"）
            AiConversationWechatSummary aiConversationWechatSummary = aiConversationWechatSummaryService.getById(req.getChatContactId());
            if (aiConversationWechatSummary != null) {
                req.setContent(aiConversationWechatSummary.getWechatContent());
                req.setUserId(aiConversationWechatSummary.getUserId());
                req.setPlannerNo(aiConversationWechatSummary.getPlannerId());
                req.setTenantId(aiConversationWechatSummary.getTenantId());
                
                // 设置chatId：企微数据源取interval_tag
                req.setChatId(aiConversationWechatSummary.getIntervalTag());
                
                String intervalTag = aiConversationWechatSummary.getIntervalTag();
                if (intervalTag != null && !intervalTag.trim().isEmpty()) {
                    try {
                        LocalDate date = LocalDate.parse(intervalTag, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        req.setCallTime(date.atTime(23, 59, 59));
                    } catch (Exception e) {
                        log.warn("解析企微小结时间标签失败，使用创建时间代替: {}", intervalTag, e);
                        req.setCallTime(aiConversationWechatSummary.getCreateTime());
                    }
                } else {
                    req.setCallTime(aiConversationWechatSummary.getCreateTime());
                }
            }
        } else if (Objects.equals(req.getSourceType(), AiConversationDataSourceEnum.WECHAT.getCode())){
            AiConversationChatSummary aiConversationChatSummary = aiConversationChatSummaryService.getById(req.getChatContactId());
            if (aiConversationChatSummary != null) {
                req.setContent(aiConversationChatSummary.getChatContent());
                req.setUserId(aiConversationChatSummary.getCustomerId());
                req.setPlannerNo(aiConversationChatSummary.getPlannerNo());
                req.setTenantId(aiConversationChatSummary.getTenantId());
                LocalDate chatDate = aiConversationChatSummary.getChatDate();
                if (chatDate != null) {
                    req.setChatId(String.valueOf(chatDate));
                    req.setCallTime(chatDate.atTime(23, 59, 59));
                } else {
                    req.setCallTime(aiConversationChatSummary.getCreateTime());
                }
            }
        } else {
            validatePlannerChatContact(req);
        }
    }

    @Override
    protected AiConversationTagsProcess getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        return new AiConversationTagsProcess();
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        // 获取当前检测到的主意图
        AiConversationProcessReq processReq = req.get();
        String detectedIntent = intentCode.get();
        if (intentCode.get() == null) {
            detectedIntent = getIntentFromFlowResult(processReq.getChatContactId(), processReq.getTenantId(), processReq.getSourceType());
            if (detectedIntent == null) {
                log.error("港险标签识别失败：未找到主意图识别结果，对话ID: {}", processReq.getChatContactId());
                throw new AiServerException(ResultCode.AI_INTENT_NOT_FOUND);
            }
            intentCode.set(detectedIntent);
        }
        
        List<AiUserTagDictionary> intentTags = getTagsByIntent(tenantId, detectedIntent);
        if (intentTags.isEmpty()) {
            throw new AiServerException(ResultCode.NO_AI_TAG_CATEGORY);
        }
        
        StringBuilder nodeDesc = new StringBuilder();
        for (AiUserTagDictionary tag : intentTags) {
            // 获取一级和二级分类信息
            AiUserTagCategory category = iAiUserTagCategoryService.selectById(tag.getUserTagCategoryId());
            AiUserTagCategory topCategory = null;
            if (category != null && category.getTopParentId() != null && !category.getTopParentId().equals(0L)) {
                topCategory = iAiUserTagCategoryService.selectById(category.getTopParentId());
            } else if (category != null && category.getTopParentId() != null && category.getTopParentId().equals(0L)) {
                topCategory = category;
            }
            
            String topCategoryName = (topCategory != null) ? topCategory.getCategoryName() : "未知一级";
            String categoryName = (category != null) ? category.getCategoryName() : "未知二级";
            String tagDescription = tag.getMatchDes();
            
            nodeDesc.append("- 一级[").append(topCategoryName).append("] -> 二级[").append(categoryName)
                   .append("] -> 三级[").append(tag.getTagName()).append("] (节点编码: ").append(tag.getNodeCode())
                   .append("; 描述: ").append(tagDescription)
                   .append("; 关键词: ").append(tag.getKeywords()).append(")\n");
        }
        return String.format(lastPromptConfig.getPrompt(), nodeDesc.toString(), content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        ObjectMapper objectMapper = new ObjectMapper();
        aiReplyContent = aiReplyContent.trim();
        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            JsonNode nodesNode = rootNode.get("reached_nodes");
            if (nodesNode != null && nodesNode.isArray()) {
                for (JsonNode node : nodesNode) {
                    String nodeCode = node.get("node_code").asText();
                    // 根据节点类型决定状态处理逻辑
                    int reachedStatus = 0;
                    if (node.has("reached_status")) {
                        int rawStatus = node.get("reached_status").asInt(0);
                        reachedStatus = normalizeNodeStatus(nodeCode, rawStatus);
                    } else if (node.has("reached")) {
                        // 兼容旧版本格式
                        boolean reached = node.get("reached").asBoolean(false);
                        reachedStatus = reached ? 1 : 0;
                    }
                    
                    BigDecimal confidence = BigDecimal.ZERO;
                    if (node.has("confidence")) {
                        confidence = new BigDecimal(node.get("confidence").asDouble());
                    }
                    String matchedKeywords = node.has("matched_keywords") ? node.get("matched_keywords").asText() : null;
                    
                    // 所有状态都记录，不仅仅是触达的
                    reachedNodes.get().add(new NodeResult(nodeCode, reachedStatus, confidence, matchedKeywords));
                }
            }
        } catch (JsonProcessingException e) {
            log.error("港险流程节点识别结果解析失败:", e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Transactional
    @Override
    public void processResponse(AiConversationProcessReq aiConversationProcessReq, AiConversationTagsProcess aiConversationTagsProcess) {
        String tenantId = aiConversationProcessReq.getTenantId();
        String taskId = aiConversationProcessReq.getTaskId();
        String model = aiPromptRecordService.getLatestPromptRecordByScene(getPromptScene().getCode()).getModel();
        Float temperature = aiPromptRecordService.getLatestPromptRecordByScene(getPromptScene().getCode()).getTemperature();

        // 获取源数据的理财师ID、用户ID和对话时间
        String plannerNo = aiConversationProcessReq.getPlannerNo();
        String userId = aiConversationProcessReq.getUserId();
        LocalDateTime chatTime = aiConversationProcessReq.getCallTime();

        List<AiUserTagDictionary> allIntentTags = getTagsByIntent(tenantId, intentCode.get());
        Set<NodeResult> reachedSet = reachedNodes.get();
        List<AiConversationFlowResult> results = new ArrayList<>();

        for (AiUserTagDictionary tag : allIntentTags) {
            AiConversationFlowResult result = new AiConversationFlowResult();
            result.setSourceType(aiConversationProcessReq.getSourceType());
            result.setSourceId(aiConversationProcessReq.getChatContactId());
            result.setChatId(aiConversationProcessReq.getChatId());
            result.setTenantId(tenantId);
            result.setPlannerNo(plannerNo);
            result.setUserId(userId);
            result.setFlowType(FLOW_TYPE);
            result.setIntentCode(intentCode.get());
            result.setNodeCode(tag.getNodeCode());
            result.setNodeName(tag.getTagName());
            result.setNodeLevel(3);
            result.setModel(model);
            result.setTemperature(temperature);
            result.setTaskId(taskId);
            result.setChatTime(chatTime);
            result.setCreateTime(LocalDateTime.now());
            result.setUpdateTime(LocalDateTime.now());
            result.setIsDeleted(0);

            // 检查节点状态：0=空，1=是，2=否
            NodeResult nodeResult = findNodeResult(reachedSet, tag.getNodeCode());
            if (nodeResult != null) {
                result.setIsReached(nodeResult.reachedStatus);
                result.setConfidenceScore(nodeResult.confidence);
                result.setMatchedKeywords(nodeResult.matchedKeywords);
            } else {
                // 未找到对应结果，设置为空状态
                result.setIsReached(0);
                result.setConfidenceScore(BigDecimal.ZERO);
            }
            results.add(result);
        }

        // 先删除现有的节点组结果（保留主意图结果和其他组的结果），再保存新结果
        if (!results.isEmpty()) {
            // 从当前任务ID中提取节点组
            String currentTaskId = aiConversationProcessReq.getTaskId();
            String nodeGroup = TaskIdUtil.extractNodeGroup(currentTaskId);
            
            // 删除现有的该节点组结果（nodeLevel > 0），保留主意图结果（nodeLevel = 0）和其他组的结果
            deleteNodeResultsByGroup(aiConversationProcessReq.getSourceType(), 
                aiConversationProcessReq.getChatContactId(), 
                tenantId, FLOW_TYPE, intentCode.get(), nodeGroup);
            
            // 保存新的节点结果
            iAiConversationFlowResultService.batchSave(results);
            log.info("港险标签识别完成，保存 {} 个节点结果（已删除旧的{}组节点结果，保留主意图结果和其他组结果）", results.size(), nodeGroup);
        }
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.HONG_KONG_INSURANCE_NODES;
    }

    /**
     * 根据主意图获取对应的标签列表
     * 如果任务ID包含节点组，则只返回该节点组的标签
     */
    private List<AiUserTagDictionary> getTagsByIntent(String tenantId, String intentCode) {
        // 从当前任务ID中提取节点组
        String currentTaskId = req.get() != null ? req.get().getTaskId() : null;
        String nodeGroup = TaskIdUtil.extractNodeGroup(currentTaskId);
        
        LambdaQueryWrapper<AiUserTagDictionary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUserTagDictionary::getTagType, intentCode)
                .eq(AiUserTagDictionary::getTenantId, tenantId)
                .eq(AiUserTagDictionary::getStatus, 1)
                .eq(AiUserTagDictionary::getIsDeleted, 0);
        
        // 如果有节点组，则只查询该节点组的标签
        if (nodeGroup != null && !nodeGroup.trim().isEmpty()) {
            queryWrapper.eq(AiUserTagDictionary::getNodeGroup, nodeGroup);
            log.info("根据节点组查询标签: intentCode={}, nodeGroup={}, taskId={}", intentCode, nodeGroup, currentTaskId);
        } else {
            // 没有节点组时，查询所有标签（兼容旧版本）
            log.info("查询所有标签: intentCode={}, taskId={}", intentCode, currentTaskId);
        }
        
        queryWrapper.orderByAsc(AiUserTagDictionary::getSortOrder);
        return iAiUserTagDictionaryService.list(queryWrapper);
    }

    /**
     * 从流程结果表中获取主意图
     * 支持子意图类型的任务ID查询
     */
    private String getIntentFromFlowResult(Long chatContactId, String tenantId, String sourceType) {
        // 如果sourceType为空，默认使用ai_planner_chat_contact
        if (sourceType == null || sourceType.trim().isEmpty()) {
            sourceType = "ai_planner_chat_contact";
        }
        
        // 从当前请求中获取任务ID，提取子意图类型
        String currentTaskId = req.get() != null ? req.get().getTaskId() : null;
        String subIntentType = TaskIdUtil.extractSubIntentType(currentTaskId);
        
        LambdaQueryWrapper<AiConversationFlowResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationFlowResult::getSourceId, chatContactId)
                .eq(AiConversationFlowResult::getSourceType, sourceType)
                .eq(AiConversationFlowResult::getTenantId, tenantId)
                .eq(AiConversationFlowResult::getFlowType, FLOW_TYPE)
                .eq(AiConversationFlowResult::getNodeCode, "INTENT")
                .eq(AiConversationFlowResult::getIsReached, 1)
                .eq(AiConversationFlowResult::getIsDeleted, 0);
        
        // 如果是子意图任务，需要查询对应的主意图任务ID
        if (subIntentType != null) {
            // 生成对应的主意图任务ID
            String intentTaskId = TaskIdUtil.generateSubIntentTaskId(subIntentType, 
                String.valueOf(AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode()), 
                sourceType, chatContactId);
            queryWrapper.eq(AiConversationFlowResult::getTaskId, intentTaskId);
        } else {
            throw new AiServerException(ResultCode.AI_INTENT_NOT_MATCH);
        }
        
        queryWrapper.orderByDesc(AiConversationFlowResult::getCreateTime)
                .last("LIMIT 1");
        
        AiConversationFlowResult intentResult = iAiConversationFlowResultService.getOne(queryWrapper);
        return intentResult != null ? intentResult.getIntentCode() : null;
    }

    private NodeResult findNodeResult(Set<NodeResult> results, String nodeCode) {
        return results.stream()
                .filter(nr -> nr.nodeCode.equals(nodeCode))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据节点类型规范化状态值
     * 从数据库读取节点类型配置，支持三状态和二状态节点
     */
    private int normalizeNodeStatus(String nodeCode, int rawStatus) {
        // 从数据库查询节点匹配类型
        AiUserTagDictionary tag = getTagByNodeCode(nodeCode);
        if (tag != null && tag.getMatchType() != null) {
            if (tag.getMatchType() == 2) {
                // 三状态节点：保持原状态（0=空，1=是，2=否）
                return rawStatus;
            } else {
                // 二状态节点：只要不是0就转换为1（0=空，1=是）
                return rawStatus > 0 ? 1 : 0;
            }
        } else {
            // 默认按二状态处理
            return rawStatus > 0 ? 1 : 0;
        }
    }
    
    /**
     * 根据节点编码查询标签信息
     */
    private AiUserTagDictionary getTagByNodeCode(String nodeCode) {
        // 从当前租户的标签中查找
        String tenantId = req.get() != null ? req.get().getTenantId() : tenantIdYiren;
        LambdaQueryWrapper<AiUserTagDictionary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUserTagDictionary::getNodeCode, nodeCode)
                .eq(AiUserTagDictionary::getTenantId, tenantId)
                .eq(AiUserTagDictionary::getStatus, 1)
                .eq(AiUserTagDictionary::getIsDeleted, 0)
                .last("LIMIT 1");
        return iAiUserTagDictionaryService.getOne(queryWrapper);
    }

    @Data
    private static class NodeResult {
        String nodeCode;
        Integer reachedStatus; // 0=空，1=是，2=否
        BigDecimal confidence;
        String matchedKeywords;

        public NodeResult(String nodeCode, Integer reachedStatus, BigDecimal confidence, String matchedKeywords) {
            this.nodeCode = nodeCode;
            this.reachedStatus = reachedStatus;
            this.confidence = confidence;
            this.matchedKeywords = matchedKeywords;
        }
    }

    /**
     * 按节点组删除节点结果，保留主意图结果和其他组的结果
     */
    private void deleteNodeResultsByGroup(String sourceType, Long sourceId, String tenantId, String flowType, String intentCode, String nodeGroup) {
        if (nodeGroup == null || nodeGroup.trim().isEmpty()) {
            // 如果没有节点组，则删除所有节点结果（兼容旧版本）
            deleteNodeResultsOnly(sourceType, sourceId, tenantId, flowType, intentCode);
            return;
        }
        
        // 先查询该节点组下的所有节点编码
        LambdaQueryWrapper<AiUserTagDictionary> tagQueryWrapper = new LambdaQueryWrapper<>();
        tagQueryWrapper.eq(AiUserTagDictionary::getTagType, intentCode)
                .eq(AiUserTagDictionary::getTenantId, tenantId)
                .eq(AiUserTagDictionary::getNodeGroup, nodeGroup)
                .eq(AiUserTagDictionary::getStatus, 1)
                .eq(AiUserTagDictionary::getIsDeleted, 0);
        
        List<AiUserTagDictionary> groupTags = iAiUserTagDictionaryService.list(tagQueryWrapper);
        if (groupTags.isEmpty()) {
            log.info("未找到节点组{}的标签，跳过删除操作", nodeGroup);
            return;
        }
        
        // 提取该组的所有节点编码
        List<String> nodeCodesInGroup = groupTags.stream()
                .map(AiUserTagDictionary::getNodeCode)
                .collect(java.util.stream.Collectors.toList());
        
        // 删除该节点组的结果，保留主意图结果（nodeLevel = 0）和其他组的结果
        LambdaQueryWrapper<AiConversationFlowResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationFlowResult::getSourceType, sourceType)
                .eq(AiConversationFlowResult::getSourceId, sourceId)
                .eq(AiConversationFlowResult::getTenantId, tenantId)
                .eq(AiConversationFlowResult::getFlowType, flowType)
                .eq(AiConversationFlowResult::getIntentCode, intentCode)
                .gt(AiConversationFlowResult::getNodeLevel, 0) // 只删除节点结果（nodeLevel > 0），保留主意图结果（nodeLevel = 0）
                .in(AiConversationFlowResult::getNodeCode, nodeCodesInGroup); // 只删除该组的节点
        
        int deletedCount = iAiConversationFlowResultService.count(queryWrapper);
        if (deletedCount > 0) {
            iAiConversationFlowResultService.remove(queryWrapper);
            log.info("删除旧的{}组节点结果，保留主意图结果和其他组结果。删除数量={}, nodeGroup={}, sourceType={}, sourceId={}, flowType={}, intentCode={}", 
                nodeGroup, deletedCount, nodeGroup, sourceType, sourceId, flowType, intentCode);
        } else {
            log.info("未找到需要删除的{}组节点结果", nodeGroup);
        }
    }
    
    /**
     * 只删除节点结果，保留主意图结果
     */
    private void deleteNodeResultsOnly(String sourceType, Long sourceId, String tenantId, String flowType, String intentCode) {
        LambdaQueryWrapper<AiConversationFlowResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationFlowResult::getSourceType, sourceType)
                .eq(AiConversationFlowResult::getSourceId, sourceId)
                .eq(AiConversationFlowResult::getTenantId, tenantId)
                .eq(AiConversationFlowResult::getFlowType, flowType)
                .eq(AiConversationFlowResult::getIntentCode, intentCode)
                .gt(AiConversationFlowResult::getNodeLevel, 0); // 只删除节点结果（nodeLevel > 0），保留主意图结果（nodeLevel = 0）
        
        int deletedCount = iAiConversationFlowResultService.count(queryWrapper);
        if (deletedCount > 0) {
            iAiConversationFlowResultService.remove(queryWrapper);
            log.info("删除旧的节点结果，保留主意图结果。删除数量={}, sourceType={}, sourceId={}, flowType={}, intentCode={}", 
                deletedCount, sourceType, sourceId, flowType, intentCode);
        }
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (reachedNodes != null) {
            reachedNodes.remove();
        }
        if (intentCode != null) {
            intentCode.remove();
        }
    }
}