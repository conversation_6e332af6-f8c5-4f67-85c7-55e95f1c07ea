package com.yirendai.voiceaiserver.task.conversation.sub;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yirendai.robot.modules.session.entity.RobotSession;
import com.yirendai.robot.modules.session.entity.RobotSessionSummary;
import com.yirendai.robot.modules.session.service.IRobotSessionService;
import com.yirendai.robot.modules.session.service.IRobotSessionSummaryService;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RobotCallUserSummaryProcess extends AbstractAiConversationProcess<RobotSessionSummary> {

    @Resource
    private IRobotSessionService robotSessionService;

    @Resource
    private IRobotSessionSummaryService robotSessionSummaryService;

    protected ThreadLocal<List<RobotCallUserSummaryProcess.ContentSummaryResult>> summaryContentList = ThreadLocal.withInitial(ArrayList::new);

    protected ThreadLocal<RobotCallUserSummaryProcess.ContentSummaryResult> lastSummaryContent = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        if (type == AiModelType.COMMON_MODEL) {
            type = AiModelType.SUMMARY_DISTILLATION_MODEL;
        }
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Override
    protected void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        // 处理AI回复内容
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.SUM_ROBOT_CALL_SUMMARY.getCode());
        String question = buildChunkAfterQuestion(latestPromptRecordByScene, processReq);
        log.info("AI对话处理请求前-机器人小结汇总请求，对话ID{}, 小结汇总长度{}, 问题:{}", processReq.getChatContactId(), processReq.getContent().length(), question);
        String response = chatService.chat(question, latestPromptRecordByScene.getModel(), latestPromptRecordByScene.getTemperature());
        String aiReplyContent = extractAiReplyContent(response);
        log.info("AI对话机器人小结切片汇总回复: {}", aiReplyContent);
        parseResponse(aiReplyContent);
        processResponse(processReq, (RobotSessionSummary) processRecord);
        // 执行意向回调
        robotSessionService.intentionTaskCallBack(processReq.getChatContactId(), processReq.getTenantId());
    }

    @Override
    protected String buildChunkAfterQuestion(AiPromptRecordVo latestPromptRecordByScene, AiConversationProcessReq processReq) {
        return String.format(latestPromptRecordByScene.getPrompt(), summaryContentList.get().toString());
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        initProcessRecord(req);
    }

    @Override
    public RobotSessionSummary initProcessRecord(AiConversationProcessReq req) {
        Long chatContactId = req.getChatContactId();
        RobotSession robotSession = robotSessionService.getById(chatContactId);
        String uuid = robotSession.getUuid();
        RobotSessionSummary robotSessionSummary = robotSessionSummaryService.getByUuid(uuid);
        if (robotSessionSummary == null) {
            robotSessionSummary = new RobotSessionSummary();
            robotSessionSummary.setCreateTime(LocalDateTime.now());
        }
        String tenantId = robotSession.getTenantId();
        String userPhone = robotSession.getCalledNumber();
        robotSessionSummary.setMobile(userPhone);
        String userIdByPhoneNumber = getUserIdByPhoneNumber(tenantId, userPhone);
        if (userIdByPhoneNumber != null) {
            log.debug("RobotSessionSummary userIdByPhoneNumber:{}", userIdByPhoneNumber);
            robotSessionSummary.setUserId(userIdByPhoneNumber);
        }
        Long robotHistoryId = robotSession.getRobotHistoryId();
        robotSessionSummary.setUuid(robotSession.getUuid());
        robotSessionSummary.setRobotId(robotSession.getRobotId());
        robotSessionSummary.setRobotHistoryId(robotHistoryId);
        robotSessionSummary.setTaskId(robotSession.getTaskId());
        robotSessionSummary.setTenantId(tenantId);
        robotSessionSummary.setStatus(0);
        robotSessionSummary.setUpdateTime(LocalDateTime.now());
        log.debug("RobotSessionSummary saveOrUpdate:{}", robotSessionSummary);
        robotSessionSummaryService.saveOrUpdate(robotSessionSummary);
        req.setContent(robotSession.getCallText());
        req.setRobotHistoryId(robotHistoryId);
        processRecord.set(robotSessionSummary);
        return robotSessionSummary;
    }

    @Override
    protected RobotSessionSummary getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        RobotSessionSummary robotSessionSummary = processRecord.get();
        if (robotSessionSummary != null) {
            return robotSessionSummary;
        }
        return initProcessRecord(aiConversationProcessReq);
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        return String.format(lastPromptConfig.getPrompt(), content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 清理和解析 JSON 字符串
            aiReplyContent = cleanJsonString(aiReplyContent);
            String plannerSummary;
            String clientSummary;
            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            // 提取理财师和客户的总结
            plannerSummary = rootNode.get("planner_summary").asText();
            clientSummary = rootNode.get("client_summary").asText();

            // 打印或记录日志信息
            log.info("AI机器人对话小结，理财师总结: {}, 客户总结: {}", plannerSummary, clientSummary);
            summaryContentList.get().add(new RobotCallUserSummaryProcess.ContentSummaryResult(plannerSummary, clientSummary));
            lastSummaryContent.set(new RobotCallUserSummaryProcess.ContentSummaryResult(plannerSummary, clientSummary));

        } catch (JsonProcessingException e) {
            log.error("AI输出结果解析失败:", e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Override
    protected void processResponse(AiConversationProcessReq req, RobotSessionSummary summary) {
        RobotCallUserSummaryProcess.ContentSummaryResult lastContentSummary = lastSummaryContent.get();
        if (lastContentSummary != null) {
            String plannerSummary = lastContentSummary.getPlannerSummary();
            String clientSummary = lastContentSummary.getClientSummary();
            summary.setSummaryContentRobot(plannerSummary);
            summary.setSummaryContentUser(clientSummary);
            // 获取最新的对话小结
            if (clientSummary == null || "-1".equals(clientSummary)
                    || clientSummary.trim().isEmpty() || "-1".equals(clientSummary.trim())) {
                log.info("AI机器人对话小结客户未做有效回应");
                summary.setUserContentValid(0);
            } else {
                log.info("AI机器人对话小结客户总结: {}", clientSummary);
                summary.setUserContentValid(1);
            }
            summary.setStatus(1);
            summary.setUpdateTime(LocalDateTime.now());
            robotSessionSummaryService.updateById(summary);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    protected static class ContentSummaryResult {
        private String plannerSummary;
        private String clientSummary;
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.ROBOT_CALL_SUMMARY;
    }
}
