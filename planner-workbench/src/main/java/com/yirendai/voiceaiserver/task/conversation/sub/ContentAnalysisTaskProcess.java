package com.yirendai.voiceaiserver.task.conversation.sub;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.constant.AiConstant;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.*;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneResultVO;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ContentAnalysisTaskProcess extends AbstractAiConversationProcess<AiAnalysisTaskResult> {

    @Resource
    private IAiAnalysisTaskResultService aiAnalysisTaskResultService;

    @Resource
    private IAiAnalysisConfigSceneService aiAnalysisConfigSceneService;

    @Resource
    private IAiAnalysisConfigSceneResultService aiAnalysisConfigSceneResultService;

    protected ThreadLocal<List<ContentAnalysisTaskResult>> createContentAnalysisTaskResultThreadLocal = ThreadLocal.withInitial(ArrayList::new);

    protected ThreadLocal<ContentAnalysisTaskResult> lastContentAnalysisTaskResultThreadLocal = new ThreadLocal<>();

    protected ThreadLocal<String> purposeThreadLocal = new ThreadLocal<>();

    protected ThreadLocal<Map<String, String>> intentionThreadLocal = new ThreadLocal<>();

    protected ThreadLocal<Map<String, Long>> idIntentionThreadLocal = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Override
    protected void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.AI_TASK_CONTENT_ANALYSIS_SUMMARY.getCode());
        String question = buildChunkAfterQuestion(latestPromptRecordByScene, processReq);
        log.info("AI对话任务内容分析处理请求前-小结汇总请求，对话ID{}, 对话任务内容分析汇总长度{}, 问题:{}", processReq.getChatContactId(), processReq.getContent().length(), question);
        String response = chatService.chat(question, latestPromptRecordByScene.getModel(), latestPromptRecordByScene.getTemperature());
        String aiReplyContent = extractAiReplyContent(response);
        log.info("AI对话任务内容分析切片汇总回复: {}", aiReplyContent);
        parseResponse(aiReplyContent);
        processResponse(processReq, (AiAnalysisTaskResult) processRecord);
    }

    @Override
    protected String buildChunkAfterQuestion(AiPromptRecordVo latestPromptRecordByScene, AiConversationProcessReq processReq) {
        String formattedIntentString = intentionThreadLocal.get().entrySet().stream()
                .map(entry -> {
                    String k = entry.getKey();
                    Object v = entry.getValue();
                    return k + "={" + v + "}";
                })
                .collect(Collectors.joining("\n"));
        return String.format(latestPromptRecordByScene.getPrompt(), purposeThreadLocal.get(), formattedIntentString, createContentAnalysisTaskResultThreadLocal.get());
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        initProcessRecord(req);
    }

    @Override
    protected AiAnalysisTaskResult getOrCreateProcessRecord(AiConversationProcessReq req) {
        AiAnalysisTaskResult aiAnalysisTaskResult = processRecord.get();
        if (aiAnalysisTaskResult != null) {
            return aiAnalysisTaskResult;
        }
        return initProcessRecord(req);
    }

    @Override
    public AiAnalysisTaskResult initProcessRecord(AiConversationProcessReq req) {
        AiAnalysisTaskResult aiAnalysisTaskResult = aiAnalysisTaskResultService.getById(req.getChatContactId());
        if (aiAnalysisTaskResult == null) {
            log.error("对话任务内容分析数据源为空 initProcessRecord chatContactId:{}", req.getChatContactId());
            throw new AiServerException(ResultCode.DIALOG_CONTENT_ANALYSIS_SOURCE_EMPTY);
        }
        processRecord.set(aiAnalysisTaskResult);
        Long chatContactId = aiAnalysisTaskResult.getChatContactId();
        AiPlannerChatContact aiPlannerChatContact = aiPlannerChatContactMapper.selectById(chatContactId);
        if (aiPlannerChatContact == null) {
            log.error("对话任务内容分析数据源为空 initProcessRecord 2 chatContactId:{}", chatContactId);
            throw new AiServerException(ResultCode.DIALOG_CONTENT_ANALYSIS_SOURCE_EMPTY);
        }
        req.setContent(aiPlannerChatContact.getProcessedContent());
        aiAnalysisTaskResult.setStatus(1);
        aiAnalysisTaskResult.setUpdateTime(LocalDateTime.now());
        aiAnalysisTaskResultService.updateById(aiAnalysisTaskResult);
        return aiAnalysisTaskResult;
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        AiAnalysisTaskResult aiAnalysisTaskResult = processRecord.get();
        Long sceneId = aiAnalysisTaskResult.getSceneId();
        AiAnalysisConfigScene aiAnalysisConfigScene = aiAnalysisConfigSceneService.getById(sceneId);
        if (!Strings.isNullOrEmpty(purposeThreadLocal.get())) {
            return String.format(lastPromptConfig.getPrompt(), purposeThreadLocal.get(), intentionThreadLocal.get(), content);
        }
        if (aiAnalysisConfigScene == null) {
            log.error("对话任务内容分析场景不存在 buildQuestion sceneId:{}", sceneId);
            throw new AiServerException(ResultCode.DIALOG_CONTENT_ANALYSIS_SCENE_NOT_EXIST);
        }
        String prompt = aiAnalysisConfigScene.getPrompt();
        purposeThreadLocal.set(prompt);
        List<AiAnalysisConfigSceneResultVO> aiAnalysisConfigSceneResultS = aiAnalysisConfigSceneResultService.listBySceneId(sceneId);
        Map<String, String> resultMap = new HashMap<>();
        Map<String, Long> idResultMap = new HashMap<>();
        if (aiAnalysisConfigSceneResultS != null && !aiAnalysisConfigSceneResultS.isEmpty()) {
            aiAnalysisConfigSceneResultS.forEach(aiAnalysisConfigSceneResultVO -> {
                resultMap.put(aiAnalysisConfigSceneResultVO.getResultName(), aiAnalysisConfigSceneResultVO.getPrompt());
                idResultMap.put(aiAnalysisConfigSceneResultVO.getResultName(), aiAnalysisConfigSceneResultVO.getId());
            });
            resultMap.putIfAbsent("其他", "如果不匹配任意选项则匹配这一项");
            idResultMap.putIfAbsent("其他", 0L);
        }
        idIntentionThreadLocal.set(idResultMap);
        intentionThreadLocal.set(resultMap);
        String formattedIntentString = resultMap.entrySet().stream()
                .map(entry -> {
                    String k = entry.getKey();
                    Object v = entry.getValue();
                    return k + "={" + v + "}";
                })
                .collect(Collectors.joining("\n"));
        return String.format(lastPromptConfig.getPrompt(), prompt, formattedIntentString, content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        if (AiConstant.EMPTY_DATA.equals(aiReplyContent)) {
            return;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        aiReplyContent = aiReplyContent.trim();
        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            // 提取理财师和客户的意向
            String intentName = rootNode.get("intent_name").asText();
            String intentDescription = rootNode.get("intent_description").asText();
            lastContentAnalysisTaskResultThreadLocal.set(new ContentAnalysisTaskResult(intentName, intentDescription));
            createContentAnalysisTaskResultThreadLocal.get().add(lastContentAnalysisTaskResultThreadLocal.get());
        } catch (Exception e) {
            log.error("ai意向结果解析失败:", e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Override
    protected void processResponse(AiConversationProcessReq req, AiAnalysisTaskResult obj) {
        ContentAnalysisTaskResult contentAnalysisTaskResult = lastContentAnalysisTaskResultThreadLocal.get();
        if (contentAnalysisTaskResult == null) {
            log.error("对话任务内容分析结果为空 processResponse");
            throw new AiServerException(ResultCode.DIALOG_CONTENT_ANALYSIS_RESULT_EMPTY);
        }
        String intentName = contentAnalysisTaskResult.getIntentName();
        String intentDescription = contentAnalysisTaskResult.getIntentDescription();
        obj.setResultName(intentName);
        obj.setResultDes(intentDescription);
        Map<String, Long> longStringMap = idIntentionThreadLocal.get();
        if (longStringMap != null && longStringMap.containsKey(intentName)) {
            obj.setSceneResultId(longStringMap.get(intentName));
        } else {
            obj.setResultName("其他");
            obj.setSceneResultId(0L);
        }
        obj.setStatus(2);
        obj.setUpdateTime(LocalDateTime.now());
        obj.setAnalysisTime(LocalDateTime.now());
        obj.setFinalStatus(1);
        aiAnalysisTaskResultService.updateById(obj);
    }

    @Override
    public void processErrorCallback(AiConversationProcessReq processReq, boolean finished) {
        log.error("对话任务内容分析处理失败 processErrorCallback:{}", processReq);
        AiAnalysisTaskResult aiAnalysisTaskResult = processRecord.get();
        aiAnalysisTaskResult.setStatus(3);
        aiAnalysisTaskResult.setFinalStatus(finished ? 1 : 0);
        aiAnalysisTaskResult.setUpdateTime(LocalDateTime.now());
        aiAnalysisTaskResultService.updateById(aiAnalysisTaskResult);
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (createContentAnalysisTaskResultThreadLocal.get() != null) {
            createContentAnalysisTaskResultThreadLocal.remove();
        }
        if (lastContentAnalysisTaskResultThreadLocal.get() != null) {
            lastContentAnalysisTaskResultThreadLocal.remove();
        }
        if (idIntentionThreadLocal.get() != null) {
            idIntentionThreadLocal.remove();
        }
        if (intentionThreadLocal.get() != null) {
            intentionThreadLocal.remove();
        }
        if (purposeThreadLocal.get() != null) {
            purposeThreadLocal.remove();
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    protected static class ContentAnalysisTaskResult {
        private String intentName;
        private String intentDescription;
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.AI_TASK_CONTENT_ANALYSIS;
    }
}
