package com.yirendai.voiceaiserver.task.conversation.factory;

import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.task.AiConversationRetryableTask;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.entity.AiConversationProcessTask;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RefreshScope
@Component
public class AiConversationRetryableTaskFactory {

    @Resource
    private AiConversationProcessTaskService aiConversationProcessTaskService;

    @Resource
    private WebHookUtil webHookUtil;

    @Resource
    private AiConversationProcessFactory aiConversationProcessFactory;

    @Value("${ai.text.split.threshold}")
    private int textSplitThreshold;
    @Value("${ai.text.split.factor}")
    private double textSplitFactor;
    @Value("${ai.text.process.minlength}")
    private int textProcessMinLength;

    public AiConversationRetryableTask createTask(AiConversationProcessTask taskData, AiConversationProcessReq requestData) {
        AiConversationRetryableTask task = new AiConversationRetryableTask(taskData, requestData);
        task.setAiConversationProcessTaskService(aiConversationProcessTaskService);
        task.setWebHookUtil(webHookUtil);
        task.setAiConversationProcessFactory(aiConversationProcessFactory);
        task.setTextSplitThreshold(textSplitThreshold);
        task.setTextSplitFactor(textSplitFactor);
        task.setTextProcessMinLength(textProcessMinLength);
        task.setRetryCount(taskData.getRetryCount());
        task.setTaskId(taskData.getTaskId());
        task.setTaskType(AiConversationProcessTypeEnum.getByCode(taskData.getTaskType()));
        return task;
    }
}

