package com.yirendai.voiceaiserver.task.conversation.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ServiceWorkOrderPromptConfig {

    @ApiModelProperty(value = "名称")
    private String key;
    private String description;
    @ApiModelProperty(value = "0:精准匹配选项，1:自由填充")
    private int type;
    @ApiModelProperty(value = "选项 list，可以为嵌套json")
    private String options;
    @ApiModelProperty("是否支持多选")
    private Boolean isMultiple;
}
