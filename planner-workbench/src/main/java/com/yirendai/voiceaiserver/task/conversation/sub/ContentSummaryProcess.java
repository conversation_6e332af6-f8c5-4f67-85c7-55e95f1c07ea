package com.yirendai.voiceaiserver.task.conversation.sub;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.AiConversationSummary;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiConversationSummaryMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ContentSummaryProcess extends AbstractAiConversationProcess<AiConversationSummary> {

    @Resource
    private AiConversationSummaryMapper aiConversationSummaryMapper;

    protected ThreadLocal<List<ContentSummaryResult>> summaryContentList = ThreadLocal.withInitial(ArrayList::new);

    protected ThreadLocal<ContentSummaryResult> lastSummaryContent = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        if (type == AiModelType.COMMON_MODEL) {
            type = AiModelType.SUMMARY_DISTILLATION_MODEL;
        }
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Override
    protected void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        // 处理AI回复内容
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.SUM_SUMMARY_SIGNAL_AVAYA_CONTENT.getCode());
        String question = buildChunkAfterQuestion(latestPromptRecordByScene, processReq);
        log.info("AI对话处理请求前-小结汇总请求，对话ID{}, 小结汇总长度{}, 问题:{}", processReq.getChatContactId(), processReq.getContent().length(), question);
        String response = chatService.chat(question, latestPromptRecordByScene.getModel(), latestPromptRecordByScene.getTemperature());
        String aiReplyContent = extractAiReplyContent(response);
        log.info("AI对话小结切片汇总回复: {}", aiReplyContent);
        parseResponse(aiReplyContent);
        processResponse(processReq, (AiConversationSummary) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        validatePlannerChatContact(req);
    }

    @Override
    protected String buildChunkAfterQuestion(AiPromptRecordVo latestPromptRecordByScene, AiConversationProcessReq processReq) {
        return String.format(latestPromptRecordByScene.getPrompt(), summaryContentList.get().toString());
    }

    @Override
    protected AiConversationSummary getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        // 查询AI小结记录
        LambdaQueryWrapper<AiConversationSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationSummary::getChatContactId, aiConversationProcessReq.getChatContactId());
        AiConversationSummary aiConversationSummary = aiConversationSummaryMapper.selectOne(queryWrapper);
        if (aiConversationSummary == null) {
            // 新增AI小结记录
            aiConversationSummary = new AiConversationSummary();
            aiConversationSummary.setChatContactId(aiConversationProcessReq.getChatContactId());
            aiConversationSummary.setPlannerNo(aiConversationProcessReq.getPlannerNo());
            aiConversationSummary.setUserId(aiConversationProcessReq.getUserId());
            aiConversationSummary.setTenantId(aiConversationProcessReq.getTenantId());
            aiConversationSummary.setCreateTime(LocalDateTime.now());
            aiConversationSummary.setUpdateTime(LocalDateTime.now());
            aiConversationSummary.setStatus(0);
            aiConversationSummary.setIsDeleted(0);
            aiConversationSummaryMapper.insert(aiConversationSummary);
        }
        return aiConversationSummary;
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        return String.format(lastPromptConfig.getPrompt(), content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 清理和解析 JSON 字符串
            aiReplyContent = cleanJsonString(aiReplyContent);
            String plannerSummary;
            String clientSummary;
            String intentionProducts;
            if (aiReplyContent == null) {
                log.error("AI输出结果JSON解析失败, 走默认result");
                plannerSummary = "";
                clientSummary = "-1";
                intentionProducts = "";
            } else {
                JsonNode rootNode = objectMapper.readTree(aiReplyContent);
                // 提取理财师和客户的总结
                plannerSummary = rootNode.get("planner_summary").asText();
                clientSummary = rootNode.get("client_summary").asText();
                intentionProducts = rootNode.get("intention_products").asText();
            }

            // 打印或记录日志信息
            log.info("AI对话小结，理财师总结: {}, 客户总结: {}", plannerSummary, clientSummary);
            summaryContentList.get().add(new ContentSummaryResult(plannerSummary, clientSummary,intentionProducts));
            lastSummaryContent.set(new ContentSummaryResult(plannerSummary, clientSummary,intentionProducts));

        } catch (JsonProcessingException e) {
            log.error("AI输出结果解析失败:", e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Override
    protected void processResponse(AiConversationProcessReq req, AiConversationSummary summary) {
        ContentSummaryResult lastContentSummary = lastSummaryContent.get();
        if (lastContentSummary != null) {
            String plannerSummary = lastContentSummary.getPlannerSummary();
            String clientSummary = lastContentSummary.getClientSummary();
            String intentionProducts = lastContentSummary.getIntentionProducts();
            summary.setSummaryContentPlanner(plannerSummary);
            summary.setSummaryContentUser(clientSummary);
            summary.setIntentionProducts(intentionProducts);
            // 获取最新的对话小结
            if (clientSummary == null || "-1".equals(clientSummary)
                    || clientSummary.trim().isEmpty() || "-1".equals(clientSummary.trim())) {
                log.info("AI对话小结客户未做有效回应");
                summary.setUserContentValid(0);
            } else {
                log.info("AI对话小结客户总结: {}", clientSummary);
                summary.setUserContentValid(1);
            }
            summary.setStatus(1);
            summary.setUpdateTime(LocalDateTime.now());
            aiConversationSummaryMapper.updateById(summary);
        }
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.SUMMARY_SIGNAL_AVAYA_CONTENT;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    protected static class ContentSummaryResult {
        private String plannerSummary;
        private String clientSummary;
        @ApiModelProperty("意向产品类型")
        private String intentionProducts;

    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (summaryContentList.get() != null) {
            summaryContentList.remove();
        }
        if (lastSummaryContent != null) {
            lastSummaryContent.remove();
        }
    }
}
