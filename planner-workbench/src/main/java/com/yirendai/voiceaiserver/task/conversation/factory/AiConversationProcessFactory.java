package com.yirendai.voiceaiserver.task.conversation.factory;

import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class AiConversationProcessFactory {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 根据处理类型获取相应的服务实例
     *
     * @param typeEnum 处理类型枚举
     * @return 相应的处理服务
     */
    public AbstractAiConversationProcess<?> getService(AiConversationProcessTypeEnum typeEnum) {
        return applicationContext.getBean(typeEnum.getConversationProcess());
    }
}

