package com.yirendai.voiceaiserver.task.conversation.sub;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yirendai.robot.constant.RobotConstant;
import com.yirendai.robot.enums.YesOrNoEnum;
import com.yirendai.robot.modules.attribute.entity.RobotAttributeIntention;
import com.yirendai.robot.modules.attribute.service.IRobotAttributeIntentionService;
import com.yirendai.robot.modules.call.dto.RobotCallBackChannelDTO;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.service.IRobotCallTasksCustomerService;
import com.yirendai.robot.modules.robot.entity.RobotConfig;
import com.yirendai.robot.modules.robot.service.IRobotConfigService;
import com.yirendai.robot.modules.robot.vo.RobotConfigVO;
import com.yirendai.robot.modules.session.entity.RobotSession;
import com.yirendai.robot.modules.session.entity.RobotSessionSummary;
import com.yirendai.robot.modules.session.service.IRobotSessionService;
import com.yirendai.robot.modules.session.service.IRobotSessionSummaryService;
import com.yirendai.robot.util.RobotFormatUtil;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.constant.AiConstant;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.enums.CallBackTypeEnum;
import com.yirendai.workbench.model.callcenter.AfterAiCallDto;
import com.yirendai.workbench.service.callcenter.ChannelRobotCallbackService;
import com.yirendai.workbench.wrapper.CustomerAfterCallWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RobotCallUserIntentionProcess extends AbstractAiConversationProcess<RobotSession> {

    @Resource
    private IRobotSessionService robotSessionService;

    @Resource
    private IRobotAttributeIntentionService robotAttributeIntentionService;

    @Resource
    private IRobotSessionSummaryService robotSessionSummaryService;

    @Resource
    private IRobotConfigService robotConfigService;

    @Resource
    private ChannelRobotCallbackService channelRobotCallbackService;

    @Autowired
    @Lazy
    private IRobotCallTasksCustomerService robotCallTasksCustomerService;

    protected ThreadLocal<ContentIntentionResult> lastContentIntentionResult = new ThreadLocal<>();

    protected ThreadLocal<Map<Integer, String>> lastContentIntentionMap = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Override
    protected void processResForChunk(AiConversationProcessReq req, Object processRecord) {
        RobotSessionSummary sessionSummary = robotSessionSummaryService.getByUuid(req.getUuid());
        if (sessionSummary == null) {
            log.error("RobotCallUserIntentionProcess sessionSummary is null, uuid:{}", req.getUuid());
            throw new AiServerException(ResultCode.ROBOT_SUMMARY_QUERY_ERROR);
        }
        String content = RobotConstant.PLANNER_PREFIX + sessionSummary.getSummaryContentRobot() + RobotConstant.CUSTOMER_PREFIX + sessionSummary.getSummaryContentUser();
        AiPromptRecordVo lastPrompt = getLastPrompt();
        String question = buildQuestion(content, lastPrompt, LocalDateTime.now(), sessionSummary.getTenantId());
        log.info("AI对话处理请求-小结-机器人用户意图请求前，对话ID{}, 用户意图长度{}, 问题:{}", req.getChatContactId(), req.getContent().length(), question);
        String aiReplyContent = chat(question, lastPrompt.getModel(), lastPrompt.getTemperature(), AiModelType.COMMON_MODEL, lastPrompt.getSystemPrompt());
        log.info("AI对话处理请求-小结-机器人用户意图回复:{}", aiReplyContent);
        parseResponse(aiReplyContent);
        processResponse(req, (RobotSession) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        initProcessRecord(req);
    }

    @Override
    public String processChunk(String chunk, AiConversationProcessReq processReq, AiPromptRecordVo lastPromptConfig) {
        return AiConstant.EMPTY_DATA;
    }

    @Override
    protected RobotSession getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        RobotSession robotSessionInfo = processRecord.get();
        if (robotSessionInfo != null) {
            return robotSessionInfo;
        }
        return initProcessRecord(aiConversationProcessReq);
    }

    @Override
    public RobotSession initProcessRecord(AiConversationProcessReq req) {
        Long chatContactId = req.getChatContactId();
        RobotSession robotSession = robotSessionService.getById(chatContactId);
        req.setContent(robotSession.getCallText());
        req.setRobotHistoryId(robotSession.getRobotHistoryId());
        req.setUuid(robotSession.getUuid());
        processRecord.set(robotSession);
        return robotSession;
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        AiConversationProcessReq processReq = req.get();
        Long robotHistoryId = processReq.getRobotHistoryId();
        List<RobotAttributeIntention> intentions = robotAttributeIntentionService.getByRobotHistoryId(robotHistoryId);
        RobotConfigVO robotCfg = robotConfigService.getRobotCfg(robotHistoryId);
        if (intentions == null || intentions.isEmpty() || robotCfg == null) {
            log.error("RobotCallUserIntentionProcess robotHistoryId:{} 没有会话意向配置", robotHistoryId);
            throw new AiServerException(ResultCode.INTENTION_NOT_FOUND);
        }
        Map<Integer, String> intentionMap = new HashMap<>();
        for (RobotAttributeIntention intention : intentions) {
            intentionMap.put(intention.getLevel(), intention.getName());
        }
        lastContentIntentionMap.set(intentionMap);
        String intentionMapStr = JSON.toJSONString(intentionMap);
        return String.format(lastPromptConfig.getPrompt(), robotCfg.getIntentPurpose(), intentionMapStr, content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        if (AiConstant.EMPTY_DATA.equals(aiReplyContent)) {
            return;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        aiReplyContent = aiReplyContent.trim();
        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            // 提取理财师和客户的意向
            Integer intentLevel = rootNode.get("intent_level").asInt();
            String recentPlan = rootNode.get("recent_plan").asText();
            lastContentIntentionResult.set(new ContentIntentionResult(intentLevel, recentPlan));
        } catch (Exception e) {
            log.error("ai意向结果解析失败:", e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Override
    protected void processResponse(AiConversationProcessReq req, RobotSession robotSession) {
        ContentIntentionResult contentIntentionResult = lastContentIntentionResult.get();
        if (contentIntentionResult != null) {
            Integer intentLevel = contentIntentionResult.getIntentLevel();
            robotSession.setIntentionLevel(intentLevel);
            robotSession.setRecentIntention(contentIntentionResult.getRecentPlan());
            Map<Integer, String> integerStringMap = lastContentIntentionMap.get();
            String des = integerStringMap.get(intentLevel);
            if (des != null) {
                robotSession.setIntentionDes(des);
            }

            robotSessionService.updateById(robotSession);
            log.info("ai意向结果解析成功:taskId:{}, robotSessionId:{}", req.getTaskId(), robotSession.getId());
            notifyChannelResult(robotSession);

            RobotCallTasksCustomer customer = robotCallTasksCustomerService.selectOneBySessionId(robotSession.getId());
            if(customer != null && YesOrNoEnum.NO.getValue().equals(customer.getIsTest())){
                try{
                    CustomerAfterCallWrapper customerAfterCallWrapper = tenantServiceHolder.getCustomerAfterCallWrapper(robotSession.getTenantId());
                    AfterAiCallDto afterAiCallDto = new AfterAiCallDto();
                    afterAiCallDto.setAiCallTime(robotSession.getCallStartTime());
                    afterAiCallDto.setRobotId(robotSession.getRobotId());
                    afterAiCallDto.setCallResult(robotSession.getCallResult());
                    afterAiCallDto.setIntentionDes(robotSession.getIntentionDes());
                    afterAiCallDto.setIntentType(robotSession.getRecentIntention());
                    afterAiCallDto.setCustomerId(customer.getUserId());
                    afterAiCallDto.setCallStatus(robotSession.getCallStatus());
                    afterAiCallDto.setTenantId(robotSession.getTenantId());
                    customerAfterCallWrapper.afterAiCall(afterAiCallDto);
                    log.info("notifyChannelResult afterAiCall 执行结束 sessionId={} uuid={}  成功", robotSession.getId(),   robotSession.getUuid());
                }catch (Exception ex){
                    log.error("notifyChannelResult afterAiCall 执行异常 sessionId={} uuid={}  error={}", robotSession.getId(),   robotSession.getUuid(), ex.getMessage(), ex);
                }
            }
            return;
        }
        log.error("ai意向结果解析失败:{}", req.getTaskId());
        throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
    }

    private void notifyChannelResult (RobotSession robotSession) {
        try {
            // 通知渠道
            RobotCallBackChannelDTO callBackChannelDTO = buildCallBackDto(robotSession);
            if (callBackChannelDTO != null) {
                //channelRobotCallbackService.initChannelRecordCallback(robotSession.getUuid(), robotSession.getTenantId());
                String dealIds = callBackChannelDTO.getDealId();
                if (StringUtils.isBlank(dealIds)) {
                    channelRobotCallbackService.recordCallbackAsync(callBackChannelDTO, robotSession.getTenantId());
                } else {
                    if (dealIds.indexOf(",") > -1) {
                        String dealIdList[] = dealIds.split(",");
                        String uuid = callBackChannelDTO.getUuid();
                        for (String dealId : dealIdList) {
                            callBackChannelDTO.setDealId(dealId);
                            callBackChannelDTO.setUuid(uuid + "-" + dealId);
                            channelRobotCallbackService.recordCallbackAsync(callBackChannelDTO, robotSession.getTenantId());
                        }
                    } else {
                        channelRobotCallbackService.recordCallbackAsync(callBackChannelDTO, robotSession.getTenantId());
                    }

                }
            }
        } catch (Exception e) {
            log.error("notifyChannelResult异常:{}", e);
        }
    }

    private RobotCallBackChannelDTO buildCallBackDto(RobotSession robotSession) {
        if (robotSession.getIntentionLevel() == null) {
            return null;
        }
        RobotCallTasksCustomer customer = robotCallTasksCustomerService.selectOneBySessionId(robotSession.getId());
        if (customer != null && YesOrNoEnum.NO.getValue().equals(customer.getIsTest())) {
            Map<String, Object> variablesMap = RobotFormatUtil.parseStringToMap(customer.getVariables());
            String dealId = null;
            if (!CollectionUtils.isEmpty(variablesMap)) {
                Object dealIdObj = variablesMap.get("dealId");
                if (dealIdObj != null) {
                    dealId = (String) dealIdObj;
                }
            }

            RobotCallBackChannelDTO dto = new RobotCallBackChannelDTO();
            dto.setUuid(robotSession.getUuid());
            dto.setUserId(customer.getUserId());
            dto.setCallBackType(CallBackTypeEnum.ROBOT.getCode());
            dto.setRecordUrl(robotSession.getCallVideo());
            dto.setDuration(robotSession.getCallDuration());
            dto.setRing(robotSession.getRing());
            dto.setHangupBy(robotSession.getHangupBy());
            dto.setHangupCause(robotSession.getHangupCause());
            dto.setStartStamp(robotSession.getCallStartTime());
            dto.setEndStamp(robotSession.getCallEndTime());
            dto.setDealId(dealId);
            dto.setReturnVisitSuccess(0);
            dto.setBizUniqueId(customer.getBizUniqueId());
            Integer successLevel = 0;
            // 有效回访
            if (successLevel.equals(robotSession.getIntentionLevel())) {
                dto.setReturnVisitSuccess(1);
            }
            dto.setReturnVisitResult(robotSession.getRecentIntention());
            return dto;
        }
        return null;
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.ROBOT_CALL_INTENTION;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    protected static class ContentIntentionResult {
        private Integer intentLevel;
        private String recentPlan;
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (lastContentIntentionResult.get() != null) {
            lastContentIntentionResult.remove();
        }
        if (lastContentIntentionMap.get() != null) {
            lastContentIntentionMap.remove();
        }
    }
}
