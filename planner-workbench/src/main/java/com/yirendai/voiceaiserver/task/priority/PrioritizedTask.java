package com.yirendai.voiceaiserver.task.priority;

import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
public class PrioritizedTask implements Runnable, Comparable<PrioritizedTask> {
    private static final AtomicLong SEQUENCE = new AtomicLong(0);

    private final Runnable task;
    private final PriorityThreadEnum priority;
    private final long sequenceNumber;

    public PrioritizedTask(Runnable task, PriorityThreadEnum priority) {
        this.task = task;
        this.priority = priority;
        this.sequenceNumber = SEQUENCE.getAndIncrement();
    }

    @Override
    public void run() {
        task.run();
    }

    @Override
    public int compareTo(PrioritizedTask other) {
        if (this.priority.getPriorityValue() != other.priority.getPriorityValue()) {
            return Integer.compare(this.priority.getPriorityValue(), other.priority.getPriorityValue());
        }
        // 同一优先级按提交顺序执行
        return Long.compare(this.sequenceNumber, other.sequenceNumber);
    }
}

