package com.yirendai.voiceaiserver.task.conversation.sub;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationTagTypeEnum;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import com.yirendai.workbench.entity.*;
import groovy.transform.EqualsAndHashCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.PersistenceException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLIntegrityConstraintViolationException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TagCategoryProcess extends AbstractAiConversationProcess<AiConversationTagsProcess> {

    protected ThreadLocal<Set<TagInfo>> matchTagsList = ThreadLocal.withInitial(HashSet::new);
    protected ThreadLocal<Set<TagInfo>> recommendTagsList = ThreadLocal.withInitial(HashSet::new);

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        if (type == AiModelType.COMMON_MODEL) {
            type = AiModelType.TAG_DISTILLATION_MODEL;
        }
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Transactional
    @Override
    public void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        // 处理AI回复内容
        processResponse(processReq, (AiConversationTagsProcess) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        validatePlannerChatContact(req);
    }

    @Override
    protected AiConversationTagsProcess getOrCreateProcessRecord(AiConversationProcessReq aiConversationProcessReq) {
        // 查询AI对话标签处理过程
        LambdaQueryWrapper<AiConversationTagsProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationTagsProcess::getChatContactId, aiConversationProcessReq.getChatContactId()).orderByDesc(AiConversationTagsProcess::getId).last("limit 1");
        AiConversationTagsProcess aiConversationTagsProcess = aiConversationTagsProcessMapper.selectOne(queryWrapper);
        if (aiConversationTagsProcess != null && Objects.equals(aiConversationTagsProcess.getProcessStatus(), AiConversationProcessStatusEnum.FINISHED.getCode())) {
            this.finished.set(true);
        } else if (aiConversationTagsProcess == null) {
            aiConversationTagsProcess = new AiConversationTagsProcess();
            aiConversationTagsProcess.setChatContactId(aiConversationProcessReq.getChatContactId());
            aiConversationTagsProcess.setProcessStatus(AiConversationProcessStatusEnum.PROCESSING.getCode());
            aiConversationTagsProcess.setTenantId(aiConversationProcessReq.getTenantId());
            aiConversationTagsProcess.setCreateTime(LocalDateTime.now());
            aiConversationTagsProcess.setUpdateTime(LocalDateTime.now());
            aiConversationTagsProcess.setIsDeleted(0);
            aiConversationTagsProcessMapper.insert(aiConversationTagsProcess);
        }
        return aiConversationTagsProcess;
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        List<AiTagCategoryWithTags> allCategoriesWithTags = iAiUserTagDictionaryService.findAllCategoriesWithTags(tenantId);
        if (allCategoriesWithTags.isEmpty()) {
            throw new AiServerException(ResultCode.NO_AI_TAG_CATEGORY);
        }
        return String.format(lastPromptConfig.getPrompt(), content, allCategoriesWithTags);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        AiConversationTagsProcess aiConversationTagsProcess = processRecord.get();
        ObjectMapper objectMapper = new ObjectMapper();
        aiReplyContent = aiReplyContent.trim();
        try {
            // 处理 matchedTags 数组
            aiReplyContent = cleanJsonString(aiReplyContent);
            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            JsonNode matchedTagsNode = rootNode.get("matchedTags");
            matchTagsList.get().addAll(parseTagInfo(matchedTagsNode));
            // 处理 recommendedNewTags 数组
            JsonNode recommendedNewTagsNode = rootNode.get("recommendedNewTags");
            recommendTagsList.get().addAll(parseTagInfo(recommendedNewTagsNode));
        } catch (JsonProcessingException e) {
            log.error("ai标签结果解析失败:", e);
            aiConversationTagsProcess.setProcessStatus(AiConversationProcessStatusEnum.FAILED.getCode());
            aiConversationTagsProcessMapper.updateById(aiConversationTagsProcess);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    /**
     * 处理Json解析结果
     */
    private List<TagInfo> parseTagInfo(JsonNode tagList) {
        List<TagInfo> tagInfoList = new ArrayList<>();
        if (tagList != null && tagList.isArray()) {
            for (JsonNode tag : tagList) {
                String category = tag.get("category").asText();
                String name = tag.get("name").asText();
                TagInfo tagInfo = new TagInfo(category, name);
                tagInfoList.add(tagInfo);
            }
        }
        return tagInfoList;
    }

    @Transactional
    @Override
    public void processResponse(AiConversationProcessReq aiConversationProcessReq, AiConversationTagsProcess aiConversationTagsProcess) {
        Set<TagInfo> matchTagsListSet = matchTagsList.get();
        String tenantId = aiConversationProcessReq.getTenantId();
        String userPhone = aiConversationProcessReq.getUserPhone();
        userPhone = normalizePhoneNumber(userPhone);
        // 先删除之前的对话标签
        aiConversationTagService.deleteByChatContactId(aiConversationProcessReq.getChatContactId());
        for (TagInfo tagInfo : matchTagsListSet) {
            String category = tagInfo.getCategory();
            String name = tagInfo.getName();
            // 查询标签分类
            AiUserTagCategory aiUserTagCategory = iAiUserTagCategoryService.selectByCategoryName(category, tenantId);
            if (aiUserTagCategory == null) {
                log.error("未找到标签分类: {}", category);
                continue;
            }
            // 查询标签
            AiUserTagDictionary aiUserTagDictionary = iAiUserTagDictionaryService.selectByTagNameAndCategoryId(name, aiUserTagCategory.getId(), tenantId);
            if (aiUserTagDictionary == null) {
                log.error("未找到标签: {}", name);
                continue;
            }
            Long tagId = aiUserTagDictionary.getId();
            Long tagCategoryId = aiUserTagCategory.getId();
            Long topParentId = aiUserTagCategory.getTopParentId();
            String topParentName = "";
            String colorValue = aiUserTagCategory.getColorValue();
            if (topParentId != null && topParentId != 0) {
                AiUserTagCategory topParent = iAiUserTagCategoryService.selectById(topParentId);
                if (topParent != null) {
                    topParentName = topParent.getCategoryName();
                    if (Strings.isNullOrEmpty(colorValue)) {
                        colorValue = topParent.getColorValue();
                    }
                }
            } else if (topParentId != null) {
                // 顶级分类
                topParentId = aiUserTagCategory.getId();
                topParentName = aiUserTagCategory.getCategoryName();
            }
            // 插入对话标签
            aiConversationTagService.insertAiConversationTag(aiConversationProcessReq, aiConversationTagsProcess.getId(), tagCategoryId, tagId, category, name, AiConversationTagTypeEnum.MATCH);
            // 判断是否在用户和标签关联
            LambdaQueryWrapper<AiCustomerTag> customerTagQueryWrapper = new LambdaQueryWrapper<>();
            if (Strings.isNullOrEmpty(userPhone)) {
                customerTagQueryWrapper.eq(AiCustomerTag::getUserId, aiConversationProcessReq.getUserId())
                        .eq(AiCustomerTag::getTagName, name)
                        .eq(AiCustomerTag::getTagCategoryName, category)
                        .isNull(AiCustomerTag::getUserPhone)
                        .eq(AiCustomerTag::getTenantId, tenantId);
            } else {
                customerTagQueryWrapper.eq(AiCustomerTag::getUserId, aiConversationProcessReq.getUserId())
                        .eq(AiCustomerTag::getTagName, name)
                        .eq(AiCustomerTag::getTagCategoryName, category)
                        .eq(AiCustomerTag::getUserPhone, userPhone)
                        .eq(AiCustomerTag::getTenantId, tenantId);
            }
            AiCustomerTag aiCustomerTag = null;
            try {
                aiCustomerTag = aiCustomerTagMapper.selectOne(customerTagQueryWrapper);
            } catch (Exception e) {
                log.warn("用户标签关联已存在，无法重复插入。错误原因：{}", e.getMessage());
            }
            if (aiCustomerTag == null) {
                aiCustomerTag = new AiCustomerTag();
                aiCustomerTag.setUserId(aiConversationProcessReq.getUserId());
                aiCustomerTag.setUserPhone(userPhone);
                aiCustomerTag.setTagId(tagId);
                aiCustomerTag.setTagCategoryId(aiUserTagCategory.getId());
                aiCustomerTag.setTagName(name);
                aiCustomerTag.setTagCategoryName(category);
                aiCustomerTag.setTopTagCategoryId(topParentId);
                aiCustomerTag.setWeight(1);
                aiCustomerTag.setColorValue(colorValue);
                aiCustomerTag.setTenantId(tenantId);
                aiCustomerTag.setSourceType(0);
                aiCustomerTag.setMatchStatus(AiConversationTagTypeEnum.MATCH.getCode());
                aiCustomerTag.setTopTagCategoryName(topParentName);
                aiCustomerTag.setCreateTime(LocalDateTime.now());
                aiCustomerTag.setUpdateTime(LocalDateTime.now());
                try {
                    aiCustomerTagMapper.insert(aiCustomerTag);
                } catch (Exception e) {
                    log.warn("用户标签关联已存在，无法重复插入。错误原因：{}", e.getMessage());
                }
            } else {
                aiCustomerTag.setUpdateTime(LocalDateTime.now());
                Integer weight = aiCustomerTag.getWeight();
                if (weight == null) {
                    weight = 0;
                }
                if (aiCustomerTag.getSourceType() != null && aiCustomerTag.getSourceType() == 1) {
                    aiCustomerTag.setSourceType(2);
                } else if (aiCustomerTag.getSourceType() == null) {
                    aiCustomerTag.setSourceType(0);
                }
                if (Strings.isNullOrEmpty(aiCustomerTag.getColorValue())) {
                    aiCustomerTag.setColorValue(colorValue);
                }
                aiCustomerTag.setWeight(weight + 1);
                aiCustomerTagMapper.updateById(aiCustomerTag);
            }
        }
        Set<TagInfo> recommendTagsListSet = this.recommendTagsList.get();
        for (TagInfo tagInfo : recommendTagsListSet) {
            String category = tagInfo.getCategory();
            String name = tagInfo.getName();
            if (Strings.isNullOrEmpty(name)) {
                continue;
            }
            AiUserTagDictionary aiUserTagDictionary = iAiUserTagDictionaryService.selectByTagName(name, tenantId);
            if (aiUserTagDictionary != null) {
                continue;
            }
            // 插入对话标签
            aiConversationTagService.insertAiConversationTag(aiConversationProcessReq, aiConversationTagsProcess.getId(), null, null, category, name, AiConversationTagTypeEnum.RECOMMEND);
            // 判断是否在用户和标签关联
            LambdaQueryWrapper<AiCustomerTag> customerTagQueryWrapper = new LambdaQueryWrapper<>();
            if (Strings.isNullOrEmpty(userPhone)) {
                customerTagQueryWrapper.eq(AiCustomerTag::getUserId, aiConversationProcessReq.getUserId())
                        .eq(AiCustomerTag::getTagName, name)
                        .eq(AiCustomerTag::getTagCategoryName, category)
                        .isNull(AiCustomerTag::getUserPhone)
                        .eq(AiCustomerTag::getTenantId, tenantId);
            } else {
                customerTagQueryWrapper.eq(AiCustomerTag::getUserId, aiConversationProcessReq.getUserId())
                        .eq(AiCustomerTag::getTagName, name)
                        .eq(AiCustomerTag::getTagCategoryName, category)
                        .eq(AiCustomerTag::getUserPhone, userPhone)
                        .eq(AiCustomerTag::getTenantId, tenantId);
            }
            AiCustomerTag aiCustomerTag = null;
            try {
                aiCustomerTag = aiCustomerTagMapper.selectOne(customerTagQueryWrapper);
            } catch (Exception e) {
                log.warn("用户标签关联已存在，无法重复插入。错误原因：{}", e.getMessage());
            }
            if (aiCustomerTag == null) {
                aiCustomerTag = new AiCustomerTag();
                aiCustomerTag.setUserId(aiConversationProcessReq.getUserId());
                aiCustomerTag.setUserPhone(userPhone);
                aiCustomerTag.setTagName(name);
                aiCustomerTag.setTagCategoryName(category);
                aiCustomerTag.setWeight(1);
                aiCustomerTag.setTenantId(tenantId);
                aiCustomerTag.setSourceType(0);
                aiCustomerTag.setMatchStatus(AiConversationTagTypeEnum.RECOMMEND.getCode());
                aiCustomerTag.setCreateTime(LocalDateTime.now());
                aiCustomerTag.setUpdateTime(LocalDateTime.now());
                try {
                    aiCustomerTagMapper.insert(aiCustomerTag);
                } catch (Exception e) {
                    log.warn("用户标签关联已存在，无法重复插入。错误原因：{}", e.getMessage());
                }
            } else {
                aiCustomerTag.setUpdateTime(LocalDateTime.now());
                Integer weight = aiCustomerTag.getWeight();
                if (weight == null) {
                    weight = 0;
                }
                if (aiCustomerTag.getSourceType() != null && aiCustomerTag.getSourceType() == 1) {
                    aiCustomerTag.setSourceType(2);
                } else if (aiCustomerTag.getSourceType() == null) {
                    aiCustomerTag.setSourceType(0);
                }
                aiCustomerTag.setWeight(weight + 1);
                aiCustomerTagMapper.updateById(aiCustomerTag);
            }
        }
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.USER_TAG;
    }

    private String normalizePhoneNumber(String userPhone) {
        if (userPhone == null) {
            return null;
        }

        if (userPhone.startsWith("+86")) {
            return userPhone.substring(3);
        } else if (userPhone.startsWith("86")) {
            return userPhone.substring(2);
        } else if (userPhone.startsWith("0")) {
            return userPhone.substring(1);
        }

        return userPhone;
    }

    @Data
    @EqualsAndHashCode
    protected static class TagInfo {
        private String category;
        private String name;

        public TagInfo(String category, String name) {
            this.category = category;
            this.name = name;
        }

        @Override
        public String toString() {
            return "TagInfo{category='" + category + "', name='" + name + "'}";
        }
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (matchTagsList.get() != null) {
            matchTagsList.remove();
        }
        if (recommendTagsList.get() != null) {
            recommendTagsList.remove();
        }
    }
}
