package com.yirendai.voiceaiserver.task;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.service.*;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.task.conversation.factory.AiConversationProcessFactory;
import com.yirendai.voiceaiserver.util.TextSplitter;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.AiConversationProcessTask;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class AiConversationRetryableTask implements AiRetryableTask {

    @Setter
    private AiConversationProcessTaskService aiConversationProcessTaskService;
    @Setter
    private WebHookUtil webHookUtil;
    @Setter
    private AiConversationProcessFactory aiConversationProcessFactory;

    private final AiConversationProcessTask taskData;
    private final AiConversationProcessReq requestData;

    @Setter
    private int textSplitThreshold;
    @Setter
    private double textSplitFactor;
    @Setter
    private int textProcessMinLength;
    @Setter
    private int retryCount;
    @Setter
    private String taskId;
    @Setter
    private AiConversationProcessTypeEnum taskType;

    public AiConversationRetryableTask(@NotNull AiConversationProcessTask aiConversationProcessTask, @NotNull AiConversationProcessReq aiConversationProcessReq) {
        this.taskData = aiConversationProcessTask;
        this.requestData = aiConversationProcessReq;
    }

    @Override
    public void run() {
        AbstractAiConversationProcess<?> aiConversationProcess = null;
        try {
            taskData.setUpdateTime(LocalDateTime.now());
            log.info("Ai-Conversation-Process-Task start: taskId: {}, taskType: {}, retryCount: {}", taskId, taskType, retryCount);
            aiConversationProcessTaskService.updateTask(this, AiConversationProcessStatusEnum.PROCESSING);
            // 准备处理类
            aiConversationProcess = aiConversationProcessFactory.getService(taskType);
            if (aiConversationProcess == null) {
                log.error("Ai-Conversation-Process-Task process is null, taskId:{}", requestData.getTaskId());
                throw new AiServerException(ResultCode.CHAT_PROCESS_CONFIG_ERROR);
            }
            // 从taskId中提取数据源类型
            String sourceType = aiConversationProcessTaskService.extractSourceTypeFromTaskId(taskId);
            if (sourceType != null) {
                requestData.setSourceType(sourceType);
            } else if (this.getTaskData() != null && this.getTaskData().getTaskData() != null) {
                // 兼容旧格式，从taskData中获取
                requestData.setSourceType(this.getTaskData().getTaskData());
            }
            AiPromptRecordVo lastPromptConfig = aiConversationProcess.processBefore(requestData);
            String content = requestData.getContent();
            if (content == null) {
                log.error("Ai-Conversation-Process-Task content is empty, taskId:{}", requestData.getTaskId());
                throw new AiServerException(ResultCode.CHAT_CONTENT_EMPTY);
            }
            String originContent = content;
            try {
                content = TextSplitter.mergeSpeakerTexts(content);
            } catch (Exception e) {
                content = originContent;
                log.error("Ai-Conversation-Process-Task merge content failed", e);
            }
            // 设置合并后的文本
            requestData.setContent(content);
            log.info("Ai-Conversation-Process-Task merged content-length: {}", content.length());
            // 此处音转文的约定，此标识意味着音转文未获取任何内容
            if ("[新呼叫中心语音]".equals(content.trim()) || "[avaya语音]".equals(content.trim()) || "[厚予语音]".equals(content.trim())) {
                taskData.setLastFailReason("无效通话");
                taskData.setUpdateTime(LocalDateTime.now());
                aiConversationProcessTaskService.updateTask(this, AiConversationProcessStatusEnum.FINISHED);
                aiConversationProcess.processErrorCallback(requestData, true);
                return;
            }
            int length = content.length();
            log.info("Ai-Conversation-Process-Task length: {}", length);
            // 分片阈值
            if (length > textSplitThreshold * textSplitFactor) {
                log.info("Ai-Conversation-Process-Task split: taskId: {}, taskType: {}, retryCount: {}", taskId, taskType, retryCount);
                // 分片处理
                Object processRecord = aiConversationProcess.getProcessRecord();
                // 串行处理每个文本块
                List<String> mergedResult = new ArrayList<>();
                for (String chunk : TextSplitter.splitText(content, textSplitThreshold, textSplitFactor)) {
                    // 同步调用 processChunk 方法
                    String result = aiConversationProcess.processChunk(chunk, requestData, lastPromptConfig);
                    mergedResult.add(result);
                }
                // 合并处理结果
                aiConversationProcess.processChunkAfter(mergedResult, requestData, processRecord);
            } else if (length <= textProcessMinLength) {
                taskData.setLastFailReason("文本长度小于最小处理长度");
                taskData.setUpdateTime(LocalDateTime.now());
                aiConversationProcessTaskService.updateTask(this, AiConversationProcessStatusEnum.CANCELLED);
                aiConversationProcess.processErrorCallback(requestData, true);
            } else {
                log.info("Ai-Conversation-Process-Task process: taskId: {}, taskType: {}, retryCount: {}", taskId, taskType, retryCount);
                // 执行正常任务
                aiConversationProcess.process(requestData, lastPromptConfig);
            }
            // 任务执行成功
            aiConversationProcessTaskService.updateTask(this, AiConversationProcessStatusEnum.FINISHED);
        } catch (Throwable e) {
            try {
                if (aiConversationProcess != null) {
                    aiConversationProcess.processErrorCallback(requestData, retryCount >= taskData.getMaxRetry());
                }
            } catch (Exception ex) {
                log.warn("Ai-Conversation-Process-Task processErrorCallback failed, {}", ex.getMessage());
            }
            handleException(e);
        } finally {
            // 清理线程变量
            if (aiConversationProcess != null) {
                aiConversationProcess.cleanThreadLocal();
            }
        }
    }

    private void handleException(Throwable e) {
        String errorMsg = String.format("Ai-Conversation-Process-Task failed: %s, type: %s, retryCount: %s, reason: %s", taskId, taskType, retryCount, e.getMessage());
        log.error(errorMsg, e);
        
        // 检查是否为配置缺失异常
        if (e instanceof AiServerException) {
            AiServerException aiServerException = (AiServerException) e;
            if (ResultCode.NO_AI_TAG_CATEGORY.getCode() == aiServerException.getCode()) {
                log.info("任务配置缺失，设置为NO_CONFIG状态: taskId={}, reason={}", taskId, e.getMessage());
                taskData.setLastFailReason(e.getMessage());
                taskData.setLastRetryTime(LocalDateTime.now());
                return;
            }
        }
        
        // 企微通知
        String weChatHookTittle = "AI对话内容处理任务";
        webHookUtil.weChatMsgDev(Objects.requireNonNull(MsgTypeEnum.getByCode(taskType.getCode())), weChatHookTittle, errorMsg, e);
        
        // 设置失败原因和重试时间
        taskData.setLastFailReason(e.getMessage());
        taskData.setLastRetryTime(LocalDateTime.now());
        
        if (retryCount >= taskData.getMaxRetry()) {
            // 重试次数已达上限，任务取消
            aiConversationProcessTaskService.updateTask(this, AiConversationProcessStatusEnum.CANCELLED);
        } else {
            // 重试次数加1
            taskData.setRetryCount(retryCount + 1);
            aiConversationProcessTaskService.updateTask(this, AiConversationProcessStatusEnum.FAILED);
        }
    }

    @Override
    public String getTaskId() {
        return taskId;
    }

    @Override
    public com.yirendai.workbench.entity.AiConversationProcessTask getTaskData() {
        return taskData;
    }

}

