package com.yirendai.voiceaiserver.task.conversation.sub;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.AiComplianceBackResultService;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.AiComplianceBackResult;
import com.yirendai.workbench.entity.AiPlannerTopUser;
import com.yirendai.workbench.mapper.AiPlannerTopUserMapper;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CallUserComplianceResultProcess extends AbstractAiConversationProcess<AiComplianceBackResult> {

    @Resource
    private AiComplianceBackResultService aiComplianceBackResultService;

    @Resource
    private AiPlannerTopUserMapper aiPlannerTopUserMapper;

    protected ThreadLocal<Set<ComplianceResult>> complianceBackThreadLocal = ThreadLocal.withInitial(HashSet::new);

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Override
    protected void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        // 处理AI回复内容
        processResponse(processReq, (AiComplianceBackResult) processRecord);
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        validatePlannerChatContact(req);
    }

    @Override
    protected AiComplianceBackResult getOrCreateProcessRecord(AiConversationProcessReq req) {
        LambdaQueryWrapper<AiComplianceBackResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiComplianceBackResult::getUuid, req.getUuid());
        AiComplianceBackResult aiComplianceBackResult = aiComplianceBackResultService.getOne(queryWrapper);
        if (aiComplianceBackResult == null) {
            aiComplianceBackResult = new AiComplianceBackResult();
            aiComplianceBackResult.setCreateTime(LocalDateTime.now());
        }
        return aiComplianceBackResult;
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        return String.format(lastPromptConfig.getPrompt(), content);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            if (aiReplyContent == null) {
                log.warn("Empty or invalid JSON content after cleaning");
                return;
            }

            JsonNode rootNode = objectMapper.readTree(aiReplyContent);
            JsonNode indicatorsNode = rootNode.get("indicators");
            JsonNode summaryNode = rootNode.get("summary");

            if (indicatorsNode == null || summaryNode == null) {
                log.error("Missing 'indicators' or 'summary' in JSON response");
                throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
            }

            ComplianceResult result = new ComplianceResult();
            result.setSummary(summaryNode.asText());

            // Parse each indicator
            result.setPersonalConfirmation(parseIndicator(indicatorsNode, "personal_confirmation"));
            result.setProductAmountConfirmation(parseIndicator(indicatorsNode, "product_amount_confirmation"));
            result.setDocumentSigningConfirmation(parseIndicator(indicatorsNode, "document_signing_confirmation"));
            result.setProductServiceRiskAwareness(parseIndicator(indicatorsNode, "product_service_risk_awareness"));
            result.setAutonomousDecisionRiskBearing(parseIndicator(indicatorsNode, "autonomous_decision_risk_bearing"));
            result.setRiskCompatibilityAcceptance(parseIndicator(indicatorsNode, "risk_compatibility_acceptance"));
            result.setNoAdditionalPromotion(parseIndicator(indicatorsNode, "no_additional_promotion"));

            // Determine overall validity
            result.setValid(result.getPersonalConfirmation().isCompleted() &&
                    result.getProductAmountConfirmation().isCompleted() &&
                    result.getDocumentSigningConfirmation().isCompleted() &&
                    result.getProductServiceRiskAwareness().isCompleted() &&
                    result.getAutonomousDecisionRiskBearing().isCompleted() &&
                    result.getRiskCompatibilityAcceptance().isCompleted() &&
                    result.getNoAdditionalPromotion().isCompleted());

            complianceBackThreadLocal.get().add(result);
        } catch (Exception e) {
            log.error("Failed to parse AI compliance result: {}", aiReplyContent, e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Override
    protected void processResponse(AiConversationProcessReq req, AiComplianceBackResult obj) {
        Set<ComplianceResult> complianceBackResults = complianceBackThreadLocal.get();
        if (complianceBackResults != null && !complianceBackResults.isEmpty()) {
            boolean isValid = false;
            StringBuilder des = new StringBuilder();
            boolean isValidPersonalConfirmation = false;
            StringBuilder desPersonalConfirmation = new StringBuilder();
            boolean isValidProductAmountConfirmation = false;
            StringBuilder desProductAmountConfirmation = new StringBuilder();
            boolean isValidDocumentSigningConfirmation = false;
            StringBuilder desDocumentSigningConfirmation = new StringBuilder();
            boolean isValidProductServiceRiskAwareness = false;
            StringBuilder desProductServiceRiskAwareness = new StringBuilder();
            boolean isValidAutonomousDecisionRiskBearing = false;
            StringBuilder desAutonomousDecisionRiskBearing = new StringBuilder();
            boolean isValidRiskCompatibilityAcceptance = false;
            StringBuilder desRiskCompatibilityAcceptance = new StringBuilder();
            boolean isValidNoAdditionalPromotion = false;
            StringBuilder desNoAdditionalPromotion = new StringBuilder();
            for (ComplianceResult complianceBackResult : complianceBackResults) {
                isValid = complianceBackResult.getPersonalConfirmation().isCompleted() && complianceBackResult.getProductAmountConfirmation().isCompleted() && complianceBackResult.getDocumentSigningConfirmation().isCompleted() && complianceBackResult.getProductServiceRiskAwareness().isCompleted()
                        && complianceBackResult.getAutonomousDecisionRiskBearing().isCompleted() && complianceBackResult.getRiskCompatibilityAcceptance().isCompleted() && complianceBackResult.getNoAdditionalPromotion().isCompleted();
                des.append(complianceBackResult.getSummary());
                isValidPersonalConfirmation = complianceBackResult.getPersonalConfirmation().isCompleted();
                isValidProductAmountConfirmation = complianceBackResult.getProductAmountConfirmation().isCompleted();
                isValidDocumentSigningConfirmation = complianceBackResult.getDocumentSigningConfirmation().isCompleted();
                isValidProductServiceRiskAwareness = complianceBackResult.getProductServiceRiskAwareness().isCompleted();
                isValidAutonomousDecisionRiskBearing = complianceBackResult.getAutonomousDecisionRiskBearing().isCompleted();
                isValidRiskCompatibilityAcceptance = complianceBackResult.getRiskCompatibilityAcceptance().isCompleted();
                isValidNoAdditionalPromotion = complianceBackResult.getNoAdditionalPromotion().isCompleted();
                desPersonalConfirmation.append(complianceBackResult.getPersonalConfirmation().getReason());
                desProductAmountConfirmation.append(complianceBackResult.getProductAmountConfirmation().getReason());
                desDocumentSigningConfirmation.append(complianceBackResult.getDocumentSigningConfirmation().getReason());
                desProductServiceRiskAwareness.append(complianceBackResult.getProductServiceRiskAwareness().getReason());
                desAutonomousDecisionRiskBearing.append(complianceBackResult.getAutonomousDecisionRiskBearing().getReason());
                desRiskCompatibilityAcceptance.append(complianceBackResult.getRiskCompatibilityAcceptance().getReason());
                desNoAdditionalPromotion.append(complianceBackResult.getNoAdditionalPromotion().getReason());
            }
            obj.setPersonalConfirmationCompleted(isValidPersonalConfirmation ? 1 : 0);
            obj.setPersonalConfirmationReason(desPersonalConfirmation.toString());
            obj.setProductAmountConfirmationCompleted(isValidProductAmountConfirmation ? 1 : 0);
            obj.setProductAmountConfirmationReason(desProductAmountConfirmation.toString());
            obj.setDocumentSigningConfirmationCompleted(isValidDocumentSigningConfirmation ? 1 : 0);
            obj.setDocumentSigningConfirmationReason(desDocumentSigningConfirmation.toString());
            obj.setProductServiceRiskAwarenessCompleted(isValidProductServiceRiskAwareness ? 1 : 0);
            obj.setProductServiceRiskAwarenessReason(desProductServiceRiskAwareness.toString());
            obj.setAutonomousDecisionRiskBearingCompleted(isValidAutonomousDecisionRiskBearing ? 1 : 0);
            obj.setAutonomousDecisionRiskBearingReason(desAutonomousDecisionRiskBearing.toString());
            obj.setRiskCompatibilityAcceptanceCompleted(isValidRiskCompatibilityAcceptance ? 1 : 0);
            obj.setRiskCompatibilityAcceptanceReason(desRiskCompatibilityAcceptance.toString());
            obj.setNoAdditionalPromotionCompleted(isValidNoAdditionalPromotion ? 1 : 0);
            obj.setNoAdditionalPromotionReason(desNoAdditionalPromotion.toString());
            obj.setValid(isValid ? 1 : 0);
            obj.setDescription(des.toString());
            obj.setUserId(req.getUserId());
            obj.setPlannerId(req.getPlannerNo());
            LambdaQueryWrapper<AiPlannerTopUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiPlannerTopUser::getUserId, req.getUserId());
            queryWrapper.eq(AiPlannerTopUser::getPlannerNo, req.getPlannerNo());
            queryWrapper.eq(AiPlannerTopUser::getTenantId, req.getTenantId());
            AiPlannerTopUser aiPlannerTopUsers = aiPlannerTopUserMapper.selectOne(queryWrapper);
            if (aiPlannerTopUsers != null) {
                obj.setUserName(aiPlannerTopUsers.getUserName());
                obj.setPlannerName(aiPlannerTopUsers.getPlannerName());
            }
            obj.setUuid(req.getUuid());
            obj.setCreateTime(LocalDateTime.now());
            obj.setTenantId(req.getTenantId());
            obj.setUpdateTime(LocalDateTime.now());
            aiComplianceBackResultService.saveOrUpdate(obj);
            log.info("保存合规结果成功: {}", obj);
            return;
        }
        log.info("ai合规结果为空, {}", req);
    }


    /**
     * Parses a single indicator from the indicators node.
     *
     * @param indicatorsNode JSON node containing indicators
     * @param indicatorName Name of the indicator
     * @return Indicator object
     */
    private Indicator parseIndicator(JsonNode indicatorsNode, String indicatorName) {
        JsonNode node = indicatorsNode.get(indicatorName);
        if (node == null) {
            log.warn("Missing indicator: {}", indicatorName);
            return new Indicator(false, "Indicator not found in response");
        }
        JsonNode completedNode = node.get("completed");
        JsonNode reasonNode = node.get("reason");
        if (completedNode == null || reasonNode == null) {
            log.warn("Invalid indicator format for: {}", indicatorName);
            return new Indicator(false, "Invalid indicator format");
        }
        return new Indicator(completedNode.asBoolean(), reasonNode.asText());
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    public static class ComplianceResult {
        private boolean isValid;
        private String summary;
        private Indicator personalConfirmation;
        private Indicator productAmountConfirmation;
        private Indicator documentSigningConfirmation;
        private Indicator productServiceRiskAwareness;
        private Indicator autonomousDecisionRiskBearing;
        private Indicator riskCompatibilityAcceptance;
        private Indicator noAdditionalPromotion;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    public static class Indicator {
        private boolean completed;
        private String reason;
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.AI_COMPLIANCE_BACK_RESULT;
    }

    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (complianceBackThreadLocal.get() != null) {
            complianceBackThreadLocal.remove();
        }
    }
}
