package com.yirendai.voiceaiserver.task.conversation;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.service.*;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.vo.response.ChatApiRes;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiConversationTagsProcessMapper;
import com.yirendai.workbench.mapper.AiCustomerTagMapper;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.wrapper.CustomerInfoWrapper;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * AI对话内容处理服务抽象类
 *
 * <AUTHOR>
 * @description 用于处理AI对话内容的抽象类，提供了对话内容处理的基本流程
 * T 数据存储类
 */

@Slf4j
public abstract class AbstractAiConversationProcess<T> {

    // 依赖注入的服务
    @Resource
    protected AiPromptRecordService aiPromptRecordService;
    @Resource
    protected AiPlannerChatContactMapper aiPlannerChatContactMapper;
    @Resource
    protected AiConversationTagsProcessMapper aiConversationTagsProcessMapper;
    @Resource
    protected ChatService chatService;
    @Resource
    protected IAiUserTagCategoryService iAiUserTagCategoryService;
    @Resource
    protected IAiUserTagDictionaryService iAiUserTagDictionaryService;
    @Resource
    protected AiCustomerTagMapper aiCustomerTagMapper;
    @Resource
    protected AiConversationTagService aiConversationTagService;
    @Resource
    protected AiConversationWechatTagService aiConversationWechatTagService;
    @Resource
    protected TenantServiceHolder tenantServiceHolder;

    protected ThreadLocal<AiConversationProcessReq> req = new ThreadLocal<>();

    protected ThreadLocal<Boolean> finished = ThreadLocal.withInitial(() -> false);

    protected ThreadLocal<T> processRecord = new ThreadLocal<>();

    // processRecord的跨线程传递方法
    public T getProcessRecord() {
        return processRecord.get();
    }


    /**
     * AI对话处理前处理
     * @param req 请求
     * @return 提示词配置
     */
    public AiPromptRecordVo processBefore(AiConversationProcessReq req) {
        this.req.set(req);
        log.info("AI对话处理开始: {}", req);
        AiPromptRecordVo aiPromptRecordVo = getLastPrompt();
        validateAndInitialize(req);
        this.processRecord.set(getOrCreateProcessRecord(req));
        otherPreProcess();
        return aiPromptRecordVo;
    }

    /**
     * AI对话处理核心
     *
     * @param req 请求
     */
    public void process(AiConversationProcessReq req, AiPromptRecordVo lastAiPromptConfig) {
        String question = buildQuestion(req.getContent(), lastAiPromptConfig, req.getCallTime(), req.getTenantId());
        log.info("AI对话处理请求前-普通请求,对话ID，{},长度，{}, 对话类型，{}，问题: {}", req.getChatContactId(), req.getContent().length(), getPromptScene(), question);
        String aiReplyContent = chat(question, lastAiPromptConfig.getModel(), lastAiPromptConfig.getTemperature(), req.getModelType(), lastAiPromptConfig.getSystemPrompt());
        log.info("AI对话处理结束: {}, 回复内容: {}", getPromptScene(), aiReplyContent);
        parseResponse(aiReplyContent);
        T t = processRecord.get();
        processResponse(req, t);
    }

    /**
     * AI对话处理请求
     * @param question 用户问题
     * @param model 模型
     * @param temperature 温度
     * @param type 模型类型
     * @return AI回复内容
     */
    protected abstract String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt);


    /**
     * AI对话处理分片处理核心
     * @param chunk 文本块
     * @param processReq 请求
     * @param lastPromptConfig 上一次的配置
     * @return 处理结果
     */
    public String processChunk(String chunk, AiConversationProcessReq processReq, AiPromptRecordVo lastPromptConfig) {
        // 使用分片文本
        chunk = chunk.trim();
        // 检查对象是否属于指定类型
        String question = buildQuestion(chunk, lastPromptConfig, processReq.getCallTime(), processReq.getTenantId());
        log.info("AI对话处理请求前-分片请求，对话ID{}, 类型，{}, 对话切分长度{}, 问题:{}", processReq.getChatContactId(), getPromptScene(), chunk.length(), question);
        String aiReplyContent = chat(question, lastPromptConfig.getModel(), lastPromptConfig.getTemperature(), processReq.getModelType(), lastPromptConfig.getSystemPrompt());
        log.info("AI对话分片处理: {}, 回复内容: {}", getPromptScene(), aiReplyContent);
        return aiReplyContent;
    }


    /**
     * 分段请求汇总处理AI回复内容
     * @param resList 回复内容
     * @param processReq 请求
     * @param processRecord 数据存储类
     */
    public void processChunkAfter(List<String> resList, AiConversationProcessReq processReq, Object processRecord) {
        if (resList.isEmpty()) {
            return;
        }
        // 解析AI回复内容
        for (String res : resList) {
            parseResponse(res);
        }
        // 处理AI处理完的回复内容
        processResForChunk(processReq, processRecord);
    }


    /**
     * 构建分段请求的合并提示词
     * @param latestPromptRecordByScene 最新的合并提示词
     * @param processReq 请求类
     * @return 构建后的合并提示词
     */
    protected String buildChunkAfterQuestion(AiPromptRecordVo latestPromptRecordByScene, AiConversationProcessReq processReq) {
        return "";
    }

    /**
     * 处理AI处理完的回复内容
     * @param processReq 请求
     * @param processRecord 数据存储类
     */
    protected abstract void processResForChunk(AiConversationProcessReq processReq, Object processRecord);

    /**
     * 提取AI回复内容
     * @param response 接口返回的回复内容
     * @return AI回复内容
     */
    protected String extractAiReplyContent(String response) {
        if (StrUtil.isBlank(response)) {
            log.error("AI对话处理调用接口异常: {}, 回复内容: {}", getPromptScene(), response);
            throw new AiServerException(ResultCode.AI_RESULT_ERROR);
        }
        ChatApiRes chatApiRes = JSON.parseObject(response, ChatApiRes.class);
        if (isInvalidChatApiResponse(chatApiRes)) {
            throw new AiServerException(ResultCode.AI_RESULT_ERROR);
        }
        return chatApiRes.getChoices().get(0).getMessage().getContent();
    }

    private boolean isInvalidChatApiResponse(ChatApiRes chatApiRes) {
        return chatApiRes == null ||
                CollectionUtils.isEmpty(chatApiRes.getChoices()) ||
                chatApiRes.getChoices().get(0).getMessage() == null ||
                StrUtil.isBlank(chatApiRes.getChoices().get(0).getMessage().getContent());
    }

    /**
     * 获取最新的AI提示语
     */
    protected AiPromptRecordVo getLastPrompt() {
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(getPromptScene().getCode());
        if (Objects.isNull(latestPromptRecordByScene) || StrUtil.isBlank(latestPromptRecordByScene.getPrompt()) ||
                StrUtil.isBlank(latestPromptRecordByScene.getModel()) || Objects.isNull(latestPromptRecordByScene.getTemperature())) {
            throw new AiServerException(ResultCode.NO_AI_PROMPT_CONFIG);
        }
        return latestPromptRecordByScene;
    }

    /**
     * 数据校验与初始化方法
     */
    private void validateAndInitialize(AiConversationProcessReq req) {
        if (Strings.isNullOrEmpty(req.getTaskId())) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        if (req.getChatContactId() == null) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        initialize(req);
    }

    /**
     * 初始化抽象方法
     */
    protected abstract void initialize(AiConversationProcessReq req);


    /**
     * 初始化记录类
     */
    public T initProcessRecord(AiConversationProcessReq req) {
        return null;
    }

    /**
     * 获取或创建处理记录
     */
    protected abstract T getOrCreateProcessRecord(AiConversationProcessReq req);

    /**
     * 其它前置处理
     */
    protected void otherPreProcess() {
    }

    /**
     * 构建AI请求问题
     */

    public abstract String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId);

    /**
     * 解析AI响应
     */
    protected abstract void parseResponse(String aiReplyContent);

    /**
     * 处理AI响应并落库
     */
    protected abstract void processResponse(AiConversationProcessReq req, T obj);

    /**
     * 获取提示词场景
     * @return 提示词场景
     */
    protected abstract PromptSceneEnum getPromptScene();


    public String cleanJsonString(String input) {
        // 找到第一个 '{' 或 '[' 的位置
        int startIndex = Math.min(
                input.indexOf('{') != -1 ? input.indexOf('{') : Integer.MAX_VALUE,
                input.indexOf('[') != -1 ? input.indexOf('[') : Integer.MAX_VALUE
        );

        // 找到最后一个 '}' 或 ']' 的位置
        int endIndex = Math.max(
                input.lastIndexOf('}') != -1 ? input.lastIndexOf('}') : Integer.MIN_VALUE,
                input.lastIndexOf(']') != -1 ? input.lastIndexOf(']') : Integer.MIN_VALUE
        );

        // 确保索引有效并且起始索引小于结束索引
        if (endIndex > startIndex) {
            return input.substring(startIndex, endIndex + 1).trim();
        }

        log.error("cleanJsonString Invalid JSON format: {}", input);
        if ((startIndex != Integer.MAX_VALUE && endIndex == Integer.MIN_VALUE) || (startIndex == Integer.MAX_VALUE && endIndex != Integer.MIN_VALUE)) {
            throw new AiServerException(ResultCode.AI_REPLY_INVALID_JSON);
        }

        return null;
    }

    /**
     * 清理线程变量
     */
    public void cleanThreadLocal() {
        if (req != null) {
            req.remove();
        }
        if (finished != null) {
            finished.remove();
        }
        if (processRecord != null) {
            processRecord.remove();
        }
    }

    public String getContent(AiConversationProcessReq aiConversationProcessReq) {
        return aiConversationProcessReq.getContent();
    }

    /**
     * 根据手机号获取用户ID
     * @return 用户ID
     */
    protected String getUserIdByPhoneNumber(String tenantId, String phoneNumber) {
        CustomerInfoWrapper customerInfoWrapper = tenantServiceHolder.getCustomerInfoWrapper(tenantId);
        if (customerInfoWrapper != null) {
            SimpleUserInfoDto userBasicInfoByPhone = customerInfoWrapper.getUserBasicInfoByPhone(phoneNumber, tenantId);
            if (userBasicInfoByPhone != null) {
                return userBasicInfoByPhone.getUserId();
            }
        }
        return null;
    }

    /**
     * 验证plannerChatContact来源表的数据
     */
    protected void validatePlannerChatContact(AiConversationProcessReq processReq) {
        AiPlannerChatContact aiPlannerChatContact = null;
        Long chatContactId = processReq.getChatContactId();
        String userId = processReq.getUserId();
        String plannerNo = processReq.getPlannerNo();
        String content = processReq.getContent();
        LocalDateTime callTime = processReq.getCallTime();
        String taskId = processReq.getTaskId();
        String uuid = processReq.getUuid();
        String userPhone = processReq.getUserPhone();
        AiModelType chatType = processReq.getModelType();
        String chatId = processReq.getChatId();

        if (Objects.isNull(plannerNo) || Objects.isNull(userId) || Strings.isNullOrEmpty(content) || Objects.isNull(chatType) || Strings.isNullOrEmpty(uuid)) {
            aiPlannerChatContact = aiPlannerChatContactMapper.selectById(chatContactId);
            if (Objects.isNull(aiPlannerChatContact)) {
                throw new AiServerException(ResultCode.CHAT_CONTENT_EMPTY);
            }
            processReq.setPlannerNo(aiPlannerChatContact.getPlannerNo());
            processReq.setUserId(aiPlannerChatContact.getUserId());
            processReq.setContent(aiPlannerChatContact.getProcessedContent());
            processReq.setCallTime(aiPlannerChatContact.getMsgTime());
            processReq.setUserPhone(aiPlannerChatContact.getPhone());
            processReq.setUuid(aiPlannerChatContact.getBusId());
            processReq.setTenantId(aiPlannerChatContact.getTenantId());
        } else if (getPromptScene() == PromptSceneEnum.NEXT_CALL_TIME && Objects.isNull(callTime)) {
            aiPlannerChatContact = aiPlannerChatContactMapper.selectById(chatContactId);
            if (Objects.isNull(aiPlannerChatContact)) {
                throw new AiServerException(ResultCode.CHAT_CONTENT_EMPTY);
            }
            processReq.setCallTime(aiPlannerChatContact.getMsgTime());
        } else if (getPromptScene() == PromptSceneEnum.USER_TAG && Strings.isNullOrEmpty(userPhone)) {
            aiPlannerChatContact = aiPlannerChatContactMapper.selectById(chatContactId);
            if (Objects.isNull(aiPlannerChatContact)) {
                throw new AiServerException(ResultCode.CHAT_CONTENT_EMPTY);
            }
            processReq.setUserPhone(aiPlannerChatContact.getPhone());
        }
        if ((getPromptScene() == PromptSceneEnum.HONG_KONG_INSURANCE_INTENT || getPromptScene() == PromptSceneEnum.HONG_KONG_INSURANCE_NODES) && Strings.isNullOrEmpty(chatId)) {
            if (aiPlannerChatContact == null) {
                aiPlannerChatContact = aiPlannerChatContactMapper.selectById(chatContactId);
            }
            if (Objects.equals(aiPlannerChatContact.getOriginType(), QwMsgTypeEnum.AVAYA.getType())) {
                processReq.setChatId(aiPlannerChatContact.getAvayaId());
            } else {
                processReq.setChatId(aiPlannerChatContact.getBusId());
            }
        }
    }

    /**
     * 处理报错回调
     * @param processReq 请求参数
     * @param finished 是否完成
     */
    public void processErrorCallback(AiConversationProcessReq processReq, boolean finished) {
    }
}

