package com.yirendai.voiceaiserver.task.priority;

import org.jetbrains.annotations.NotNull;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
public class PriorityThreadPoolExecutor extends ThreadPoolExecutor {

    public PriorityThreadPoolExecutor(int corePoolSize,
                                      int maximumPoolSize,
                                      long keepAliveTime,
                                      TimeUnit unit,
                                      BlockingQueue<Runnable> workQueue,
                                      ThreadFactory threadFactory,
                                      RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    @Override
    public void execute(@NotNull Runnable command) {
        if (command instanceof PrioritizedTask) {
            super.execute(command);
        } else {
            super.execute(new PrioritizedTask(command, PriorityThreadEnum.MEDIUM));
        }
    }

    public void execute(Runnable command, PriorityThreadEnum priority) {
        super.execute(new PrioritizedTask(command, priority));
    }
}

