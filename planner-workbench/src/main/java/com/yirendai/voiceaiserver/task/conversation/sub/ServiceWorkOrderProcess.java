package com.yirendai.voiceaiserver.task.conversation.sub;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.constant.AiConstant;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.AiCustomerWorkOrderService;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.AiAnalysisTaskResult;
import com.yirendai.workbench.entity.AiConversationTagsProcess;
import com.yirendai.workbench.entity.AiCustomerWorkOrder;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ServiceWorkOrderProcess extends AbstractAiConversationProcess<AiCustomerWorkOrder> {

    @Resource
    private AiCustomerWorkOrderService aiCustomerWorkOrderService;

    protected ThreadLocal<List<String>> lastResultList = ThreadLocal.withInitial(ArrayList::new);

    protected ThreadLocal<String> lastResult = new ThreadLocal<>();

    @Override
    protected String chat(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        String response = chatService.chatAiContent(question, model, temperature, type);
        return extractAiReplyContent(response);
    }

    @Override
    protected void processResForChunk(AiConversationProcessReq processReq, Object processRecord) {
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.AI_SERVICE_WORK_ORDER_SUMMARY.getCode());
        String question = buildChunkAfterQuestion(latestPromptRecordByScene, processReq);
        log.info("AI客服工单处理请求前-小结汇总请求，对话ID{}, 客服工单汇总长度{}, 问题:{}", processReq.getChatContactId(), processReq.getContent().length(), question);
        String response = chatService.chat(question, latestPromptRecordByScene.getModel(), latestPromptRecordByScene.getTemperature());
        String aiReplyContent = extractAiReplyContent(response);
        log.info("AI客服工单汇总回复: {}", aiReplyContent);
        parseResponse(aiReplyContent);
        processResponse(processReq, (AiCustomerWorkOrder) processRecord);
    }

    @Override
    protected String buildChunkAfterQuestion(AiPromptRecordVo latestPromptRecordByScene, AiConversationProcessReq processReq) {
        return String.format(latestPromptRecordByScene.getPrompt(), lastResultList.get().toString());
    }

    @Override
    protected void initialize(AiConversationProcessReq req) {
        initProcessRecord(req);
    }

    @Override
    protected AiCustomerWorkOrder getOrCreateProcessRecord(AiConversationProcessReq req) {
        AiCustomerWorkOrder aiCustomerWorkOrder = processRecord.get();
        if (aiCustomerWorkOrder != null) {
            return aiCustomerWorkOrder;
        }
        return initProcessRecord(req);
    }

    @Override
    public AiCustomerWorkOrder initProcessRecord(AiConversationProcessReq req) {
        Long id = req.getChatContactId();
        AiCustomerWorkOrder aiCustomerWorkOrder = aiCustomerWorkOrderService.getById(id);
        if (aiCustomerWorkOrder == null) {
            log.error("对话任务内容分析数据源为空 initProcessRecord 1 id:{}", id);
            throw new AiServerException(ResultCode.WORK_ORDER_ANALYSIS_SOURCE_EMPTY);
        }
        processRecord.set(aiCustomerWorkOrder);
        Long chatContactId = aiCustomerWorkOrder.getChatContactId();
        AiPlannerChatContact aiPlannerChatContact = aiPlannerChatContactMapper.selectById(chatContactId);
        if (aiPlannerChatContact == null) {
            log.error("对话任务内容分析数据源为空 initProcessRecord 2 chatContactId:{}", chatContactId);
            throw new AiServerException(ResultCode.WORK_ORDER_ANALYSIS_SOURCE_EMPTY);
        }
        req.setContent(aiPlannerChatContact.getProcessedContent());
        aiCustomerWorkOrder.setStatus(1);
        aiCustomerWorkOrder.setUpdateTime(LocalDateTime.now());
        aiCustomerWorkOrderService.updateById(aiCustomerWorkOrder);
        return aiCustomerWorkOrder;
    }

    @Override
    public String buildQuestion(String content, AiPromptRecordVo lastPromptConfig, LocalDateTime callTime, String tenantId) {
        AiCustomerWorkOrder aiCustomerWorkOrder = processRecord.get();
        LocalDateTime now = LocalDateTime.now();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss");
        String timeStr = now.format(formatter);

        String dayOfWeek = now.getDayOfWeek().getDisplayName(java.time.format.TextStyle.FULL, Locale.CHINESE);

        String promptWithTime = lastPromptConfig.getPrompt() + "   当前时间：%s %s";
        return String.format(promptWithTime, content, aiCustomerWorkOrder.getSegmentConfig(), timeStr, dayOfWeek);
    }

    @Override
    protected void parseResponse(String aiReplyContent) {
        if (AiConstant.EMPTY_DATA.equals(aiReplyContent)) {
            return;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        aiReplyContent = aiReplyContent.trim();
        try {
            aiReplyContent = cleanJsonString(aiReplyContent);
            objectMapper.readTree(aiReplyContent);
            lastResult.set(aiReplyContent);
            lastResultList.get().add(aiReplyContent);
        } catch (Exception e) {
            log.error("ai意向结果解析失败:", e);
            throw new AiServerException(ResultCode.AI_REPLY_PARSE_ERROR);
        }
    }

    @Override
    protected void processResponse(AiConversationProcessReq req, AiCustomerWorkOrder obj) {
        if (Strings.isNullOrEmpty(lastResult.get())) {
            log.error("客服工单分析结果为空 processResponse");
            throw new AiServerException(ResultCode.WORK_ORDER_ANALYSIS_RESULT_EMPTY);
        }
        obj.setResult(lastResult.get());
        obj.setStatus(2);
        obj.setUpdateTime(LocalDateTime.now());
        aiCustomerWorkOrderService.updateById(obj);
    }

    @Override
    public void processErrorCallback(AiConversationProcessReq processReq, boolean finished) {
        log.error("对话任务内容分析处理失败 processErrorCallback:{}", processReq);
        AiCustomerWorkOrder aiCustomerWorkOrder = processRecord.get();
        aiCustomerWorkOrder.setStatus(finished ? 3 : 4);
        aiCustomerWorkOrder.setUpdateTime(LocalDateTime.now());
        aiCustomerWorkOrderService.updateById(aiCustomerWorkOrder);
    }

    @Override
    protected PromptSceneEnum getPromptScene() {
        return PromptSceneEnum.AI_SERVICE_WORK_ORDER;
    }


    @Override
    public void cleanThreadLocal() {
        super.cleanThreadLocal();
        if (lastResultList.get() != null) {
            lastResultList.remove();
        }
        if (lastResult.get() != null) {
            lastResult.remove();
        }
    }
}
