package com.yirendai.voiceaiserver.listener;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.vo.response.ChatMessagesStreamRes;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2023/11/14 10:19
 **/
@Slf4j
public class TeleprompterEventSourceListener extends EventSourceListener {

    private final SseEmitter sseEmitter;

    private static final String MESSAGE_END = "message_end";

    private final String account;

    private final String query;

    public TeleprompterEventSourceListener(SseEmitter sseEmitter, String account, String query) {
        this.sseEmitter = sseEmitter;
        this.account = account;
        this.query = query;
    }

    public void onOpen(EventSource eventSource, Response response) {
        log.info("aics建立sse连接...");
    }

    public void onEvent(EventSource eventSource, String id, String type, String data) {
        if ("ping".equals(data)) {
            return;
        }
        try {
            ChatMessagesStreamRes chatMessagesRes = JSONUtil.toBean(data, ChatMessagesStreamRes.class);
            if (MESSAGE_END.equals(chatMessagesRes.getEvent())) {
                chatMessagesRes.setAnswer("");
                chatMessagesRes.setEnd(Boolean.TRUE);
            } else if (Objects.nonNull(chatMessagesRes.getAnswer())) {
                chatMessagesRes.setAnswer(chatMessagesRes.getAnswer().replaceAll("\\r?\\n|\\r", "</br>"));
            }
            sseEmitter.send(JSONUtil.toJsonStr(R.data(chatMessagesRes)));
        } catch (IOException e) {
            log.error("aics sse 发送数据异常", e);
        }
    }

    public void onClosed(EventSource eventSource) {
        log.info("aics 关闭sse连接...");
        sseEmitter.complete();
    }

    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        try {
            if (Objects.isNull(response)) {
                log.error("aics sse连接异常:{}", t);
                eventSource.cancel();
            } else {
                ResponseBody body = response.body();
                if (Objects.nonNull(body)) {
                    log.error("aics sse连接异常data：{}，异常：{}", body.string(), t);
                } else {
                    log.error("aics sse连接异常data：{}，异常：{}", response, t);
                }
                eventSource.cancel();
            }
            sseEmitter.completeWithError(t);
        } catch (Throwable t2) {
            log.error("aics sse onFailure error", t2);
        }
    }
}
