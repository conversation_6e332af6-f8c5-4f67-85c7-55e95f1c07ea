package com.yirendai.voiceaiserver.listener;

import java.io.IOException;
import java.util.Objects;

import cn.hutool.json.JSONUtil;
import com.yirendai.voiceaiserver.service.AiHelperService;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.EventResponse;
import com.yirendai.voiceaiserver.vo.request.AiHelperMessageReq;
import com.yirendai.workbench.entity.AiHelperMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI 智能体助手
 **/
@Slf4j
public class AiHelperEventSourceListener extends EventSourceListener {

    private final SseEmitter sseEmitter;

    private static final String MESSAGE_END = "message_end";

    /**
     * 消息内容替换事件。 开启内容审查和审查输出内容时，若命中了审查条件，则会通过此事件替换消息内容为预设回复。
     */
    private static final String MESSAGE_REPLACE = "message_replace";

    /**
     *  Agent模式下有关Agent思考步骤的相关内容，涉及到工具调用（仅Agent模式下使用）
     */
    private static final String AGENT_THOUGHT = "agent_thought";

    private static final String NODE_STARTED = "node_started";

    private static final String NODE_FINISHED = "node_finished";


    private static final String AGENT_MESSAGE = "agent_message";

    private AiHelperMessageReq aiHelperMessageReq;

    private AiHelperService aiHelperService;
    private final StringBuilder answer = new StringBuilder();

    public AiHelperEventSourceListener(SseEmitter sseEmitter, AiHelperMessageReq aiHelperMessageReq ,AiHelperService aiHelperService) {
        this.sseEmitter = sseEmitter;
        this.aiHelperMessageReq = aiHelperMessageReq;
        this.aiHelperService = aiHelperService;
    }

    public void onOpen(EventSource eventSource, Response response) {
        log.info("AiHelper see 建立sse连接...");
    }

    public void onEvent(EventSource eventSource, String id, String type, String data) {
        if ("ping".equals(data)) {
            return;
        }
        try {
            log.info("AiHelper see response = {}",data);
            EventResponse chatMessagesRes = JSONUtil.toBean(data, EventResponse.class);
            if (StringUtil.isNotBlank(chatMessagesRes.getAnswer() )) {
                answer.append(chatMessagesRes.getAnswer());
            }
            if(MESSAGE_REPLACE.equals(chatMessagesRes.getEvent()))
            {
                log.info("AiHelper see answer is message_replace: {}", answer);
                answer.setLength(0);
                answer.append(chatMessagesRes.getAnswer());
                sseEmitter.send(" "+data);
            }
            else if(AGENT_THOUGHT.equals(chatMessagesRes.getEvent())){
                log.info("AiHelper see answer is agent_thought: {}", answer);
            }
            else if(NODE_STARTED.equals(chatMessagesRes.getEvent())){
                log.info("AiHelper see answer is node_started: {}", answer);
            }
            else if(NODE_FINISHED.equals(chatMessagesRes.getEvent())){
                log.info("AiHelper see answer is node_finished: {}", answer);
            }
            else if(AGENT_MESSAGE.equals(chatMessagesRes.getEvent())){
                log.info("AiHelper see answer is agent_message: {}", answer);
                sseEmitter.send(" "+data);
            }
            else if (MESSAGE_END.equals(chatMessagesRes.getEvent())) {
                log.info("AiHelper see event : {} answer is : {}",chatMessagesRes.getEvent(), answer);
                //ai_helper_message
                AiHelperMessage aiHelperMessage = new AiHelperMessage();
                aiHelperMessage.setAccount(aiHelperMessageReq.getAccount());
                aiHelperMessage.setUserId(aiHelperMessageReq.getUserId());
                aiHelperMessage.setConversationId(chatMessagesRes.getConversation_id());
                aiHelperMessage.setQuestion(aiHelperMessageReq.getQuery());
                aiHelperMessage.setAnswer(answer.toString());
                aiHelperMessage.setMessageId(chatMessagesRes.getMessage_id());
                aiHelperMessage.setTenantId(aiHelperMessageReq.getTenantId());
                aiHelperService.saveMessage(aiHelperMessage);
                sseEmitter.send(" "+data);
            }else{
                log.info("AiHelper see event : {} answer is : {}",chatMessagesRes.getEvent(), chatMessagesRes.getAnswer());
                sseEmitter.send(" "+data);
            }

        } catch (IOException e) {
            log.error("AiHelper see 发送数据异常", e);
        }
    }

    public void onClosed(EventSource eventSource) {
        log.info("AiHelper see 关闭sse连接...");
        sseEmitter.complete();
    }

    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        try {
            if (Objects.isNull(response)) {
                log.error("AiHelper see 连接异常:{} {}", t.getMessage(),t);
                eventSource.cancel();
            }
            else {
                ResponseBody body = response.body();
                if (Objects.nonNull(body)) {
                    log.error("AiHelper see 连接异常data：{}，异常：{} {}", body.string(),t.getMessage(), t);
                }
                else {
                    log.error("AiHelper see 连接异常data：{}，异常：{} {}", response,t.getMessage(), t);
                }

                eventSource.cancel();
            }
            sseEmitter.completeWithError(t);
        } catch (Throwable t2) {
            log.error("AiHelper see  onFailure error {}",t2.getMessage(), t2);
        }
    }
}
