package com.yirendai.voiceaiserver.controller;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.enums.AssetTypeEnum;
import com.yirendai.workbench.entity.AiPlannerTopUser;
import com.yirendai.workbench.entity.AdsCsgDOrderDetailDf;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.service.AdsCsgDOrderDetailDfService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.voiceaiserver.service.impl.AvayaVoiceService;
import com.yirendai.voiceaiserver.vo.request.*;
import com.yirendai.voiceaiserver.vo.response.AssetTypeVO;
import com.yirendai.voiceaiserver.vo.response.OrderPageRes;
import com.yirendai.voiceaiserver.vo.response.PlannerListRes;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * D类保险业绩明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@RestController
@RequestMapping("/voice/ai/server/planner")
@Api(tags = "理财师")
public class PlannerAchievementController {

    @Resource
    AdsCsgDOrderDetailDfService adsCsgDOrderDetailDfService;
    @Resource
    IAiPlannerTopUserService aiPlannerTopUserService;
    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;
    @Resource
    AvayaVoiceService avayaVoiceService;

    @ApiOperation(value = "分页查询理财师列表")
    @PostMapping("/page")
    public R<PlannerListRes> page(@Validated PlannerReq req) {
        if (Objects.isNull(req.getSize())) {
            req.setSize(10);
        }
        if (Objects.isNull(req.getCurrent())) {
            req.setCurrent(1);
        }
        return R.data(adsCsgDOrderDetailDfService.page(req));
    }

    @PostMapping("/relatedUsers")
    @ApiOperation(value = "分页查询理财师名下的客户列表")
    public R<IPage<AiPlannerTopUser>> relatedUsers(@Validated PlannerUserReq req) {
        if (Objects.isNull(req.getSize())) {
            req.setSize(10);
        }
        if (Objects.isNull(req.getCurrent())) {
            req.setCurrent(1);
        }
        return R.data(aiPlannerTopUserService.relatedUsers(req));
    }

    @PostMapping("/user/orders")
    @ApiOperation(value = "根据客户、理财师分页查询D类订单列表")
    public R<IPage<AdsCsgDOrderDetailDf>> userOrders(@Validated PlannerUserOrderReq req) {
        if (Objects.isNull(req.getSize())) {
            req.setSize(10);
        }
        if (Objects.isNull(req.getCurrent())) {
            req.setCurrent(1);
        }
        return R.data(adsCsgDOrderDetailDfService.userOrders(req));
    }

    @PostMapping("/user/types/orders")
    @ApiOperation(value = "根据客户、理财师查询指定资产类型的订单列表")
    public R<OrderPageRes> userTypesOrders(@Validated PlannerUserTypeOrderReq req) {
        String[] types = req.getAssetType().split(",");
        List<Integer> typeList = Arrays.stream(types).filter(StrUtil::isNotBlank).map(Integer::valueOf).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(typeList)) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        List<Integer> errList = typeList.stream().filter(t -> !AssetTypeEnum.allCodeList.contains(t)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errList)) {
            return R.fail(ResultCode.PARAM_INVALID, "存在不合法的资产类型");
        }
        return R.data(adsCsgDOrderDetailDfService.userTypesOrders(req, typeList));
    }

    @GetMapping("/getAssetTypes")
    @ApiOperation(value = "获取所有资产类型")
    public R<List<AssetTypeVO>> getAssetTypes() {
        return R.data(AssetTypeEnum.allList);
    }

    @PostMapping("/user/chatMsgPage")
    @ApiOperation(value = "根据客户、理财师分页查询消息列表")
    public R<IPage<AiPlannerChatContact>> userChatMsgPage(@Validated PlannerUserChatReq req) {
        if (Objects.isNull(req.getSize())) {
            req.setSize(10);
        }
        if (Objects.isNull(req.getCurrent())) {
            req.setCurrent(1);
        }
        if (Objects.nonNull(req.getStartId()) && Objects.nonNull(req.getEndId())
                || Objects.nonNull(req.getStartId()) && Objects.isNull(req.getStartTime())
                || Objects.nonNull(req.getEndId()) && (Objects.isNull(req.getEndTime()) || Objects.nonNull(req.getStartTime()))) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return R.data(iAiPlannerChatContactService.userChatMsgPage(req));
    }

    @PostMapping("/user/chatMsgList")
    @ApiOperation(value = "根据客户、理财师查询某个时间点附近的消息列表")
    public R<List<AiPlannerChatContact>> userChatMsgList(@Validated PlannerUserChatListReq req) {
        if (Objects.isNull(req.getSize())) {
            req.setSize(10);
        }
        return R.data(iAiPlannerChatContactService.userChatMsgList(req));
    }

    @GetMapping("/updateUSDExchangeRate/{rate}")
    @ApiOperation(value = "更新美元汇率")
    public R<String> updateUSDExchangeRate(
            @ApiParam(value = "美元汇率", required = true) @PathVariable("rate") String rate) {
        return R.data(adsCsgDOrderDetailDfService.updateUSDExchangeRate(rate));
    }

    @PostMapping("/updateModelPrompt")
    @ApiOperation(value = "更新分割avaya语料模型提示词")
    public R<Object> updateModelPrompt(@Validated PromptReq req) {
        avayaVoiceService.updateModelPrompt(req);
        return R.data(null);
    }
}

