package com.yirendai.voiceaiserver.controller;

import com.google.protobuf.ServiceException;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.IAiUserTagDictionaryService;
import com.yirendai.voiceaiserver.vo.response.*;
import com.yirendai.workbench.mapper.AiPromptRecordMapper;
import com.yirendai.workbench.entity.AiPromptRecord;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.vo.request.PromptConfigReq;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yirendai.workbench.util.OwnAuthUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.utils.DateTimeUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAccessor;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.yirendai.voiceaiserver.enums.AiModelType.COMMON_MODEL;

@RestController
@RequestMapping("/voice/ai/server/prompt")
@Slf4j
@Api(tags = "提示词配置管理")
public class AiPromptManageController {
    @Resource
    private ChatService chatService;
    @Resource
    private AiPromptRecordService aiPromptRecordService;
    @Resource
    private AiPromptRecordMapper aiPromptRecordMapper;
    @Resource
    private IAiUserTagDictionaryService aiUserTagDictionaryService;

    @PostMapping("/test")
    @ApiOperation(value = "测试提示词效果")
    public R<String> testPrompt(@Valid @RequestBody PromptConfigReq promptConfigReq) {
        String prompt = promptConfigReq.getPrompt();
        String content = promptConfigReq.getContent();
        String model = promptConfigReq.getModel();
        Float temperature = promptConfigReq.getTemperature();
        Integer scene = promptConfigReq.getScene();
        if (StringUtils.isBlank(prompt) || StringUtils.isBlank(content) || StringUtils.isBlank(model)) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        String resContent = chat(prompt, content, model, temperature, scene);

        return R.data(resContent);
    }


    @PostMapping("/batchTest")
    @ApiOperation(value = "批量测试提示词效果")
    public void batchTestPrompt(@ApiParam(value = "提示词", required = true) @RequestParam("prompt") String prompt,
            @ApiParam(value = "模型", required = true) @RequestParam("model") String model,
            @ApiParam(value = "温度", required = true) @RequestParam("temperature") Float temperature,
            @ApiParam(value = "使用场景", required = true) @RequestParam("scene") Integer scene,
            @ApiParam(value = "聊天文件", required = true) MultipartFile file,  HttpServletResponse response) throws ServiceException {

        List<ChatContentExcel> list = ExcelUtil.read(file, ChatContentExcel.class);
        if(CollectionUtils.isNotEmpty(list)){
            for(ChatContentExcel excel:list){
                excel.setResult(chat(prompt,excel.getContent(),model,temperature,scene));
            }
            ExcelUtil.export(response, "chat-result", "处理后结果", list, ChatContentExcel.class);
        }else{
            throw new ServiceException("导入数据不正常");
        }
    }


    private String chat(String prompt, String content, String model, Float temperature, Integer scene) {
        String question = null;
        if(PromptSceneEnum.NEXT_CALL_TIME.getCode().equals(scene)){
            String datetime = DateUtil.format(new Date(),DateUtil.PATTERN_DATETIME);
            question=String.format(prompt,datetime, content);
        } else if (PromptSceneEnum.USER_TAG.getCode().equals(scene)) {
            String tenantId = OwnAuthUtil.getTenantId();
            List<AiTagCategoryWithTags> tags = aiUserTagDictionaryService.findAllCategoriesWithTags(tenantId);
            question=String.format(prompt, content, tags);
        } else{
            question=String.format(prompt, content);
        }
        String response = chatService.chatAiContent(question, model, temperature,COMMON_MODEL);
        String resContent = null;
        if (StringUtils.isBlank(response)) {
            resContent =  ResultCode.FAILURE.getMessage();
        }else{
            ChatApiRes chatApiRes = JSON.parseObject(response, ChatApiRes.class);
            if (Objects.isNull(chatApiRes) || CollectionUtils.isEmpty(chatApiRes.getChoices())
                    || Objects.isNull(chatApiRes.getChoices().get(0).getMessage())
                    || StringUtils.isBlank(chatApiRes.getChoices().get(0).getMessage().getContent())) {
                resContent = ResultCode.FAILURE.getMessage();
            }else{
                resContent = chatApiRes.getChoices().get(0).getMessage().getContent();
            }
        }
        return resContent;
    }

    @GetMapping("/allScenes")
    @ApiOperation(value = "获取提示词配置使用的所有场景")
    public R<List<PromptSceneVo>> getAllScenes() {
        return R.data(PromptSceneEnum.allList);
    }

    @PostMapping("/update")
    @ApiOperation(value = "根据场景类型更新提示词配置")
    public R<Object> updatePrompt(@Valid @RequestBody PromptConfigReq promptConfigReq) {
        AiPromptRecord aiPromptRecord = new AiPromptRecord();
        //校验提示词配置是否符合要求
        if (!aiPromptRecordService.validatePromptConfig(promptConfigReq)) {
            return R.fail("提示词配置未通过校验,更新提示词配置失败");
        }
        BeanUtils.copyProperties(promptConfigReq, aiPromptRecord);
        aiPromptRecordMapper.insert(aiPromptRecord);
        return R.data(null);
    }

    @GetMapping("/latest/{sceneType}")
    @ApiOperation(value = "根据场景类型获取当前使用的提示词配置")
    public R<AiPromptRecordVo> getLatestPromptByScene(@PathVariable Integer sceneType) {
        AiPromptRecordVo aiPromptRecordVo = aiPromptRecordService.getLatestPromptRecordByScene(sceneType);
        return R.data(aiPromptRecordVo);
    }

    @PostMapping("/history")
    @ApiOperation(value = "根据场景类型获取使用过的提示词配置")
    public R<IPage<AiPromptRecordVo>> getHistoryPromptByScene(@Valid @RequestBody PromptConfigReq promptConfigReq) {
        IPage<AiPromptRecordVo> record = aiPromptRecordService.getHistoryPromptRecord(promptConfigReq);
        return R.data(record);
    }
}
