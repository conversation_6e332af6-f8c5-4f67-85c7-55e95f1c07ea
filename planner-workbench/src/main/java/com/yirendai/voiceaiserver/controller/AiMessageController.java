package com.yirendai.voiceaiserver.controller;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.enums.MessageTypeEnum;
import com.yirendai.voiceaiserver.service.AiMessageService;
import com.yirendai.voiceaiserver.vo.request.MessageSaveReq;
import com.yirendai.voiceaiserver.vo.request.SuggestionBatchReq;
import com.yirendai.voiceaiserver.vo.request.SuggestionReq;
import com.yirendai.voiceaiserver.vo.response.AiMessageVO;
import com.yirendai.voiceaiserver.vo.response.SuggestionInfo;
import com.yirendai.voiceaiserver.vo.response.SuggestionListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * ai信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@RestController
@RequestMapping("/voice/ai/server/message")
@Api(tags = "信息")
public class AiMessageController {

    @Resource
    AiMessageService aiMessageService;

    /**
     * 新增/更新今日新闻或活动规则
     * @param req 类型+内容
     * @return 处理结果
     */
    @ApiOperation(value = "新增/更新今日新闻或活动规则")
    @PostMapping("/saveOrUpdate")
    public R<AiMessageVO> saveOrUpdate(@Validated MessageSaveReq req) {
        if (!MessageTypeEnum.getAll().contains(req.getType())) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return aiMessageService.saveOrUpdate(req.getType(), req.getContent());
    }

    /**
     * 查询今日新闻或活动规则
     * @param type 类型
     * @return 信息VO
     */
    @ApiOperation(value = "查询今日新闻或活动规则", notes = "不需要登录")
    @PostMapping("/get")
    public R<AiMessageVO> get(@ApiParam(value = "类型 1：新闻 2：活动规则", required = true) @RequestParam("type") Integer type) {
        if (Objects.isNull(type) || !MessageTypeEnum.getAll().contains(type)) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return aiMessageService.get(type);
    }

    /**
     * 根据客户userId查询一套推荐话术
     * @param req 客户userId
     * @return 推荐话术
     */
    @ApiOperation(value = "根据客户userId查询一套推荐话术")
    @PostMapping("/getSuggestion")
    public R<SuggestionInfo> getSuggestion(@Validated SuggestionReq req) {
        return aiMessageService.getSuggestion(req.getUserId());
    }

    /**
     * 根据客户userId查询指定数量的推荐话术
     * @param req 客户userId+生成数量
     * @return 推荐话术列表
     */
    @ApiOperation(value = "根据客户userId查询指定数量的推荐话术")
    @PostMapping("/getBatchSuggestion")
    public R<SuggestionListRes> getBatchSuggestion(@Validated SuggestionBatchReq req) {
        return aiMessageService.getBatchSuggestion(req.getUserId(), req.getCount());
    }
}

