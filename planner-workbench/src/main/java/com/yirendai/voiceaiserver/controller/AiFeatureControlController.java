/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.controller;

import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yirendai.voiceaiserver.constant.AiConstant;
import com.yirendai.workbench.entity.AiFeatureControl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.bind.annotation.*;
import com.yirendai.voiceaiserver.service.IAiFeatureControlService;

/**
 * AI功能控制表 控制器
 * <AUTHOR>
 * @since 2024-11-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai-feature-control")
@Api(value = "AI功能控制表", tags = "AI功能控制表接口")
public class AiFeatureControlController {

    private final IAiFeatureControlService aiFeatureControlService;

    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "查询AI权限", notes = "传入tenantId")
    public R<List<AiFeatureControl>> list(@RequestParam("tenantId") String tenantId) {
        String adminTenantID = AuthUtil.getTenantId();
        if (StringUtil.isBlank(adminTenantID) || !AiConstant.ADMIN_TENANT_ID.equals(adminTenantID)) {
            return R.fail("用户租户ID必须为000000");
        }
        // 查询数据
        List<AiFeatureControl> aiFeatureControls = aiFeatureControlService.listByTenantId(tenantId);

        return R.data(aiFeatureControls);

    }
    /**
     * 根据当前登录租户查询AI权限列表
     *
     * @return 查询到的 AI 权限列表
     */
    @GetMapping("/currentTenantPermissions")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "查询当前登录租户的AI权限列表", notes = "无需传入参数")
    public R<List<AiFeatureControl>> getCurrentTenantAiPermissions() {
        String tenantID = AuthUtil.getTenantId();
        if (StringUtil.isBlank(tenantID)) {
            return R.fail("未获取到当前登录租户ID");
        }
        // 查询数据
        List<AiFeatureControl> aiFeatureControls = aiFeatureControlService.listByTenantId(tenantID);

        return R.data(aiFeatureControls);
    }
    /**
     * 修改 AI功能控制表
     */
    @PostMapping("/updateStatus")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "修改状态", notes = "传入aiFeatureControl")
    public R updateStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status) {
        String tenantID = AuthUtil.getTenantId();
        Long userId = AuthUtil.getUserId();
        if (StringUtil.isBlank(tenantID) || !AiConstant.ADMIN_TENANT_ID.equals(tenantID)) {
            return R.fail("用户租户ID必须为000000");
        }
        boolean success = aiFeatureControlService.updateStatusById(id, status,userId);
        if (success) {
            return R.success("状态更新成功");
        } else {
            return R.fail("状态更新失败");
        }
    }

    /**
     * 修改 AI功能控制表
     */
    @PostMapping("/updateBatchStatus")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "批量修改状态", notes = "list 只需要带 id 和status")
    public R updateStatus(@RequestBody List<AiFeatureControl> list) {
        String tenantID = AuthUtil.getTenantId();
        Long userId = AuthUtil.getUserId();
        if (StringUtil.isBlank(tenantID) || !AiConstant.ADMIN_TENANT_ID.equals(tenantID)) {
            return R.fail("用户租户ID必须为000000");
        }
        if(CollectionUtils.isEmpty(list)){
            return R.fail("参数不能为空");
        }
        boolean success = aiFeatureControlService.batchUpdateStatus(list,userId);
        if (success) {
            return R.success("状态更新成功");
        } else {
            return R.fail("状态更新失败");
        }
    }

}
