package com.yirendai.voiceaiserver.controller;

import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.protobuf.ServiceException;
import com.yirendai.voiceaiserver.biz.asr.*;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.service.CallCenterService;
import com.yirendai.voiceaiserver.vo.response.AsrOnlineVO;
import com.yirendai.workbench.entity.AiPlannerChatContactSub;
import com.yirendai.workbench.wrapper.YxhApiWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Slf4j
@RefreshScope
@RestController
@Api(tags = "呼叫中心")
@RequestMapping("/voice/ai/server/call/center")
public class CallCenterController {

    @Resource
    CallCenterService callCenterService;

    @Autowired
    private AsrOnlineBiz acrOnlineBiz;

    @Resource
    private VadProcessFactory vadProcessFactory;

    @Resource
    private VadProcessFactory2 vadProcessFactory2;

    @Resource
    private MP3VadProcessFactory mp3VadProcessFactory;

    @Autowired
    @Qualifier("asrOnlineThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Value("${yr_asr.open.model}")
    private int audioOpenModel;

    @Resource
    private YxhApiWrapper yxhApiWrapper;


    @ApiOperation(value = "新增通话记录")
    @PostMapping("/addRecord")
    public R<Object> addRecord(@ApiParam(value = "通话uuid", required = true) @RequestParam("uuid") String uuid,
                               @ApiParam(value = "响铃时长（秒）", required = true) @RequestParam("ringTime") Long ringTime) {
        if (StrUtil.isBlank(uuid) || Objects.isNull(ringTime)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        try {
            callCenterService.addRecord(uuid, ringTime);
        } catch (Exception e) {
            log.error("新增通话记录uuid={}发生异常，异常原因为", uuid, e);
        }
        return R.data(null);
    }

    @PostMapping(value = "/asrOnline", produces = {MediaType.TEXT_EVENT_STREAM_VALUE} )
    @CrossOrigin("*")
    @ApiOperation(value = "通话实时音转文", notes = "通话实时音转文")
    public SseEmitter asrOline(@ApiParam(value = "通话uuid", required = true) @RequestParam("uuid") String uuid,
            @ApiParam(value = "响铃时长（秒）", required = true) @RequestParam("ringTime") Long ringTime) throws ServiceException {
        if (StrUtil.isBlank(uuid) || Objects.isNull(ringTime)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        SseEmitter sseEmitter = new SseEmitter(60 * 1000 * 60 * 6L);
        if (audioOpenModel == 0){
            VadAsrProcessor vadAsrProcessor = vadProcessFactory.createMp3VadAsrProcessor(uuid);
            threadPoolTaskExecutor.execute(() -> vadAsrProcessor.startMonitoring(sseEmitter, uuid, ringTime));
        } else if (audioOpenModel == 1) {
            Mp3VadAsrProcessor mp3VadAsrProcessor = mp3VadProcessFactory.createMp3VadAsrProcessor();
            threadPoolTaskExecutor.execute(() -> mp3VadAsrProcessor.startMonitoring(sseEmitter, uuid, ringTime));
        } else if (audioOpenModel == 2) {
            VadAsrProcessor2 vadAsrProcessor2 = vadProcessFactory2.createMp3VadAsrProcessor(uuid);
            threadPoolTaskExecutor.execute(() -> vadAsrProcessor2.startMonitoring(sseEmitter, uuid, ringTime));
        }
        return sseEmitter;
    }

    @GetMapping(value = "/asr/white")
    @ApiOperation(value = "asr判断agent是否是白名单", notes = "asr判断agent是否是白名单")
    public R<Boolean> getAudioText(@RequestParam(name="agentId") String agentId){
        return R.data(yxhApiWrapper.isCallOutAsrWhiteList(agentId));
    }

    @PostMapping(value = "/asr/sendAudio")
    @ApiOperation(value = "asr接收音频", notes = "asr接收音频")
    public void asrOline(@ApiParam(value = "通话uuid", required = true) @RequestParam("uuid") String uuid,
                               @ApiParam(value = "声音类型，0-左声道，1-右声道", required = true) @RequestParam("channelType") int channelType,
                               @ApiParam(value = "音频数据", required = true) @RequestPart("pcmData") byte[] pcmData){
        vadProcessFactory.processPcmAudio(uuid, channelType, pcmData);
    }



    @GetMapping( "/asrOnlinelist" )
    @ApiOperation(value = "通话记录列表", notes = "通话记录列表")
    public R<IPage<AiPlannerChatContactSub>> asrOnlinelist(AiPlannerChatContactSub contactSub, Query query)  {

        return R.data(acrOnlineBiz.asrOnlinelist(contactSub,query));
    }


    @ApiOperation(value = "通话挂断通知")
    @PostMapping("/finishNotify")
    public R<Object> finishNotify(@ApiParam(value = "通话uuid", required = true) @RequestParam("uuid") String uuid) {
        if (StrUtil.isBlank(uuid)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        callCenterService.finishNotify(uuid);
        return R.data(null);
    }

    @ApiOperation(value = "对比切割处理结果")
    @PostMapping("/compareSplit")
    public R<Object> compareSplit(@ApiParam(value = "文件（仅支持mp3格式）", required = true) @RequestPart("file") MultipartFile file,
                                  @ApiParam(value = "呼叫方向", required = true) @RequestParam("direction") Integer direction) {
        if (Objects.isNull(file) || Objects.isNull(direction)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        callCenterService.compareSplit(file, direction);
        return R.data(null);
    }
}
