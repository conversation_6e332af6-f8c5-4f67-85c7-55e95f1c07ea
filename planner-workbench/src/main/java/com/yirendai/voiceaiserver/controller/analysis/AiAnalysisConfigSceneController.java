/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright customer,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  customer, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.controller.analysis;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.service.IAiAnalysisConfigSceneService;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneVO;
import com.yirendai.workbench.entity.AiAnalysisConfigScene;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;

import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ai分析场景配置 控制器
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/analysis/config/scene")
@Api(value = "ai分析场景配置", tags = "ai分析场景配置接口")
public class AiAnalysisConfigSceneController   {

	private IAiAnalysisConfigSceneService aiAnalysisConfigSceneService;

	/**
	 * 根据ID查看详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ID查看详情", notes = "传入id ")
	public R<AiAnalysisConfigScene> detail(AiAnalysisConfigScene aiAnalysisConfigScene) {
		if(aiAnalysisConfigScene.getId() == null){
			return R.fail("参数ID不能为空");
		}
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		AiAnalysisConfigScene detail = aiAnalysisConfigSceneService.getByIdAndUser(aiAnalysisConfigScene.getId(),userId);
		return R.data(detail);
	}

	/**
	 * 查询场景列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询场景列表(仅场景列表)", notes = "查询场景列表")
	public R<List<AiAnalysisConfigScene>> list( ) {
		String tenantId = AuthUtil.getTenantId();
		if(tenantId == null){
			return R.fail("租户ID没有获取到");
		}
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		List<AiAnalysisConfigScene> list = aiAnalysisConfigSceneService.listByUserId(tenantId,userId);
		return R.data(list);
	}

	/**
	 * 查询场景列表
	 */
	@GetMapping("/listAll")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询场景列表(带结果选项)", notes = "查询场景列表")
	public R<List<AiAnalysisConfigSceneVO>> listAll( ) {
		String tenantId = AuthUtil.getTenantId();
		if(tenantId == null){
			return R.fail("租户ID没有获取到");
		}
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		List<AiAnalysisConfigSceneVO> list = aiAnalysisConfigSceneService.listAll(tenantId,userId);
		return R.data(list);
	}


	/**
	 * 新增 ai分析场景配置
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入aiAnalysisConfigScene")
	public R save(@Valid @RequestBody AiAnalysisConfigScene aiAnalysisConfigScene) {
		String tenantId = AuthUtil.getTenantId();
		if(tenantId == null){
			return R.fail("租户ID没有获取到");
		}
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		if( aiAnalysisConfigScene.getSceneName() == null){
			return R.fail("场景名称不能为空");
		}
		if( aiAnalysisConfigScene.getPrompt() == null){
			return R.fail("场景描述不能为空");
		}
		aiAnalysisConfigScene.setTenantId(tenantId);
		aiAnalysisConfigScene.setIsDeleted(0);
		aiAnalysisConfigScene.setCreateUser(userId);
		aiAnalysisConfigScene.setUpdateUser(userId);
		aiAnalysisConfigScene.setCreateTime(LocalDateTime.now());
		aiAnalysisConfigScene.setUpdateTime(LocalDateTime.now());
		//判断名称是否重复
		int count = aiAnalysisConfigSceneService.selectCountBySceneName(aiAnalysisConfigScene.getSceneName(), tenantId, userId,null);
		if (count > 0) {
			return R.fail("场景已存在，不可重复添加");
		}
		return R.status(aiAnalysisConfigSceneService.save(aiAnalysisConfigScene));
	}

	/**
	 * 修改 ai分析场景配置
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入aiAnalysisConfigScene")
	public R update(@Valid @RequestBody AiAnalysisConfigScene aiAnalysisConfigScene) {
		String tenantId = AuthUtil.getTenantId();
		if(tenantId == null){
			return R.fail("租户ID没有获取到");
		}
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		if( aiAnalysisConfigScene.getSceneName() == null){
			return R.fail("场景名称不能为空");
		}
		if( aiAnalysisConfigScene.getPrompt() == null){
			return R.fail("场景描述不能为空");
		}
		if(aiAnalysisConfigScene.getId() == null){
			return R.fail("参数ID不能为空");
		}

		AiAnalysisConfigScene detail = aiAnalysisConfigSceneService.getByIdAndUser(aiAnalysisConfigScene.getId(),userId);
		if(detail == null){
			return R.fail("数据不存在");
		}
		aiAnalysisConfigScene.setUpdateTime(LocalDateTime.now());
		aiAnalysisConfigScene.setUpdateUser(userId);
		//判断名称是否重复
		int count = aiAnalysisConfigSceneService.selectCountBySceneName(aiAnalysisConfigScene.getSceneName(), tenantId, userId,aiAnalysisConfigScene.getId());
		if (count > 0) {
			return R.fail("场景已存在，不可重复添加");
		}
		return R.status(aiAnalysisConfigSceneService.updateById(aiAnalysisConfigScene));
	}
	/**
	 * 删除 ai分析场景配置
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(aiAnalysisConfigSceneService.removeByIds(Func.toLongList(ids)));
	}

	
}
