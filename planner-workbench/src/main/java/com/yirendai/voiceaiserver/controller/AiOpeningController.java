package com.yirendai.voiceaiserver.controller;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.enums.BehaviorTypeEnum;
import com.yirendai.voiceaiserver.enums.OpeningTypeEnum;
import com.yirendai.voiceaiserver.service.AiOpeningService;
import com.yirendai.voiceaiserver.vo.db.AiOpeningBehaviorVO;
import com.yirendai.voiceaiserver.vo.request.BasePageReq;
import com.yirendai.voiceaiserver.vo.request.OpeningBehaviorReq;
import com.yirendai.voiceaiserver.vo.request.OpeningRandomReq;
import com.yirendai.voiceaiserver.vo.request.OpeningSearchReq;
import com.yirendai.voiceaiserver.vo.response.OpeningSearchRes;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * ai开场白 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-24
 */
@RestController
@RequestMapping("/voice/ai/server/opening")
@Api(tags = "开场白")
public class AiOpeningController {

    @Resource
    AiOpeningService aiOpeningService;

    /**
     * 随机获取指定数量的推荐话题
     * @param size 数量
     * @return 推荐话题随机列表
     */
    @ApiOperation(value = "随机获取指定数量的推荐话题", notes = "不需要登录")
    @PostMapping("/getRandomTopic/{size}")
    public R<OpeningSearchRes> getRandomTopic(
            @ApiParam(value = "数量", required = true) @PathVariable("size") Integer size) {
        if (size < 1) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return aiOpeningService.getRandomTopic(size);
    }

    /**
     * 随机获取指定数量的开场白(最大20)
     * @param request 数量 + 用户账号
     * @return 开场白随机列表
     */
    @ApiOperation(value = "随机获取指定数量的开场白(最大20)")
    @PostMapping("/getRandomOpening")
    public R<OpeningSearchRes> getRandomOpening(@Validated OpeningRandomReq request) {
        return aiOpeningService.getRandomOpening(request.getSize(), request.getAccount());
    }

    /**
     * 搜索开场白
     * @param request 检索内容
     * @return 搜索结果
     */
    @ApiOperation(value = "搜索开场白")
    @PostMapping("/search")
    public R<OpeningSearchRes> search(@Validated OpeningSearchReq request) {
        if (Objects.nonNull(request.getSize()) && request.getSize().compareTo(20) > 0) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        if (Objects.isNull(request.getSize())) {
            request.setSize(10);
        }
        return aiOpeningService.search(request);
    }

    /**
     * 点赞、踩、收藏、取消收藏
     * @param request 账号、关键字列表、开场白、用户行为
     * @return 处理结果
     */
    @ApiOperation(value = "点赞、踩、收藏、取消收藏开场白")
    @PostMapping("/behavior")
    public R<Object> behavior(@Validated OpeningBehaviorReq request) {
        if (!OpeningTypeEnum.DB.getCode().equals(request.getOpeningType())
                && !OpeningTypeEnum.AI_GENERATE.getCode().equals(request.getOpeningType())) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        if (!BehaviorTypeEnum.getAll().contains(request.getBehavior())) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return aiOpeningService.behavior(request);
    }

    /**
     * 分页查询收藏记录
     * @param request 分页信息
     * @return 查询结果
     */
    @ApiOperation(value = "分页查询收藏记录")
    @PostMapping("/getPage")
    public R<IPage<AiOpeningBehaviorVO>> getPage(@Validated BasePageReq request) {
        if (Objects.isNull(request.getSize())) {
            request.setSize(10);
        }
        if (Objects.isNull(request.getCurrent())) {
            request.setCurrent(1);
        }
        return aiOpeningService.getPage(request);
    }
}

