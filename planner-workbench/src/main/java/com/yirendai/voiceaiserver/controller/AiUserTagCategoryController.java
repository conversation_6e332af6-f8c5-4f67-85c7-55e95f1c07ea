/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.controller;

import java.io.IOException;
import java.util.List;

import com.github.benmanes.caffeine.cache.Cache;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import com.yirendai.voiceaiserver.vo.response.AiUserTagCategoryVO;
import com.yirendai.workbench.entity.AiUserTagCategory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.service.IAiUserTagCategoryService;
import org.springframework.web.multipart.MultipartFile;

/**
 * ai用户标签分类 控制器
 * <AUTHOR>
 * @since 2024-11-13
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/ai/user/tag/category")
@Api(value = "ai用户标签分类", tags = "ai用户标签分类接口")
public class AiUserTagCategoryController {

    private final IAiUserTagCategoryService aiUserTagCategoryService;
    @Resource
    private Cache<String, List<AiTagCategoryWithTags>> aiTagCategoryCache;

    /**
     * 标签分类详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入aiUserTagCategory")
    public R<AiUserTagCategory> detail(AiUserTagCategory aiUserTagCategory) {
        AiUserTagCategory detail = aiUserTagCategoryService.getOne(Condition.getQueryWrapper(aiUserTagCategory));
        return R.data(detail);
    }

    /**
     * 标签分类列表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "标签分类列表（递归）", notes = "传入aiUserTagCategory")
    public R<List<AiUserTagCategoryVO>> list(@RequestParam(name ="categoryName", required = false) String categoryName, @RequestParam(name ="userType", required = false) Integer userType) {
        List<AiUserTagCategoryVO> list = aiUserTagCategoryService.categoryVOList(categoryName, userType);
        return R.data(list);
    }

    /**
     * 新增 ai用户标签分类
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增标签分类", notes = "传入aiUserTagCategory")
    public R save(@Valid @RequestBody AiUserTagCategory aiUserTagCategory) {
        return R.status(aiUserTagCategoryService.save(aiUserTagCategory));
    }

    /**
     * 修改 ai用户标签分类
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改标签分类", notes = "传入aiUserTagCategory")
    public R update(@Valid @RequestBody AiUserTagCategory aiUserTagCategory) {
        return R.status(aiUserTagCategoryService.updateById(aiUserTagCategory));
    }

    /**
     * 删除 ai用户标签分类
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "删除标签分类", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(aiUserTagCategoryService.removeByIds(Func.toLongList(ids)));
    }

    /**
     * 以JSON格式批量插入标签和分类
     * @return String
     */
    @PostMapping("/insertTagsAndCategories")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "以JSON格式批量插入标签和分类", notes = "传入JSON格式数据")
    public String insertTagsAndCategories(@RequestBody String jsonData) {
        try {
            aiUserTagCategoryService.insertTagsAndCategoriesByJSON(jsonData);
            return "批量插入标签和分类成功！！！";
        } catch (Exception e) {
            log.error("数据插入失败：", e);
            return "数据插入失败！！！";
        }
    }

    /**
     * 以excel格式批量插入标签和分类
     * @return String
     */
    @PostMapping("/batchImport")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "以文件格式批量插入标签和分类", notes = "传入excel格式数据")
    public R<Boolean> insertTagsAndCategories(@RequestParam("file") MultipartFile file, @RequestParam("tenantId") String tenantId) {
        String contentType = file.getContentType();
        try {
            if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType)) {
                Boolean b = aiUserTagCategoryService.parseExcelFile(file.getInputStream(), tenantId);
                if (b) {
                    aiTagCategoryCache.invalidateAll();
                }
                return R.data(b);
            } else {
                throw new AiServerException(ResultCode.PARAM_INVALID);
            }
        } catch (IOException e) {
            throw new AiServerException(ResultCode.FILE_PARSE_ERROR);
        }
    }

}
