/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright customer,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  customer, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.controller.analysis;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.enums.AiAnalysisTaskSourceEnum;
import com.yirendai.voiceaiserver.service.IAiAnalysisTaskCallRecordService;
import com.yirendai.voiceaiserver.vo.request.CallRecordAnalysisReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskCallRecordPageVO;
import com.yirendai.workbench.util.DataScopeUtil;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.analysis.AiAnalysisTaskCallRecordReq;
import com.yirendai.workbench.vo.res.callcenter.BatchOperationResult;
import com.yirendai.workbench.wrapper.dto.CallCenterTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * ai分析任务通话记录 控制器
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/analysis/task/callrecord")
@Api(value = "ai分析任务通话记录", tags = "ai分析任务通话记录接口")
@Slf4j
public class AiAnalysisTaskCallRecordController {

    private IAiAnalysisTaskCallRecordService aiAnalysisTaskCallRecordService;

    @Autowired
    private DataScopeUtil dataScopeUtil;


    /**
     * 分页查询通话记录分析结果
     */
    @PostMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页查询通话记录分析结果", notes = "传入aiAnalysisTaskCallRecord")
    public R<AiAnalysisTaskCallRecordPageVO> list(@RequestBody AiAnalysisTaskCallRecordReq req) {
        AiAnalysisTaskCallRecordPageVO result = aiAnalysisTaskCallRecordService.pageList(Condition.getPage(req), req);
        return R.data(result);
    }

    @PostMapping("/exportBatch")
    @ApiOperation("批量导出通话记录分析结果")
    public R<BatchOperationResult> exportBatch(@RequestBody AiAnalysisTaskCallRecordReq req) {
        log.info("exportBatch:{}, operator:{}", JsonUtilExt.beanToJson(req), OwnAuthUtil.getAgentFullAccount());
        CallCenterTask taskInfo = aiAnalysisTaskCallRecordService.exportBatch(req);
        return R.data(new BatchOperationResult(true, taskInfo.getId(), taskInfo.getTaskName()));
    }


    @ApiOperation(value = "呼叫记录发起AI分析", tags = "呼叫记录查询")
    @PostMapping("/analysis")
    public R<String> analysis(@RequestBody CallRecordAnalysisReq request) {
        List<String> plannerNoList = dataScopeUtil.getPlannerNoByDeptId(request.getDeptId());
        if (CollectionUtils.isEmpty(plannerNoList)) {
            log.info("无部门权限 req= {}", request);
            return R.fail("无部门权限");
        }
        request.setPlannerNoList(plannerNoList);
        Long userId = OwnAuthUtil.getUserId();
        String userName = OwnAuthUtil.getRealName();
        String tenantId = OwnAuthUtil.getTenantId();
//        userId = 1854769895833624578L;
//        userName = "曹森";
//        tenantId = "278576";
        
        aiAnalysisTaskCallRecordService.analysis(request, userId, userName, tenantId);
        return R.success("成功");
    }

    /**
     * 新增任务模版.xslx
     * @param analysisType 分析类型：0-录音ID分析，1-客户ID分析
     */
    @GetMapping("/downTemplate")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "下载新增任务模版.xlsx", notes = "analysisType: 0-录音ID, 1-客户ID")
    public void downTemplate(HttpServletResponse response, HttpServletRequest request,
                           @ApiParam(value = "分析类型 0-录音ID 1-客户ID", required = false) @RequestParam(required = false, defaultValue = "0") Integer analysisType) {
        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        
        // 根据分析类型创建不同的sheet和表头
        String sheetName;
        String headerName;
        String fileName;
        
        if (analysisType != null && analysisType == 1) {
            // 客户ID分析
            sheetName = "客户ID";
            headerName = "客户ID";
            fileName = "客户分析任务模版.xlsx";
        } else {
            // 录音ID分析（默认）
            sheetName = "录音ID";
            headerName = "录音ID";
            fileName = "新增任务模版.xlsx";
        }
        
        Sheet sheet = workbook.createSheet(sheetName);
        
        // 创建单元格样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置列宽
        // 列宽设置为30个字符宽度
        sheet.setColumnWidth(0, 30 * 256);
        
        // 写入数据
        Row row = sheet.createRow(0);
        int colNum = 0;
        Cell cell1 = row.createCell(colNum++);
        cell1.setCellValue(headerName);
        cell1.setCellStyle(headerStyle);
        
        // 将工作簿写入响应输出流
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String encodedFileName = "UTF-8''" + URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*= " + encodedFileName);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            log.error("下载模板异常", e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                log.info("关闭工作簿异常", e);
            }
        }
    }

    /**
     * 导入文件发起AI分析
     *
     * @param file 上传的Excel文件
     * @param sceneIds 场景ID集合
     * @param taskName 任务名称
     * @param analysisType 分析类型：0-录音ID分析，1-客户ID分析
     * @param analysisContent 分析内容（客户ID分析时必填）：1通话记录，2短信记录，3微信记录，4企微记录，5客户基本信息，6客户业务数据
     * @param startTime 开始时间（客户ID分析时可选，用于筛选时间范围内的数据）
     * @param endTime 结束时间（客户ID分析时可选，用于筛选时间范围内的数据）
     * @return
     */
    @PostMapping("/importAnalysis")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "导入文件发起AI分析", notes = "支持录音ID和客户ID两种分析类型")
    public R<String> importAnalysis(
            @RequestParam("file") MultipartFile file, 
            @ApiParam(value = "场景ID集合", required = true) @RequestParam List<Long> sceneIds, 
            @ApiParam(value = "任务名称", required = true) @RequestParam String taskName,
            @ApiParam(value = "分析类型 0-录音ID 1-客户ID", required = false) @RequestParam(required = false, defaultValue = "0") Integer analysisType,
            @ApiParam(value = "分析内容（客户ID分析时必填），多个用逗号分隔：1通话记录，2短信记录，3微信记录，4企微记录，5客户基本信息，6客户业务数据", required = false) @RequestParam(required = false) String analysisContent,
            @ApiParam(value = "开始时间（客户ID分析时可选，格式：yyyy-MM-dd HH:mm:ss）", required = false) @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间（客户ID分析时可选，格式：yyyy-MM-dd HH:mm:ss）", required = false) @RequestParam(required = false) String endTime) {
        
        Long userId = OwnAuthUtil.getUserId();
        String userName = OwnAuthUtil.getRealName();
        String tenantId = OwnAuthUtil.getTenantId();
        
        CallRecordAnalysisReq request = new CallRecordAnalysisReq();
        request.setSceneIds(sceneIds);
        request.setTaskName(taskName);
        request.setAnalysisType(analysisType);
        request.setSourcePage(AiAnalysisTaskSourceEnum.CONTENT_ANALYSIS_TASK.getCode());
        
        // 如果是客户ID分析，设置分析内容和时间范围
        if (analysisType != null && analysisType == 1) {
            if (analysisContent == null || analysisContent.trim().isEmpty()) {
                return R.fail("客户ID分析必须选择至少一种分析内容");
            }
            request.setAnalysisContent(analysisContent);
            
            // 设置时间范围（如果提供）
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (startTime != null && !startTime.trim().isEmpty()) {
                try {
                    request.setStartTime(LocalDateTime.parse(startTime, formatter));
                } catch (Exception e) {
                    log.error("开始时间格式错误: {}", startTime, e);
                    return R.fail("开始时间格式错误，请使用格式：yyyy-MM-dd HH:mm:ss");
                }
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    request.setEndTime(LocalDateTime.parse(endTime, formatter));
                } catch (Exception e) {
                    log.error("结束时间格式错误: {}", endTime, e);
                    return R.fail("结束时间格式错误，请使用格式：yyyy-MM-dd HH:mm:ss");
                }
            }
        }
        
        try {
            R<String> r = aiAnalysisTaskCallRecordService.analysis(file, request, userId, userName, tenantId);
            return r;
        } catch (IOException e) {
            log.error("导入文件异常", e);
            return R.fail("导入文件异常");
        }
    }
}
