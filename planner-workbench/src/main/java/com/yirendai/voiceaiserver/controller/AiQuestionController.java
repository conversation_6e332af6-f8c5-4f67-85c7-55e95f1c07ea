package com.yirendai.voiceaiserver.controller;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.enums.BehaviorTypeEnum;
import com.yirendai.voiceaiserver.enums.QuestionSourceEnum;
import com.yirendai.voiceaiserver.enums.QuestionTypeEnum;
import com.yirendai.voiceaiserver.service.AiQuestionService;
import com.yirendai.voiceaiserver.vo.response.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yirendai.voiceaiserver.vo.request.BasePageReq;
import com.yirendai.voiceaiserver.vo.request.QuestionBehaviorReq;
import com.yirendai.voiceaiserver.vo.request.QuestionSearchReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * ai问题 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@RestController
@RequestMapping("/voice/ai/server/question")
@Api(tags = "问题")
public class AiQuestionController {

    @Resource
    AiQuestionService aiQuestionService;

    /**
     * 查询全部推荐话题或指定推荐话题下指定数量的相关随机推荐
     * @param size 数量
     * @param parentId 推荐话题id
     * @param type 类型 1：推荐话题 2：相关推荐
     * @return 推荐话题/相关推荐列表
     */
    @ApiOperation(value = "查询全部推荐话题或指定推荐话题下指定数量的相关随机推荐", notes = "不需要登录")
    @PostMapping("/getTopicOrRecommendation")
    public R<QuestionSearchRes> getTopicOrRecommendation(
            @ApiParam(value = "数量，type = 2时必传") @RequestParam(value = "size", required = false) Integer size,
            @ApiParam(value = "推荐话题id，type = 2时必传") @RequestParam(value = "parentId", required = false) Long parentId,
            @ApiParam(value = "类型 1：推荐话题 2：相关推荐", required = true) @RequestParam("type") Integer type) {
        if (!QuestionTypeEnum.TOPIC.getCode().equals(type) && !QuestionTypeEnum.RECOMMENDATION.getCode().equals(type)) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        if (QuestionTypeEnum.RECOMMENDATION.getCode().equals(type)
                && (Objects.isNull(size) || size.compareTo(1) < 0 || Objects.isNull(parentId))) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return aiQuestionService.getTopicOrRecommendation(size, parentId, type);
    }

    @ApiOperation(value = "查询今日问题", notes = "不需要登录")
    @PostMapping("/getToday")
    public R<QuestionAndAnswerListRes> getToday() {
        return aiQuestionService.getToday();
    }

    @ApiOperation(value = "搜索指定数量的关联总结问题")
    @PostMapping("/getSummaryList")
    public R<QuestionListRes> getSummaryList(
            @ApiParam(value = "数量", required = true) @RequestParam(value = "size") Integer size,
            @ApiParam(value = "搜索内容", required = true) @RequestParam("content") String content) {
        if (StringUtils.isBlank(content) || size.compareTo(1) < 0) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return aiQuestionService.getSummaryList(size, content);
    }

    @ApiOperation(value = "搜索话术获取指定数量的问题")
    @PostMapping("/search")
    public R<QuestionAndAnswerListRes> search(@Validated QuestionSearchReq request) {
        if (!QuestionSourceEnum.getAll().contains(request.getSource())) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return aiQuestionService.search(request);
    }

    @ApiOperation(value = "根据问题获取一个答案")
    @PostMapping("/answer")
    public R<QuestionAndAnswerInfo> answer(
            @ApiParam(value = "问题内容", required = true) @RequestParam(value = "question") String question,
            @ApiParam(value = "关联内容id") @RequestParam(value = "relationId", required = false) Long relationId,
            @ApiParam(value = "登录账号", required = true) @RequestParam(value = "account") String account) {
        return aiQuestionService.answer(question, relationId, account);
    }

    @ApiOperation(value = "点赞、踩、收藏、取消收藏问题&答案")
    @PostMapping("/behavior")
    public R<Object> behavior(@Validated QuestionBehaviorReq request) {
        if (!BehaviorTypeEnum.getAll().contains(request.getBehavior())) {
            return R.fail(ResultCode.PARAM_INVALID);
        }
        return aiQuestionService.behavior(request);
    }

    @ApiOperation(value = "分页查询收藏记录")
    @PostMapping("/getPage")
    public R<IPage<AiQuestionBehaviorStarVO>> getPage(@Validated BasePageReq request) {
        if (Objects.isNull(request.getSize())) {
            request.setSize(10);
        }
        if (Objects.isNull(request.getCurrent())) {
            request.setCurrent(1);
        }
        return aiQuestionService.getPage(request);
    }
}

