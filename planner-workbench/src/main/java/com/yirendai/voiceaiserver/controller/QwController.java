package com.yirendai.voiceaiserver.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiFeatureControlEnum;
import com.yirendai.voiceaiserver.service.IAiFeatureControlService;
import com.yirendai.voiceaiserver.service.QwService;
import com.yirendai.voiceaiserver.vo.request.*;
import com.yirendai.voiceaiserver.vo.response.*;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.AiPlannerTopUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@Api(tags = "企微")
@RequestMapping("/voice/ai/server/qw")
public class QwController {

    @Resource
    QwService qwService;

    @Resource
    IAiFeatureControlService iAiFeatureControlService;

    @ApiOperation(value = "查询用户信息（含企微信息）")
    @PostMapping("/getUserInfo")
    public R<CustomerInfoRes> getUserInfo(
            @ApiParam(value = "客户id", required = true) @RequestParam("userId") String userId,
            @ApiParam(value = "功能位置", required = true) @RequestParam("position") String position) {
        if (StrUtil.isBlank(userId) || StrUtil.isBlank(position)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        String tenantId = AuthUtil.getTenantId();
        if (StrUtil.isBlank(tenantId)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (checkNoPermission(position, tenantId)) {
            throw new AiServerException(ResultCode.NO_PERMISSION);
        }
        return R.data(qwService.getUserInfo(userId, tenantId));
    }

    @ApiOperation(value = "查询指定月份中存在聊天记录的日期")
    @PostMapping("/getExistDateList")
    public R<QwExistDateRes> getExistDateList(
            @ApiParam(value = "客户id", required = true) @RequestParam("userId") String userId,
            @ApiParam(value = "理财师id", required = true) @RequestParam("plannerNo") String plannerNo,
            @ApiParam(value = "月份，格式为yyyy-MM，不传时查询存在聊天记录的最新一个月") @RequestParam(value = "month", required = false) String month,
            @ApiParam(value = "功能位置", required = true) @RequestParam("position") String position) {
        if (StrUtil.isBlank(userId) || StrUtil.isBlank(plannerNo) || StrUtil.isBlank(position)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        String tenantId = AuthUtil.getTenantId();
        if (StrUtil.isBlank(tenantId)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (checkNoPermission(position, tenantId)) {
            throw new AiServerException(ResultCode.NO_PERMISSION);
        }
        YearMonth yearMonth = null;
        if (StrUtil.isNotBlank(month)) {
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_MONTH_PATTERN);
            yearMonth = YearMonth.parse(month, dateFormatter);
        }
        return R.data(qwService.getExistDateList(userId, plannerNo, tenantId, yearMonth));
    }

    @ApiOperation(value = "分页查询企微聊天记录和企微小结")
    @PostMapping("/getPageMsgAndSummary")
    public R<Page<QwMsgAndSummaryVO>> getPageMsgAndSummary(@Validated QwMsgSearchReq req) {
        if (StrUtil.isBlank(req.getStartTime()) && StrUtil.isBlank(req.getEndTime())
                || StrUtil.isNotBlank(req.getStartTime()) && StrUtil.isNotBlank(req.getEndTime())) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        String tenantId = AuthUtil.getTenantId();
        if (StrUtil.isBlank(tenantId)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (checkNoPermission(req.getPosition(), tenantId)) {
            throw new AiServerException(ResultCode.NO_PERMISSION);
        }
        if (Objects.isNull(req.getSize())) {
            req.setSize(10);
        }
        if (Objects.isNull(req.getCurrent()) || req.getCurrent() < 1) {
            req.setCurrent(1);
        }

        QwMsgAndSummaryReq request = new QwMsgAndSummaryReq();
        BeanUtil.copyProperties(req, request);
        request.setTenantId(tenantId);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (StrUtil.isNotBlank(req.getStartTime())) {
            request.setStartTime(LocalDateTime.parse(req.getStartTime(), formatter));
        }
        if (StrUtil.isNotBlank(req.getEndTime())) {
            request.setEndTime(LocalDateTime.parse(req.getEndTime(), formatter));
        }
        return R.data(qwService.getPageMsgAndSummary(request));
    }

    @ApiOperation(value = "查询指定大小的客户列表")
    @PostMapping("/getCustomerList")
    public R<List<AiPlannerTopUser>> getCustomerList(@Validated QwCusSearchReq req) {
        String tenantId = AuthUtil.getTenantId();
        if (StrUtil.isBlank(tenantId)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (checkNoPermission(req.getPosition(), tenantId)) {
            throw new AiServerException(ResultCode.NO_PERMISSION);
        }
        if (Objects.isNull(req.getSize())) {
            req.setSize(10);
        }

        QwCusReq request = new QwCusReq();
        BeanUtil.copyProperties(req, request);
        request.setTenantId(tenantId);
        return R.data(qwService.getCustomerList(request));
    }

    @ApiOperation(value = "分页查询与客户有关联的理财师列表")
    @PostMapping("/getPagePlannerWithCustomer")
    public R<Page<AiPlannerTopUser>> getPagePlannerWithCustomer(@Validated QwPlannerSearchReq req) {
        String tenantId = AuthUtil.getTenantId();
        if (StrUtil.isBlank(tenantId)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (checkNoPermission(req.getPosition(), tenantId)) {
            throw new AiServerException(ResultCode.NO_PERMISSION);
        }
        if (Objects.isNull(req.getSize())) {
            req.setSize(10);
        }
        if (Objects.isNull(req.getCurrent()) || req.getCurrent() < 1) {
            req.setCurrent(1);
        }

        QwPlannerReq request = new QwPlannerReq();
        BeanUtil.copyProperties(req, request);
        request.setTenantId(tenantId);
        return R.data(qwService.getPagePlannerWithCustomer(request));
    }

    private boolean checkNoPermission(String position, String tenantId) {
        AiFeatureControlEnum controlEnum = AiFeatureControlEnum.fromCode(position);
        return !iAiFeatureControlService.isFeatureEnabled(controlEnum, tenantId);
    }

    @ApiOperation(value = "分页查询已绑定企微的员工列表")
    @PostMapping("/pageBindPlanner")
    public R<Page<QwPlannerResp>> pageBindPlanner(@RequestBody @Validated NewQwBindPlannerPageReq pageReq) {
        return R.data(qwService.pageBindPlanner(pageReq));
    }

    @ApiOperation(value = "分页查询某一员工的企微好友列表")
    @PostMapping("/pagePlannerFriend")
    public R<Page<AiPlannerChatContact>> pagePlannerFriend(@RequestBody @Validated NewQwPlannerFriendPageReq pageReq) {
        return R.data(qwService.pagePlannerFriend(pageReq));
    }
}
