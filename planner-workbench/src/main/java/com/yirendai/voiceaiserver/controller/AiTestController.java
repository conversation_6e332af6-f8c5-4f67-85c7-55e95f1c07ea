package com.yirendai.voiceaiserver.controller;

import java.io.File;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import cn.hutool.core.util.RandomUtil;
import com.yirendai.voiceaiserver.biz.ContentCheckBiz;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.job.AiConversationProcessTaskJob;
import com.yirendai.voiceaiserver.model.contentcheck.MatchResult;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.mq.send.MqSendUtil;
import com.yirendai.voiceaiserver.mq.send.TopicEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.service.IAiContentService;
import com.yirendai.voiceaiserver.task.conversation.factory.AiConversationProcessFactory;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ZhiYuApiService;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.HitTestingResponse;
import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/voice/ai/test")
@Slf4j
@Api(tags = "测试类")
public class AiTestController {

    @Value("${nas.path}")
    private String path;
    @Resource
    private ContentCheckBiz contentCheckBiz;

    @Autowired
    private WebHookUtil webHookUtil;

    @Autowired
    private MqSendUtil mqSendUtil;

    @Resource
    private AIConversationProcessService aiConversationProcessService;

    @Resource
    private AiConversationProcessFactory aiConversationProcessFactory;

    @Resource
    private AiConversationProcessTaskJob aiConversationProcessTaskJob;
    @Autowired
    private ZhiYuApiService zhiYuApiService;


    @Autowired
    private IAiContentService   aiContentService;
    @PostMapping("/filterWord")
    @ApiOperation(value = "敏感词过滤")
    public R<List<MatchResult>> filterWord(@RequestParam("text") String text) {
        return R.data(contentCheckBiz.filterWord(text));
    }


    @PostMapping("/testWebhook")
    @ApiOperation(value = "testWebhook")
    public R<String> testWebhook( ){
        try{
            String a = null;
            a.equals(1);
        }catch (Exception ex){
            webHookUtil.weChatMsgDev(MsgTypeEnum.USER_TAG,"空指针异常","测试一个空指针异常通知",ex);
        }

        return R.data("");
    }
    @PostMapping("/testWebhook2")
    @ApiOperation(value = "testWebhook2")
    public R<String> testWebhook2( ){
        try{
            String a = null;
            a.equals(1);
        }catch (Exception ex){
            webHookUtil.weChatMsgDev(MsgTypeEnum.USER_TAG,"测试一个空指针异常通知");
        }

        return R.data("");
    }
    @PostMapping("/mqSendTest")
    @ApiOperation(value = "mqSendTest")
    public R<String> mqSendTest(){
        MqBaseDto dto = new MqBaseDto();
        dto.setChatContactId(200L);
        mqSendUtil.sendMessage(TopicEnum.SUMMARY,dto);
        return R.data("");
    }

    @PostMapping("/submitMQTasks")
    @ApiOperation(value = "submitMQTasks")
    public R<String> submitMQTasks(@RequestBody AiConversationProcessReq req){
        aiConversationProcessService.submitMQTasks(req);
        return R.data("");
    }

    @GetMapping("/submitTask")
    @ApiOperation(value = "submitTask")
    public R<String> submitTask(Long chatContactId, Integer type, String taskId){
        AiConversationProcessReq req = new AiConversationProcessReq();
        req.setChatContactId(chatContactId);
        req.setTaskId(taskId);
        req.setTaskType(AiConversationProcessTypeEnum.getByCode(type));
        aiConversationProcessService.submitTask(req);
        return R.data("");
    }


    @GetMapping("/retryTaskJobHandler")
    @ApiOperation(value = "retryTaskJobHandler")
    public R<String> retryTaskJobHandler(){
        aiConversationProcessTaskJob.retryTaskJobHandler();
        return R.data("");
    }

    @GetMapping("/checkLeakyDataTaskJobHandler")
    @ApiOperation(value = "checkLeakyDataTaskJobHandler")
    public R<String> checkLeakyDataTaskJobHandler(){
        aiConversationProcessTaskJob.checkLeakyDataTaskJobHandler();
        return R.data("");
    }

    @GetMapping("/retryWechatSummaryJobHandler")
    @ApiOperation(value = "retryWechatSummaryJobHandler")
    public R<String> retryWechatSummaryJobHandler(String date){
        aiConversationProcessTaskJob.aiConversationWechatSummaryInit(date);
        return R.data("");
    }

    @GetMapping("/processWechatSummaryJobHandler")
    @ApiOperation(value = "processWechatSummaryJobHandler")
    public R<String> processWechatSummaryJobHandler(){
        aiConversationProcessTaskJob.processWechatSummaryJobHandler();
        return R.data("");
    }

    @ApiOperation(value = "上传一个测试用的音频文件")
    @PostMapping("/testUpdate")
    public R<Object> testUpdate(
            @ApiParam(value = "录音文件", required = true) MultipartFile file) {
        if (  Objects.isNull(file)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        String fileName = file.getOriginalFilename();

        fileName =   "test_" + RandomUtil.randomString(10) + ".wav";
        File saveFile = FileUtil.saveUploadFile(file, path + "plannerVoice/test/" + fileName);
        return R.data(null);
    }
    @PostMapping("/hitTesting")
    @ApiOperation(value = "调用知识库命中测试接口")
    public R<HitTestingResponse> hitTesting(
            @ApiParam(value = "命中测试的查询字符串", required = true)
            @RequestParam("query") String query) {
        return zhiYuApiService.hitTesting(query);
    }

    @PostMapping("/syncContentNews")
    @ApiOperation(value = "同步新闻")
    public R<String> syncContentNews( @RequestParam("query") String query) {
        aiContentService.syncContentNews(query);
        return R.success("OK");
    }

    @PostMapping("/checkLeakyData")
    @ApiOperation(value = "检查失败ai对话任务查漏")
    public R<Integer> checkLeakyData(Integer processType, String tenantId) {
        return R.data(aiConversationProcessTaskJob.aiConversationProcessTaskLeakyData(processType, tenantId));
    }

    @PostMapping("/robotSummary")
    @ApiOperation(value = "机器人小结测试接口")
    public R<Boolean> robotSummary(Long sessionId, String tenantId) {
        aiConversationProcessService.submitTask(AiConversationProcessReq.builder().chatContactId(sessionId).taskType(AiConversationProcessTypeEnum.ROBOT_CALL_USER_SUMMARY).tenantId(tenantId).build());
        return R.data(true);
    }

    @PostMapping("/getWavDuration")
    @ApiOperation(value = "获取音频时长")
    public R<Integer> getWavDuration(String url) throws Exception {
        double wavDuration = FileUtil.getAudioDuration(url);
        int audioDuration = (int) (wavDuration * 1000);
        return R.data(audioDuration);
    }

    @PostMapping("/syncChatSummary")
    @ApiOperation(value = "同步微信聊天记录")
    public R<Boolean> syncChatSummary(String data) {
        aiConversationProcessTaskJob.syncWechatChatSummaryJobHandler(data);
        return R.data(true);
    }

    @PostMapping("/chatSummaryInit")
    @ApiOperation(value = "微信聊天记录初始化")
    public R<Boolean> chatSummaryInit(String data) {
        aiConversationProcessTaskJob.wechatHongKongInsuranceInitJobHandler(data);
        return R.data(true);
    }
}
