package com.yirendai.voiceaiserver.controller;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.service.TeleprompterService;
import com.yirendai.voiceaiserver.service.impl.AiVoiceToTextService;
import com.yirendai.voiceaiserver.vo.request.ChatMessagesStreamReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;

/**
 * <p>
 * ai提示词 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2024-04-28
 */
@RestController
@RequestMapping("/voice/ai/teleprompter")
@Api(tags = "提示词")
public class AiTeleprompterController {

    @Resource
    TeleprompterService teleprompterService;

    @Resource
    AiVoiceToTextService aiVoiceToTextService;

    /**
     * 根据输入内容检测提示词
     * @param account 登录账号
     * @param content 输入内容
     * @return 检测结果
     */
    @ApiOperation(value = "根据输入内容检测提示词")
    @PostMapping("/detect")
    public R<String> detect(@ApiParam(value = "登录账号", required = true) @RequestParam("account") String account,
            @ApiParam(value = "输入内容", required = true) @RequestParam("content") String content) {
        return R.data(teleprompterService.getTeleprompter(content, account));
    }

    /**
     * 结束本次提示词检测
     * @param account 登录账号
     * @return 结束结果
     */
    @ApiOperation(value = "结束本次提示词检测")
    @PostMapping("/finish")
    public R<Object> detect(@ApiParam(value = "登录账号", required = true) @RequestParam("account") String account) {
        teleprompterService.finish(account);
        return R.data(null);
    }

    /**
     * 知识库+模型聊天（流式回答）
     * @param chatMessagesReq 提问内容
     * @return 流式回答
     */
    @CrossOrigin("*")
    @ApiOperation(value = "知识库+模型聊天（流式回答）", notes = "不需要中转，直接调用")
    @RequestMapping(value = "/chatAics", produces = {MediaType.TEXT_EVENT_STREAM_VALUE}, method = {RequestMethod.GET,
            RequestMethod.POST})
    public SseEmitter chatAics(@Validated ChatMessagesStreamReq chatMessagesReq) {
        return teleprompterService.chatAicsMessageStream(chatMessagesReq);
    }

    /**
     * 大模型聊天（流式回答）
     * @param chatMessagesReq 提问内容
     * @return 流式回答
     */
    @CrossOrigin("*")
    @ApiOperation(value = "大模型聊天（流式回答）", notes = "不需要中转，直接调用")
    @RequestMapping(value = "/chatModel", produces = {MediaType.TEXT_EVENT_STREAM_VALUE}, method = {RequestMethod.GET,
            RequestMethod.POST})
    public SseEmitter chatModel(@Validated ChatMessagesStreamReq chatMessagesReq) {
        return teleprompterService.chatModelMessageStream(chatMessagesReq);
    }

    /**
     * 根据输入内容检测提示词
     * @param file 输入内容
     * @param account 登录账号
     * @return 检测结果
     */
    @ApiOperation(value = "根据语音文件检测提示词")
    @PostMapping("/detectByVoice")
    @CrossOrigin("*")
    public R<String> detectByVoice(@ApiParam(value = "语音文件", required = true) MultipartFile file,
            @ApiParam(value = "登录账号", required = true) @RequestParam("account") String account) {
        String text = aiVoiceToTextService.voiceToText(file);
        return R.data(teleprompterService.getTeleprompter(text, account));
    }

    /**
     * Agent聊天（流式回答）
     * @param chatMessagesReq 提问内容
     * @return 流式回答
     */
    @CrossOrigin("*")
    @ApiOperation(value = "Agent聊天（流式回答）", notes = "不需要中转，直接调用")
    @RequestMapping(value = "/chatAgent", produces = {MediaType.TEXT_EVENT_STREAM_VALUE},
            method = {RequestMethod.GET, RequestMethod.POST})
    public SseEmitter chatAgent(@Validated ChatMessagesStreamReq chatMessagesReq) {
        return teleprompterService.chatAgentMessageStream(chatMessagesReq);
    }
}
