package com.yirendai.voiceaiserver.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiFeatureControlEnum;
import com.yirendai.voiceaiserver.service.AiCustomerTagService;
import com.yirendai.voiceaiserver.service.IAiFeatureControlService;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiCustomerTagVO;
import com.yirendai.workbench.util.OwnAuthUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/customer/tag")
@Api(value = "ai用户和标签关联", tags = "ai用户和标签关联接口")
public class AiCustomerTagController {

    private static final Logger log = LoggerFactory.getLogger(AiCustomerTagController.class);
    @Resource
    private final AiCustomerTagService aiCustomerTagService;
    @Resource
    private final IAiFeatureControlService aiFeatureControlService;

    /**
     * 用户标签列表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "用户标签列表", notes = "传入userId")
    public R<List<AiCustomerTagVO>> list(String userId) {
        String tenantId = OwnAuthUtil.getTenantId();
//        tenantId = "278576";
        if (tenantId == null) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        boolean featureEnabled = aiFeatureControlService.isFeatureEnabled(AiFeatureControlEnum.AI_USER_TAGS_FLOOR, tenantId);
        if (!featureEnabled) {
            log.error("用户标签: {}, 标签功能未开启", tenantId);
            return R.fail("用户标签功能未开启");
        }
        List<AiCustomerTagVO> list = aiCustomerTagService.listByUserId(userId);
        return R.data(list);
    }

    /**
     * 用户标签反馈
     */
    @PostMapping("/feedback")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "用户标签反馈", notes = "传入aiCustomerTagFeedbackReq")
    public R<Boolean> feedback(@Validated AiFeedbackReq req) {
        String tenantId = OwnAuthUtil.getTenantId();
        if (tenantId == null) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        boolean featureEnabled = aiFeatureControlService.isFeatureEnabled(AiFeatureControlEnum.AI_USER_TAGS_FLOOR, tenantId);
        if (!featureEnabled) {
            log.error("用户标签: {}, 反馈功能未开启", tenantId);
            return R.fail("用户标签功能未开启");
        }
        return R.data(aiCustomerTagService.feedback(req));
    }
}
