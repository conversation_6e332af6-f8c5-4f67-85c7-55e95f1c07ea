package com.yirendai.voiceaiserver.controller.automation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ConversationFlowReachedVO", description = "节点触达查询结果")
public class ConversationFlowReachedVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否触达")
    private Boolean reached;

    @ApiModelProperty(value = "触发原因/关键词")
    private String reason;

    @ApiModelProperty(value = "触发时间")
    private LocalDateTime chatTime;
}
