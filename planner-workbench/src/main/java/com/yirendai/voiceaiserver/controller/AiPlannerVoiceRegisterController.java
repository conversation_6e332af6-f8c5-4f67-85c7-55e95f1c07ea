package com.yirendai.voiceaiserver.controller;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.service.AiPlannerVoiceInfoService;
import com.yirendai.voiceaiserver.service.AiPlannerVoiceRegisterService;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 理财师声纹注册信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@RestController
@RequestMapping("/voice/ai/server/planner/voice")
@Api(tags = "理财师声纹注册信息")
public class AiPlannerVoiceRegisterController {

    @Resource
    AiPlannerVoiceInfoService aiPlannerVoiceInfoService;
    @Resource
    AiPlannerVoiceRegisterService aiPlannerVoiceRegisterService;

    @ApiOperation(value = "上传或更新指定理财师的声纹（仅支持上传wav格式和mp3格式的音频文件）")
    @PostMapping("/saveOrUpdate")
    public R<Object> saveOrUpdate(@ApiParam(value = "理财师工号", required = true) @RequestParam("plannerNo") String plannerNo,
                                  @ApiParam(value = "租户id，不传时默认为宜人渠道租户id") @RequestParam(value = "tenantId", required = false) String tenantId,
                                  @ApiParam(value = "录音文件", required = true) MultipartFile file) {
        if (StrUtil.isBlank(plannerNo) || Objects.isNull(file)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        aiPlannerVoiceInfoService.saveOrUpdate(plannerNo, file, tenantId);
        return R.data(null);
    }

    @ApiOperation(value = "校验理财师声纹音频时长")
    @PostMapping("/check")
    public R<Object> check() {
        return R.data(aiPlannerVoiceInfoService.check());
    }

    @ApiOperation(value = "全量/剩余声纹注册到指定模型下")
    @PostMapping("/register")
    public R<Object> register(@ApiParam(value = "租户id，不传时注册所有租户下的声纹") @RequestParam(value = "tenantId", required = false) String tenantId,
                              @ApiParam(value = "模型名称", required = true) @RequestParam("model") String model) {
        if (StrUtil.isBlank(model)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        aiPlannerVoiceRegisterService.register(tenantId, model);
        return R.data(null);
    }

    @ApiOperation(value = "将指定理财师的声纹注册到指定模型下")
    @PostMapping("/registerPlanner")
    public R<Object> registerPlanner(@ApiParam(value = "租户id，不传时默认为宜人渠道租户id") @RequestParam(value = "tenantId", required = false) String tenantId,
                                     @ApiParam(value = "理财师工号", required = true) @RequestParam("plannerNo") String plannerNo,
                                     @ApiParam(value = "模型名称", required = true) @RequestParam("model") String model) {
        if (StrUtil.isBlank(plannerNo) || StrUtil.isBlank(model)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        aiPlannerVoiceRegisterService.registerPlanner(tenantId, plannerNo, model);
        return R.data(null);
    }
}

