/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.workbench.entity.AiUserTagDictionary;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.yirendai.voiceaiserver.service.IAiUserTagDictionaryService;

/**
 * ai用户标签字典 控制器
 * <AUTHOR>
 * @since 2024-11-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/user/tag/dictionary")
@Api(value = "ai用户标签字典", tags = "ai用户标签字典接口")
public class AiUserTagDictionaryController {

    private final IAiUserTagDictionaryService aiUserTagDictionaryService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入aiUserTagDictionary")
    public R<AiUserTagDictionary> detail(AiUserTagDictionary aiUserTagDictionary) {
        AiUserTagDictionary detail = aiUserTagDictionaryService.getOne(Condition.getQueryWrapper(aiUserTagDictionary));
        return R.data(detail);
    }

    /**
     * 分页 ai用户标签字典
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入aiUserTagDictionary")
    public R<IPage<AiUserTagDictionary>> list(AiUserTagDictionary aiUserTagDictionary, Query query) {
        IPage<AiUserTagDictionary> pages = aiUserTagDictionaryService.page(Condition.getPage(query), Condition.getQueryWrapper(aiUserTagDictionary));
        return R.data(pages);
    }



    /**
     * 新增 ai用户标签字典
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入aiUserTagDictionary")
    public R save(@Valid @RequestBody AiUserTagDictionary aiUserTagDictionary) {
        return R.status(aiUserTagDictionaryService.save(aiUserTagDictionary));
    }

    /**
     * 修改 ai用户标签字典
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入aiUserTagDictionary")
    public R update(@Valid @RequestBody AiUserTagDictionary aiUserTagDictionary) {
        return R.status(aiUserTagDictionaryService.updateById(aiUserTagDictionary));
    }


    /**
     * 删除 ai用户标签字典
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(aiUserTagDictionaryService.removeByIds(Func.toLongList(ids)));
    }

}
