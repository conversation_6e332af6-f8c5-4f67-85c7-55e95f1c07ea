package com.yirendai.voiceaiserver.controller;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiFeatureControlEnum;
import com.yirendai.voiceaiserver.service.AiConversationSummaryService;
import com.yirendai.voiceaiserver.service.IAiFeatureControlService;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationSummaryVO;
import com.yirendai.workbench.util.OwnAuthUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * ai对话内容小结表(AiConversationSummary)表控制层
 *
 * <AUTHOR>
 * @since 2024-11-28 14:59:06
 */
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/ai/conversation/summary")
@Api(value = "ai对话内容小结", tags = "ai对话内容小结接口")
public class AiConversationSummaryController {
    /**
     * 服务对象
     */
    @Resource
    private AiConversationSummaryService aiConversationSummaryService;
    @Resource
    private IAiFeatureControlService aiFeatureControlService;


    /**
     * 根据对话id查询小结
     *
     * @param uuid 对话id
     * @param systemType 系统类型（1: avaya, 2: new callcenter）
     * @param scene 系统类型（0: 呼叫后小结loading, 1: 通话记录小结）
     * @return 小结数据
     */
    @GetMapping("/select")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "根据对话uuid查询小结", notes = "传入chatContactId")
    public R<AiConversationSummaryVO> select(String uuid, Integer systemType, Integer scene) {
        if (uuid == null || systemType == null || scene < 0 || scene > 1) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        String tenantId = OwnAuthUtil.getTenantId();
        if (tenantId == null) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        boolean featureEnabled;
        if (scene == 0) {
            featureEnabled = aiFeatureControlService.isFeatureEnabled(AiFeatureControlEnum.CALL_AFTER_SUMMARY_POPUP, tenantId);
        } else {
            featureEnabled = aiFeatureControlService.isFeatureEnabled(AiFeatureControlEnum.TELESALES_RECORD_LISTEN_SUMMARY, tenantId);
        }
        if (!featureEnabled) {
            log.error("对话小结: {}, 小结功能未开启", tenantId);
            return R.fail("对话小结功能未开启");
        }
        return R.data(aiConversationSummaryService.selectByChatContactId(uuid, systemType));
    }

    /**
     * 小结反馈
     *
     * @param req 反馈信息
     * @return 是否成功
     */
    @PostMapping("/feedback")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "小结反馈", notes = "传入id")
    public R<Boolean> feedback(@Validated AiFeedbackReq req) {
        String tenantId = OwnAuthUtil.getTenantId();
        if (tenantId == null) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        return R.data(aiConversationSummaryService.feedback(req));
    }

    /**
     * 根据用户id查询小结
     */
    @GetMapping("/selectByUser")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "根据用户id查询小结", notes = "传入userId")
    public R<AiConversationSummaryVO> selectByUser(String userId) {
        String tenantId = OwnAuthUtil.getTenantId();
        if (tenantId == null) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        boolean featureEnabled = aiFeatureControlService.isFeatureEnabled(AiFeatureControlEnum.CALL_BUTTON_FLOAT_SUMMARY, tenantId);
        if (!featureEnabled) {
            log.error("对话小结: {}, 用户id小结功能未开启", tenantId);
            return R.fail("对话小结功能未开启");
        }
        return R.data(aiConversationSummaryService.selectByUserId(userId));
    }
}

