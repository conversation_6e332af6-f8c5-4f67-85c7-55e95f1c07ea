package com.yirendai.voiceaiserver.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.service.AiCallReservationService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.entity.AiCallReservation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/call/reservation")
@Api(value = "ai通话预约", tags = "ai通话预约接口")
public class AiCallReservationController {

    private final AiCallReservationService aiCallReservationService;

    /**
     * 通话预约列表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "通话预约列表", notes = "传入aiCallReservation")
    public R<List<AiCallReservation>> list(AiCallReservation aiCallReservation) {
        List<AiCallReservation> list = aiCallReservationService.list(Condition.getQueryWrapper(aiCallReservation));
        return R.data(list);
    }

}