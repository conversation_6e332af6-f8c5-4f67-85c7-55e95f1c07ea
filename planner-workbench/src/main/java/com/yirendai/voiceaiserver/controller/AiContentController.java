/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.service.IAiContentService;
import com.yirendai.workbench.entity.AiContent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * AI内容 控制器
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/aiContent")
@Api(value = "AI内容", tags = "AI内容接口")
public class AiContentController   {

	private final IAiContentService aiContentService;



	/**
	 * 分页 AI内容
	 */
	@GetMapping("/newsList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "新闻列表", notes = "传入aiContent")
	public R<IPage<AiContent>> newsList( Query query) {
		return R.data(aiContentService.getAiContentPage(query));
	}
}
