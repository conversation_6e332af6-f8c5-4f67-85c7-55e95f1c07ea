package com.yirendai.voiceaiserver.controller;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.VoiceStatusEnum;
import com.yirendai.workbench.entity.AiPlannerVoice;
import com.yirendai.voiceaiserver.service.AiPlannerVoiceService;
import com.yirendai.voiceaiserver.vo.response.AiPlannerVoiceListRes;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 理财师声音信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@RestController
@RequestMapping("/voice/ai/server/voice")
@Api(tags = "理财师声音信息")
public class AiPlannerVoiceController {

    @Resource
    AiPlannerVoiceService aiPlannerVoiceService;

    @ApiOperation(value = "录入或更新理财师声音")
    @PostMapping("/saveOrUpdate")
    @CrossOrigin("*")
    public R<String> saveOrUpdate(@ApiParam(value = "登录账号", required = true) @RequestParam("account") String account,
                                  @ApiParam(value = "理财师工号", required = true) @RequestParam("staffId") String staffId,
                                  @ApiParam(value = "录音文件", required = true) MultipartFile file) {
        if (StrUtil.isBlank(account) || StrUtil.isBlank(staffId)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (Objects.isNull(file)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }

        return R.data(aiPlannerVoiceService.saveOrUpdate(account, staffId, file));
    }

    @ApiOperation(value = "提交审核或去重新录音", notes = "根据当前状态自动变更")
    @PostMapping("/autoUpdateStatus")
    public R<Object> autoUpdateStatus(@ApiParam(value = "登录账号", required = true) @RequestParam("account") String account) {
        if (StrUtil.isBlank(account)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        aiPlannerVoiceService.autoUpdateStatus(account);
        return R.data(null);
    }

    @ApiOperation(value = "获取当前声音信息")
    @PostMapping("/get")
    public R<AiPlannerVoice> get(@ApiParam(value = "登录账号", required = true) @RequestParam("account") String account) {
        if (StrUtil.isBlank(account)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        return R.data(aiPlannerVoiceService.get(account));
    }

    @ApiOperation(value = "查看理财师声音信息列表")
    @PostMapping("/list")
    public R<List<AiPlannerVoiceListRes>> list(@ApiParam(value = "登录账号", required = true) @RequestParam("account") String account,
                                               @ApiParam(value = "理财师邮箱") @RequestParam(value = "email", required = false) String email,
                                               @ApiParam(value = "审核状态") @RequestParam(value = "status", required = false) Integer status) {
        if (StrUtil.isBlank(account)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        return R.data(aiPlannerVoiceService.list(email, status));
    }

    @ApiOperation(value = "审核通过/审核拒绝理财师声音")
    @PostMapping("/audit")
    public R<Object> audit(@ApiParam(value = "登录账号", required = true) @RequestParam("account") String account,
                           @ApiParam(value = "声音信息id", required = true) @RequestParam("id") Long id,
                           @ApiParam(value = "审核状态", required = true) @RequestParam("status") Integer status) {
        if (StrUtil.isBlank(account)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (!VoiceStatusEnum.APPROVED.getCode().equals(status) && !VoiceStatusEnum.REJECTED.getCode().equals(status)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        aiPlannerVoiceService.audit(account, id, status);
        return R.data(null);
    }
}

