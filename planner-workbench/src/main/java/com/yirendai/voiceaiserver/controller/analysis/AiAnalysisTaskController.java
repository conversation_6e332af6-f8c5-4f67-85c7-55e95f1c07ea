/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright customer,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  customer, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.controller.analysis;

import com.yirendai.voiceaiserver.service.IAiAnalysisTaskService;
import com.yirendai.voiceaiserver.vo.request.AiAnalysisTaskReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * ai分析任务 控制器
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/analysis/task")
@Api(value = "ai分析任务", tags = "ai分析任务接口")
public class AiAnalysisTaskController  {

	private IAiAnalysisTaskService aiAnalysisTaskService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分析任务详情", notes = "传入id")
	public R<AiAnalysisTaskVO> detail(AiAnalysisTaskReq aiAnalysisTaskReq) {
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		String tenantId = AuthUtil.getTenantId();
		if(tenantId == null){
			return R.fail("租户ID没有获取到");
		}
		if(aiAnalysisTaskReq.getId() == null){
			return R.fail("参数ID不能为空");
		}
		aiAnalysisTaskReq.setCreateUser(userId);
		aiAnalysisTaskReq.setTenantId(tenantId);

		AiAnalysisTaskVO detail = aiAnalysisTaskService.getByIdAndUserId(aiAnalysisTaskReq);
		return R.data(detail);
	}

	/**
	 * 分页 ai分析任务
	 */
	@PostMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页查询分析任务列表", notes = "传入aiAnalysisTask")
	public R<IPage<AiAnalysisTaskVO>> list(@RequestBody AiAnalysisTaskReq aiAnalysisTaskReq ) {
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		String tenantId = AuthUtil.getTenantId();
		if(tenantId == null){
			return R.fail("租户ID没有获取到");
		}
//		tenantId = "278576";
//		userId = 1854769895833624578L;
		aiAnalysisTaskReq.setCreateUser(userId);
		aiAnalysisTaskReq.setTenantId(tenantId);
		IPage<AiAnalysisTaskVO> pages = aiAnalysisTaskService.aiAnalysisTaskPageList(Condition.getPage(aiAnalysisTaskReq), aiAnalysisTaskReq);
		return R.data(pages);
	}

	
	/**
	 * 删除 ai分析任务
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(aiAnalysisTaskService.removeByIds(Func.toLongList(ids)));
	}

	
}
