package com.yirendai.voiceaiserver.controller;

import java.util.List;
import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.protobuf.ServiceException;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.service.AiHelperService;
import com.yirendai.voiceaiserver.vo.request.AiHelperMessageReq;
import com.yirendai.voiceaiserver.vo.response.WelComeVO;
import com.yirendai.workbench.entity.AiHelperMessage;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@RestController
@RequestMapping("aiHelper")
@Api(tags = "AI智能体助手")
@Slf4j
public class AiHelperController {

    @Resource
    AiHelperService AiHelperService;


    @PostMapping(value = "/chat-messages", produces = {MediaType.TEXT_EVENT_STREAM_VALUE} )
    @CrossOrigin("*")
    @ApiOperation(value = "AI智能体助手发送消息", notes = "AI智能体助手发送消息")
    public SseEmitter chatMessage(@RequestBody AiHelperMessageReq chatMessagesReq) throws ServiceException {
        BladeUser bladeUser = AuthUtil.getUser();
        if(bladeUser == null || bladeUser.getUserId() == null|| bladeUser.getUserId() == -1){
            throw new BusinessException(BusinessError.NOT_LOGIN);
        }
        chatMessagesReq.setAccount(bladeUser.getAccount());
        chatMessagesReq.setUserId(String.valueOf(bladeUser.getUserId()));
        chatMessagesReq.setTenantId(bladeUser.getTenantId());
        log.info("aiHelper chatMessage:{}", chatMessagesReq);
        if(chatMessagesReq.getQuery() == null)  {
            throw new BusinessException(BusinessError.PARAM_NULL_ERROR,"问题不能为空");
        }
        return AiHelperService.chatMessageStream(chatMessagesReq);
    }

    @PostMapping("/stop")
    @ApiOperation(value = "停止响应")
    public R<String> stop(@ApiParam(value = "任务 ID，可在流式返回 Chunk 中获取") @RequestParam(value = "taskId", required = true ) String taskId,
            @ApiParam(value = "场景: 201 默认场景 ,211 通话实时转语音场景") @RequestParam(value = "scene" ,defaultValue = "201") Integer scene) {
        Long userId = AuthUtil.getUserId();
        if(userId == null || userId == -1 ){
            return R.fail( BusinessError.NOT_LOGIN.getMessage());
        }
        return AiHelperService.stop(taskId,String.valueOf(userId),scene);
    }


    @PostMapping("/rating")
    @ApiOperation(value = "点赞、点踩")
    public R<String> rating(@ApiParam(value = "点赞 like, 点踩 dislike, 撤销点赞 0") @RequestParam(value = "rating", required = true ) String rating
            ,@ApiParam(value = "消息ID") @RequestParam(value = "messageId", required = true ) String messageId) {
        BladeUser bladeUser = AuthUtil.getUser();
        if(bladeUser == null  || bladeUser.getUserId() == null){
            return R.fail(BusinessError.NOT_LOGIN.getMessage());
        }
        return AiHelperService.rating(bladeUser,rating,messageId);
    }


    @GetMapping("/welcome")
    @ApiOperation(value = "获取欢迎语")
    public R<WelComeVO> getWelcome(  ) {
        BladeUser bladeUser = AuthUtil.getUser();
        if(bladeUser == null  || bladeUser.getUserId() == null|| bladeUser.getUserId() == -1){
            return R.fail( BusinessError.NOT_LOGIN.getMessage());
        }
        return AiHelperService.getWelcome(bladeUser );
    }

    @PostMapping("/suggested")
    @ApiOperation(value = "获取下一轮建议问题列表")
    public R<List<String>> suggested(  @ApiParam(value = "消息ID") @RequestParam(value = "messageId", required = true ) String messageId,
            @ApiParam(value = "场景: 201 默认场景 ,211 通话实时转语音场景") @RequestParam(value = "scene" ,defaultValue = "201") Integer scene) {
        BladeUser bladeUser = AuthUtil.getUser();
        if(bladeUser == null  || bladeUser.getUserId() == null|| bladeUser.getUserId() == -1){
            return R.fail( BusinessError.NOT_LOGIN.getMessage());
        }
        return AiHelperService.suggested(messageId,String.valueOf(bladeUser.getUserId() ));
    }
    @PostMapping("/delete")
    @ApiOperation(value = "删除聊天记录")
    public R<String> delete(@ApiParam(value = "会话ID") @RequestParam(value = "conversationId", required = false ) String conversationId) {
        Long userId = AuthUtil.getUserId();
        if(userId == null || userId == -1 ){
            return R.fail( BusinessError.NOT_LOGIN.getMessage());
        }
        return AiHelperService.delete(conversationId,String.valueOf(userId));
    }




    @PostMapping("listHistory")
    @ApiOperation(value = "聊天历史记录")
    public R<IPage<AiHelperMessage>> listAiHelperMessage(
            @ApiParam(value = "开始时间") @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            @ApiParam(value = "页码") @RequestParam(value = "pageNum", defaultValue = "1", required = false) int pageNum,
            @ApiParam(value = "每页显示") @RequestParam(value = "pageSize", defaultValue = "10", required = false) int pageSize) {
        Long userId = AuthUtil.getUserId();
        if(userId == null || userId == -1 ){
            return R.fail( BusinessError.NOT_LOGIN.getMessage());
        }
        return R.data(AiHelperService.listAiHelperMessage(userId, startTime, endTime, pageNum, pageSize));
    }

}
