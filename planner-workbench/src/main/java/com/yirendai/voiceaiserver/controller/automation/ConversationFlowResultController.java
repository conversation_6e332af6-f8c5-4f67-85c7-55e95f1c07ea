package com.yirendai.voiceaiserver.controller.automation;

import com.yirendai.voiceaiserver.service.IAiConversationFlowResultService;
import com.yirendai.workbench.entity.AiConversationFlowResult;
import com.yirendai.workbench.vo.req.salesProcess.ApiReq;
import com.yirendai.workbench.vo.res.ApiRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/ai/conversation/flow/result")
@Api(value = "AI会话流程结果", tags = "AI会话流程结果查询")
public class ConversationFlowResultController {

    @Resource
    private IAiConversationFlowResultService flowResultService;

    @PostMapping("/reached")
    @ApiOperation(value = "查询节点触达情况", notes = "根据用户ID、节点名称和起始时间查询首条触达记录")
    public R<ApiRes> getReachedResult(@RequestBody ApiReq req) {
        ApiRes apiRes = new ApiRes();
        if (req == null) {
            return R.data(apiRes);
        }
        AiConversationFlowResult result = flowResultService.getReachedResult(
                req.getCustomerId(), req.getExpectedValue(), req.getStartTime());
        if (result == null) {
            return R.data(apiRes.setResult(false));
        }
        apiRes.setResult(result.getIsReached() == 1);
        apiRes.setRemark(result.getMatchedKeywords());
        apiRes.setFinishTime(result.getChatTime());
        return R.data(apiRes);
    }

    @PostMapping("/mainIntent")
    @ApiOperation(value = "查询节点触达情况", notes = "根据用户ID、节点名称和起始时间查询首条触达记录")
    public R<ApiRes> getMainIntent(@RequestBody ApiReq req) {
        ApiRes apiRes = new ApiRes();
        if (req == null) {
            return R.data(apiRes);
        }
        AiConversationFlowResult result = flowResultService.getMainIntent(
                req.getCustomerId(), req.getExpectedValue(), req.getStartTime());
        if (result == null) {
            return R.data(apiRes.setResult(false));
        }
        apiRes.setResult(result.getIsReached() == 1);
        apiRes.setRemark(result.getMatchedKeywords());
        apiRes.setFinishTime(result.getChatTime());
        return R.data(apiRes);
    }
}
