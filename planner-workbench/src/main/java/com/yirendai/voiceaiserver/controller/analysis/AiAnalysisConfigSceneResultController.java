/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright customer,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  customer, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.controller.analysis;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.service.IAiAnalysisConfigSceneResultService;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneResultVO;
import com.yirendai.workbench.entity.AiAnalysisConfigSceneResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;

import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ai分析场景分析结果选项配置 控制器
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/analysis/config/scene/result")
@Api(value = "ai分析场景分析结果选项配置", tags = "ai分析场景分析结果选项配置接口")
public class AiAnalysisConfigSceneResultController   {

	private IAiAnalysisConfigSceneResultService aiAnalysisConfigSceneResultService;

	/**
	 * 查询场景分析结果列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询场景分析结果列表", notes = "传入sceneId")
	public R<List<AiAnalysisConfigSceneResultVO>> list(AiAnalysisConfigSceneResult aiAnalysisConfigSceneResult ) {
		if(aiAnalysisConfigSceneResult.getSceneId()==null){
			return R.fail("请传入场景ID");
		}
		List<AiAnalysisConfigSceneResultVO> pages = aiAnalysisConfigSceneResultService.listBySceneId(aiAnalysisConfigSceneResult.getSceneId());
		return R.data(pages);
	}


	/**
	 * 新增 ai分析场景分析结果选项配置
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入aiAnalysisConfigSceneResult")
	public R save(@Valid @RequestBody AiAnalysisConfigSceneResult aiAnalysisConfigSceneResult) {
		String tenantId = AuthUtil.getTenantId();
		if(tenantId == null){
			return R.fail("租户ID没有获取到");
		}
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		if( aiAnalysisConfigSceneResult.getSceneId() == null){
			return R.fail("场景ID不能为空");
		}
		if( aiAnalysisConfigSceneResult.getPrompt() == null){
			return R.fail("分析结果描述不能为空");
		}
		aiAnalysisConfigSceneResult.setCreateTime(LocalDateTime.now());
		aiAnalysisConfigSceneResult.setTenantId(tenantId);
		aiAnalysisConfigSceneResult.setCreateUser(userId);
		aiAnalysisConfigSceneResult.setUpdateUser(userId);
		aiAnalysisConfigSceneResult.setUpdateTime(LocalDateTime.now());
		//判断名称是否重复
		int count = aiAnalysisConfigSceneResultService.selectCountBySceneResultName(aiAnalysisConfigSceneResult.getResultName(), aiAnalysisConfigSceneResult.getSceneId(),null);
		if (count > 0) {
			return R.fail("分析结果已存在，不可重复添加");
		}
		return R.status(aiAnalysisConfigSceneResultService.save(aiAnalysisConfigSceneResult));
	}

	/**
	 * 修改 ai分析场景分析结果选项配置
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入aiAnalysisConfigSceneResult")
	public R update(@Valid @RequestBody AiAnalysisConfigSceneResult aiAnalysisConfigSceneResult) {
		String tenantId = AuthUtil.getTenantId();
		if(tenantId == null){
			return R.fail("租户ID没有获取到");
		}
		Long userId = AuthUtil.getUserId();
		if(userId == null){
			return R.fail("用户ID没有获取到");
		}
		if( aiAnalysisConfigSceneResult.getSceneId() == null){
			return R.fail("场景ID不能为空");
		}
		if( aiAnalysisConfigSceneResult.getPrompt() == null){
			return R.fail("分析结果描述不能为空");
		}
		aiAnalysisConfigSceneResult.setUpdateTime(LocalDateTime.now());
		aiAnalysisConfigSceneResult.setUpdateUser(userId);
		//判断名称是否重复
		int count = aiAnalysisConfigSceneResultService.selectCountBySceneResultName(aiAnalysisConfigSceneResult.getResultName(), aiAnalysisConfigSceneResult.getSceneId(),aiAnalysisConfigSceneResult.getId());
		if (count > 0) {
			return R.fail("分析结果已存在，不可重复添加");
		}
		return R.status(aiAnalysisConfigSceneResultService.updateById(aiAnalysisConfigSceneResult));
	}


	/**
	 * 删除 ai分析场景分析结果选项配置
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(aiAnalysisConfigSceneResultService.removeByIds(Func.toLongList(ids)));
	}

	
}
