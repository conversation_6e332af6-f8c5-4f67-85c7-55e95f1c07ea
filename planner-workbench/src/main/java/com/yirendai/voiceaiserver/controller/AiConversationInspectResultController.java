package com.yirendai.voiceaiserver.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.service.AiConversationInspectResultService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.entity.AiConversationInspectResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/conversation/inspect")
@Api(value = "ai对话内容稽查", tags = "ai对话内容稽查接口")
public class AiConversationInspectResultController {

    private final AiConversationInspectResultService aiConversationInspectResultService;

    /**
     * 对话稽查列表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "对话稽查列表", notes = "传入aiConversationInspectResult")
    public R<List<AiConversationInspectResult>> list(AiConversationInspectResult aiConversationInspectResult) {
        List<AiConversationInspectResult> list = aiConversationInspectResultService.list(Condition.getQueryWrapper(aiConversationInspectResult));
        return R.data(list);
    }
}
