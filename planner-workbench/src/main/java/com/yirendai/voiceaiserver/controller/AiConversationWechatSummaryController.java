package com.yirendai.voiceaiserver.controller;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiFeatureControlEnum;
import com.yirendai.voiceaiserver.service.AiConversationWechatSummaryService;
import com.yirendai.voiceaiserver.service.IAiFeatureControlService;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationSummaryVO;
import com.yirendai.workbench.util.OwnAuthUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * ai对话内容小结表(AiConversationWechatSummary)表控制层
 *
 * <AUTHOR>
 * @since 2024-11-28 14:59:06
 */
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/ai/conversation/wechat/summary")
@Api(value = "ai对话企微内容小结", tags = "ai对话企微内容小结接口")
public class AiConversationWechatSummaryController {
    /**
     * 服务对象
     */
    @Resource
    private AiConversationWechatSummaryService aiConversationWechatSummaryService;
    @Resource
    private IAiFeatureControlService aiFeatureControlService;


    /**
     * 根据对话id查询小结
     *
     * @param plannerId 理财师id
     * @param userId    用户id
     * @param date      日期
     * @return 小结数据
     */
    @GetMapping("/select")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "根据用户理财师日期查询企微小结", notes = "传入chatContactId")
    public R<AiConversationSummaryVO> select(String plannerId, String userId, String date) {
        if (Strings.isNullOrEmpty(plannerId) || Strings.isNullOrEmpty(userId) || Strings.isNullOrEmpty(date)) {
            log.warn("对话企微小结: 参数错误, plannerId: {}, userId: {}, date: {}", plannerId, userId, date);
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        String tenantId = OwnAuthUtil.getTenantId();
        if (tenantId == null) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        boolean featureEnabled = aiFeatureControlService.isFeatureEnabled(AiFeatureControlEnum.WECHAT_RECORD_QUERY_PAGE, tenantId);
        if (!featureEnabled) {
            log.error("对话企微小结: {}, 企微小结功能未开启", tenantId);
            return R.fail("对话小结功能未开启");
        }
        return R.data(aiConversationWechatSummaryService.selectByChatContactId(plannerId, userId, date));
    }

    /**
     * 小结反馈
     *
     * @param req 反馈信息
     * @return 是否成功
     */
    @PostMapping("/feedback")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "企微小结反馈", notes = "传入id")
    public R<Boolean> feedback(@Validated AiFeedbackReq req) {
        String tenantId = OwnAuthUtil.getTenantId();
        if (tenantId == null) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        boolean featureEnabled = aiFeatureControlService.isFeatureEnabled(AiFeatureControlEnum.WECHAT_RECORD_QUERY_PAGE, tenantId);
        if (!featureEnabled) {
            log.error("企微对话小结反馈: {}, 小结反馈功能未开启", tenantId);
            return R.fail("对话企微小结功能未开启");
        }
        return R.data(aiConversationWechatSummaryService.feedback(req));
    }
}

