package com.yirendai.voiceaiserver.controller;

import javax.annotation.Resource;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.entity.AiAicsHistory;
import com.yirendai.workbench.entity.AiAicsLikeHistory;
import com.yirendai.voiceaiserver.service.AicsService;
import com.yirendai.voiceaiserver.service.IAiAicsHistoryService;
import com.yirendai.voiceaiserver.service.IAiAicsLikeHistoryService;
import com.yirendai.voiceaiserver.vo.request.ChatLikeReq;
import com.yirendai.voiceaiserver.vo.request.ChatMessagesStreamReq;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @time 2023/11/13 16:32
 **/
@RestController
@RequestMapping("aics")
@Api(tags = "合规机器人")
@Slf4j
public class AicsController {

    @Resource
    AicsService aicsService;

    @Resource
    IAiAicsHistoryService aicsHistoryService;

    @Resource
    IAiAicsLikeHistoryService aicsLikeHistoryService;

    @RequestMapping(value = "/chat/stream", produces = {MediaType.TEXT_EVENT_STREAM_VALUE}, method = {RequestMethod.GET,
            RequestMethod.POST})
    @CrossOrigin("*")
    @ApiOperation(value = "合规机器人聊天", notes = "不需要中转，直接调用")
    public SseEmitter chatMessage(@Validated ChatMessagesStreamReq chatMessagesReq) {
        return aicsService.chatMessageStream(chatMessagesReq);
    }

    @PostMapping("/like")
    @ApiOperation(value = "点赞、踩")
    public R<Boolean> like(@Validated ChatLikeReq chatLikeReq) {
        return aicsService.like(chatLikeReq);
    }

    @PostMapping("listHistory")
    @ApiOperation(value = "合规机器人聊天历史记录")
    public R<IPage<AiAicsHistory>> listHistoryConversation(
            @ApiParam(value = "理财师编号") @RequestParam(value = "customerNo", required = false) String customerNo,
            @ApiParam(value = "开始时间") @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            @ApiParam(value = "页码") @RequestParam(value = "pageNum", defaultValue = "1", required = false) int pageNum,
            @ApiParam(value = "每页显示") @RequestParam(value = "pageSize", defaultValue = "10", required = false) int pageSize) {

        return R.data(aicsHistoryService.listHistoryPage(customerNo, startTime, endTime, pageNum, pageSize));
    }

    @PostMapping("listLikeHistory")
    @ApiOperation(value = "点赞历史记录")
    public R<IPage<AiAicsLikeHistory>> listLikeHistoryConversation(
            @ApiParam(value = "理财师编号") @RequestParam(value = "customerNo", required = false) String customerNo,
            @ApiParam(value = "开始时间") @RequestParam(value = "startTime", required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            @ApiParam(value = "页码") @RequestParam(value = "pageNum", defaultValue = "1", required = false) int pageNum,
            @ApiParam(value = "每页显示") @RequestParam(value = "pageSize", defaultValue = "10", required = false) int pageSize) {

        return R.data(aicsLikeHistoryService.listLikeHistoryPage(customerNo, startTime, endTime, pageNum, pageSize));
    }
}
