package com.yirendai.voiceaiserver.controller;

import javax.annotation.Resource;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.service.impl.AiVoiceToTextService;
import com.yirendai.voiceaiserver.service.impl.AvayaVoiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024/6/14 16:35
 **/
@RestController
@Api(tags = "avaya消息处理")
@RequestMapping("/voice/ai/server/avaya")
public class AvayaVoiceController {

    @Resource
    AvayaVoiceService avayaVoiceService;

    @Resource
    AiVoiceToTextService aiVoiceToTextService;

    @GetMapping("/countAvayaVoice")
    public R<BigDecimal> countAvayaVoice(){
        return R.data(avayaVoiceService.countAvayaVoice());
    }

    @PostMapping("/voiceToSplitText")
    public R<String> voiceToSplitText(@RequestParam("file") MultipartFile file){
        return R.data(aiVoiceToTextService.voiceToSplitText(file));
    }

    @ApiOperation(value = "批量处理指定id的消息")
    @PostMapping("/batchHandle")
    public R<Object> batchHandle(
            @ApiParam(value = "消息id，多个使用英文逗号分隔", required = true) @RequestParam("idList") String idList) {
        List<Long> ids = Arrays.stream(idList.split(",")).filter(StrUtil::isNotBlank).map(Long::valueOf).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(ids) || ids.size() > 80) {
            throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "入参id为空或id数量超过最大量80");
        }
        int round = (int) Math.ceil(((double) ids.size()) / 10);
        for (int i = 0; i < round; i++) {
            List<Long> subList = ids.subList(i * 10, Math.min((i + 1) * 10, ids.size()));
            avayaVoiceService.batchHandle(subList);
        }
        return R.data(null);
    }
}
