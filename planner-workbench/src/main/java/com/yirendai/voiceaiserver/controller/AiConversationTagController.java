package com.yirendai.voiceaiserver.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.service.AiConversationTagService;
import com.yirendai.voiceaiserver.vo.response.AiConversationTagExportDto;
import com.yirendai.workbench.entity.AiConversationTag;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Condition;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/ai/conversation/tag")
@Api(value = "ai对话和标签关联", tags = "ai对话和标签关联接口")
public class AiConversationTagController {

    private final AiConversationTagService aiConversationTagService;

    /**
     * 对话标签列表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "对话标签列表", notes = "传入aiConversationTag")
    public R<List<AiConversationTag>> list(AiConversationTag aiConversationTag) {
        List<AiConversationTag> list = aiConversationTagService.list(Condition.getQueryWrapper(aiConversationTag));
        return R.data(list);
    }

    /**
     * 对话标签详情excel导出
     * matchStatus: 0-匹配 1-推荐
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "对话标签详情excel导出", notes = "传入aiConversationTag")
    public R<Boolean> export(HttpServletResponse response, Integer matchStatus) {
        List<AiConversationTagExportDto> dtoList = aiConversationTagService.getAllDataByMatchStatus(matchStatus);
        // 导出操作
        try {
            String fileName = URLEncoder.encode(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "对话标签", "UTF-8").replaceAll("\\+", "%20");;
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcelFactory.write(response.getOutputStream(), AiConversationTagExportDto.class).sheet("sheet1").doWrite(dtoList);
        } catch (Exception e) {
            log.error("导出奖励明细失败", e);
            throw new AiServerException(ResultCode.FAILURE);
        }
        return R.data(true);
    }

}
