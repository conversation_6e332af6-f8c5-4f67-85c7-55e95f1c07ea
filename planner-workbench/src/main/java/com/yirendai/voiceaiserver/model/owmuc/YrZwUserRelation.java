package com.yirendai.voiceaiserver.model.owmuc;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 指旺宜人用户关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "指旺宜人用户关系信息")
public class YrZwUserRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "指旺用户ID")
    private Long zwUserId;

    @ApiModelProperty(value = "宜人用户ID")
    private Long yrUserId;

    @ApiModelProperty(value = "1-绑定，0-未绑定(一般不会出现)")
    private String bind;

    @ApiModelProperty(value = "绑定种类-9种")
    private String bindType;

    @ApiModelProperty(value = "绑定描述，描述操作过程")
    private String description;

    @ApiModelProperty(value = "备注信息")
    private String comment;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
}
