package com.yirendai.voiceaiserver.model.linkwechat;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会话消息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WeChatContactMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 发送人id
     */
    private String fromId;

    /**
     * 接收人id（列表）
     */
    private String toList;

    /**
     * 群聊id
     */
    private String roomId;

    /**
     * 消息类型
     */
    private String action;

    /**
     * 消息类型(如：文本，图片)
     */
    private String msgType;

    /**
     * 发送时间
     */
    private LocalDateTime msgTime;

    /**
     * 消息标识
     */
    private Long seq;

    /**
     * 消息内容
     */
    private String contact;

    /**
     * 是否为外部聊天 0 外部 1 内部
     */
    private Integer isExternal;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人id
     */
    private Long createById;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新人id
     */
    private Long updateById;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识 0正常 1 删除
     */
    private Integer delFlg;


}
