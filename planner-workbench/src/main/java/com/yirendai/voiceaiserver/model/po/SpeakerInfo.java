package com.yirendai.voiceaiserver.model.po;

import com.yirendai.voiceaiserver.util.SpeakerHelper;
import com.yirendai.workbench.enums.callcenter.anmi.AnmiConstants;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SpeakerInfo {
    private String leftSpeaker;
    private String rightSpeaker;
    private Integer direction;

    public SpeakerInfo(String leftSpeaker, String rightSpeaker) {
        this.leftSpeaker = leftSpeaker;
        this.rightSpeaker = rightSpeaker;
    }
    public SpeakerInfo(Integer direction) {
        leftSpeaker = SpeakerHelper.speakerOfLeft(direction);
        rightSpeaker = SpeakerHelper.speakerOfRight(direction);
        this.direction = direction;
    }

    public SpeakerInfo(Integer direction, AnmiConstants.AnmiDeviceType anmiDeviceType) {
        if (anmiDeviceType == null){
            leftSpeaker = SpeakerHelper.speakerOfLeft(direction);
            rightSpeaker = SpeakerHelper.speakerOfRight(direction);
        }else {
            leftSpeaker = anmiDeviceType.getAgentChannel() == 1 ? "客户" : "理财师";
            rightSpeaker = anmiDeviceType.getAgentChannel() == 1 ? "理财师" : "客户";
        }
        this.direction = direction;
    }
}
