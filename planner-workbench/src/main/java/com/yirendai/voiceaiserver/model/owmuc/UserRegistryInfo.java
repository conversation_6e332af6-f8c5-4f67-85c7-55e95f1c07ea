package com.yirendai.voiceaiserver.model.owmuc;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="UserRegistryInfo对象", description="")
public class UserRegistryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户id，唯一")
    private Long userId;

    @ApiModelProperty(value = "登录名称(邮箱或手机号)，唯一")
    private String loginName;

    @ApiModelProperty(value = "密码")
    private String passWord;

    @ApiModelProperty(value = "salt，暂不用")
    private String salt;

    @ApiModelProperty(value = "注册手机号，每个用户在系统中只能有一个手机号！")
    private String mobileNo;

    @ApiModelProperty(value = "注册邮箱。不支持邮箱注册，为了兼容旧系统，可登录")
    private String email;

    @ApiModelProperty(value = "邮箱验证状态 0 发送失败 1发送成功 2 验证成功")
    private String emailVerifyState;

    @ApiModelProperty(value = "邮箱激活时间")
    private LocalDateTime emailVerifyDate;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "头像url")
    private String avatarUrl;

    @ApiModelProperty(value = "注册来源")
    private String sourceType;

    @ApiModelProperty(value = "注册子渠道，由渠道自定义")
    private String subSourceType;

    @ApiModelProperty(value = "注册ip")
    private String ip;

    @ApiModelProperty(value = "用户状态 0已注销 1已激活")
    private String userStatus;

    @ApiModelProperty(value = "user_flag用户标志 0 借款 1 出借")
    private String userFlag;

    @ApiModelProperty(value = "角色类型  0 普通用户 1企业用户 2平台用户")
    private String roleType;

    @ApiModelProperty(value = "最新更新时间,初始同创建时间，更新记录时修改,迁移数据存在空字段")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "注册时间,迁移数据存在空字段")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "最近修改密码时间")
    private LocalDate lastPasswordUpdateDate;


}
