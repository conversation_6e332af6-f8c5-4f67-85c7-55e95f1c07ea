package com.yirendai.voiceaiserver.model.owmuc;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 注销记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="UserCancelHis对象", description="注销记录表")
public class UserCancelHis implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "宜人贷用户id")
    private Long userId;

    @ApiModelProperty(value = "登陆账号")
    private String loginName;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "证件号码")
    private String idCardNo;

    @ApiModelProperty(value = "证件类型")
    private Integer idCardType;

    @ApiModelProperty(value = "注销描述信息")
    private String cancelDesc;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "请求系统")
    private String sysCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "销户状态，0:未销户 1：销户成功 2:销户失败")
    private Integer cancelStatus;

    private String comments;


}
