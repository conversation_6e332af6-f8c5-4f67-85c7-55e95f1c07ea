package com.yirendai.voiceaiserver.model.owmuc;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 指旺用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ZwUserRegister对象", description="指旺用户表")
public class ZwUserRegister implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    @ApiModelProperty(value = "登录账号")
    private String username;

    @ApiModelProperty(value = "用户姓名")
    private String name;

    private String password;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @ApiModelProperty(value = "证件类型")
    private Integer idcardType;

    @ApiModelProperty(value = "证件号")
    private String idcardNumber;

    @ApiModelProperty(value = "手机号")
    private String mobilePhone;

    private String salt;

    private String tradePassword;

    private String ecifId;

    private String lenderId;

    private String lenderCertSerialNo;

    private String lenderCertDn;

    private Integer lenderCertSync;

    @ApiModelProperty(value = "0: 正常, -1: 销户")
    private Integer status;

    @ApiModelProperty(value = "广发开户状态")
    private Integer isTrusteeshipVerify;

    @ApiModelProperty(value = "代理商Id（注册渠道）")
    private Long bpBrokerId;

    @ApiModelProperty(value = "0:不符合公募基金准入要求 1: 符合公募基金准入要求")
    private Integer fundPermit;

    @ApiModelProperty(value = "广发二期开户状态")
    private Integer isNewTrusteeshipVerify;

    @ApiModelProperty(value = "结算用户号")
    private String settleUserId;

    @ApiModelProperty(value = "投资授权")
    private String investAuthStatus;

    @ApiModelProperty(value = "业务授权")
    private String bizAuthStatus;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime importAt;

    @ApiModelProperty(value = "0-未完成-1完成")
    private String finish;

    @ApiModelProperty(value = "0-未实名-1已实名")
    private String isVerify;

    @ApiModelProperty(value = "备注")
    private String comment;


}
