package com.yirendai.voiceaiserver.model.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Description: 语料媒体文件内容持久层对象
 * @Author: yuanbo
 * @Date: 2024/6/14 18:18
 * @Version: 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class AiChatPO implements Serializable {

    private static final long serialVersionUID = 1L;
    // 主键
    private Long id;

    // 处理前内容
    private String originContent;

    // 处理后内容
    private String processedContent;

    // 内容类型
    private String originType;

    // 消息标签
    private String msgTag;
}
