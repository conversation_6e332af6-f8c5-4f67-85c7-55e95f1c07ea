package com.yirendai.voiceaiserver.model.linkwechat;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 企业微信客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WeCustomer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外部联系人的userid
     */
    private String externalUserid;

    /**
     * 外部联系人名称
     */
    private String customerName;

    /**
     * 客户姓名
     */
    private String customerFullName;

    /**
     * 外部联系人的类型，1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户
     */
    private Integer customerType;

    /**
     * 外部联系人头像
     */
    private String avatar;

    /**
     * 外部联系人性别 0-未知 1-男性 2-女性
     */
    private Boolean gender;

    /**
     * 当前添加客户添加人是否离职 1:是; 0:否
     */
    private Boolean addUserLeave;

    /**
     * 0:加入黑名单;1:不加入黑名单;
     */
    private Integer isJoinBlacklist;

    /**
     * 外部联系人在微信开放平台的唯一身份标识,通过此字段企业可将外部联系人与公众号/小程序用户关联起来。
     */
    private String unionid;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 客户企业名称
     */
    private String corpName;

    /**
     * 客户职位
     */
    private String position;

    /**
     * 是否开启会话存档 0：关闭 1：开启
     */
    private Boolean isOpenChat;

    /**
     * 开通会话存档时间
     */
    private LocalDateTime openChatTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人id
     */
    private Long createById;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新人id
     */
    private Long updateById;

    /**
     * 删除标识 0 有效 1 删除
     */
    private Boolean delFlag;

    private String addUserId;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 1:待跟进;2:跟进中;3:已成交;4:无意向;5:已流失
     */
    private Boolean trackState;

    /**
     * 跟进内容
     */
    private String trackContent;

    /**
     * 跟进时间
     */
    private LocalDateTime trackTime;

    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 其他描述
     */
    private String otherDescr;

    /**
     * 地址
     */
    private String address;

    /**
     * 当前接替人
     */
    private String takeoverUserId;

    /**
     * 标签id冗余字段，方便查询
     */
    private String tagIds;

    /**
     * 添加方式
     */
    private Integer addMethod;

    /**
     * 渠道,当前用户通过哪个活码添加
     */
    private String state;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 区id
     */
    private Integer areaId;

    /**
     * 省/市/区
     */
    private String area;

    /**
     * 备注名
     */
    private String remarkName;

}
