package com.yirendai.voiceaiserver.model.owmuc;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 宜人用户实名信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "宜人用户实名信息")
public class UserVerifyInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户类型,0 出借人，默认0")
    private String userType;

    @ApiModelProperty(value = "证件类型")
    private String idCardType;

    @ApiModelProperty(value = "证件号码")
    private String idCardNo;

    @ApiModelProperty(value = "银行id")
    private String bankId;

    @ApiModelProperty(value = "银行卡号")
    private String bankAccountNo;

    @ApiModelProperty(value = "银行预留手机号")
    private String bankMobileNo;

    @ApiModelProperty(value = "0 不鉴权 2 二要素  3 三要素 4 四要素")
    private String verifyType;

    @ApiModelProperty(value = "鉴权次数，默认0，每次调用接口成功后加1")
    private Integer verifyNum;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "实名验证成功时间")
    private LocalDateTime verifySuccDate;

    @ApiModelProperty(value = "实名认证状态，0未认证 1认证成功(开户成功、开户待确认均为实名认证成功) 2认证失败2")
    private String status;

    @ApiModelProperty(value = "废弃")
    private String jsUserId;

    @ApiModelProperty(value = "请求id")
    private String requestId;

    @ApiModelProperty(value = "广发二期废弃********")
    private String verifyMode;

    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "结算处理时间")
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "结算返回码")
    private String respCode;

    @ApiModelProperty(value = "结算返回描述")
    private String respDesc;

    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "ecifId")
    private Long ecifId;
}
