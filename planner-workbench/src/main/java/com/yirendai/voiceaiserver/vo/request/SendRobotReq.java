package com.yirendai.voiceaiserver.vo.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/5/10 10:36
 **/
@Data
public class SendRobotReq {

    /**
     * 消息类型，固定为text
     */
    private String msgtype;

    private Content text;

    @Data
    public static class Content{

        /**
         * 文本内容，最长不超过2048个字节，必须是utf8编码
         */
        private String content;

        /**
         * userid的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人
         */
        private List<String> mentioned_list;

        /**
         * 手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人
         */
        private List<String> mentioned_mobile_list;
    }
}


