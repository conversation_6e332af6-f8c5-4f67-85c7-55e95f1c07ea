package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(value = "提示词使用场景类型")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromptSceneVo {
    @ApiModelProperty(value = "码值")
    private Integer code;

    @ApiModelProperty(value = "描述")
    private String desc;
}
