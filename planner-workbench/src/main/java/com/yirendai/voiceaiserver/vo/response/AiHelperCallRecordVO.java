package com.yirendai.voiceaiserver.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 小盟AI助手 分析任务通话记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "小盟AI助手 分析任务通话记录", description = "小盟AI助手 分析任务通话记录")
public class AiHelperCallRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "员工编号 ")
    private String plannerNo;

    @ApiModelProperty(value = "员工姓名")
    private String agentName;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户ID")
    private String customerUid;

    @ApiModelProperty(value = "电话唯一标识")
    private String uuid;

    @ApiModelProperty(value = "1呼入，2呼出")
    private Integer callType;

    @ApiModelProperty(value = "部门名称")
    private String agentDept;

    /**
     * 呼叫发起的日期/时间
     */
    private LocalDateTime startStamp;

    /**
     * 呼叫发起的日期/时间
     */
    private LocalDateTime endStamp;

    @ApiModelProperty(value = "音转文结果")
    @ExcelProperty(value = "音转文结果", index = 12)
    private String  processedContent;

}
