package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "订单检索信息")
public class PlannerUserOrderReq extends BasePageReqNoLogin {

    @NotBlank(message = "宜人用户id不能为空")
    @ApiModelProperty(value = "宜人用户id", required = true)
    private String userId;

    @NotBlank(message = "理财师工号不能为空")
    @ApiModelProperty(value = "理财师工号", required = true)
    private String plannerNo;

    @ApiModelProperty(value = "开始时间（包含）", example = "2024-01-01 00:00:00")
    private String startTime;

    @ApiModelProperty(value = "结束时间（包含）", example = "2024-07-01 00:00:00")
    private String endTime;
}