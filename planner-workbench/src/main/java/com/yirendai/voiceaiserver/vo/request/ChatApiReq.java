package com.yirendai.voiceaiserver.vo.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/1/31 11:27
 **/
@Data
public class ChatApiReq {

    private String model;

    private List<Message> messages;

    private Float temperature;

    private Boolean stream;

    public ChatApiReq(){
        Message message = new Message();
        message.setRole("system");
        message.setContent("你是一个翻译员，把输入中文翻译成英文，不需要原文");
        messages = new ArrayList<>();
        messages.add(message);
    }
    public ChatApiReq(String systemContent){
        Message message = new Message();
        message.setRole("system");
        message.setContent(systemContent);
        messages = new ArrayList<>();
        messages.add(message);
    }

    @Data
    public static class Message {

        private String role;

        private String content;
    }
}
