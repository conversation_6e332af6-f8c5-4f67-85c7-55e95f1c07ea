package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "企微理财师列表Request")
public class QwPlannerReq extends BasePageReq {

    @NotBlank(message = "客户userId不能为空")
    @ApiModelProperty(value = "客户userId", required = true)
    private String userId;

    @NotBlank(message = "租户id不能为空")
    @ApiModelProperty(value = "租户id", required = true)
    private String tenantId;

    @ApiModelProperty(value = "理财师id")
    private String plannerName;

    @ApiModelProperty(value = "理财师姓名")
    private String plannerNo;
}
