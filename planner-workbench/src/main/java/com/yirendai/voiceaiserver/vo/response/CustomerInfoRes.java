package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "客户信息Res")
public class CustomerInfoRes {

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "userId")
    private String userId;

    @ApiModelProperty(value = "真实姓名")
    private String userName;

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty(value = "externalUserId")
    private String externalUserid;

    @ApiModelProperty(value = "外部联系人名称（昵称）")
    private String customerName;

    @ApiModelProperty(value = "外部联系人的类型，1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户")
    private Integer customerType;

    @ApiModelProperty(value = "外部联系人头像")
    private String avatar;

    @ApiModelProperty(value = "备注名")
    private String remarkName;
}
