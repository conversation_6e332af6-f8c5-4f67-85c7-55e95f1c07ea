package com.yirendai.voiceaiserver.vo.response;

import lombok.Data;

import java.util.List;

/**
 * 发送对话消息Res
 */
@Data
public class ChatMessagesBlockRes {
    private String answer;

    private String conversation_id;

    private String event;

    private String task_id;

    private String id;

    private Metadata metadata;

    @Data
    public class RetrieverResources {
        private int position;

        private String dataset_id;

        private String dataset_name;

        private String document_id;

        private String document_name;

        private String data_source_type;

        private String segment_id;

        private String retriever_from;

        private double score;

        private String content;
    }

    @Data
    public class Metadata {
        private List<RetrieverResources> retriever_resources;
    }
}
