package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问题和答案列表Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("问题和答案列表Response")
public class QuestionAndAnswerListRes {

    @ApiModelProperty("问题和答案列表")
    private List<QuestionAndAnswerInfo> list;
}
