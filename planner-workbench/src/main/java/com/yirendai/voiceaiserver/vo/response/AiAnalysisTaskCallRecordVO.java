package com.yirendai.voiceaiserver.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * ai分析任务通话记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AiAnalysisTaskCallRecord对象", description = "ai分析任务通话记录")
public class AiAnalysisTaskCallRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "ai分析任务Id ai_analysis_task表ID")
    private Long analysisTaskId;

    @ApiModelProperty(value = "ai分析任务数据母表ID ai_analysis_task_data表ID")
    private Long analysisTaskDataId;

    @ApiModelProperty(value = "场景ID")
    private Long sceneId;

    @ApiModelProperty(value = "通话记录ID callcenter_call_record 表ID")
    private Long callRecordId;

    @ApiModelProperty(value = "用户沟通会话ID ai_planner_chat_contact 表ID")
    private Long chatContactId;

    @ApiModelProperty(value = "员工编号 ")
    private String plannerNo;

    @ApiModelProperty(value = "员工姓名")
    private String agentName;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "电话唯一标识")
    private String uuid;

    @ApiModelProperty(value = "1呼入，2呼出")
    private Integer callType;

    @ApiModelProperty(value = "客户手机号")
    private String phone;

    @ApiModelProperty(value = "部门名称")
    private String agentDept;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUser;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "AI分析状态 0未开始，1进行中，2已完成,3失败")
    private Integer status;

    @ApiModelProperty(value = "AI分析完成时间")
    private LocalDateTime analysisTime;

    @ApiModelProperty(value = "是否已删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "通话记录分析结果列表")
    private List<AiAnalysisTaskResultVO> aiAnalysisTaskResultList;

    @ApiModelProperty(value = "通话记录分析结果")
    private String taskResult;

    @ExcelProperty(value = "AI分析结果说明")
    private String resultDesc;

    @ApiModelProperty(value = "音频地址")
    private String recordUrl;

    @ApiModelProperty(value = "记录类型 0:通话记录，1:客户数据")
    private Integer recordType;

    @ApiModelProperty(value = "客户ID，当recordType=2时使用")
    private String customerId;

    @ApiModelProperty(value = "数据源类型：1通话记录，2短信记录，3微信记录，4企微记录，5客户基本信息，6客户业务数据")
    private String dataSourceType;

    @ApiModelProperty(value = "数据源ID，根据类型关联不同的表主键")
    private Long dataSourceId;

    @ApiModelProperty(value = "客户数据JSON，包含基本信息等")
    private String customerBaseData;

    @ApiModelProperty(value = "客户数据JSON，业务数据")
    private String customerBusinessData;

    @ApiModelProperty(value = "数据源类型，多个用逗号分隔：1通话记录，2短信记录，3微信记录，4企微记录，5客户基本信息，6客户业务数据，7客户订单数据")
    private String dataSourceTypes;
}
