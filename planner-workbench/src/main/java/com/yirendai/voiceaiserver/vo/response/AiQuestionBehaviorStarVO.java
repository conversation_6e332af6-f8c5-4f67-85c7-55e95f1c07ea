package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 问题收藏记录信息
 */
@Data
@ApiModel("问题收藏记录信息")
public class AiQuestionBehaviorStarVO {

    @ApiModelProperty("问题")
    private String question;

    @ApiModelProperty("收藏记录信息")
    private List<AiQuestionBehaviorVO> behaviorList;
}
