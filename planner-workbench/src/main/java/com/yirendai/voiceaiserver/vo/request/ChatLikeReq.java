package com.yirendai.voiceaiserver.vo.request;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2024/1/22 17:47
 **/
@Data
@ApiModel(value = "点赞req")
public class ChatLikeReq extends BaseReq {

    @NotEmpty(message = "问题不能为空")
    @ApiModelProperty(value = "客户问题", required = true)
    private String question;

    @NotEmpty(message = "答案不能为空")
    @ApiModelProperty(value = "机器人答案", required = true)
    private String answer;

    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态 1:点赞 2：踩", required = true)
    private Integer likeStatus;
}
