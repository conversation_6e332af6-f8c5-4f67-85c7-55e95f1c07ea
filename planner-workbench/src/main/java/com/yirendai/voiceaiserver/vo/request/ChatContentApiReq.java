package com.yirendai.voiceaiserver.vo.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class ChatContentApiReq {

    private String model;

    private List<Message> messages;

    /**
     * 温度：0.0-1.0；控制生成文本的随机性。较低的温度（接近0）会使模型更保守，倾向于选择概率最高的词；较高的温度会增加随机性，使输出更加多样化。
     */
    private Float temperature;

    private Boolean stream;

    /**
     * 核采样：0.0-1.0；控制生成文本的多样性。0.0表示无核采样，1.0表示完全核采样。
     */
    private Float top_p;

    /**
     * 重复惩罚：0.0-1.0；对重复的惩罚。0.0表示没有惩罚，1.0表示完全惩罚。
     */
    private Float repetition_penalty;



    public ChatContentApiReq(){
        Message message = new Message();
        message.setRole("system");
        message.setContent("你是一个翻译员，把输入中文翻译成英文，不需要原文");
        messages = new ArrayList<>();
        messages.add(message);
    }
    public ChatContentApiReq(String systemContent){
        Message message = new Message();
        message.setRole("system");
        message.setContent(systemContent);
        messages = new ArrayList<>();
        messages.add(message);
    }

    @Data
    public static class Message {

        private String role;

        private String content;
    }
}
