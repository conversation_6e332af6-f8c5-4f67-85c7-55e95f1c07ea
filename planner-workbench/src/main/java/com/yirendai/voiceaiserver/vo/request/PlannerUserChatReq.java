package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "聊天记录检索信息")
public class PlannerUserChatReq extends BasePageReq {

    @NotBlank(message = "宜人用户id不能为空")
    @ApiModelProperty(value = "宜人用户id", required = true)
    private String userId;

    @NotBlank(message = "理财师工号不能为空")
    @ApiModelProperty(value = "理财师工号", required = true)
    private String plannerNo;

    @ApiModelProperty(value = "开始时间（包含）", example = "2024-01-01 00:00:00")
    private String startTime;

    @ApiModelProperty(value = "消息开始id（不包含），用于过滤重复消息，与endId二选一，此字段传值时，要求startTime必传")
    private Long startId;

    @ApiModelProperty(value = "结束时间（包含）", example = "2024-08-01 00:00:00")
    private String endTime;

    @ApiModelProperty(value = "消息结束id（不包含），用于过滤重复消息，与startId二选一，此字段传值时，要求endTime必传且startTime不传值")
    private Long endId;
}
