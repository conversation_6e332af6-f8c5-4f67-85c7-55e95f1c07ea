package com.yirendai.voiceaiserver.vo.response;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @time 2024/3/5 10:55
 **/
@Data
public class AudioToTextVo {

    private String language;

    private String duration;

    private List<Segment> segments;

    private String text;

    private Integer code;

    private String error_message;

    @Data
    public static class Segment {

        private String text;

        private Integer start;

        private Integer end;

        private String speaker;

        private Integer spk;

        private List<List<Long>> timestamp;
    }
}
