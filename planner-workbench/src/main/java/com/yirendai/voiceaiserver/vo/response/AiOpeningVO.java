package com.yirendai.voiceaiserver.vo.response;

import com.yirendai.workbench.entity.AiOpening;
import com.yirendai.workbench.entity.AiOpeningBehavior;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 开场白VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "开场白VO")
public class AiOpeningVO {

    @ApiModelProperty(value = "开场白信息")
    private AiOpening openingInfo;

    @ApiModelProperty(value = "推荐话题id&后台关键词id列表")
    private List<Long> keywordIdList;

    @ApiModelProperty(value = "用户行为信息")
    private AiOpeningBehavior openingBehavior;
}
