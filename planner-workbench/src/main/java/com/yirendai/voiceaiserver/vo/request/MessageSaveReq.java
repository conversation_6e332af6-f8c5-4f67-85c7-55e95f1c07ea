package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增/更新今日新闻或活动规则Requset
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("新增/更新今日新闻或活动规则Requset")
public class MessageSaveReq extends BaseReq {

    @NotNull
    @ApiParam(value = "类型 1：新闻 2：活动规则", required = true)
    private Integer type;

    @NotBlank
    @ApiParam(value = "内容", required = true)
    private String content;
}
