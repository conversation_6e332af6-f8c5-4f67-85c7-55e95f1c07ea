/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.vo.response;

import com.yirendai.workbench.entity.AiHelperMessage;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI助手会话记录视图实体类
 * <AUTHOR>
 * @since 2024-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AiHelperMessageVO对象", description = "AI助手会话记录")
public class AiHelperMessageVO extends AiHelperMessage {

    private static final long serialVersionUID = 1L;

}
