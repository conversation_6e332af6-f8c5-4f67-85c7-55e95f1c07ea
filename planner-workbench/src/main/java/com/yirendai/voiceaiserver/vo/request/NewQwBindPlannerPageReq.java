package com.yirendai.voiceaiserver.vo.request;

import com.yirendai.workbench.vo.req.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("新企微记录-已绑定企微的员工列表分页搜索Request")
public class NewQwBindPlannerPageReq extends BasePageReq {

    @ApiModelProperty(value = "员工姓名，模糊搜索")
    private String name;

    @ApiModelProperty(value = "企微名，模糊搜索")
    private String qwName;

    @ApiModelProperty(value = "部门id")
    private String deptId;
}
