package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AsrOnlineVO {

    /**
     * 语音输出模式
     * 2pass-online 临时转的文本内容
     * 2pass-offline 最终的文本内容，展示时要用 offline的替换换前边输出的online内容
     * online 直接转的文本内容
     */
    private String mode;

    /**
     * 文本内容
     */
    private String text;

    /**
     * 0:理财师 1:客户
     */
    private Integer direction;

    /**
     * 0异常 1正常情况
     */
    private Integer status;

    /**
     * 异常情况的提示信息
     */
    private String msg;

    @ApiModelProperty(value = "说话人姓名")
    private String userName;

    // 租户id
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    // 理财师工号
    @ApiModelProperty(value = "理财师工号")
    private String plannerNo;

}
