package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * ai分析任务通话记录分析场景数量
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ai分析任务通话记录分析场景数量 ", description = "ai分析任务通话记录分析场景数量 ")
public class AiAnalysisTaskSceneCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "场景ID")
    private Long id;

    @ApiModelProperty(value = "场景ID")
    private String idStr;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "场景名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private String resultCount;

    @ApiModelProperty(value = "场景结果数量")
    List<AiAnalysisTaskSceneResultCountVO> resultCountVOList;



}
