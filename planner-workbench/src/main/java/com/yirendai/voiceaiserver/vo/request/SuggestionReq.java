package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 获取推荐话术Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("获取推荐话术Request")
public class SuggestionReq extends BaseReq {

    @NotNull(message = "客户userId不能为空")
    @ApiParam(value = "客户userId", required = true)
    private Long userId;
}
