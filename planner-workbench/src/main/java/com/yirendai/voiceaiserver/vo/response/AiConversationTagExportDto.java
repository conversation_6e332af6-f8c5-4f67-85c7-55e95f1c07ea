package com.yirendai.voiceaiserver.vo.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("ai对话标签导出对象")
@ColumnWidth(20)
@ContentRowHeight(20)
@HeadRowHeight(30)
@ExcelIgnoreUnannotated
public class AiConversationTagExportDto {
    @ExcelProperty(value = "对话id", index = 0)
    @ApiModelProperty("对话id")
    private Long id;
    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id", index = 1)
    @ApiModelProperty("用户id")
    private Long userId;
    /**
     * 理财师工号
     */
    @ExcelProperty(value = "理财师工号", index = 2)
    @ApiModelProperty("理财师工号")
    private String plannerNo;
    /**
     * ai处理内容
     */
    @ExcelProperty(value = "ai处理内容", index = 3)
    @ApiModelProperty("ai处理内容")
    private String aiContent;
    /**
     * 标签和分类
     */
    @ExcelProperty(value = "标签和分类", index = 4)
    @ApiModelProperty("标签和分类")
    private String tagAndCategory;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 5)
    @ApiModelProperty("创建时间")
    private String createTime;
    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间", index = 6)
    @ApiModelProperty("更新时间")
    private String updateTime;
    /**
     * 音频路径
     */
    @ExcelProperty(value = "音频路径", index = 7)
    @ApiModelProperty("音频路径")
    private String audioPath;

}
