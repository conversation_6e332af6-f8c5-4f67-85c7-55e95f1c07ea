package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问题和答案信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("问题和答案信息")
public class QuestionAndAnswerInfo {

    @ApiModelProperty("问题")
    private String question;

    @ApiModelProperty("答案列表")
    private List<QuestionAnswerInfo> answerList;

    @ApiModelProperty("关联内容id，可为总结id或相关推荐id")
    private Long relationId;
}
