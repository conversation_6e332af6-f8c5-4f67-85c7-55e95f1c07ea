package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "聊天记录列表检索信息")
public class PlannerUserChatListReq extends BaseReq {

    @NotBlank(message = "宜人用户id不能为空")
    @ApiModelProperty(value = "宜人用户id", required = true)
    private String userId;

    @NotBlank(message = "理财师工号不能为空")
    @ApiModelProperty(value = "理财师工号", required = true)
    private String plannerNo;

    @NotBlank(message = "时间点不能为空")
    @ApiModelProperty(value = "时间点", example = "2024-08-01 00:00:00", required = true)
    private String timePoint;

    @ApiModelProperty(value = "每页展示条数", example = "10")
    @Min(value = 1, message = "size最小为1")
    private Integer size = 10;
}
