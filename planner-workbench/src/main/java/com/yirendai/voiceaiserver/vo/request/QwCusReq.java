package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "企微客户列表Request")
public class QwCusReq extends BaseReq {

    @ApiModelProperty(value = "客户userId（完全匹配)")
    private String userId;

    @ApiModelProperty(value = "客户姓名")
    private String userName;

    @ApiModelProperty(value = "起始客户userId（大于)")
    private String startUserId;

    @NotBlank(message = "租户id不能为空")
    @ApiModelProperty(value = "租户id", required = true)
    private String tenantId;

    @NonNull
    @ApiModelProperty(value = "每页展示条数")
    private Integer size;
}
