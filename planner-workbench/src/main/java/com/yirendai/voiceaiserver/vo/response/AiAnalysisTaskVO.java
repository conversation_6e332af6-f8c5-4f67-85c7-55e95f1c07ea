package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * ai分析任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AiAnalysisTask对象", description = "ai分析任务")
public class AiAnalysisTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUser;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "状态 0未开始，1进行中，2已完成")
    private Integer status;

    @ApiModelProperty(value = "是否已删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "分析类型 1:客户ID，0:录音ID")
    private Integer analysisType;

    @ApiModelProperty(value = "开始时间（客户分析时使用）")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间（客户分析时使用）")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "分析内容，多个用逗号分隔：1通话记录，2短信记录，3微信记录，4企微记录，5客户基本信息，6客户业务数据，7客户订单数据")
    private String analysisContent;

    @ApiModelProperty(value = "场景数量")
    private List<AiAnalysisTaskSceneCountVO> sceneCountVOList;

    @ApiModelProperty(value = "任务数量")
    private Integer taskCount;

    @ApiModelProperty(value = "任务来源,0-会话任务分析，1-通讯记录，2-公海列表，3-客户列表")
    private Integer sourcePage;

    @ApiModelProperty(value = "任务来源,0-会话任务分析，1-通讯记录，2-公海列表，3-客户列表")
    private String sourcePageDesc;


}
