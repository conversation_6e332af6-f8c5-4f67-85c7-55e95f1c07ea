package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推荐话题或开场白搜索Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "推荐话题或开场白搜索结果信息")
public class OpeningSearchRes {

    @ApiModelProperty(value = "推荐话题或开场白列表")
    private List<AiOpeningVO> openingVOList;
}
