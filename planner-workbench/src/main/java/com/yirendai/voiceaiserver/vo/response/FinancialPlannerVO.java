package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * Scrm系统中理财师企微信息
 */
@Data
public class FinancialPlannerVO {

    @ApiModelProperty(value = "企微员工ID")
    private String userId;

    @ApiModelProperty(value = "企微员工姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "性别。0表示未定义，1表示男性，2表示女性")
    private Integer gender;

    @ApiModelProperty(value = "头像url")
    private String avatar;

    @ApiModelProperty(value = "激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    @ApiModelProperty(value = "理财师编号")
    private String plannerNumber;

    @ApiModelProperty(value = "理财师姓名")
    private String plannerName;
}
