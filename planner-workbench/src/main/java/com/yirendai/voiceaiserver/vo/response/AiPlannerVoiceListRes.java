package com.yirendai.voiceaiserver.vo.response;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 理财师声音信息
 * </p>
 * <AUTHOR>
 * @since 2024-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "理财师声音列表信息")
public class AiPlannerVoiceListRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "登录账号")
    private String account;

    @ApiModelProperty(value = "理财师工号")
    private String staffId;

    @ApiModelProperty(value = "理财师姓名")
    private String name;

    @ApiModelProperty(value = "声音文件url")
    private String fileUrl;

    @ApiModelProperty(value = "声音文件nas路径")
    private String filePath;

    @ApiModelProperty(value = "状态 0：未审核 1：审核中 2：审核通过 3：审核拒绝")
    private Integer status;

    @ApiModelProperty(value = "声纹库id")
    private String voiceId;

    @ApiModelProperty(value = "审核账号")
    private String auditAccount;

    @ApiModelProperty(value = "组别名称")
    private String groupsName;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
