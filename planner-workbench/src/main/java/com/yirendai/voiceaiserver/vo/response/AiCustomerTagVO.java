package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AiCustomerTagVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;
    /**
     * 顶级标签分类ID
     */
    @ApiModelProperty("顶级标签分类ID")
    private Long topTagCategoryId;
    /**
     * 顶级标签分类名
     */
    @ApiModelProperty("顶级标签分类名")
    private String topTagCategoryName;
    /**
     * 标签详情列表
     */
    @ApiModelProperty("标签详情列表")
    private List<AiCustomerTagDetail> aiCustomerTagDetail;
    /**
     * 权重值
     */
    @ApiModelProperty("总权重值")
    private Integer totalWeight;

    @Builder
    @Data
    public static class AiCustomerTagDetail {
        /**
         * 主键ID
         */
        @ApiModelProperty("主键ID")
        private Long id;
        /**
         * 标签ID
         */
        @ApiModelProperty("标签ID")
        private Long tagId;
        /**
         * 标签名
         */
        @ApiModelProperty("标签名")
        private String tagName;
        /**
         * 标签分类ID
         */
        @ApiModelProperty("标签分类ID")
        private Long tagCategoryId;
        /**
         * 标签分类名
         */
        @ApiModelProperty("标签分类名")
        private String tagCategoryName;
        /**
         * 权重值
         */
        @ApiModelProperty("权重值")
        private Integer weight;
        /**
         * 色值
         */
        @ApiModelProperty("色值")
        private String colorValue;
        /**
         * 反馈状态，0=未反馈, 1=准确，2=不准确
         */
        @ApiModelProperty("反馈状态，0=未反馈, 1=准确，2=不准确")
        private Integer feedbackStatus;
    }
}
