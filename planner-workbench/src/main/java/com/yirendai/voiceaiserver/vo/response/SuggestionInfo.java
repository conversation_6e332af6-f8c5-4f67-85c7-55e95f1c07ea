package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推荐话术信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("推荐话术信息")
public class SuggestionInfo {

    @ApiModelProperty("话术内容")
    private String content;
}
