package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "企微聊天记录检索信息")
public class QwMsgSearchReq extends BasePageReq {

    @NotBlank(message = "客户userId不能为空")
    @ApiModelProperty(value = "客户userId", required = true)
    private String userId;

    @NotBlank(message = "理财师工号不能为空")
    @ApiModelProperty(value = "理财师工号", required = true)
    private String plannerNo;

    @ApiModelProperty(value = "开始时间（包含），与结束时间二选一", example = "2024-01-01 00:00:00")
    private String startTime;

    @ApiModelProperty(value = "结束时间（不包含），与开始时间二选一", example = "2024-08-01 00:00:00")
    private String endTime;

    @NotBlank(message = "功能位置不能为空")
    @ApiModelProperty(value = "功能位置", required = true)
    private String position;

    @ApiModelProperty("是否查询小结，不传时默认查询")
    private Boolean needSummary;
}
