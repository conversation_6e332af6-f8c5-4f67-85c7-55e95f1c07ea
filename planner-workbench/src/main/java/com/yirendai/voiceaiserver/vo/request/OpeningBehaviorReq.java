package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 开场白行为Req
 */
@Data
@ApiModel(value = "开场白-用户行为信息")
public class OpeningBehaviorReq extends BaseReq {

    @NotNull(message = "开场白类型不可为空")
    @ApiModelProperty(value = "开场白类型 1：库中话术 2：AI生成话术", required = true)
    private Integer openingType;

    @NotBlank(message = "开场白id或开场白不可为空")
    @ApiModelProperty(value = "开场白id或开场白 openingType=1时传开场白id，openingType=2时传开场白", required = true)
    private String opening;

    @ApiModelProperty(value = "推荐话题id&后台关键词id列表")
    private List<Long> keywordIdList;

    @NotNull(message = "用户行为不可为空")
    @ApiModelProperty(value = "用户行为 1：点赞 2：踩 3：收藏 4：取消收藏", required = true)
    private Integer behavior;
}
