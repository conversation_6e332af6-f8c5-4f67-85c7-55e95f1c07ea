package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "资产类型")
public class AssetTypeVO {

    @ApiModelProperty(value = "码值")
    private Integer code;

    @ApiModelProperty(value = "描述")
    private String desc;
}
