package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 默认随机库中开场白Request
 */
@Data
@ApiModel(value = "随机库中开场白Request(默认)")
public class OpeningRandomReq extends BaseReq {

    @NotNull(message = "展示数量不可为空")
    @Max(value = 20, message = "size最大不可超过20")
    @Min(value = 1, message = "size最小为1")
    @ApiModelProperty(value = "展示数量", required = true)
    private Integer size;
}
