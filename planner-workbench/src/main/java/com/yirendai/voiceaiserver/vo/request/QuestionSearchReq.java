package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 问题搜索Request
 */
@Data
@ApiModel("问题搜索Request")
public class QuestionSearchReq extends BaseReq {

    @NotNull(message = "数量不可为空")
    @ApiModelProperty(value = "数量", required = true)
    @Min(value = 1, message = "size最小为1")
    @Max(value = 10, message = "size最大为10")
    private Integer size;

    @NotNull(message = "问题来源不可为空")
    @ApiModelProperty(value = "问题来源 1：搜索内容来自匹配列表 2：点击找话术按钮", required = true)
    private Integer source;

    @NotBlank(message = "搜索内容不可为空")
    @ApiModelProperty(value = "搜索内容，source = 1时传总结问题id", required = true)
    private String content;
}
