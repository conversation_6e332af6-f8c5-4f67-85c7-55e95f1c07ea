package com.yirendai.voiceaiserver.vo.request;

import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.enums.AiModelType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiConversationProcessReq {
    /**
     * 对话ID
     */
    @ApiModelProperty(value = "数据来源表主键ID（非固定表id），如果没有则必须传taskId!!!")
    private Long chatContactId;
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID,和对话ID选择性传入一个!!!")
    private String taskId;
    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID，第一次必须要传!!!")
    private String tenantId;
    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型， 必须传入!!!", required = true)
    private AiConversationProcessTypeEnum taskType;
    /**
     * 理财师工号
     */
    @ApiModelProperty(value = "理财师工号")
    private String plannerNo;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     * 对话内容
     */
    @ApiModelProperty(value = "对话内容")
    private String content;
    /**
     * 通话时间
     */
    @ApiModelProperty(value = "通话时间")
    private LocalDateTime callTime;
    /**
     * 用户手机号
     */
    @ApiModelProperty(value = "用户手机号")
    private String userPhone;
    /**
     * 模型类型 0：通用模型
     */
    @ApiModelProperty(value = "模型类型")
    private AiModelType modelType;
    /**
     * 机器人版本号ID
     */
    @ApiModelProperty(value = "机器人版本号ID")
    private Long robotHistoryId;
    /**
     * 通话uuid
     */
    @ApiModelProperty(value = "uuid")
    private String uuid;
    
    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型")
    private String sourceType;
    
    /**
     * 对话标识符
     */
    @ApiModelProperty(value = "对话标识符：avaya_id或uuid或企微消息时间标识符")
    private String chatId;
}
