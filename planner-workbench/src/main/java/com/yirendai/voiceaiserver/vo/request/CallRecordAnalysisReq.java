package com.yirendai.voiceaiserver.vo.request;

import com.yirendai.workbench.vo.req.callcenter.CallRecordSearchReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="通话任务AI分析请求", description="通话任务AI分析请求")
public class CallRecordAnalysisReq extends CallRecordSearchReq {


    @ApiModelProperty(value = "场景ID集合")
    private List<Long> sceneIds;

    @ApiModelProperty(value = "任务名称")
    private String taskName;


    @ApiModelProperty(value = "plannerNoList" ,hidden = true)
    List<String> plannerNoList;

    @ApiModelProperty(value = "录音ID")
    private List<String> uuidList;

    @ApiModelProperty(value = "分析类型 1:客户ID，0:录音ID，默认0")
    private Integer analysisType = 0;

    @ApiModelProperty(value = "分析内容，多个用逗号分隔：1通话记录，2短信记录，3微信记录，4企微记录，5客户基本信息，6客户业务数据，7客户订单数据")
    private String analysisContent;

    @ApiModelProperty(value = "客户ID列表，当分析类型为1时使用")
    private List<String> customerIdList;

    //开始时间
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    //结束时间
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    //任务来源
    @ApiModelProperty(value = "任务来源,0-会话任务分析，1-通讯记录，2-公海列表，3-客户列表")
    private Integer sourcePage;
}
