package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "订单检索信息")
public class PlannerUserTypeOrderReq {

    @NotBlank(message = "宜人用户id不能为空")
    @ApiModelProperty(value = "宜人用户id", required = true)
    private String userId;

    @NotBlank(message = "资产类型不能为空")
    @ApiModelProperty(value = "资产类型（多种资产类型时英文逗号分隔） 1：D类 2：B类 3：大额001 4：小额001", required = true)
    private String assetType;
}
