package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * ai分析任务通话记录分析结果数量
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ai分析任务通话记录分析结果数量 ", description = "ai分析任务通话记录分析结果数量 ")
public class AiAnalysisTaskSceneResultCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分析结果ID")
    private Long id;

    @ApiModelProperty(value = "场景ID")
    private String idStr;

    @ApiModelProperty(value = "分析结果名称")
    private String resultName;

    @ApiModelProperty(value = "分析结果名称")
    private String name;

    @ApiModelProperty(value = "分析结果数量")
    private String resultCount;



}
