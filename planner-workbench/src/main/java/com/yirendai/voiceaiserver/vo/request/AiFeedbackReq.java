package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AiFeedbackReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 反馈状态，0=未反馈, 1=准确，2=不准确
     */
    @NotNull(message = "反馈状态不能为空")
    @ApiModelProperty("反馈状态，0=未反馈, 1=准确，2=不准确")
    private Integer feedbackStatus;
    /**
     * 反馈描述或备注
     */
    @ApiModelProperty("反馈描述或备注")
    private String feedbackDetails;
}
