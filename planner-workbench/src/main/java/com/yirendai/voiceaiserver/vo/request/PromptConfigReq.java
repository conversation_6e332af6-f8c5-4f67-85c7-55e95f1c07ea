package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(value = "提示词配置信息req")
public class PromptConfigReq extends BasePageReqNoLogin {
    @ApiModelProperty(value = "使用场景", required = true)
    @NotNull(message = "使用场景不能为空")
    private Integer scene;

    @ApiModelProperty(value = "提示词")
    private String prompt;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "模型")
    private String model;

    @ApiModelProperty(value = "温度")
    private Float temperature;

    @ApiModelProperty(value = "热词")
    private String hotWords;

    @ApiModelProperty(value = "降噪采样率")
    private Integer sampleRate;

    @ApiModelProperty(value = "第一轮相似度")
    private BigDecimal firstRoundDist;

    @ApiModelProperty(value = "第二轮相似度")
    private BigDecimal secondRoundDist;

    @ApiModelProperty(value = "降噪/音转文/声纹检测是否开启")
    private Boolean open;

    @ApiModelProperty(value = "查询开始时间")
    private String startTime;

    @ApiModelProperty(value = "查询结束时间")
    private String endTime;

    @ApiModelProperty(value = "片段切割长度（以秒为单位）")
    private Long segmentLong;
}
