package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "通话记录音转文结果状态")
public class ChatTranslateStatusVO {

    @ApiModelProperty(value = "消息id")
    private Long id;

    @ApiModelProperty(value = "处理状态 0:处理中 1:成功 2:失败")
    private Integer status;
}
