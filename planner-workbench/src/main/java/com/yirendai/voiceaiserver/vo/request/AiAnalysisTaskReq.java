package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.support.Query;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * ai分析任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AiAnalysisTask对象", description = "ai分析任务")
public class AiAnalysisTaskReq extends Query implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID") 
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @ApiModelProperty(value = "创建人名称")
    private Long createUserName;

    @ApiModelProperty(value = "创建时间开始")
    private LocalDateTime createTimeBeg;

    @ApiModelProperty(value = "创建时间结束")
    private LocalDateTime createTimeEnd;


    @ApiModelProperty(value = "状态 0未开始，1进行中，2已完成")
    private List<Integer> status;



}
