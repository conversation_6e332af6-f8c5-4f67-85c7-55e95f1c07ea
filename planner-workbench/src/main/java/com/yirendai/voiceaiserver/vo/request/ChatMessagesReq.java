package com.yirendai.voiceaiserver.vo.request;

import cn.hutool.core.map.MapUtil;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 发送对话消息Req
 */
@Data
public class ChatMessagesReq {

    private Map<String, Object> inputs = MapUtil.empty();

    @NotBlank
    private String query;

    @NotBlank
    private String response_mode;

    @NotBlank
    private String user;
}
