package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "企微理财师列表检索信息")
public class QwPlannerSearchReq extends BasePageReq {

    @NotBlank(message = "客户userId不能为空")
    @ApiModelProperty(value = "客户userId")
    private String userId;

    @ApiModelProperty(value = "理财师id")
    private String plannerName;

    @ApiModelProperty(value = "理财师姓名，支持模糊查询（左匹配）")
    private String plannerNo;

    @NotBlank(message = "功能位置不能为空")
    @ApiParam(value = "功能位置", required = true)
    private String position;
}
