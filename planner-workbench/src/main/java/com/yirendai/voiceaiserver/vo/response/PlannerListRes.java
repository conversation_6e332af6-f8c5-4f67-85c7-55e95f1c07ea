package com.yirendai.voiceaiserver.vo.response;

import com.yirendai.voiceaiserver.vo.db.PlannerAchievementInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 理财师信息列表Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "理财师信息分页检索信息")
public class PlannerListRes {

    @ApiModelProperty(value = "理财师信息列表")
    private List<PlannerAchievementInfo> plannerList;

    @ApiModelProperty(value = "是否有下一页")
    private Boolean haveNext;
}
