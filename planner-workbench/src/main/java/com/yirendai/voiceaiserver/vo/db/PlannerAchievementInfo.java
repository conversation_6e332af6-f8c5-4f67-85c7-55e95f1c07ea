package com.yirendai.voiceaiserver.vo.db;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 理财师D类业绩信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "理财师D类业绩信息")
public class PlannerAchievementInfo {

    @ApiModelProperty(value = "理财师id")
    private String plannerNo;

    @ApiModelProperty(value = "理财师姓名")
    private String plannerName;

    @ApiModelProperty(value = "名下保单总金额")
    private BigDecimal sumPolicyAmount;
}
