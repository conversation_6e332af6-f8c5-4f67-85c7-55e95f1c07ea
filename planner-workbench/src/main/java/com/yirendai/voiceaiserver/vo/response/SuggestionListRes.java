package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推荐话术列表Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("推荐话术列表Response")
public class SuggestionListRes {

    @ApiModelProperty("客户在投信息")
    private String userInvestmentInfo;

    @ApiModelProperty("推荐话术列表")
    private List<SuggestionInfo> contentList;
}
