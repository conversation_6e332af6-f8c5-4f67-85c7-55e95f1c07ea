package com.yirendai.voiceaiserver.vo.response;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 欢迎语
 */
@Data
public class WelComeVO {

    @ApiModelProperty(value = "欢迎语")
    private String welcome;
    @ApiModelProperty(value = "用户名")
    private String userName;
    @ApiModelProperty(value = "推荐问题")
    private List<String> recommendedQuestions;

}
