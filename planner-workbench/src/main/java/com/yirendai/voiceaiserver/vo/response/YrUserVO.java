package com.yirendai.voiceaiserver.vo.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Scrm宜人用户信息
 */
@Data
public class YrUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 宜人用户ID
     */
    private String yrUserId;

    /**
     * 宜人用户姓名
     */
    private String name;

    /**
     * 宜人用户性别 0:女 1:男
     */
    private Integer gender;

    /**
     * 宜人用户手机号
     */
    private String mobile;

    /**
     * 宜人注册时间
     */
    private Date registerTime;

    /**
     * 宜人用户年龄
     */
    private Integer age;

    /**
     * 企微uid
     */
    private String wxUserId;

    /**
     * 企微昵称
     */
    private String wxNickName;

    /**
     * 企微unionid
     */
    private String unionid;

    /**
     * 理财师工号
     */
    private String plannerNumber;

    /**
     * 理财师姓名
     */
    private String plannerName;

    /**
     * 理财师企微id
     */
    private String plannerWxUserId;

    /**
     * 理财师企微昵称
     */
    private String plannerWxNickName;

    /**
     * 理财师企微头像
     */
    private String plannerWxUserAvatar;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 宜人标签
     */
    private List<YrTagVO> tag;
}
