package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 获取批量推荐话术Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("获取批量推荐话术Request")
public class SuggestionBatchReq extends BaseReq {

    @NotNull(message = "客户userId不能为空")
    @ApiParam(value = "客户userId", required = true)
    private Long userId;

    @Min(value = 1, message = "count最小为1")
    @Max(value = 10, message = "count最大为10")
    @NotNull(message = "生成数量不能为空")
    @ApiParam(value = "生成数量", required = true)
    private Integer count;
}
