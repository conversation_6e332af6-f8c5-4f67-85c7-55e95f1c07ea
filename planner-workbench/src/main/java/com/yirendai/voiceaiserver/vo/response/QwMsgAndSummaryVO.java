package com.yirendai.voiceaiserver.vo.response;

import com.yirendai.workbench.entity.AiConversationWechatSummary;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "企微聊天记录&小结信息")
public class QwMsgAndSummaryVO {

    @ApiModelProperty(value = "企微小结标识，true表示本条数据是企微小结，false标识本条数据是企微聊天记录")
    private boolean summaryFlag;

    @ApiModelProperty(value = "企微消息")
    private AiPlannerChatContact msg;

    @ApiModelProperty(value = "企微消息")
    private AiConversationWechatSummary summary;
}
