package com.yirendai.voiceaiserver.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "提示词使用记录vo")
public class AiPromptRecordVo {
    @ApiModelProperty(value = "使用场景")
    private Integer scene;

    @ApiModelProperty(value = "提示词内容")
    private String prompt;

    @ApiModelProperty(value = "模型")
    private String model;

    @ApiModelProperty(value = "温度")
    private Float temperature;

    @ApiModelProperty(value = "系统提示词，为空取默认")
    private String systemPrompt;

    @ApiModelProperty(value = "热词")
    private String hotWords;

    @ApiModelProperty(value = "降噪采样率")
    private Integer sampleRate;

    @ApiModelProperty(value = "第一轮相似度")
    private BigDecimal firstRoundDist;

    @ApiModelProperty(value = "第二轮相似度")
    private BigDecimal secondRoundDist;

    @ApiModelProperty(value = "降噪/音转文/声纹检测是否开启")
    private Boolean open;

    @ApiModelProperty(value = "片段切割长度（以秒为单位）")
    private Long segmentLong;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}
