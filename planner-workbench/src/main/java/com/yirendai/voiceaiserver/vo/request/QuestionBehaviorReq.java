package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 答案行为Request
 */
@Data
@ApiModel("问题&答案行为Request")
public class QuestionBehaviorReq extends BaseReq {

    @NotBlank(message = "问题不可为空")
    @ApiModelProperty(value = "问题", required = true)
    private String question;

    @NotBlank(message = "答案不可为空")
    @ApiModelProperty(value = "答案", required = true)
    private String answer;

    @ApiModelProperty("关联内容id，可为总结id或相关推荐id")
    private Long relationId;

    @NotNull(message = "用户行为不可为空")
    @ApiModelProperty(value = "用户行为 1：点赞 2：踩 3：收藏 4：取消收藏", required = true)
    private Integer behavior;
}
