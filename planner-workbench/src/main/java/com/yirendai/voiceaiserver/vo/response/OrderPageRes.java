package com.yirendai.voiceaiserver.vo.response;

import com.yirendai.workbench.entity.AdsCsgDOrderDetailDf;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "用户订单列表信息")
public class OrderPageRes {

    @ApiModelProperty(value = "D类订单列表信息")
    private List<AdsCsgDOrderDetailDf> dList;

    @ApiModelProperty(value = "B类订单列表信息")
    private List<Map<String, Object>> bList;

    @ApiModelProperty(value = "大额001订单列表信息")
    private List<Map<String, Object>> bigList;

    @ApiModelProperty(value = "小额001订单列表信息")
    private List<Map<String, Object>> smallList;
}
