package com.yirendai.voiceaiserver.vo.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * ai分析场景分析结果选项配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AiAnalysisConfigSceneResult对象", description = "ai分析场景分析结果选项配置")
public class AiAnalysisConfigSceneResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "ai_analysis_config_scene表ID")
    private Long sceneId;

    @ApiModelProperty(value = "分析结果")
    private String resultName;

    @ApiModelProperty(value = "分析结果描述（提示词)")
    private String prompt;



}
