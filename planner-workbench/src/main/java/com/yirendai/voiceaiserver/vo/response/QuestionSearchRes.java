package com.yirendai.voiceaiserver.vo.response;

import com.yirendai.workbench.entity.AiQuestion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问题搜索Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("问题搜索返回信息")
public class QuestionSearchRes {

    @ApiModelProperty("问题信息列表")
    private List<AiQuestion> questionList;
}
