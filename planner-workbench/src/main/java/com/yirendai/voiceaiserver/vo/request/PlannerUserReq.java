package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "客户分页检索信息")
public class PlannerUserReq extends BasePageReqNoLogin {

    @ApiModelProperty(value = "宜人用户id")
    private String userId;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @NotNull(message = "理财师工号不能为空")
    @ApiModelProperty(value = "理财师工号", required = true)
    private String plannerNo;
}
