package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "企微聊天记录&小结信息Request")
public class QwMsgAndSummaryReq extends BasePageReq {

    @NotBlank(message = "宜人用户id不能为空")
    @ApiModelProperty(value = "宜人用户id", required = true)
    private String userId;

    @NotBlank(message = "理财师工号不能为空")
    @ApiModelProperty(value = "理财师工号", required = true)
    private String plannerNo;

    @NotBlank(message = "租户id")
    @ApiModelProperty(value = "租户id", required = true)
    private String tenantId;

    @ApiModelProperty(value = "开始时间（包含）", example = "2024-01-01 00:00:00")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间（包含）", example = "2024-08-01 00:00:00")
    private LocalDateTime endTime;

    @ApiModelProperty("是否查询小结，不传时默认查询")
    private Boolean needSummary;
}
