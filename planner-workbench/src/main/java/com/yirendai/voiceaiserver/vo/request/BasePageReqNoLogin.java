package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 分页对象（未登录）
 */
@Data
@ApiModel("分页对象（未登录）")
public class BasePageReqNoLogin {

    @ApiModelProperty(value = "当前页数", example = "1")
    private Integer current = 1;

    @ApiModelProperty(value = "每页展示条数", example = "10")
    @Min(value = 1, message = "size最小为1")
    private Integer size = 10;
}
