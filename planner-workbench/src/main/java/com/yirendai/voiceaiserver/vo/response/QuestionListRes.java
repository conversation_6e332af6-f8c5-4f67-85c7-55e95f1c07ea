package com.yirendai.voiceaiserver.vo.response;

import com.yirendai.workbench.entity.AiQuestion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问题列表Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("问题列表Response")
public class QuestionListRes {

    @ApiModelProperty("问题列表")
    private List<AiQuestion> questionList;
}
