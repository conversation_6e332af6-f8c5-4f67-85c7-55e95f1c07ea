package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * AI助手会话请求
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AI助手会话请求")
public class AiHelperMessageReq {
    @ApiModelProperty(value = "用户账号", hidden = true)
    private String account;
    @ApiModelProperty(value = "用户Id", hidden = true)
    private String userId;
    @ApiModelProperty(value = "租户ID", hidden = true)
    private String tenantId;
    @ApiModelProperty(value = "问题",required = true)
    private String query;
    @ApiModelProperty(value = "会话ID")
    private String conversationId;
    @ApiModelProperty(value = "场景: 201 默认场景 ,211 通话实时转语音场景,212 分析通话记录")
    private Integer scene;
    @ApiModelProperty(value = "通话记录ID集合 scene为212时必填")
    private List<Long> callRecordIds;


}