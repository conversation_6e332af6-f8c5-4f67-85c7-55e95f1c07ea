package com.yirendai.voiceaiserver.vo.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * AI分析任务通话记录分页返回对象（含统计信息）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AiAnalysisTaskCallRecordPageVO对象", description = "AI分析任务通话记录分页返回（含统计）")
public class AiAnalysisTaskCallRecordPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分页数据")
    private IPage<AiAnalysisTaskCallRecordVO> pageData;
}

