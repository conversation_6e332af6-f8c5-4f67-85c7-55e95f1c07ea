package com.yirendai.voiceaiserver.vo.request;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2023/11/13 17:06
 **/
@Data
@ApiModel(value = "合规机器人问答req")
public class ChatMessagesStreamReq extends BaseReq {

    @NotEmpty(message = "问题不能为空")
    @ApiModelProperty(value = "问题",required = true)
    private String query;
}
