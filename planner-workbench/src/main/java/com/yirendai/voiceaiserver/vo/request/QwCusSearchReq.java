package com.yirendai.voiceaiserver.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "企微客户列表检索信息")
public class QwCusSearchReq extends BaseReq {

    @ApiModelProperty(value = "客户userId（完全匹配)")
    private String userId;

    @ApiModelProperty(value = "客户姓名，支持模糊查询（左匹配）")
    private String userName;

    @ApiModelProperty(value = "起始客户userId（大于)")
    private String startUserId;

    @NotBlank(message = "功能位置不能为空")
    @ApiParam(value = "功能位置", required = true)
    private String position;

    @ApiModelProperty(value = "检索列表大小", example = "10")
    @Min(value = 1, message = "size最小为1")
    private Integer size = 10;
}
