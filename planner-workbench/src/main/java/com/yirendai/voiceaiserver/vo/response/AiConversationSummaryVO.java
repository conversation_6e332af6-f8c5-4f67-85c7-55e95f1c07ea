package com.yirendai.voiceaiserver.vo.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiConversationSummaryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 对话ID
     */
    @ApiModelProperty("对话ID")
    private Long chatContactId;
    /**
     * 理财师工号
     */
    @ApiModelProperty("理财师工号")
    private String plannerNo;
    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;
    /**
     * 理财师总结内容
     */
    @ApiModelProperty("理财师总结内容")
    private String summaryContentPlanner;
    /**
     * 用户总结内容
     */
    @ApiModelProperty("用户总结内容")
    private String summaryContentUser;
    /**
     * 用户内容是否有效，0-无效，1-有效
     */
    @ApiModelProperty("用户内容是否有效，0-无效，1-有效")
    private Integer userContentValid;
    /**
     * AI生成的置信度得分,没有默认1.00
     */
    @ApiModelProperty("AI生成的置信度得分,没有默认1.00")
    private BigDecimal confidenceScore;
    /**
     * 反馈状态，0=未反馈, 1=准确，2=不准确
     */
    @ApiModelProperty("反馈状态，0=未反馈, 1=准确，2=不准确")
    private Integer feedbackStatus;

    @ApiModelProperty("意向产品类型")
    private String intentionProducts;
}
