package com.yirendai.voiceaiserver.mq.listener;

import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.anno.LogTrace;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 通话预约处理消息
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "topic-planner-workbench-ai-robot-call-user-analysis-${rocketmq.suffix}", consumerGroup = "group_planner-workbench-ai-robot-call-user-analysis-${rocketmq.suffix}")
public class RobotCallUserAnalysisReceive implements RocketMQListener<String> {

    @Resource
    private AIConversationProcessService aiConversationProcessService;

    @LogTrace
    @Override
    public void onMessage(String message) {
        log.info("AI Conversation topic-planner-workbench-ai-robot-call-user-analysis Received message: {}", message);
        AiConversationProcessReq aiConversationProcessReq = new AiConversationProcessReq();
        MqBaseDto mqBaseDto = JsonUtil.parse(message, MqBaseDto.class);
        Long chatContactId = mqBaseDto.getChatContactId();
        aiConversationProcessReq.setTaskId(mqBaseDto.getTaskId());
        aiConversationProcessReq.setChatContactId(chatContactId);
        aiConversationProcessReq.setTaskType(AiConversationProcessTypeEnum.getByCode(mqBaseDto.getType()));
        aiConversationProcessService.submitTask(aiConversationProcessReq);
    }
}