package com.yirendai.voiceaiserver.mq.dto;

import com.yirendai.voiceaiserver.vo.response.AsrOnlineVO;
import com.yirendai.workbench.vo.res.WebSocketMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MqBaseDto {

    private Long chatContactId;

    /**
     * 开始处理时间，以秒为单位
     */
    private Long startTime;

    /**
     * 是否需要发起延时消息
     */
    private Boolean needSendDelay;

    private String taskId;

    /**
     * 类型
     */
    private Integer type;


    private String webSocketCode;


    private WebSocketMessage<?> webSocketMessage;
}
