package com.yirendai.voiceaiserver.mq.listener;

import com.alibaba.fastjson.JSON;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.vo.response.AsrOnlineVO;
import com.yirendai.workbench.enums.WebsocketMessageTypeEnum;
import org.apache.rocketmq.spring.annotation.MessageModel;
import com.yirendai.workbench.vo.res.WebSocketMessage;
import com.yirendai.workbench.websocket.WebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "topic-planner-workbench-webSocket-online-${rocketmq.suffix}",
        consumerGroup = "group_planner-workbench-webSocket-online-${rocketmq.suffix}",
        messageModel = MessageModel.BROADCASTING)
public class WebSocketOnlineReceive implements RocketMQListener<String> {

    @Override
    public void onMessage(String message) {
        log.info("WebSocketOnlineReceive Received message: {}", message);
        MqBaseDto mqBaseDto = JsonUtil.parse(message, MqBaseDto.class);
        WebSocketSession session = WebSocketHandler.sessionMap.get(mqBaseDto.getWebSocketCode());
        if (Objects.nonNull(session)) {
            log.info("WebSocketOnlineReceive message:{}", mqBaseDto.getWebSocketMessage());
            WebSocketMessage<?> webSocketMessage = mqBaseDto.getWebSocketMessage();
            try {
                session.sendMessage(new TextMessage(JSON.toJSONString(webSocketMessage)));
            } catch (IOException e) {
                log.error("WebSocketOnlineReceive 处理消息失败 message:{}", webSocketMessage, e);
            }
        }
    }
}
