package com.yirendai.voiceaiserver.mq.listener;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.service.CallCenterService;
import com.yirendai.workbench.anno.LogTrace;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 新呼叫中心消息实时音转文处理消息
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "topic-planner-workbench-ai-call-center-msg-translate-${rocketmq.suffix}",
        consumerGroup = "topic-planner-workbench-ai-call-center-msg-translate-${rocketmq.suffix}")
public class CallCenterMsgTranslateReceive implements RocketMQListener<String> {

    @Resource
    CallCenterService callCenterService;

    @Resource
    RedissonClient redissonClient;

    /**
     * 新呼叫中心消息实时音转文锁前缀
     */
    private static final String LOCK_CALL_CENTER_MSG_TRANSLATE = "voice:ai:server:call:center:msg:translate:%s:%s";

    @LogTrace
    @Override
    public void onMessage(String message) {
        log.info("[topic-planner-workbench-ai-call-center-msg-translate] Received message: {}", message);
        MqBaseDto mqBaseDto = JsonUtil.parse(message, MqBaseDto.class);
        RLock lock = null;
        try {
            if (Objects.isNull(mqBaseDto) || Objects.isNull(mqBaseDto.getChatContactId())
                    || Objects.isNull(mqBaseDto.getStartTime()) || Objects.isNull(mqBaseDto.getNeedSendDelay())) {
                throw new AiServerException(ResultCode.PARAM_INVALID);
            }

            lock = redissonClient.getLock(
                    String.format(LOCK_CALL_CENTER_MSG_TRANSLATE, mqBaseDto.getChatContactId(), mqBaseDto.getStartTime()));
            boolean lockFlag = lock.tryLock(10, 10, TimeUnit.MINUTES);
            if (!lockFlag) {
                throw new AiServerException(ResultCode.HANDING);
            }
            callCenterService.translate(mqBaseDto);
            log.info("[topic-planner-workbench-ai-call-center-msg-translate] handle success message: {}", message);
        } catch (Exception e) {
            log.error("[topic-planner-workbench-ai-call-center-msg-translate] handle message: {}, err: ", message, e);
        } finally {
            if (Objects.nonNull(lock) && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
