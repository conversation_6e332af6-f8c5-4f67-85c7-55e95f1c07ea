package com.yirendai.voiceaiserver.mq.send;

import lombok.AllArgsConstructor;
import lombok.Getter;


@AllArgsConstructor
@Getter
public enum TopicEnum {

    SUMMARY("topic-planner-workbench-ai-summary", "group_planner_workbench_ai_summary","通话总结处理消息"),

    USER_TAG("topic-planner-workbench-ai-user-tag", "group_planner_workbench_ai-user-tag","用户标签处理消息"),

    CONVERSATION_INSPECT("topic-planner-workbench-conversation-inspect", "group_planner_workbench_conversation-inspect","内容稽查处理消息"),

    CALL_RESERVATION("topic-planner-workbench-ai-call-reservation", "group_planner-workbench-ai-call-reservation","下次通话建议处理消息"),

    CONTENT_WECHAT_SUMMARY_TAG("topic-planner-workbench-ai-wechat-summary", "group_planner_workbench_ai_wechat-summary","企微小结标签处理消息"),

    CALL_CENTER_MSG_TRANSLATE("topic-planner-workbench-ai-call-center-msg-translate", "topic-planner-workbench-ai-call-center-msg-translate","新呼叫中心消息实时音转文处理消息"),

    ROBOT_CALL_USER_ANALYSIS("topic-planner-workbench-ai-robot-call-user-analysis", "group_planner-workbench-ai-robot-call-user-analysis","机器人通话用户分析处理消息"),

    // 默认对话处理topic
    DEFAULT_DIALOGUE_PROCESS_TOPIC("topic-planner-workbench-ai-dialogue-process", "group_planner-workbench-ai-dialogue-process","默认对话处理消息"),

    WEBSOCKET_ONLINE_TOPIC("topic-planner-workbench-webSocket-online", "group_planner-workbench-webSocket-online","webSocket实时处理消息"),
    ;
    private String topic;

    private String group;

    private String explain;

}
