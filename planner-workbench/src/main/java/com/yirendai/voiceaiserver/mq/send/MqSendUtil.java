package com.yirendai.voiceaiserver.mq.send;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class MqSendUtil {
    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private WebHookUtil webHookUtil;

    @Value("${rocketmq.suffix}")
    private String suffix;

    // 定义默认的延迟时间（以毫秒为单位）
    private static final List<Long> DELAY_LEVELS = Arrays.asList(
            TimeUnit.SECONDS.toMillis(1),
            TimeUnit.SECONDS.toMillis(5),
            TimeUnit.SECONDS.toMillis(10),
            TimeUnit.SECONDS.toMillis(30),
            TimeUnit.MINUTES.toMillis(1),
            TimeUnit.MINUTES.toMillis(2),
            TimeUnit.MINUTES.toMillis(3),
            TimeUnit.MINUTES.toMillis(4),
            TimeUnit.MINUTES.toMillis(5),
            TimeUnit.MINUTES.toMillis(6),
            TimeUnit.MINUTES.toMillis(7),
            TimeUnit.MINUTES.toMillis(8),
            TimeUnit.MINUTES.toMillis(9),
            TimeUnit.MINUTES.toMillis(10),
            TimeUnit.MINUTES.toMillis(20),
            TimeUnit.MINUTES.toMillis(30),
            TimeUnit.HOURS.toMillis(1),
            TimeUnit.HOURS.toMillis(2)
    );

    /**
     * 发送mq消息
     * @param topic
     * @param message
     */
    public void sendMessage(TopicEnum topic, MqBaseDto message) {
        String toJson =  JsonUtil.toJson(message);
        try{
            String topicName = topic.getTopic() + "-" + suffix;
            rocketMQTemplate.convertAndSend(topicName, toJson);
            log.info("【MQ_SEND】sendToMq {} success {} ",topic.getTopic(), toJson );
        }catch (Exception ex){
            log.warn("【MQ_SEND】sendToMqEX {} ex {}", toJson, ex.getMessage(), ex);
            webHookUtil.weChatMsgDev(MsgTypeEnum.MQ_SEND_ERROR,topic.getExplain()  ,"消息发送异常", ex);
        }

    }

    /**
     * 获取延迟级别
     * @param delayMillis 延迟时间，以毫秒为单位
     * @return 延迟级别
     */
    private int getDelayLevel(Long delayMillis) {
        for (int i = DELAY_LEVELS.size(); i > 0; i--) {
            if (DELAY_LEVELS.get(i - 1).compareTo(delayMillis) <= 0) {
                return i;
            }
        }
        return -1;
    }

    public void sendDelayMessage(TopicEnum topic, MqBaseDto message, Long delayMillis) {
        String toJson =  JsonUtil.toJson(message);
        try{
            String topicName = topic.getTopic() + "-" + suffix;
            int delayLevel = getDelayLevel(delayMillis);
            if (delayLevel < 1) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "未获取到延迟级别");
            }
            SendResult sendResult = rocketMQTemplate.syncSend(topicName, MessageBuilder.withPayload(toJson).build(),
                    rocketMQTemplate.getProducer().getSendMsgTimeout(), delayLevel);
            log.info("【MQ_SEND】sendDelayToMq={}, success={}, delay={}, msgId={}", topic.getTopic() + "-" + suffix, toJson, delayMillis, sendResult.getMsgId());
        }catch (Exception ex){
            log.error("【MQ_SEND】sendDelayToMq={}, delay={}, ex=", toJson, delayMillis, ex);
            webHookUtil.weChatMsgDev(MsgTypeEnum.MQ_SEND_ERROR, topic.getExplain(),"延时消息发送异常", ex);
        }
    }
}
