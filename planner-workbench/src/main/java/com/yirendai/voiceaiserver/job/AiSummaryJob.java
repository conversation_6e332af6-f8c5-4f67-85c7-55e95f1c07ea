package com.yirendai.voiceaiserver.job;

import com.yirendai.voiceaiserver.service.AiSummaryContentService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.workbench.anno.LogTrace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Slf4j
@Component
public class AiSummaryJob {

    @Resource
    private AiSummaryContentService aiSummaryContentService;

    @LogTrace
    @XxlJob("AiSummaryJob")
    public ReturnT<String> getAiSummary() {
        try {
            aiSummaryContentService.summaryContentFull();
            XxlJobHelper.log("获取聊天内容总结成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("获取聊天内容总结失败，失败原因为", e);
            XxlJobHelper.log("获取聊天内容总结失败");
            return ReturnT.FAIL;
        }
    }

    /**
     * 获取top理财师部分avaya的内容总结,开启定时任务时传入三个参数, "params[0],params[1],params[2]"
     *
     * 此定时任务只在测试验证阶段使用
     * @params params[0] 通话内容的最小长度,不需要指定长度时为-1
     * @params param[1] 通话内容的最大长度,不需要指定长度时为-1
     * @params params[2] 获取内容总结的数量
     * @return
     */
    @XxlJob("partAvayaSummaryJob")
    public ReturnT<String> handlePartAvayaSummary() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("获取聊天内容总结为{}", param);
        Integer[] params = Arrays.stream(param.split(",")).map(Integer::parseInt).toArray(Integer[]::new);
        try {
            log.info("开始获取top理财师部分avaya的内容总结, 内容最小长度为:{}, 内容最大长度为:{} ,数量为:{}",
                    params[0], params[1], params[2]);
            aiSummaryContentService.avayaSummaryContentPart(params[0], params[1], params[2]);
            XxlJobHelper.log("获取聊天内容总结成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("获取top理财师部分avaya的内容总结失败，失败原因为", e);
            XxlJobHelper.log("获取聊天内容总结失败");
            return ReturnT.FAIL;
        }
    }
}
