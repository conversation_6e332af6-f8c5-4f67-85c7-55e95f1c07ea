package com.yirendai.voiceaiserver.job;

import com.yirendai.voiceaiserver.service.AiChatMessageHandleService;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 图片,混合类型转文字任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ImageAndMixedToTextJob {

    @Resource
    AiChatMessageHandleService aiChatMessageHandleService;

    @XxlJob("ImageToTextJob")
    public ReturnT<String> handleImageToText() {
        try {
            aiChatMessageHandleService.pictureHandle();
            XxlJobHelper.log("图片转文字任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("图片转文字任务处理失败，失败原因为", e);
            XxlJobHelper.log("图片转文字任务处理失");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("ImageToTextSplitJob")
    public ReturnT<String> splitHandleImageToText() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("分批处理图片任务输入参数为{}", param);
        List<Pair<Long, Long>> list = new ArrayList<>();
        if(StrUtil.isNotEmpty(param)){
            String[] split = param.split(";");
            for (String str: split){
                String[] pairStr = str.split(",");
                list.add(Pair.of(Long.parseLong(pairStr[0]), Long.parseLong(pairStr[1])));
            }
        }
        for (Pair<Long, Long> pair : list) {
            aiChatMessageHandleService.pictureSplitHandle(pair.getKey(), pair.getValue());
        }
        XxlJobHelper.log("分批处理图片转文字任务处理成功");
        return ReturnT.SUCCESS;
    }

    @XxlJob("MixedToTextJob")
    public ReturnT<String> handleMixedToText() {
        try {
            aiChatMessageHandleService.mixedHandle();
            XxlJobHelper.log("混合类型转文字任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("混合类型转文字任务处理失败，失败原因为", e);
            XxlJobHelper.log("混合类型转文字任务处理失");
            return ReturnT.FAIL;
        }
    }
}
