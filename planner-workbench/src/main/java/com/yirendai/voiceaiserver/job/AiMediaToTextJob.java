package com.yirendai.voiceaiserver.job;

import com.yirendai.voiceaiserver.service.AiMediaService;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * 语料媒体文件转文字任务
 */
@Slf4j
@Component
public class AiMediaToTextJob {
    @Resource
    private AiMediaService aiMediaService;

    @XxlJob("AiMediaToTextJob")
    public ReturnT<String> handleMedias() {
        try {
            CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
                // 语音文件处理
                aiMediaService.audioHandle();
            });

            CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
                // 视频文件处理
                aiMediaService.videoHandle();
            });

            // 使用 CompletableFuture.allOf 等待两个任务都完成
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(task1, task2);
            // 当所有任务完成时，执行后续操作
            allTasks.thenRun(() -> log.info("AiMediaToTextJob tasks are completed"));
            // 阻塞主线程，等待所有任务完成
            allTasks.get();

            XxlJobHelper.log("语料数据媒体文件转文字任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("语料数据媒体文件转文字任务失败，原因为：{}", ExceptionUtil.getMessage(e), e);
            XxlJobHelper.log("语料数据媒体文件转文字任务处理失败");
            return ReturnT.FAIL;
        }
    }
}