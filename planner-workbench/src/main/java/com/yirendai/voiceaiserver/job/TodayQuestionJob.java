package com.yirendai.voiceaiserver.job;

import com.yirendai.voiceaiserver.enums.QuestionTypeEnum;
import com.yirendai.workbench.mapper.AiQuestionMapper;
import com.yirendai.workbench.entity.AiQuestion;
import com.yirendai.voiceaiserver.service.AiQuestionService;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.service.impl.ChatServiceImpl;
import com.yirendai.voiceaiserver.vo.response.QuestionAndAnswerInfo;
import com.yirendai.voiceaiserver.vo.response.QuestionAnswerInfo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取今日问题
 */
@Slf4j
@Component
public class TodayQuestionJob {

    /**
     * 问题数量
     */
    private static final Integer QUESTION_COUNT = 10;

    /**
     * 每个总结问题生成的问题数量
     */
    private static final Integer QUESTION_PER_COUNT = 1;

    /**
     * 答案数量
     */
    private static final Integer ANSWER_COUNT = 1;

    /**
     * 今日随机问题缓存key
     */
    public static final String KEY_RANDOM_QUESTION_AND_ANSWER_CACHE = "voice:ai:server:random:question:answer:cache";

    @Resource
    AiQuestionService aiQuestionService;
    @Resource
    AiQuestionMapper aiQuestionMapper;
    @Resource
    ChatService chatService;
    @Resource
    JedisCluster jedisCluster;

    @XxlJob("TodayQuestionJob")
    public ReturnT<String> getTodayQuestionList() {
        LambdaQueryWrapper<AiQuestion> topicWrapper = new LambdaQueryWrapper<>();
        topicWrapper.select(AiQuestion::getId).eq(AiQuestion::getType, QuestionTypeEnum.TOPIC.getCode());
        List<AiQuestion> topicList = aiQuestionService.list(topicWrapper);
        if (CollectionUtils.isEmpty(topicList)) {
            XxlJobHelper.log("获取今日问题失败:未查询到话题");
            return ReturnT.FAIL;
        }

        List<Long> topicIdList = topicList.stream().map(AiQuestion::getId).collect(Collectors.toList());
        LambdaQueryWrapper<AiQuestion> recommendationWrapper = new LambdaQueryWrapper<>();
        recommendationWrapper.select(AiQuestion::getId, AiQuestion::getParentId)
                .eq(AiQuestion::getType, QuestionTypeEnum.RECOMMENDATION.getCode()).in(AiQuestion::getParentId, topicIdList);
        List<AiQuestion> recommendationList = aiQuestionMapper.selectList(recommendationWrapper);
        if (CollectionUtils.isEmpty(recommendationList)) {
            XxlJobHelper.log("获取今日问题失败:未查询到相关推荐");
            return ReturnT.FAIL;
        }
        Map<Long, List<AiQuestion>> recommendationMap = recommendationList.stream().collect(Collectors.groupingBy(AiQuestion::getParentId));
        List<Long> recommendationIdList = new ArrayList<>();
        for (Long topicId : recommendationMap.keySet()) {
            Random random = new Random();
            int numbers = random.ints(0, recommendationMap.get(topicId).size()).distinct().limit(1)
                    .boxed().collect(Collectors.toList()).get(0);
            recommendationIdList.add(recommendationMap.get(topicId).get(numbers).getId());
        }

        LambdaQueryWrapper<AiQuestion> summaryWrapper = new LambdaQueryWrapper<>();
        summaryWrapper.select(AiQuestion::getId, AiQuestion::getParentId)
                .eq(AiQuestion::getType, QuestionTypeEnum.SUMMARY.getCode()).in(AiQuestion::getParentId, recommendationIdList);
        List<AiQuestion> summaryResList = aiQuestionMapper.selectList(summaryWrapper);
        if (CollectionUtils.isEmpty(summaryResList)) {
            XxlJobHelper.log("获取今日问题失败:未查询到总结");
            return ReturnT.FAIL;
        }
        Map<Long, List<AiQuestion>> summaryMap = summaryResList.stream().collect(Collectors.groupingBy(AiQuestion::getParentId));
        List<Long> summaryIdList = new ArrayList<>();
        for (Long recommendationId : summaryMap.keySet()) {
            Random random = new Random();
            int numbers = random.ints(0, summaryMap.get(recommendationId).size()).distinct().limit(1)
                    .boxed().collect(Collectors.toList()).get(0);
            summaryIdList.add(summaryMap.get(recommendationId).get(numbers).getId());
        }

        Collections.shuffle(summaryIdList);
        List<AiQuestion> summaryList = aiQuestionService.listByIds(summaryIdList.subList(0, Math.min(QUESTION_COUNT, summaryIdList.size())));

        try {
            List<QuestionAndAnswerInfo> resList = new ArrayList<>();
            for (AiQuestion question : summaryList) {
                String content = String.format(ChatServiceImpl.CHAT_GET_QUESTION_MSG,
                        ChatServiceImpl.CHINESE_NUMBERS[QUESTION_PER_COUNT - 1], question.getContext());
                List<String> questionList = chatService.chatQuestion(content, QUESTION_PER_COUNT, "JobAdmin");
                String questionContent = questionList.get(0);
                String answerContent = questionContent.endsWith("?") || questionContent.endsWith("？") ? questionContent : questionContent + "？";
                List<String> answerList = chatService.chatQuestion(
                        String.format(ChatServiceImpl.CHAT_GET_ANSWER_MSG, answerContent), ANSWER_COUNT, "JobAdmin");
                List<QuestionAnswerInfo> answerInfoList = answerList.stream()
                        .map(a -> QuestionAnswerInfo.builder().answer(a).build()).collect(Collectors.toList());
                resList.add(QuestionAndAnswerInfo.builder().question(questionContent)
                        .answerList(answerInfoList).relationId(question.getId()).build());
            }

            jedisCluster.setex(KEY_RANDOM_QUESTION_AND_ANSWER_CACHE, 24 * 60 * 60, JSON.toJSONString(resList));
            XxlJobHelper.log("获取今日问题处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("问题生成失败，原因为", e);
            XxlJobHelper.log("获取今日问题失败:问题或答案生成失败");
            return ReturnT.FAIL;
        }
    }
}