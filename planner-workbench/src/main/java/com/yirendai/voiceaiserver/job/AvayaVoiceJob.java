package com.yirendai.voiceaiserver.job;

import javax.annotation.Resource;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.TypeReference;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.voiceaiserver.service.impl.AvayaVoiceService;
import com.yirendai.voiceaiserver.vo.db.AvayaGroupVO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024/6/18 14:07
 **/
@Slf4j
@Component
public class AvayaVoiceJob {

    @Resource
    AvayaVoiceService avayaVoiceService;
    @Resource
    JedisCluster jedisCluster;
    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;

    private static final Pattern INTEGER_PATTERN = Pattern.compile("^-?\\d+$");

    public static final String KEY_AVAYA_SYNC_BATCH = "voice:ai:server:avaya:sync:batch";

    @XxlJob("syncAvayaVoice")
    public ReturnT<String> syncAvayaVoice() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("同步avaya语音任务输入参数为{}", param);
        if (StrUtil.isBlank(param) || !(INTEGER_PATTERN.matcher(param).matches())) {
            return ReturnT.FAIL;
        }
        int size = Integer.parseInt(param);
        if (size <= 0) {
            return ReturnT.FAIL;
        }

        String cache = jedisCluster.get(KEY_AVAYA_SYNC_BATCH);
        List<AvayaGroupVO> list = null;
        if (StrUtil.isBlank(cache)) {
            list = avayaVoiceService.getGroupsUser();
        } else {
            list = JSON.parseArray(cache, AvayaGroupVO.class);
            if (CollUtil.isEmpty(list)) {
                list = avayaVoiceService.getGroupsUser();
            }
        }
        List<AvayaGroupVO> handleList = list.subList(0, Math.min(list.size(), size));
        if (handleList.size() >= list.size()) {
            jedisCluster.del(KEY_AVAYA_SYNC_BATCH);
        } else {
            list = list.subList(handleList.size(), list.size());
            jedisCluster.setex(KEY_AVAYA_SYNC_BATCH, 24 * 60 * 60, JSON.toJSONString(list));
        }

        for (AvayaGroupVO group : handleList) {
            avayaVoiceService.syncAvayaVoice(group.getMinId().intValue(), group.getMaxId().intValue());
        }
        XxlJobHelper.log("同步avaya语音处理成功");
        return ReturnT.SUCCESS;
    }

    @XxlJob("avayaVoice2text")
    public ReturnT<String> avayaVoice2text() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("avaya语音音转文任务输入参数为{}", param);
        if (StrUtil.isBlank(param) || !(INTEGER_PATTERN.matcher(param).matches())) {
            return ReturnT.FAIL;
        }
        int size = Integer.parseInt(param);
        if (size <= 0) {
            return ReturnT.FAIL;
        }

        List<AvayaGroupVO> list = avayaVoiceService.getGroups();
        list = list.subList(0, Math.min(size, list.size()));
        for (AvayaGroupVO group : list) {
            avayaVoiceService.avayaVoice2text(group.getMinId(), group.getMaxId());
        }
        XxlJobHelper.log("avaya语音音转文处理成功");
        return ReturnT.SUCCESS;
    }

    @XxlJob("SplitAvayaTextJobNight")
    public ReturnT<String> splitTextJobNight() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("切割Avaya语料任务输入参数为{}", param);
            if (StrUtil.isBlank(param) || !(INTEGER_PATTERN.matcher(param).matches())) {
                return ReturnT.FAIL;
            }
            int size = Integer.parseInt(param);
            if (size <= 0) {
                return ReturnT.FAIL;
            }

            List<AvayaGroupVO> list = avayaVoiceService.getHandleList(size);
            for (AvayaGroupVO group : list) {
                avayaVoiceService.splitText(group.getMinId(), group.getMaxId(), true);
            }
            XxlJobHelper.log("切割Avaya语料处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("切割Avaya语料失败，失败原因为", e);
            XxlJobHelper.log("切割Avaya语料失败:获取待切割语料失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SplitAvayaTextJob")
    public ReturnT<String> splitTextJob() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("切割Avaya语料任务输入参数为{}", param);
            if (StrUtil.isBlank(param) || !(INTEGER_PATTERN.matcher(param).matches())) {
                return ReturnT.FAIL;
            }
            int size = Integer.parseInt(param);
            if (size <= 0) {
                return ReturnT.FAIL;
            }

            List<AvayaGroupVO> list = avayaVoiceService.getHandleList(size);
            for (AvayaGroupVO group : list) {
                avayaVoiceService.splitText(group.getMinId(), group.getMaxId(), false);
            }
            XxlJobHelper.log("切割Avaya语料处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("切割Avaya语料失败，失败原因为", e);
            XxlJobHelper.log("切割Avaya语料失败:获取待切割语料失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncAllAvayaJob")
    public ReturnT<String> syncAllAvaya() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("同步全量/增量Avaya语音任务输入参数为{}", param);
            if (StrUtil.isNotBlank(param) && !(INTEGER_PATTERN.matcher(param).matches())) {
                return ReturnT.FAIL;
            }
            if (StrUtil.isNotBlank(param)) { // 传参时同步全量avaya语料，初始同步时间为2020年12月1日，只能传1和2，机器只支持两个并发
                Integer start = Integer.valueOf(param);
                if (!start.equals(1) && !start.equals(2)) {
                    return ReturnT.FAIL;
                }
                avayaVoiceService.syncAllAvaya(start, null);
            } else { // 不传参时同步增量avaya语料，初始同步时间取自ai_planner_chat_contact表中avaya消息的最大消息时间
                log.info("开始查询avaya消息的最大消息时间");
                LocalDateTime startTime = iAiPlannerChatContactService.getAllMaxMsgTime();
                log.info("查询avaya消息的最大消息时间结束");
                avayaVoiceService.syncAllAvaya(null, startTime);
            }
            XxlJobHelper.log("同步全量/增量Avaya语音处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步全量/增量Avaya语音失败，失败原因为", e);
            XxlJobHelper.log("同步全量/增量Avaya语音失败:获取avaya语音记录失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncAvayaIdJob")
    public ReturnT<String> syncAvayaId() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("同步Avaya语音id任务输入参数为{}", param);
            if (StrUtil.isNotBlank(param) && !(INTEGER_PATTERN.matcher(param).matches())) {
                return ReturnT.FAIL;
            }
            avayaVoiceService.syncAvayaId(StrUtil.isBlank(param) ? null : Integer.valueOf(param));
            XxlJobHelper.log("同步Avaya语音id处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步Avaya语音id失败，失败原因为", e);
            XxlJobHelper.log("同步Avaya语音id失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncAvayaPhoneJob")
    public ReturnT<String> syncAvayaPhone() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("同步Avaya通话电话任务输入参数为{}", param);
            if (StrUtil.isNotBlank(param) && !(INTEGER_PATTERN.matcher(param).matches())) {
                return ReturnT.FAIL;
            }
            avayaVoiceService.syncAvayaPhone(StrUtil.isBlank(param) ? null : Integer.valueOf(param));
            XxlJobHelper.log("同步Avaya通话电话处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步Avaya通话电话失败，失败原因为", e);
            XxlJobHelper.log("同步Avaya通话电话失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("HandleAvayaLongTimeJob")
    public ReturnT<String> handleAvayaLongTime() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("处理Avaya语音时长任务输入参数为{}", param);
            if (StrUtil.isNotBlank(param) && !(INTEGER_PATTERN.matcher(param).matches())) {
                return ReturnT.FAIL;
            }
            avayaVoiceService.handleAvayaLongTime(StrUtil.isBlank(param) ? null : Integer.valueOf(param));
            XxlJobHelper.log("处理Avaya语音时长任务执行成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("处理Avaya语音时长失败，失败原因为", e);
            XxlJobHelper.log("处理Avaya语音时长执行失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("CompareSplitJob")
    public ReturnT<String> compareSplit() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("对比Avaya语料切割结果任务输入参数为{}", param);
            if (StrUtil.isNotBlank(param) && !(INTEGER_PATTERN.matcher(param).matches())) {
                return ReturnT.FAIL;
            }
            avayaVoiceService.compareSplit(Integer.valueOf(param), false);
            XxlJobHelper.log("对比Avaya语料切割结果任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("对比Avaya语料切割结果任务处理失败，失败原因为", e);
            XxlJobHelper.log("对比Avaya语料切割结果任务处理失败，失败原因为" + e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob("CompareSplitLongAvayaJob")
    public ReturnT<String> compareSplitLongAvaya() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("对比Avaya长音频切割结果任务输入参数为{}", param);
            if (StrUtil.isNotBlank(param) && !(INTEGER_PATTERN.matcher(param).matches())) {
                return ReturnT.FAIL;
            }
            avayaVoiceService.compareSplit(Integer.valueOf(param), true);
            XxlJobHelper.log("对比Avaya长音频切割结果任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("对比Avaya长音频切割结果任务处理失败，失败原因为", e);
            XxlJobHelper.log("对比Avaya长音频切割结果任务处理失败，失败原因为" + e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob("CompareSplitWithDesignatedPlannerJob")
    public ReturnT<String> compareSplitWithDesignatedPlanner() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("【指定理财师】对比Avaya语料切割结果任务输入参数为{}", param);
            if (StrUtil.isBlank(param)) {
                return ReturnT.FAIL;
            }
            JSONObject map = JSON.parseObject(param);
            if (Objects.isNull(map)) {
                return ReturnT.FAIL;
            }
            String plannerNos = map.getString("plannerNo");
            if (StrUtil.isBlank(plannerNos)) {
                return ReturnT.FAIL;
            }
            List<String> list = Arrays.stream(plannerNos.split(",")).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(list)) {
                return ReturnT.FAIL;
            }
            Integer count = map.getInteger("count");
            if (Objects.isNull(count)) {
                count = 100;
            }
            for (String plannerNo : list) {
                avayaVoiceService.compareSplitWithDesignatedPlanner(plannerNo, count, Boolean.FALSE);
            }
            XxlJobHelper.log("【指定理财师】对比Avaya语料切割结果任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("【指定理财师】对比Avaya语料切割结果任务处理失败，失败原因为", e);
            XxlJobHelper.log("【指定理财师】对比Avaya语料切割结果任务处理失败，失败原因为" + e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob("CompareSplitLongAvayaWithDesignatedPlannerJob")
    public ReturnT<String> compareSplitLongAvayaWithDesignatedPlanner() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("【指定理财师】对比Avaya长音频切割结果任务输入参数为{}", param);
            if (StrUtil.isBlank(param)) {
                return ReturnT.FAIL;
            }
            JSONObject map = JSON.parseObject(param);
            if (Objects.isNull(map)) {
                return ReturnT.FAIL;
            }
            String plannerNos = map.getString("plannerNo");
            if (StrUtil.isBlank(plannerNos)) {
                return ReturnT.FAIL;
            }
            List<String> list = Arrays.stream(plannerNos.split(",")).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(list)) {
                return ReturnT.FAIL;
            }
            Integer count = map.getInteger("count");
            if (Objects.isNull(count)) {
                count = 100;
            }
            for (String plannerNo : list) {
                avayaVoiceService.compareSplitWithDesignatedPlanner(plannerNo, count, Boolean.TRUE);
            }
            XxlJobHelper.log("【指定理财师】对比Avaya长音频切割结果任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("【指定理财师】对比Avaya长音频切割结果任务处理失败，失败原因为", e);
            XxlJobHelper.log("【指定理财师】对比Avaya长音频切割结果任务处理失败，失败原因为" + e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SplitWithAllCrmCustomerJob")
    public ReturnT<String> splitWithAllCrmCustomer() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务输入参数为{}", param);
            if (StrUtil.isBlank(param)) {
                return ReturnT.FAIL;
            }
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(param, dateFormatter);
            avayaVoiceService.splitWithAllCrmCustomer(localDate.atStartOfDay());
            XxlJobHelper.log("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务处理失败，失败原因为", e);
            XxlJobHelper.log("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务处理失败，失败原因为" + e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SplitAddJob")
    public ReturnT<String> splitAdd() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("切割增量Avaya语料任务输入参数为{}", param);
            if (StrUtil.isBlank(param)) {
                return ReturnT.FAIL;
            }
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(param, dateFormatter);
            avayaVoiceService.splitAdd(localDate.atStartOfDay());
            XxlJobHelper.log("切割增量Avaya语料任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("切割增量Avaya语料任务处理失败，失败原因为", e);
            XxlJobHelper.log("切割增量Avaya语料任务处理失败，失败原因为" + e);
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SplitTimePeriodJob")
    public ReturnT<String> splitTimePeriod() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("切割指定时间段内的Avaya语料任务输入参数为{}", param);
            if (StrUtil.isBlank(param)) {
                return ReturnT.FAIL;
            }

            Map<String, String> map = JSON.parseObject(param, new TypeReference<HashMap<String, String>>() {});
            String startDate = MapUtil.getStr(map, "startDate"), endDate = MapUtil.getStr(map, "endDate");
            if (StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
                return ReturnT.FAIL;
            }
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDateTime startTime = LocalDate.parse(startDate, dateFormatter).atStartOfDay(),
                    endTime = LocalDate.parse(endDate, dateFormatter).atStartOfDay();
            Integer size = MapUtil.getInt(map, "size", 10000);
            avayaVoiceService.splitTimePeriod(startTime, endTime, size);
            XxlJobHelper.log("切割指定时间段内的Avaya语料任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("切割指定时间段内的Avaya语料任务处理失败，失败原因为", e);
            XxlJobHelper.log("切割指定时间段内的Avaya语料任务处理失败，失败原因为" + e);
            return ReturnT.FAIL;
        }
    }
}
