package com.yirendai.voiceaiserver.job;

import javax.annotation.Resource;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.voiceaiserver.service.IAiContentService;
import com.yirendai.workbench.anno.LogTrace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AiContentJob {
    @Resource
    private IAiContentService apiContentService;

    @LogTrace
    @XxlJob("syncContentNews")
    public ReturnT<String> syncContentNews() {
        try {
            apiContentService.syncContentNews();

            XxlJobHelper.log("同步新闻处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步新闻处理失败，原因为：{}", ExceptionUtil.getMessage(e), e);
            XxlJobHelper.log("同步新闻处理失败");
            return ReturnT.FAIL;
        }
    }
}