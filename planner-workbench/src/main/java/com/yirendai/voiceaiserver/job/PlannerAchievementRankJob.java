package com.yirendai.voiceaiserver.job;

import com.yirendai.voiceaiserver.service.CallCenterService;
import com.yirendai.workbench.service.AdsCsgDOrderDetailDfService;
import com.yirendai.voiceaiserver.service.impl.AvayaVoiceService;
import com.yirendai.voiceaiserver.vo.db.PlannerAchievementInfo;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 获取理财师业绩排名
 */
@Slf4j
@Component
public class PlannerAchievementRankJob {

    /**
     * 理财师D类业绩排名缓存key
     */
    public static final String KEY_PLANNER_ACHIEVEMENT_RANK = "voice:ai:server:planner:achievement:rank";

    @Resource
    JedisCluster jedisCluster;
    @Resource
    AdsCsgDOrderDetailDfService adsCsgDOrderDetailDfService;
    @Resource
    AvayaVoiceService avayaVoiceService;
    @Resource
    CallCenterService callCenterService;

    @XxlJob("PlannerAchievementRankJob")
    public ReturnT<String> getPlannerAchievementRank() {
        try {
            log.info("获取理财师业绩排名开始");
            List<PlannerAchievementInfo> orderList = adsCsgDOrderDetailDfService.getPlannerAchievements();
            List<PlannerAchievementInfo> avayaList = avayaVoiceService.getPlannerList();
            List<PlannerAchievementInfo> callCenterList = callCenterService.getPlannerList();

            List<PlannerAchievementInfo> resList = new ArrayList<>();
            Map<String, Boolean> plannerMap = new HashMap<>();
            if (CollUtil.isNotEmpty(orderList)) {
                resList.addAll(orderList);
                plannerMap = orderList.stream().collect(Collectors.toMap(PlannerAchievementInfo::getPlannerNo, p -> Boolean.TRUE));
            }
            if (CollUtil.isNotEmpty(avayaList)) {
                for (PlannerAchievementInfo planner : avayaList) {
                    if (!plannerMap.containsKey(planner.getPlannerNo())) {
                        resList.add(planner);
                        plannerMap.put(planner.getPlannerNo(), Boolean.TRUE);
                    }
                }
            }
            if (CollUtil.isNotEmpty(callCenterList)) {
                for (PlannerAchievementInfo planner : callCenterList) {
                    if (!plannerMap.containsKey(planner.getPlannerNo())) {
                        resList.add(planner);
                    }
                }
            }
            if (CollUtil.isNotEmpty(resList)) {
                jedisCluster.setex(KEY_PLANNER_ACHIEVEMENT_RANK, 2 * 24 * 60 * 60, JSON.toJSONString(resList));
                XxlJobHelper.log("获取理财师业绩排名成功");
                log.info("获取理财师业绩排名结束");
                return ReturnT.SUCCESS;
            } else {
                XxlJobHelper.log("今日理财师业绩列表为空");
                log.error("获取理财师业绩排名结束，列表为空");
                return ReturnT.FAIL;
            }
        } catch (Exception e) {
            log.error("获取理财师业绩排名失败，原因为", e);
            XxlJobHelper.log("获取理财师业绩排名失败");
            return ReturnT.FAIL;
        }
    }
}
