package com.yirendai.voiceaiserver.job;

import com.yirendai.voiceaiserver.service.AiIntentService;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 客户会话意图分析
 * 打标签
 */
@Slf4j
@Component
public class AiMsgTagJob {
    @Resource
    private AiIntentService aiIntentService;

    @XxlJob("AiMsgTagJob")
    public ReturnT<String> handleMsgTag() {
        try {
            aiIntentService.chatIntentHandle();

            XxlJobHelper.log("客户会话意图分析打标任务处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("客户会话意图分析打标任务失败，原因为：{}", ExceptionUtil.getMessage(e), e);
            XxlJobHelper.log("客户会话意图分析打标任务处理失败");
            return ReturnT.FAIL;
        }
    }
}