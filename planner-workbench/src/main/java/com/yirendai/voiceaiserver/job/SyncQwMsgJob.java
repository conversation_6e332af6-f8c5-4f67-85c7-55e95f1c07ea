package com.yirendai.voiceaiserver.job;

import cn.hutool.core.util.StrUtil;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 同步企微消息Job
 */
@Slf4j
@Component
public class SyncQwMsgJob {

    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;

    @XxlJob("SyncQwMsgJob")
    public ReturnT<String> syncQwMsgJob() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("同步企微消息任务输入参数为{}", param);
            if (StrUtil.isNotBlank(param) && !"0".equals(param) && !"1".equals(param)) {
                return ReturnT.FAIL;
            }
            iAiPlannerChatContactService.syncQwMsg("1".equals(param));
            XxlJobHelper.log("同步企微消息处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步企微消息失败，失败原因为", e);
            XxlJobHelper.log("同步企微消息失败：查询客户与理财师关系失败或查询待同步消息发生异常");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncQwMsgWithTimeJob")
    public ReturnT<String> syncQwMsgWithTime() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("按照时间同步企微消息任务输入参数为{}", param);
            if (StrUtil.isNotBlank(param) && !"0".equals(param) && !"1".equals(param)) {
                return ReturnT.FAIL;
            }
            iAiPlannerChatContactService.syncQwMsgWithTime("1".equals(param));
            XxlJobHelper.log("按照时间同步企微消息处理成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("按照时间同步企微消息失败，失败原因为", e);
            XxlJobHelper.log("按照时间同步企微消息失败");
            return ReturnT.FAIL;
        }
    }
}
