package com.yirendai.voiceaiserver.job;

import com.yirendai.workbench.service.AdsCsgDOrderDetailDfService;
import com.yirendai.voiceaiserver.service.impl.AvayaVoiceService;
import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 同步理财师&客户关系
 */
@Slf4j
@Component
public class SyncPlannerUserJob {

    @Resource
    AdsCsgDOrderDetailDfService adsCsgDOrderDetailDfService;
    @Resource
    AvayaVoiceService avayaVoiceService;
    @Resource
    IAiPlannerTopUserService iAiPlannerTopUserService;

    @XxlJob("SyncPlannerUserWithOrderJob")
    public ReturnT<String> syncPlannerUserWithOrder() {
        try {
            adsCsgDOrderDetailDfService.syncPlannerUserWithOrder();
            XxlJobHelper.log("同步理财师&客户关系（带订单信息）成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步理财师&客户关系（带订单信息）失败，原因为", e);
            XxlJobHelper.log("同步理财师&客户关系（带订单信息）失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncPlannerUserJob")
    public ReturnT<String> syncPlannerUser() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("同步理财师&客户关系任务输入参数为{}", param);
            Boolean isInit = Boolean.FALSE;
            if (StrUtil.isNotBlank(param) && "1".equals(param)) {
                isInit = Boolean.TRUE; // 只有传入参数1时才认为是初始同步
            }
            adsCsgDOrderDetailDfService.syncPlannerUser(isInit);
            XxlJobHelper.log("同步理财师&客户关系成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步理财师&客户关系失败，原因为", e);
            XxlJobHelper.log("同步理财师&客户关系失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncPlannerUserWithAvayaJob")
    public ReturnT<String> syncPlannerUserWithAvayaJob() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("同步理财师&客户关系（来自avaya语音记录）任务输入参数为{}", param);
            Boolean isInit = Boolean.FALSE;
            if (StrUtil.isNotBlank(param) && "1".equals(param)) {
                isInit = Boolean.TRUE; // 只有传入参数1时才认为是初始同步
            }
            avayaVoiceService.syncPlannerUserWithAvaya(isInit);
            XxlJobHelper.log("同步理财师&客户关系（来自avaya语音记录）成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步理财师&客户关系（来自avaya语音记录）失败，原因为", e);
            XxlJobHelper.log("同步理财师&客户关系（来自avaya语音记录）失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncUserNameJob")
    public ReturnT<String> syncUserName() {
        try {
            iAiPlannerTopUserService.syncUserName();
            XxlJobHelper.log("同步客户姓名成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步客户姓名失败，原因为", e);
            XxlJobHelper.log("同步客户姓名失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncUserUnionIdJob")
    public ReturnT<String> syncUserUnionId() {
        try {
            iAiPlannerTopUserService.syncUserUnionId();
            XxlJobHelper.log("同步客户unionId成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步客户unionId失败，原因为", e);
            XxlJobHelper.log("同步客户unionId失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("SyncPlannerQwNoJob")
    public ReturnT<String> syncPlannerQwNo() {
        try {
            adsCsgDOrderDetailDfService.syncPlannerQwNo();
            XxlJobHelper.log("同步理财师企微号成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步理财师企微号失败，原因为", e);
            XxlJobHelper.log("同步理财师企微号失败");
            return ReturnT.FAIL;
        }
    }
}
