package com.yirendai.voiceaiserver.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.voiceaiserver.biz.AiAnalysisTaskBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * AI分析任务定时
 */
@Slf4j
@Component
public class AiAnalysisTaskJob {

    @Autowired
    private AiAnalysisTaskBiz aiAnalysisTaskBiz;


    /**
     * XXL-JOB 定时任务入口
     * 执行AI分析任务
     */
    @XxlJob("aiAnalysisTaskJobHandler")
    public void aiAnalysisTaskJobHandler() {
        log.info("执行AI分析任务...");
        List<String> tenantIdList = aiAnalysisTaskBiz.selectTenantId();
        if (CollectionUtils.isEmpty(tenantIdList)) {
            log.info( "没有需要执行AI分析任务...");
            return;
        }
        // 循环租户分别执行执行AI分析任务
        for (String tenantId : tenantIdList) {
            log.info("开始执行租户：{} 的执行AI分析任务...", tenantId);
            aiAnalysisTaskBiz.execute(tenantId);
            log.info("执行租户：{} 的执行AI分析任务完成...", tenantId);
        }
        log.info("执行AI分析任务完成...");
    }

    @XxlJob("aiAnalysisCustomerTaskJobHandler")
    public void aiAnalysisCustomerTaskJobHandler() {
        log.info("执行AI客户分析任务...");
        List<String> tenantIdList = aiAnalysisTaskBiz.selectTenantId();
        if (CollectionUtils.isEmpty(tenantIdList)) {
            log.info( "没有客户需要执行AI分析任务...");
            return;
        }
        // 循环租户分别执行执行AI分析任务
        for (String tenantId : tenantIdList) {
            log.info("开始执行租户：{} 的执行AI客户分析任务...", tenantId);
            aiAnalysisTaskBiz.executeCustomer(tenantId);
            log.info("执行租户：{} 的执行AI客户分析任务完成...", tenantId);
        }
        log.info("执行AI客户分析任务完成...");
    }
}
