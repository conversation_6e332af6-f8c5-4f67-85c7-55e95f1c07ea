package com.yirendai.voiceaiserver.job;

import javax.annotation.Resource;

import com.yirendai.voiceaiserver.service.impl.QwCallService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.workbench.anno.LogTrace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @time 2024/6/19 09:30
 **/
@Slf4j
@Component
public class QwCallJob {

    @Resource
    QwCallService qwCallService;

    @LogTrace
    @XxlJob("splitQwCall")
    public ReturnT<String> splitQwCall() {
        qwCallService.splitQwCall();
        return ReturnT.SUCCESS;
    }
}
