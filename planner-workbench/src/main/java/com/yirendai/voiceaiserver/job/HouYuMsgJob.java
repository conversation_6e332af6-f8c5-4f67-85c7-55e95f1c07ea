package com.yirendai.voiceaiserver.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.voiceaiserver.service.HouYuMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Slf4j
@Component
public class HouYuMsgJob {

    @Resource
    HouYuMsgService houYuMsgService;

    @XxlJob("syncHouYuVideo")
    public ReturnT<String> syncVideo() {
        try {
            houYuMsgService.syncVideo();
            XxlJobHelper.log("同步厚予通话音频文件成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步厚予通话音频文件失败，原因为", e);
            XxlJobHelper.log("同步厚予通话音频文件失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("syncHouYuRecord")
    public ReturnT<String> syncRecord() {
        try {
            houYuMsgService.syncRecord();
            XxlJobHelper.log("同步厚予通话记录成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步厚予通话记录失败，原因为", e);
            XxlJobHelper.log("同步厚予通话记录失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("translateHouYuVideo")
    public ReturnT<String> translateVideo() {
        try {
            houYuMsgService.translateVideo();
            XxlJobHelper.log("厚予历史音频音转文成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("厚予历史音频音转文失败，原因为", e);
            XxlJobHelper.log("厚予历史音频音转文失败");
            return ReturnT.FAIL;
        }
    }
}
