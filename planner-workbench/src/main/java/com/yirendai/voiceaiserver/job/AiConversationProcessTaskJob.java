package com.yirendai.voiceaiserver.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Strings;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.robot.enums.RobotResult;
import com.yirendai.robot.enums.YesOrNoEnum;
import com.yirendai.robot.exception.RobotException;
import com.yirendai.robot.modules.call.entity.RobotCallTasks;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.mapper.RobotCallTasksCustomerMapper;
import com.yirendai.robot.modules.call.service.IRobotCallTasksService;
import com.yirendai.robot.modules.robot.entity.AiRobotDialogNode;
import com.yirendai.robot.modules.robot.entity.RobotHistory;
import com.yirendai.robot.modules.robot.mapper.RobotHistoryMapper;
import com.yirendai.robot.modules.robot.service.AiRobotDialogNodeService;
import com.yirendai.robot.modules.session.entity.RobotSession;
import com.yirendai.robot.modules.session.service.IRobotSessionService;
import com.yirendai.robot.util.RobotFormatUtil;
import com.yirendai.voiceaiserver.enums.AiConversationDataSourceEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.mq.send.MqSendUtil;
import com.yirendai.voiceaiserver.mq.send.TopicEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.service.AiConversationWechatSummaryService;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import com.yirendai.workbench.anno.LogTrace;
import com.yirendai.workbench.entity.AiConversationChatSummary;
import com.yirendai.workbench.entity.AiConversationProcessTask;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.CallCenterWechatMessage;
import com.yirendai.workbench.entity.callcenter.CallcenterWechatCustomerMapping;
import com.yirendai.workbench.service.IAiConversationChatSummaryService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.workbench.service.ICallCenterWechatMessageService;
import com.yirendai.workbench.service.callcenter.ICallcenterWechatCustomerMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RefreshScope
@Component
public class AiConversationProcessTaskJob {

    @Resource
    private AiConversationProcessTaskService aiConversationProcessTaskService;
    @Resource
    private AIConversationProcessService aiConversationProcessService;
    @Resource
    private IAiPlannerChatContactService aiPlannerChatContactService;
    @Resource
    private MqSendUtil mqSendUtil;
    @Resource
    private AiConversationWechatSummaryService aiConversationWechatSummaryService;
    @Resource
    private WebHookUtil webHookUtil;
    @Resource
    private RobotCallTasksCustomerMapper robotCallTasksCustomerMapper;
    @Resource
    private IRobotSessionService robotSessionService;
    @Resource
    private IRobotCallTasksService robotCallTasksService;
    @Resource
    private RobotHistoryMapper robotHistoryMapper;
    @Resource
    private AiRobotDialogNodeService aiRobotDialogNodeService;
    @Resource
    private ICallCenterWechatMessageService callCenterWechatMessageService;
    @Resource
    private ICallcenterWechatCustomerMappingService callcenterWechatCustomerMappingService;
    @Resource
    private IAiConversationChatSummaryService aiConversationChatSummaryService;

    @Value("${third.access.tenantId.kaishi}")
    private String tenantIdKaishi;

    @Value("${third.access.tenantId.yiren}")
    private String tenantIdYiren;

    @LogTrace
    @XxlJob("aiConversationProcessTask")
    public void retryTaskJobHandler() {
        // 获取最久的四条失败的任务
        List<AiConversationProcessTask> pendingTasks = aiConversationProcessTaskService.fetchFailedProcessTasks();
        log.info("XXL-Job aiConversationProcessTask pendingTasks size:{}", pendingTasks.size());
        if (pendingTasks.isEmpty()) {
            return;
        }
        for (AiConversationProcessTask entity : pendingTasks) {
            if (Objects.isNull(entity)) {
                log.error("XXL-Job aiConversationProcessTask entity is null");
                continue;
            }
            Integer taskType = entity.getTaskType();
            String taskId = entity.getTaskId();
            Long chatContactId = aiConversationProcessTaskService.extractChatContactFromTaskId(taskId);
            if (Objects.isNull(chatContactId)) {
                log.error("XXL-Job aiConversationProcessTask chatContactId is null, taskId:{}", taskId);
                continue;
            }
            MqBaseDto mqBaseDto = new MqBaseDto();
            mqBaseDto.setChatContactId(chatContactId);
            mqBaseDto.setType(taskType);
            mqBaseDto.setTaskId(taskId);
            AiConversationProcessTypeEnum typeEnum = AiConversationProcessTypeEnum.getByCode(taskType);
            log.info("XXL-Job aiConversationProcessTask send message on chatContactId:{}, taskType:{}", chatContactId, taskType);
            mqSendUtil.sendMessage(typeEnum.getMqTopic(), mqBaseDto);
        }
    }

    /**
     * 查漏ai对话处理任务
     */
    @XxlJob("aiConversationProcessTaskLeakyData")
    public void checkLeakyDataTaskJobHandler() {
//        String processType = XxlJobHelper.getJobParam();
//        log.info("XXL-Job aiConversationProcessTaskLeakyData processType:{}", processType);
//        aiConversationProcessTaskLeakyData(Integer.valueOf(processType));
//        aiConversationProcessTaskService.checkLeakyDataTask(0);
        aiConversationProcessTaskService.checkLeakyDataTask(9, tenantIdKaishi);
    }

    public int aiConversationProcessTaskLeakyData(Integer processType, String tenantId) {
        return aiConversationProcessTaskService.checkLeakyDataTask(processType, tenantId);
    }

    @XxlJob("aiRobotCallTaskCustomerTts")
    public void aiRobotCallTaskCustomerTts() {
        String ids = XxlJobHelper.getJobParam();
        log.info("XXL-Job aiRobotCallTaskCustomerTts processTaskIds:{}", ids);
        if (Strings.isNullOrEmpty(ids)) {
            log.info("XXL-Job aiRobotCallTaskCustomerTts processTaskIds is null");
            return;
        }
        List<Long> taskIds =  Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        LambdaQueryWrapper<RobotCallTasksCustomer> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RobotCallTasksCustomer::getTaskId, taskIds);
        wrapper.eq(RobotCallTasksCustomer::getIsTest, YesOrNoEnum.NO.getValue());
        List<RobotCallTasksCustomer> list = robotCallTasksCustomerMapper.selectList(wrapper);
        log.info("XXL-Job aiRobotCallTaskCustomerTts list size:{}", list.size());
        if (list.isEmpty()) {
            log.info("XXL-Job aiRobotCallTaskCustomerTts list is empty");
            return;
        }
        for (RobotCallTasksCustomer robotCallTasksCustomer : list) {
            Long taskId = robotCallTasksCustomer.getTaskId();
            if (Objects.isNull(taskId)) {
                log.info("XXL-Job aiRobotCallTaskCustomerTts taskId is null");
                continue;
            }
            RobotCallTasks callTasks = robotCallTasksService.getById(taskId);
            if (callTasks == null) {
                log.info("XXL-Job aiRobotCallTaskCustomerTts callTasks is null");
                continue;
            }
            LambdaQueryWrapper<RobotHistory> historyWrapper = new LambdaQueryWrapper<>();
            historyWrapper.eq(RobotHistory::getRobotId, callTasks.getRobotId());
            historyWrapper.eq(RobotHistory::getVersion, callTasks.getRobotVersion());
            historyWrapper.last("limit 1");
            RobotHistory robotHistory = robotHistoryMapper.selectOne(historyWrapper);
            if (robotHistory == null) {
                log.info("XXL-Job aiRobotCallTaskCustomerTts robotHistory is null");
                continue;
            }
            List<AiRobotDialogNode> robotDialogNodes = aiRobotDialogNodeService.getByHistoryRobotId(robotHistory.getId());
            if (robotDialogNodes == null || robotDialogNodes.isEmpty()) {
                log.info("XXL-Job aiRobotCallTaskCustomerTts robotDialogNodes is null");
                continue;
            }
            Map<String, Object> variablesMap = RobotFormatUtil.parseStringToMap(robotCallTasksCustomer.getVariables());
            if (variablesMap.isEmpty()) {
                log.info("XXL-Job aiRobotCallTaskCustomerTts variablesMap is null");
                continue;
            }
            robotSessionService.buildStaticVariable(variablesMap, robotDialogNodes, robotHistory, RobotFormatUtil.generateCallTtsUuid());
        }
    }

    /**
     * 同步消息初始化入库AI企微小结
     */
    @XxlJob("aiConversationWechatSummaryInit")
    public void retryWechatSummaryJobHandler() {
        log.info("XXL-Job aiConversationWechatSummaryInit start");
        // 获取需要执行的对话处理任务
        String jobParam = XxlJobHelper.getJobParam();
        log.info("XXL-Job aiConversationWechatSummaryInit jobParam:{}", jobParam);
        aiConversationWechatSummaryInit(jobParam);
    }

    public void aiConversationWechatSummaryInit(String date) {
        Map<String, List<AiPlannerChatContact>> userPlanerDayChatMap = aiPlannerChatContactService.fetchUserPlanerDayChat(date);
        log.info("AI Conversation Wechat Summary userPlanerDayChatMap size:{}", userPlanerDayChatMap.size());
        if (userPlanerDayChatMap.isEmpty()) {
            return;
        }
        int successCount = 0;
        int failCount = 0;
        for (Map.Entry<String, List<AiPlannerChatContact>> entry : userPlanerDayChatMap.entrySet()) {
            List<AiPlannerChatContact> chatContactList = entry.getValue();
            if (chatContactList.isEmpty()) {
                continue;
            }
            try {
                Boolean b = aiConversationWechatSummaryService.initConversationWechatSummary(chatContactList);
                if (b) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Throwable e) {
                failCount++;
                log.error("XXL-Job aiConversationWechatSummaryInit error", e);
            }
        }
        log.info("XXL-Job aiConversationWechatSummaryInit end, successCount:{}, failCount:{}", successCount, failCount);
        if (failCount > 0) {
            webHookUtil.weChatMsgDev(MsgTypeEnum.QW_MESSAGE_SUMMARY_INIT_ERROR, "无效任务数:" + failCount + "，成功任务数:" + successCount);
        }
    }

    /**
     * AI待处理任务处理，-1的任务跑起来
     */
    @XxlJob("aiConversationWechatSummaryProcess")
    public void processWechatSummaryJobHandler() {
        log.info("XXL-Job aiConversationWechatSummaryProcess start");
        // 获取需要执行的对话处理任务
        List<AiConversationProcessTask> pendingTasks = aiConversationProcessTaskService.fetchPendingWechatProcessTasks(AiConversationProcessStatusEnum.INIT_WAITING);
        log.info("XXL-Job aiConversationWechatSummaryProcess pendingTasks size:{}", pendingTasks.size());
        if (pendingTasks.isEmpty()) {
            return;
        }
        for (AiConversationProcessTask entity : pendingTasks) {
            if (Objects.isNull(entity)) {
                log.error("XXL-Job aiConversationWechatSummaryProcess entity is null");
                continue;
            }
            String taskId = entity.getTaskId();
            if (Strings.isNullOrEmpty(taskId)) {
                log.error("XXL-Job aiConversationWechatSummaryProcess taskId is null");
                continue;
            }
            MqBaseDto mqBaseDto = new MqBaseDto();
            mqBaseDto.setTaskId(taskId);
            Long chatContactId = aiConversationProcessTaskService.extractChatContactFromTaskId(taskId);
            if (Objects.isNull(chatContactId)) {
                log.error("XXL-Job aiConversationWechatSummaryProcess chatContactId is null, taskId:{}", taskId);
                continue;
            }
            mqBaseDto.setChatContactId(chatContactId);
            mqBaseDto.setType(entity.getTaskType());
            log.info("XXL-Job aiConversationWechatSummaryProcess send message on taskId:{}", taskId);
            mqSendUtil.sendMessage(TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC, mqBaseDto);
        }
        log.info("XXL-Job aiConversationWechatSummaryProcess end");
    }

    /**
     * AI待处理任务处理，-2的任务跑起来
     */
    @XxlJob("aiConversationLowTaskProcess")
    public void aiConversationLowTaskJobHandler() {
        log.info("XXL-Job aiConversationLowTaskProcess start");
        // 获取需要执行的对话处理任务
        List<AiConversationProcessTask> pendingTasks = aiConversationProcessTaskService.fetchPendingWechatProcessTasks(AiConversationProcessStatusEnum.LOW_PRIORITY_INIT);
        log.info("XXL-Job aiConversationLowTaskProcess pendingTasks size:{}", pendingTasks.size());
        if (pendingTasks.isEmpty()) {
            return;
        }
        for (AiConversationProcessTask entity : pendingTasks) {
            if (Objects.isNull(entity)) {
                log.error("XXL-Job aiConversationLowTaskProcess entity is null");
                continue;
            }
            String taskId = entity.getTaskId();
            if (Strings.isNullOrEmpty(taskId)) {
                log.error("XXL-Job aiConversationLowTaskProcess taskId is null");
                continue;
            }
            MqBaseDto mqBaseDto = new MqBaseDto();
            mqBaseDto.setTaskId(taskId);
            Long chatContactId = aiConversationProcessTaskService.extractChatContactFromTaskId(taskId);
            if (Objects.isNull(chatContactId)) {
                log.error("XXL-Job aiConversationLowTaskProcess chatContactId is null, taskId:{}", taskId);
                continue;
            }
            mqBaseDto.setChatContactId(chatContactId);
            mqBaseDto.setType(entity.getTaskType());
            log.info("XXL-Job aiConversationLowTaskProcess send message on taskId:{}", taskId);
            mqSendUtil.sendMessage(TopicEnum.DEFAULT_DIALOGUE_PROCESS_TOPIC, mqBaseDto);
        }
        log.info("XXL-Job aiConversationLowTaskProcess end");
    }

    /**
     * 港险初始化任务
     * 支持参数格式：startTime,endTime (例如：2025-01-01 00:00:00,2025-01-02 00:00:00)
     * 如果不传参数，默认取当天00:00之后的数据
     */
    @XxlJob("hongKongInsuranceInit")
    public void hongKongInsuranceInitJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("XXL-Job hongKongInsuranceInit start, param: {}", jobParam);

        LocalDateTime startTime;
        LocalDateTime endTime;

        if (Strings.isNullOrEmpty(jobParam)) {
            // 默认取当天00:00之后的数据
            startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            endTime = LocalDateTime.now();
        } else {
            // 解析参数
            String[] timeParams = jobParam.split(",");
            if (timeParams.length != 2) {
                log.error("XXL-Job hongKongInsuranceInit 参数格式错误，应为：startTime,endTime");
                return;
            }
            try {
                startTime = LocalDateTime.parse(timeParams[0].trim(), java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                endTime = LocalDateTime.parse(timeParams[1].trim(), java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                log.error("XXL-Job hongKongInsuranceInit 时间参数解析失败", e);
                return;
            }
        }

        hongKongInsuranceInit(startTime, endTime);
    }

    public void hongKongInsuranceInit(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("港险初始化任务开始，时间范围：{} - {}，租户：{}", startTime, endTime, tenantIdYiren);

        final int BATCH_SIZE = 1000;
        final int SLEEP_SECONDS = 3; // 每次间隔3秒

        Long lastId = 0L;
        int totalProcessed = 0;
        int totalIntentTasks = 0;
        int batchCount = 0;

        try {
            while (true) {
                List<AiPlannerChatContact> chatContacts = aiPlannerChatContactService.fetchHongKongInsuranceDataForInit(
                    startTime, endTime, lastId, BATCH_SIZE, tenantIdYiren);

                if (chatContacts.isEmpty()) {
                    log.info("港险初始化任务完成，未找到更多数据");
                    break;
                }

                batchCount++;
                log.info("处理第{}批数据，本批数量：{}", batchCount, chatContacts.size());

                // 为每条记录创建港险主意图检测任务
                for (AiPlannerChatContact chatContact : chatContacts) {
                    try {
                        // 根据originType确定数据源类型
                        String sourceType = AiConversationDataSourceEnum.CALL_CENTER.getCode();

                        // 只创建港险主意图检测任务，节点识别任务由主意图检测完成后根据结果决定是否创建
                        // 格式：{taskType}-{sourceType}_{chatContactId}
                        String nodeTaskId = AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode() + "-" + sourceType + "_" + chatContact.getId();
                        aiConversationProcessTaskService.createProcessTask(
                                nodeTaskId,
                                chatContact.getId(),
                                AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode(),
                                AiConversationProcessStatusEnum.INIT_WAITING.getCode(),
                                chatContact.getTenantId(),
                                sourceType
                        );
                        totalIntentTasks++;
                        totalProcessed++;
                    } catch (Exception e) {
                        log.error("为对话ID {} 创建港险主意图任务失败", chatContact.getId(), e);
                    }
                }

                // 更新lastId为当前批次的最大ID
                lastId = chatContacts.get(chatContacts.size() - 1).getId();

                log.info("第{}批处理完成，累计处理记录数：{}，意图任务数：{}",
                    batchCount, totalProcessed, totalIntentTasks);

                // 如果当前批次少于BATCH_SIZE，说明已经处理完所有数据
                if (chatContacts.size() < BATCH_SIZE) {
                    log.info("港险初始化任务完成，已处理完所有数据");
                    break;
                }

                // 休眠防止数据库压力过大
                try {
                    Thread.sleep(SLEEP_SECONDS * 1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("港险初始化任务被中断", e);
                    break;
                }
            }

            log.info("港险初始化任务总结 - 租户：{}，处理批次数：{}，总记录数：{}，意图任务数：{}",
                tenantIdYiren, batchCount, totalProcessed, totalIntentTasks);

        } catch (Exception e) {
            log.error("港险初始化任务执行异常", e);
            throw e;
        }
    }

    public void syncWechatChatSummaryJobHandler(String data) {
        LocalDate targetDate = LocalDate.parse(data.trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        syncWechatChatSummary(targetDate);

    }

    /**
     * 同步微信聊天记录汇总任务
     * 支持参数格式：yyyy-MM-dd (例如：2025-01-20)
     * 如果不传参数，默认同步昨天的数据
     */
    @XxlJob("syncWechatChatSummary")
    public void syncWechatChatSummaryJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("XXL-Job syncWechatChatSummary start, param: {}", jobParam);

        LocalDate targetDate;
        if (Strings.isNullOrEmpty(jobParam)) {
            // 默认同步昨天的数据
            targetDate = LocalDate.now().minusDays(1);
        } else {
            try {
                targetDate = LocalDate.parse(jobParam.trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                log.error("XXL-Job syncWechatChatSummary 日期参数解析失败: {}", jobParam, e);
                return;
            }
        }

        syncWechatChatSummary(targetDate);
    }

    public void wechatHongKongInsuranceInitJobHandler(String  data) {
        LocalDate targetDate = LocalDate.parse(data.trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        wechatHongKongInsuranceInit(targetDate);
    }

    /**
     * 微信消息港险主意图初始化任务
     * 支持参数格式：yyyy-MM-dd (例如：2025-01-20)
     * 如果不传参数，默认初始化昨天的数据
     */
    @XxlJob("wechatHongKongInsuranceInit")
    public void wechatHongKongInsuranceInitJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("XXL-Job wechatHongKongInsuranceInit start, param: {}", jobParam);

        LocalDate targetDate;
        if (Strings.isNullOrEmpty(jobParam)) {
            // 默认初始化昨天的数据
            targetDate = LocalDate.now().minusDays(1);
        } else {
            try {
                targetDate = LocalDate.parse(jobParam.trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                log.error("XXL-Job wechatHongKongInsuranceInit 日期参数解析失败: {}", jobParam, e);
                return;
            }
        }

        wechatHongKongInsuranceInit(targetDate);
    }

    /**
     * 微信消息港险主意图初始化
     * @param targetDate 目标日期
     */
    public void wechatHongKongInsuranceInit(LocalDate targetDate) {
        log.info("微信消息港险主意图初始化任务开始，目标日期：{}，租户：{}", targetDate, tenantIdYiren);

        final int BATCH_SIZE = 500;
        // 每次间隔2秒
        final int SLEEP_SECONDS = 2;

        int totalProcessed = 0;
        int totalIntentTasks = 0;
        int successCount = 0;
        int failCount = 0;

        try {
            // 1. 查询目标日期的微信聊天汇总记录
            List<AiConversationChatSummary> chatSummaries = aiConversationChatSummaryService.list(
                    new LambdaQueryWrapper<AiConversationChatSummary>()
                            .eq(AiConversationChatSummary::getChatDate, targetDate)
                            .eq(AiConversationChatSummary::getTenantId, tenantIdYiren)
                            .eq(AiConversationChatSummary::getIsDeleted, 0)
                            .isNotNull(AiConversationChatSummary::getChatContent)
                            .orderByAsc(AiConversationChatSummary::getId)
            );

            log.info("查询到{}条微信聊天汇总记录", chatSummaries.size());

            if (chatSummaries.isEmpty()) {
                log.info("未找到目标日期的微信聊天汇总记录，任务结束");
                return;
            }

            // 2. 分批处理聊天汇总记录
            int currentIndex = 0;
            while (currentIndex < chatSummaries.size()) {
                int endIndex = Math.min(currentIndex + BATCH_SIZE, chatSummaries.size());
                List<AiConversationChatSummary> batchSummaries = chatSummaries.subList(currentIndex, endIndex);
                
                log.info("处理第{}批数据，范围：{}-{}，本批数量：{}", 
                        (currentIndex / BATCH_SIZE) + 1, currentIndex + 1, endIndex, batchSummaries.size());

                // 为每条聊天汇总记录创建港险主意图检测任务
                for (AiConversationChatSummary summary : batchSummaries) {
                    try {
                        // 微信数据源类型
                        String sourceType = AiConversationDataSourceEnum.WECHAT.getCode();
                        
                        // 任务ID格式：{taskType}-{sourceType}_{chatSummaryId}
                        String taskId = AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode() + 
                                       "-" + sourceType + "_" + summary.getId();
                        
                        aiConversationProcessTaskService.createProcessTask(
                                taskId,
                                summary.getId(),
                                AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode(),
                                AiConversationProcessStatusEnum.INIT_WAITING.getCode(),
                                summary.getTenantId(),
                                sourceType
                        );
                        
                        totalIntentTasks++;
                        successCount++;
                        log.debug("创建微信港险主意图任务成功: chatSummaryId={}, plannerNo={}, customerId={}", 
                                summary.getId(), summary.getPlannerNo(), summary.getCustomerId());
                        
                    } catch (Exception e) {
                        failCount++;
                        log.error("创建微信港险主意图任务失败: chatSummaryId={}, plannerNo={}, customerId={}", 
                                summary.getId(), summary.getPlannerNo(), summary.getCustomerId(), e);
                    }
                    
                    totalProcessed++;
                }

                currentIndex = endIndex;

                log.info("批次处理完成，累计处理记录数：{}，意图任务数：{}，成功：{}，失败：{}",
                        totalProcessed, totalIntentTasks, successCount, failCount);

                // 如果还有更多数据要处理，休眠一段时间
                if (currentIndex < chatSummaries.size()) {
                    try {
                        Thread.sleep(SLEEP_SECONDS * 1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("微信港险初始化任务被中断", e);
                        break;
                    }
                }
            }

            log.info("微信消息港险主意图初始化任务完成 - 租户：{}，目标日期：{}，总记录数：{}，意图任务数：{}，成功：{}，失败：{}",
                    tenantIdYiren, targetDate, totalProcessed, totalIntentTasks, successCount, failCount);

            // 发送通知（如果有失败）
            if (failCount > 0) {
                webHookUtil.weChatMsgDev(MsgTypeEnum.QW_MESSAGE_SUMMARY_INIT_ERROR,
                        String.format("微信港险意图初始化异常，日期：%s，成功：%d，失败：%d", targetDate, successCount, failCount));
            }

        } catch (Exception e) {
            log.error("微信消息港险主意图初始化任务执行异常，目标日期：{}", targetDate, e);
            webHookUtil.weChatMsgDev(MsgTypeEnum.QW_MESSAGE_SUMMARY_INIT_ERROR,
                    String.format("微信港险意图初始化严重异常，日期：%s，错误：%s", targetDate, e.getMessage()));
            throw e;
        }
    }

    /**
     * 同步指定日期的微信聊天记录汇总
     * @param targetDate 目标日期
     */
    public void syncWechatChatSummary(LocalDate targetDate) {
        log.info("开始同步微信聊天记录汇总，目标日期：{}", targetDate);

        LocalDateTime startTime = targetDate.atStartOfDay();
        LocalDateTime endTime = targetDate.plusDays(1).atStartOfDay().minusSeconds(1);

        int successCount = 0;
        int failCount = 0;
        int totalMessageCount = 0;
        int totalGroupCount = 0;

        try {
            // 1. 查询目标日期的所有微信消息
            LambdaQueryWrapper<CallCenterWechatMessage> messageWrapper = new LambdaQueryWrapper<>();
            messageWrapper.between(CallCenterWechatMessage::getSendTime, startTime, endTime)
                    .isNotNull(CallCenterWechatMessage::getUserNo)
                    .orderByAsc(CallCenterWechatMessage::getUserNo)
                    .orderByAsc(CallCenterWechatMessage::getSendTime);

            List<CallCenterWechatMessage> messages = callCenterWechatMessageService.list(messageWrapper);
            log.info("查询到{}条微信消息记录", messages.size());

            if (messages.isEmpty()) {
                log.info("未找到目标日期的微信消息记录，任务结束");
                return;
            }

            // 2. 按理财师分组
            Map<String, List<CallCenterWechatMessage>> messagesByPlanner = messages.stream()
                    .collect(Collectors.groupingBy(CallCenterWechatMessage::getUserNo));

            log.info("按理财师分组，共{}个理财师", messagesByPlanner.size());

            // 3. 提取所有客户标识并批量查询映射关系
            Set<String> allCustomerKeys = new HashSet<>();
            for (List<CallCenterWechatMessage> plannerMessages : messagesByPlanner.values()) {
                Map<String, List<CallCenterWechatMessage>> dialogueGroups = groupMessagesByDialogue(plannerMessages, "");
                allCustomerKeys.addAll(dialogueGroups.keySet());
            }

            // 批量查询客户映射关系
            Map<String, String> customerMappings = batchGetCustomerMappings(allCustomerKeys);
            log.info("批量查询客户映射关系完成，查询{}个客户标识，匹配到{}个客户ID",
                    allCustomerKeys.size(), customerMappings.size());

            // 4. 为每个理财师处理聊天记录
            for (Map.Entry<String, List<CallCenterWechatMessage>> entry : messagesByPlanner.entrySet()) {
                String plannerNo = entry.getKey();
                List<CallCenterWechatMessage> plannerMessages = entry.getValue();

                log.debug("处理理财师{}的消息，共{}条", plannerNo, plannerMessages.size());

                try {
                    // 按发送人和接收人进一步分组（区分不同的客户对话）
                    Map<String, List<CallCenterWechatMessage>> dialogueGroups = groupMessagesByDialogue(plannerMessages, plannerNo);

                    for (Map.Entry<String, List<CallCenterWechatMessage>> dialogueEntry : dialogueGroups.entrySet()) {
                        String customerKey = dialogueEntry.getKey();
                        List<CallCenterWechatMessage> dialogueMessages = dialogueEntry.getValue();

                        // 处理单个客户的对话，传入预查询的映射关系
                        boolean result = processSingleDialogue(plannerNo, customerKey, dialogueMessages, targetDate, customerMappings);
                        if (result) {
                            successCount++;
                            totalMessageCount += dialogueMessages.size();
                        } else {
                            failCount++;
                        }
                        totalGroupCount++;
                    }
                } catch (Exception e) {
                    log.error("处理理财师{}的消息失败", plannerNo, e);
                    failCount++;
                }
            }

            log.info("微信聊天记录汇总任务完成，成功：{}，失败：{}，总对话组：{}，总消息数：{}",
                    successCount, failCount, totalGroupCount, totalMessageCount);

            // 发送通知（如果有失败）
            if (failCount > 0) {
                webHookUtil.weChatMsgDev(MsgTypeEnum.QW_MESSAGE_SUMMARY_INIT_ERROR,
                        String.format("微信聊天记录同步异常，日期：%s，成功：%d，失败：%d",
                                targetDate, successCount, failCount));
            }

        } catch (Exception e) {
            log.error("同步微信聊天记录汇总任务执行异常，目标日期：{}", targetDate, e);
            webHookUtil.weChatMsgDev(MsgTypeEnum.QW_MESSAGE_SUMMARY_INIT_ERROR,
                    String.format("微信聊天记录同步严重异常，日期：%s，错误：%s", targetDate, e.getMessage()));
        }
    }

    /**
     * 将消息按对话分组（理财师与不同客户的对话）
     */
    private Map<String, List<CallCenterWechatMessage>> groupMessagesByDialogue(List<CallCenterWechatMessage> messages, String plannerNo) {
        Map<String, List<CallCenterWechatMessage>> groups = new HashMap<>();

        for (CallCenterWechatMessage message : messages) {
            String customerKey;

            // 根据isSend字段判断对话对象
            if (Integer.valueOf(1).equals(message.getIsSend())) {
                // 理财师发送的消息，接收人是客户
                customerKey = message.getRecipient();
            } else {
                // 客户发送的消息，发送人是客户
                customerKey = message.getSender();
            }

            if (Strings.isNullOrEmpty(customerKey)) {
                log.warn("消息ID{}的客户标识为空，跳过", message.getId());
                continue;
            }

            // 过滤自己给自己发的消息（发送和接收微信ID一样）
            if (Objects.equals(message.getSender(), message.getRecipient())) {
                log.debug("消息ID{}为自己给自己发送，跳过", message.getId());
                continue;
            }

            groups.computeIfAbsent(customerKey, k -> new ArrayList<>()).add(message);
        }

        return groups;
    }

    /**
     * 批量查询客户映射关系
     */
    private Map<String, String> batchGetCustomerMappings(Set<String> customerKeys) {
        Map<String, String> mappings = new HashMap<>();

        if (customerKeys.isEmpty()) {
            return mappings;
        }

        try {
            // 批量查询微信原生ID映射
            List<CallcenterWechatCustomerMapping> originalIdMappings = callcenterWechatCustomerMappingService.list(
                    new LambdaQueryWrapper<CallcenterWechatCustomerMapping>()
                            .in(CallcenterWechatCustomerMapping::getWechatOriginalId, customerKeys)
                            .orderByDesc(CallcenterWechatCustomerMapping::getUpdateTime)
            );

            for (CallcenterWechatCustomerMapping mapping : originalIdMappings) {
                mappings.put(mapping.getWechatOriginalId(), mapping.getCustomerId());
            }

            // 找出还没有映射的客户标识，尝试通过微信号查询
            Set<String> unmappedKeys = customerKeys.stream()
                    .filter(key -> !mappings.containsKey(key))
                    .collect(Collectors.toSet());

            if (!unmappedKeys.isEmpty()) {
                List<CallcenterWechatCustomerMapping> wechatNoMappings = callcenterWechatCustomerMappingService.list(
                        new LambdaQueryWrapper<CallcenterWechatCustomerMapping>()
                                .in(CallcenterWechatCustomerMapping::getWechatNo, unmappedKeys)
                                .orderByDesc(CallcenterWechatCustomerMapping::getUpdateTime)
                );

                for (CallcenterWechatCustomerMapping mapping : wechatNoMappings) {
                    mappings.put(mapping.getWechatNo(), mapping.getCustomerId());
                }
            }

        } catch (Exception e) {
            log.error("批量查询客户映射关系失败", e);
        }

        return mappings;
    }

    /**
     * 处理单个客户与理财师的对话
     */
    private boolean processSingleDialogue(String plannerNo, String customerKey, List<CallCenterWechatMessage> messages,
                                          LocalDate chatDate, Map<String, String> customerMappings) {
        try {
            // 1. 构建聊天内容
            StringBuilder chatContent = new StringBuilder();
            LocalDateTime firstMessageTime = null;
            LocalDateTime lastMessageTime = null;
            int plannerMessageCount = 0;
            int customerMessageCount = 0;

            // 按时间排序
            messages.sort(Comparator.comparing(CallCenterWechatMessage::getSendTime));

            for (CallCenterWechatMessage message : messages) {
                if (firstMessageTime == null) {
                    firstMessageTime = message.getSendTime();
                }
                lastMessageTime = message.getSendTime();

                // 确定发送者身份
                String sender;
                if (Integer.valueOf(1).equals(message.getIsSend())) {
                    sender = "理财师";
                    plannerMessageCount++;
                } else {
                    sender = "客户";
                    customerMessageCount++;
                }

                // 获取消息内容（优先voiceContent）
                String content = !Strings.isNullOrEmpty(message.getVoiceContent())
                        ? message.getVoiceContent()
                        : message.getContent();

                if (!Strings.isNullOrEmpty(content)) {
                    chatContent.append(String.format("[%s %s] %s: %s\n",
                            message.getSendTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")),
                            sender,
                            sender.equals("理财师") ? plannerNo : customerKey,
                            content));
                }
            }

            if (chatContent.length() == 0) {
                log.warn("理财师{}与客户{}的对话内容为空，跳过", plannerNo, customerKey);
                return false;
            }

            // 2. 从预查询的映射关系中获取客户ID
            String customerId = customerMappings.get(customerKey);
            if (Strings.isNullOrEmpty(customerId)) {
                // 如果找不到客户映射，跳过该组数据
                log.debug("理财师{}与客户标识{}未找到客户ID映射，跳过该组数据", plannerNo, customerKey);
                return false;
            }

            // 3. 创建或更新汇总记录
            AiConversationChatSummary summary = new AiConversationChatSummary();
            summary.setPlannerNo(plannerNo);
            summary.setCustomerId(customerId);
            summary.setChatContent(chatContent.toString());
            summary.setChatDate(chatDate);
            summary.setMessageCount(messages.size());
            summary.setPlannerMessageCount(plannerMessageCount);
            summary.setCustomerMessageCount(customerMessageCount);
            summary.setFirstMessageTime(firstMessageTime);
            summary.setLastMessageTime(lastMessageTime != null ? lastMessageTime : firstMessageTime);
            summary.setSyncStatus(1);
            summary.setTenantId(messages.get(0).getTenantId());

            boolean saveResult = aiConversationChatSummaryService.saveOrUpdateChatSummary(summary);
            if (saveResult) {
                log.info("处理完成 - 理财师：{}，客户：{}，消息数：{}（理财师：{}，客户：{}），内容长度：{}字符",
                        plannerNo, customerId, messages.size(), plannerMessageCount, customerMessageCount, chatContent.length());
            } else {
                log.error("保存聊天汇总记录失败 - 理财师：{}，客户：{}", plannerNo, customerId);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("处理理财师{}与客户{}的对话失败", plannerNo, customerKey, e);
            return false;
        }
    }

    /**
     * 处理单个客户与理财师的对话（兼容旧版本）
     * @deprecated 请使用带customerMappings参数的版本
     */
    @Deprecated
    private boolean processSingleDialogue(String plannerNo, String customerKey, List<CallCenterWechatMessage> messages, LocalDate chatDate) {
        // 为了兼容性，创建一个空的映射并调用新版本
        Map<String, String> emptyMappings = new HashMap<>();
        return processSingleDialogue(plannerNo, customerKey, messages, chatDate, emptyMappings);
    }

}

