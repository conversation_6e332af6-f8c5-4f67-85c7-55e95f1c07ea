package com.yirendai.voiceaiserver.job;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.service.CallCenterService;
import com.yirendai.workbench.anno.LogTrace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 新呼叫中心数据相关Job
 */
@Slf4j
@Component
public class CallCenterMsgJob {

    @Resource
    CallCenterService callCenterService;

    @LogTrace
    @XxlJob("HandleTodayFailSegmentJob")
    public ReturnT<String> handleTodayFailSegment() {
        try {
            callCenterService.handleTodayFailSegment();
            XxlJobHelper.log("处理当天音转文失败片段任务执行成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("处理当天音转文失败片段任务执行失败，失败原因为", e);
            XxlJobHelper.log("处理当天音转文失败片段任务执行失败");
            return ReturnT.FAIL;
        }
    }

    @LogTrace
    @XxlJob("SyncCallCenterRecordJob")
    public ReturnT<String> syncCallCenterRecord() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("同步指定日期至今的通话记录任务输入参数为{}", param);
            LocalDate startDate = LocalDate.now();
            if (StrUtil.isNotBlank(param)) {
                DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                startDate = LocalDate.parse(param, dateFormatter);
            }
            callCenterService.syncRecord(startDate);
            XxlJobHelper.log("同步指定日期至今的通话记录任务执行成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("同步指定日期至今的通话记录任务执行失败，失败原因为", e);
            XxlJobHelper.log("同步指定日期至今的通话记录任务执行失败");
            return ReturnT.FAIL;
        }
    }

    @LogTrace
    @XxlJob("HandleFailCompleteJob")
    public ReturnT<String> handleFailComplete() {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("处理指定时间范围内的音转文失败的通话记录任务输入参数为{}", param);
            LocalDate startDate = LocalDate.now(), endDate = null;
            if (StrUtil.isNotBlank(param)) {
                Map<String, String> paramMap = JSON.parseObject(param, new TypeReference<HashMap<String, String>>() {});
                if (MapUtil.isNotEmpty(paramMap) && paramMap.containsKey("startDate")) {
                    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    startDate = LocalDate.parse(paramMap.get("startDate"), dateFormatter);
                    if (paramMap.containsKey("endDate")) {
                        endDate = LocalDate.parse(paramMap.get("endDate"), dateFormatter);
                        if (!endDate.isAfter(startDate)) {
                            throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "endDate必须大于startDate");
                        }
                    }
                }
            }
            callCenterService.handleFailComplete(startDate, endDate);
            XxlJobHelper.log("处理指定时间范围内的音转文失败的通话记录任务执行成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("处理指定时间范围内的音转文失败的通话记录任务执行失败，失败原因为", e);
            XxlJobHelper.log("处理指定时间范围内的音转文失败的通话记录任务执行失败");
            return ReturnT.FAIL;
        }
    }

    @XxlJob("NotifyTodayFailLimitJob")
    public ReturnT<String> notifyTodayFailLimit() {
        try {
            callCenterService.notifyTodayFailLimit();
            XxlJobHelper.log("当天音转文失败次数已达上限告警任务执行成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("当天音转文失败次数已达上限告警任务执行失败，失败原因为", e);
            XxlJobHelper.log("当天音转文失败次数已达上限告警任务执行失败");
            return ReturnT.FAIL;
        }
    }

    /**
     * 清理脏数据和异常数据告警任务
     */
    @LogTrace
    @XxlJob("ClearDirtyAndNotifyJob")
    public ReturnT<String> clearDirtyAndNotify() {
        try {
            callCenterService.clearDirtyAndNotify();
            XxlJobHelper.log("清理脏数据和异常数据告警任务执行成功");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("清理脏数据和异常数据告警任务执行失败，失败原因为", e);
            XxlJobHelper.log("清理脏数据和异常数据告警任务执行失败");
            return ReturnT.FAIL;
        }
    }
}
