package com.yirendai.voiceaiserver.job;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.service.impl.AvayaVoiceService;
import com.yirendai.voiceaiserver.vo.db.AvayaGroupVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AvayaSplitTask {

    @Resource
    AvayaVoiceService avayaVoiceService;
    @Resource
    JedisCluster jedisCluster;
    @Resource
    RedissonClient redissonClient;

    private static final String KEY_AVAYA_TRANSFER_TASK_LOCK = "voice:ai:server:avaya:transfer:task:lock";

    private static final String KEY_AVAYA_TRANSFER_DAY_TASK_COUNT_CACHE = "voice:ai:server:avaya:transfer:day:task:count:cache";

//    @Scheduled(cron = "0 5 20 * * ?")
    public void performTaskNight() {
        log.info("切割Avaya语料(夜间任务)开始...");
        RLock lock = redissonClient.getLock(KEY_AVAYA_TRANSFER_TASK_LOCK);
        try {
            boolean lockFlag = lock.tryLock(5, TimeUnit.MINUTES);
            if (!lockFlag) {
                log.info("切割Avaya语料(夜间任务)未获取到锁");
                return;
            }
            List<AvayaGroupVO> list = avayaVoiceService.getHandleList(8);
            for (AvayaGroupVO group : list) {
                avayaVoiceService.splitTextNew(group.getMinId(), group.getMaxId(), true);
            }
            log.info("切割Avaya语料(夜间任务)结束");
        }  catch (InterruptedException e) {
            log.error("(夜间)切割Avaya语料任务发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

//    @Scheduled(cron = "0 5 7 * * ?")
    public void performTaskDay() {
        executeDay();
    }

    public void executeDay() {
        log.info("切割Avaya语料(白天任务)开始...");
        RLock lock = redissonClient.getLock(KEY_AVAYA_TRANSFER_TASK_LOCK);
        try {
            boolean lockFlag = lock.tryLock(5, TimeUnit.MINUTES);
            if (!lockFlag) {
                log.info("切割Avaya语料(白天任务)未获取到锁");
                return;
            }

            Long count = jedisCluster.incr(KEY_AVAYA_TRANSFER_DAY_TASK_COUNT_CACHE);
            if (Long.valueOf(16).compareTo(count) < 0) {
                log.info("切割Avaya语料(白天任务)并发量已达上限，本次无需执行");
                return;
            }

            List<AvayaGroupVO> list = avayaVoiceService.getHandleList(8);
            for (AvayaGroupVO group : list) {
                avayaVoiceService.splitTextNew(group.getMinId(), group.getMaxId(), false);
            }
            log.info("切割Avaya语料(白天任务)结束");
        }  catch (InterruptedException e) {
            log.error("(白天)切割Avaya语料任务发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
