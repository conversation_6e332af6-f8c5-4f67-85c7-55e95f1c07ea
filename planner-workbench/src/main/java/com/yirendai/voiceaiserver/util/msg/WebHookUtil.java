package com.yirendai.voiceaiserver.util.msg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WebHookUtil {

    @Autowired
    private WebHookAsync webHookAsync;

    @Value("${qw.robot.errorMsg.url}")
    private String robotModelUrl;


    /**
     * 推送企微消息给开发人员
     * @param type 问题类型
     * @param title 标题
     * @param describe 问题描述
     * @param throwable 异常信息，没有的话传null
     */
    public void weChatMsgDev(MsgTypeEnum type,String title, String describe, Throwable throwable) {
        webHookAsync.webHookToBusiness(robotModelUrl, title, type.getMessage(), describe,
                throwable);
    }

    /**
     * 推送企微消息给开发人员
     * @param type 问题类型
     * @param title 标题
     * @param describe 问题描述
     */
    public void weChatMsgDev(MsgTypeEnum type,String title,  String describe) {
        weChatMsgDev( type,title, describe, null);
    }

    /**
     * 推送企微消息给开发人员
     * @param type 问题类型
     * @param describe 问题描述
     */
    public void weChatMsgDev(MsgTypeEnum type, String describe) {
        weChatMsgDev( type,null, describe, null);
    }
}
