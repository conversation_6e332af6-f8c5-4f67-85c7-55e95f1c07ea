package com.yirendai.voiceaiserver.util;

import com.google.common.base.Strings;
import com.yirendai.robot.constant.RobotConstant;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class TextSplitter {

    // 理财师发起的对话
    public final static String PLANNER_PREFIX = "理财师:";
    // 客户发起的对话
    public final static String CUSTOMER_PREFIX = "客户:";

    /**
     * 根据指定的阈值将文本分割成多个片段，
     *
     * @param text      原始文本
     * @param threshold 分片的最大字符数
     * @return 分割后的文本片段列表
     */
    public static List<String> splitText(String text, int threshold, double factor) {
        List<String> chunks = new ArrayList<>();
        if (text == null || text.isEmpty()) {
            return chunks;
        }

        int length = text.length();

        // 计算每个片段的目标长度
        int numChunks = (int) Math.ceil((double) length / threshold);
        int targetLength = (int) Math.ceil((double) length / numChunks);

        int start = 0;

        while (start < length) {
            int end = Math.min(start + targetLength, length);
            // 尝试找到一个合适的断点进行拆分
            int lastSpace = findSplitRole(text, start, end, (int) (threshold * factor));

            if (lastSpace <= start) {
                lastSpace = end;
            }

            String chunk = text.substring(start, lastSpace).trim();

            if (!chunk.isEmpty()) {
                chunks.add(chunk);
                start = lastSpace;
            } else {
                break;
            }
        }

        return chunks;
    }


    private static int findSplitPoint(String text, int start, int initialEnd, int maxThreshold) {
        // 从initialEnd开始往后找，最多到maxThreshold的位置，以寻找句子结束符作为断点
        for (int i = initialEnd; i < Math.min(text.length(), start + maxThreshold); i++) {
            char c = text.charAt(i);
            if (c == '。' || c == '.' || c == '!' || c == '?' || c == '！' || c == '？') {
                return i + 1;
            }
        }
        return initialEnd;
    }

    private static int findSplitRole(String text, int start, int initialEnd, int maxThreshold) {
        // 从start开始往后找，最多到maxThreshold的位置，以寻找对话角色标记作为断点
        for (int i = initialEnd; i < Math.min(text.length(), start + maxThreshold); i++) {
            // 检查是否遇到新的对话角色标记
            if (text.startsWith(CUSTOMER_PREFIX, i) || text.startsWith(PLANNER_PREFIX, i)) {
                return i;
            }
        }
        return initialEnd;
    }

    public static String mergeSpeakerTexts(String input) {
        // 按照空格或换行符分割输入字符串为行
        String[] lines = input.split("[\\s\\n]+");

        if (lines.length == 0) {
            return input;
        }
        if (lines.length == 1) {
            return input;
        }
        log.info("lines.length:{}", lines.length);

        List<String> result = new ArrayList<>();
        StringBuilder mergedText = new StringBuilder();

        // 初始化第一个说话者和文本
        String[] firstLineParts = getSpeakerText(lines[0]);
        String preSpeaker = firstLineParts[0];
        mergedText.append(firstLineParts[1]);

        for (int i = 1; i < lines.length; i++) {
            String[] currentLineParts = getSpeakerText(lines[i]);
            String curSpeaker = currentLineParts[0];
            String curText = currentLineParts[1];
            if (curText == null) {
                continue;
            }
            if (curSpeaker == null) {
                mergedText.append(curText);
                continue;
            }

            if (curSpeaker.equals(preSpeaker)) {
                // 如果当前说话者与之前相同，继续合并文本
                mergedText.append(curText);
            } else {
                // 如果不同，将之前的结果存储起来，并开始新的累积
                result.add(preSpeaker + ":" + mergedText);
                preSpeaker = curSpeaker;
                // 清空并重置合并文本
                mergedText.setLength(0);
                mergedText.append(curText);
            }
        }

        // 添加最后一个合并结果
        result.add(preSpeaker + ":" + mergedText);

        return String.join("\n", result);
    }

    private static String[] getSpeakerText(String s) {
        int colonIndex = s.indexOf(':');
        if (colonIndex != -1) {
            String speaker = s.substring(0, colonIndex).trim();
            String text = s.substring(colonIndex + 1).trim();
            return new String[]{speaker, text};
        }
        return new String[]{null, s};
    }

}

