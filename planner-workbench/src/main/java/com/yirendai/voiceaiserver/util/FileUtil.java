package com.yirendai.voiceaiserver.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ShortBuffer;
import java.nio.file.Paths;
import java.util.*;
import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.Parser;
import org.apache.tika.parser.mp3.Mp3Parser;
import org.apache.tika.sax.BodyContentHandler;
import java.io.FileInputStream;

/**
 * <AUTHOR>
 * @time 2021/9/1 09:41
 **/
@Slf4j
public class FileUtil {

    public static File saveUploadFile(MultipartFile file, String fileName) {
        File targetFile = new File(fileName);

        try (InputStream in = file.getInputStream(); FileOutputStream out = new FileOutputStream(targetFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            return targetFile;
        } catch (IOException e) {
            log.error("保存文件失败，失败原因为", e);
            return null;
        }
    }

    /**
     * 写入文件，文件已存在则覆盖原文件
     */
    public static File byteToFile(byte[] bytes, String filePath) throws Exception {
        File file = new File(filePath);
        try (FileOutputStream fos = new FileOutputStream(file);
             BufferedOutputStream bos = new BufferedOutputStream(fos);) {
            bos.write(bytes);
        }
        return file;
    }

    /**
     * 写入文件并返回文件路径，文件已存在则直接返回文件路径
     */
    public static String byteToFileCheckExist(byte[] bytes, String basePath, String filePath, String filePathBak) throws Exception {
        File file = new File(basePath + filePath);
        if (file.exists()) {
            return filePath;
        }
        File fileBak = new File(basePath + filePathBak);
        if (fileBak.exists()) {
            return filePathBak;
        }

        File directory = fileBak.getParentFile();
        // 如果目录不存在，则创建它
        if (Objects.nonNull(directory) && !directory.exists()) {
            boolean isCreated = directory.mkdirs();
            if (!isCreated) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "创建文件路径失败");
            }
        }

        try (FileOutputStream fos = new FileOutputStream(fileBak);
             BufferedOutputStream bos = new BufferedOutputStream(fos);) {
            bos.write(bytes);
        }
        return filePathBak;
    }

    public static void splitAudioChannel(String inputFile, String leftChannelOutputFile,
                                         String rightChannelOutputFile) {
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile);
             FFmpegFrameRecorder leftRecorder = new FFmpegFrameRecorder(leftChannelOutputFile, 1);
             FFmpegFrameRecorder rightRecorder = new FFmpegFrameRecorder(rightChannelOutputFile, 1)) {

            grabber.start();

            leftRecorder.setSampleRate(grabber.getSampleRate());
            leftRecorder.setAudioCodec(grabber.getAudioCodec());
            rightRecorder.setSampleRate(grabber.getSampleRate());
            rightRecorder.setAudioCodec(grabber.getAudioCodec());

            leftRecorder.start();
            rightRecorder.start();

            Frame frame;
            while ((frame = grabber.grabSamples()) != null) {
                if (frame.samples != null && frame.samples.length > 0) {
                    ShortBuffer buffer = (ShortBuffer) frame.samples[0];
                    short[] samples = new short[buffer.capacity()];
                    buffer.get(samples);

                    short[] leftChannelSamples = new short[samples.length / 2];
                    short[] rightChannelSamples = new short[samples.length / 2];

                    for (int i = 0; i < samples.length / 2; i++) {
                        leftChannelSamples[i] = samples[2 * i];
                        rightChannelSamples[i] = samples[2 * i + 1];
                    }

                    ShortBuffer leftBuffer = ShortBuffer.wrap(leftChannelSamples);
                    ShortBuffer rightBuffer = ShortBuffer.wrap(rightChannelSamples);

                    leftRecorder.recordSamples(grabber.getSampleRate(), 1, leftBuffer);
                    rightRecorder.recordSamples(grabber.getSampleRate(), 1, rightBuffer);
                }
            }

            grabber.stop();
            leftRecorder.stop();
            rightRecorder.stop();

        } catch (Exception e) {
            log.error("音频通道分离失败，文件路径 {}, 失败原因为", inputFile, e);
        }
    }

    /**
     * 根据左右声道拆分音频文件
     *
     * @param inputFile              文件绝对路径
     * @param leftChannelOutputFile  左声道文件路径
     * @param rightChannelOutputFile 右声道文件路径
     */
    public static void splitAudioByChannel(String inputFile, String leftChannelOutputFile, String rightChannelOutputFile) {
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile);
             FFmpegFrameRecorder leftRecorder = new FFmpegFrameRecorder(leftChannelOutputFile, 1);
             FFmpegFrameRecorder rightRecorder = new FFmpegFrameRecorder(rightChannelOutputFile, 1)) {

            grabber.start();

            leftRecorder.setSampleRate(grabber.getSampleRate());
            leftRecorder.setAudioCodec(grabber.getAudioCodec());
            rightRecorder.setSampleRate(grabber.getSampleRate());
            rightRecorder.setAudioCodec(grabber.getAudioCodec());

            leftRecorder.start();
            rightRecorder.start();

            Frame frame;
            while ((frame = grabber.grabSamples()) != null) {
                if (frame.samples != null && frame.samples.length > 0) {
                    ShortBuffer buffer = (ShortBuffer) frame.samples[0];

                    int availableSamples = buffer.remaining();
                    if (availableSamples > 0) {
                        short[] samples = new short[availableSamples];
                        buffer.get(samples);

                        short[] leftChannelSamples = new short[samples.length / 2];
                        short[] rightChannelSamples = new short[samples.length / 2];

                        for (int i = 0; i < samples.length / 2; i++) {
                            leftChannelSamples[i] = samples[2 * i];
                            rightChannelSamples[i] = samples[2 * i + 1];
                        }

                        ShortBuffer leftBuffer = ShortBuffer.wrap(leftChannelSamples);
                        ShortBuffer rightBuffer = ShortBuffer.wrap(rightChannelSamples);

                        leftRecorder.recordSamples(grabber.getSampleRate(), 1, leftBuffer);
                        rightRecorder.recordSamples(grabber.getSampleRate(), 1, rightBuffer);
                    }
                }
            }

            grabber.stop();
            leftRecorder.stop();
            rightRecorder.stop();

        } catch (Exception e) {
            log.error("音频通道分离失败，文件路径 {}, 失败原因为", inputFile, e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "音频通道分离失败" + e.getMessage());
        }
    }

    /**
     * 按照指定时长切割原文件（只支持wav格式）
     *
     * @param inputFile       原文件
     * @param secondsPerChunk 切割时长（秒）
     * @param checkSeconds    校验时长（秒）：用于校验最后一个音频时长是否短于校验时长，若短则合并到前一个音频，若本身原音频短于校验时长，则不做处理
     * @return 切割文件列表
     * @throws UnsupportedAudioFileException
     * @throws IOException
     */
    public static List<File> splitAudioFile(File inputFile, int secondsPerChunk, int checkSeconds)
            throws UnsupportedAudioFileException, IOException {
        // 打开音频输入流
        AudioInputStream originalStream = AudioSystem.getAudioInputStream(inputFile);
        AudioFormat format = originalStream.getFormat();

        // 计算每个分割片段应有的帧数
        long framesPerSecond = (long) format.getFrameRate();
        long framesPerChunk = framesPerSecond * secondsPerChunk;
        long framesCheck = framesPerSecond * checkSeconds;

        // 读取并写入分割后的文件
        long totalFramesRead = 0;
        int fileCount = 0;

        String fileName = inputFile.getName();
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            fileName = fileName.substring(0, dotIndex);
        }

        List<File> fileList = new ArrayList<>();
        while (totalFramesRead < originalStream.getFrameLength()) {
            long remainingFrames = originalStream.getFrameLength() - totalFramesRead;
            long framesToWrite = Math.min(framesPerChunk, remainingFrames);

            // 创建新的音频输入流用于当前分割片段
            AudioInputStream chunkStream = new AudioInputStream(originalStream, format, framesToWrite);

            if (framesToWrite < framesCheck && CollUtil.isNotEmpty(fileList)) {
                File lastFile = fileList.get(fileList.size() - 1);
                try (AudioInputStream existStream = AudioSystem.getAudioInputStream(lastFile)) {
                    long frameLength = existStream.getFrameLength() + chunkStream.getFrameLength();
                    SequenceInputStream sequence = new SequenceInputStream(existStream, chunkStream);
                    try (AudioInputStream appendedStream = new AudioInputStream(sequence, format, frameLength)) {
                        File tempOutputFile = new File(lastFile.getAbsolutePath() + ".tmp");
                        AudioSystem.write(appendedStream, AudioFileFormat.Type.WAVE, tempOutputFile);
                        if (!lastFile.delete() || !tempOutputFile.renameTo(lastFile)) {
                            throw new IOException("无法替换原始输出文件");
                        }
                    }
                }

                log.info("Rewritten chunk to: " + lastFile.getAbsolutePath());
            } else {
                File outputFile = new File(inputFile.getParent(), fileName + "_" + fileCount + ".wav");
                if (AudioSystem.write(chunkStream, AudioFileFormat.Type.WAVE, outputFile) == -1) {
                    throw new IOException("Could not write audio file: " + outputFile);
                }
                log.info("Written chunk to: " + outputFile.getAbsolutePath());
                fileList.add(outputFile);
            }

            totalFramesRead += framesToWrite;
            fileCount++;
        }

        originalStream.close();
        return fileList;
    }

    /**
     * 按照指定时长切割原文件（支持wav格式、mp3格式）
     *
     * @param inputAudioPath         原文件路径
     * @param segmentDuration        切割时长（秒）
     * @param minLastSegmentDuration 最后一个片段的最短时长（秒）
     * @throws IOException
     */
    public static List<File> splitAudio(String inputAudioPath, long segmentDuration, long minLastSegmentDuration) throws IOException {
        List<File> fileList = new ArrayList<>();
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputAudioPath)) {
            grabber.start();
            long totalDuration = grabber.getLengthInTime() / 1000; // 转换为毫秒

            int segmentIndex = 0;
            long startTime = 0;
            segmentDuration = segmentDuration * 1000;
            minLastSegmentDuration = minLastSegmentDuration * 1000;

            String direction = inputAudioPath.substring(0, inputAudioPath.lastIndexOf("/"));
            String fileName = inputAudioPath.substring(inputAudioPath.lastIndexOf("/") + 1);
            while (startTime < totalDuration) {
                long endTime = Math.min(startTime + segmentDuration, totalDuration);
                if (totalDuration - endTime < minLastSegmentDuration && endTime != totalDuration) {
                    endTime = totalDuration; // 将最后一段合并到倒数第二段
                }

                String outputFilePath = Paths.get(direction, fileName.substring(0, fileName.lastIndexOf("."))
                        + "_" + segmentIndex + fileName.substring(fileName.lastIndexOf("."))).toString();
                try (FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputFilePath, grabber.getAudioChannels())) {
                    recorder.setSampleRate(grabber.getSampleRate());
                    recorder.setAudioCodec(grabber.getAudioCodec());
                    recorder.start();

                    grabber.setTimestamp(startTime * 1000); // 转换为微秒
                    Frame frame;
                    while ((frame = grabber.grabFrame()) != null) {
                        recorder.record(frame);
                        if (grabber.getTimestamp() >= endTime * 1000) {
                            break; // 停止录制
                        }
                    }

                    recorder.stop();
                }
                fileList.add(new File(outputFilePath));

                startTime = endTime;
                segmentIndex++;
            }

            grabber.stop();
        }
        return fileList;
    }

    public static void convertAmrToWav(File inputFile, File outputFile) throws Exception {
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile);
             FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputFile, 1)) {
            grabber.setFormat(getFormat(inputFile.getAbsolutePath()));
            grabber.start();

            // 设置输出格式和参数
            recorder.setAudioCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_PCM_S16LE);
            recorder.setFormat("wav");
            recorder.setSampleRate(grabber.getSampleRate());
            recorder.setAudioChannels(grabber.getAudioChannels());

            recorder.start();

            while (true) {
                org.bytedeco.javacv.Frame frame = grabber.grab();
                if (frame == null) {
                    break;
                }
                if (frame.samples != null) {
                    recorder.record(frame);
                }
            }

            recorder.stop();
            grabber.stop();
        }
    }

    /**
     * Base64流转文件
     *
     * @param base64String Base64流
     * @param outputFile   输出文件路径
     * @return 转换结果
     */
    public static boolean convertBase64ToFile(String base64String, File outputFile) {
        // 检查 Base64 字符串是否包含数据 URI 前缀，并去除它
        if (base64String.contains(",")) {
            base64String = base64String.substring(base64String.indexOf(",") + 1);
        }

        try (FileOutputStream fileOutputStream = new FileOutputStream(outputFile)) {
            // 解码 Base64 字符串
            byte[] decodedBytes = Base64.getDecoder().decode(base64String);
            // 将字节写入文件
            fileOutputStream.write(decodedBytes);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean appendBase4ToAudioFile(String base4Data, File outputFile) {
        byte[] decodedBytes = Base64.getDecoder().decode(base4Data);
        try (ByteArrayInputStream bais = new ByteArrayInputStream(decodedBytes);
             AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(bais)) {
            if (!outputFile.exists()) {
                // 如果输出文件不存在，则创建新文件并写入头部信息和初始数据块
                AudioSystem.write(audioInputStream, AudioFileFormat.Type.WAVE, outputFile);
            } else {
                // 如果输出文件已经存在，则读取现有内容并追加新的数据块
                try (AudioInputStream existingAIS = AudioSystem.getAudioInputStream(outputFile)) {
                    AudioFormat format = existingAIS.getFormat();
                    long frameLength = existingAIS.getFrameLength() + audioInputStream.getFrameLength();
                    try (SequenceInputStream sequenceIS = new SequenceInputStream(existingAIS, audioInputStream);
                         AudioInputStream appendedAIS = new AudioInputStream(sequenceIS, format, frameLength)) {
                        File tempOutputFile = new File(outputFile.getAbsolutePath() + ".tmp");
                        AudioSystem.write(appendedAIS, AudioFileFormat.Type.WAVE, tempOutputFile);
                        if (!outputFile.delete() || !tempOutputFile.renameTo(outputFile)) {
                            throw new IOException("无法替换原始输出文件");
                        }
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("追加base64流到文件中失败，失败原因为", e);
            return false;
        }
    }

    /**
     * 从远程url下载到本地path，若本地path文件已存在，则直接使用本地path文件；若不存在则下载到本地path
     *
     * @param url  文件远程url
     * @param path 本地path
     * @return 是否需要下载到本地path
     */
    public static boolean download(String url, String path) {
        File file = new File(path);
        if (file.exists()) {
            return false;
        }
        if (StrUtil.isBlank(url)) {
            throw new AiServerException(ResultCode.NO_EXIST.getCode(), "文件不存在并且远程url为空");
        }

        File directory = file.getParentFile();
        // 如果目录不存在，则创建它
        if (Objects.nonNull(directory) && !directory.exists()) {
            boolean isCreated = directory.mkdirs();
            if (!isCreated) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "文件下载失败");
            }
        }

        try (InputStream in = new BufferedInputStream(new URL(url).openStream());
             FileOutputStream fileOutputStream = new FileOutputStream(path)) {
            byte dataBuffer[] = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(dataBuffer, 0, 1024)) != -1) {
                fileOutputStream.write(dataBuffer, 0, bytesRead);
            }
            return true;
        } catch (IOException e) {
            log.error("文件下载失败，失败原因为", e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "文件下载失败");
        }
    }

    /**
     * 获取文件格式
     *
     * @param filePath 文件路径
     * @return 文件格式
     */
    public static String getFormat(String filePath) {
        // 部分常见格式
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put("mp3", "mp3");
        formatMap.put("wav", "wav");
        formatMap.put("ogg", "ogg");
        formatMap.put("flac", "flac");
        formatMap.put("aac", "aac");

        String extension = filePath.substring(filePath.lastIndexOf(".") + 1).toLowerCase();
        String res = formatMap.getOrDefault(extension, null);
        if (StrUtil.isBlank(res)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "文件扩展名extension=[" + extension + "]未知");
        }
        return res;
    }

    public static double getAudioDuration(String audioUrl) throws IOException {
        // 创建临时文件
        Path tempFile = Files.createTempFile("audio_", audioUrl.endsWith(".mp3") ? ".mp3" : ".wav");
        File file = tempFile.toFile();

        try {
            // 下载文件
            HttpURLConnection conn = (HttpURLConnection) new URL(audioUrl).openConnection();
            // 15 秒连接超时
            conn.setConnectTimeout(15000);
            // 30 秒读取超时
            conn.setReadTimeout(30000);

            int responseCode = conn.getResponseCode();
            if (responseCode != 200) {
                throw new IOException("Failed to download file, HTTP status: " + responseCode + ", URL: " + audioUrl);
            }

            try (var inputStream = conn.getInputStream()) {
                Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
            }

            // 验证文件大小
            long fileSize = Files.size(tempFile);
            if (fileSize < 1024) {
                throw new IOException("Downloaded file is too small (" + fileSize + " bytes) for URL: " + audioUrl);
            }
            log.info("Downloaded file size: {} bytes", fileSize);

            // 解析时长
            try (FileInputStream inputStream = new FileInputStream(file)) {
                Parser parser = new Mp3Parser();
                BodyContentHandler handler = new BodyContentHandler();
                Metadata metadata = new Metadata();
                metadata.set(Metadata.CONTENT_TYPE, "audio/mpeg");
                parser.parse(inputStream, handler, metadata, null);

                // 提取时长
                String durationStr = metadata.get("xmpDM:duration");
                if (durationStr != null && !durationStr.isEmpty()) {
                    try {
                        double duration = Double.parseDouble(durationStr);
                        if (duration > 0) {
                            return duration;
                        }
                    } catch (NumberFormatException e) {
                        log.info("Invalid duration format: {}", durationStr);
                    }
                }

                // 如果元数据缺失，估算时长
                double bitRate = 16000;
                double duration = (fileSize * 8.0) / bitRate;
                if (duration > 0) {
                    log.info("Estimated duration based on bitrate: {} seconds", duration);
                    return duration;
                }
                throw new IOException("No valid duration found for file: " + audioUrl);
            } catch (Exception e) {
                // 估算时长
                double bitRate = 16000;
                double duration = (fileSize * 8.0) / bitRate;
                if (duration > 0) {
                    log.info("Estimated Exception duration based on bitrate: {} seconds", duration);
                    return duration;
                }
                throw new IOException("Failed to parse audio duration for file: " + audioUrl, e);
            }
        } finally {
            // 删除临时文件
            try {
                Files.deleteIfExists(tempFile);
            } catch (IOException e) {
                log.error("Failed to delete temporary file: {}, error:", tempFile, e);
            }
        }
    }

    public static void main(String[] args) throws Exception {
        String inputAudioPath = "/Users/<USER>/Desktop/voice/E5637.wav";
        List<File> fileList = splitAudioFile(new File(inputAudioPath), 30 * 60, 5 * 60);
        System.out.println(fileList.size());
//        double wavDuration = getAudioDuration("http://call-hub.jhjj.paas.test/contactCenter/api/downloadS3/easypeso/freeswitch/call_out/20250422-181659yrRobot_202504221816080000085.mp3");
//        int audioDuration = (int) (wavDuration);
//        System.out.println(wavDuration);
    }

    public static String getSuffix(String fileName) {
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    public static String getPrefix(String fileName) {
        return fileName.substring(fileName.lastIndexOf("."));
    }
}
