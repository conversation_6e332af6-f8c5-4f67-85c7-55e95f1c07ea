package com.yirendai.voiceaiserver.util;

import com.alibaba.excel.EasyExcel;
import com.yirendai.robot.modules.call.dto.CellDto;
import com.yirendai.workbench.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static org.springblade.core.tool.api.ResultCode.INTERNAL_SERVER_ERROR;

@Slf4j
public class ExcelUtil
{
    /**
     * 导入Excel
     * @param file
     * @return
     * @throws IOException
     */
    public static List<List<CellDto>> importExcel(MultipartFile file) throws IOException {
        List<List<CellDto>> data = new ArrayList<>();

        // 获取文件输入流
        InputStream inputStream = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(inputStream);
        workbook.setForceFormulaRecalculation(false);
        // 获取第一个工作表
        Sheet sheet = workbook.getSheetAt(0);

        // 遍历行
        Iterator<Row> rowIterator = sheet.iterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();

            // 遍历单元格
            Iterator<Cell> cellIterator = row.cellIterator();
            List<CellDto> rowData = new ArrayList<>();
            while (cellIterator.hasNext()) {
                Cell cell = cellIterator.next();
                switch (cell.getCellType()) {
                    case STRING:
                        rowData.add(new CellDto(cell.getColumnIndex(),cell.getStringCellValue()));
                        break;
                    case NUMERIC:
                        // 将所有数值类型的数据都转换为字符串
                        DecimalFormat df = new DecimalFormat("#");
                        rowData.add(new CellDto(cell.getColumnIndex(),df.format(cell.getNumericCellValue())));
                        break;
                    case BOOLEAN:
                        rowData.add(new CellDto(cell.getColumnIndex(),String.valueOf(cell.getBooleanCellValue())));
                        break;
                    case FORMULA:
                        rowData.add(new CellDto(cell.getColumnIndex(),cell.getCellFormula()));
                        break;
                    default:
                        rowData.add(new CellDto(cell.getColumnIndex(),""));
                        break;
                }
            }
            data.add(rowData);
        }
        workbook.close();
        inputStream.close();
        return data;
    }

    /*
    copy自bladex的ExcelUtil.export()
     */
    public static <T> void exportToResponse(HttpServletResponse response, String fileName, String sheetName, List<T> dataList, Class<T> clazz) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding(Charsets.UTF_8.name());
            fileName = URLEncoder.encode(fileName, Charsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), clazz).sheet(sheetName).doWrite(dataList);
        } catch (Throwable e) {
            log.error("导出失败", e);
            throw new BusinessException(INTERNAL_SERVER_ERROR, e);
        }
    }
}
