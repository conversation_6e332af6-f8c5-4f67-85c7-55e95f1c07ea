package com.yirendai.voiceaiserver.util;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;

/**
 * MD5工具类
 * 
 * <AUTHOR>
 * 
 */
@Slf4j
public class MD5Utils {

	public static void main(String[] args) {
		String s = "中国";
		System.out.println(md5(s));
	}

	/**
	 * md5加密 暂时注释掉（）
	 * 
	 * @param str
	 * @return
	 */
	public static String md5(String str) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(str.getBytes("UTF-8"));
			byte[] byteDigest = md.digest();
			int i;
			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < byteDigest.length; offset++) {
				i = byteDigest[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			// 32位加密
			return buf.toString();
			// 16位的加密
			// return buf.toString().substring(8, 24);
		} catch (Exception e) {
			log.error("calculate md5 error", e);
			return null;
		}
	}

	public static String digist(String text) {
		String result = "";

		try {
			MessageDigest md = MessageDigest.getInstance("md5");
			// 定义编码方式
			byte[] bufs = text.getBytes("UTF-8");
			md.update(bufs);
			byte[] b = md.digest();
			int i;
			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			result = buf.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	public static byte[] MD52Byte(String text) {
		byte[] returnByte = null;

		try {
			MessageDigest md = MessageDigest.getInstance("md5");
			// 定义编码方式
			returnByte = md.digest(text.getBytes("UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return returnByte;
	}

	public static String byte2hex(byte[] b) {
		String hs = "";
		String stmp = "";
		for (int n = 0; n < b.length; n++) {
			stmp = (Integer.toHexString(b[n] & 0XFF));
			if (stmp.length() == 1)
				hs = hs + "0" + stmp;
			else
				hs = hs + stmp;
			if (n < b.length - 1)
				hs = hs + ":";
		}
		return hs.toUpperCase();
	}
}
