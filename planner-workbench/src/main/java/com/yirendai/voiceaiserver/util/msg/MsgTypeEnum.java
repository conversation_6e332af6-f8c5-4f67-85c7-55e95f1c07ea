package com.yirendai.voiceaiserver.util.msg;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  通知异常码
 */
@Getter
@AllArgsConstructor
public enum MsgTypeEnum {
    SUMMARY(0,"通话总结"),

    USER_TAG(1,"用户标签"),

    CONVERSATION_INSPECT(2,"内容稽查"),

    CALL_RESERVATION(3,"下次通话建议"),

    MQ_SEND_ERROR(4,"mq消息发送异常"),

    QW_MESSAGE_SUMMARY_INIT_ERROR(5,"企微消息小结任务初始化异常"),

    SYNC_CONTENT_NEWS(6,"同步AI新闻"),


    AI_ROBOT_TASK_EXECUTE_ERROR(7,"外呼机器人执行过程中异常"),

    AI_ROBOT_TASK_AUDIO_ERROR(8,"外呼机器人节点音频批量生成异常"),


    AI_ROBOT_TTS_ERROR(9,"外呼机器人TTS异常"),


    AI_ROBOT_TASK_CALL_ERROR(10,"执行AI外呼呼叫"),

    WRITE_DATA_TO_TARGET_TASK(11,"写入数据到目标呼叫任务"),

    AI_ROBOT_TASK_CALL_RECORD_ERROR(12,"执行AI外呼任务通话记录异常"),

    AI_ROBOT_CALL_IN_ERROR(13,"执行AI呼入异常"),

    ;

    /**
     * 中文信息描述
     */
    private final Integer code;
    private final String message;

    public static MsgTypeEnum getByCode(Integer code) {
        for (MsgTypeEnum value : MsgTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
