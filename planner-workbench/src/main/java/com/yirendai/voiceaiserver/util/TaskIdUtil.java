package com.yirendai.voiceaiserver.util;

import com.yirendai.voiceaiserver.enums.PrimaryIntentEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务ID工具类
 * 支持子意图类型的任务ID生成和解析
 * 
 * 任务ID格式：
 * - 普通任务：{taskType}-{sourceType}_{chatContactId}
 * - 子意图任务：{subIntentType}_{taskType}-{sourceType}_{chatContactId}
 * 
 * <AUTHOR>
 */
@Slf4j
public class TaskIdUtil {
    
    private static final String SEPARATOR = "_";
    private static final String TASK_SEPARATOR = "-";
    
    /**
     * 生成普通任务ID
     * 格式：{taskType}-{sourceType}_{chatContactId}
     */
    public static String generateTaskId(String taskType, String sourceType, Long chatContactId) {
        return taskType + TASK_SEPARATOR + sourceType + SEPARATOR + chatContactId;
    }
    
    /**
     * 生成子意图任务ID
     * 格式：{taskType}-{sourceType}_{subIntentType}_{chatContactId}
     */
    public static String generateSubIntentTaskId(String subIntentType, String taskType, String sourceType, Long chatContactId) {
        return taskType + TASK_SEPARATOR + sourceType + SEPARATOR + subIntentType + SEPARATOR + chatContactId;
    }
    
    /**
     * 生成节点组任务ID
     * 格式：{taskType}-{sourceType}_{subIntentType}_{nodeGroup}_{chatContactId}
     */
    public static String generateNodeGroupTaskId(String subIntentType, String nodeGroup, String taskType, String sourceType, Long chatContactId) {
        return taskType + TASK_SEPARATOR + sourceType + SEPARATOR + subIntentType + SEPARATOR + nodeGroup + SEPARATOR + chatContactId;
    }
    
    /**
     * 生成子意图任务ID（基于主意图枚举）
     */
    public static String generateSubIntentTaskId(PrimaryIntentEnum intentEnum, String taskType, String sourceType, Long chatContactId) {
        return generateSubIntentTaskId(String.valueOf(intentEnum.getId()), taskType, sourceType, chatContactId);
    }
    
    /**
     * 解析任务ID，提取子意图类型
     * @param taskId 任务ID
     * @return 子意图类型，如果没有则返回null
     */
    public static String extractSubIntentType(String taskId) {
        if (taskId == null || taskId.trim().isEmpty()) {
            return null;
        }
        
        // 检查是否包含子意图类型
        // 支持格式：
        // - {taskType}-{sourceType}_{subIntentType}_{chatContactId}
        // - {taskType}-{sourceType}_{subIntentType}_{nodeGroup}_{chatContactId}
        String[] parts = taskId.split(TASK_SEPARATOR, 2);
        if (parts.length == 2) {
            String afterTaskType = parts[1];
            String[] subParts = afterTaskType.split(SEPARATOR);
            if (subParts.length >= 3) {
                // 第二部分总是子意图类型
                return subParts[1];
            }
        }
        
        return null;
    }
    
    /**
     * 解析任务ID，提取节点组
     * @param taskId 任务ID
     * @return 节点组，如果没有则返回null
     */
    public static String extractNodeGroup(String taskId) {
        if (taskId == null || taskId.trim().isEmpty()) {
            return null;
        }
        
        // 检查是否包含节点组
        // 节点组任务ID格式：{taskType}-{sourceType}_{subIntentType}_{nodeGroup}_{chatContactId}
        String[] parts = taskId.split(TASK_SEPARATOR, 2);
        if (parts.length == 2) {
            String afterTaskType = parts[1]; // sourceType_subIntentType_nodeGroup_chatContactId
            String[] subParts = afterTaskType.split(SEPARATOR);
            if (subParts.length >= 4) {
                // 第三部分是节点组
                return subParts[2];
            }
        }
        
        return null;
    }
    
    /**
     * 从任务ID中提取基础任务ID（去掉子意图类型和节点组）
     * @param taskId 完整任务ID
     * @return 基础任务ID
     */
    public static String extractBaseTaskId(String taskId) {
        String subIntentType = extractSubIntentType(taskId);
        if (subIntentType != null) {
            // 支持格式：
            // - {taskType}-{sourceType}_{subIntentType}_{chatContactId}
            // - {taskType}-{sourceType}_{subIntentType}_{nodeGroup}_{chatContactId}
            // 移除子意图类型和节点组，变成：{taskType}-{sourceType}_{chatContactId}
            String[] parts = taskId.split(TASK_SEPARATOR, 2);
            if (parts.length == 2) {
                String taskType = parts[0];
                String afterTaskType = parts[1]; // sourceType_subIntentType_... 
                String[] subParts = afterTaskType.split(SEPARATOR);
                if (subParts.length >= 3) {
                    // 重组为：taskType-sourceType_chatContactId
                    String chatContactId = subParts[subParts.length - 1]; // 最后一部分总是chatContactId
                    return taskType + TASK_SEPARATOR + subParts[0] + SEPARATOR + chatContactId;
                }
            }
        }
        return taskId;
    }
    
    /**
     * 检查任务ID是否为子意图任务
     */
    public static boolean isSubIntentTask(String taskId) {
        return extractSubIntentType(taskId) != null;
    }
    
    /**
     * 从任务ID中提取chatContactId
     */
    public static Long extractChatContactId(String taskId) {
        if (taskId == null || taskId.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 支持格式：
            // - {taskType}-{sourceType}_{chatContactId}
            // - {taskType}-{sourceType}_{subIntentType}_{chatContactId}
            // - {taskType}-{sourceType}_{subIntentType}_{nodeGroup}_{chatContactId}
            String[] parts = taskId.split(TASK_SEPARATOR, 2);
            if (parts.length == 2) {
                String afterTaskType = parts[1];
                String[] subParts = afterTaskType.split(SEPARATOR);
                if (subParts.length >= 2) {
                    // 最后一部分总是chatContactId
                    return Long.parseLong(subParts[subParts.length - 1]);
                }
            }
        } catch (NumberFormatException e) {
            log.warn("无法从任务ID中提取chatContactId: {}", taskId, e);
        }
        
        return null;
    }
    
    /**
     * 从任务ID中提取sourceType
     */
    public static String extractSourceType(String taskId) {
        if (taskId == null || taskId.trim().isEmpty()) {
            return null;
        }
        
        // 支持格式：
        // - {taskType}-{sourceType}_{chatContactId}
        // - {taskType}-{sourceType}_{subIntentType}_{chatContactId}
        // - {taskType}-{sourceType}_{subIntentType}_{nodeGroup}_{chatContactId}
        String[] parts = taskId.split(TASK_SEPARATOR, 2);
        if (parts.length == 2) {
            String afterTaskType = parts[1];
            String[] subParts = afterTaskType.split(SEPARATOR);
            if (subParts.length >= 2) {
                // 第一部分总是sourceType
                return subParts[0];
            }
        }
        
        return null;
    }
    
    /**
     * 生成用于查询主意图的任务ID模式
     * 支持查询指定子意图类型或所有子意图类型的主意图结果
     * 
     * @param subIntentType 子意图类型，如果为null则查询所有类型
     * @param taskType 任务类型
     * @param sourceType 数据源类型
     * @param chatContactId 对话ID
     * @return 用于查询的任务ID模式
     */
    public static String generateIntentQueryPattern(String subIntentType, String taskType, String sourceType, Long chatContactId) {
        if (subIntentType != null) {
            // 查询指定子意图类型
            return generateSubIntentTaskId(subIntentType, taskType, sourceType, chatContactId);
        } else {
            // 查询普通任务ID
            return generateTaskId(taskType, sourceType, chatContactId);
        }
    }
}
