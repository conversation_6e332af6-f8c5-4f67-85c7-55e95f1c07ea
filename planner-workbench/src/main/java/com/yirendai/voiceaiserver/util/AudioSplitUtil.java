package com.yirendai.voiceaiserver.util;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.vo.response.SegmentVo;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.avutil.LogCallback;
import org.bytedeco.ffmpeg.global.avutil;
import org.bytedeco.javacpp.BytePointer;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.FFmpegLogCallback;
import org.bytedeco.javacv.Frame;
import javax.sound.sampled.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;
import static org.bytedeco.ffmpeg.global.avutil.*;

/**
 * <AUTHOR>
 * @time 2024/3/5 10:17
 **/
@Slf4j
public class AudioSplitUtil {

    private static final String FFMPEG_LOG_PREFIX = "[FFmpeg] ";

    /**
     * 切割音频文件
     * @param inputAudioPath 输入文件路径
     * @param outputAudioPath 输出文件路径
     * @param startMillisecond 开始时间（秒）
     * @param durationMilliseconds 结束时间（秒），值为0时截止到音频末尾
     */
    public static void splitAudio(String inputAudioPath, String outputAudioPath, int startMillisecond, int durationMilliseconds) {
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputAudioPath);) {
            grabber.start();
            log.info("总时长：{}", grabber.getLengthInTime());
            try (FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputAudioPath, grabber.getAudioChannels());) {
                recorder.setSampleRate(grabber.getSampleRate());
                recorder.setAudioCodec(grabber.getAudioCodec());
                recorder.start();
                // 跳过开始的几毫秒
                grabber.setTimestamp(startMillisecond * 1000L); // 转换毫秒到微秒
                Frame frame;

                long endTime = durationMilliseconds == 0 ? grabber.getLengthInTime() : durationMilliseconds * 1000L; // 转换毫秒到微秒
                while ((frame = grabber.grabFrame()) != null) {
                    if (grabber.getTimestamp() >= endTime) {
                        break; // 停止录制
                    }
                    recorder.record(frame);
                }
                recorder.stop();
                recorder.release();
                grabber.stop();
            }
        } catch (Exception e) {
            log.error("splitAudio error", e);
        }
    }

    /**
     * 按照切割信息切割文件后拼接成一个音频
     * @param inputAudioPath 原文件路径
     * @param outputAudioPath 输出文件路径
     * @param segmentVoList 切割信息 为空时直接将完整原文件输出到输出路径
     */
    public static void splitAndConcatenateAudio(String inputAudioPath, String outputAudioPath, List<SegmentVo> segmentVoList) throws IOException {
        if (CollUtil.isEmpty(segmentVoList)) {
            Path sourcePath = Paths.get(inputAudioPath);
            Path destinationPath = Paths.get(outputAudioPath);
            Files.copy(sourcePath, destinationPath, StandardCopyOption.REPLACE_EXISTING);
            return;
        }
        segmentVoList = segmentVoList.stream().sorted(Comparator.comparing(SegmentVo::getStart)).collect(Collectors.toList());

        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputAudioPath)) {
            grabber.setFormat(FileUtil.getFormat(inputAudioPath));
            grabber.start();
            try (FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputAudioPath, grabber.getAudioChannels())) {
                recorder.setSampleRate(grabber.getSampleRate());
                recorder.setAudioCodec(grabber.getAudioCodec());
                recorder.start();

                Frame frame;
                for (SegmentVo s : segmentVoList) {
                    long start = s.getStart() * 1000; // 转换毫秒到微秒
                    if (start >= grabber.getLengthInTime()) {
                        break;
                    }
                    grabber.setTimestamp(start); // 跳过开始的几微秒
                    long endTime = s.getEnd() * 1000L;
                    while ((frame = grabber.grabFrame()) != null) {
                        if (grabber.getTimestamp() >= endTime) {
                            break; // 停止录制
                        }
                        recorder.record(frame);
                    }
                }
                recorder.stop();
                recorder.release();
                grabber.stop();
            }
        }
    }

    /**
     * 按照切割信息切割文件后拼接成一个音频，输出音频格式固定为wav
     * @param inputAudioPath 原文件路径
     * @param outputAudioPath 输出文件路径
     * @param segmentVoList 切割信息 为空时只转换格式
     * @apiNote 文件切割部分会打印FFmpeg日志
     */
    public static void splitAndConcatenateAudioToWav(String inputAudioPath, String outputAudioPath, List<SegmentVo> segmentVoList) throws Exception {
        if (!outputAudioPath.endsWith(".wav")) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "输出文件格式目前仅支持wav");
        }
        if (CollUtil.isEmpty(segmentVoList)) {
            if (!inputAudioPath.endsWith(".wav")) {
                FileUtil.convertAmrToWav(new File(inputAudioPath), new File(outputAudioPath));
            }
            return;
        }
        segmentVoList = segmentVoList.stream().sorted(Comparator.comparing(SegmentVo::getStart)).collect(Collectors.toList());

//        avutil.av_log_set_level(AV_LOG_ERROR);
//        LogCallback logCallback = new FFmpegLogCallback() {
//            @Override
//            public void call(int level, BytePointer msg) {
//                String message = msg.getString().trim();
//                if (level <= AV_LOG_ERROR) {
//                    log.error(FFMPEG_LOG_PREFIX + message);
//                }
//            }
//        };
//        avutil.setLogCallback(logCallback);

        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputAudioPath)) {
            grabber.setFormat(FileUtil.getFormat(inputAudioPath));
            grabber.start();
            try (FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputAudioPath, grabber.getAudioChannels())) {
                recorder.setAudioCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_PCM_S16LE);
                recorder.setFormat("wav");
                recorder.setSampleRate(grabber.getSampleRate());
                recorder.setAudioChannels(grabber.getAudioChannels());

                recorder.start();

                Frame frame;
                for (SegmentVo s : segmentVoList) {
                    long start = s.getStart() * 1000; // 转换毫秒到微秒
                    if (start >= grabber.getLengthInTime()) {
                        break;
                    }
                    grabber.setTimestamp(start); // 跳过开始的几微秒
                    long endTime = s.getEnd() * 1000L;
                    while ((frame = grabber.grabFrame()) != null) {
                        if (grabber.getTimestamp() >= endTime) {
                            break; // 停止录制
                        }
                        recorder.record(frame);
                    }
                }
                recorder.stop();
                recorder.release();
                grabber.stop();
            }
        }
    }

    public static double getWavDuration(File file) throws IOException, UnsupportedAudioFileException {
        AudioFileFormat fileFormat = AudioSystem.getAudioFileFormat(file);

        // 获取格式属性
        float sampleRate = fileFormat.getFormat().getSampleRate();
        int channels = fileFormat.getFormat().getChannels();
        int sampleSizeInBits = fileFormat.getFormat().getSampleSizeInBits();

        // 数据大小（以字节为单位）
        long dataSizeInBytes = file.length() - fileFormat.getByteLength() + fileFormat.getFrameLength() * fileFormat.getFormat().getFrameSize();

        // 计算持续时间
        return dataSizeInBytes / (sampleRate * channels * (sampleSizeInBits / 8.0));
    }

    public static void main(String[] args) throws IOException {
        String inputAudioPath = "/Users/<USER>/Desktop/voice/491339.wav"; // 输入音频文件路径
        String outputAudioPath = "/Users/<USER>/Desktop/voice/1.wav"; // 输出音频文件路径
        List<SegmentVo> list = new ArrayList<>();
        list.add(SegmentVo.builder().start(5670L).end(34310L).build());
        list.add(SegmentVo.builder().start(71170L).end(78250L).build());

        splitAndConcatenateAudio(inputAudioPath, outputAudioPath, list);
        System.out.println("Audio has been split successfully.");
    }
}
