package com.yirendai.voiceaiserver.util;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import javax.annotation.PostConstruct;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
public class HttpClientUtil {

    private static HttpClient httpClient;

    //初始化pool及设置http超时时间
    @PostConstruct
    public void init() {
        RequestConfig config = RequestConfig.custom().setSocketTimeout(1000 * 30)//数据传输处理时间
                .setConnectTimeout(1000 * 10)//建立连接的timeout时间
                .setConnectionRequestTimeout(2000)//从连接池获取链接超时时间
                .build();
        httpClient = HttpClients.custom().setDefaultRequestConfig(config).setMaxConnTotal(100)
                .setMaxConnPerRoute(50).build();
    }
    public static HttpClientUtil getInstance() {
        return HttpClientUtilHolder.instance;
    }

    private HttpClientUtil() {}

    private static class HttpClientUtilHolder {
        private final static HttpClientUtil instance = new HttpClientUtil();
    }

    public String get(String url) throws IOException {
        String result;
        long start = System.currentTimeMillis();
        HttpGet httpGet = new HttpGet(url);
        try {
            HttpResponse httpResponse = httpClient.execute(httpGet);
            if(httpResponse.getStatusLine().getStatusCode()!= HttpStatus.SC_OK){
                log.error("HTTP GET failed,url = {}, statusLine = {}",url,httpResponse.getStatusLine());
                return null;
            }
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity,"utf-8");
            log.info("http get callback uri {}, result:{}", url, result);
        } catch (IOException e) {
            log.error("wrong HttpClient get method ! url = {} ,exception message:{} ",url,e.getMessage());
            throw e;
        }finally {
            httpGet.reset();
        }
        log.info("execute http GET url = {}, spend time:{} milliseconds",url,System.currentTimeMillis()-start);
        return result;
    }

    public String post(String uri,List<NameValuePair> nameValuePairs) throws IOException {
        String result;
        long start = System.currentTimeMillis();
        HttpPost httpPost = new HttpPost(uri);
        StringBuffer params = new StringBuffer("");
        httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, Consts.UTF_8));
        try {
            HttpResponse httpResponse = httpClient.execute(httpPost);
            if(!CollectionUtils.isEmpty(nameValuePairs)){
                for(NameValuePair nameValuePair:nameValuePairs){
                    params.append(nameValuePair.getName()).append("=").append(nameValuePair.getValue()).append("&");
                }
            }
            if(httpResponse.getStatusLine().getStatusCode()!= HttpStatus.SC_OK){
                log.error("HTTP POST failed,uri = {}, params = {} ,statusLine = {}",uri,params.toString(),httpResponse.getStatusLine());
                return null;
            }
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity,Consts.UTF_8);
            log.info("http post form callback uri {}, param {}, result:{}", uri, params, result);
        } catch (IOException e) {
            log.error("wrong HttpClient post method ! uri = {}, params = {} ,exception message:{}",uri,params.toString(),e.getMessage());
            throw e;
        }finally {
            httpPost.reset();
        }
        log.info("execute http POST uri = {}, params= {} ,spend time:{} milliseconds",uri,params.toString(),System.currentTimeMillis()-start);
        return result;
    }

    public String postForJSon(String uri,String paramsJson) throws Exception {
    	HttpPost httpPost = null;
    	String result = null;
    	 try {
    		 httpPost = new HttpPost(uri);
			 StringEntity stringEntity = new StringEntity(paramsJson,ContentType.APPLICATION_JSON);
			 httpPost.setEntity(stringEntity);
			 HttpResponse httpResponse = httpClient.execute(httpPost);
			 if(httpResponse.getStatusLine().getStatusCode()!= HttpStatus.SC_OK){
	            log.error("HTTP POST failed,uri = {}, params = {} ,statusLine = {}",uri,paramsJson,httpResponse.getStatusLine());
	            return result;
			 }
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity,Consts.UTF_8);
            log.info("http post callback uri {}, param {}, result:{}", uri, paramsJson, result);
            return result;
		} catch (Exception e) {
			log.error("HTTP POST failed,uri:{},paramsJson:{},exception message:{}",uri,paramsJson,e.getMessage());
			throw e;
		} finally {
			if(httpPost !=null){
				httpPost.reset();
			}
		}
    }
    
    public JSONObject postForJSONObject(String url,List<NameValuePair> nameValuePairs) throws IOException {
        JSONObject result = null;
        try{
            result = JSONObject.parseObject(post(url, nameValuePairs));
        }catch(Exception e){
            result = new JSONObject();
            log.error("postForJSONObject fromObject error:",e);
        }
        return result;
    }

    public List<NameValuePair> postParameter(JSONObject paramJsonObject) {
        List<NameValuePair> list = new ArrayList<NameValuePair>();
        Set<String> keySet = paramJsonObject.keySet();
		for (String key : keySet) {
            list.add(new BasicNameValuePair(key, paramJsonObject.get(key).toString()));
		}
		
        return list;
    }

    // 参数sign加密
    public String getSign(JSONObject paramJsonObject, String secret) throws Exception {
        String paramJsonObjectAfterSort = sort(paramJsonObject);
        String signValue = secret + paramJsonObjectAfterSort + secret;
        return MD5Utils.byte2hex(MD5Utils.MD52Byte(signValue)); // md5加密并转换为大写
    }

    // 参数排序
    public String sort(JSONObject paramJsonObject) throws Exception {
    	Set<String> keySet = paramJsonObject.keySet();
		ArrayList<String> keys = new ArrayList<String>(keySet);
		//排序
        Collections.sort(keys);
        
        StringBuffer result = new StringBuffer("");
        for(String key:keys){
            result.append(key).append(paramJsonObject.getString(key));
        }
        return result.toString();
    }
}