package com.yirendai.voiceaiserver.util.msg;

import java.io.PrintWriter;
import java.io.StringWriter;

import com.github.benmanes.caffeine.cache.Cache;
import com.yirendai.voiceaiserver.util.HttpClientProxyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * webhook异步处理
 */
@Slf4j
@Component
public class WebHookAsync {

    @Value("${spring.profiles.active}")
    private String active;

    @Resource
    private Cache<String, Boolean> webHookErrorCache;


    /**
     * 推送企微消息
     * @param webhook webHook地址
     * @param title 标题
     * @param type 问题类型
     * @param describe 问题描述
     * @param throwable 异常，如果有就发没有就空着。
     */
    @Async("taskExecutor")
    public void webHookToBusiness(String webhook, String title, String type, String describe, Throwable throwable) {

        try {
            // 构建唯一键，用于标识特定类型和描述的错误
            String cacheKey = type + ":" + describe;
            if (throwable != null) {
                cacheKey = type + ":" + throwable.getMessage();
            }

            // 检查缓存中是否已存在该错误
            if (webHookErrorCache.getIfPresent(cacheKey) != null) {
                log.info("Error already sent within the last hour: {}", cacheKey);
                return;
            }

            WebhookMarkdown webhookMarkdown = new WebhookMarkdown();
            WebhookMarkdown.Markdown markdown = webhookMarkdown.new Markdown();
            String env = "";
            try {
                if ("prod".equals(active)) {
                    env = "生产";
                }
                else if ("dev".equals(active)) {
                    env = "开发";
                }
                else {
                    env = "测试";
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            StringBuilder conten = new StringBuilder();
            if(StringUtil.isEmpty(title)){
                conten = conten.append("异常通知");
            }else{
                conten = conten.append(title);
            }
            conten = conten.append(
                    "<font color=\"warning\">请相关同事注意</font>\n" + ">类型:<font color=\"comment\">" + type
                            + "</font>" + "\n>描述:<font color=\"comment\">" + describe + "</font>"
                            + "\n>环境:<font color=\"comment\">" + env + "</font>");
            if (throwable != null) {
                conten.append("\n异常信息：\n> ");
                String content = getStackTrace(throwable);
                if (content.length() > 500) {
                    content = content.substring(0, 500);
                }
                conten.append(content);
            }
            markdown.setContent(conten.toString());
            webhookMarkdown.setMarkdown(markdown);
            String json = JsonUtil.toJson(webhookMarkdown);
            String res = post(webhook, json );
            // 将成功发送的信息存入缓存
            webHookErrorCache.put(cacheKey, true);
            log.info(res);
        } catch (Exception ex) {
            log.error("【WEB_HOOK】webHookToBusiness推送异常 {}", ex.getMessage(), ex);
        }
    }

    //代理调用
    public String post(String url, String content ) throws Exception {
        String res = HttpClientProxyUtil.getInstance().postForJSon(url, content);
        return res;
    }

    public String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);

        try {
            throwable.printStackTrace(pw);
            return sw.toString();
        } finally {
            pw.close();
        }
    }
}
