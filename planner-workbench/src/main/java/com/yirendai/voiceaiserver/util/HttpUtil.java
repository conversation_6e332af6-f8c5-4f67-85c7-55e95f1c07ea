package com.yirendai.voiceaiserver.util;

import java.util.concurrent.TimeUnit;

import cn.hutool.http.Header;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;

/**
 * <AUTHOR>
 * @time 2023/11/14 10:11
 **/
@Slf4j
public class HttpUtil {
    public static final MediaType MEDIA_TYPE_JSON = MediaType.parse("application/json; charset=utf-8");
    public final static int MAX_IDLE_CONNECTIONS = 20;
    public final static long KEEP_ALIVE_DURATION = 30L;

    public final static int CONNECT_TIME_OUT = 6;

    public final static int WRITE_TIME_OUT = 10;

    public final static int READ_TIME_OUT = 40;


    private final static OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIME_OUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIME_OUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIME_OUT, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION, TimeUnit.MINUTES))
            .build();


    public static void stream(String apiKey, String url, String json, EventSourceListener eventSourceListener) {
        try {
            RequestBody body = RequestBody.create(MEDIA_TYPE_JSON, json);
            Request.Builder builder = new Request.Builder();
            builder.header(Header.AUTHORIZATION.getValue(), "Bearer " + apiKey);
            Request request = builder.url(url).post(body).build();
            EventSource.Factory factory = EventSources.createFactory(HTTP_CLIENT);
            log.info("aics stream请求,url: {},参数: {}", url, json);
            factory.newEventSource(request, eventSourceListener);
        } catch (Exception e) {
            log.error("aics stream请求，url: {} 失败 ,参数: {}", url, json, e);
        }
    }
}
