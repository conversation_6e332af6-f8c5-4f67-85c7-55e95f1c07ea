package com.yirendai.voiceaiserver.constant;

/**
 * AI常量
 */
public class AiConstant {

    /**
     * 大后台租户号
     */
    public static final String ADMIN_TENANT_ID = "000000";

    /**
     * 新闻知识库查询条件
     */
    public static final String HIT_TESTING_NEWS_QUERY = "发布时间是今天的最近的新闻，今天的时间是 %s";
    /**
     * 新闻知识库查询内容格式化模型
     */
    public static final String HIT_TESTING_NEWS_FORMAT_MODEL = "qwen-plus";

    /**
     * 新闻知识库查询内容格式化模型 温度
     */
    public static final Float HIT_TESTING_NEWS_FORMAT_TEMPERATURE = 0.8F;

    /**
     * 新闻知识库查询内容格式化 提示词
     */
    public static final String HIT_TESTING_NEWS_FORMAT_QUESTION = " %s 根据以上内容格式化成json 格式提出标题、内容、发布时间、文章链接 返回格式如下：{\"title\":\"标题\",\"textContent\":\"内容\",\"url\":\"新闻链接\",\"releaseTime\":\"发布时间\"}  ,只返回json内容不要返回其它内容";

    /**
     * 空数据表示
     */
    public static final String EMPTY_DATA = "EMPTY";
}
