package com.yirendai.voiceaiserver.tripartite.zhiyu.ws;

import com.yirendai.voiceaiserver.biz.asr.AsrBytes;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ws.dto.req.AudioTranslationsReq;
import com.yirendai.workbench.entity.AiPlannerChatContactSub;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ASRWebsocketRecognizer {

    private AudioTranslationsClient client;

    private String mode = "2pass";

    private String wavFormat = "pcm";

    private String chunkSize = "5,10,5";

    private int chunkInterval = 10;

    private String url  ;

    private String key  ;

    private String modelNmae = "funasr-online";

    private String model = "2pass";

    //    private String url = "ws://paas-chat-api.paas.paas.test/v1/ws/audio/translations";
//    private String key = "sk-M7DkKrU1opQ697xQ2YNbGcTZIT2v96izs6O8i0e4UFD115KR";
//    private String model = "funasr-online";
    public ASRWebsocketRecognizer(String url,String key,String wavName, Integer direction, SseEmitter sseEmitter, List<AiPlannerChatContactSub> synchronizedList,String userName ,String customerName) throws Exception {
        this.url = url;
        this.key = key;
        log.info(url);
        Map<String, String> httpHeaders = new HashMap<>();
        httpHeaders.put("Authorization", key);
        httpHeaders.put("Model", model);
        client = new AudioTranslationsClient(new URI(url), httpHeaders, direction, sseEmitter, synchronizedList,userName, customerName);
        client.connectBlocking();

        AudioTranslationsReq preRequest = new AudioTranslationsReq();
        preRequest.setMode(mode);
        preRequest.setModelName(modelNmae);
        preRequest.setChunkSize(chunkSize);
        preRequest.setEncoderChunkLookBack(4);
        preRequest.setDecoderChunkLookBack(1);
        preRequest.setChunkInterval(chunkInterval);
        preRequest.setWavName(wavName);
        preRequest.setIsSpeaking(true);
        preRequest.setWavFormat(wavFormat);
        preRequest.setAudioFs(null);
        client.sendRequest(preRequest);
    }

    public void feedChunk(byte[] chunk ) throws InterruptedException {
        client.sendBinary(chunk);
        log.info("feedChunk");
    }

    public void close(int timeout) throws InterruptedException {
        Thread.sleep(timeout);
        AudioTranslationsReq preRequest = new AudioTranslationsReq();
        preRequest.setIsSpeaking(false);
        client.sendRequest(preRequest);
        log.info("close");
        Thread.sleep(timeout);
        client.close();
    }

    public static byte[] readWavFile(String filePath, AsrBytes asrBytes, int channel) throws IOException, UnsupportedAudioFileException {
        File file = new File(filePath);
        long length = file.length();
        long skip = 0;
        if (channel == 0) {
            skip = asrBytes.getLeftLength();
            asrBytes.setLeftLength(length);
            length = length -skip;
            log.info("读取音频 skip={}，fileLength={},length={}",skip,file.length(),length);
        }
        else if (channel == 1) {
            skip = asrBytes.getRightLength();
            asrBytes.setRightLength(length);
            length = length - skip;
        }
        byte[] bytes = new byte[ (int) length ];
        try (FileInputStream fis =  new FileInputStream(file)) {
            if(skip>0){
                skip = fis.skip(skip);
                log.info("跳过{}个字节",skip);
            }
            fis.read(bytes);
        }
        return bytes;
    }

}
