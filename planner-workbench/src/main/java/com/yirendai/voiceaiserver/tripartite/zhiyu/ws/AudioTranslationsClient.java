package com.yirendai.voiceaiserver.tripartite.zhiyu.ws;

import java.io.IOException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.yirendai.voiceaiserver.enums.MsgSendDirectionEnum;
import com.yirendai.voiceaiserver.enums.YesOrNoEnum;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ws.dto.req.AudioTranslationsReq;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ws.dto.res.AudioTranslationsRes;
import com.yirendai.voiceaiserver.vo.response.AsrOnlineVO;
import com.yirendai.workbench.entity.AiPlannerChatContactSub;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.handshake.ServerHandshake;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Slf4j
public class AudioTranslationsClient extends WebSocketClient {

    private List<AiPlannerChatContactSub> synchronizedList;
    @ApiModelProperty(value = "0:理财师发起 1:客户发起")
    private Integer direction;

    /**
     * 员工姓名
     */
    String userName  ;
    /**
     * 客户姓名
     */
    String customerName  ;

    SseEmitter sseEmitter;
    public AudioTranslationsClient(URI serverUri, Map<String, String> httpHeaders,Integer direction,SseEmitter sseEmitter,List<AiPlannerChatContactSub> synchronizedList,String userName ,String customerName) {
        super(serverUri, new Draft_6455(),httpHeaders);
        this.direction = direction;
        this.sseEmitter = sseEmitter;
        this.synchronizedList = synchronizedList;
        this.userName = userName;
        this.customerName = customerName;
    }

    @Override
    public void onOpen(ServerHandshake handshakedata) {
        System.out.println("Opened connection status:"+handshakedata.getHttpStatus()+",msg:"+handshakedata.getHttpStatusMessage());

    }

    @Override
    public void onMessage(String message) {
        System.out.println("Received: " + message);
        if(sseEmitter != null){
            AudioTranslationsRes audioTranslations = JsonUtil.parse(message, AudioTranslationsRes.class);
            log.info("收到语音转文字结果文本:{}",audioTranslations.getText());
            AsrOnlineVO asrOnlineVO =  new AsrOnlineVO();
            asrOnlineVO.setMode(audioTranslations.getMode());
            asrOnlineVO.setText(audioTranslations.getText());
            asrOnlineVO.setDirection(direction);
            asrOnlineVO.setStatus(YesOrNoEnum.YES.getKey());
            if(MsgSendDirectionEnum.FROM_CRM.getCode().equals(direction)){
                asrOnlineVO.setUserName(userName);
            }else{
                asrOnlineVO.setUserName(customerName);
            }
            if(audioTranslations.getMode()!=null && audioTranslations.getMode().equals("2pass-offline")){
                AiPlannerChatContactSub aiPlannerChatContactSub = new AiPlannerChatContactSub();
                aiPlannerChatContactSub.setMode(audioTranslations.getMode());
                aiPlannerChatContactSub.setText(audioTranslations.getText());
                aiPlannerChatContactSub.setDirection(direction);
                aiPlannerChatContactSub.setCreateTime(LocalDateTime.now());
                if(MsgSendDirectionEnum.FROM_CRM.getCode().equals(direction)){
                    aiPlannerChatContactSub.setUserName(userName);
                }else{
                    aiPlannerChatContactSub.setUserName(customerName);
                }
                synchronizedList.add(aiPlannerChatContactSub);
            }
            try {
                sseEmitter.send(JsonUtil.toJson(asrOnlineVO));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

    }

    @Override
    public void onMessage(ByteBuffer bytes) {
        System.out.println("Received binary data");
        // Handle binary data if needed
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        System.out.println("Connection closed by  with code " + code + " and reason " + reason +"remote:"+remote);

    }

    @Override
    public void onError(Exception ex) {
        ex.printStackTrace();
        System.out.println("An error occurred!");
    }

    public void sendRequest(AudioTranslationsReq preRequest) {
        try {
            String jsonRequest = toJson(preRequest);
            System.out.println("Sending request: " + jsonRequest);
            this.send(jsonRequest);
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("Failed to send request: " + e.getMessage());
        }
    }

    public void sendBinary(byte[] data) {
        if (this.isOpen()) {
            log.info("Sending binary data length:"+data.length);
            this.send(data);
        } else {
            // 可以选择抛出异常或记录日志并返回
            log.error("WebSocket is not connected");
        }
    }

    private String toJson(AudioTranslationsReq preRequest) {
        try {
            return JsonUtil.toJson(preRequest);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert PreRequest to JSON", e);
        }
    }


//    public void connectWithRetry(URI serverUri, int maxRetries) {
//        int retryCount = 0;
//        while (retryCount < maxRetries && !this.isOpen()) {
//            try {
//                this.reconnect();
//                System.out.println("Connected to server on attempt " + (retryCount + 1));
//                break;
//            } catch ( Exception e) {
//                e.printStackTrace();
//                retryCount++;
//                if (retryCount >= maxRetries) {
//                    System.err.println("Failed to connect after " + maxRetries + " attempts");
//                }
//            }
//        }
//    }
}

