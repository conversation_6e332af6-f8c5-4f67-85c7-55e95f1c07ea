package com.yirendai.voiceaiserver.tripartite.zhiyu.ws;

import com.yirendai.workbench.entity.AiPlannerChatContactSub;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class Test {
    public static void main(String[] args) throws Exception {
        String wavPath = "/Users/<USER>/2025年/1月/1/yrcall_1869687765910835200.mp3";
        File file = new File(wavPath);
        List<AiPlannerChatContactSub> synchronizedList =  new ArrayList<>();
        //List<File> fileList = null;
        String url = "ws://aigc-api.aigc.paas.corp/v1/ws/audio/translations";
        String key = "sk-itQdczJ3KcYNsn4OaO8F6M2MTfb887nGYay8E6oZjOSSAz9Q";
        SseEmitter sseEmitter = new SseEmitter();
             ASRWebsocketRecognizer rcgleft = new ASRWebsocketRecognizer( url,key,"example-left.wav", 1, sseEmitter,synchronizedList, "理财师","客户");

//        File wavFile = new File(file.getAbsolutePath() + ".wav");
        try {
//            FileUtil.convertAmrToWav(file, wavFile);
            //fileList = FileUtil.splitAudioFile(wavFile, 1, 0);
            //fileList.forEach(f -> {
                try {
                    byte[] bytes = new byte[(int) file.length()];
                    try (FileInputStream fis = new FileInputStream(file)) {
                        fis.read(bytes);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    feedChunk(bytes,rcgleft  );

                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

            //});
            Thread.sleep(20000);
            synchronizedList.forEach(System.out::println);
        } catch (Exception e) {
            log.error("getContent error", e);
        } finally {
//            String textRight = rcgright.close(3);
//            System.out.println("textRight: " + textRight);
//            rcgright.printText();
//            String textLeft = rcgleft.close(3);
//            System.out.println("textLeft: " + textLeft);
//            rcgleft.printText();
//            new Thread(() -> {
//                try {
//                    while (true){
//                        System.out.println("rightText:"+rcgright.receiveMessage());
//                        System.out.println("leftText:"+rcgleft.receiveMessage());
//                    }
//
//                } catch (Exception   e) {
//                    throw new RuntimeException(e);
//                }
//            }).start();

//            if (wavFile.exists()) {
//                wavFile.delete();
//            }
//            if (CollectionUtil.isNotEmpty(fileList)) {
//                fileList.forEach(File::delete);
//            }
//            if (CollectionUtil.isNotEmpty(leftFileList)) {
//                leftFileList.forEach(File::delete);
//            }
//            if (CollectionUtil.isNotEmpty(rightFileList)) {
//                rightFileList.forEach(File::delete);
//            }
        }

    }



    public static void feedChunk(byte[] audioBytes,ASRWebsocketRecognizer rcg) throws Exception {

        if(false){
            rcg.feedChunk(audioBytes );
        }else{
            // 使用浮点数运算并确保结果不为零
//            double strideDouble = 60 * 10.0 / 10 / 1000 * 16000 * 2;
//            int stride = (int) Math.max(1, strideDouble); // 确保 stride 至少为 1
//            int chunkNum = (audioBytes.length - 1) / stride + 1;
//
//            for (int i = 0; i < chunkNum; i++) {
//                int beg = i * stride;
//                // 确保不会超出 audioBytes 的长度
//                int copyLength = Math.min(beg+stride, audioBytes.length  );
//
//                if (copyLength <= 0) {
//                    break; // 如果没有数据可以复制，退出循环
//                }
//                byte[] data = new byte[copyLength-beg];
//                System.arraycopy(audioBytes, beg, data, 0, copyLength-beg);
//                rcg.feedChunk(data, 20);
//
//            }
            int chunkSize = 1920; // 假设每个分段的大小为1024字节
            for (int i = 0; i < audioBytes.length; i += chunkSize) {
                int end = Math.min(i + chunkSize, audioBytes.length);
                byte[] chunk = Arrays.copyOfRange(audioBytes, i, end);
                rcg.feedChunk(chunk );
            }
        }



    }
}
