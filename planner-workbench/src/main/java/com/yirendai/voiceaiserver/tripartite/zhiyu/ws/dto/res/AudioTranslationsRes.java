package com.yirendai.voiceaiserver.tripartite.zhiyu.ws.dto.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 音转文结果
 */
@Data
@ToString
public class AudioTranslationsRes {

    /**
     * 模式，默认为 "online"
     */
    private String mode;

    /**
     * 实时的文本
     */
    private String text;

    /**
     * 文件名
     */
    @JsonProperty("wav_name")
    private String wavName;

    /**
     * 是否为最终结果
     */
    @JsonProperty("is_final")
    private boolean isFinal;
}
