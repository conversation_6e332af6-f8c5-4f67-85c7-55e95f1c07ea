package com.yirendai.voiceaiserver.tripartite.zhiyu.dto;

import java.util.Map;

public class ChatMessageReq {

    /**
     * 允许传入 App 定义的各变量值。
     * inputs 参数包含了多组键值对（Key/Value pairs），
     * 每组的键对应一个特定变量，每组的值则是该变量的具体值。 默认
     */
    private Map<String,String> inputs;

    /**
     * 用户输入/提问内容。
     */

    private String query;

    /**
     * streaming 流式模式（推荐）。基于 SSE（Server-Sent Events）实现类似打字机输出方式的流式返回。
     * blocking 阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）。
     * 由于 Cloudflare 限制，请求会在 100 秒超时无返回后中断。 注：Agent模式下不允许blocking。
     */
    private String responseMode;

    /**
     * （选填）会话 ID，需要基于之前的聊天记录继续对话，必须传之前消息的 conversation_id。
     */
    private String conversationId;

    /**
     * 用户标识，用于定义终端用户的身份，方便检索、统计。 由开发者定义规则，需保证用户标识在应用内唯一。
     */
    private String user;
}
