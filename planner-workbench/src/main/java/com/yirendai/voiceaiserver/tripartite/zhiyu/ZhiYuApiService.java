package com.yirendai.voiceaiserver.tripartite.zhiyu;

import java.util.List;
import javax.annotation.Resource;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.config.ZhiyuConfiguration;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.HitTestingRequest;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.RerankingModel;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.RetrievalModel;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.StopChatMessageRequest;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.HitTestingResponse;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.SuggestedResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/**
 * 智语接口
 */
@Component
@Slf4j
public class ZhiYuApiService {

    @Resource
    RestTemplate restTemplate;

    @Autowired
    private ZhiyuConfiguration zhiyuConfiguration;

    /**
     * 调用停止聊天消息接口
     * 此方法用于向外部API发送停止聊天消息请求，以停止指定任务的聊天消息
     * 它使用了Spring框架的RestTemplate类来发送HTTP请求
     * @param taskId 任务ID
     * @param userId 用户标识
     * @return 返回包含停止聊天消息结果的ResponseEntity对象
     */
    public R<String> stopChatMessage(String taskId, String userId,String aicsKey) {
        try {
            // 构建请求URL
            String url = zhiyuConfiguration.getAiCsUrl() + "/chat-messages/" + taskId + "/stop";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + aicsKey);

            // 创建请求体
            StopChatMessageRequest request = new StopChatMessageRequest();
            request.setUser(userId);
            // 创建 HttpEntity 对象
            HttpEntity<StopChatMessageRequest> entity = new HttpEntity<>(request, headers);

            // 发送 POST 请求
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            // 检查响应状态码
            if (response.getStatusCode().is2xxSuccessful()) {
                return R.data(response.getBody());
            }
            else {
                return R.fail(response.getStatusCodeValue(), "请求失败: " + response.getBody());
            }
        } catch (RestClientException e) {
            log.error("停止聊天消息异常", e);
            return R.fail(500, "网络请求异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("停止聊天消息异常", e);
            return R.fail(500, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 调用知识库命中测试接口
     * 此方法用于向外部API发送命中测试请求，以获取测试结果
     * 它使用了Spring框架的RestTemplate类来发送HTTP请求
     * @param query 命中测试的查询字符串，是发送请求时需要的参数
     * @return 返回包含命中测试结果的ResponseEntity对象
     */
    public R<HitTestingResponse> hitTesting(String query) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + zhiyuConfiguration.getClsKey());

        // 创建命中测试请求对象
        HitTestingRequest request = new HitTestingRequest();
        request.setQuery(query);

        // 设置检索模型参数
        RetrievalModel retrievalModel = new RetrievalModel();
        retrievalModel.setSearchMethod("hybrid_search");
        retrievalModel.setRerankingEnable(false);
        retrievalModel.setRerankingModel(null);

        // 设置重新排序模型参数
        RerankingModel rerankingModel = new RerankingModel();
        rerankingModel.setRerankingProviderName("");
        rerankingModel.setRerankingModelName("");

        // 将重新排序模型参数设置到检索模型中
        retrievalModel.setRerankingModel(rerankingModel);
        retrievalModel.setWeights(null);
        retrievalModel.setTopK(5);
        retrievalModel.setScoreThresholdEnabled(false);
        retrievalModel.setScoreThreshold(null);
        request.setRetrievalModel(retrievalModel);
        // 创建 HttpEntity 对象
        HttpEntity<HitTestingRequest> entity = new HttpEntity<>(request, headers);

        String url = zhiyuConfiguration.getClsUrl() + "/datasets/" + zhiyuConfiguration.getClsDataset() + "/hit-testing";
        // 发送 POST 请求
        ResponseEntity<HitTestingResponse> response = restTemplate.postForEntity(url, entity, HitTestingResponse.class);
        // 检查响应状态码
        if (response.getStatusCode().is2xxSuccessful()) {
            log.info("hitTesting 新闻:{}",response.getBody());
            return R.data(response.getBody() );
        }
        else {
            log.info("hitTesting fail: {} {}",response.getStatusCodeValue(),response.getBody());
            return R.fail(  "请求失败: " + response.getBody());
        }
    }

    /**
     * 获取下一轮建议问题列表
     * @param messageId
     * @return
     */
    public R<List<String>> suggested(String messageId, String userId) {
        try {
            // 构建请求URL
            String url = zhiyuConfiguration.getAiCsUrl() + "/messages/" + messageId + "/suggested?user="+userId;

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + zhiyuConfiguration.getAiCsKey());
            HttpEntity  entity = new HttpEntity<>(   headers);
            // 发送 请求
            ResponseEntity<SuggestedResponse> response =restTemplate.exchange(url, HttpMethod.GET, entity, SuggestedResponse.class);

            // 检查响应状态码
            if (response.getStatusCode().is2xxSuccessful()) {
                return R.data(response.getBody().getData());
            }
            else {
                return R.fail(response.getStatusCodeValue(), "请求失败: " + response.getBody());
            }
        } catch (RestClientException e) {
            log.error("suggested 获取下一轮建议问题列表异常 {}",e.getMessage() ,e);
            return R.fail( "网络请求异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("suggested 获取下一轮建议问题列表异常", e);
            return R.fail( "系统异常: " + e.getMessage());
        }

    }
}
