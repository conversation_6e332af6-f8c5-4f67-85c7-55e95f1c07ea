package com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EventResponse {

    private String event;
    @ApiModelProperty(value = "任务 ID，用于请求跟踪和下方的停止响应接口")
    private String task_id;
    @ApiModelProperty(value = "消息唯一 ID")
    private String message_id;
    @ApiModelProperty(value = "会话 ID")
    private String conversation_id;

    @ApiModelProperty(value = " LLM 返回文本块内容")
    private String answer;
    @ApiModelProperty(value = " 创建时间戳")
    private int created_at;

    @ApiModelProperty(value = " agent_thought ID，每一轮Agent迭代都会有一个唯一的id ")
    private String id;

    @ApiModelProperty(value = "agent_thought在消息中的位置，如第一轮迭代position为1")
    private Integer position;

    @ApiModelProperty(value = "agent的思考内容")
    private String thought;
    @ApiModelProperty(value = " 工具调用的返回结果")
    private String observation;
    @ApiModelProperty(value = "使用的工具列表，以 ; 分割多个工具")
    private String tool;
    @ApiModelProperty(value = "工具的输入，JSON格式的字符串(object)。")
    private String tool_input;

    private List<String> message_files;

    // 文件事件的属性

    @ApiModelProperty(value = "文件类型，目前仅为image")
    private String type;
    @ApiModelProperty(value = "文件归属，user或assistant，该接口返回仅为 assistant")
    private String belongs_to;
    @ApiModelProperty(value = "文件访问地址")
    private String url;


    // 异常事件的属性
    @ApiModelProperty(value = "HTTP 状态码")
    private Integer status;
    @ApiModelProperty(value = "错误码")
    private String code;
    @ApiModelProperty(value = "错误消息")
    private String message;
}
