package com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res;

import java.util.List;

import lombok.Data;

@Data
public class HitTestingSegment {
    private String id;
    private Integer position;
    private String documentId;
    private String content;
    private String answer;
    private Integer wordCount;
    private Integer tokens;
    private List<String> keywords;
    private String indexNodeId;
    private String indexNodeHash;
    private Integer hitCount;
    private Boolean enabled;
    private String disabledAt;
    private String disabledBy;
    private String status;
    private String createdBy;
    private Long createdAt;
    private Long indexingAt;
    private Long completedAt;
    private String error;
    private String stoppedAt;
    private HitTestingDocument document;
}
