package com.yirendai.voiceaiserver.tripartite.zhiyu.ws.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 实时语音转文字
 */
@Data
@ToString
public class AudioTranslationsReq {

    /**
     * 模式，默认为 "online"。
     * 必填字段。
     */
    @JsonProperty("mode")
    private String mode;

    /**
     * 模型名称，默认为 "funasr-online"。
     * 可选字段。
     */
    @JsonProperty("model_name")
    private String modelName;

    /**
     * 编码器、解码器和编码器的块大小，默认为 "5,10,5"。
     * 必填字段。
     */
    @JsonProperty("chunk_size")
    private String chunkSize;

    /**
     * 编码器块回溯，默认为 4。
     * 必填字段。
     */
    @JsonProperty("encoder_chunk_look_back")
    private int encoderChunkLookBack =4;

    /**
     * 解码器块回溯，默认为 1。
     * 必填字段。
     */
    @JsonProperty("decoder_chunk_look_back")
    private int decoderChunkLookBack =1;

    /**
     * 块间隔，默认为 10。
     * 必填字段。
     */
    @JsonProperty("chunk_interval")
    private int chunkInterval =10;

    /**
     * 文件名。
     * 必填字段。
     */
    @JsonProperty("wav_name")
    private String wavName;

    /**
     * 是否正在说话，默认为 true。
     * 必填字段。
     */
    @JsonProperty("is_speaking")
    private Boolean isSpeaking = true;

    /**
     * 热词，默认为空字符串。
     * 可选字段。
     */
    @JsonProperty("hotwords")
    private String hotwords;

    /**
     * 是否需要标点，默认为 null。
     * 可选字段。
     */
    @JsonProperty("itn")
    private Boolean itn;

    /**
     * 采样率，默认为 null。
     * 可选字段。
     */
    @JsonProperty("audio_fs")
    private Integer audioFs;

    /**
     * 文件格式，默认为 null。
     * 可选字段。
     */
    @JsonProperty("wav_format")
    private String wavFormat;
}
