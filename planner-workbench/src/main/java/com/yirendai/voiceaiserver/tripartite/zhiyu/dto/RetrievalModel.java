package com.yirendai.voiceaiserver.tripartite.zhiyu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RetrievalModel {
    @JsonProperty("search_method")
    private String searchMethod;
    @JsonProperty("reranking_enable")
    private Boolean rerankingEnable;
    @JsonProperty("reranking_mode")
    private RerankingModel rerankingModel;
    @JsonProperty("weights")
    private Double weights;
    @JsonProperty("top_k")
    private Integer topK;
    @JsonProperty("score_threshold_enabled")
    private Boolean scoreThresholdEnabled;
    @JsonProperty("score_threshold")
    private Double scoreThreshold;
}
