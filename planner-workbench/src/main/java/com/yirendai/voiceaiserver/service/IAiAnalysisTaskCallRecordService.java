package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.vo.request.CallRecordAnalysisReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskCallRecordPageVO;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskCallRecordVO;
import com.yirendai.workbench.entity.AiAnalysisTaskCallRecord;
import com.yirendai.workbench.vo.req.analysis.AiAnalysisTaskCallRecordReq;
import com.yirendai.workbench.wrapper.dto.CallCenterTask;
import org.springblade.core.tool.api.R;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <p>
 * ai分析任务通话记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface IAiAnalysisTaskCallRecordService extends IService<AiAnalysisTaskCallRecord> {

    /**
     * 分页查询（含统计信息）
     * @param page
     * @param req
     * @return
     */
    AiAnalysisTaskCallRecordPageVO pageList(IPage<AiAnalysisTaskCallRecordVO> page, AiAnalysisTaskCallRecordReq req);

    /**
     * 呼叫记录发起AI分析
     * @param request
     * @return
     */
    void analysis(CallRecordAnalysisReq request,Long userId,String userName,String tenantId);

    /**
     * 批量导出
     * @param req
     * @return
     */
    CallCenterTask exportBatch(AiAnalysisTaskCallRecordReq req);

    /**
     * 批量导入分析
     * @param file
     * @param request
     * @param userId
     * @param userName
     * @param tenantId
     */
    R<String> analysis(MultipartFile file, CallRecordAnalysisReq request, Long userId, String userName, String tenantId) throws IOException;
}
