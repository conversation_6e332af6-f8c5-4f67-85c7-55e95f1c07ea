package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.AiConversationFlowResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI对话流程结果表(按节点维度) 服务类
 */
public interface IAiConversationFlowResultService extends IService<AiConversationFlowResult> {

    /**
     * 根据来源类型和来源ID查询结果
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @param tenantId 租户ID
     * @return 结果列表
     */
    List<AiConversationFlowResult> getResultsBySource(String sourceType, Long sourceId, String tenantId);

    /**
     * 批量保存结果
     * @param results 结果列表
     */
    void batchSave(List<AiConversationFlowResult> results);

    /**
     * 批量保存结果（带去重）
     * 先删除现有的相同来源、流程类型、意图的结果，再插入新结果
     * @param results 结果列表
     */
    void batchSaveWithDeduplication(List<AiConversationFlowResult> results);

    /**
     * 删除指定条件的结果
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @param tenantId 租户ID
     * @param flowType 流程类型
     */
    void deleteResults(String sourceType, Long sourceId, String tenantId, String flowType);

    /**
     * 清理特定意图的所有节点结果（当主意图为NONE时调用）
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @param tenantId 租户ID
     * @param flowType 流程类型
     */
    void clearNodeResultsWhenNoIntent(String sourceType, Long sourceId, String tenantId, String flowType);

    /**
     * 查询指定用户、节点在起始时间之后是否已有触达记录
     *
     * @param userId    用户ID
     * @param nodeName  节点名称
     * @param startTime 起始时间
     * @return 首条触达记录，若不存在返回 null
     */
    AiConversationFlowResult getReachedResult(String userId, String nodeName, java.time.LocalDateTime startTime);

    AiConversationFlowResult getMainIntent(String customerId, String expectedValue, LocalDateTime startTime);
}
