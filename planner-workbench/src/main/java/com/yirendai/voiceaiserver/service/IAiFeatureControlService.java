/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.enums.AiFeatureControlEnum;
import com.yirendai.workbench.entity.AiFeatureControl;

/**
 * AI功能控制表 服务类
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface IAiFeatureControlService extends IService<AiFeatureControl> {

    /**
     * 大后台通过租户ID查询AI权限
     * @param tenantID
     * @return
     */
    List<AiFeatureControl> listByTenantId(String tenantID);

    /**
     * 根据ID修改状态
     * @param id
     * @param status
     * @return
     */
    boolean updateStatusById(Long id, Integer status,Long userId);
    /**
     * 根据 AiFeatureControlEnum 和 tenantId 查询一条 AiFeatureControl 记录
     *
     * @param featureEnum 功能枚举
     * @param tenantId 租户ID
     * @return 查询到的 AiFeatureControl 记录，如果没有找到则返回 null
     */

    public AiFeatureControl getByEnumAndTenantId(AiFeatureControlEnum featureEnum, String tenantId) ;

    /**
     * 根据 AiFeatureControlEnum 和 tenantId 查询一条 AiFeatureControl 记录，并判断其状态
     *
     * @param featureEnum 功能枚举
     * @param tenantId 租户ID
     * @return 如果记录存在且状态为启用，则返回 true；否则返回 false
     */
    public boolean isFeatureEnabled(AiFeatureControlEnum featureEnum, String tenantId);

    /**
     * 批量修改状态
     * @param list
     * @param userId
     * @return
     */
    boolean batchUpdateStatus(List<AiFeatureControl> list, Long userId);
}
