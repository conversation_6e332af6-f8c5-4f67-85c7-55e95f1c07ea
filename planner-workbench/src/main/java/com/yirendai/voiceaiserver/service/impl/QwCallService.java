package com.yirendai.voiceaiserver.service.impl;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.voiceaiserver.vo.response.AudioToTextVo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @time 2024/6/17 17:16
 **/
@Service
@Slf4j
public class QwCallService {

    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;

    @Resource
    AiVoiceToTextService aiVoiceToTextService;

    @Resource
    RestTemplate restTemplateProxy;

    public void splitQwCall() {
        Long id = 0L;
        int limit = 200;
        while (true) {
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.MEETING_VOICE_CALL.getType());
            wrapper.gt(AiPlannerChatContact::getId, id);
            wrapper.isNull(AiPlannerChatContact::getProcessedContent);
            wrapper.orderByAsc(AiPlannerChatContact::getId);
            wrapper.last(" limit " + limit);
            List<AiPlannerChatContact> aiPlannerChatContactList = iAiPlannerChatContactService.list(wrapper);
            if (CollectionUtils.isEmpty(aiPlannerChatContactList)) {
                return;
            }
            for (AiPlannerChatContact aiPlannerChatContact : aiPlannerChatContactList) {
                String fileName = RandomUtil.randomString(32) + ".amr";
                File file = null;
                try {
                    ResponseEntity<byte[]> responseEntity = restTemplateProxy.getForEntity(
                            aiPlannerChatContact.getOriginContent(), byte[].class);
                    file = FileUtil.byteToFile(responseEntity.getBody(), fileName);
                    aiPlannerChatContact.setProcessedContent(getContent(file));
                    iAiPlannerChatContactService.saveOrUpdate(aiPlannerChatContact);
                } catch (Exception e) {
                    log.error("splitQwCall id {}, error", aiPlannerChatContact.getId(), e);
                } finally {
                    if (Objects.nonNull(file) && file.exists()) {
                        file.delete();
                    }
                }
            }
            id = aiPlannerChatContactList.get(aiPlannerChatContactList.size() - 1).getId();
        }
    }

    private String getContent(File file) {
        List<File> leftFileList = new ArrayList<>();
        List<File> rightFileList = new ArrayList<>();
        List<File> fileList = null;
        StringBuilder sb = new StringBuilder();
        File wavFile = new File(file.getAbsolutePath() + ".wav");
        try {
            FileUtil.convertAmrToWav(file, wavFile);
            fileList = FileUtil.splitAudioFile(wavFile, 60, 0);
            fileList.forEach(f -> {
                String leftChannelOutputFile = RandomUtil.randomString(32) + ".wav";
                String rightChannelOutputFile = RandomUtil.randomString(32) + ".wav";
                FileUtil.splitAudioChannel(f.getAbsolutePath(), leftChannelOutputFile, rightChannelOutputFile);
                leftFileList.add(new File(leftChannelOutputFile));
                rightFileList.add(new File(rightChannelOutputFile));
                List<AudioToTextVo.Segment> allSegmentList = new ArrayList<>();
                allSegmentList.addAll(getSegment(leftChannelOutputFile, "客户"));
                allSegmentList.addAll(getSegment(rightChannelOutputFile, "理财师"));
                allSegmentList.sort(Comparator.comparingInt(AudioToTextVo.Segment::getStart));
                sb.append(segmentListToContent(allSegmentList));
            });
        } catch (Exception e) {
            log.error("getContent error", e);
        } finally {
            if (wavFile.exists()) {
                wavFile.delete();
            }
            if (CollectionUtil.isNotEmpty(fileList)) {
                fileList.forEach(File::delete);
            }
            if (CollectionUtil.isNotEmpty(leftFileList)) {
                leftFileList.forEach(File::delete);
            }
            if (CollectionUtil.isNotEmpty(rightFileList)) {
                rightFileList.forEach(File::delete);
            }
        }
        if (sb.length() > 0) {
            return sb.toString();
        }
        return "[语音电话]";
    }

    private String segmentListToContent(List<AudioToTextVo.Segment> allSegmentList) {
        return allSegmentList.stream().map(s -> s.getSpeaker() + ":" + s.getText()).collect(Collectors.joining("\n"));
    }

    private List<AudioToTextVo.Segment> getSegment(String path, String speaker) {
        JSONObject textJson = aiVoiceToTextService.voiceToTextJson(new File(path), "ce-asr", null, null, null, null);
        if (Objects.isNull(textJson)) {
            return Collections.emptyList();
        }
        AudioToTextVo audioToTextVo = JSON.parseObject(String.valueOf(textJson), AudioToTextVo.class);
        if (CollUtil.isEmpty(audioToTextVo.getSegments())) {
            return Collections.emptyList();
        }
        return audioToTextVo.getSegments().stream().peek(s -> s.setSpeaker(speaker)).collect(Collectors.toList());
    }

    public List<AudioToTextVo.Segment> getSegmentWithConfig(File file, String speaker, AiPromptRecordVo config) {
        JSONObject textJson = aiVoiceToTextService.voiceToTextJson(file, config.getModel(), null, config.getHotWords(), config.getPrompt(), config.getTemperature());
        if (Objects.isNull(textJson)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "音转文发生异常");
        }
        AudioToTextVo audioToTextVo = JSON.parseObject(String.valueOf(textJson), AudioToTextVo.class);
        if (CollUtil.isEmpty(audioToTextVo.getSegments())) {
            return Collections.emptyList();
        }
        return audioToTextVo.getSegments().stream().peek(s -> s.setSpeaker(speaker)).collect(Collectors.toList());
    }
}
