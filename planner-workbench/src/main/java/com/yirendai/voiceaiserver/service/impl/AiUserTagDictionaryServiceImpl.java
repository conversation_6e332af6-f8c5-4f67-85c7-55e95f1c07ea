/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.yirendai.voiceaiserver.service.IAiUserTagDictionaryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import com.yirendai.workbench.entity.AiUserTagDictionary;
import com.yirendai.workbench.mapper.AiUserTagDictionaryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * ai用户标签字典 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Service
public class AiUserTagDictionaryServiceImpl extends ServiceImpl<AiUserTagDictionaryMapper, AiUserTagDictionary> implements IAiUserTagDictionaryService {

    @Autowired
    private AiUserTagDictionaryMapper aiUserTagDictionaryMapper;

    @Autowired
    private Cache<String, List<AiTagCategoryWithTags>> aiTagCategoryCache;

    // 标签和分类的全查询结果缓存key
    public static final String AI_TAG_CATEGORY_CACHE_KEY_PREFIX = "aiTagCategoryCache:";

    @Override
    public List<AiTagCategoryWithTags> findAllCategoriesWithTags(String tenantId) {
        String cacheKey = AI_TAG_CATEGORY_CACHE_KEY_PREFIX + tenantId;
        List<AiTagCategoryWithTags> cachedData = aiTagCategoryCache.getIfPresent(cacheKey);
        if (cachedData != null) {
            return cachedData;
        }
        // 查询所有标签分类及标签
        List<AiTagCategoryWithTags> allCategoriesWithTags = aiUserTagDictionaryMapper.findAllCategoriesWithTags(tenantId);
        // 缓存查询结果
        aiTagCategoryCache.put(cacheKey, allCategoriesWithTags);
        return allCategoriesWithTags;
    }

    @Override
    public AiUserTagDictionary selectByTagNameAndCategoryId(String name, Long categoryId, String tenantId) {
        LambdaQueryWrapper<AiUserTagDictionary> tagQueryWrapper = new LambdaQueryWrapper<>();
        tagQueryWrapper.eq(AiUserTagDictionary::getUserTagCategoryId, categoryId)
                .eq(AiUserTagDictionary::getTagName, name)
                .eq(AiUserTagDictionary::getTenantId, tenantId);
        AiUserTagDictionary aiUserTagDictionary = aiUserTagDictionaryMapper.selectOne(tagQueryWrapper);
        if (aiUserTagDictionary == null) {
            return null;
        }
        return aiUserTagDictionary;
    }

    @Override
    public AiUserTagDictionary selectByTagName(String tagName, String tenantId) {
        LambdaQueryWrapper<AiUserTagDictionary> tagQueryWrapper = new LambdaQueryWrapper<>();
        tagQueryWrapper.eq(AiUserTagDictionary::getTagName, tagName)
                .eq(AiUserTagDictionary::getTenantId, tenantId);
        List<AiUserTagDictionary> aiUserTagDictionaryList = aiUserTagDictionaryMapper.selectList(tagQueryWrapper);
        if (aiUserTagDictionaryList == null || aiUserTagDictionaryList.isEmpty()) {
            return null;
        } else {
            return aiUserTagDictionaryList.get(0);
        }
    }
}
