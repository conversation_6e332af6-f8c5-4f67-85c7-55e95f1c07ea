package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneResultVO;
import com.yirendai.workbench.entity.AiAnalysisConfigSceneResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * ai分析场景分析结果选项配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface IAiAnalysisConfigSceneResultService extends IService<AiAnalysisConfigSceneResult> {

    /**
     * 根据场景ID查询结果选项
     * @param id
     * @return
     */
    List<AiAnalysisConfigSceneResultVO> listBySceneId(Long id);

    /**
     * 根据结果选项名称查询数量
     * @param resultName
     * @param sceneId
     * @return
     */
    int selectCountBySceneResultName(String resultName, Long sceneId,Long excludeId);
}
