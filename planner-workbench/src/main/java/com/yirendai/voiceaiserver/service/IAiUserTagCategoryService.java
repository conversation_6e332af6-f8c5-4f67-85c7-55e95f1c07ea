/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.service;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.vo.response.AiUserTagCategoryVO;
import com.yirendai.workbench.entity.AiUserTagCategory;
import org.springframework.transaction.annotation.Transactional;

/**
 * ai用户标签分类 服务类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface IAiUserTagCategoryService extends IService<AiUserTagCategory> {

    /**
     * 标签分类列表（递归）
     * @return
     */
    List<AiUserTagCategoryVO> categoryVOList(String categoryName,Integer userType );

    /**
     * 以JSON格式批量插入ai用户标签和分类
     */
    @Transactional
    void insertTagsAndCategoriesByJSON(String jsonData);

    /**
     * 插入标签和分类
     */
    @Transactional
    Boolean insertTagsAndCategory(Long parentId, String categoryName, Integer multiSelect, String matchDes, List<String> tags, String tenantId);

    AiUserTagCategory selectByCategoryName(String category, String tenantId);

    AiUserTagCategory selectById(Long topParentId);

    AiUserTagCategory getLastTopTagCategory();

    Boolean parseExcelFile(InputStream inputStream, String tenantId) throws IOException;
}
