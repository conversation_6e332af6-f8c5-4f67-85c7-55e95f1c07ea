package com.yirendai.voiceaiserver.service.impl;
import com.yirendai.workbench.mapper.AdsChangxianOrderDetailDfMapper;
import com.yirendai.workbench.entity.AdsChangxianOrderDetailDf;
import com.yirendai.voiceaiserver.service.AdsChangxianOrderDetailDfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 业绩达成-保险相关-长险订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
public class AdsChangxianOrderDetailDfServiceImpl extends ServiceImpl<AdsChangxianOrderDetailDfMapper, AdsChangxianOrderDetailDf> implements AdsChangxianOrderDetailDfService {

    @Override
    public List<AdsChangxianOrderDetailDf> getPlannerAchievements() {
        return baseMapper.getPlannerAchievements();
    }

    @Override
    public List<AdsChangxianOrderDetailDf> getPlannerUsers() {
        return baseMapper.getPlannerUsers();
    }
}
