package com.yirendai.voiceaiserver.service;

import java.util.List;
import java.util.Map;

public interface EmailSender {

    void sendEmail(String subject, String toAddress, String ccAddress, String content);

    void sendEmail(String subject, String fromAddress, String toAddress, String ccAddress, String content);

    /**
     * 发送带附件的邮件
     *
     * @param from        发件人
     * @param to          收件人
     * @param ccAddress   抄送人
     * @param subject     主题
     * @param content     内容
     * @param attachments 附件文件路径列表
     * @return 发送结果
     */
    boolean sendMailWithAttachments(String from, String to, String ccAddress, String subject,
                                    String content, List<String> attachments);

    /**
     * 发送带内嵌图片的HTML邮件
     *
     * @param from        发件人
     * @param to          收件人
     * @param ccAddress   抄送人
     * @param subject     主题
     * @param content     HTML内容
     * @param images      图片路径Map，key为contentId，value为图片路径
     * @param attachments 附件路径列表
     * @return 发送结果
     */
    boolean sendMailWithInlineImages(String from, String to, String ccAddress, String subject,
                                     String content, Map<String, String> images, List<String> attachments);
}
