package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.service.AiConversationInspectDetailService;
import com.yirendai.workbench.entity.AiConversationInspectDetail;
import com.yirendai.workbench.mapper.AiConversationInspectDetailMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_inspect_detail(ai对话内容稽查详情表)】的数据库操作Service实现
* @createDate 2024-11-18 16:00:34
*/
@Service
public class AiConversationInspectDetailServiceImpl extends ServiceImpl<AiConversationInspectDetailMapper, AiConversationInspectDetail>
implements AiConversationInspectDetailService {

}
