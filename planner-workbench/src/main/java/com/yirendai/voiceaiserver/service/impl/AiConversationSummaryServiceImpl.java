package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.service.CallCenterService;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationSummaryVO;
import com.yirendai.voiceaiserver.vo.response.ChatTranslateStatusVO;
import com.yirendai.workbench.entity.AiConversationProcessTask;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.mapper.AiConversationSummaryMapper;
import com.yirendai.workbench.entity.AiConversationSummary;
import com.yirendai.voiceaiserver.service.AiConversationSummaryService;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.util.OwnAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * ai对话内容小结表(AiConversationSummary)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-28 14:59:38
 */
@Slf4j
@Service
public class AiConversationSummaryServiceImpl extends ServiceImpl<AiConversationSummaryMapper, AiConversationSummary> implements AiConversationSummaryService {

    @Resource
    private AiConversationSummaryMapper aiConversationSummaryMapper;
    @Resource
    private AiConversationProcessTaskService aiConversationProcessTaskService;
    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;
    @Resource
    private CallcenterCallRecordService callcenterCallRecordService;
    @Resource
    private JedisCluster jedisCluster;
    @Resource
    private CallCenterService callCenterService;

    @Override
    public AiConversationSummaryVO selectByChatContactId(String uuid, Integer systemType) {
        String redisKey;
        String systemTypeStr = null;
        if (systemType == 1) {
            systemTypeStr = QwMsgTypeEnum.AVAYA.getType();
            redisKey = QwMsgTypeEnum.AVAYA.getType() + ":" + uuid;
        } else if (systemType == 2) {
            systemTypeStr = QwMsgTypeEnum.CALL_CENTER.getType();
            redisKey = QwMsgTypeEnum.CALL_CENTER.getType() + ":" + uuid;
        } else if (systemType == 3) {
            systemTypeStr = QwMsgTypeEnum.HOUYU.getType();
            redisKey = QwMsgTypeEnum.HOUYU.getType() + ":" + uuid;
        } else {
            log.info("对话小结查询类型未知，systemType:{}", systemType);
            redisKey = "callUuid:" + uuid;
        }
        if (jedisCluster.exists(redisKey)) {
            if ("1".equals(jedisCluster.get(redisKey))) {
                log.info("缓存-对话小结任务未完成，uuid:{}", uuid);
                throw new AiServerException(ResultCode.CONVERSATION_SUMMARY_HANDING);
            }
            log.info("缓存-对话音转文处理中，uuid:{}", uuid);
            throw new AiServerException(ResultCode.CONVERSATION_ASR_WAITING);
        }
        CallcenterCallRecord callcenterCallRecord = callcenterCallRecordService.selectOneByUuidAndSystemType(uuid, systemType);
        if (callcenterCallRecord == null || callcenterCallRecord.getAnswered() != 2) {
            log.info("无效通话，uuid:{}, systemType:{}", uuid, systemType);
            throw new AiServerException(ResultCode.INVALID_CALL);
        }
        ChatTranslateStatusVO translateStatus;
        String busId = uuid;
        if (systemType == 1) {
            // avaya通话
            String recordUrl = callcenterCallRecord.getRecordUrl();
            if (Strings.isNullOrEmpty(recordUrl)) {
                log.info("通话未结束，recordUrl未找到，uuid:{}", uuid);
                throw new AiServerException(ResultCode.CONVERSATION_ASR_WAITING);
            }
            busId = extractFileName(recordUrl);
            if (Strings.isNullOrEmpty(busId)) {
                log.info("avaya唯一busId提取失败，uuid:{}", uuid);
                throw new AiServerException(ResultCode.INVALID_CALL);
            }
        }
        log.info("对话小结查询，uuid:{}, systemType:{}, busId:{}, systemTypeStr:{}", uuid, systemType, busId, systemTypeStr);
        try {
            translateStatus = callCenterService.getTranslateStatus(busId, systemTypeStr);
        } catch (Exception e) {
            log.error("对话小结查询异常,通话未结束，uuid:{}, systemType:{};", uuid, systemType, e);
            jedisCluster.setex(redisKey, 30, "0");
            throw new AiServerException(ResultCode.CONVERSATION_ASR_WAITING);
        }
        // 处理状态 0:处理中 1:成功 2:失败
        Integer status = translateStatus.getStatus();
        Long chatContactId = translateStatus.getId();
        if (status == 2) {
            log.info("对话小结音转文失败，uuid:{}", uuid);
            throw new AiServerException(ResultCode.INVALID_CALL);
        } else if (status == 0) {
            log.info("音转文任务未完成，uuid:{}", uuid);
            jedisCluster.setex(redisKey, 30, "0");
            throw new AiServerException(ResultCode.CONVERSATION_ASR_WAITING);
        }

        String taskId = aiConversationProcessTaskService.buildTaskId(chatContactId, AiConversationProcessTypeEnum.CONTENT_SUMMARY);
        LambdaQueryWrapper<AiConversationProcessTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationProcessTask::getTaskId, taskId);
        AiConversationProcessTask aiConversationProcessTask = aiConversationProcessTaskService.getOne(queryWrapper);
        if (aiConversationProcessTask == null) {
            log.info("对话小结任务未写入，taskId:{}", taskId);
            jedisCluster.setex(redisKey, 30, "0");
            throw new AiServerException(ResultCode.CONVERSATION_ASR_WAITING);
        }
        Integer taskStatus = aiConversationProcessTask.getTaskStatus();
        if (AiConversationProcessStatusEnum.NOT_START.getCode().equals(taskStatus) || AiConversationProcessStatusEnum.PROCESSING.getCode().equals(taskStatus)) {
            log.info("对话小结任务未完成，taskId:{}, 状态,{}", taskId, taskStatus);
            jedisCluster.setex(redisKey, 30, "1");
            throw new AiServerException(ResultCode.CONVERSATION_SUMMARY_HANDING);
        } else if (AiConversationProcessStatusEnum.FAILED.getCode().equals(taskStatus) || AiConversationProcessStatusEnum.CANCELLED.getCode().equals(taskStatus)) {
            log.info("对话小结任务失败，taskId:{}, 状态,{}", taskId, taskStatus);
            throw new AiServerException(ResultCode.CONVERSATION_SUMMARY_FAIL);
        }
        LambdaQueryWrapper<AiConversationSummary> summaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        summaryLambdaQueryWrapper.eq(AiConversationSummary::getChatContactId, chatContactId);
        // 0是插入之后未处理的，1是处理完成的
        AiConversationSummary aiConversationSummary = aiConversationSummaryMapper.selectOne(summaryLambdaQueryWrapper);
        if (aiConversationSummary == null || aiConversationSummary.getStatus() != 1) {
            log.info("无效通话，不产生对话小结，chatContactId:{}", chatContactId);
            throw new AiServerException(ResultCode.INVALID_CALL);
        }
        log.info("对话小结查询成功，chatContactId:{}", chatContactId);
        return buildAiConversationSummaryVO(aiConversationSummary);
    }

    @Override
    public Boolean feedback(AiFeedbackReq req) {
        LambdaQueryWrapper<AiConversationSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationSummary::getId, req.getId());
        AiConversationSummary aiConversationSummary = aiConversationSummaryMapper.selectOne(queryWrapper);
        if (aiConversationSummary == null) {
            log.error("对话小结不存在，id:{}", req.getId());
            throw new AiServerException(ResultCode.NO_CONVERSATION_SUMMARY);
        }
        aiConversationSummary.setFeedbackStatus(req.getFeedbackStatus());
        aiConversationSummary.setFeedbackDetails(req.getFeedbackDetails());
        BladeUser user = OwnAuthUtil.getUser();
        if (user == null) {
            log.error("feedback failed, user not found");
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        aiConversationSummary.setFeedbackUserId(String.valueOf(user.getUserId()));
        aiConversationSummary.setFeedbackUserName(user.getUserName());
        aiConversationSummary.setFeedbackTime(LocalDateTime.now());
        aiConversationSummary.setUpdateTime(LocalDateTime.now());
        aiConversationSummaryMapper.updateById(aiConversationSummary);
        log.info("对话小结反馈成功，id:{}", req.getId());
        return true;
    }

    /**
     * 查询用户最近一条对话小结
     *
     * @param userId 用户ID
     * @return 对话小结
     */
    @Override
    public AiConversationSummaryVO selectByUserId(String userId) {
        //
        CallcenterCallRecord latestCallRecord = callcenterCallRecordService.getLatestCallRecord(userId, 2);
        if (latestCallRecord == null) {
            return null;
        }
        String uuid = latestCallRecord.getUuid();
        Integer systemType = latestCallRecord.getSystemType();
        log.info("用户对话小结查询，userId:{}, uuid:{}, systemType:{}", userId, uuid, systemType);
        String busId = uuid;
        String systemTypeStr = null;
        if (systemType == 1) {
            String recordUrl = latestCallRecord.getRecordUrl();
            if (Strings.isNullOrEmpty(recordUrl)) {
                log.info("用户对话小结查询,recordUrl为null，uuid:{}", uuid);
                return null;
            }
            busId = extractFileName(recordUrl);
            if (Strings.isNullOrEmpty(busId)) {
                log.info("用户对话小结查询 avaya唯一busId提取失败，uuid:{}", uuid);
                return null;
            }
            systemTypeStr = QwMsgTypeEnum.AVAYA.getType();
        } else if (systemType == 2) {
            systemTypeStr = QwMsgTypeEnum.CALL_CENTER.getType();
        } else if (systemType == 3) {
            systemTypeStr = QwMsgTypeEnum.HOUYU.getType();
        } else {
            log.info("用户对话小结查询,systemType为null，uuid:{}", uuid);
        }
        log.info("用户对话小结查询,busId:{}, systemType:{}", busId, systemTypeStr);
        AiPlannerChatContact aiPlannerChatContact = getAiPlannerChatContact(busId, systemTypeStr);
        if (aiPlannerChatContact == null) {
            log.info("用户对话小结查询 对话小结音转文未处理完!，uuid:{}, systemType:{}", uuid, systemType);
            return null;
        }
        LambdaQueryWrapper<AiConversationSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationSummary::getChatContactId, aiPlannerChatContact.getId());
        AiConversationSummary aiConversationSummary = aiConversationSummaryMapper.selectOne(queryWrapper);
        if (aiConversationSummary == null
                || aiConversationSummary.getStatus() == 0
                || aiConversationSummary.getFeedbackStatus() == 2
                || aiConversationSummary.getUserContentValid() == 0) {
            log.info("用户对话小结查询 对话小结无效或不需要展示!，uuid:{}, systemType:{}", uuid, systemType);
            return null;
        }
        return buildAiConversationSummaryVO(aiConversationSummary);
    }

    private AiConversationSummaryVO buildAiConversationSummaryVO(AiConversationSummary aiConversationSummary) {
        if (aiConversationSummary == null) {
            return null;
        }
        if (isValidSummaryContent(aiConversationSummary.getSummaryContentUser())) {
            aiConversationSummary.setUserContentValid(1);
        } else {
            aiConversationSummary.setUserContentValid(0);
        }
        return AiConversationSummaryVO.builder()
                .id(aiConversationSummary.getId())
                .chatContactId(aiConversationSummary.getChatContactId())
                .plannerNo(aiConversationSummary.getPlannerNo())
                .userId(aiConversationSummary.getUserId())
                .summaryContentPlanner(isValidSummaryContent(aiConversationSummary.getSummaryContentPlanner()) ? aiConversationSummary.getSummaryContentPlanner() : "本次通话理财师未做有效沟通")
                .summaryContentUser(aiConversationSummary.getUserContentValid() == 0 ? "本次通话客户未做有效反馈" : aiConversationSummary.getSummaryContentUser())
                .confidenceScore(aiConversationSummary.getConfidenceScore())
                .userContentValid(aiConversationSummary.getUserContentValid())
                .feedbackStatus(aiConversationSummary.getFeedbackStatus())
                .intentionProducts(aiConversationSummary.getIntentionProducts())
                .build();
    }

    /**
     * 判断字符串是否符合指定条件.
     *
     * @param summaryContent 待检查的字符串
     * @return 如果字符串不符合任何一个条件则返回true，否则返回false
     */
    private boolean isValidSummaryContent(String summaryContent) {
        if (summaryContent == null || summaryContent.isEmpty()) {
            return false;
        }

        // 条件 1: 是否为 "-1"
        if ("-1".equals(summaryContent)) {
            return false;
        }

        // 条件 2: 是否为纯数字字符串
        if (summaryContent.matches("\\d+")) {
            return false;
        }

        // 条件 3: 是否不包含中文字符（使用Unicode范围判断）
        if (!summaryContent.matches(".*[\\u4E00-\\u9FA5].*")) {
            return false;
        }

        return true;
    }

    private AiPlannerChatContact getAiPlannerChatContact(String busId, String systemType) {
        LambdaQueryWrapper<AiPlannerChatContact> contactQueryWrapper = new LambdaQueryWrapper<>();
        if (Strings.isNullOrEmpty(systemType)) {
            log.info("对话小结任务查询类型未知，systemType:{}，默认只用单条件查询", systemType);
            contactQueryWrapper.eq(AiPlannerChatContact::getBusId, busId);
        } else {
            contactQueryWrapper.eq(AiPlannerChatContact::getOriginType, systemType);
            contactQueryWrapper.eq(AiPlannerChatContact::getBusId, busId);
        }
        AiPlannerChatContact aiPlannerChatContact;
        try {
            aiPlannerChatContact = aiPlannerChatContactMapper.selectOne(contactQueryWrapper);
        } catch (Exception e) {
            log.error("对话小结任务查询异常，uuid:{}, systemType:{};", busId, systemType, e);
            throw new AiServerException(ResultCode.DB_DATA_ERROR);
        }
        return aiPlannerChatContact;
    }

    private boolean beforeYesterday(LocalDateTime msgTime) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDate msgDate = msgTime.toLocalDate();
        return msgDate.equals(yesterday);
    }

    public static String extractFileName(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        String regex = ".*/([^/]+?)\\.wav$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }
}

