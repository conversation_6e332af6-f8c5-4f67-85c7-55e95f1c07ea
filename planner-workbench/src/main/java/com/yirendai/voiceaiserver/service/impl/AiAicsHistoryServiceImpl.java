package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.workbench.mapper.AiAicsHistoryMapper;
import com.yirendai.workbench.entity.AiAicsHistory;
import com.yirendai.voiceaiserver.service.IAiAicsHistoryService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 基金ai客服历史对话记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Service
public class AiAicsHistoryServiceImpl extends ServiceImpl<AiAicsHistoryMapper, AiAicsHistory> implements
        IAiAicsHistoryService {

    @Autowired
    AiAicsHistoryMapper aiAicsHistoryMapper;

    @Override
    public IPage<AiAicsHistory> listHistoryPage(String customerNo, String startTime, String endTime, int pageNum, int pageSize) {

        Page<AiAicsHistory> page = new Page<>(pageNum,pageSize);

        LambdaQueryWrapper<AiAicsHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotEmpty(customerNo), AiAicsHistory::getCustomerNo, customerNo)
                .ge(StrUtil.isNotEmpty(startTime), AiAicsHistory::getCreateTime, startTime)
                .le(StrUtil.isNotEmpty(endTime), AiAicsHistory::getCreateTime, endTime)
                .orderByDesc(AiAicsHistory::getCreateTime);

        return aiAicsHistoryMapper.selectPage(page, queryWrapper);
    }
}
