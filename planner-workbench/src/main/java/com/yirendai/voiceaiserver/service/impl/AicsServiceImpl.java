package com.yirendai.voiceaiserver.service.impl;

import javax.annotation.Resource;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.enums.ResponseModeEnum;
import com.yirendai.voiceaiserver.listener.AicsEventSourceListener;
import com.yirendai.workbench.entity.AiAicsLikeHistory;
import com.yirendai.voiceaiserver.service.AicsService;
import com.yirendai.voiceaiserver.service.IAiAicsLikeHistoryService;
import com.yirendai.voiceaiserver.util.HttpUtil;
import com.yirendai.voiceaiserver.vo.request.AiChatMessageReq;
import com.yirendai.voiceaiserver.vo.request.ChatLikeReq;
import com.yirendai.voiceaiserver.vo.request.ChatMessagesStreamReq;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @time 2023/11/13 17:14
 **/
@Service
@Slf4j
@RefreshScope
public class AicsServiceImpl implements AicsService {

    @Value("${chat.key.compliance:app-oGto0tfiKEcd1LAEUynOXEhr}")
    private String apiKey;

    @Value("${chat.url:http://aics-pro-api.aigc.paas.test/v1}")
    private String apiUrl;

    @Resource
    IAiAicsLikeHistoryService aicsLikeHistoryService;

    @Override
    public SseEmitter chatMessageStream(ChatMessagesStreamReq chatMessagesReq) {
        SseEmitter sseEmitter = new SseEmitter();
        AiChatMessageReq aiChatMessageReq = new AiChatMessageReq();
        aiChatMessageReq.setQuery(chatMessagesReq.getQuery());
        aiChatMessageReq.setResponse_mode(ResponseModeEnum.STREAMING.getValue());
        HttpUtil.stream(apiKey, apiUrl + "/chat-messages", JSONUtil.toJsonStr(aiChatMessageReq),
                new AicsEventSourceListener(sseEmitter, chatMessagesReq.getAccount(), chatMessagesReq.getQuery()));
        return sseEmitter;
    }

    @Override
    public R<Boolean> like(ChatLikeReq chatLikeReq) {
        AiAicsLikeHistory aiAicsLikeHistory = new AiAicsLikeHistory();
        aiAicsLikeHistory.setCustomerNo(chatLikeReq.getAccount());
        aiAicsLikeHistory.setQuestion(chatLikeReq.getQuestion());
        aiAicsLikeHistory.setAnswer(chatLikeReq.getAnswer());
        aiAicsLikeHistory.setStatus(chatLikeReq.getLikeStatus());
        aicsLikeHistoryService.save(aiAicsLikeHistory);
        return R.data(true);
    }
}
