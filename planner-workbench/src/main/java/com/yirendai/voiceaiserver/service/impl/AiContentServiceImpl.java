/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.service.impl;

import java.util.Date;
import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.constant.AiConstant;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.service.IAiContentService;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ZhiYuApiService;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.HitTestingRecords;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.HitTestingResponse;
import com.yirendai.voiceaiserver.tripartite.zhiyu.dto.res.HitTestingSegment;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.vo.response.ChatApiRes;
import com.yirendai.workbench.entity.AiContent;
import com.yirendai.workbench.mapper.AiContentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * AI内容 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Service
@Slf4j
public class AiContentServiceImpl extends ServiceImpl<AiContentMapper, AiContent> implements IAiContentService {

    @Autowired
    ZhiYuApiService zhiYuApiService;

    @Resource
    private ChatService chatService;


    @Resource
    protected AiPromptRecordService aiPromptRecordService;

    @Resource
    WebHookUtil webHookUtil;


    @Override
    @TenantIgnore
    public IPage<AiContent> getAiContentPage(Query query) {
        LambdaQueryWrapper<AiContent> wrapper =	new LambdaQueryWrapper<>();
        wrapper.eq(AiContent::getIsDeleted, 0);
        wrapper.eq(AiContent::getType, 1);
        wrapper.orderByDesc(AiContent::getReleaseTime);
        IPage<AiContent> pages = this.page(Condition.getPage(query), wrapper);
        if(pages!=null && CollectionUtils.isNotEmpty(pages.getRecords())){
            for(AiContent content:pages.getRecords()){
                if(StrUtil.isNotBlank(content.getOriginalContent())){
                    content.setOriginalContent(removeFirstLine(content.getOriginalContent()));
                }
            }
        }
        return pages;
    }
    /**
     * 同步新闻知识库
     */
    @Override
    public void syncContentNews(){
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.AI_NEWS.getCode());
        String query = null;
        String dateTime = DateUtil.format(new Date(),DateUtil.PATTERN_DATETIME);
        try{
            query = String.format(latestPromptRecordByScene.getPrompt(),dateTime);
        }catch (Exception ex){
            log.error("syncContentNews fail: {}",ex.getMessage());
            query = String.format(AiConstant.HIT_TESTING_NEWS_QUERY, DateUtil.format(new Date(),DateUtil.PATTERN_DATETIME));
        }
        this.syncContentNews(query);
    }
    /**
     * 同步新闻知识库
     */
    @Override
    public void syncContentNews(String query){
        R<HitTestingResponse> response = zhiYuApiService.hitTesting(query);
        if(response.isSuccess()){
            HitTestingResponse  hitTestingResponse = response.getData();
            if(hitTestingResponse!=null && CollectionUtil.isNotEmpty(hitTestingResponse.getRecords())){
                for( HitTestingRecords record:hitTestingResponse.getRecords()){
                    HitTestingSegment  segment  = record.getSegment();
                    if(segment == null){
                        log.info("syncContentNews fail: segment is null");
                        continue;
                    }
                    String segmentId = segment.getId();
                    int count = baseMapper.countBySegmentId(segmentId);
                    if(count > 0){
                        log.info("syncContentNews fail: segmentId:{} already exist",segmentId);
                        continue;
                    }
                    try {
                        AiContent content = getAiContent(segment);
                        if(content != null){
                            if(content.getTextContent()!= null && content.getTextContent().length() > 4000){
                                content.setTextContent(content.getTextContent().substring(0,4000));
                            }
                            baseMapper.insert(content);
                        }else{
                            log.info("syncContentNews fail: content is null");
                        }

                    }catch (Exception e){
                        log.error("syncContentNews error: {}",e.getMessage(),e);
                        webHookUtil.weChatMsgDev(MsgTypeEnum.SYNC_CONTENT_NEWS,"异常",e.getMessage(),e);
                    }


                }
            }
        }else{
            log.info("syncContentNews fail: {}",response.getMsg());
        }
    }


    private AiContent getAiContent(HitTestingSegment  segment){
        AiPromptRecordVo latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.AI_NEWS_FORMAT.getCode());
        String model = StringUtil.isBlank(latestPromptRecordByScene.getModel()) ? AiConstant.HIT_TESTING_NEWS_FORMAT_MODEL:latestPromptRecordByScene.getModel();
        Float temperature = latestPromptRecordByScene.getTemperature() == null ? AiConstant.HIT_TESTING_NEWS_FORMAT_TEMPERATURE:latestPromptRecordByScene.getTemperature();
        String question = null;
        try{
            question = String.format(latestPromptRecordByScene.getPrompt(),segment.getContent());
        }catch (Exception ex){
            log.error("getAiContent fail: {}",ex.getMessage());
            question = String.format(AiConstant.HIT_TESTING_NEWS_FORMAT_QUESTION,segment.getContent());
        }
        String response = chatService.chat(question, model, temperature);
        String aiContent = extractAiReplyContent(response);
        log.info("getAiContent aiContent = {}" ,aiContent);
        AiContent content = JsonUtil.parse(aiContent,AiContent.class);
        if(content == null ){
            return null;
        }
        content.setSegmentId(segment.getId());
        content.setOriginalContent(segment.getContent());
        content.setType(1);
        content.setTenantId(AiConstant.ADMIN_TENANT_ID);
        return content;
    }
    protected String extractAiReplyContent(String response) {
        if (StrUtil.isBlank(response)) {
            throw new AiServerException(ResultCode.AI_RESULT_ERROR);
        }

        ChatApiRes chatApiRes = JSON.parseObject(response, ChatApiRes.class);
        if (isInvalidChatApiResponse(chatApiRes)) {
            throw new AiServerException(ResultCode.AI_RESULT_ERROR);
        }

        return chatApiRes.getChoices().get(0).getMessage().getContent();
    }
    private boolean isInvalidChatApiResponse(ChatApiRes chatApiRes) {
        return chatApiRes == null ||
                CollectionUtils.isEmpty(chatApiRes.getChoices()) ||
                chatApiRes.getChoices().get(0).getMessage() == null ||
                StrUtil.isBlank(chatApiRes.getChoices().get(0).getMessage().getContent());
    }
    public   String removeFirstLine(String input) {
        // 使用换行符分割字符串
        String[] lines = input.split("\n", 2);

        // 检查是否至少有两部分（即至少有一行和其他内容）
        if (lines.length > 1) {
            return lines[1]; // 返回除去第一行后的剩余部分
        } else {
            return ""; // 如果只有一行，返回空字符串
        }
    }
}
