package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.workbench.mapper.AiQuestionBehaviorMapper;
import com.yirendai.workbench.entity.AiQuestionBehavior;
import com.yirendai.voiceaiserver.service.AiQuestionBehaviorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ai问题-用户行为 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
public class AiQuestionBehaviorServiceImpl extends ServiceImpl<AiQuestionBehaviorMapper, AiQuestionBehavior> implements AiQuestionBehaviorService {

}
