package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.BehaviorTypeEnum;
import com.yirendai.voiceaiserver.enums.ChatModeEnum;
import com.yirendai.voiceaiserver.enums.OpeningLevelEnum;
import com.yirendai.voiceaiserver.enums.OpeningTypeEnum;
import com.yirendai.workbench.entity.AiOpening;
import com.yirendai.workbench.entity.AiOpeningBehavior;
import com.yirendai.voiceaiserver.service.AiOpeningBehaviorService;
import com.yirendai.voiceaiserver.service.AiOpeningService;
import com.yirendai.voiceaiserver.vo.db.AiOpeningBehaviorVO;
import com.yirendai.voiceaiserver.vo.request.BasePageReq;
import com.yirendai.voiceaiserver.vo.request.ChatMessagesReq;
import com.yirendai.voiceaiserver.vo.request.OpeningBehaviorReq;
import com.yirendai.voiceaiserver.vo.request.OpeningSearchReq;
import com.yirendai.voiceaiserver.vo.response.AiOpeningVO;
import com.yirendai.voiceaiserver.vo.response.ChatMessagesBlockRes;
import com.yirendai.voiceaiserver.vo.response.OpeningSearchRes;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.mapper.AiOpeningMapper;
import lombok.extern.slf4j.Slf4j;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.text.TextContentRenderer;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ai开场白 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-24
 */
@Service
@Slf4j
@RefreshScope
@Transactional
public class AiOpeningServiceImpl extends ServiceImpl<AiOpeningMapper, AiOpening> implements AiOpeningService {

    /**
     * 随机开场白-推荐话题缓存key
     */
    private static final String KEY_RANDOM_OPENING_TOPIC_CACHE = "voice:ai:server:random:opening:topic:cache";
    /**
     * 随机开场白-推荐话题并发锁
     */
    private static final String KEY_RANDOM_OPENING_TOPIC_LOCK = "voice:ai:server:random:opening:topic:lock";

    /**
     * 开场白聊天信息
     */
    private static final String CHAT_OPENING_MSG =
            "请生成%s条以%s为相关话题的开场白话术，每条话术在称呼对方时要用姓氏+哥、姐、叔叔、阿姨等等称呼对方，" +
                    "整个话术要偏口语化一些，字数要在五百个字以上。";

    /**
     * 二十以内汉字
     */
    private static final String[] CHINESE_NUMBERS = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
            "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十"
    };

    @Value("${chat.key.opening}")
    private String key;
    @Value("${chat.url}")
    private String baseUrl;

    @Resource
    JedisCluster jedisCluster;
    @Resource
    RedissonClient redissonClient;
    @Resource
    AiOpeningBehaviorService aiOpeningBehaviorService;

    @Override
    public R<OpeningSearchRes> getRandomTopic(Integer size) {
        RLock lock = redissonClient.getLock(KEY_RANDOM_OPENING_TOPIC_LOCK);
        try {
            boolean lockFlag = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new AiServerException(ResultCode.HANDING);
            }
            String openingCache = jedisCluster.get(KEY_RANDOM_OPENING_TOPIC_CACHE);
            if (StringUtils.isNotBlank(openingCache)) {
                List<AiOpeningVO> openingList = JSON.parseArray(openingCache, AiOpeningVO.class);
                if (CollectionUtils.isNotEmpty(openingList) && openingList.size() >= size) {
                    // 缓存
                    List<AiOpeningVO> res = openingList.subList(0, size);
                    openingList = openingList.subList(size, openingList.size());
                    jedisCluster.setex(KEY_RANDOM_OPENING_TOPIC_CACHE, 300, JSON.toJSONString(openingList));
                    return R.data(OpeningSearchRes.builder().openingVOList(res).build());
                }
            }

            LambdaQueryWrapper<AiOpening> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AiOpening::getLevel, OpeningLevelEnum.TOPIC.getCode());
            List<AiOpening> list = this.list(wrapper);
            if (CollectionUtils.isEmpty(list)) {
                return R.data(OpeningSearchRes.builder().openingVOList(Collections.emptyList()).build());
            }
            Collections.shuffle(list);

            List<AiOpeningVO> res = list.subList(0, Math.min(size, list.size())).stream()
                    .map(o -> AiOpeningVO.builder().openingInfo(o).build()).collect(Collectors.toList());
            List<AiOpeningVO> cacheList = list.subList(Math.min(size, list.size()), list.size()).stream()
                    .map(o -> AiOpeningVO.builder().openingInfo(o).build()).collect(Collectors.toList());
            jedisCluster.setex(KEY_RANDOM_OPENING_TOPIC_CACHE, 300, JSON.toJSONString(cacheList));
            return R.data(OpeningSearchRes.builder().openingVOList(res).build());
        }  catch (InterruptedException e) {
            log.error("获取随机推荐话题加锁发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public R<OpeningSearchRes> getRandomOpening(Integer size, String userAccount) {
        LambdaQueryWrapper<AiOpening> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(AiOpening::getId).eq(AiOpening::getLevel, OpeningLevelEnum.OPENING.getCode());
        List<AiOpening> idRes = this.list(wrapper);
        if (CollectionUtils.isEmpty(idRes)) {
            return R.data(OpeningSearchRes.builder().openingVOList(Collections.emptyList()).build());
        }
        List<Long> idList = idRes.stream().map(AiOpening::getId).distinct().collect(Collectors.toList());
        Collections.shuffle(idList);
        List<AiOpening> list = this.listByIds(idList.subList(0, Math.min(size, idList.size())));

        List<String> openingIdList = list.stream().map(AiOpening::getId).map(String::valueOf).collect(Collectors.toList());
        LambdaQueryWrapper<AiOpeningBehavior> behaviorWrapper = new LambdaQueryWrapper<>();
        behaviorWrapper.eq(AiOpeningBehavior::getUserAccount, userAccount)
                .eq(AiOpeningBehavior::getType, OpeningTypeEnum.DB.getCode())
                .in(AiOpeningBehavior::getOpening, openingIdList);
        List<AiOpeningBehavior> behaviorList = aiOpeningBehaviorService.list(behaviorWrapper);
        Map<String, AiOpeningBehavior> behaviorMap = CollectionUtils.isEmpty(behaviorList) ? MapUtil.empty()
                : behaviorList.stream().collect(Collectors.toMap(AiOpeningBehavior::getOpening, Function.identity()));

        List<AiOpeningVO> res = new ArrayList<>();
        list.forEach(o -> {
            AiOpeningVO vo = AiOpeningVO.builder().openingInfo(o).build();
            if (behaviorMap.containsKey(String.valueOf(o.getId()))) {
                vo.setOpeningBehavior(behaviorMap.get(String.valueOf(o.getId())));
            }
            res.add(vo);
        });
        return R.data(OpeningSearchRes.builder().openingVOList(res).build());
    }

    @Override
    public R<OpeningSearchRes> search(OpeningSearchReq request) {
        if (StringUtils.isBlank(request.getContext())) {
            return getRandomOpening(request.getSize(), request.getAccount());
        }
        String[] arr = request.getContext().split("、");
        List<String> searchList = Arrays.stream(arr).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(searchList)) {
            return getRandomOpening(request.getSize(), request.getAccount());
        }

        // 获取推荐话题并转换后台关键词，记录搜索次数&命中次数，获取命中的前台关键词id&后台关键词id
        List<Long> keywordIdList = new ArrayList<>();
        LambdaQueryWrapper<AiOpening> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiOpening::getLevel, OpeningLevelEnum.TOPIC.getCode()).in(AiOpening::getContext, searchList);
        List<AiOpening> list = this.list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> idList = list.stream().map(AiOpening::getId).collect(Collectors.toList());
            LambdaQueryWrapper<AiOpening> randomWrapper = new LambdaQueryWrapper<>();
            randomWrapper.select(AiOpening::getId, AiOpening::getParentId)
                    .eq(AiOpening::getLevel, OpeningLevelEnum.BACKGROUND_KEYWORD.getCode()).in(AiOpening::getParentId, idList);
            List<AiOpening> randomList = this.list(randomWrapper);
            if (CollectionUtils.isNotEmpty(randomList)) {
                Map<Long, List<AiOpening>> randomMap = randomList.stream().collect(Collectors.groupingBy(AiOpening::getParentId));
                List<Long> randomIdList = new ArrayList<>(); // 前台关键词id & 随机后台关键词id
                for (Long parentId : randomMap.keySet()) {
                    randomIdList.add(parentId);
                    Random random = new Random();
                    int numbers = random.ints(0, randomMap.get(parentId).size()).distinct().limit(1)
                            .boxed().collect(Collectors.toList()).get(0);
                    randomIdList.add(randomMap.get(parentId).get(numbers).getId());
                }
                List<AiOpening> randomOpeningList = this.listByIds(randomIdList);
                for (AiOpening opening : randomOpeningList) {
                    if (OpeningLevelEnum.TOPIC.getCode().equals(opening.getLevel())) {
                        searchList.remove(opening.getContext());
                    } else {
                        searchList.add(opening.getContext());
                    }
                }

                Set<Long> addCountIdSet = new HashSet<>();
                addCountIdSet.addAll(randomIdList);
                addCountIdSet.addAll(idList);
                LambdaUpdateWrapper<AiOpening> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(AiOpening::getId, addCountIdSet).setSql("search_count = search_count + 1");
                this.update(updateWrapper);
                keywordIdList.addAll(addCountIdSet);
            }
        }

        // 调用模型生成话术&库中话术，获取收藏、点赞、踩历史记录
        try {
            String content = String.format(CHAT_OPENING_MSG, CHINESE_NUMBERS[request.getSize() - 1], String.join("、", searchList));
            ChatMessagesReq req = new ChatMessagesReq();
            req.setQuery(content);
            req.setResponse_mode(ChatModeEnum.BLOCKING.getValue());
            req.setUser(request.getAccount());
            log.info("调用Chat接口生成开场白 url={}, query={}, user={}", baseUrl + "/chat-messages", content, request.getAccount());
            String response = HttpRequest.post(baseUrl + "/chat-messages").auth("Bearer " + key)
                    .body(JSONUtil.toJsonStr(req), ContentType.JSON.getValue()).execute().body();
            if (StringUtils.isBlank(response)) {
                log.info("调用Chat接口生成开场白返回结果为{}", response);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "开场白生成失败");
            }
            ChatMessagesBlockRes res = JSONUtil.toBean(response, ChatMessagesBlockRes.class);
            if (Objects.isNull(res) || StringUtils.isBlank(res.getAnswer())
                    || Objects.isNull(res.getMetadata()) || CollectionUtils.isEmpty(res.getMetadata().getRetriever_resources())) {
                log.info("调用Chat接口生成开场白返回结果为{}", response);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "开场白生成失败");
            }

            // 库中话术
            List<String> contentList = res.getMetadata().getRetriever_resources().stream()
                    .map(ChatMessagesBlockRes.RetrieverResources::getContent).filter(StringUtils::isNotBlank)
                    .distinct().collect(Collectors.toList());
            List<String> subList = contentList.subList(0, Math.min(request.getSize() / 2, contentList.size()));
            LambdaQueryWrapper<AiOpening> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(AiOpening::getContext, subList).eq(AiOpening::getLevel, OpeningLevelEnum.OPENING.getCode());
            List<AiOpening> openingList = this.list(queryWrapper);
            List<String> dbList = CollectionUtils.isEmpty(openingList) ? Collections.emptyList()
                    : openingList.stream().map(o -> String.valueOf(o.getId())).collect(Collectors.toList());

            // AI生成话术
            Parser parser = Parser.builder().build();
            Node document = parser.parse(res.getAnswer());
            TextContentRenderer renderer = TextContentRenderer.builder().build();
            String answer = renderer.render(document);
            String[] generateArr = answer.split("\n");
            List<String> generateList = Arrays.stream(generateArr).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (generateList.size() < request.getSize()) {
                log.info("调用Chat接口生成开场白返回结果为{}", response);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "开场白生成失败");
            } else if (generateList.size() > request.getSize()) {
                generateList = generateList.stream().filter(s -> s.matches("^(\\d+\\. |[一二三四五六七八九十百千万亿]+[、]).*"))
                        .distinct().collect(Collectors.toList());
                if (generateList.size() != request.getSize()) {
                    log.info("调用Chat接口生成开场白返回结果为{}", response);
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "开场白生成失败");
                }
            }
            generateList = generateList.stream().map(s -> s.replaceAll("^(\\d+\\. |[一二三四五六七八九十百千万亿]+[、])", " "))
                    .distinct().collect(Collectors.toList());
            List<String> subGenerateList = generateList.subList(0, Math.min(generateList.size(), request.getSize() - openingList.size()));

            LambdaQueryWrapper<AiOpeningBehavior> behaviorWrapper = new LambdaQueryWrapper<>();
            behaviorWrapper.eq(AiOpeningBehavior::getUserAccount, request.getAccount())
                    .eq(AiOpeningBehavior::getType, OpeningTypeEnum.AI_GENERATE.getCode()).in(AiOpeningBehavior::getOpening, subGenerateList);
            if (CollectionUtils.isNotEmpty(dbList)) {
                behaviorWrapper.or().eq(AiOpeningBehavior::getUserAccount, request.getAccount())
                        .eq(AiOpeningBehavior::getType, OpeningTypeEnum.DB.getCode()).in(AiOpeningBehavior::getOpening, dbList);
            }
            List<AiOpeningBehavior> behaviorList = aiOpeningBehaviorService.list(behaviorWrapper);
            Map<String, AiOpeningBehavior> behaviorMap = CollectionUtils.isEmpty(behaviorList) ?
                    MapUtil.empty() : behaviorList.stream().collect(Collectors.toMap(AiOpeningBehavior::getOpening, Function.identity()));

            List<AiOpeningVO> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(openingList)) {
                for (AiOpening opening : openingList) {
                    AiOpeningVO vo = AiOpeningVO.builder().openingInfo(opening).keywordIdList(keywordIdList).build();
                    if (behaviorMap.containsKey(String.valueOf(opening.getId()))) {
                        vo.setOpeningBehavior(behaviorMap.get(String.valueOf(opening.getId())));
                    }
                    result.add(vo);
                }
            }
            subGenerateList.forEach(s -> {
                AiOpening opening = new AiOpening();
                opening.setContext(s);
                AiOpeningVO vo = AiOpeningVO.builder().openingInfo(opening).keywordIdList(keywordIdList).build();
                if (behaviorMap.containsKey(s)) {
                    vo.setOpeningBehavior(behaviorMap.get(s));
                }
                result.add(vo);
            });
            Collections.shuffle(result);
            return R.data(OpeningSearchRes.builder().openingVOList(result).build());
        } catch(Exception e){
            log.error("AiOpeningService.search 生成开场白失败，失败原因为", e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "开场白生成失败");
        }
    }

    @Override
    public R<Object> behavior(OpeningBehaviorReq request) {
        if (OpeningTypeEnum.DB.getCode().equals(request.getOpeningType())) {
            AiOpening aiOpening = this.getById(Long.valueOf(request.getOpening()));
            if (Objects.isNull(aiOpening)) {
                return R.fail(ResultCode.PARAM_INVALID);
            }
        }

        LambdaQueryWrapper<AiOpeningBehavior> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiOpeningBehavior::getUserAccount, request.getAccount())
                .eq(AiOpeningBehavior::getType, request.getOpeningType())
                .eq(AiOpeningBehavior::getOpening, request.getOpening());
        AiOpeningBehavior behavior = aiOpeningBehaviorService.getOne(wrapper);
        if (Objects.isNull(behavior)) {
            if (BehaviorTypeEnum.CANCEL_STAR.getCode().equals(request.getBehavior())) {
                return R.fail(ResultCode.PARAM_INVALID);
            }
            AiOpeningBehavior newBehavior = new AiOpeningBehavior();
            newBehavior.setType(request.getOpeningType());
            newBehavior.setOpening(request.getOpening());
            newBehavior.setUserAccount(request.getAccount());
            if (BehaviorTypeEnum.STAR.getCode().equals(request.getBehavior())) {
                newBehavior.setIsStar(Boolean.TRUE);
                newBehavior.setStarTime(LocalDateTime.now());
            } else {
                newBehavior.setLikeOrDown(request.getBehavior());
                newBehavior.setLikeOrDownTime(LocalDateTime.now());
            }
            aiOpeningBehaviorService.save(newBehavior);
        } else {
            if (BehaviorTypeEnum.getLikeOrDown().contains(request.getBehavior())) {
                if (request.getBehavior().equals(behavior.getLikeOrDown())) {
                    return R.fail(ResultCode.PARAM_INVALID);
                }
                if (!behavior.getLikeOrDown().equals(0)) {
                    return R.fail(ResultCode.NO_EDIT);
                }
                behavior.setLikeOrDown(request.getBehavior());
                behavior.setLikeOrDownTime(LocalDateTime.now());
            } else {
                if (BehaviorTypeEnum.STAR.getCode().equals(request.getBehavior()) && behavior.getIsStar()
                        || BehaviorTypeEnum.CANCEL_STAR.getCode().equals(request.getBehavior()) && !behavior.getIsStar()) {
                    return R.fail(ResultCode.PARAM_INVALID);
                }
                behavior.setIsStar(BehaviorTypeEnum.STAR.getCode().equals(request.getBehavior()));
                behavior.setStarTime(LocalDateTime.now());
            }
            aiOpeningBehaviorService.updateById(behavior);
        }

        if (CollectionUtils.isNotEmpty(request.getKeywordIdList())
                && !BehaviorTypeEnum.CANCEL_STAR.getCode().equals(request.getBehavior())) {
            LambdaUpdateWrapper<AiOpening> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(AiOpening::getId, request.getKeywordIdList());
            if (BehaviorTypeEnum.LIKE.getCode().equals(request.getBehavior())) {
                updateWrapper.setSql("like_count = like_count + 1");
            } else if (BehaviorTypeEnum.DOWN.getCode().equals(request.getBehavior())) {
                updateWrapper.setSql("down_count = down_count + 1");
            } else {
                updateWrapper.setSql("star_count = star_count + 1");
            }
            this.update(updateWrapper);
        }

        return R.data(null);
    }

    @Override
    public R<IPage<AiOpeningBehaviorVO>> getPage(BasePageReq request) {
        Page<AiOpeningBehavior> page = new Page<>(request.getCurrent(), request.getSize());
        LambdaQueryWrapper<AiOpeningBehavior> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiOpeningBehavior::getUserAccount, request.getAccount())
                .eq(AiOpeningBehavior::getIsStar, Boolean.TRUE).orderByDesc(AiOpeningBehavior::getStarTime);
        IPage<AiOpeningBehavior> list = aiOpeningBehaviorService.page(page, wrapper);

        Page<AiOpeningBehaviorVO> pageResult = new Page<>();
        pageResult.setTotal(list.getTotal());
        pageResult.setPages(list.getPages());
        pageResult.setCurrent(list.getCurrent());
        pageResult.setSize(list.getSize());
        if (CollectionUtils.isEmpty(list.getRecords())) {
            pageResult.setRecords(Collections.emptyList());
        } else {
            List<Long> openingIdList = list.getRecords().stream().filter(b -> OpeningTypeEnum.DB.getCode().equals(b.getType()))
                    .map(b -> Long.valueOf(b.getOpening())).collect(Collectors.toList());
            List<AiOpening> openingList = this.listByIds(openingIdList);
            if (openingIdList.size() != openingList.size()) {
                throw new AiServerException(ResultCode.FAILURE);
            }
            Map<Long, String> openingMap = openingList.stream().collect(Collectors.toMap(AiOpening::getId, AiOpening::getContext));
            List<AiOpeningBehaviorVO> res = new ArrayList<>();
            for (AiOpeningBehavior behavior : list.getRecords()) {
                AiOpeningBehaviorVO behaviorVO = new AiOpeningBehaviorVO();
                BeanUtils.copyProperties(behavior, behaviorVO);
                behaviorVO.setOpeningText(
                        OpeningTypeEnum.DB.getCode().equals(behavior.getType()) && openingMap.containsKey(Long.valueOf(behavior.getOpening()))
                                ? openingMap.get(Long.valueOf(behavior.getOpening())) : behavior.getOpening());
                res.add(behaviorVO);
            }
            pageResult.setRecords(res);
        }
        return R.data(pageResult);
    }
}
