package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yirendai.voiceaiserver.service.EmailSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RefreshScope
public class EmailSenderImpl implements EmailSender {

    private static final String COMMA = ",";

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    public void sendEmail(String subject, String toAddress, String ccAddress, String content) {
        send(from, subject, toAddress, ccAddress, content);
    }

    private void send(String fromAddress, String subject, String toAddress, String ccAddress, String content) {
        try {
            log.info("sending email from {}, to {}, cc {}, subject:{}", fromAddress, toAddress, ccAddress, subject);
            MimeMessage mailMessage = mailSender.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true);
            messageHelper.setFrom(fromAddress);
            messageHelper.setTo(toAddress.split(COMMA));
            if (StrUtil.isNotEmpty(ccAddress)) {
                messageHelper.setCc(ccAddress.split(COMMA));
            }
            messageHelper.setSubject(subject);
            messageHelper.setText(content, true);
            mailSender.send(mailMessage);
        } catch (Exception e) {
            log.error("exception happened during sending mail.", e);
        }
    }

    @Override
    public void sendEmail(String subject, String fromAddress, String toAddress, String ccAddress, String content) {
        send(fromAddress, subject, toAddress, ccAddress, content);
    }

    @Override
    public boolean sendMailWithAttachments(String from, String to, String ccAddress, String subject, String content,
                                           List<String> attachments) {
        try {
            // 创建MimeMessage
            MimeMessage mailMessage = mailSender.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true);

            // 设置基本信息
            messageHelper.setFrom(from);
            messageHelper.setTo(to.split(COMMA));
            if (StrUtil.isNotEmpty(ccAddress)) {
                messageHelper.setCc(ccAddress.split(COMMA));
            }
            messageHelper.setSubject(subject);
            messageHelper.setText(content, true); // true表示支持HTML内容

            // 添加附件
            if (attachments != null && !attachments.isEmpty()) {
                for (String filePath : attachments) {
                    File file = new File(filePath);
                    if (file.exists()) {
                        // 使用文件名作为附件名
                        messageHelper.addAttachment(file.getName(), file);
                    } else {
                        log.warn("附件文件不存在: {}", filePath);
                    }
                }
            }

            // 发送邮件
            mailSender.send(mailMessage);
            log.info("邮件发送成功 - 收件人: {}, 主题: {}", to, subject);
            return true;

        } catch (MessagingException e) {
            log.error("邮件发送失败 - 收件人: {}, 主题: {}, 错误: {}", to, subject, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendMailWithInlineImages(String from, String to, String ccAddress, String subject, String content,
                                            Map<String, String> images, List<String> attachments) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setFrom(from);
            helper.setTo(to.split(COMMA));
            if (StrUtil.isNotEmpty(ccAddress)) {
                helper.setCc(ccAddress.split(COMMA));
            }
            helper.setSubject(subject);
            helper.setText(content, true);

            // 添加内嵌图片
            if (images != null && !images.isEmpty()) {
                for (Map.Entry<String, String> entry : images.entrySet()) {
                    FileSystemResource file = new FileSystemResource(entry.getValue());
                    helper.addInline(entry.getKey(), file);
                }
            }

            // 添加附件
            if (attachments != null && !attachments.isEmpty()) {
                for (String filePath : attachments) {
                    File file = new File(filePath);
                    if (file.exists()) {
                        helper.addAttachment(file.getName(), file);
                    }
                }
            }

            mailSender.send(message);
            log.info("带图片的邮件发送成功 - 收件人: {}, 主题: {}", to, subject);
            return true;

        } catch (MessagingException e) {
            log.error("带图片的邮件发送失败 - 收件人: {}, 主题: {}, 错误: {}",
                    to, subject, e.getMessage(), e);
            return false;
        }
    }
}
