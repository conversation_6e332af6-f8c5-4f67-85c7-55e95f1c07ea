package com.yirendai.voiceaiserver.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.vo.request.AiHelperMessageReq;
import com.yirendai.voiceaiserver.vo.response.WelComeVO;
import com.yirendai.workbench.entity.AiHelperMessage;
import org.springblade.core.secure.BladeUser;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI 智能体助手
 **/
public interface AiHelperService {

    SseEmitter chatMessageStream(AiHelperMessageReq chatMessagesReq);

    void saveMessage(AiHelperMessage aiHelperMessage);

    /**
     * 停止响应
     * @param taskId
     * @param userId
     * @return
     */
    R<String> stop(String taskId,String userId , Integer scene);

    /**
     * 点赞、点踩
     * @param bladeUser
     * @param rating 点赞 like, 点踩 dislike, 撤销点赞 0
     * @param messageId 消息ID
     * @return
     */
    R<String> rating(BladeUser bladeUser, String rating, String messageId);

    /**
     * 获取欢迎语
     * @param bladeUser
     * @return
     */
    R<WelComeVO> getWelcome(BladeUser bladeUser);

    /**
     * 获取下一轮建议问题列表
     * @param messageId
     * @return
     */
    R<List<String>> suggested(String messageId,String userId);

    /**
     * 根据会话ID删除聊天记录
     * @param conversationId
     * @param s
     * @return
     */
    R<String> delete(String conversationId, String s);

    /**
     * 分布查询历史消息记录
     * @param userId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     */
    IPage<AiHelperMessage> listAiHelperMessage(Long userId, String startTime, String endTime, int pageNum, int pageSize);


}
