package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.workbench.mapper.AdsQwLongInsOrderDetailDfMapper;
import com.yirendai.workbench.entity.AdsQwLongInsOrderDetailDf;
import com.yirendai.voiceaiserver.service.AdsQwLongInsOrderDetailDfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 新客团队长险佣金订单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
public class AdsQwLongInsOrderDetailDfServiceImpl extends ServiceImpl<AdsQwLongInsOrderDetailDfMapper, AdsQwLongInsOrderDetailDf> implements AdsQwLongInsOrderDetailDfService {

    @Override
    public List<AdsQwLongInsOrderDetailDf> getPlannerAchievements() {
        return baseMapper.getPlannerAchievements();
    }

    @Override
    public List<AdsQwLongInsOrderDetailDf> getPlannerUsers() {
        return baseMapper.getPlannerUsers();
    }
}
