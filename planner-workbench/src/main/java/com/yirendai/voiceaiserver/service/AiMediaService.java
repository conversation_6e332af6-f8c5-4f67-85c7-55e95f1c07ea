package com.yirendai.voiceaiserver.service;

import java.io.File;

/**
 * @Description: AI媒体文件转换接口
 * @Author: yuanbo
 * @Date: 2024/6/14 13:53
 * @Version: 1.0
 **/

public interface AiMediaService {

    // 分批处理的音频数据
    void audioHandle();

    // 音频文件转文字
    String audioToText(File file, String type);

    // 音频文件url转文件对象
    File urlToFile(String url, String suffix);

    // 音频文件转换为MP3格式
    File audioToMp3(File sourceFile);

    // 分批处理的视频数据
    void videoHandle();

    // 视频文件转音频
    File videoToAudio(File sourceFile);
}
