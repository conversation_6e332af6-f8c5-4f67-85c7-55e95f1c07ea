/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.config.CaffeineCacheConfig;
import com.yirendai.voiceaiserver.service.IAiUserTagCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.service.IAiUserTagDictionaryService;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import com.yirendai.voiceaiserver.vo.response.AiUserTagCategoryVO;
import com.yirendai.workbench.entity.AiUserTagCategory;
import com.yirendai.workbench.entity.AiUserTagDictionary;
import com.yirendai.workbench.mapper.AiUserTagCategoryMapper;
import com.yirendai.workbench.mapper.AiUserTagDictionaryMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.yirendai.voiceaiserver.service.impl.AiUserTagDictionaryServiceImpl.AI_TAG_CATEGORY_CACHE_KEY_PREFIX;

/**
 * ai用户标签分类 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Slf4j
@Service
public class AiUserTagCategoryServiceImpl extends ServiceImpl<AiUserTagCategoryMapper, AiUserTagCategory> implements IAiUserTagCategoryService {

    @Autowired
    private AiUserTagCategoryMapper aiUserTagCategoryMapper;

    @Autowired
    private AiUserTagDictionaryMapper aiUserTagDictionaryMapper;

    @Autowired
    private Cache<String, List<AiTagCategoryWithTags>> aiTagCategoryCache;

    @Resource
    private IAiUserTagDictionaryService iAiUserTagDictionaryService;

    /**
     * 标签分类列表（递归）
     *
     * @return
     */
    @Override
    public List<AiUserTagCategoryVO> categoryVOList(String categoryName, Integer userType) {
        return selectByParentId(0L, categoryName, userType);
    }

    private List<AiUserTagCategoryVO> selectByParentId(Long parentId, String categoryName, Integer userType) {
        List<AiUserTagCategoryVO> resList = new ArrayList<>();
        List<AiUserTagCategory> list = baseMapper.selectByParentId(parentId, categoryName, userType);
        if (CollectionUtil.isNotEmpty(list)) {
            for (AiUserTagCategory aiUserTagCategory : list) {
                AiUserTagCategoryVO vo = BeanUtil.copy(aiUserTagCategory, AiUserTagCategoryVO.class);
                vo.setChildren(this.selectByParentId(vo.getId(), categoryName, userType));
                resList.add(vo);
            }
        }
        return resList;
    }

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Transactional
    @Override
    public void insertTagsAndCategoriesByJSON(String jsonData) {
        JsonNode rootNode = null;
        try {
            rootNode = objectMapper.readTree(jsonData);
        } catch (JsonProcessingException e) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }

        Iterator<String> categoryNames = rootNode.fieldNames();
        int size = rootNode.size();
        while (categoryNames.hasNext()) {
            if (size > 100) {
                throw new AiServerException(ResultCode.DATA_OVER_LIMIT);
            }
            String categoryName = categoryNames.next();
            JsonNode categoryNode = rootNode.get(categoryName);
            // 解析并插入标签
            JsonNode tagsNode = categoryNode.get("tags");

            List<String> tags = StreamSupport.stream(tagsNode.spliterator(), false)
                    .map(JsonNode::asText)
                    .collect(Collectors.toList());
            JsonNode matchDes = categoryNode.get("match_des");
            String matchDesStr = "";
            if (matchDes != null) {
                matchDesStr = matchDes.asText();
            }
            insertTagsAndCategory(0L, categoryName, categoryNode.get("multi_select").asInt(), matchDesStr, tags, categoryNode.get("color_value").asText());
            size++;
        }
    }

    @Override
    @Transactional
    public Boolean insertTagsAndCategory(Long parentId, String categoryName, Integer multiSelect, String matchDes, List<String> tags, String tenantId) {
        multiSelect = multiSelect == -1 ? 0 : 1;
        LambdaQueryWrapper<AiUserTagCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AiUserTagCategory::getId)
                .eq(AiUserTagCategory::getCategoryName, categoryName)
                .eq(AiUserTagCategory::getTenantId, tenantId);
        List<AiUserTagCategory> aiUserTagCategories = aiUserTagCategoryMapper.selectList(queryWrapper);
        Long categoryId = null;
        if (aiUserTagCategories.size() > 1) {
            throw new AiServerException(ResultCode.DB_DATA_ERROR);
        }
        if (aiUserTagCategories.size() == 1 && aiUserTagCategories.get(0) != null) {
            categoryId = aiUserTagCategories.get(0).getId();
        } else {
            AiUserTagCategory category = new AiUserTagCategory();
            category.setCategoryName(categoryName);
            category.setMultiSelect(multiSelect);
            category.setUserType(0);
            category.setParentId(parentId);
            category.setStatus(1);
            category.setIsDeleted(0);
            category.setMatchDes(matchDes);
            category.setUpdateTime(LocalDateTime.now());
            category.setCreateTime(LocalDateTime.now());
            category.setTenantId(tenantId);
            aiUserTagCategoryMapper.insert(category);
            categoryId = category.getId();
        }

        if (categoryId == null) {
            throw new AiServerException(ResultCode.FAILURE);
        }

        List<AiUserTagDictionary> aiUserTagDictionaryList = new ArrayList<>();

        for (String tag : tags) {
            LambdaQueryWrapper<AiUserTagDictionary> tagQueryWrapper = new LambdaQueryWrapper<>();
            tagQueryWrapper.select(AiUserTagDictionary::getId)
                    .eq(AiUserTagDictionary::getTagName, tag)
                    .eq(AiUserTagDictionary::getUserTagCategoryId, categoryId)
                    .eq(AiUserTagDictionary::getTenantId, tenantId);
            List<AiUserTagDictionary> aiUserTagDictionaries = aiUserTagDictionaryMapper.selectList(tagQueryWrapper);
            if (aiUserTagDictionaries.size() > 1) {
                throw new AiServerException(ResultCode.DB_DATA_ERROR);
            }
            if (aiUserTagDictionaries.size() == 1 && aiUserTagDictionaries.get(0) != null) {
                continue;
            }
            AiUserTagDictionary tagEntity = new AiUserTagDictionary();
            tagEntity.setTagName(tag);
            tagEntity.setUserTagCategoryId(categoryId);
            tagEntity.setUserType(0);
            tagEntity.setStatus(1);
            tagEntity.setIsDeleted(0);
            tagEntity.setUpdateTime(LocalDateTime.now());
            tagEntity.setCreateTime(LocalDateTime.now());
            tagEntity.setTenantId(tenantId);
            aiUserTagDictionaryList.add(tagEntity);
        }
        boolean b = iAiUserTagDictionaryService.saveBatch(aiUserTagDictionaryList);
        if (b) {
            aiTagCategoryCache.invalidate(AI_TAG_CATEGORY_CACHE_KEY_PREFIX + tenantId);
        }
        return b;
    }

    @Override
    public AiUserTagCategory selectByCategoryName(String category, String tenantId) {
        LambdaQueryWrapper<AiUserTagCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUserTagCategory::getCategoryName, category);
        queryWrapper.eq(AiUserTagCategory::getTenantId, tenantId);
        AiUserTagCategory aiUserTagCategory = aiUserTagCategoryMapper.selectOne(queryWrapper);
        if (aiUserTagCategory == null) {
            return null;
        }
        return aiUserTagCategory;
    }

    @Override
    public AiUserTagCategory selectById(Long topParentId) {
        return aiUserTagCategoryMapper.selectById(topParentId);
    }

    @Override
    public AiUserTagCategory getLastTopTagCategory() {
        LambdaQueryWrapper<AiUserTagCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiUserTagCategory::getParentId, 0);
        queryWrapper.orderByDesc(AiUserTagCategory::getId);
        queryWrapper.last("limit 1");
        return aiUserTagCategoryMapper.selectOne(queryWrapper);
    }

    @Transactional
    @Override
    public Boolean parseExcelFile(InputStream inputStream, String tenantId) throws IOException {
        Workbook workbook = WorkbookFactory.create(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null) {
            return false;
        }
        boolean allBatchesSavedSuccessfully = false;
        String lastCategory = "";
        String lastSubCategory = "";
        String lastColor = "";
        int lastMatchMoreInt = 0;
        List<String> curTagList = new ArrayList<>();
        Map<String, Long> categoryHashMap = new HashMap<>();
        for (Row row : sheet) {
            // 跳过第一行
            if (row.getRowNum() == 0) {
                continue;
            }
            Cell categoryCell = row.getCell(0);
            Cell colorCell = row.getCell(1);
            Cell subCategoryCell = row.getCell(2);
            Cell matchMore = row.getCell(3);
            Cell tagCell = row.getCell(4);

            String category = categoryCell != null ? categoryCell.getStringCellValue() : "";
            String color = colorCell != null ? colorCell.getStringCellValue() : "";
            String subCategory = subCategoryCell != null ? subCategoryCell.getStringCellValue() : "";
            int matchMoreInt = matchMore != null ? (int)matchMore.getNumericCellValue() : 0;
            String tag = tagCell != null ? tagCell.getStringCellValue() : "";
            if (Strings.isNullOrEmpty(category)) {
                category = lastCategory;
            }
            if (Strings.isNullOrEmpty(subCategory)) {
                subCategory = lastSubCategory;
            }
            if (Strings.isNullOrEmpty(color)) {
                color = lastColor;
            }
            if (matchMoreInt == 0) {
                matchMoreInt = lastMatchMoreInt;
            }

            // 处理每一行的数据
            log.info("分类: {}, 色值: {} 二级分类: {}, 是否可多选: {}, 标签: {}", category, color, subCategory, matchMoreInt, tag);
            if (Strings.isNullOrEmpty(category)) {
                continue;
            }
            if (!categoryHashMap.containsKey(category)) {
                LambdaQueryWrapper<AiUserTagCategory> tagCategoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                tagCategoryLambdaQueryWrapper.eq(AiUserTagCategory::getCategoryName, category)
                        .eq(AiUserTagCategory::getParentId, 0L)
                        .eq(AiUserTagCategory::getTenantId, tenantId);
                AiUserTagCategory tagCategory = aiUserTagCategoryMapper.selectOne(tagCategoryLambdaQueryWrapper);
                if (tagCategory == null) {
                    tagCategory = new AiUserTagCategory();
                    tagCategory.setCategoryName(category);
                    tagCategory.setMultiSelect(1);
                    tagCategory.setUserType(0);
                    tagCategory.setParentId(0L);
                    tagCategory.setStatus(1);
                    tagCategory.setColorValue(color);
                    tagCategory.setIsDeleted(0);
                    tagCategory.setMatchDes("");
                    tagCategory.setUpdateTime(LocalDateTime.now());
                    tagCategory.setCreateTime(LocalDateTime.now());
                    tagCategory.setTenantId(tenantId);
                    aiUserTagCategoryMapper.insert(tagCategory);
                }
                Long categoryId = tagCategory.getId();
                if (categoryId != 0L) {
                    categoryHashMap.put(category, categoryId);
                }
            }

            if ("NULL".equals(lastSubCategory)) {
                if ((!Strings.isNullOrEmpty(subCategory) || !category.equals(lastCategory)) && !Strings.isNullOrEmpty(lastCategory)) {
                    allBatchesSavedSuccessfully |= insertTagsAndCategory(0L, lastCategory, lastMatchMoreInt, "", curTagList, tenantId);
                    curTagList = new ArrayList<>();
                }
            } else {
                if ((!category.equals(lastCategory) || !subCategory.equals(lastSubCategory)) && !Strings.isNullOrEmpty(lastCategory)) {
                    allBatchesSavedSuccessfully |= insertTagsAndCategory(categoryHashMap.get(lastCategory), lastSubCategory, lastMatchMoreInt, "", curTagList, tenantId);
                    curTagList = new ArrayList<>();
                }
            }
            curTagList.add(tag);
            lastCategory = category;
            lastSubCategory = subCategory;
            lastMatchMoreInt = matchMoreInt;
        }
        if ("NULL".equals(lastSubCategory)) {
            allBatchesSavedSuccessfully |= insertTagsAndCategory(0L, lastCategory, lastMatchMoreInt, "", curTagList, tenantId);
        } else {
            allBatchesSavedSuccessfully |= insertTagsAndCategory(categoryHashMap.get(lastCategory), lastSubCategory, lastMatchMoreInt, "", curTagList, tenantId);
        }
        log.info("标签批量导入结果：{}", allBatchesSavedSuccessfully);
        return allBatchesSavedSuccessfully;
    }
}
