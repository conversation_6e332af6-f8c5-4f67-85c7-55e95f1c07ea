package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.app.fortune.common.util.DateUtil;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.service.AiCallReservationService;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.AiCallReservation;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiCallReservationMapper;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【ai_call_reservation(ai通话预约表)】的数据库操作Service实现
* @createDate 2024-11-14 11:29:35
*/
@Slf4j
@Service
public class AiCallReservationServiceImpl extends ServiceImpl<AiCallReservationMapper, AiCallReservation>
implements AiCallReservationService {
}