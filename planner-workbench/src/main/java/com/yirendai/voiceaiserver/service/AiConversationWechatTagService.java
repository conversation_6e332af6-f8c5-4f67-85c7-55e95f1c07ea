package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.enums.AiConversationTagTypeEnum;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationTagExportDto;
import com.yirendai.workbench.entity.AiConversationWechatSummary;
import com.yirendai.workbench.entity.AiConversationWechatTag;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_wechat_tag(ai企微对话与标签关联表)】的数据库操作Service
* @createDate 2024-12-20 11:28:41
*/
public interface AiConversationWechatTagService extends IService<AiConversationWechatTag> {

    /**
     * 插入AI对话标签
     * @param wechatSummaryId 企微对话ID
     * @param plannerNo 理财师工号
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param intervalTag 间隔标签
     * @param tagCategoryId 标签分类ID
     * @param tagId 标签ID
     * @param category 标签分类名
     * @param name 标签名
     * @param matchStatus 标签匹配状态
     */
    void insertAiConversationTag(Long wechatSummaryId, String plannerNo, String userId, String tenantId, String intervalTag, Long tagCategoryId, Long tagId, String category, String name, AiConversationTagTypeEnum matchStatus);


    /**
     * 插入AI对话标签
     * @param aiConversationWechatSummary 企微小结
     * @param tagCategoryId 标签分类ID
     * @param tagId 标签ID
     * @param category 标签分类名
     * @param name 标签名
     * @param matchStatus 标签匹配状态
     */
    void insertAiConversationTag(AiConversationWechatSummary aiConversationWechatSummary, Long tagCategoryId, Long tagId, String category, String name, AiConversationTagTypeEnum matchStatus);

    void deleteByChatContactId(@NotNull(message = "对话ID不可为空") Long wechatSummaryId);
}
