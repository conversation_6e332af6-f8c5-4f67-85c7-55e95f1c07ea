package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.enums.PrimaryIntentEnum;
import com.yirendai.voiceaiserver.service.IAiConversationFlowResultService;
import com.yirendai.workbench.entity.AiConversationFlowResult;
import com.yirendai.workbench.mapper.AiConversationFlowResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI对话流程结果表(按节点维度) 服务实现类
 */
@Slf4j
@Service
public class AiConversationFlowResultServiceImpl extends ServiceImpl<AiConversationFlowResultMapper, AiConversationFlowResult> implements IAiConversationFlowResultService {

    @Override
    public List<AiConversationFlowResult> getResultsBySource(String sourceType, Long sourceId, String tenantId) {
        return baseMapper.selectBySource(sourceType, sourceId, tenantId);
    }

    @Override
    public void batchSave(List<AiConversationFlowResult> results) {
        if (results != null && !results.isEmpty()) {
            baseMapper.batchInsert(results);
        }
    }

    @Transactional
    @Override
    public void batchSaveWithDeduplication(List<AiConversationFlowResult> results) {
        if (results == null || results.isEmpty()) {
            return;
        }
        
        // 获取第一条记录的关键信息用于删除
        AiConversationFlowResult firstResult = results.get(0);
        String sourceType = firstResult.getSourceType();
        Long sourceId = firstResult.getSourceId();
        String tenantId = firstResult.getTenantId();
        String flowType = firstResult.getFlowType();
        String intentCode = firstResult.getIntentCode();
        
        log.info("开始保存流程结果（带去重），sourceType={}, sourceId={}, flowType={}, intentCode={}, 结果数量={}", 
            sourceType, sourceId, flowType, intentCode, results.size());
        
        // 先删除现有的相同条件的结果
        deleteResults(sourceType, sourceId, tenantId, flowType);
        
        // 再插入新结果
        batchSave(results);
        
        log.info("流程结果保存完成，已覆盖旧结果");
    }

    @Override
    public void deleteResults(String sourceType, Long sourceId, String tenantId, String flowType) {
        LambdaQueryWrapper<AiConversationFlowResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationFlowResult::getSourceType, sourceType)
                .eq(AiConversationFlowResult::getSourceId, sourceId)
                .eq(AiConversationFlowResult::getTenantId, tenantId)
                .eq(AiConversationFlowResult::getFlowType, flowType);
        
        int deletedCount = baseMapper.delete(queryWrapper);
        log.info("删除旧的流程结果，数量={}, sourceType={}, sourceId={}, flowType={}",
            deletedCount, sourceType, sourceId, flowType);
    }

    @Transactional
    @Override
    public void clearNodeResultsWhenNoIntent(String sourceType, Long sourceId, String tenantId, String flowType) {
        // 删除所有非意图节点的结果（nodeLevel > 0的节点）
        LambdaQueryWrapper<AiConversationFlowResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationFlowResult::getSourceType, sourceType)
                .eq(AiConversationFlowResult::getSourceId, sourceId)
                .eq(AiConversationFlowResult::getTenantId, tenantId)
                .eq(AiConversationFlowResult::getFlowType, flowType)
                .gt(AiConversationFlowResult::getNodeLevel, 0); // 只删除节点结果，保留主意图结果
        
        int deletedCount = baseMapper.delete(queryWrapper);
        log.info("清理无效意图的节点结果，数量={}, sourceType={}, sourceId={}, flowType={}", 
            deletedCount, sourceType, sourceId, flowType);
    }

    @Override
    public AiConversationFlowResult getReachedResult(String userId, String nodeName, java.time.LocalDateTime startTime) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(nodeName)) {
            return null;
        }
        return baseMapper.selectReachedResult(userId, nodeName, startTime);
    }

    @Override
    public AiConversationFlowResult getMainIntent(String customerId, String expectedValue, LocalDateTime startTime) {
        LambdaQueryWrapper<AiConversationFlowResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationFlowResult::getUserId, customerId)
                .eq(AiConversationFlowResult::getIntentCode, PrimaryIntentEnum.getById(Integer.valueOf(expectedValue)).getCode())
                .ge(AiConversationFlowResult::getChatTime, startTime)
                .last("LIMIT 1");
        return baseMapper.selectOne(queryWrapper);
    }
}
