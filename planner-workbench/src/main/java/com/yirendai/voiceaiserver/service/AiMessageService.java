package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.entity.AiMessage;
import com.yirendai.voiceaiserver.vo.response.AiMessageVO;
import com.yirendai.voiceaiserver.vo.response.SuggestionInfo;
import com.yirendai.voiceaiserver.vo.response.SuggestionListRes;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ai信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
public interface AiMessageService extends IService<AiMessage> {

    R<AiMessageVO> saveOrUpdate(Integer type, String content);

    R<AiMessageVO> get(Integer type);

    R<SuggestionInfo> getSuggestion(Long userId);

    R<SuggestionListRes> getBatchSuggestion(Long userId, Integer count);
}
