package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationDataSourceEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.service.AiConversationWechatSummaryService;
import com.yirendai.voiceaiserver.util.TextSplitter;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationSummaryVO;
import com.yirendai.workbench.entity.AiConversationProcessTask;
import com.yirendai.workbench.entity.AiConversationWechatSummary;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.enums.QwMsgTypeEnum;
import com.yirendai.workbench.mapper.AiConversationProcessTaskMapper;
import com.yirendai.workbench.mapper.AiConversationWechatSummaryMapper;
import com.yirendai.workbench.util.OwnAuthUtil;
import org.springblade.core.secure.BladeUser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_wechat_summary(ai对话企微小结表)】的数据库操作Service实现
* @createDate 2024-12-04 17:11:24
*/
@Service
public class AiConversationWechatSummaryServiceImpl extends ServiceImpl<AiConversationWechatSummaryMapper, AiConversationWechatSummary>
implements AiConversationWechatSummaryService {

    @Resource
    private AiConversationWechatSummaryMapper aiConversationWechatSummaryMapper;
    @Resource
    private AiConversationProcessTaskService aiConversationProcessTaskService;
    @Resource
    private AiConversationProcessTaskMapper aiConversationProcessTaskMapper;

    @Value("${ai.text.process.retry}")
    private int textProcessRetry;


    @Override
    public Boolean initConversationWechatSummary(List<AiPlannerChatContact> chatContactList) {
        if (chatContactList == null || chatContactList.isEmpty()) {
            return false;
        }
        // 开始处理文本
        StringBuilder finalContent = new StringBuilder();
        for (AiPlannerChatContact chatContact : chatContactList) {
            if (chatContact == null) {
                continue;
            }
            StringBuilder content = new StringBuilder();
            String originType = chatContact.getOriginType();
            Integer direction = chatContact.getDirection();
            // 根据发起方向添加前缀
            if (direction == 0) {
                content.append(TextSplitter.PLANNER_PREFIX);
            } else if (direction == 1) {
                content.append(TextSplitter.CUSTOMER_PREFIX);
            }
            if (originType == null || originType.isEmpty()) {
                continue;
            } else if (originType.equals(QwMsgTypeEnum.TEXT.getType())) {
                String originContent = chatContact.getOriginContent();
                if (originContent == null || originContent.isEmpty()) {
                    continue;
                }
                content.append(originContent);
            } else if (originType.equals(QwMsgTypeEnum.IMAGE.getType())){
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.IMAGE.getName());
                content.append("-");
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.LINK.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.EMOTION.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.VOICE.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.WEAPP.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.REVOKE.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.EXTERNAL_REDPACKET.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.FILE.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.MEETING_VOICE_CALL.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.MEETING_VOICE_CALL.getName());
                content.append("-");
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.SPHFEED.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.VIDEO.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.VIDEO.getName());
                content.append("-");
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.MIXED.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.MIXED.getName());
                content.append("-");
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.CHATRECORD.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.CHATRECORD.getType());
                content.append("-");
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.CARD.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.CARD.getName());
            } else if (originType.equals(QwMsgTypeEnum.LOCATION.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.LOCATION.getName());
                content.append("-");
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.MEETING.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.MEETING.getName());
                content.append("-");
                content.append(processedContent);
            } else if (originType.equals(QwMsgTypeEnum.REDPACKET.getType())) {
                String processedContent = chatContact.getProcessedContent();
                if (processedContent == null || processedContent.isEmpty()) {
                    continue;
                }
                content.append(QwMsgTypeEnum.REDPACKET.getName());
                content.append("-");
                content.append(processedContent);
            }
            finalContent.append(content);
            // 添加换行符
            finalContent.append("\n");
        }
        String finalContentString = finalContent.toString();
        finalContentString = finalContentString.trim();
        // 空内容不处理
        if (finalContentString.isEmpty()) {
            return false;
        }
        // 入库
        AiPlannerChatContact aiPlannerChatContact = chatContactList.get(0);
        String plannerNo = aiPlannerChatContact.getPlannerNo();
        String userId = aiPlannerChatContact.getUserId();
        String intervalTag = aiPlannerChatContact.getMsgTime().toLocalDate().toString();
        AiConversationWechatSummary aiConversationWechatSummary = selectByOnlyKey(String.valueOf(plannerNo), String.valueOf(userId), intervalTag);
        if (aiConversationWechatSummary == null) {
            aiConversationWechatSummary = new AiConversationWechatSummary();
            aiConversationWechatSummary.setPlannerId(String.valueOf(plannerNo));
            aiConversationWechatSummary.setUserId(String.valueOf(userId));
            aiConversationWechatSummary.setWechatContent(finalContentString);
            // 区间标识用昨天的日期
            aiConversationWechatSummary.setIntervalTag(intervalTag);
            aiConversationWechatSummary.setTenantId(aiPlannerChatContact.getTenantId());
            aiConversationWechatSummary.setStatus(0);
            aiConversationWechatSummary.setUpdateTime(LocalDateTime.now());
            aiConversationWechatSummary.setCreateTime(LocalDateTime.now());
            try {
                aiConversationWechatSummaryMapper.insert(aiConversationWechatSummary);
            } catch (Exception e) {
                log.error("AiConversationWechatSummaryServiceImpl initConversationWechatSummary error", e);
            }
        }

        Long wechatSummaryId = aiConversationWechatSummary.getId();
        if (wechatSummaryId == null) {
            LambdaQueryWrapper<AiConversationWechatSummary> summaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            summaryLambdaQueryWrapper.eq(AiConversationWechatSummary::getPlannerId, plannerNo);
            summaryLambdaQueryWrapper.eq(AiConversationWechatSummary::getUserId, userId);
            summaryLambdaQueryWrapper.eq(AiConversationWechatSummary::getIntervalTag, intervalTag);
            aiConversationWechatSummary = aiConversationWechatSummaryMapper.selectOne(summaryLambdaQueryWrapper);
            if (aiConversationWechatSummary == null) {
                throw new AiServerException(ResultCode.WECHAT_MESSAGE_SUMMARY_ERROR);
            }
            wechatSummaryId = aiConversationWechatSummary.getId();
        }
        // 插入小结任务
        String taskId = aiConversationProcessTaskService.buildTaskId(wechatSummaryId, AiConversationProcessTypeEnum.CONTENT_WECHAT_SUMMARY);
        AiConversationProcessTask aiConversationProcessTask = aiConversationProcessTaskService.selectByTaskId(taskId);
        if (aiConversationProcessTask == null) {
            aiConversationProcessTask = new AiConversationProcessTask();
            aiConversationProcessTask.setTaskId(taskId);
            aiConversationProcessTask.setTaskType(AiConversationProcessTypeEnum.CONTENT_WECHAT_SUMMARY.getCode());
            aiConversationProcessTask.setTaskData(AiConversationProcessTypeEnum.CONTENT_WECHAT_SUMMARY.getDesc() + ":" + plannerNo + ":" + userId + ":" + intervalTag);
            aiConversationProcessTask.setTaskStatus(AiConversationProcessStatusEnum.INIT_WAITING.getCode());
            aiConversationProcessTask.setRetryCount(0);
            aiConversationProcessTask.setMaxRetry(textProcessRetry);
            aiConversationProcessTask.setTenantId(aiPlannerChatContact.getTenantId());
            aiConversationProcessTask.setCreateTime(LocalDateTime.now());
            aiConversationProcessTask.setUpdateTime(LocalDateTime.now());
            aiConversationProcessTaskMapper.insert(aiConversationProcessTask);
        }
        // 插入标签任务
        String tagTaskId = aiConversationProcessTaskService.buildTaskId(wechatSummaryId, AiConversationProcessTypeEnum.CONTENT_WECHAT_TAG);
        AiConversationProcessTask tagTask = aiConversationProcessTaskService.selectByTaskId(tagTaskId);
        if (tagTask == null) {
            tagTask = new AiConversationProcessTask();
            tagTask.setTaskId(tagTaskId);
            tagTask.setTaskType(AiConversationProcessTypeEnum.CONTENT_WECHAT_TAG.getCode());
            tagTask.setTaskData(AiConversationProcessTypeEnum.CONTENT_WECHAT_TAG.getDesc() + ":" + plannerNo + ":" + userId + ":" + intervalTag);
            tagTask.setTaskStatus(AiConversationProcessStatusEnum.INIT_WAITING.getCode());
            tagTask.setRetryCount(0);
            tagTask.setMaxRetry(textProcessRetry);
            tagTask.setTenantId(aiPlannerChatContact.getTenantId());
            tagTask.setCreateTime(LocalDateTime.now());
            tagTask.setUpdateTime(LocalDateTime.now());
            aiConversationProcessTaskMapper.insert(tagTask);
        }
        // 插入港险主意图任务
        try {
            String hkTaskId = AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode() + "-" + AiConversationDataSourceEnum.QI_WECHAT.getCode() + "_" + wechatSummaryId;
            // 企微数据源任务ID格式：{taskType}-{sourceType}_{chatContactId}
            String sourceType = AiConversationDataSourceEnum.QI_WECHAT.getCode();
            aiConversationProcessTaskService.createProcessTask(
                    hkTaskId,
                    wechatSummaryId,
                    AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode(),
                    AiConversationProcessStatusEnum.INIT_WAITING.getCode(),
                    aiPlannerChatContact.getTenantId(),
                    sourceType
            );
        } catch (Exception e) {
            log.error("创建企微港险主意图任务失败", e);
        }
        return true;
    }

    @Override
    public AiConversationSummaryVO selectByChatContactId(String plannerId, String userId, String date) {
        AiConversationWechatSummary aiConversationWechatSummary = selectByOnlyKey(plannerId, userId, date);
        if (aiConversationWechatSummary == null) {
            return null;
        }
        return AiConversationSummaryVO.builder()
                .plannerNo(aiConversationWechatSummary.getPlannerId())
                .userId(aiConversationWechatSummary.getUserId())
                .summaryContentPlanner(aiConversationWechatSummary.getSummaryContentPlanner())
                .summaryContentUser(aiConversationWechatSummary.getSummaryContentUser())
                .userContentValid(aiConversationWechatSummary.getUserContentValid())
                .confidenceScore(aiConversationWechatSummary.getConfidenceScore())
                .feedbackStatus(aiConversationWechatSummary.getFeedbackStatus())
                .build();
    }

    @Override
    public AiConversationWechatSummary selectByOnlyKey(String plannerId, String userId, String date) {
        LambdaQueryWrapper<AiConversationWechatSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationWechatSummary::getPlannerId, plannerId)
                .eq(AiConversationWechatSummary::getUserId, userId)
                .eq(AiConversationWechatSummary::getIntervalTag, date)
                .eq(AiConversationWechatSummary::getStatus, 1);
        return aiConversationWechatSummaryMapper.selectOne(queryWrapper);
    }

    @Override
    public Boolean feedback(AiFeedbackReq req) {
        Long id = req.getId();
        Integer feedbackStatus = req.getFeedbackStatus();
        LambdaQueryWrapper<AiConversationWechatSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationWechatSummary::getId, id);
        AiConversationWechatSummary aiConversationWechatSummary = aiConversationWechatSummaryMapper.selectOne(queryWrapper);
        if (aiConversationWechatSummary == null) {
            throw new AiServerException(ResultCode.NO_AI_CONVERSATION_WECHAT_SUMMARY);
        }
        aiConversationWechatSummary.setFeedbackStatus(feedbackStatus);
        BladeUser user = OwnAuthUtil.getUser();
        if (user == null) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        aiConversationWechatSummary.setFeedbackUserId(String.valueOf(user.getUserId()));
        aiConversationWechatSummary.setFeedbackUserName(user.getUserName());
        aiConversationWechatSummary.setFeedbackTime(LocalDateTime.now());
        aiConversationWechatSummary.setUpdateTime(LocalDateTime.now());
        aiConversationWechatSummaryMapper.updateById(aiConversationWechatSummary);
        return true;
    }
}
