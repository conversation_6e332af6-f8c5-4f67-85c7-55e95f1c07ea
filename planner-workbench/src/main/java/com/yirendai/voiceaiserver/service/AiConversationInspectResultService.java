package com.yirendai.voiceaiserver.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.entity.AiConversationInspectResult;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_inspect_result(ai对话内容稽查结果表)】的数据库操作Service
* @createDate 2024-11-13 18:59:09
*/
public interface AiConversationInspectResultService extends IService<AiConversationInspectResult> {

}
