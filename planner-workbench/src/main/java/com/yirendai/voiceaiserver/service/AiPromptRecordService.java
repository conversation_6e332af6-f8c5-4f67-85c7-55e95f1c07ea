package com.yirendai.voiceaiserver.service;

import com.yirendai.workbench.entity.AiPromptRecord;
import com.yirendai.voiceaiserver.vo.request.PromptConfigReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

public interface AiPromptRecordService extends IService<AiPromptRecord> {
    /**
     * 根据使用场景获取当前的提示词配置
     * @param sceneType 使用场景
     * @return
     */
    AiPromptRecordVo getLatestPromptRecordByScene(int sceneType);


    /**
     * 获取历史使用过的提示词配置,包括模型,温度和热词等
     * @param promptConfigReq
     * @return
     */
    IPage<AiPromptRecordVo> getHistoryPromptRecord(PromptConfigReq promptConfigReq);

    /**
     * 校验提示词配置是否符合要求
     * @param promptConfigReq
     */
    boolean validatePromptConfig(PromptConfigReq promptConfigReq);



}
