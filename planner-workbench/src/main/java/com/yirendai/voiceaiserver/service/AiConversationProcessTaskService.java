package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.task.AiRetryableTask;
import com.yirendai.workbench.entity.AiConversationProcessTask;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_process_task(ai对话内容处理任务表)】的数据库操作Service
* @createDate 2024-11-19 11:00:33
*/
public interface AiConversationProcessTaskService extends IService<AiConversationProcessTask> {

    /**
     * 暂时只有avaya类型的查漏
     * @param processType 任务类型
     * @return 任务数量
     */
    Integer checkLeakyDataTask(Integer processType, String tenantId);

    @Transactional
    void saveTask(AiRetryableTask task, AiConversationProcessStatusEnum status, String lastFailReason);

    void updateTask(AiRetryableTask task, AiConversationProcessStatusEnum status);

    List<AiConversationProcessTask> fetchFailedTasks();

    List<AiConversationProcessTask> fetchFailedProcessTasks();

    AiRetryableTask deserializeTaskData(String taskData);

    Long extractChatContactFromTaskId(String taskId);

    Integer extractTypeFromTaskId(String taskId);
    
    /**
     * 从taskId中提取数据源类型
     * @param taskId 任务ID，格式：12-0_123456
     * @return 数据源类型，如"0"(呼叫中心)、"1"(企微)，如果没有则返回null
     */
    String extractSourceTypeFromTaskId(String taskId);

    String buildTaskId(Long chatContactId, AiConversationProcessTypeEnum taskType);

    List<AiConversationProcessTask> fetchPendingWechatProcessTasks(AiConversationProcessStatusEnum  status);

    AiConversationProcessTask selectByTaskId(String taskId);

    /**
     * 创建AI对话处理任务
     * @param taskId 任务ID
     * @param chatContactId 对话ID
     * @param taskType 任务类型
     * @param status 任务状态 0-初始化 1-待执行 2-执行中 3-执行成功 4-执行失败
     */
    void createProcessTask(String taskId, Long chatContactId, Integer taskType, Integer status, String tenantId);
    
    /**
     * 创建AI对话处理任务（支持数据源类型）
     * @param taskId 任务ID
     * @param chatContactId 对话ID
     * @param taskType 任务类型
     * @param status 任务状态
     * @param tenantId 租户ID
     * @param sourceType 数据源类型
     */
    void createProcessTask(String taskId, Long chatContactId, Integer taskType, Integer status, String tenantId, String sourceType);
}
