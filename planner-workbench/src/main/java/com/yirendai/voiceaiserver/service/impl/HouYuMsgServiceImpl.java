package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.MsgSendDirectionEnum;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.service.HouYuMsgService;
import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.voiceaiserver.util.HttpClientProxyUtil;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.request.SendRobotReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import com.yirendai.workbench.service.ICallhistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class HouYuMsgServiceImpl implements HouYuMsgService {

    @Value("${nas.path}")
    private String path;

    @Value("${nas.url}")
    private String nasUrl;

    @Value("${qw.robot.sync.url}")
    private String robotSyncUrl;

    /**
     * 厚予音转文任务开始处理的消息idKey
     */
    private static final String KEY_HOUYU_TRANSLATE_START_ID = "voice:ai:server:houyu:translate:start:id";

    @Resource
    RestTemplate restTemplateVoice;

    @Resource
    JedisCluster jedisCluster;

    @Resource
    @Qualifier("avayaAddThreadPoolExecutor")
    ExecutorService avayaAddThreadPoolExecutor;

    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;

    @Resource
    CallcenterCallRecordService callcenterCallRecordService;

    @Resource
    ICallhistoryService iCallhistoryService;

    @Resource
    IAiPlannerTopUserService iAiPlannerTopUserService;

    @Resource
    AiPromptRecordService aiPromptRecordService;

    @Resource
    AvayaVoiceService avayaVoiceService;

    @Resource
    AIConversationProcessService aiConversationProcessService;

    @Override
    public void syncVideo() {
        log.info("同步厚予通话音频文件开始");
        Long startId = null;
        while (true) {
            LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
            if (Objects.nonNull(startId)) {
                wrapper.gt(CallcenterCallRecord::getId, startId);
            }
            wrapper.eq(CallcenterCallRecord::getSystemType, 3)
                    .eq(CallcenterCallRecord::getAnswered, 2)
                    .and(w -> w.isNull(CallcenterCallRecord::getRecordUrl).or().apply("TRIM(record_url) = ''"))
                    .orderByAsc(CallcenterCallRecord::getId).last("LIMIT 100");
            List<CallcenterCallRecord> list = callcenterCallRecordService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            List<String> hisIdList = list.stream().map(CallcenterCallRecord::getUuid).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(hisIdList)) {
                List<Callhistory> historyList = iCallhistoryService.listByIds(hisIdList);
                if (CollUtil.isNotEmpty(historyList)) {
                    Map<String, String> historyMap = historyList.stream().filter(h -> StrUtil.isNotBlank(h.getCallid()))
                            .collect(Collectors.toMap(Callhistory::getCallhisid, Callhistory::getCallid));
                    if (MapUtil.isNotEmpty(historyMap)) {
                        for (CallcenterCallRecord record : list) {
                            try {
                                String fileName = "houyu/" + record.getUuid() + ".mp3";
                                File file = new File(path + fileName);
                                if (!file.exists()) {
                                    if (!historyMap.containsKey(record.getUuid())) {
                                        throw new AiServerException(ResultCode.DB_DATA_ERROR.getCode(), "未获取到当前通话记录对应的callId");
                                    }
                                    String originUrl = String.format(
                                            "http://10.135.11.251:30077/query/recordings/open/call/%s/stream",
                                            historyMap.get(record.getUuid()));
                                    ResponseEntity<byte[]> responseEntity = restTemplateVoice.getForEntity(originUrl, byte[].class);
                                    FileUtil.byteToFileCheckExist(responseEntity.getBody(), path, fileName, fileName);
                                }

                                LambdaUpdateWrapper<CallcenterCallRecord> updateWrapper = new LambdaUpdateWrapper<>();
                                updateWrapper.set(CallcenterCallRecord::getRecordUrl, nasUrl + fileName)
                                        .eq(CallcenterCallRecord::getId, record.getId());
                                callcenterCallRecordService.update(updateWrapper);
                            } catch (Exception e) {
                                log.error("同步厚予通话记录recordId={}音频文件发生异常，异常原因为", record.getId(), e);
                            }
                        }
                    }
                }
            }

            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }
        log.info("同步厚予通话音频文件结束");
    }

    @Override
    public void syncRecord() {
        log.info("同步厚予通话记录开始");
        Long startId = null;
        while (true) {
            LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
            if (Objects.nonNull(startId)) {
                wrapper.gt(CallcenterCallRecord::getId, startId);
            }
            wrapper.eq(CallcenterCallRecord::getSystemType, 3)
                    .eq(CallcenterCallRecord::getAnswered, 2)
                    .isNotNull(CallcenterCallRecord::getCallPlannerId).apply("trim(call_planner_id) != ''")
                    .isNotNull(CallcenterCallRecord::getCustomerUid).apply("trim(customer_uid) != ''")
                    .isNotNull(CallcenterCallRecord::getPhone).apply("trim(phone) != ''")
                    .isNotNull(CallcenterCallRecord::getCsBillsec).gt(CallcenterCallRecord::getCsBillsec, 0L)
                    .isNotNull(CallcenterCallRecord::getRecordUrl).apply("TRIM(record_url) != ''")
                    .orderByAsc(CallcenterCallRecord::getId).last("LIMIT 100");
            List<CallcenterCallRecord> list = callcenterCallRecordService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            List<CallcenterCallRecord> handleList = new ArrayList<>(list);
            List<String> hisIdList = list.stream().map(CallcenterCallRecord::getUuid)
                    .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(hisIdList)) {
                LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(AiPlannerChatContact::getBusId)
                        .in(AiPlannerChatContact::getBusId, hisIdList)
                        .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.HOUYU.getType());
                List<AiPlannerChatContact> chatList = iAiPlannerChatContactService.list(queryWrapper);
                if (CollUtil.isNotEmpty(chatList)) {
                    List<String> busIdList = chatList.stream().map(AiPlannerChatContact::getBusId).distinct().collect(Collectors.toList());
                    handleList = handleList.stream().filter(c -> !busIdList.contains(c.getUuid())).collect(Collectors.toList());
                }
            }

            if (CollUtil.isNotEmpty(handleList)) {
                Map<String, Set<String>> plannerUserMap = handleList.stream().collect(Collectors.groupingBy(CallcenterCallRecord::getCallPlannerId,
                        Collectors.mapping(CallcenterCallRecord::getCustomerUid, Collectors.toSet())));
                for (Map.Entry<String, Set<String>> entry : plannerUserMap.entrySet()) {
                    LambdaQueryWrapper<AiPlannerTopUser> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(AiPlannerTopUser::getPlannerNo, entry.getKey())
                            .in(AiPlannerTopUser::getUserId, entry.getValue())
                            .eq(AiPlannerTopUser::getTenantId, handleList.get(0).getTenantId());
                    List<AiPlannerTopUser> topUserList = iAiPlannerTopUserService.list(queryWrapper);
                    if (CollUtil.isNotEmpty(topUserList)) {
                        Set<String> userIdSet = topUserList.stream().map(AiPlannerTopUser::getUserId).collect(Collectors.toSet());
                        entry.getValue().removeAll(userIdSet);
                        plannerUserMap.put(entry.getKey(), entry.getValue());
                    }
                }
                plannerUserMap = plannerUserMap.entrySet().stream().filter(a -> CollUtil.isNotEmpty(a.getValue()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                if (MapUtil.isNotEmpty(plannerUserMap)) {
                    List<CallcenterCallRecord> recordList = handleList.stream()
                            .sorted(Comparator.comparing(CallcenterCallRecord::getAgentName, Comparator.nullsLast(Comparator.reverseOrder()))
                                    .thenComparing(CallcenterCallRecord::getCustomerName, Comparator.nullsLast(Comparator.reverseOrder())))
                            .collect(Collectors.toList());
                    Map<String, Map<String, CallcenterCallRecord>> callRecordMap = recordList.stream()
                            .collect(Collectors.groupingBy(CallcenterCallRecord::getCallPlannerId,
                                    Collectors.toMap(CallcenterCallRecord::getCustomerUid, Function.identity(), (existing, replacement) -> existing)));
                    for (Map.Entry<String, Set<String>> entry : plannerUserMap.entrySet()) {
                        for (String userId : entry.getValue()) {
                            if (callRecordMap.containsKey(entry.getKey()) && callRecordMap.get(entry.getKey()).containsKey(userId)) {
                                CallcenterCallRecord record = callRecordMap.get(entry.getKey()).get(userId);
                                AiPlannerTopUser topUser = new AiPlannerTopUser();
                                topUser.setPlannerNo(record.getCallPlannerId());
                                topUser.setPlannerName(record.getAgentName());
                                topUser.setUserId(record.getCustomerUid());
                                topUser.setUserName(record.getCustomerName());
                                topUser.setTenantId(record.getTenantId());
                                iAiPlannerTopUserService.save(topUser);
                            }
                        }
                    }
                }

                for (CallcenterCallRecord record : handleList) {
                    try {
                        if (Objects.isNull(record.getCsAnswerStamp()) || StrUtil.isBlank(record.getTenantId())) {
                            continue;
                        }
                        AiPlannerChatContact chatContact = new AiPlannerChatContact();
                        chatContact.setPlannerNo(record.getCallPlannerId());
                        chatContact.setUserId(record.getCustomerUid());
                        chatContact.setDirection(Integer.valueOf(1).equals(record.getCallType()) ?
                                MsgSendDirectionEnum.FROM_CUS.getCode() : MsgSendDirectionEnum.FROM_CRM.getCode());
                        chatContact.setOriginContent(record.getRecordUrl());
                        chatContact.setNasPath(record.getRecordUrl().replaceFirst(nasUrl, path));
                        chatContact.setOriginType(QwMsgTypeEnum.HOUYU.getType());
                        chatContact.setPhone(record.getPhone());
                        chatContact.setMsgTime(record.getCsAnswerStamp());
                        chatContact.setBusId(record.getUuid());
                        chatContact.setAvayaId(String.valueOf(record.getId()));
                        chatContact.setLongTime(record.getCsBillsec());
                        chatContact.setTenantId(record.getTenantId());
                        if (chatContact.getLongTime() < 1) {
                            chatContact.setProcessedContent("[厚予语音]");
                        }
                        iAiPlannerChatContactService.save(chatContact);
                    } catch (Exception e) {
                        log.error("同步厚予通话记录recordId={}发生异常，异常原因为", record.getId(), e);
                    }
                }
            }

            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }
        log.info("同步厚予通话记录结束");
    }

    @Override
    public void translateVideo() {
        log.info("厚予历史音频音转文开始");

        AiPromptRecordVo deNoiseRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.HOUYU_DENOISE.getCode());
        AiPromptRecordVo translateRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.HOUYU_VOICE_TO_TEXT.getCode());
        AiPromptRecordVo searchRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.HOUYU_VOICE_SEARCH.getCode());

        SendRobotReq.Content content = new SendRobotReq.Content();
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");

        Long startId = null;
        String cache = jedisCluster.get(KEY_HOUYU_TRANSLATE_START_ID);
        log.info("厚予历史音频音转文任务当前开始处理的消息id为{}", startId);
        if (StrUtil.isNotBlank(cache)) {
            startId = Long.parseLong(cache);
        }
        AtomicReference<Long> startIdNext = new AtomicReference<>(null);
        while (true) {
            LocalTime now = LocalTime.now();
            if (now.isAfter(LocalTime.of(3, 30, 0)) && now.isBefore(LocalTime.of(17, 0, 0))) {
                log.info("厚予历史音频音转文任务执行结束, 主线程执行时间已到");
                break;
            }

            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            if (Objects.nonNull(startId)) {
                wrapper.gt(AiPlannerChatContact::getId, startId);
            }
            wrapper.eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.HOUYU.getType())
                    .isNotNull(AiPlannerChatContact::getNasPath).apply("TRIM(nas_path) != ''")
                    .and(w -> w.isNull(AiPlannerChatContact::getProcessedContent).or().apply("TRIM(processed_content) = ''"))
                    .orderByAsc(AiPlannerChatContact::getId).last("LIMIT 100");
            List<AiPlannerChatContact> list = iAiPlannerChatContactService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            CountDownLatch latch = new CountDownLatch(list.size());
            Map<Long, Future<?>> futures = new HashMap<>();
            for (AiPlannerChatContact contact : list) {
                Long finalStartId = startId;
                futures.put(contact.getId(), avayaAddThreadPoolExecutor.submit(() -> {
                    try {
                        LocalTime nowTime = LocalTime.now();
                        if (nowTime.isAfter(LocalTime.of(3, 30, 0)) && nowTime.isBefore(LocalTime.of(17, 0, 0))) {
                            log.info("厚予历史音频音转文任务执行结束, 异步线程执行时间已到");
                            if (Objects.isNull(startIdNext.get()) || startIdNext.get().compareTo(finalStartId) > 0) {
                                startIdNext.set(finalStartId);
                            }
                            return;
                        }

                        String text = avayaVoiceService.getCrmWithVoiceWithDesignatedPlanner(contact, null, deNoiseRecord, translateRecord, searchRecord);
                        if ("[厚予语音]".equals(text)) {
                            LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                            wrapperText.set(AiPlannerChatContact::getProcessingContent, text)
                                    .set(AiPlannerChatContact::getProcessedContent, text)
                                    .eq(AiPlannerChatContact::getId, contact.getId());
                            iAiPlannerChatContactService.update(wrapperText);
                        }
                        aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(contact.getId()).build());
                    } catch (Exception e) {
                        log.error("厚予历史音频[id = {}]音转文发生异常，异常原因为", contact.getId(), e);
                        throw e;
                    } finally {
                        latch.countDown();
                    }
                }));
            }
            try {
                latch.await();
            } catch (Exception e) {
                log.error("主线程等待异常，异常原因为", e);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "主线程等待异常");
            }

            Map<Long, String> err = new HashMap<>();
            for (Map.Entry<Long, Future<?>> future : futures.entrySet()) {
                try {
                    future.getValue().get();
                } catch (Exception e) {
                    Throwable cause = e.getCause();
                    if (cause instanceof AiServerException) {
                        err.put(future.getKey(), cause.getMessage());
                    }
                }
            }
            if (MapUtil.isNotEmpty(err)) {
                content.setContent(
                        "厚予历史音频音转文任务：当前批次中" + err.size() + "条语料处理失败，详细失败信息如下：\n" +
                                err.entrySet().stream().map(e -> e.getKey() + "    " + e.getValue()).collect(Collectors.joining("\n")));
                try {
                    HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                } catch (Exception e) {
                    log.error("发送企微群机器人通知失败", e);
                }
            }

            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }

        if (Objects.isNull(startIdNext.get())) {
            startIdNext.set(startId);
        }
        if (Objects.nonNull(startIdNext.get())) {
            jedisCluster.setex(KEY_HOUYU_TRANSLATE_START_ID, 3 * 24 * 60 * 60, String.valueOf(startIdNext.get()));
        }
        content.setContent("厚予历史音频音转文任务执行结束，下一次任务执行时开始处理的消息id为" + startIdNext.get());
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }
        log.info("厚予历史音频音转文结束");
    }
}
