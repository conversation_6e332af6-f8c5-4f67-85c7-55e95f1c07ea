package com.yirendai.voiceaiserver.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiModelType;
import com.yirendai.voiceaiserver.enums.ChatModeEnum;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.vo.request.ChatApiReq;
import com.yirendai.voiceaiserver.vo.request.ChatContentApiReq;
import com.yirendai.voiceaiserver.vo.request.ChatMessagesReq;
import com.yirendai.voiceaiserver.vo.response.ChatApiRes;
import com.yirendai.voiceaiserver.vo.response.ChatMessagesBlockRes;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.text.TextContentRenderer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
@RefreshScope
public class ChatServiceImpl implements ChatService {

    /**
     * 生成问题聊天信息
     */
    public static final String CHAT_GET_QUESTION_MSG = "请生成%s条关于%s的问题，生成结果不要包含人名";

    /**
     * 生成问题聊天信息
     */
    public static final String CHAT_GET_ANSWER_MSG =
            "%s请对此问题生成一个口语化的答案，在称呼对方时要用姓氏+哥、姐、叔叔、阿姨等等称呼对方，不要返回人名；" +
                    "整个答案要尽可能口语化一些，如果内容较多，请分段说明，要能直观地展示出主要内容。";

    /**
     * AIGC内容专用模型系统提示词
     */
    public static final String AIGC_CONTENT_MODEL_PROMPT = "You are a helpful assistant";

    /**
     * 十以内汉字
     */
    public static final String[] CHINESE_NUMBERS = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"
    };

    public static final String ASSISTANT = "你性格幽默、善解人意，擅长以轻松的朋友间对话方式交流。你会适时引入话题，推动对话发展。对用户问题，你能准确理解并关注其背后情感和处境，展现深度共情，恰当时引导倾诉，提供针对性建议。你生活态度积极，兴趣广泛，乐于探讨主流价值观认可的话题，并分享个人经历。作为全能型人才，你知识渊博，能浅显易懂地解答各类问题，包括撰写文案、编写代码、作文以及解答数学题，根据用户需求提供直接帮助。";
    public static final String ASSISTANT_JUDGE = "你是一个判断模型，你需要判断某个问题的类别是否符合候选类别之一。若符合，请输出命中的候选类别和提问的关键内容，格式为<命中的候选类别>：<提问的关键内容>；若全部不符合，请输出\"不符合\"。";

    public static final String MODEL_PLUS = "qwen-plus";
    public static final String MODEL_TURBO = "qwen-turbo";

    @Value("${chat.url}")
    private String baseUrl;
    @Value("${chat.key.question}")
    private String questionKey;
    @Value("${chat-api.url}")
    private String chatApiBaseUrl;
    @Value("${chat-api.key}")
    private String chatApiKey;
    @Value("${chat-api.chat.url}")
    private String chatApiChatUrl;

    // 临时-模型KEY
    @Value("${chat-api.aigc.key}")
    private String chatApiAiGcKey;
    // 临时-模型URL
    @Value("${chat-api.aigc.url}")
    private String chatApiAiGcUrl;

    // 临时-普通模型
    @Value("${chat-api.aigc.model}")
    private String chatApiAiGcModel;

    // 临时-0-通用模型 1-对话模型
    @Value("${chat-api.aigc.type}")
    private Integer chatApiAiGcType;

    // 临时-核采样
    @Value("${chat-api.aigc.topP}")
    private Float chatApiAiGcTopP;

    // 临时-重复惩罚
    @Value("${chat-api.aigc.repetitionPenalty}")
    private Float chatApiAiGcRepetitionPenalty;

    @Resource
    RestTemplate restTemplateVoice;
    @Resource
    RestTemplate restTemplateConcurrent;

    @Override
    public List<String> chatQuestion(String content, Integer size, String userAccount) {
        ChatMessagesReq req = new ChatMessagesReq();
        req.setQuery(content);
        req.setResponse_mode(ChatModeEnum.BLOCKING.getValue());
        req.setUser(userAccount);
        log.info("调用Chat接口生成问题&答案 url={}, query={}", baseUrl + "/chat-messages", content);
        String response = HttpRequest.post(baseUrl + "/chat-messages").auth("Bearer " + questionKey)
                .body(JSONUtil.toJsonStr(req), ContentType.JSON.getValue()).execute().body();
        if (StringUtils.isBlank(response)) {
            log.info("调用Chat接口生成问题&答案返回结果为{}", response);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "问题&答案生成失败");
        }
        ChatMessagesBlockRes res = JSONUtil.toBean(response, ChatMessagesBlockRes.class);
        if (Objects.isNull(res) || StringUtils.isBlank(res.getAnswer())) {
            log.info("调用Chat接口生成问题&答案返回结果为{}", response);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "问题&答案生成失败");
        }

        // 创建一个Markdown解析器
        Parser parser = Parser.builder().build();
        Node document = parser.parse(res.getAnswer());
        // 创建一个渲染器，用于生成纯文本
        TextContentRenderer renderer = TextContentRenderer.builder().build();
        // 渲染并输出纯文本
        String answer = renderer.render(document);

        if (size == 1) {
            return Collections.singletonList(answer);
        }
        String[] generateArr = answer.split("\n");
        List<String> generateList = Arrays.stream(generateArr).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (generateList.size() < size) {
            log.info("调用Chat接口生成问题&答案返回结果为{}", response);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "问题&答案生成失败");
        } else if (generateList.size() > size) {
            generateList = generateList.stream().filter(s -> s.matches("^(\\d+\\. |[一二三四五六七八九十百千万亿]+[、]).*"))
                    .distinct().collect(Collectors.toList());
            if (generateList.size() != size) {
                log.info("调用Chat接口生成问题&答案返回结果为{}", response);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "问题&答案生成失败");
            }
        }
        generateList = generateList.stream().map(s -> s.replaceAll("^(\\d+\\. |[一二三四五六七八九十百千万亿]+[、])", " "))
                .distinct().collect(Collectors.toList());
        return generateList;
    }

    public String chat(String question, String model, Float temperature) {
        ChatApiReq chatApiReq = new ChatApiReq(ASSISTANT);
        ChatApiReq.Message message = new ChatApiReq.Message();
        message.setRole("user");
        message.setContent(question);
        chatApiReq.setModel(model);
        chatApiReq.getMessages().add(message);
        chatApiReq.setTemperature(Objects.isNull(temperature) ? Float.valueOf("0.8") : temperature);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", chatApiKey);
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(chatApiReq), headers);
        ResponseEntity<String> response = null;
        log.info("chatText 入参：{}", JSON.toJSONString(chatApiReq));
        try {
            response = restTemplateConcurrent.postForEntity(chatApiBaseUrl + chatApiChatUrl, requestEntity, String.class);
        } catch (HttpServerErrorException e) {
            log.error("chatText HttpServerError error", e);
            int statusCode = e.getRawStatusCode();
            if (statusCode == HttpStatus.BAD_GATEWAY.value()) {
                try {
                    Thread.sleep(1000L * 60 * 5);
                } catch (InterruptedException ex) {
                    log.error("chatText sleep error", e);
                }
            }
        } catch (Exception e) {
            log.error("chatText error", e);

        } finally {
            log.info("chatText 出参：{}", JSON.toJSONString(response));
        }
        if (Objects.nonNull(response)) {
            return response.getBody();
        }
        return null;
    }

    @Override
    public String chatAiContent(String question, String model, Float temperature, AiModelType type) {
        return chatAiContent(question, model, temperature, type, null);
    }

    @Override
    public String chatAiContent(String question, String model, Float temperature, AiModelType type, String systemPrompt) {
        ChatContentApiReq chatApiReq;
        String apiKey = chatApiAiGcKey;
        
        // 如果传入了systemPrompt，优先使用；否则使用默认系统提示词
        String finalSystemPrompt;
        if (StringUtils.isNotBlank(systemPrompt)) {
            finalSystemPrompt = systemPrompt;
        } else if (type == AiModelType.LARGE_MODEL) {
            finalSystemPrompt = ASSISTANT;
            model = chatApiAiGcModel;
        } else if (type == AiModelType.SUMMARY_DISTILLATION_MODEL) {
            finalSystemPrompt = AIGC_CONTENT_MODEL_PROMPT;
        } else if (type == AiModelType.TAG_DISTILLATION_MODEL) {
            finalSystemPrompt = ASSISTANT;
        } else {
            finalSystemPrompt = ASSISTANT;
        }
        
        chatApiReq = new ChatContentApiReq(finalSystemPrompt);
        
        if (type == AiModelType.SUMMARY_DISTILLATION_MODEL || type == AiModelType.TAG_DISTILLATION_MODEL) {
            chatApiReq.setTop_p(chatApiAiGcTopP);
            chatApiReq.setRepetition_penalty(chatApiAiGcRepetitionPenalty);
        }
        
        ChatContentApiReq.Message message = new ChatContentApiReq.Message();
        message.setRole("user");
        message.setContent(question);
        chatApiReq.setModel(model);
        chatApiReq.getMessages().add(message);
        chatApiReq.setTemperature(Objects.isNull(temperature) ? Float.valueOf("0.8") : temperature);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        headers.add("Authorization", apiKey);
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(chatApiReq, SerializerFeature.SkipTransientField), headers);
        ResponseEntity<String> response = null;
        String reqUrl;
        log.info("chatAiContent {} 入参：{}", type.getDesc(), JSON.toJSONString(chatApiReq));
        reqUrl = chatApiAiGcUrl + chatApiChatUrl;
        try {
            response = restTemplateConcurrent.postForEntity(reqUrl, requestEntity, String.class);
        } catch (HttpServerErrorException e) {
            log.error("chatAiContent HttpServerError error", e);
        } catch (Exception e) {
            log.error("chatAiContent error", e);

        } finally {
            log.info("chatAiContent {} 出参：{}", type.getDesc(), JSON.toJSONString(response));
        }
        if (Objects.nonNull(response)) {
            return response.getBody();
        }
        return null;
    }

    @Override
    @Async("commonThreadPoolExecutor")
    public CompletableFuture<String> chatAsync(String content, Float temperature) {
        String chatRes = chat(content, ChatServiceImpl.MODEL_PLUS, temperature);
        if (StringUtils.isBlank(chatRes)) {
            throw new AiServerException(ResultCode.FAILURE);
        }
        ChatApiRes chatApiRes = JSON.parseObject(chatRes, ChatApiRes.class);
        if (Objects.isNull(chatApiRes) || CollectionUtils.isEmpty(chatApiRes.getChoices())
                || Objects.isNull(chatApiRes.getChoices().get(0).getMessage())
                || StringUtils.isBlank(chatApiRes.getChoices().get(0).getMessage().getContent())) {
            throw new AiServerException(ResultCode.FAILURE);
        }
        return CompletableFuture.completedFuture(chatApiRes.getChoices().get(0).getMessage().getContent());
    }

    @Override
    public String chatJudge(String question, String model, Float temperature) {
        ChatApiReq chatApiReq = new ChatApiReq(ASSISTANT_JUDGE);
        ChatApiReq.Message message = new ChatApiReq.Message();
        message.setRole("user");
        message.setContent(question);
        chatApiReq.setModel(StringUtils.isBlank(model) ? MODEL_PLUS : model);
        chatApiReq.getMessages().add(message);
        chatApiReq.setTemperature(Objects.isNull(temperature) ? Float.valueOf("0.2") : temperature);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", chatApiKey);
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(chatApiReq), headers);
        ResponseEntity<String> response = null;
        log.info("chatJudge 入参：{}", JSON.toJSONString(chatApiReq));
        try {
            response = restTemplateVoice.postForEntity(chatApiBaseUrl + chatApiChatUrl, requestEntity, String.class);
        } catch (Exception e) {
            log.error("chatJudge error", e);
        } finally {
            log.info("chatJudge 出参：{}", JSON.toJSONString(response));
        }
        if (Objects.nonNull(response)) {
            ChatApiRes chatApiRes = JSON.parseObject(response.getBody(), ChatApiRes.class);
            if (Objects.isNull(chatApiRes) || CollectionUtils.isEmpty(chatApiRes.getChoices())
                    || Objects.isNull(chatApiRes.getChoices().get(0).getMessage())
                    || StringUtils.isBlank(chatApiRes.getChoices().get(0).getMessage().getContent())) {
                return null;
            }
            return chatApiRes.getChoices().get(0).getMessage().getContent();
        }
        return null;
    }
}
