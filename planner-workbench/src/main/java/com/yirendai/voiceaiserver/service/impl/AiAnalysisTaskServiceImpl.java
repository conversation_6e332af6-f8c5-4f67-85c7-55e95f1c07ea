package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.enums.AiAnalysisTaskSourceEnum;
import com.yirendai.voiceaiserver.service.IAiAnalysisTaskDataService;
import com.yirendai.voiceaiserver.service.IAiAnalysisTaskService;
import com.yirendai.voiceaiserver.vo.request.AiAnalysisTaskReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskSceneCountVO;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskSceneResultCountVO;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskVO;
import com.yirendai.workbench.entity.AiAnalysisTask;
import com.yirendai.workbench.mapper.AiAnalysisTaskCallRecordMapper;
import com.yirendai.workbench.mapper.AiAnalysisTaskMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.mapper.AiAnalysisTaskResultMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * ai分析任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
public class AiAnalysisTaskServiceImpl extends ServiceImpl<AiAnalysisTaskMapper, AiAnalysisTask> implements IAiAnalysisTaskService {

    @Resource
    private AiAnalysisTaskResultMapper aiAnalysisTaskResultMapper;

    @Resource
    private AiAnalysisTaskCallRecordMapper aiAnalysisTaskCallRecordMapper;

    @Resource
    private IAiAnalysisTaskDataService aiAnalysisTaskDataService;
    /**
     * 根据id和用户id查询
     * @param aiAnalysisTaskReq
     * @return
     */
    @Override
    public AiAnalysisTaskVO getByIdAndUserId(AiAnalysisTaskReq aiAnalysisTaskReq) {
        AiAnalysisTask aiAnalysisTask = baseMapper.selectByIdAndUserId(aiAnalysisTaskReq);
        if(aiAnalysisTask != null){
            AiAnalysisTaskVO aiAnalysisTaskVO = new AiAnalysisTaskVO();
            if (aiAnalysisTask.getAnalysisType() == 1) {
                int count = aiAnalysisTaskDataService.selectUserCountByTaskId(aiAnalysisTask.getId());
                aiAnalysisTaskVO.setTaskCount(count);
            } else {
                int count = aiAnalysisTaskCallRecordMapper.selectCountByTaskId(aiAnalysisTask.getId());
                aiAnalysisTaskVO.setTaskCount( count);
            }

            BeanUtil.copyProperties(aiAnalysisTask, aiAnalysisTaskVO);

            // 查询结果数据
            List<AiAnalysisTaskSceneCountVO> aiAnalysisTaskSceneCountVOList = aiAnalysisTaskResultMapper.selectSceneCount(aiAnalysisTask.getId());
            if(CollectionUtil.isNotEmpty(aiAnalysisTaskSceneCountVOList)){
                aiAnalysisTaskVO.setSceneCountVOList(aiAnalysisTaskSceneCountVOList);
                for (AiAnalysisTaskSceneCountVO aiAnalysisTaskSceneCountVO : aiAnalysisTaskSceneCountVOList) {
                    aiAnalysisTaskSceneCountVO.setName(aiAnalysisTaskSceneCountVO.getSceneName());
                    aiAnalysisTaskSceneCountVO.setIdStr("scene_"+aiAnalysisTaskSceneCountVO.getId());
                    List<AiAnalysisTaskSceneResultCountVO> resultCountVOList = aiAnalysisTaskResultMapper.selectSceneResultCount(aiAnalysisTask.getId(),aiAnalysisTaskSceneCountVO.getId());
                    for(AiAnalysisTaskSceneResultCountVO aiAnalysisTaskSceneResultCountVO : resultCountVOList){
                        aiAnalysisTaskSceneResultCountVO.setName(aiAnalysisTaskSceneResultCountVO.getResultName());
                        if (aiAnalysisTask.getAnalysisType() == 1) {
                            aiAnalysisTaskSceneResultCountVO.setResultName(aiAnalysisTaskSceneResultCountVO.getResultName());
                        } else {
                            aiAnalysisTaskSceneResultCountVO.setResultName(aiAnalysisTaskSceneCountVO.getSceneName()+"-"+aiAnalysisTaskSceneResultCountVO.getResultName());
                        }
                        aiAnalysisTaskSceneResultCountVO.setIdStr("result_"+aiAnalysisTaskSceneResultCountVO.getId());
                    }
                    aiAnalysisTaskSceneCountVO.setResultCountVOList(resultCountVOList);
                }
            }
            return aiAnalysisTaskVO;
        }else{
            return null;
        }
    }

    /**
     * 分页查询
     * @param page
     * @param aiAnalysisTaskReq
     * @return
     */
    @Override
    public IPage<AiAnalysisTaskVO> aiAnalysisTaskPageList(IPage<AiAnalysisTaskVO> page, AiAnalysisTaskReq aiAnalysisTaskReq) {
        IPage<AiAnalysisTaskVO> pages = baseMapper.aiAnalysisTaskPageList(page, aiAnalysisTaskReq);
        if (pages != null && pages.getRecords() != null && !pages.getRecords().isEmpty()){
            for (AiAnalysisTaskVO vo : pages.getRecords()) {
                if (vo.getSourcePage() == null){
                    vo.setSourcePageDesc("未知");
                    continue;
                }
                vo.setSourcePageDesc(AiAnalysisTaskSourceEnum.getName(vo.getSourcePage()));
            }
        }
        return pages;
    }
}
