package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.enums.AiConversationTagTypeEnum;
import com.yirendai.voiceaiserver.service.AiConversationWechatTagService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationTagExportDto;
import com.yirendai.workbench.entity.AiConversationTag;
import com.yirendai.workbench.entity.AiConversationWechatSummary;
import com.yirendai.workbench.entity.AiConversationWechatTag;
import com.yirendai.workbench.mapper.AiConversationTagMapper;
import com.yirendai.workbench.mapper.AiConversationWechatTagMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_wechat_tag(ai企微对话与标签关联表)】的数据库操作Service实现
* @createDate 2024-12-20 11:28:41
*/
@Service
public class AiConversationWechatTagServiceImpl extends ServiceImpl<AiConversationWechatTagMapper, AiConversationWechatTag>
implements AiConversationWechatTagService {


    @Resource
    private AiConversationWechatTagMapper aiConversationWechatTagMapper;

    @Override
    public void insertAiConversationTag(Long wechatSummaryId, String plannerNo, String userId, String tenantId, String intervalTag, Long tagCategoryId, Long tagId, String category, String name, AiConversationTagTypeEnum matchStatus) {
        AiConversationWechatTag aiConversationWechatTag = new AiConversationWechatTag();
        aiConversationWechatTag.setWechatSummaryId(wechatSummaryId);
        aiConversationWechatTag.setPlannerNo(plannerNo);
        aiConversationWechatTag.setUserId(userId);
        aiConversationWechatTag.setIntervalTag(intervalTag);
        aiConversationWechatTag.setTenantId(tenantId);
        if (tagCategoryId != null && tagId != null) {
            aiConversationWechatTag.setTagId(tagId);
            aiConversationWechatTag.setTagCategoryId(tagCategoryId);
        }
        aiConversationWechatTag.setTagName(name);
        aiConversationWechatTag.setTagCategoryName(category);
        aiConversationWechatTag.setMatchStatus(matchStatus.getCode());
        aiConversationWechatTag.setCreateTime(LocalDateTime.now());
        aiConversationWechatTag.setUpdateTime(LocalDateTime.now());
        aiConversationWechatTagMapper.insert(aiConversationWechatTag);
    }

    @Override
    public void insertAiConversationTag(AiConversationWechatSummary aiConversationWechatSummary, Long tagCategoryId, Long tagId, String category, String name, AiConversationTagTypeEnum matchStatus) {
        insertAiConversationTag(aiConversationWechatSummary.getId(), aiConversationWechatSummary.getPlannerId(), aiConversationWechatSummary.getUserId(), aiConversationWechatSummary.getTenantId(), aiConversationWechatSummary.getIntervalTag(), tagCategoryId, tagId, category, name, matchStatus);
    }

    @Override
    public void deleteByChatContactId(Long wechatSummaryId) {
        LambdaQueryWrapper<AiConversationWechatTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationWechatTag::getWechatSummaryId, wechatSummaryId);
        aiConversationWechatTagMapper.delete(queryWrapper);
    }

}
