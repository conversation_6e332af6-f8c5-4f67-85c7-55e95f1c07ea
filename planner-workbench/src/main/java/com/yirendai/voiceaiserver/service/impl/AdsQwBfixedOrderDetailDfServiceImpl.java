package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.workbench.mapper.AdsQwBfixedOrderDetailDfMapper;
import com.yirendai.workbench.entity.AdsQwBfixedOrderDetailDf;
import com.yirendai.voiceaiserver.service.AdsQwBfixedOrderDetailDfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 新客团队大额001佣金订单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
public class AdsQwBfixedOrderDetailDfServiceImpl extends ServiceImpl<AdsQwBfixedOrderDetailDfMapper, AdsQwBfixedOrderDetailDf> implements AdsQwBfixedOrderDetailDfService {

    @Override
    public List<AdsQwBfixedOrderDetailDf> getPlannerAchievements() {
        return baseMapper.getPlannerAchievements();
    }

    @Override
    public List<AdsQwBfixedOrderDetailDf> getPlannerUsers() {
        return baseMapper.getPlannerUsers();
    }
}
