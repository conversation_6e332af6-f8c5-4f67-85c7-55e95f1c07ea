package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.workbench.mapper.AdsCsgSmallFixedAchievementDfMapper;
import com.yirendai.workbench.entity.AdsCsgSmallFixedAchievementDf;
import com.yirendai.voiceaiserver.service.AdsCsgSmallFixedAchievementDfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 业绩达成-类固收相关-小额001 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
public class AdsCsgSmallFixedAchievementDfServiceImpl extends ServiceImpl<AdsCsgSmallFixedAchievementDfMapper, AdsCsgSmallFixedAchievementDf> implements AdsCsgSmallFixedAchievementDfService {

    @Override
    public List<AdsCsgSmallFixedAchievementDf> getPlannerAchievements() {
        return baseMapper.getPlannerAchievements();
    }

    @Override
    public List<AdsCsgSmallFixedAchievementDf> getPlannerUsers() {
        return baseMapper.getPlannerUsers();
    }
}
