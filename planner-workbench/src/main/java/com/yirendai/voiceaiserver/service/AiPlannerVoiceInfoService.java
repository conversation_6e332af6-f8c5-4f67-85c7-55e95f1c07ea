package com.yirendai.voiceaiserver.service;

import com.yirendai.workbench.entity.AiPlannerVoiceInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 理财师声纹信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface AiPlannerVoiceInfoService extends IService<AiPlannerVoiceInfo> {

    void saveOrUpdate(String plannerNo, MultipartFile file, String tenantId);

    String check();
}
