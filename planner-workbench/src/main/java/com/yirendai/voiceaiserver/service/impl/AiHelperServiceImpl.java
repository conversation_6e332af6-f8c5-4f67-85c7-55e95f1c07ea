package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.voiceaiserver.biz.aihelper.AiHelperFactory;
import com.yirendai.voiceaiserver.biz.aihelper.AiHelperHandlerBasic;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.config.ZhiyuConfiguration;
import com.yirendai.voiceaiserver.enums.AISceneEnum;
import com.yirendai.voiceaiserver.enums.RatingEnum;
import com.yirendai.voiceaiserver.service.AiHelperService;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.tripartite.zhiyu.ZhiYuApiService;
import com.yirendai.voiceaiserver.vo.request.AiHelperMessageReq;
import com.yirendai.voiceaiserver.vo.response.WelComeVO;
import com.yirendai.workbench.entity.AiHelperMessage;
import com.yirendai.workbench.entity.AiHelperOpeningRemarks;
import com.yirendai.workbench.mapper.AiHelperMessageMapper;
import com.yirendai.workbench.mapper.AiHelperOpeningRemarksMapper;
import com.yirendai.workbench.mapper.AiRecommendedQuestionMapper;
import com.yirendai.workbench.util.OwnAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * AI 智能体助手
 **/
@Service
@Slf4j
@RefreshScope
public class AiHelperServiceImpl implements AiHelperService {
    @Autowired
    private ZhiyuConfiguration zhiyuConfiguration;

    @Autowired
    private ZhiYuApiService zhiYuApiService;
    @Resource
    AiHelperMessageMapper aiHelperMessageMapper;

    @Resource
    AiHelperOpeningRemarksMapper aiHelperOpeningRemarksMapper;

    @Resource
    AiRecommendedQuestionMapper aiRecommendedQuestionMapper;


    @Resource
    protected AiPromptRecordService aiPromptRecordService;

    @Autowired
    AiHelperFactory aiHelperFactory;
    @Override
    public SseEmitter chatMessageStream(AiHelperMessageReq chatMessagesReq) {
        AiHelperHandlerBasic aiHelperHandlerBasic = aiHelperFactory.getHandler(chatMessagesReq.getScene());
        return aiHelperHandlerBasic.chatMessageStream(chatMessagesReq);

    }

    @Override
    public void saveMessage(AiHelperMessage aiHelperMessage) {
        aiHelperMessageMapper.insert(aiHelperMessage);
    }


    private String getAicsKey(Integer scene){
        String aiCsKey = zhiyuConfiguration.getAiCsKey();
        if(AISceneEnum.REAL_TIME_CALL_TRANSCRIPTION.getKey().equals(scene)){
            aiCsKey = zhiyuConfiguration.getAiCsKeyTts();
        }
        return aiCsKey;
    }
    /**
     * 停止响应
     * @param taskId
     * @return
     */
    @Override
    public R<String> stop(String taskId,String userId, Integer scene ){
        AiHelperHandlerBasic aiHelperHandlerBasic = aiHelperFactory.getHandler(scene);
        return aiHelperHandlerBasic.stop(taskId, userId );
    }
//    private String getText(Integer scene) {
//        AiPromptRecordVo latestPromptRecordByScene = null;
//        if(AISceneEnum.REAL_TIME_CALL_TRANSCRIPTION.getKey().equals(scene)){
//            latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.AI_HELPER_TTS.getCode());
//        }
//        if(latestPromptRecordByScene == null){
//            latestPromptRecordByScene = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.AI_HELPER.getCode());
//        }
//        String dateTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
//        try{
//            return String.format(latestPromptRecordByScene.getPrompt(), dateTime);
//        }catch (Exception e){
//            return "不要返回思考过程，现在的时间是：" + dateTime;
//        }
//    }
    /**
     * 点赞、点踩
     * @param bladeUser
     * @param rating 点赞 like, 点踩 dislike, 撤销点赞 0
     * @param messageId 消息ID
     * @return
     */
    @Override
    public R<String> rating(BladeUser bladeUser, String rating, String messageId){
        RatingEnum ratingEnum = RatingEnum.getByValue(rating);
        if(ratingEnum == null){
            log.info("点赞、点踩 参数错误 {} {}",rating,messageId);
            return R.fail("参数错误");
        }
        int count = aiHelperMessageMapper.updateRatingByMessageIdAndUserId(rating,messageId,String.valueOf(bladeUser.getUserId()));
        if(count > 0){
            return R.success(ratingEnum.getDescription()+"成功");
        }else{
            return R.fail(ratingEnum.getDescription()+"失败");
        }

    }
    /**
     * 获取欢迎语
     * @param bladeUser
     * @return
     */
    @Override
    public R<WelComeVO> getWelcome(BladeUser bladeUser){
        WelComeVO welComeVO = new WelComeVO();
        welComeVO.setUserName(OwnAuthUtil.getRealName());
        welComeVO.setWelcome("欢迎使用AI智能助手，请问您需要什么帮助？");
        welComeVO.setRecommendedQuestions(Arrays.asList(new String[] {"你能告诉我今日热点要闻吗？", "开场白"}));
        AiHelperOpeningRemarks aiHelperOpeningRemarks = aiHelperOpeningRemarksMapper.selectOneByTenantId(bladeUser.getTenantId());
        if(aiHelperOpeningRemarks != null){
            welComeVO.setWelcome(aiHelperOpeningRemarks.getTextContent());
            aiHelperOpeningRemarksMapper.updateUsageCount(aiHelperOpeningRemarks.getId());
        }
        List<String> recommendedQuestions = aiRecommendedQuestionMapper.selectRecommendedQuestions(bladeUser.getTenantId());
        if(CollectionUtil.isNotEmpty(recommendedQuestions)){
            welComeVO.setRecommendedQuestions(recommendedQuestions);
        }

        return R.data(welComeVO);
    }

    /**
     * 获取下一轮建议问题列表
     * @param messageId
     * @return
     */
    @Override
    public R<List<String>> suggested(String messageId,String userId){
        R<List<String>> result = zhiYuApiService.suggested(messageId,userId);
        if(R.isSuccess(result)){
            return result;
        }else{
            List< String> suggested = new ArrayList<>();
            suggested.add("热点要闻");
            return R.data(suggested);
        }
    }

    /**
     * 根据会话ID删除聊天记录
     * @param conversationId
     * @param userId
     * @return
     */
    @Override
    public R<String> delete(String conversationId, String userId){
        int count = aiHelperMessageMapper.deleteByCconversationIdAndUserId(conversationId,userId);
        if(count > 0){
            return R.success("删除成功");

        }else{
            return R.fail("删除失败");
        }
    }

    /**
     * 分布查询历史消息记录
     * @param userId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public IPage<AiHelperMessage> listAiHelperMessage(Long userId, String startTime, String endTime, int pageNum, int pageSize){
        Page<AiHelperMessage> page = new Page<>(pageNum,pageSize);

        LambdaQueryWrapper<AiHelperMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(  AiHelperMessage::getUserId, userId)
                .ge(StrUtil.isNotEmpty(startTime), AiHelperMessage::getCreateTime, startTime)
                .le(StrUtil.isNotEmpty(endTime), AiHelperMessage::getCreateTime, endTime)
                .eq(AiHelperMessage::getIsDeleted,0)
                .orderByDesc(AiHelperMessage::getCreateTime);

        return aiHelperMessageMapper.selectPage(page, queryWrapper);
    }
}
