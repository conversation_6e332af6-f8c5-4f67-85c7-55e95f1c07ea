package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.service.IAiAnalysisTaskDataService;
import com.yirendai.workbench.entity.AiAnalysisTaskData;
import com.yirendai.workbench.mapper.AiAnalysisTaskDataMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ai分析任务数据母表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class AiAnalysisTaskDataServiceImpl extends ServiceImpl<AiAnalysisTaskDataMapper, AiAnalysisTaskData> implements IAiAnalysisTaskDataService {



    @Override
    public int selectUserCountByTaskId(Long id) {
        if (id == null) {
            return 0;
        }
        Integer count = baseMapper.countDistinctCustomerByTaskId(id);
        return count == null ? 0 : count;
    }
}
