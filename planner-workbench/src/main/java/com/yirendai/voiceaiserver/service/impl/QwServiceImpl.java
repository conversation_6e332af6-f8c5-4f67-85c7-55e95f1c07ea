package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.mapper.linkwechat.WeCustomerMapper;
import com.yirendai.voiceaiserver.model.linkwechat.WeCustomer;
import com.yirendai.voiceaiserver.service.AiConversationWechatSummaryService;
import com.yirendai.voiceaiserver.service.QwService;
import com.yirendai.voiceaiserver.vo.request.*;
import com.yirendai.voiceaiserver.vo.response.*;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.config.ThirdAccessProperty;
import com.yirendai.workbench.entity.AiConversationWechatSummary;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.AiPlannerTopUser;
import com.yirendai.workbench.model.PlannerInfoDto;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import com.yirendai.workbench.service.IBladeUserService;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.util.DataScopeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QwServiceImpl implements QwService {

    @Value("${yr.tenant.id}")
    private String yrTenantId;

    @Value("${scrm.url}")
    private String scrmUrl;

    @Value("${scrm.planner.search.url:/financialPlanner/search}")
    private String scrmPlannerSearchUrl;

    @Resource
    IAiPlannerTopUserService iAiPlannerTopUserService;

    @Resource
    WeCustomerMapper weCustomerMapper;

    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;

    @Resource
    AiConversationWechatSummaryService aiConversationWechatSummaryService;

    @Resource
    ThirdAccessProperty thirdAccessProperty;

    @Resource
    DataScopeUtil dataScopeUtil;

    @Resource
    CallcenterProperty callcenterProperty;

    @Resource
    RestTemplate restTemplate;

    @Resource
    IBladeUserService iBladeUserService;

    @Override
    public CustomerInfoRes getUserInfo(String userId, String tenantId) {
        if (!yrTenantId.equals(tenantId)) {
            throw new AiServerException(ResultCode.CHANNEL_UNSUPPORTED_QUERY_CUSTOMER_INFO);
        }
        Map<String, YrUserVO> userMap = iAiPlannerTopUserService.getByUserIds(Collections.singletonList(userId));
        if (!userMap.containsKey(userId)) {
            throw new AiServerException(ResultCode.USER_NOT_EXIST);
        }
        if (StrUtil.isBlank(userMap.get(userId).getUnionid()) || StrUtil.isBlank(userMap.get(userId).getWxUserId())) {
            throw new AiServerException(ResultCode.USER_NO_QW);
        }

        LambdaQueryWrapper<WeCustomer> linkWechatWrapper = new LambdaQueryWrapper<>();
        linkWechatWrapper.eq(WeCustomer::getUnionid, userMap.get(userId).getUnionid())
                .eq(WeCustomer::getExternalUserid, userMap.get(userId).getWxUserId())
                .eq(WeCustomer::getDelFlag, 0)
                .groupBy(WeCustomer::getExternalUserid);
        WeCustomer weCustomer = weCustomerMapper.selectOne(linkWechatWrapper);
        if (Objects.isNull(weCustomer)) {
            throw new AiServerException(ResultCode.USER_NO_QW);
        }

        CustomerInfoRes res = new CustomerInfoRes();
        res.setTenantId(tenantId);
        res.setUserId(userId);
        res.setUserName(userMap.get(userId).getName());
        res.setUnionId(weCustomer.getUnionid());
        res.setExternalUserid(weCustomer.getExternalUserid());
        res.setCustomerName(weCustomer.getCustomerName());
        res.setCustomerType(weCustomer.getCustomerType());
        res.setAvatar(weCustomer.getAvatar());
        res.setRemarkName(weCustomer.getRemarkName());
        return res;
    }

    @Override
    public QwExistDateRes getExistDateList(String userId, String plannerNo, String tenantId, YearMonth yearMonth) {
        LambdaQueryWrapper<AiPlannerTopUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(AiPlannerTopUser::getUserId, userId)
                .eq(AiPlannerTopUser::getPlannerNo, plannerNo)
                .eq(AiPlannerTopUser::getTenantId, tenantId);
        AiPlannerTopUser user = iAiPlannerTopUserService.getOne(userWrapper);
        if (Objects.isNull(user)) {
            throw new AiServerException(ResultCode.NO_EXIST);
        }

        if (Objects.isNull(yearMonth)) {
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(AiPlannerChatContact::getMsgTime)
                    .eq(AiPlannerChatContact::getPlannerNo, plannerNo)
                    .eq(AiPlannerChatContact::getUserId, userId)
                    .eq(AiPlannerChatContact::getTenantId, tenantId)
                    .notIn(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.nonQwMsgTypeList)
                    .orderByDesc(AiPlannerChatContact::getMsgTime).last("limit 1");
            AiPlannerChatContact chatContact = iAiPlannerChatContactService.getOne(wrapper);
            if (Objects.isNull(chatContact)) {
                return QwExistDateRes.builder().dateList(Collections.emptyList()).build();
            }
            yearMonth = YearMonth.from(chatContact.getMsgTime());
        }
        List<LocalDate> res = iAiPlannerChatContactService.getWeChatTimeWithMonth(yearMonth.atDay(1).atStartOfDay(),
                yearMonth.plusMonths(1L).atDay(1).atStartOfDay(), plannerNo, userId, tenantId);
        return QwExistDateRes.builder().dateList(CollUtil.isEmpty(res) ? Collections.emptyList() : res).build();
    }

    @Override
    public Page<QwMsgAndSummaryVO> getPageMsgAndSummary(QwMsgAndSummaryReq request) {
        LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerChatContact::getPlannerNo, request.getPlannerNo())
                .eq(AiPlannerChatContact::getUserId, request.getUserId())
                .eq(AiPlannerChatContact::getTenantId, request.getTenantId())
                .notIn(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.nonQwMsgTypeList);
        if (Objects.nonNull(request.getEndTime())) {
            wrapper.lt(AiPlannerChatContact::getMsgTime, request.getEndTime())
                    .orderByDesc(AiPlannerChatContact::getMsgTime);
        } else {
            wrapper.ge(AiPlannerChatContact::getMsgTime, request.getStartTime())
                    .orderByAsc(AiPlannerChatContact::getMsgTime);
        }
        Page<AiPlannerChatContact> page = new Page<>(request.getCurrent(), request.getSize());
        Page<AiPlannerChatContact> list = iAiPlannerChatContactService.page(page, wrapper);

        Page<QwMsgAndSummaryVO> res = new Page<>();
        res.setTotal(list.getTotal());
        res.setPages(list.getPages());
        res.setCurrent(list.getCurrent());
        res.setSize(list.getSize());
        if (CollectionUtils.isEmpty(list.getRecords())) {
            res.setRecords(Collections.emptyList());
        } else {
            List<AiPlannerChatContact> chatList = list.getRecords().stream()
                    .sorted(Comparator.comparing(AiPlannerChatContact::getMsgTime)).collect(Collectors.toList());
            Map<String, AiConversationWechatSummary> map = new HashMap<>();

            if (Objects.isNull(request.getNeedSummary()) || Boolean.TRUE.equals(request.getNeedSummary())) {
                List<String> dateList = chatList.stream().map(c -> DateUtil.format(c.getMsgTime(), DatePattern.NORM_DATE_PATTERN))
                        .distinct().collect(Collectors.toList());
                if (Objects.nonNull(request.getEndTime()) || request.getCurrent() > 1) {
                    Page<AiPlannerChatContact> pagePre = new Page<>(
                            request.getCurrent() + (Objects.nonNull(request.getEndTime()) ? 1 : -1), request.getSize());
                    Page<AiPlannerChatContact> listPre = iAiPlannerChatContactService.page(pagePre, wrapper);
                    if (CollUtil.isNotEmpty(listPre.getRecords())) {
                        AiPlannerChatContact chatContact = listPre.getRecords()
                                .get(Objects.nonNull(request.getEndTime()) ? 0 : listPre.getRecords().size() - 1);
                        if (dateList.get(0).equals(DateUtil.format(chatContact.getMsgTime(), DatePattern.NORM_DATE_PATTERN))) {
                            dateList.remove(0);
                        }

                    }
                }
                if (CollUtil.isNotEmpty(dateList)) {
                    LambdaQueryWrapper<AiConversationWechatSummary> summaryWrapper = new LambdaQueryWrapper<>();
                    summaryWrapper.eq(AiConversationWechatSummary::getPlannerId, request.getPlannerNo())
                            .eq(AiConversationWechatSummary::getUserId, request.getUserId())
                            .in(AiConversationWechatSummary::getIntervalTag, dateList)
                            .eq(AiConversationWechatSummary::getTenantId, request.getTenantId())
                            .eq(AiConversationWechatSummary::getStatus, 1);
                    List<AiConversationWechatSummary> summaryList = aiConversationWechatSummaryService.list(summaryWrapper);
                    if (CollUtil.isNotEmpty(summaryList)) {
                        map = summaryList.stream().collect(Collectors.toMap(AiConversationWechatSummary::getIntervalTag, Function.identity()));
                    }
                }
            }

            List<QwMsgAndSummaryVO> dataList = new ArrayList<>();
            for (AiPlannerChatContact chatContact : chatList) {
                String tempDay = DateUtil.format(chatContact.getMsgTime(), DatePattern.NORM_DATE_PATTERN);
                if (map.containsKey(tempDay)) {
                    dataList.add(QwMsgAndSummaryVO.builder().summaryFlag(true).summary(map.get(tempDay)).build());
                    map.remove(tempDay);
                }
                dataList.add(QwMsgAndSummaryVO.builder().summaryFlag(false).msg(chatContact).build());
            }
            res.setRecords(dataList);
        }
        return res;
    }

    @Override
    public List<AiPlannerTopUser> getCustomerList(QwCusReq req) {
        QueryWrapper<AiPlannerTopUser> wrapper = new QueryWrapper<>();
        wrapper.select("user_id, max(user_name) as userName")
                .eq("tenant_id", req.getTenantId());
        if (StrUtil.isNotBlank(req.getUserId())) {
            wrapper.eq("user_id", req.getUserId());
        }
        if (StrUtil.isNotBlank(req.getStartUserId())) {
            wrapper.gt("user_id", req.getStartUserId());
        }
        if (StrUtil.isNotBlank(req.getUserName())) {
            wrapper.likeRight("user_name", req.getUserName());
        }
        wrapper.apply("LENGTH(TRIM(user_id)) != 0")
                .groupBy("user_id")
                .orderByAsc("user_id").last("limit " + req.getSize());
        return iAiPlannerTopUserService.list(wrapper);
    }

    @Override
    public Page<AiPlannerTopUser> getPagePlannerWithCustomer(QwPlannerReq req) {
        QueryWrapper<AiPlannerTopUser> wrapper = new QueryWrapper<>();
        wrapper.select("planner_no, max(planner_name) as plannerName");
        if (StrUtil.isNotBlank(req.getPlannerNo())) {
            wrapper.eq("planner_no", req.getPlannerNo());
        }
        wrapper.eq("user_id", req.getUserId()).eq("tenant_id", req.getTenantId());
        if (StrUtil.isNotBlank(req.getPlannerName())) {
            wrapper.likeRight("planner_name", req.getPlannerName());
        }
        wrapper.apply("length(trim(planner_no)) != 0")
                .isNotNull("planner_name").apply("length(trim(planner_name)) != 0")
                .groupBy("planner_no").orderByAsc("planner_no");
        Page<AiPlannerTopUser> page = new Page<>(req.getCurrent(), req.getSize());
        return iAiPlannerTopUserService.page(page, wrapper);
    }

    @Override
    public Page<QwPlannerResp> pageBindPlanner(NewQwBindPlannerPageReq pageReq) {
        String tenantId = AuthUtil.getTenantId();
        if (StrUtil.isBlank(tenantId)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (!tenantId.equals(thirdAccessProperty.getTenantId().get("yiren"))) {
            throw new AiServerException(ResultCode.CHANNEL_UNSUPPORTED_QUERY_QW_INFO);
        }

        Page<QwPlannerResp> res = new Page<>();
        res.setCurrent(pageReq.getPageNo());
        res.setSize(pageReq.getPageSize());

        StringBuilder url = new StringBuilder();
        url.append(scrmUrl).append(scrmPlannerSearchUrl)
                .append("?status=1")
                .append("&current=").append(pageReq.getPageNo())
                .append("&size=").append(pageReq.getPageSize());
        if (StrUtil.isNotBlank(pageReq.getName())) {
            url.append("&plannerName=").append(pageReq.getName());
        }
        if (StrUtil.isNotBlank(pageReq.getQwName())) {
            url.append("&name=").append(pageReq.getQwName());
        }
        Set<String> dataScopeSet = dataScopeUtil.getUserMenuDataScope(callcenterProperty.getYumengNewQwContact(), TenantServiceHolder.SCOPE_PLANNER_FIELD)
                .stream().filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(dataScopeSet) && dataScopeSet.size() == 1 && dataScopeSet.contains(DataScopeUtil.DEFAULT_MENU_DATA_SCOPE)) {
            return res;
        }
        Set<String> plannerIdSet = dataScopeSet;
        if (StrUtil.isNotBlank(pageReq.getDeptId())) {
            Set<String> deptSet = dataScopeUtil.getPlannerNoByDeptId(pageReq.getDeptId()).stream().filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(deptSet)) {
                return res;
            }
            if (CollectionUtils.isEmpty(dataScopeSet)) {
                plannerIdSet = deptSet;
            } else {
                plannerIdSet = dataScopeSet.stream().filter(deptSet::contains).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(plannerIdSet)) {
                    return res;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(plannerIdSet)) {
            url.append("&plannerNumberSet=").append(String.join(",", plannerIdSet));
        }

        log.info("调用Scrm接口分页查询理财师信息列表开始，url:{}", url.toString());
        String resp = restTemplate.getForEntity(url.toString(), String.class).getBody();
        if (StrUtil.isNotBlank(resp)) {
            R<Page<FinancialPlannerVO>> result = JSON.parseObject(resp, new TypeReference<R<Page<FinancialPlannerVO>>>(){});
            if (result.isSuccess()) {
                BeanUtil.copyProperties(result.getData(), res);
                if (CollectionUtils.isEmpty(result.getData().getRecords())) {
                    return res;
                }

                List<PlannerInfoDto> plannerInfoList = iBladeUserService.findPlannerDeptInfoByPlannerIds(tenantId,
                        result.getData().getRecords().stream().map(FinancialPlannerVO::getPlannerNumber).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList()));
                Map<String, String> plannerDeptMap = CollUtil.isEmpty(plannerInfoList) ? new HashMap<>() :
                        plannerInfoList.stream().collect(Collectors.toMap(PlannerInfoDto::getPlannerId, PlannerInfoDto::getGroupName));
                res.setRecords(result.getData().getRecords().stream().map(planner -> {
                    QwPlannerResp qwPlanner = new QwPlannerResp();
                    qwPlanner.setPlannerName(planner.getPlannerName());
                    qwPlanner.setPlannerNo(planner.getPlannerNumber());
                    qwPlanner.setPlannerQwName(planner.getName());
                    qwPlanner.setDeptName(plannerDeptMap.getOrDefault(planner.getPlannerNumber(), null));
                    return qwPlanner;
                }).collect(Collectors.toList()));
            }
        }
        log.info("调用Scrm接口分页查询理财师信息列表结束，url:{}，result:{}", url.toString(), resp);
        return res;
    }

    @Override
    public Page<AiPlannerChatContact> pagePlannerFriend(NewQwPlannerFriendPageReq pageReq) {
        String tenantId = AuthUtil.getTenantId();
        if (StrUtil.isBlank(tenantId)) {
            throw new AiServerException(ResultCode.NO_LOGIN);
        }
        if (!tenantId.equals(thirdAccessProperty.getTenantId().get("yiren"))) {
            throw new AiServerException(ResultCode.CHANNEL_UNSUPPORTED_QUERY_QW_INFO);
        }

        Long total = iAiPlannerChatContactService.getLatestMsgCountByPlannerNo(pageReq.getPlannerNo(), pageReq.getUserId(), QwMsgTypeEnum.nonQwMsgTypeList);
        List<AiPlannerChatContact> records = iAiPlannerChatContactService.getLatestMsgByPlannerNoWithPage(
                pageReq.getPlannerNo(), pageReq.getUserId(), QwMsgTypeEnum.nonQwMsgTypeList,
                Math.max(0, (pageReq.getPageNo() - 1) * pageReq.getPageSize()), pageReq.getPageSize());

        Page<AiPlannerChatContact> res = new Page<>(pageReq.getPageNo(), pageReq.getPageSize());
        res.setTotal(total);
        res.setRecords(records);
        return res;
    }
}
