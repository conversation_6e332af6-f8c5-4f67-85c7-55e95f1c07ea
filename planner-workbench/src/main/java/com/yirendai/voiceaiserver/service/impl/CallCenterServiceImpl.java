package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.MsgSendDirectionEnum;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.model.po.SpeakerInfo;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.mq.send.MqSendUtil;
import com.yirendai.voiceaiserver.mq.send.TopicEnum;
import com.yirendai.voiceaiserver.service.*;
import com.yirendai.voiceaiserver.util.AudioSplitUtil;
import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.voiceaiserver.util.HttpClientProxyUtil;
import com.yirendai.voiceaiserver.vo.db.PlannerAchievementInfo;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.request.SendRobotReq;
import com.yirendai.voiceaiserver.vo.response.*;
import com.yirendai.workbench.config.YxhCallServiceProperty;
import com.yirendai.workbench.entity.AiCallCenterTranslate;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.AiPlannerTopUser;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.enums.callcenter.anmi.AnmiConstants;
import com.yirendai.workbench.service.AdsCsgDOrderDetailDfService;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import com.yirendai.workbench.service.callcenter.CallRecordQueryService;
import com.yirendai.workbench.util.CallRecordUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.JedisCluster;
import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class CallCenterServiceImpl implements CallCenterService {

    @Value("${yr.tenant.id}")
    private String yrTenantId;

    @Value("${nas.path}")
    private String path;

    @Value("${callCenter.failRetryCount.limit}")
    private Integer failRetryCountLimit;

    @Value("${qw.robot.sync.url}")
    private String robotSyncUrl;

    @Resource
    CallcenterCallRecordService callcenterCallRecordService;

    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;

    @Resource
    CallRecordQueryService callRecordQueryService;

    @Resource
    IAiPlannerTopUserService iAiPlannerTopUserService;

    @Resource
    AdsCsgDOrderDetailDfService adsCsgDOrderDetailDfService;

    @Resource
    JedisCluster jedisCluster;

    @Resource
    MqSendUtil mqSendUtil;

    @Resource
    AiPromptRecordService aiPromptRecordService;

    @Resource
    AiCallCenterTranslateService aiCallCenterTranslateService;

    @Resource
    AiPlannerVoiceService aiPlannerVoiceService;

    @Resource
    QwCallService qwCallService;

    @Resource
    @Qualifier("callCenterRealTimeThreadPoolExecutor")
    ExecutorService callCenterRealTimeThreadPoolExecutor;

    @Resource
    AIConversationProcessService aiConversationProcessService;

    @Resource
    TransactionTemplate transactionTemplate;

    @Autowired
    private YxhCallServiceProperty yxhCallServiceProperty;

    /**
     * 新呼叫中心语音开始处理时间
     */
    private static final String KEY_CALL_CENTER_START_TIME = "voice:ai:server:call:center:start:time";

    /**
     * 当天音转文失败次数已达上限告警-startTime（一个消息一天只通知一次）
     */
    private static final String KEY_CALL_CENTER_FAIL_RETRY_UP_LIMIT_NOTIFY_START_TIME =
            "voice:ai:server:call:center:fail:retry:up:limit:notify:start:time";

    @Override
    @Transactional
    public void addRecord(String uuid, Long ringTime) {
        log.info("同步新呼叫中心消息id={}开始", uuid);
        LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CallcenterCallRecord::getUuid, uuid).eq(CallcenterCallRecord::getSystemType, 2);
        CallcenterCallRecord record = callcenterCallRecordService.getOne(wrapper);
        if (Objects.isNull(record) || StrUtil.isBlank(record.getCallPlannerId())
                || StrUtil.isBlank(record.getCustomerUid()) || StrUtil.isBlank(record.getPhone())) {
            log.info("当前消息uuid={}部分数据缺失，无法同步消息", uuid);
            return;
        }

        LambdaQueryWrapper<AiPlannerChatContact> chatWrapper = new LambdaQueryWrapper<>();
        chatWrapper.eq(AiPlannerChatContact::getBusId, uuid).eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.CALL_CENTER.getType());
        AiPlannerChatContact chatContactDB = iAiPlannerChatContactService.getOne(chatWrapper);
        if (Objects.nonNull(chatContactDB)) {
            log.info("当前消息uuid={}已同步，无需再次同步", uuid);
            return;
        }
        if (Objects.nonNull(record.getCallUuid())) {
            log.info("当前消息uuid={}已同步 & 数据可能存在异常，无需再次同步", uuid);
            return;
        }

        AiPromptRecordVo config = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.CALL_CENTER_TRANSLATE.getCode());

        AiPlannerChatContact chatContact = new AiPlannerChatContact();
        chatContact.setPlannerNo(record.getCallPlannerId());
        chatContact.setUserId(record.getCustomerUid());
        chatContact.setNasPath(callRecordQueryService.getRecordPathByUuid(record.getUuid(),record.getAgentId()));
        chatContact.setOriginContent("");
        chatContact.setOriginType(QwMsgTypeEnum.CALL_CENTER.getType());
        chatContact.setPhone(record.getPhone());
        chatContact.setMsgTime(LocalDateTime.now());
        chatContact.setBusId(record.getUuid());
        chatContact.setAvayaId(String.valueOf(record.getId()));
        chatContact.setTenantId(record.getTenantId());
        iAiPlannerChatContactService.save(chatContact);
        syncPlannerUser(record);

        boolean isExist = jedisCluster.exists(KEY_CALL_CENTER_START_TIME);
        jedisCluster.hset(KEY_CALL_CENTER_START_TIME, String.valueOf(chatContact.getId()), String.valueOf(ringTime));
        if (!isExist) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endOfDay = now.toLocalDate().atStartOfDay().plusDays(1);
            jedisCluster.expire(KEY_CALL_CENTER_START_TIME, (int) ChronoUnit.SECONDS.between(now, endOfDay));
        }
        mqSendUtil.sendDelayMessage(TopicEnum.CALL_CENTER_MSG_TRANSLATE,
                MqBaseDto.builder().chatContactId(chatContact.getId()).startTime(ringTime).needSendDelay(Boolean.TRUE).build(), config.getSegmentLong() * 1000);
        log.info("同步新呼叫中心消息id={}结束", uuid);
    }

    @Override
    public void syncPlannerUser(CallcenterCallRecord record) {
        LambdaQueryWrapper<AiPlannerTopUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(AiPlannerTopUser::getPlannerNo, record.getCallPlannerId())
                .eq(AiPlannerTopUser::getUserId, record.getCustomerUid())
                .eq(AiPlannerTopUser::getTenantId, record.getTenantId());
        AiPlannerTopUser topUser = iAiPlannerTopUserService.getOne(userWrapper);
        if (Objects.isNull(topUser)) {
            AiPlannerTopUser user = new AiPlannerTopUser();
            user.setPlannerNo(record.getCallPlannerId());
            user.setPlannerName(record.getAgentName());
            user.setUserId(record.getCustomerUid());
            user.setUserName(record.getCustomerName());
            user.setTenantId(record.getTenantId());
            if (yrTenantId.equals(record.getTenantId())) {
                try {
                    Map<String, PlannerVO> plannerMap =
                            adsCsgDOrderDetailDfService.getPlannerMap(Collections.singletonList(record.getCallPlannerId()));
                    user.setPlannerQwNo(plannerMap.get(record.getCallPlannerId()).getWxUserId());
                } catch (Exception e) {
                    log.error("查询理财师企微号发生异常，异常原因为", e);
                }
                Map<String, YrUserVO> userMap = iAiPlannerTopUserService.getByUserIds(Collections.singletonList(user.getUserId()));
                if (userMap.containsKey(user.getUserId())) {
                    user.setUserUnionId(userMap.get(user.getUserId()).getUnionid());
                    user.setCreateTime(Objects.isNull(userMap.get(user.getUserId()).getRegisterTime()) ?
                            Objects.isNull(userMap.get(user.getUserId()).getCreatedAt()) ?
                                    LocalDateTime.now()
                                    : userMap.get(user.getUserId()).getCreatedAt().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime()
                            : userMap.get(user.getUserId()).getRegisterTime().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
                }
            }
            iAiPlannerTopUserService.save(user);
        }
    }

    @Override
    @Async("commonThreadPoolExecutor")
    @Transactional
    public void finishNotify(String uuid) {
        log.info("新呼叫中心消息id={}挂断通知处理开始", uuid);
        LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CallcenterCallRecord::getUuid, uuid).eq(CallcenterCallRecord::getSystemType, 2);
        CallcenterCallRecord record = callcenterCallRecordService.getOne(wrapper);
        LambdaQueryWrapper<AiPlannerChatContact> chatWrapper = new LambdaQueryWrapper<>();
        chatWrapper.eq(AiPlannerChatContact::getBusId, uuid).eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.CALL_CENTER.getType());
        AiPlannerChatContact chatContact = iAiPlannerChatContactService.getOne(chatWrapper);
        if (Objects.isNull(record)) {
            log.info("当前消息uuid={}未查询到对应的通话记录，无法同步挂断通知", uuid);
            if (Objects.nonNull(chatContact)) {
                iAiPlannerChatContactService.removeById(chatContact.getId());
            }
            return;
        } else {
            if (Objects.isNull(chatContact)) {
                if (StrUtil.isBlank(record.getRecordUrl()) || Objects.isNull(record.getCsAnswerStamp()) || Objects.isNull(record.getCsBillsec())) {
                    log.info("当前消息uuid={}部分数据缺失，无法同步挂断通知", uuid);
                    return;
                }
                chatContact = syncChat(record, Long.valueOf(1L).compareTo(record.getCsBillsec()) > 0 ? "[新呼叫中心语音]" : null, null, LocalDateTime.now());
                syncPlannerUser(record);
            } else {
                if (StrUtil.isBlank(record.getRecordUrl()) || Objects.isNull(record.getCsAnswerStamp()) || Objects.isNull(record.getCsBillsec())) {
                    log.info("当前消息uuid={}部分数据缺失，无法同步挂断通知", uuid);
                    LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(AiPlannerChatContact::getId, chatContact.getId())
                            .set(AiPlannerChatContact::getFinishNotifyTime, LocalDateTime.now());
                    iAiPlannerChatContactService.update(updateWrapper);
                    return;
                }
                LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(AiPlannerChatContact::getDirection, "inbound".equals(record.getDirection()) ?
                        MsgSendDirectionEnum.FROM_CUS.getCode() : MsgSendDirectionEnum.FROM_CRM.getCode())
                        .set(AiPlannerChatContact::getOriginContent, record.getRecordUrl())
                        .set(AiPlannerChatContact::getMsgTime, record.getCsAnswerStamp())
                        .set(AiPlannerChatContact::getLongTime, record.getCsBillsec())
                        .set(AiPlannerChatContact::getFinishNotifyTime, LocalDateTime.now())
                        .eq(AiPlannerChatContact::getId, chatContact.getId());
                if (Long.valueOf(1L).compareTo(record.getCsBillsec()) > 0) {
                    updateWrapper.set(AiPlannerChatContact::getProcessedContent, "[新呼叫中心语音]");
                }
                iAiPlannerChatContactService.update(updateWrapper);
            }
        }

        if (Long.valueOf(0L).compareTo(record.getCsBillsec()) < 0) {
            String cacheTime = jedisCluster.hget(KEY_CALL_CENTER_START_TIME, String.valueOf(chatContact.getId()));
            Long startTime = StrUtil.isNotBlank(cacheTime) && StrUtil.isNumeric(cacheTime) ?
                    Long.parseLong(cacheTime) : Objects.nonNull(record.getCsRing()) ? record.getCsRing() : 0L;
            mqSendUtil.sendMessage(TopicEnum.CALL_CENTER_MSG_TRANSLATE,
                    MqBaseDto.builder().chatContactId(chatContact.getId()).startTime(startTime).needSendDelay(Boolean.FALSE).build());
        } else {
            aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(chatContact.getId()).build());
        }
        log.info("新呼叫中心消息id={}挂断通知处理结束", uuid);
    }

    @Override
    public AiPlannerChatContact syncChat(CallcenterCallRecord record, String content, AnmiConstants.AnmiDeviceType anmiDeviceType, LocalDateTime finishNotifyTime) {
        AiPlannerChatContact chatContact = new AiPlannerChatContact();
        chatContact.setPlannerNo(record.getCallPlannerId());
        chatContact.setUserId(record.getCustomerUid());
        chatContact.setDirection("inbound".equals(record.getDirection())
                ? MsgSendDirectionEnum.FROM_CUS.getCode() : MsgSendDirectionEnum.FROM_CRM.getCode());
        if (anmiDeviceType != null){
            //1 表示客户左声道
            chatContact.setDirection(anmiDeviceType.getAgentChannel() == 1
                    ? MsgSendDirectionEnum.FROM_CUS.getCode() : MsgSendDirectionEnum.FROM_CRM.getCode());
        }
        chatContact.setNasPath(StringUtils.isNotBlank(record.getNasPath())? record.getNasPath()
                : callRecordQueryService.getRecordPathByUuid(record.getUuid(),record.getAgentId()));
        chatContact.setOriginContent(record.getRecordUrl());
        chatContact.setOriginType(QwMsgTypeEnum.CALL_CENTER.getType());
        chatContact.setPhone(record.getPhone());
        if (StrUtil.isNotBlank(content)) {
            chatContact.setProcessedContent(content);
        }
        chatContact.setMsgTime(record.getCsAnswerStamp());
        chatContact.setBusId(record.getUuid());
        chatContact.setAvayaId(String.valueOf(record.getId()));
        chatContact.setLongTime(record.getCsBillsec());
        if (Objects.nonNull(finishNotifyTime)) {
            chatContact.setFinishNotifyTime(finishNotifyTime);
        }
        chatContact.setTenantId(record.getTenantId());
        iAiPlannerChatContactService.save(chatContact);
        return chatContact;
    }

    @Override
    public void translate(MqBaseDto mqBaseDto) {
        log.info("新呼叫中心通话记录片段音转文开始，param={}", JSON.toJSONString(mqBaseDto));
        try {
            AiPlannerChatContact chatContact = iAiPlannerChatContactService.getById(mqBaseDto.getChatContactId());
            if (Objects.isNull(chatContact)) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "未查询到待处理消息");
            }
            if (StrUtil.isNotBlank(chatContact.getProcessedContent())) {
                log.info("当前消息已处理完成");
                return;
            }

            // 先发延时消息后处理当前通话片段
            AiPromptRecordVo config = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.CALL_CENTER_TRANSLATE.getCode());
            if (Boolean.TRUE.equals(mqBaseDto.getNeedSendDelay()) && Objects.isNull(chatContact.getFinishNotifyTime())) {
                Long startTime = mqBaseDto.getStartTime() + config.getSegmentLong();
                jedisCluster.hset(KEY_CALL_CENTER_START_TIME, String.valueOf(chatContact.getId()), String.valueOf(startTime));
                mqSendUtil.sendDelayMessage(TopicEnum.CALL_CENTER_MSG_TRANSLATE,
                        MqBaseDto.builder().chatContactId(chatContact.getId()).startTime(startTime).needSendDelay(Boolean.TRUE).build(),
                        config.getSegmentLong() * 1000);
            }

            LambdaQueryWrapper<AiCallCenterTranslate> translateWrapper = new LambdaQueryWrapper<>();
            translateWrapper.eq(AiCallCenterTranslate::getChatId, chatContact.getId())
                    .eq(AiCallCenterTranslate::getStartTime, mqBaseDto.getStartTime());
            AiCallCenterTranslate translate = aiCallCenterTranslateService.getOne(translateWrapper);
            if (Objects.nonNull(translate) && !translate.getText().startsWith("处理失败,失败原因为:")) {
                return;
            }

            String text = "处理失败,失败原因为:";
            File handleFile = null;
            Long endTime = mqBaseDto.getStartTime() + config.getSegmentLong();
            try {
                File file = new File(chatContact.getNasPath());
                if (!file.exists()) {
                    if (chatContact.getNasPath().contains(yxhCallServiceProperty.getRealtimeFileDir())) {
                        String newPath = StringUtils
                                .replace(chatContact.getNasPath(), yxhCallServiceProperty.getRealtimeFileDir(), yxhCallServiceProperty.getRealtimeFileDirYrCallCenter());
                        file = new File(newPath);
                        if (!file.exists()) {
                            log.error("translate 未获取到文件 path ={}",newPath);
                            throw new AiServerException(ResultCode.FAILURE.getCode(), "未获取到文件");
                        }
                    } else if (chatContact.getNasPath().contains(yxhCallServiceProperty.getRealtimeFileDirYrCallCenter())) {
                        String newPath = StringUtils
                                .replace(chatContact.getNasPath(), yxhCallServiceProperty.getRealtimeFileDirYrCallCenter(), yxhCallServiceProperty.getRealtimeFileDir());
                        file = new File(newPath);
                        if (!file.exists()) {
                            log.error("translate 未获取到文件 path ={}",newPath);
                            throw new AiServerException(ResultCode.FAILURE.getCode(), "未获取到文件");
                        }
                    } else {
                        throw new AiServerException(ResultCode.FAILURE.getCode(), "未获取到文件");
                    }
                }

                if (Objects.nonNull(chatContact.getLongTime())) {
                    if (StrUtil.isBlank(chatContact.getBusId())) {
                        throw new AiServerException(ResultCode.DB_DATA_ERROR);
                    }
                    LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(CallcenterCallRecord::getUuid, chatContact.getBusId()).eq(CallcenterCallRecord::getSystemType, 2);
                    CallcenterCallRecord record = callcenterCallRecordService.getOne(wrapper);
                    if (Objects.isNull(record) || Objects.isNull(record.getCsDuration())) {
                        throw new AiServerException(ResultCode.DB_DATA_ERROR);
                    }
                    if (record.getCsDuration().compareTo(endTime) < 0) {
                        endTime = record.getCsDuration();
                    }
                }

                String segmentFilePath = file.getParent() + "/" + RandomUtil.randomString(32) + ".wav";
                try {
                    AudioSplitUtil.splitAndConcatenateAudioToWav(chatContact.getNasPath(), segmentFilePath,
                            Collections.singletonList(SegmentVo.builder().start(mqBaseDto.getStartTime() * 1000)
                                    .end(endTime * 1000).build()));
                } catch (Exception e) {
                    log.error("获取文件片段发生异常，异常原因为", e);
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "获取文件片段发生异常");
                }
                handleFile = new File(segmentFilePath);
                log.info("通话记录chatId={}当前处理音频片段文件为:{}", chatContact.getId(), handleFile.getAbsolutePath());

                text = getTranslateText(handleFile, config, new SpeakerInfo(chatContact.getDirection()), null, Objects.nonNull(chatContact.getFinishNotifyTime()));
            } catch (Exception e) {
                text = text + e.getMessage();
            } finally {
                if (Objects.nonNull(handleFile)) {
                    handleFile.delete();
                }
            }

            Long finalEndTime = endTime;
            String finalText = text;
            transactionTemplate.executeWithoutResult(status -> {
                if (Objects.isNull(translate)) {
                    AiCallCenterTranslate translateRecord = new AiCallCenterTranslate();
                    translateRecord.setChatId(chatContact.getId());
                    translateRecord.setStartTime(mqBaseDto.getStartTime());
                    translateRecord.setEndTime(finalEndTime);
                    translateRecord.setText(finalText);
                    aiCallCenterTranslateService.save(translateRecord);
                } else {
                    LambdaUpdateWrapper<AiCallCenterTranslate> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(AiCallCenterTranslate::getId, translate.getId())
                            .set(AiCallCenterTranslate::getText, finalText)
                            .set(AiCallCenterTranslate::getEndTime, finalEndTime);
                    if (finalText.startsWith("处理失败,失败原因为:")) {
                        updateWrapper.setSql("fail_retry_count = fail_retry_count + 1");
                    }
                    aiCallCenterTranslateService.update(updateWrapper);
                }

                if (!finalText.startsWith("处理失败,失败原因为:") && Objects.nonNull(chatContact.getLongTime())) {
                    LambdaQueryWrapper<AiCallCenterTranslate> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(AiCallCenterTranslate::getChatId, chatContact.getId())
                            .apply("text not like '处理失败,失败原因为:%'")
                            .orderByAsc(AiCallCenterTranslate::getStartTime);
                    List<AiCallCenterTranslate> translateList = aiCallCenterTranslateService.list(wrapper);
                    Long allTime = translateList.stream().mapToLong(t -> t.getEndTime() - t.getStartTime()).sum();
                    if (chatContact.getLongTime().compareTo(allTime) <= 0) {
                        String content = translateList.stream().map(AiCallCenterTranslate::getText).collect(Collectors.joining("\n"));
                        LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(AiPlannerChatContact::getProcessedContent, StrUtil.isBlank(content) ? "[新呼叫中心语音]" : content)
                                .eq(AiPlannerChatContact::getId, chatContact.getId());
                        iAiPlannerChatContactService.update(updateWrapper);

                        LambdaQueryWrapper<AiCallCenterTranslate> deleteWrapper = new LambdaQueryWrapper<>();
                        deleteWrapper.eq(AiCallCenterTranslate::getChatId, chatContact.getId());
                        aiCallCenterTranslateService.remove(deleteWrapper);

                        jedisCluster.hdel(KEY_CALL_CENTER_START_TIME, String.valueOf(chatContact.getId()));

                        aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(chatContact.getId()).build());
                    }
                }
            });
        }  catch (Exception e) {
            log.error("实时音转文处理发生异常，param={}，异常原因为", JSON.toJSONString(mqBaseDto), e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), e.getMessage());
        }
        log.info("新呼叫中心通话记录片段音转文结束，param={}", JSON.toJSONString(mqBaseDto));
    }

    private String getTranslateText(File file, AiPromptRecordVo config, SpeakerInfo speakerInfo, Long longTime, boolean isFinish) {
        try {
            Future<String> future = callCenterRealTimeThreadPoolExecutor.submit(() -> handleTranslate(file, config, speakerInfo, longTime, isFinish));
            return future.get();
        } catch (Exception e) {
            log.error("获取音转文结果发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), e.getMessage());
        }
    }

    private String handleTranslate(File file, AiPromptRecordVo config, SpeakerInfo speakerInfo, Long longTime, boolean isFinish) {
            log.info("开始异步音转文，param=[file={}, config={}, direction={}, longTime={}, isFinish={}]", file.getAbsolutePath(), JSON.toJSONString(config), speakerInfo.getDirection(), longTime, isFinish);
        if (Objects.isNull(longTime)) {
            try {
                longTime = Math.round(AudioSplitUtil.getWavDuration(file));
            } catch (Exception e) {
                log.error("获取处理音频时长发生异常，异常原因为", e);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "获取处理音频时长发生异常");
            }
        }
        if (longTime < 1) {
            if (isFinish) {
                return "";
            } else {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "当前通话未挂断，获取处理音频时长为" + longTime + "，时长计算异常");
            }
        }

        if (config.getOpen()) {
            file = aiPlannerVoiceService.deNoise(file, config.getSampleRate());
        }

        try {
            String leftChannelOutputFile = file.getParent() + "/" + RandomUtil.randomString(32) + ".wav";
            String rightChannelOutputFile = file.getParent() + "/" + RandomUtil.randomString(32) + ".wav";
            FileUtil.splitAudioByChannel(file.getAbsolutePath(), leftChannelOutputFile, rightChannelOutputFile);
            File left = new File(leftChannelOutputFile);
            File right = new File(rightChannelOutputFile);
            try {
                List<AudioToTextVo.Segment> allSegmentList = new ArrayList<>();
                allSegmentList.addAll(qwCallService.getSegmentWithConfig(left, speakerInfo.getLeftSpeaker(), config));
                allSegmentList.addAll(qwCallService.getSegmentWithConfig(right, speakerInfo.getRightSpeaker(), config));
                allSegmentList.sort(Comparator.comparingInt(AudioToTextVo.Segment::getStart));
                return allSegmentList.stream().map(s -> s.getSpeaker() + ":" + s.getText()).collect(Collectors.joining("\n"));
            } finally {
                left.delete();
                right.delete();
            }
        } finally {
            if (config.getOpen()) {
                file.delete();
            }
        }
    }

    @Override
    public void handleTodayFailSegment() {
        log.info("处理当天音转文失败片段任务开始...");
        Long startId = null;
        while (true) {
            LambdaQueryWrapper<AiCallCenterTranslate> wrapper = new LambdaQueryWrapper<>();
            if (Objects.nonNull(startId)) {
                wrapper.gt(AiCallCenterTranslate::getId, startId);
            }
            wrapper.ge(AiCallCenterTranslate::getCreateTime, LocalDate.now().atStartOfDay())
                    .likeRight(AiCallCenterTranslate::getText, "处理失败,失败原因为:")
                    .lt(AiCallCenterTranslate::getFailRetryCount, failRetryCountLimit)
                    .orderByAsc(AiCallCenterTranslate::getCreateTime).last("limit 100");
            List<AiCallCenterTranslate> translateList = aiCallCenterTranslateService.list(wrapper);
            if (CollUtil.isEmpty(translateList)) {
                break;
            }

            for (AiCallCenterTranslate translate : translateList) {
                mqSendUtil.sendMessage(TopicEnum.CALL_CENTER_MSG_TRANSLATE, MqBaseDto.builder()
                        .chatContactId(translate.getChatId()).startTime(translate.getStartTime()).needSendDelay(Boolean.FALSE).build());
            }

            if (translateList.size() < 100) {
                break;
            }
            startId = translateList.get(translateList.size() - 1).getId();
        }
        log.info("处理当天音转文失败片段任务结束");
    }

    @Override
    public List<PlannerAchievementInfo> getPlannerList() {
        List<CallcenterCallRecord> list = callcenterCallRecordService.getCallCenterPlannerList();
        return list.stream().map(c -> PlannerAchievementInfo.builder().plannerNo(c.getCallPlannerId())
                .plannerName(c.getAgentName()).build()).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void syncRecord(LocalDate startDate) {
        log.info("同步指定日期至今的通话记录任务开始，startDate={}", startDate);
        Long startId = null;
        AiPromptRecordVo config = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.CALL_CENTER_TRANSLATE.getCode());
        while (true) {
            LambdaQueryWrapper<CallcenterCallRecord> wrapper = new LambdaQueryWrapper<>();
            if (Objects.nonNull(startId)) {
                wrapper.gt(CallcenterCallRecord::getId, startId);
            }
            wrapper.isNotNull(CallcenterCallRecord::getCsAnswerStamp)
                    .ge(CallcenterCallRecord::getCsAnswerStamp, startDate.atStartOfDay())
                    .lt(CallcenterCallRecord::getCsAnswerStamp, LocalDateTimeUtil.now())
                    .eq(CallcenterCallRecord::getSystemType, 2)
                    .isNotNull(CallcenterCallRecord::getCsBillsec).gt(CallcenterCallRecord::getCsBillsec, 0L)
                    .isNotNull(CallcenterCallRecord::getRecordUrl).apply("length(trim(record_url)) != 0")
                    .orderByAsc(CallcenterCallRecord::getId).last("Limit 100");
            List<CallcenterCallRecord> list = callcenterCallRecordService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            List<CallcenterCallRecord> handleList = new ArrayList<>(list);
            List<String> uuidList = list.stream().map(CallcenterCallRecord::getUuid).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(uuidList)) {
                LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(AiPlannerChatContact::getBusId)
                        .in(AiPlannerChatContact::getBusId, uuidList)
                        .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.CALL_CENTER.getType());
                List<AiPlannerChatContact> chatList = iAiPlannerChatContactService.list(queryWrapper);
                if (CollUtil.isNotEmpty(chatList)) {
                    List<String> busIdList = chatList.stream().map(AiPlannerChatContact::getBusId).distinct().collect(Collectors.toList());
                    handleList = handleList.stream().filter(c -> !busIdList.contains(c.getUuid())).collect(Collectors.toList());
                }
            }

            if (CollUtil.isNotEmpty(handleList)) {
                for (CallcenterCallRecord record : handleList) {
                    try {
                        AnmiConstants.AnmiDeviceType anmiDeviceType = null;
                        if (StringUtils.isNotBlank(record.getDeviceModel())){
                            anmiDeviceType = AnmiConstants.AnmiDeviceType.getByModel(record.getDeviceModel());
                        }
                        AiPlannerChatContact chatContact = syncChat(record, null, anmiDeviceType, null);
                        syncPlannerUser(record);
                        handleComplete(chatContact, config, record.getCsRing(), record.getCsDuration(), anmiDeviceType);
                    } catch (Exception e) {
                        log.error("同步新呼叫中心消息id={}发生异常，异常原因为", record.getId(), e);
                    }
                }
            }

            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }
        log.info("同步指定日期至今的通话记录任务结束，startDate={}", startDate);
    }

    @Override
    public void handleComplete(AiPlannerChatContact chatContact, AiPromptRecordVo config, Long startTime, Long endTime, AnmiConstants.AnmiDeviceType anmiDeviceType) {
        boolean needDel = FileUtil.download(CallRecordUtil.getCallRecordIdcUrl(chatContact.getOriginContent()), chatContact.getNasPath());
        File file = new File(chatContact.getNasPath()), segmentFile = null;
        if (Objects.nonNull(startTime) && Long.valueOf(0L).compareTo(startTime) < 0) {
            if (Objects.isNull(endTime) || endTime.compareTo(startTime) <= 0) {
                throw new AiServerException(ResultCode.DB_DATA_ERROR);
            }
            String segmentFilePath = file.getParent() + "/" + RandomUtil.randomString(32) + ".wav";
            try {
                AudioSplitUtil.splitAndConcatenateAudioToWav(chatContact.getNasPath(), segmentFilePath,
                        Collections.singletonList(SegmentVo.builder().start(startTime * 1000).end(endTime * 1000).build()));
            } catch (Exception e) {
                log.error("切割文件发生异常，异常原因为", e);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "切割文件发生异常");
            }
            segmentFile = new File(segmentFilePath);
        }
        try {
            File handleFile = Objects.nonNull(segmentFile) ? segmentFile : file;
            log.info("通话记录chatId={}处理完整音频文件为:{}", chatContact.getId(), handleFile.getAbsolutePath());
            String content = getTranslateText(handleFile, config, new SpeakerInfo(chatContact.getDirection(), anmiDeviceType), chatContact.getLongTime(), true);
            LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(AiPlannerChatContact::getProcessedContent, StrUtil.isBlank(content) ? "[新呼叫中心语音]" : content)
                    .eq(AiPlannerChatContact::getId, chatContact.getId());
            iAiPlannerChatContactService.update(updateWrapper);

            aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(chatContact.getId()).build());
        } finally {
            if (needDel) {
                file.delete();
            }
            if (Objects.nonNull(segmentFile)) {
                segmentFile.delete();
            }
        }
    }

    @Override
    @Transactional
    public void handleFailComplete(LocalDate startDate, LocalDate endDate) {
        log.info("处理指定时间范围内的音转文失败音频任务开始，startDate={}，endDate={}", startDate, endDate);
        Long startId = null;
        AiPromptRecordVo config = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.CALL_CENTER_TRANSLATE.getCode());
        LocalDateTime endTime = Objects.isNull(endDate) ? LocalDateTimeUtil.now() : endDate.atStartOfDay();
        while (true) {
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            if (Objects.nonNull(startId)) {
                wrapper.gt(AiPlannerChatContact::getId, startId);
            }
            wrapper.eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.CALL_CENTER.getType())
                    .ge(AiPlannerChatContact::getMsgTime, startDate.atStartOfDay())
                    .lt(AiPlannerChatContact::getMsgTime, endTime)
                    .and(w -> w.isNull(AiPlannerChatContact::getProcessedContent).or().apply("length(trim(processed_content)) = 0"))
                    .orderByAsc(AiPlannerChatContact::getId).last("Limit 100");
            List<AiPlannerChatContact> list = iAiPlannerChatContactService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            for (AiPlannerChatContact chatContact : list) {
                try {
                    boolean needTranslate = syncFinish(chatContact);
                    if (needTranslate) {
                        if (StrUtil.isBlank(chatContact.getOriginContent())) {
                            chatContact = iAiPlannerChatContactService.getById(chatContact.getId());
                        }
                        CallcenterCallRecord record = callcenterCallRecordService.getById(chatContact.getAvayaId());
                        if (Objects.isNull(record)) {
                            throw new AiServerException(ResultCode.DB_DATA_ERROR);
                        }
                        handleComplete(chatContact, config, record.getCsRing(), record.getCsDuration(), null);
                    }
                } catch (Exception e) {
                    log.error("处理消息id={}发生异常，异常原因为", chatContact.getId(), e);
                }
            }

            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }
        log.info("处理指定时间范围内的音转文失败音频任务结束，startDate={}，endDate={}", startDate, endDate);
    }

    private boolean syncFinish(AiPlannerChatContact chatContact) {
        LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiPlannerChatContact::getId, chatContact.getId());
        boolean needUpdate = false;
        String content = null;
        if (StrUtil.isBlank(chatContact.getOriginContent())) {
            if (StrUtil.isBlank(chatContact.getAvayaId())) {
                throw new AiServerException(ResultCode.DB_DATA_ERROR);
            }
            CallcenterCallRecord record = callcenterCallRecordService.getById(chatContact.getAvayaId());
            if (Objects.isNull(record) || StrUtil.isBlank(record.getRecordUrl())
                    || Objects.isNull(record.getCsAnswerStamp()) || Objects.isNull(record.getCsBillsec())) {
                throw new AiServerException(ResultCode.DB_DATA_ERROR);
            }
            chatContact.setLongTime(record.getCsBillsec());
            updateWrapper.set(AiPlannerChatContact::getDirection, "inbound".equals(record.getDirection()) ?
                    MsgSendDirectionEnum.FROM_CUS.getCode() : MsgSendDirectionEnum.FROM_CRM.getCode())
                    .set(AiPlannerChatContact::getOriginContent, record.getRecordUrl())
                    .set(AiPlannerChatContact::getMsgTime, record.getCsAnswerStamp())
                    .set(AiPlannerChatContact::getLongTime, record.getCsBillsec());
            if (Long.valueOf(1L).compareTo(record.getCsBillsec()) > 0) {
                content = "[新呼叫中心语音]";
                updateWrapper.set(AiPlannerChatContact::getProcessedContent, content);
            }
            needUpdate = true;
        }
        LambdaQueryWrapper<AiCallCenterTranslate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiCallCenterTranslate::getChatId, chatContact.getId());
        List<AiCallCenterTranslate> translateList = aiCallCenterTranslateService.list(wrapper);
        if (CollUtil.isNotEmpty(translateList)) {
            if (StrUtil.isBlank(content)) {
                List<AiCallCenterTranslate> successList = translateList.stream().filter(t -> !t.getText().startsWith("处理失败,失败原因为:"))
                        .sorted(Comparator.comparing(AiCallCenterTranslate::getStartTime)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(successList)) {
                    Long allTime = successList.stream().mapToLong(t -> t.getEndTime() - t.getStartTime()).sum();
                    if (chatContact.getLongTime().compareTo(allTime) <= 0) {
                        needUpdate = true;
                        content = successList.stream().map(AiCallCenterTranslate::getText).collect(Collectors.joining("\n"));
                        if (StrUtil.isBlank(content)) {
                            content = "[新呼叫中心语音]";
                        }
                        updateWrapper.set(AiPlannerChatContact::getProcessedContent, content);
                    }
                }
            }
            LambdaQueryWrapper<AiCallCenterTranslate> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(AiCallCenterTranslate::getChatId, chatContact.getId());
            aiCallCenterTranslateService.remove(deleteWrapper);
            jedisCluster.hdel(KEY_CALL_CENTER_START_TIME, String.valueOf(chatContact.getId()));
        }
        if (needUpdate) {
            iAiPlannerChatContactService.update(updateWrapper);
            if (StrUtil.isNotBlank(content)) {
                aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(chatContact.getId()).build());
            }
        }
        return StrUtil.isBlank(content);
    }

    @Override
    public void compareSplit(MultipartFile file, Integer direction) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String fileName = path + LocalDate.now().format(formatter) + "_" + RandomUtil.randomString(10) + ".mp3";
        File saveFile = FileUtil.saveUploadFile(file, fileName);
        if (Objects.isNull(saveFile)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "保存文件失败");
        }

        try {
            AiPromptRecordVo config = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.CALL_CENTER_TRANSLATE.getCode());
            File wavFile = new File(saveFile.getAbsolutePath() + ".wav");
            try {
                FileUtil.convertAmrToWav(saveFile, wavFile);
                List<File> list = FileUtil.splitAudioFile(wavFile, 2 * 60, 0);
                StringBuilder str = new StringBuilder();
                for (File per : list) {
                    String content = null;
                    try {
                        content = getTranslateText(per, config, new SpeakerInfo(direction), null, true);
                    } catch (Exception e) {
                        log.error("当前片段音转文发生异常，异常原因为", e);
                        content = "处理失败,失败原因为:" + e.getMessage();
                    } finally {
                        per.delete();
                    }
                    str.append(content).append("\n-----\n");
                }
                log.info("切割处理结果:{}", str.toString());
            } catch (Exception e) {
                log.error("切割处理发生异常，异常原因为", e);
            } finally {
                wavFile.delete();
            }
            String str = null;
            try {
                str = getTranslateText(saveFile, config, new SpeakerInfo( direction), null, true);
            } catch (Exception e) {
                log.error("完整音频音转文发生异常，异常原因为", e);
                str = "处理失败,失败原因为:" + e.getMessage();
            }
            log.info("初始结果:{}", str);
        } finally {
            saveFile.delete();
        }
    }

    @Override
    public ChatTranslateStatusVO getTranslateStatus(String busId, String originType) {
        if (StrUtil.isBlank(busId)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }

        LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerChatContact::getBusId, busId)
                .eq(StrUtil.isNotBlank(originType), AiPlannerChatContact::getOriginType, originType);
        AiPlannerChatContact chatContact = iAiPlannerChatContactService.getOne(wrapper);
        if (Objects.isNull(chatContact)) {
            throw new AiServerException(ResultCode.NO_EXIST);
        }
        if (StrUtil.isBlank(chatContact.getOriginContent())) {
            throw new AiServerException(ResultCode.HANDING.getCode(), "正在通话中，请通话结束后再试");
        }

        ChatTranslateStatusVO res = ChatTranslateStatusVO.builder().id(chatContact.getId()).build();
        if (StrUtil.isNotBlank(chatContact.getProcessedContent())) {
            res.setStatus(1);
        } else if (LocalDate.now().isAfter(chatContact.getMsgTime().toLocalDate())) {
            res.setStatus(2);
        } else {
            LambdaQueryWrapper<AiCallCenterTranslate> translateWrapper = new LambdaQueryWrapper<>();
            translateWrapper.eq(AiCallCenterTranslate::getChatId, chatContact.getId())
                    .likeRight(AiCallCenterTranslate::getText, "处理失败,失败原因为:")
                    .ge(AiCallCenterTranslate::getFailRetryCount, failRetryCountLimit);
            long count = aiCallCenterTranslateService.count(translateWrapper);
            res.setStatus(count > 0 ? 2 : 0);
        }
        return res;
    }

    @Override
    public void notifyTodayFailLimit() {
        log.info("当天音转文失败次数已达上限告警任务开始执行");
        SendRobotReq.Content content = new SendRobotReq.Content();
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");

        LocalDateTime startTime = LocalDate.now().atStartOfDay(), endTime = LocalDateTime.now();
        String cache = jedisCluster.get(KEY_CALL_CENTER_FAIL_RETRY_UP_LIMIT_NOTIFY_START_TIME);
        if (StrUtil.isNotBlank(cache)) {
            startTime = LocalDateTime.parse(cache, DatePattern.NORM_DATETIME_FORMATTER);
        }
        LocalDateTime endOfDay = endTime.toLocalDate().atStartOfDay().plusDays(1);
        jedisCluster.setex(KEY_CALL_CENTER_FAIL_RETRY_UP_LIMIT_NOTIFY_START_TIME,
                (int) ChronoUnit.SECONDS.between(endTime, endOfDay), DateUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN));

        long current = 1L;
        while (true) {
            Page<AiCallCenterTranslate> page = new Page<>(current, 50);
            LambdaQueryWrapper<AiCallCenterTranslate> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(AiCallCenterTranslate::getChatId)
                    .ge(AiCallCenterTranslate::getCreateTime, startTime)
                    .lt(AiCallCenterTranslate::getCreateTime, endTime)
                    .likeRight(AiCallCenterTranslate::getText, "处理失败,失败原因为:")
                    .ge(AiCallCenterTranslate::getFailRetryCount, failRetryCountLimit)
                    .groupBy(AiCallCenterTranslate::getChatId)
                    .orderByAsc(AiCallCenterTranslate::getChatId);
            IPage<AiCallCenterTranslate> data = aiCallCenterTranslateService.page(page, wrapper);
            if (CollUtil.isEmpty(data.getRecords())) {
                break;
            }
            List<Long> handleList = data.getRecords().stream().map(AiCallCenterTranslate::getChatId).collect(Collectors.toList());

            if (!LocalDate.now().atStartOfDay().isEqual(startTime)) {
                LambdaQueryWrapper<AiCallCenterTranslate> preWrapper = new LambdaQueryWrapper<>();
                preWrapper.select(AiCallCenterTranslate::getChatId)
                        .in(AiCallCenterTranslate::getChatId, handleList)
                        .ge(AiCallCenterTranslate::getCreateTime, LocalDate.now().atStartOfDay())
                        .lt(AiCallCenterTranslate::getCreateTime, startTime)
                        .likeRight(AiCallCenterTranslate::getText, "处理失败,失败原因为:")
                        .ge(AiCallCenterTranslate::getFailRetryCount, failRetryCountLimit)
                        .groupBy(AiCallCenterTranslate::getChatId);
                List<AiCallCenterTranslate> preList = aiCallCenterTranslateService.list(preWrapper);
                if (CollUtil.isNotEmpty(preList)) {
                    List<Long> preChatIdList = preList.stream().map(AiCallCenterTranslate::getChatId).collect(Collectors.toList());
                    handleList.removeAll(preChatIdList);
                }
            }

            if (CollUtil.isNotEmpty(handleList)) {
                content.setContent("今日音转文失败次数已达上限告警通知\n详细语料id如下：\n"
                        + handleList.stream().map(String::valueOf).collect(Collectors.joining(","))
                        + "\n具体失败原因见【表ai_call_center_translate】");
                try {
                    HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                } catch (Exception e) {
                    log.error("发送企微群机器人通知失败", e);
                }
            }

            if (data.getRecords().size() < 50) {
                break;
            }
            current++;
        }

        log.info("当天音转文失败次数已达上限告警任务执行结束");
    }

    @Override
    public void clearDirtyAndNotify() {
        log.info("清理脏数据和异常数据告警任务开始执行");

        SendRobotReq.Content content = new SendRobotReq.Content();
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");

        Long startId = null;
        while (true) {
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(Objects.nonNull(startId), AiPlannerChatContact::getId, startId)
                    .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.CALL_CENTER.getType())
                    .and(w -> w.isNull(AiPlannerChatContact::getOriginContent).or().apply("trim(origin_content) = ''"))
                    .lt(AiPlannerChatContact::getMsgTime, LocalDate.now().minusDays(1L).atStartOfDay())
                    .orderByAsc(AiPlannerChatContact::getId).last("Limit 100");
            List<AiPlannerChatContact> list = iAiPlannerChatContactService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            Set<String> recordIdSet = list.stream().map(AiPlannerChatContact::getAvayaId).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            if (CollUtil.isEmpty(recordIdSet) || recordIdSet.size() != list.size()) {
                throw new AiServerException(ResultCode.DB_DATA_ERROR);
            }
            List<CallcenterCallRecord> recordList = callcenterCallRecordService.listByIds(recordIdSet);
            if (CollUtil.isEmpty(recordList) || recordList.size() != list.size()) {
                throw new AiServerException(ResultCode.DB_DATA_ERROR);
            }
            Map<String, CallcenterCallRecord> recordMap = recordList.stream().collect(Collectors.toMap(r -> String.valueOf(r.getId()), Function.identity()));

            List<Long> deleteIdList = new ArrayList<>(), notifyIdList = new ArrayList<>();
            List<String> deleteUuidList = new ArrayList<>();
            for (AiPlannerChatContact chatContact : list) {
                CallcenterCallRecord record = recordMap.getOrDefault(chatContact.getAvayaId(), null);
                if (Objects.isNull(record) || Objects.isNull(record.getAnswered()) || record.getAnswered() == 1
                        || Objects.isNull(record.getCsAnswerStamp()) || Objects.isNull(record.getCsBillsec())) {
                    deleteIdList.add(chatContact.getId());
                    deleteUuidList.add(chatContact.getBusId());
                } else if (Objects.nonNull(record.getCsBillsec()) && Long.valueOf(0L).compareTo(record.getCsBillsec()) < 0 && StrUtil.isBlank(record.getRecordUrl())) {
                    notifyIdList.add(chatContact.getId());
                }
            }

            if (CollUtil.isNotEmpty(deleteIdList)) {
                log.info("要清除的通话对应的Uuid为{}", String.join(",", deleteUuidList));
                iAiPlannerChatContactService.removeByIds(deleteIdList);
                LambdaQueryWrapper<AiCallCenterTranslate> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.in(AiCallCenterTranslate::getChatId, deleteIdList);
                aiCallCenterTranslateService.remove(deleteWrapper);
            }

            if (CollUtil.isNotEmpty(notifyIdList)) {
                content.setContent("呼叫中心告警通知\n检测发现存在" + notifyIdList.size() + "条异常语料数据，请及时处理。\n详细语料id如下：\n"
                        + notifyIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                try {
                    HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                } catch (Exception e) {
                    log.error("发送企微群机器人通知失败", e);
                }
            }

            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }

        log.info("清理脏数据和异常数据告警任务执行结束");
    }
}
