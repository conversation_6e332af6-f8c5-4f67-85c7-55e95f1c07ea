package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.VoiceStatusEnum;
import com.yirendai.workbench.mapper.AiPlannerVoiceMapper;
import com.yirendai.workbench.entity.AiPlannerVoice;
import com.yirendai.voiceaiserver.service.AiPlannerVoiceService;
import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.voiceaiserver.vo.response.*;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <p>
 * 理财师声音信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Slf4j
@Service
@RefreshScope
public class AiPlannerVoiceServiceImpl extends ServiceImpl<AiPlannerVoiceMapper, AiPlannerVoice> implements AiPlannerVoiceService {

    @Value("${nas.path}")
    private String path;
    @Value("${nas.url}")
    private String nasUrl;
    @Value("${chat-api.key}")
    private String chatApiKey;
    @Value("${chat-api.url}")
    private String chatApiBaseUrl;

    @Resource
    AiPlannerVoiceMapper aiPlannerVoiceMapper;

    @Resource
    RestTemplate restTemplateVoice;

    @Override
    public String saveOrUpdate(String account, String staffId, MultipartFile file) {
        if (file.isEmpty()) {
            throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "文件不可为空");
        }
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName) || !fileName.toLowerCase().endsWith(".wav")) {
            throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "文件必须为wav文件");
        }

        LambdaQueryWrapper<AiPlannerVoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerVoice::getAccount, account);
        AiPlannerVoice aiPlannerVoice = baseMapper.selectOne(wrapper);
        if (Objects.nonNull(aiPlannerVoice)) {
            if (VoiceStatusEnum.UNREVIEWED.getCode().equals(aiPlannerVoice.getStatus())) {
                File existFile = new File(aiPlannerVoice.getFilePath());
                existFile.delete();
                baseMapper.deleteById(aiPlannerVoice.getId());
            } else {
                throw new AiServerException(ResultCode.STATUS_ERROR);
            }
        }

        File saveFile = FileUtil.saveUploadFile(file, path + fileName);
        if (Objects.isNull(saveFile)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "保存文件失败");
        }

        AiPlannerVoice saveVoice = new AiPlannerVoice();
        saveVoice.setAccount(account);
        saveVoice.setStaffId(staffId);
        saveVoice.setFileUrl(nasUrl + fileName);
        saveVoice.setFilePath(path + fileName);
        this.save(saveVoice);

        return saveVoice.getFileUrl();
    }

    @Override
    public void autoUpdateStatus(String account) {
        LambdaQueryWrapper<AiPlannerVoice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiPlannerVoice::getAccount, account);
        AiPlannerVoice aiPlannerVoice = baseMapper.selectOne(queryWrapper);
        if (Objects.isNull(aiPlannerVoice)) {
            throw new AiServerException(ResultCode.NO_EXIST);
        }
        if (!VoiceStatusEnum.UNREVIEWED.getCode().equals(aiPlannerVoice.getStatus())
                && !VoiceStatusEnum.REJECTED.getCode().equals(aiPlannerVoice.getStatus())) {
            throw new AiServerException(ResultCode.STATUS_ERROR.getCode(), "当前状态无法提交审核或去重新录音");
        }

        LambdaUpdateWrapper<AiPlannerVoice> wrapper = new LambdaUpdateWrapper<>();
        if (VoiceStatusEnum.UNREVIEWED.getCode().equals(aiPlannerVoice.getStatus())) {
            wrapper.set(AiPlannerVoice::getStatus, VoiceStatusEnum.UNDER_REVIEW.getCode());
        } else {
            wrapper.set(AiPlannerVoice::getStatus, VoiceStatusEnum.UNREVIEWED.getCode());
        }
        wrapper.eq(AiPlannerVoice::getId, aiPlannerVoice.getId());
        this.update(wrapper);
    }

    @Override
    public AiPlannerVoice get(String account) {
        LambdaQueryWrapper<AiPlannerVoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerVoice::getAccount, account);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public List<AiPlannerVoiceListRes> list(String email, Integer status) {
        return aiPlannerVoiceMapper.selectListByConditions(email, status);
    }

    @Override
    public void audit(String account, Long id, Integer status) {
        AiPlannerVoice aiPlannerVoice = baseMapper.selectById(id);
        if (Objects.isNull(aiPlannerVoice)) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        if (!VoiceStatusEnum.UNDER_REVIEW.getCode().equals(aiPlannerVoice.getStatus())) {
            throw new AiServerException(ResultCode.STATUS_ERROR.getCode(), "当前声音的状态不是审核中");
        }

        LambdaUpdateWrapper<AiPlannerVoice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(AiPlannerVoice::getStatus, status).set(AiPlannerVoice::getAuditAccount, account)
                .eq(AiPlannerVoice::getId, id);
        if (VoiceStatusEnum.APPROVED.getCode().equals(status)) {
            wrapper.set(AiPlannerVoice::getVoiceId, voiceRegister(aiPlannerVoice, null));
        }
        update(wrapper);
    }

    @Override
    public String voiceRegister(AiPlannerVoice aiPlannerVoice, AiPromptRecordVo aiPromptRecordVo) {
        File file = new File(aiPlannerVoice.getFilePath());
        if (!file.exists()) {
            throw new AiServerException(ResultCode.NO_EXIST.getCode(), "文件不存在");
        }
        if (Objects.isNull(aiPromptRecordVo) || aiPromptRecordVo.getOpen()) {
            file = deNoise(file, Objects.isNull(aiPromptRecordVo) ? null : aiPromptRecordVo.getSampleRate());
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.add("Authorization", chatApiKey);
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("file", new FileSystemResource(file));
        map.add("userKey", aiPlannerVoice.getAccount());
        map.add("userType", "Financier");
        map.add("userRegion", "YiRenYouXuan");
        map.add("userVoiceUuid", StrUtil.isBlank(aiPlannerVoice.getVoiceUuid()) ? UUID.randomUUID().toString() : aiPlannerVoice.getVoiceUuid());
        map.add("modelName", StrUtil.isBlank(aiPlannerVoice.getModel()) ? "ResNetSE" : aiPlannerVoice.getModel());
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(map, headers);
        String response = null;
        String reqUrl = chatApiBaseUrl + "/voice/register";
        try {
            response = restTemplateVoice.postForEntity(reqUrl, request, String.class).getBody();
        } catch (Exception e) {
            log.error("调用ChatAPI接口进行声纹注册发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.VOICE_REGISTER_FAIL);
        } finally {
            if (Objects.isNull(aiPromptRecordVo) || aiPromptRecordVo.getOpen()) {
                file.delete();
            }
        }

        if (StrUtil.isBlank(response)) {
            throw new AiServerException(ResultCode.VOICE_REGISTER_FAIL);
        }
        ChatApiResult<VoiceRegisterResponse> voiceRegisterRes = JSON.parseObject(response, new TypeReference<ChatApiResult<VoiceRegisterResponse>>() {});
        if (Objects.isNull(voiceRegisterRes) || !"200".equals(voiceRegisterRes.getCode()) || !voiceRegisterRes.getSuccess()
                || Objects.isNull(voiceRegisterRes.getData()) || StrUtil.isBlank(voiceRegisterRes.getData().getUuid())) {
            log.info("调用ChatAPI接口进行声纹注册 response {}", response);
            throw new AiServerException(ResultCode.VOICE_REGISTER_FAIL);
        }
        return voiceRegisterRes.getData().getUuid();
    }

    @Override
    public File deNoise(File file, Integer sampleRate) {
        List<File> fileList = new ArrayList<>();
        try {
            fileList.addAll(FileUtil.splitAudio(file.getAbsolutePath(), 10 * 60, 2 * 60));

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String fileNameAfter = LocalDate.now().format(formatter) + "_" + RandomUtil.randomString(10) + ".wav";
            File fileAfter = new File(file.getParent(), fileNameAfter);
            for (File perFile : fileList) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                headers.add("Authorization", chatApiKey);
                MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
                map.add("file", new FileSystemResource(perFile));
                map.add("sampleRate", Objects.isNull(sampleRate) ? 16 : sampleRate);
                map.add("chunkDuration", 1);
                map.add("removeSilence", "N");
                HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(map, headers);
                String response = null;
                String reqUrl = chatApiBaseUrl + "/voice/denoise";
                try {
                    log.info("调用ChatAPI接口进行降噪开始,fileName={}", perFile.getName());
                    response = restTemplateVoice.postForEntity(reqUrl, request, String.class).getBody();
                    log.info("调用ChatAPI接口进行降噪结束,fileName={}", perFile.getName());
                } catch (Exception e) {
                    log.error("调用ChatAPI接口进行降噪发生异常,异常原因为", e);
                    throw new AiServerException(ResultCode.VOICE_DENOICE_FAIL);
                }

                if (StringUtils.isBlank(response)) {
                    throw new AiServerException(ResultCode.VOICE_DENOICE_FAIL);
                }
                ChatApiResult<VoiceDenoiseResponse> voiceDenoiseRes = JSON.parseObject(response, new TypeReference<ChatApiResult<VoiceDenoiseResponse>>() {});
                if (Objects.isNull(voiceDenoiseRes) || !"200".equals(voiceDenoiseRes.getCode()) || !voiceDenoiseRes.getSuccess()
                        || Objects.isNull(voiceDenoiseRes.getData()) || StringUtils.isBlank(voiceDenoiseRes.getData().getAudio())) {
                    log.info("调用ChatAPI接口进行降噪 response {}", response);
                    throw new AiServerException(ResultCode.VOICE_DENOICE_FAIL);
                }

                boolean handle = FileUtil.appendBase4ToAudioFile(voiceDenoiseRes.getData().getAudio(), fileAfter);
                if (!handle) {
                    throw new AiServerException(ResultCode.VOICE_DENOICE_FAIL.getCode(), "文件写入失败");
                }
            }
            return fileAfter;
        } catch (Exception e) {
            log.error("处理音频降噪发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.VOICE_DENOICE_FAIL.getCode(), e.getMessage());
        } finally {
            if (!CollectionUtils.isEmpty(fileList)) {
                for (File perFile : fileList) {
                    perFile.delete();
                }
            }
        }
    }

    @Override
    public VoiceSearchResponse search(File file, String account, String model) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.add("Authorization", chatApiKey);
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("file", new FileSystemResource(file));
        if (StrUtil.isNotBlank(account)) {
            map.add("userKey", account);
        }
        if (StrUtil.isBlank(model)) {
            map.add("modelName", "ResNetSE");
        } else {
            map.add("modelName", model);
        }
        map.add("userType", "Financier");
        map.add("userRegion", "YiRenYouXuan");
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(map, headers);

        String response = null;
        String reqUrl = chatApiBaseUrl + "/voice/search";
        try {
            response = restTemplateVoice.postForEntity(reqUrl, request, String.class).getBody();
        } catch (Exception e) {
            log.error("调用ChatAPI接口进行声纹检测发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.VOICE_SEARCH_FAIL);
        } finally {
            log.info("调用ChatAPI接口进行声纹检测 response {}", response);
        }

        if (StringUtils.isBlank(response)) {
            throw new AiServerException(ResultCode.VOICE_SEARCH_FAIL);
        }
        ChatApiResult<VoiceSearchResponse> voiceSearchRes = JSON.parseObject(response, new TypeReference<ChatApiResult<VoiceSearchResponse>>() {});
        if (Objects.isNull(voiceSearchRes) || !"200".equals(voiceSearchRes.getCode()) || !voiceSearchRes.getSuccess()
                || Objects.isNull(voiceSearchRes.getData()) || Objects.isNull(voiceSearchRes.getData().getDist())) {
            throw new AiServerException(ResultCode.VOICE_SEARCH_FAIL);
        }
        return voiceSearchRes.getData();
    }
}
