package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.enums.AiModelType;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 聊天接口
 */
public interface ChatService {

    /**
     * 热门Q&A聊天
     * @param content 提问内容
     * @param size 答案大小
     * @param userAccount 用户
     * @return 提问答案列表
     */
    List<String> chatQuestion(String content, Integer size, String userAccount);

    /**
     * 纯聊天
     * @param question 提示词
     * @param model 模型
     * @param temperature 温度
     * @return 结果
     */
    String chat(String question, String model, Float temperature);

    /**
     * ai小结标签专用模型
     * @param question 提示词
     * @param model 模型
     * @param temperature 温度
     * @param type 类型，0：普通聊天，1：ai小结标签专用模型
     * @return 结果
     */
    String chatAiContent(String question, String model, Float temperature, AiModelType type);

    /**
     * ai小结标签专用模型（支持自定义系统提示词）
     * @param question 提示词
     * @param model 模型
     * @param temperature 温度
     * @param type 类型，0：普通聊天，1：ai小结标签专用模型
     * @param systemPrompt 系统提示词，为空时使用默认系统提示词
     * @return 结果
     */
    String chatAiContent(String question, String model, Float temperature, AiModelType type, String systemPrompt);

    /**
     * 异步纯聊天+主线程等待
     * @param content 提示词
     * @return 结果
     */
    CompletableFuture<String> chatAsync(String content, Float temperature);

    /**
     * 判断聊天
     * @param question 问题
     * @param model 模型
     * @param temperature 温度
     * @return 结果
     */
    String chatJudge(String question, String model, Float temperature);
}
