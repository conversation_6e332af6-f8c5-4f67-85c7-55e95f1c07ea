package com.yirendai.voiceaiserver.service;

import com.yirendai.workbench.entity.AdsCsgSmallFixedAchievementDf;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 业绩达成-类固收相关-小额001 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public interface AdsCsgSmallFixedAchievementDfService extends IService<AdsCsgSmallFixedAchievementDf> {

    List<AdsCsgSmallFixedAchievementDf> getPlannerAchievements();

    List<AdsCsgSmallFixedAchievementDf> getPlannerUsers();
}
