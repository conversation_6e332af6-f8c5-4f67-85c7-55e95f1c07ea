package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;

/**
 * @description AI对话处理Service
 */
public interface AIConversationProcessService {

    /**
     * 事务提交后提交AI解析任务,通过MQ发送
     * @param requestData 请求数据，对话ID必传
     */
    void submitMQTasks(AiConversationProcessReq requestData);

    /**
     * 提交AI解析任务
     * @param requestData 请求数据，对话ID必传
     */
    @Deprecated
    void submitTasks(AiConversationProcessReq requestData);

    /**
     * 提交AI单个解析任务
     */
    void submitTask(AiConversationProcessReq requestData);


    /**
     * 串行提交AI单个解析任务
     */
    void submitSerialTask(AiConversationProcessReq requestData, AiConversationProcessTypeEnum taskType);
}
