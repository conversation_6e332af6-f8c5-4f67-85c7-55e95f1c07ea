package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.workbench.mapper.AiMediaMapper;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.voiceaiserver.model.po.AiChatPO;
import com.yirendai.voiceaiserver.service.AiMediaService;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: AI媒体文件转换实现类
 * @Author: yuanbo
 * @Date: 2024/6/14 13:53
 * @Version: 1.0
 **/
@Slf4j
@Service
@RefreshScope
public class AiMediaServiceImpl implements AiMediaService {
    @Resource
    private AiMediaMapper aiMediaMapper;

    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;

    @Value("${chat-api.url}")
    private String openai_url;

    @Value("${chat-api.key}")
    private String openai_key;

    @Value("${http.proxy.url}")
    private String proxy_url;

    @Value("${http.proxy.port}")
    private int proxy_port;

    // whisper、funasr、ce-asr
    private static final String ai_model = "ce-asr";
    private static final int limit = 1000;
    private static final int connectionTimeout = 15000;
    private static final int readTimeout = 60000;

    @Override
    public void audioHandle() {
        long count = aiMediaMapper.getHandleCount(QwMsgTypeEnum.VOICE.getType());
        // 计算当前需要分多少批去查询
        int batchNum = (int) Math.ceil((double) count / limit);
        for (int i = 0; i < batchNum; i++) {
            List<AiChatPO> list = aiMediaMapper.getHandleList(limit, QwMsgTypeEnum.VOICE.getType());
            list.forEach(po -> {
                File sourceFile = null;
                File targetFile = null;
                try {
                    String originContent = po.getOriginContent();
                    // 1、转化为文件
                    sourceFile = this.urlToFile(originContent, ".amr");
                    if (Objects.isNull(sourceFile)) {
                        return;
                    }
                    // 2、语音文件转换为mp3文件
                    targetFile = this.audioToMp3(sourceFile);
                    if (Objects.isNull(targetFile)) {
                        return;
                    }
                    // 3、mp3文件转换为文字
                    String text = this.audioToText(targetFile, QwMsgTypeEnum.VOICE.getType());
                    // 4、更新处理结果到语料表
                    if (StrUtil.isBlank(text)) {
                        return;
                    }
                    po.setProcessedContent(text);
                    this.updateProcessed(po);
                } finally {
                    // 删除临时文件
                    this.deleteFile(sourceFile, targetFile);
                }
            });
        }
    }

    /**
     * 音频文件转换为MP3格式
     *
     * @param sourceFile 源文件
     * @return 目标文件
     */
    @Override
    public File audioToMp3(File sourceFile) {
        String inputFile = sourceFile.getAbsolutePath(); // 输入AMR文件的路径
        String outputFile = inputFile + ".mp3"; // 输出MP3文件的路径

        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile);
             FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputFile, grabber.getAudioChannels())
        ) {
            grabber.start();

            recorder.setAudioCodec(avcodec.AV_CODEC_ID_MP3);
            recorder.setSampleRate(grabber.getSampleRate());
            recorder.setAudioChannels(grabber.getAudioChannels());
            // 设置音频选项，可以设置比特率等参数
            recorder.setOption("b:a", "192k"); // 设置音频比特率为192kbps
            recorder.start();

            Frame frame;
            while ((frame = grabber.grabFrame()) != null) {
                recorder.record(frame);
            }

            grabber.stop();
            recorder.stop();

            return new File(outputFile);
        } catch (Exception e) {
            log.error("audioToMp3 error ：{}", ExceptionUtil.getMessage(e), e);
        }
        return null;
    }

    /**
     * 语音转文字
     *
     * @param file 源文件
     * @return 文字内容
     */
    @Override
    public String audioToText(File file, String type) {
        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + openai_key);

        // 设置请求体中的参数
        Map<String, Object> formParams = new HashMap<>();
        formParams.put("version", "v2");
        formParams.put("response_format", "json");
        formParams.put("model", ai_model);

        // 将文件添加到表单参数中
        formParams.put("file", file);

        try {
            // 创建 HttpRequest 对象并设置超时时间、请求头和表单参数
            HttpRequest request = HttpUtil.createPost(openai_url + "/v1/audio/translations")
                    .addHeaders(headers)
                    .form(formParams)
                    .setConnectionTimeout(connectionTimeout)  // 连接超时5秒
                    .setReadTimeout(readTimeout);      // 读取超时10秒

            // 执行请求并获取响应
            HttpResponse response = request.execute();

            if (response.isOk()) {
                log.info("audio2Text HttpResponse is : {}", response.body());
                String result = JSONObject.parseObject(response.body()).getString("text");
                if (StrUtil.isBlank(result)) {
                    if (QwMsgTypeEnum.VOICE.getType().equals(type)) {
                        return "[语音]";
                    } else if (QwMsgTypeEnum.VIDEO.getType().equals(type)) {
                        return "[视频]";
                    }
                }
                return result;
            } else {
                log.error("audio2Text HttpResponse error. HTTP Status:{}", response.getStatus());
            }

        } catch (Exception e) {
            log.error("audio2Text error :{}", ExceptionUtil.getMessage(e), e);
        }
        return StrUtil.EMPTY;
    }

    /**
     * 文件url链接下载到本地临时路径
     *
     * @param url    地址
     * @param suffix 文件后缀
     * @return 文件对象
     */
    @Override
    public File urlToFile(String url, String suffix) {
        String localPath = UUID.fastUUID() + suffix;
        try {
            HttpResponse response = this.sendGet(url, false);
            if (response.isOk()) {
                // 将响应体写入到本地文件中
                File file = new File(localPath);
                response.writeBody(file);
                log.info("no proxy audio url downloadPath is :[{}] {}", url, file.getAbsolutePath());
                return file;
            }
        } catch (Exception e) {
            log.error("audio urlToFile error : {}", ExceptionUtil.getMessage(e), e);
            try {
                log.info("正常请求异常，用正向代理再请求一次");
                HttpResponse response = this.sendGet(url, true);
                if (response.isOk()) {
                    // 将响应体写入到本地文件中
                    File file = new File(localPath);
                    response.writeBody(file);
                    log.info("proxy audio url downloadPath is :[{}] {}", url, file.getAbsolutePath());
                    return file;
                }
            } catch (Exception exception) {
                log.error("proxy audio urlToFile error : {}", ExceptionUtil.getMessage(exception), exception);
            }
        }
        return null;
    }

    /**
     * 发送get请求
     *
     * @param url     url
     * @param isProxy 是否使用代理
     * @return 响应对象
     */
    private HttpResponse sendGet(String url, boolean isProxy) {
        // 创建 HttpRequest 对象并设置超时时间
        HttpRequest request;
        if (isProxy) {
            request = HttpUtil.createGet(url)
                    .setHttpProxy(proxy_url, proxy_port)
                    // 连接超时5秒
                    .setConnectionTimeout(connectionTimeout)
                    // 读取超时10秒
                    .setReadTimeout(readTimeout);
        } else {
            request = HttpUtil.createGet(url)
                    // 连接超时5秒
                    .setConnectionTimeout(connectionTimeout)
                    // 读取超时10秒
                    .setReadTimeout(readTimeout);
        }
        // 执行请求并获取响应
        return request.execute();
    }


    @Override
    public void videoHandle() {
        long count = aiMediaMapper.getHandleCount(QwMsgTypeEnum.VIDEO.getType());
        // 计算当前需要分多少批去查询
        int batchNum = (int) Math.ceil((double) count / limit);
        for (int i = 0; i < batchNum; i++) {
            List<AiChatPO> list = aiMediaMapper.getHandleList(limit, QwMsgTypeEnum.VIDEO.getType());
            list.forEach(po -> {
                File sourceFile = null;
                File targetFile = null;
                try {
                    String originContent = po.getOriginContent();
                    // 1、转化为文件
                    sourceFile = this.urlToFile(originContent, ".mp4");
                    if (Objects.isNull(sourceFile)) {
                        return;
                    }
                    // 2、视频文件转换为音频文件
                    targetFile = this.videoToAudio(sourceFile);
                    if (Objects.isNull(targetFile)) {
                        return;
                    }
                    // 3、音频文件转换为文字
                    String text = this.audioToText(targetFile, QwMsgTypeEnum.VIDEO.getType());
                    if (StrUtil.isBlank(text)) {
                        return;
                    }
                    // 4、更新处理结果到语料表
                    po.setProcessedContent(text);
                    this.updateProcessed(po);
                } finally {
                    // 删除临时文件
                    this.deleteFile(sourceFile, targetFile);
                }
            });
        }
    }

    /**
     * 视频文件转为音频文件
     *
     * @param sourceFile 源文件
     * @return 转换完的文件对象
     */
    @Override
    public File videoToAudio(File sourceFile) {
        String inputFile = sourceFile.getAbsolutePath();  // 输入视频文件的路径
        String outputFile = inputFile + ".mp3";  // 输出MP3文件的路径

        try (
                FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile);
                FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputFile, grabber.getAudioChannels())
        ) {
            grabber.start();

            recorder.setAudioCodec(avcodec.AV_CODEC_ID_MP3);
            recorder.setSampleRate(grabber.getSampleRate());
            recorder.setAudioChannels(grabber.getAudioChannels());
            recorder.setOption("b:a", "192k"); // 设置音频比特率为192kbps

            recorder.start();

            Frame frame;
            while ((frame = grabber.grabFrame()) != null) {
                if (frame.samples != null) { // 只记录音频帧
                    recorder.record(frame);
                }
            }

            grabber.stop();
            recorder.stop();
            return new File(outputFile);
        } catch (Exception e) {
            log.error("videoToAudio error ：{}", ExceptionUtil.getMessage(e), e);
        }
        return null;
    }


    /**
     * 删除临时文件
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     */
    private void deleteFile(File sourceFile, File targetFile) {
        if (sourceFile != null && sourceFile.exists()) {
            boolean isDeleted = sourceFile.delete();
            if (isDeleted) {
                log.info("sourceFile isDeleted!");
            }
        }
        if (targetFile != null && targetFile.exists()) {
            boolean isDeleted = targetFile.delete();
            if (isDeleted) {
                log.info("targetFile isDeleted!");
            }
        }
    }

    /**
     * 更新处理结果到语料表
     *
     * @param po 处理对象
     */
    private void updateProcessed(AiChatPO po) {
        AiPlannerChatContact entity = new AiPlannerChatContact();
        entity.setId(po.getId());
        entity.setProcessedContent(po.getProcessedContent());
        aiPlannerChatContactMapper.updateById(entity);
    }
}
