package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.workbench.mapper.AiPlannerTopUserMapper;
import com.yirendai.workbench.mapper.AiPlannerVoiceInfoMapper;
import com.yirendai.workbench.entity.AiPlannerVoiceInfo;
import com.yirendai.voiceaiserver.service.AiPlannerVoiceInfoService;
import com.yirendai.voiceaiserver.util.AudioSplitUtil;
import com.yirendai.voiceaiserver.util.FileUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.AiPlannerTopUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * <p>
 * 理财师声纹信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
@RefreshScope
@Transactional
public class AiPlannerVoiceInfoServiceImpl extends ServiceImpl<AiPlannerVoiceInfoMapper, AiPlannerVoiceInfo> implements AiPlannerVoiceInfoService {

    @Value("${nas.path}")
    private String path;
    @Value("${nas.url}")
    private String nasUrl;
    @Value("${yr.tenant.id}")
    private String yrTenantId;

    @Resource
    AiPlannerTopUserMapper aiPlannerTopUserMapper;

    @Override
    public void saveOrUpdate(String plannerNo, MultipartFile file, String tenantId) {
        if (file.isEmpty()) {
            throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "文件不可为空");
        }
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName) || !fileName.toLowerCase().endsWith(".wav") && !fileName.toLowerCase().endsWith(".mp3")) {
            throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "文件必须为wav格式或mp3格式");
        }

        tenantId = StrUtil.isBlank(tenantId) ? yrTenantId : tenantId;
        LambdaQueryWrapper<AiPlannerTopUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerTopUser::getPlannerNo, plannerNo).eq(AiPlannerTopUser::getTenantId, tenantId);
        if (aiPlannerTopUserMapper.countWithCustomTenant(wrapper) < 1) {
            throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "理财师工号异常");
        }

        fileName = tenantId  + "_" + plannerNo + "_" + RandomUtil.randomString(10) + fileName.substring(fileName.lastIndexOf("."));
        File saveFile = FileUtil.saveUploadFile(file, path + "plannerVoice/" + fileName);
        if (Objects.isNull(saveFile)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "保存文件失败");
        }

        if (fileName.toLowerCase().endsWith(".wav")) {
            double duration;
            try {
                duration = AudioSplitUtil.getWavDuration(saveFile);
            } catch (Exception e) {
                log.error("获取音频时长发生异常，异常原因为", e);
                saveFile.delete();
                throw new AiServerException(ResultCode.FAILURE.getCode(), "获取音频时长失败");
            }
            if (duration < 15 || duration > 40) {
                saveFile.delete();
                throw new AiServerException(ResultCode.FAILURE.getCode(), "WAV格式的音频时长校验不通过，音频时长范围为15~40s");
            }
        }

        try {
            LambdaQueryWrapper<AiPlannerVoiceInfo> infoWrapper = new LambdaQueryWrapper<>();
            infoWrapper.eq(AiPlannerVoiceInfo::getPlannerNo, plannerNo).eq(AiPlannerVoiceInfo::getChannelId, tenantId)
                    .eq(AiPlannerVoiceInfo::getIsUse, Boolean.TRUE);
            AiPlannerVoiceInfo voiceInfo = getOne(infoWrapper);
            if (Objects.nonNull(voiceInfo)) {
                LambdaUpdateWrapper<AiPlannerVoiceInfo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(AiPlannerVoiceInfo::getIsUse, Boolean.FALSE).eq(AiPlannerVoiceInfo::getId, voiceInfo.getId());
                update(updateWrapper);
            }

            AiPlannerVoiceInfo info = new AiPlannerVoiceInfo();
            info.setPlannerNo(plannerNo);
            info.setFileUrl(nasUrl + "plannerVoice/" + fileName);
            info.setFilePath(path + "plannerVoice/" + fileName);
            info.setChannelId(tenantId);
            save(info);
        } catch (Exception e) {
            log.error("上传或更新声纹信息发生异常，异常原因为", e);
            saveFile.delete();
            throw e;
        }
    }

    @Override
    public String check() {
        LambdaQueryWrapper<AiPlannerVoiceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerVoiceInfo::getIsUse, Boolean.TRUE);
        List<AiPlannerVoiceInfo> list = list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return "待校验列表为空";
        }

        List<Map<String, Object>> res = new ArrayList<>();
        for (AiPlannerVoiceInfo info : list) {
            try {
                double duration = AudioSplitUtil.getWavDuration(new File(info.getFilePath()));
                if (duration < 15 || duration > 40) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("plannerNo", info.getPlannerNo());
                    map.put("duration", duration);
                    res.add(map);
                }
            } catch (Exception e) {
                log.error("获取理财师{}声纹音频时长失败", info.getPlannerNo(), e);
            }
        }
        return JSON.toJSONString(res);
    }

}
