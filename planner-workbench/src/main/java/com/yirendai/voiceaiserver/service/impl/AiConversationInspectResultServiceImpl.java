package com.yirendai.voiceaiserver.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.biz.ContentCheckBiz;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.model.contentcheck.MatchResult;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.service.AiConversationInspectResultService;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.mapper.AiConversationInspectDetailMapper;
import com.yirendai.workbench.mapper.AiConversationInspectResultMapper;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_inspect_result(ai对话内容稽查结果表)】的数据库操作Service实现
* @createDate 2024-11-13 18:59:09
*/
@Slf4j
@Service
public class AiConversationInspectResultServiceImpl extends ServiceImpl<AiConversationInspectResultMapper, AiConversationInspectResult>
implements AiConversationInspectResultService {

}
