package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.IResultCode;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.task.AiRetryableTask;
import com.yirendai.voiceaiserver.util.TaskIdUtil;
import com.yirendai.workbench.entity.AiConversationProcessTask;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiConversationProcessTaskMapper;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ai_conversation_process_task(ai对话内容处理任务表)】的数据库操作Service实现
 * @createDate 2024-11-19 11:00:33
 */
@Slf4j
@Service
public class AiConversationProcessTaskServiceImpl extends ServiceImpl<AiConversationProcessTaskMapper, AiConversationProcessTask>
        implements AiConversationProcessTaskService {

    @Resource
    private AiConversationProcessTaskMapper aiConversationProcessTaskMapper;


    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;

    @Resource
    private JedisCluster jedisCluster;

    /**
     * 任务补漏
     */
    @Override
    public Integer checkLeakyDataTask(Integer processType, String tenantId) {
        int insertCount = 0;
        int batchSize = 200;
        int currentPage = 1;
        boolean hasNext = true;
        if (processType == null || processType < 0) {
            log.error("ai历史音转文到小结查漏任务失败, processType 为空");
        }
        AiConversationProcessTypeEnum typeEnum = AiConversationProcessTypeEnum.getByCode(processType);
        log.info("开始ai历史音转文到小结查漏任务, processType: {}", processType);

        while (hasNext) {
            // 创建分页对象
            Page<AiPlannerChatContact> page = new Page<>(currentPage, batchSize);

            // 定义查询条件
            LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.CALL_CENTER.getType())
                    .ne(AiPlannerChatContact::getProcessedContent, "[新呼叫中心语音]")
                    .isNotNull(AiPlannerChatContact::getProcessedContent)
                    .eq(AiPlannerChatContact::getTenantId, tenantId)
                    .orderByAsc(AiPlannerChatContact::getId);

            // 获取分页结果
            IPage<AiPlannerChatContact> aiPlannerChatContactPage = aiPlannerChatContactMapper.selectPage(page, queryWrapper);
            List<AiPlannerChatContact> records = aiPlannerChatContactPage.getRecords();

            if (records.isEmpty()) {
                break;
            }

            // 生成 task_ids 并映射到对应的记录
            List<String> taskIds = new ArrayList<>();
            Map<String, String> taskIdToTenantId = new HashMap<>();
            for (AiPlannerChatContact contact : records) {
                String taskId = buildTaskId(contact.getId(), typeEnum);
                taskIds.add(taskId);
                taskIdToTenantId.put(taskId, contact.getTenantId());
            }

            // 查询已存在的 task_ids
            LambdaQueryWrapper<AiConversationProcessTask> taskQueryWrapper = new LambdaQueryWrapper<>();
            taskQueryWrapper.in(AiConversationProcessTask::getTaskId, taskIds);
            List<AiConversationProcessTask> existingTasks = aiConversationProcessTaskMapper.selectList(taskQueryWrapper);
            Set<String> existingTaskIds = existingTasks.stream()
                    .map(AiConversationProcessTask::getTaskId)
                    .collect(Collectors.toSet());

            // 过滤出需要插入的新任务
            List<AiConversationProcessTask> newTasks = new ArrayList<>();
            for (String taskId : taskIds) {
                if (!existingTaskIds.contains(taskId)) {
                    AiConversationProcessTask task = new AiConversationProcessTask();
                    task.setTaskId(taskId);
                    task.setTaskType(extractTypeFromTaskId(taskId));
                    task.setRetryCount(0);
                    task.setMaxRetry(3);
                    task.setLastFailReason("查漏任务");
                    task.setTenantId(taskIdToTenantId.get(taskId));
                    task.setTaskStatus(AiConversationProcessStatusEnum.FAILED.getCode());
                    task.setCreateTime(LocalDateTime.now());
                    task.setUpdateTime(LocalDateTime.now());
                    newTasks.add(task);
                }
            }

            if (!newTasks.isEmpty()) {
                // 批量插入新任务
                try {
                    int i = aiConversationProcessTaskMapper.insertBatch(newTasks);
                    insertCount += i;
                    log.info("批次插入查漏任务成功, processType: {}, selectCount:{}, insertCount: {}", processType, taskIds.size(), i);
                } catch (Exception e) {
                    log.error("批次插入查漏任务失败, processType: {}, taskIds: {}", processType, taskIds, e);
                }
            } else {
                log.info("该批次无需插入查漏任务, processType: {}, selectCount:{}", processType, taskIds.size());
            }

            // 判断是否有下一页
            if (records.size() < batchSize) {
                hasNext = false;
            } else {
                currentPage++;
            }
        }
        log.info("ai历史音转文到小结查漏任务完成, processType: {}, insertCount: {}", processType, insertCount);
        return insertCount;
    }

    @Override
    @Transactional
    public void saveTask(AiRetryableTask task, AiConversationProcessStatusEnum status, String lastFailReason) {
        AiConversationProcessTask taskData = task.getTaskData();
        String taskId = task.getTaskId();
        LambdaQueryWrapper<AiConversationProcessTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationProcessTask::getTaskId, taskId);
        AiConversationProcessTask entity = aiConversationProcessTaskMapper.selectOne(queryWrapper);
        if (entity != null) {
            entity.setTaskStatus(status.getCode());
            aiConversationProcessTaskMapper.updateById(entity);
            return;
        }
        entity = new AiConversationProcessTask();
        entity.setTaskId(taskId);
        entity.setTaskType(taskData.getTaskType());
        entity.setTaskStatus(taskData.getTaskStatus());
        entity.setLastRetryTime(taskData.getLastRetryTime());
        entity.setLastFailReason(lastFailReason);
        entity.setRetryCount(taskData.getRetryCount());
        entity.setMaxRetry(3);
        entity.setTaskStatus(status.getCode());
        aiConversationProcessTaskMapper.insert(entity);
    }

    @Override
    public void updateTask(AiRetryableTask task, AiConversationProcessStatusEnum status) {
        AiConversationProcessTask taskData = task.getTaskData();
        if (taskData != null) {
            if (AiConversationProcessTypeEnum.CONTENT_SUMMARY.getCode().equals(taskData.getTaskType())) {
                // 内容小结可能会有缓存，需要清除缓存
                if (taskData.getTaskStatus() >= 2) {
                    String key = taskData.getTaskData();
                    if (!Strings.isNullOrEmpty(key)) {
                        jedisCluster.del(key);
                    }
                }
            }
            taskData.setTaskStatus(status.getCode());
            taskData.setUpdateTime(LocalDateTime.now());
            aiConversationProcessTaskMapper.updateById(taskData);
        }
    }

    @Override
    public List<AiConversationProcessTask> fetchFailedTasks() {
        LambdaQueryWrapper<AiConversationProcessTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationProcessTask::getTaskStatus, AiConversationProcessStatusEnum.FAILED.getCode());
        return aiConversationProcessTaskMapper.selectList(queryWrapper);
    }

    /**
     * 查最久失败的2条数据
     *
     * @return
     */
    @Override
    public List<AiConversationProcessTask> fetchFailedProcessTasks() {
        LambdaQueryWrapper<AiConversationProcessTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationProcessTask::getTaskStatus, AiConversationProcessStatusEnum.FAILED.getCode());
        queryWrapper.orderByAsc(AiConversationProcessTask::getUpdateTime);
        queryWrapper.last("limit 2");
        return aiConversationProcessTaskMapper.selectList(queryWrapper);
    }

    private String serializeTaskData(AiRetryableTask task) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(task);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize task", e);
        }
    }

    @Override
    public AiRetryableTask deserializeTaskData(String taskData) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(taskData, AiRetryableTask.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to deserialize task", e);
        }
    }

    /**
     * 从字符串中提取chatContactId
     * 支持格式：
     * - 旧格式："12-123456"
     * - 数字编码格式："12-0_123456" (呼叫中心)、"12-1_123456" (企微)
     * - 子意图格式："12-0_3_123456" (带子意图类型)
     * - 节点组格式："12-0_3_4_123456" (带子意图类型和节点组)
     *
     * @param taskId 输入字符串
     * @return 提取的数字部分（Long 类型），如果提取失败或格式不符合，返回 null
     */
    @Override
    public Long extractChatContactFromTaskId(String taskId) {
        if (taskId == null || !taskId.contains("-")) {
            return null;
        }
        try {
            // 使用TaskIdUtil统一处理，兼容所有格式
            Long chatContactId = TaskIdUtil.extractChatContactId(taskId);
            if (chatContactId != null) {
                return chatContactId;
            }
            
            // 兼容原有逻辑作为备选
            String afterDash = taskId.substring(taskId.indexOf("-") + 1);
            
            if (afterDash.contains("_")) {
                // 格式可能是：0_123456 或 0_3_123456 或 0_3_4_123456
                String[] parts = afterDash.split("_");
                // 取最后一部分作为chatContactId
                String chatContactIdStr = parts[parts.length - 1].trim();
                return Long.parseLong(chatContactIdStr);
            } else {
                // 旧格式：123456
                return Long.parseLong(afterDash.trim());
            }
        } catch (NumberFormatException e) {
            // 处理数字解析失败
            log.error("Ai Conversation Failed to extract chatContactId from taskId: {}", taskId);
            throw new AiServerException(ResultCode.DB_DATA_ERROR);
        }
    }

    /**
     * 从taskId中提取数据源类型
     * 支持格式：
     * - 数字编码格式："12-0_123456" (呼叫中心)、"12-1_123456" (企微)
     * - 子意图格式："12-0_3_123456" (带子意图类型)
     * - 节点组格式："12-0_3_4_123456" (带子意图类型和节点组)
     * @param taskId 任务ID
     * @return 数据源类型，如"0"(呼叫中心)、"1"(企微)，如果没有则返回null
     */
    @Override
    public String extractSourceTypeFromTaskId(String taskId) {
        if (taskId == null || !taskId.contains("-")) {
            return null;
        }
        try {
            // 使用TaskIdUtil统一处理，兼容所有格式
            String sourceType = TaskIdUtil.extractSourceType(taskId);
            if (sourceType != null) {
                return sourceType;
            }
            
            // 兼容原有逻辑作为备选
            String afterDash = taskId.substring(taskId.indexOf("-") + 1);
            
            if (afterDash.contains("_")) {
                // 格式可能是：0_123456 或 0_3_123456 或 0_3_4_123456
                // 第一个下划线前面的部分总是sourceType
                int underscoreIndex = afterDash.indexOf("_");
                return afterDash.substring(0, underscoreIndex).trim();
            } else {
                // 旧格式没有数据源类型
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to extract sourceType from taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 从字符串中提取最后的数字并转换为 int 类型
     *
     * @param taskId 输入字符串，格式为 "0-数字"
     * @return 提取的数字部分（int 类型），如果提取失败或格式不符合，返回 null
     */
    @Override
    public Integer extractTypeFromTaskId(String taskId) {
        if (taskId == null || !taskId.contains("-")) {
            return null;
        }
        try {
            // 截取 "-" 后的部分
            String[] parts = taskId.split("-");
            if (parts.length != 2) {
                return null;
            }
            // 转换为 Long 类型
            return Integer.parseInt(parts[0].trim());
        } catch (NumberFormatException e) {
            // 处理数字解析失败
            log.error("Ai Conversation Failed to extract number from string: {}", taskId);
            throw new AiServerException(ResultCode.DB_DATA_ERROR);
        }
    }

    // 构造taskID
    @Override
    public String buildTaskId(Long chatContactId, AiConversationProcessTypeEnum taskType) {
        return taskType.getCode() + "-" + chatContactId;
    }

    @Override
    public List<AiConversationProcessTask> fetchPendingWechatProcessTasks(AiConversationProcessStatusEnum  status) {
        LambdaQueryWrapper<AiConversationProcessTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AiConversationProcessTask::getTaskStatus, status.getCode());
        queryWrapper.orderByAsc(AiConversationProcessTask::getUpdateTime);
        queryWrapper.last("limit 2");
        return aiConversationProcessTaskMapper.selectList(queryWrapper);
    }

    @Override
    public AiConversationProcessTask selectByTaskId(String taskId) {
        LambdaQueryWrapper<AiConversationProcessTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationProcessTask::getTaskId, taskId);
        return aiConversationProcessTaskMapper.selectOne(queryWrapper);
    }

    @Override
    public void createProcessTask(String taskId, Long chatContactId, Integer taskType, Integer status, String tenantId) {
        createProcessTask(taskId, chatContactId, taskType, status, tenantId, null);
    }
    
    @Override
    public void createProcessTask(String taskId, Long chatContactId, Integer taskType, Integer status, String tenantId, String sourceType) {
        AiConversationProcessTask task = new AiConversationProcessTask();
        task.setTaskId(taskId);
        task.setTaskType(taskType);
        task.setTaskStatus(status);
        task.setTenantId(tenantId);
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        
        // 数据源类型已经编码在taskId中，无需存储在taskData中
        
        // 检查任务是否已存在
        AiConversationProcessTask existingTask = selectByTaskId(taskId);
        if (existingTask != null) {
            // 如果任务已存在，更新状态和更新时间
            existingTask.setTaskStatus(status);
            existingTask.setUpdateTime(LocalDateTime.now());
            this.updateById(existingTask);
            log.info("任务已存在，更新状态：taskId={}, chatContactId={}, status={}, sourceType={}", taskId, chatContactId, status, sourceType);
            return;
        }
        
        try {
            this.save(task);
            log.debug("成功创建AI对话处理任务：taskId={}, chatContactId={}, taskType={}, sourceType={}", taskId, chatContactId, taskType, sourceType);
        } catch (Exception e) {
            log.error("创建AI对话处理任务失败：taskId={}, chatContactId={}, taskType={}, sourceType={}", taskId, chatContactId, taskType, sourceType, e);
            throw e;
        }
    }

}
