package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.workbench.mapper.AdsCsgBigFixedAchievementDfMapper;
import com.yirendai.workbench.entity.AdsCsgBigFixedAchievementDf;
import com.yirendai.voiceaiserver.service.AdsCsgBigFixedAchievementDfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 业绩达成-类固收相关-大额001 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
public class AdsCsgBigFixedAchievementDfServiceImpl extends ServiceImpl<AdsCsgBigFixedAchievementDfMapper, AdsCsgBigFixedAchievementDf> implements AdsCsgBigFixedAchievementDfService {

    @Override
    public List<AdsCsgBigFixedAchievementDf> getPlannerAchievements() {
        return baseMapper.getPlannerAchievements();
    }

    @Override
    public List<AdsCsgBigFixedAchievementDf> getPlannerUsers() {
        return baseMapper.getPlannerUsers();
    }
}
