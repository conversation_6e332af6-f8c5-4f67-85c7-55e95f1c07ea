package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.entity.AiOpening;
import com.yirendai.voiceaiserver.vo.db.AiOpeningBehaviorVO;
import com.yirendai.voiceaiserver.vo.request.BasePageReq;
import com.yirendai.voiceaiserver.vo.request.OpeningBehaviorReq;
import com.yirendai.voiceaiserver.vo.request.OpeningSearchReq;
import com.yirendai.voiceaiserver.vo.response.OpeningSearchRes;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ai开场白 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-24
 */
public interface AiOpeningService extends IService<AiOpening> {

    R<OpeningSearchRes> getRandomTopic(Integer size);

    R<OpeningSearchRes> getRandomOpening(Integer size, String userAccount);

    R<OpeningSearchRes> search(OpeningSearchReq request);

    R<Object> behavior(OpeningBehaviorReq request);

    R<IPage<AiOpeningBehaviorVO>> getPage(BasePageReq request);
}
