package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.workbench.mapper.AdsQwSfixedOrderDetailDfMapper;
import com.yirendai.workbench.entity.AdsQwSfixedOrderDetailDf;
import com.yirendai.voiceaiserver.service.AdsQwSfixedOrderDetailDfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 新客团队小额001佣金订单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
public class AdsQwSfixedOrderDetailDfServiceImpl extends ServiceImpl<AdsQwSfixedOrderDetailDfMapper, AdsQwSfixedOrderDetailDf> implements AdsQwSfixedOrderDetailDfService {

    @Override
    public List<AdsQwSfixedOrderDetailDf> getPlannerAchievements() {
        return baseMapper.getPlannerAchievements();
    }

    @Override
    public List<AdsQwSfixedOrderDetailDf> getPlannerUsers() {
        return baseMapper.getPlannerUsers();
    }
}
