package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneVO;
import com.yirendai.workbench.entity.AiAnalysisConfigScene;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * ai分析场景配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface IAiAnalysisConfigSceneService extends IService<AiAnalysisConfigScene> {

    /**
     * 根据用户ID查询场景列表
     *
     * @param tenantId
     * @param userId
     * @return
     */
    List<AiAnalysisConfigScene> listByUserId(String tenantId, Long userId);

    /**
     * 根据ID和用户ID查询场景
     * @param id
     * @param userId
     * @return
     */
    AiAnalysisConfigScene getByIdAndUser(Long id, Long userId);

    /**
     * 查询场景列表(带结果选项)
     * @param tenantId
     * @param userId
     * @return
     */
    List<AiAnalysisConfigSceneVO> listAll(String tenantId, Long userId);

    /**
     * 根据场景名称查询场景数量
     * @param sceneName
     * @param tenantId
     * @param userId
     * @return
     */
    int selectCountBySceneName(String sceneName, String tenantId, Long userId,Long excludeId);
}
