package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.workbench.mapper.AiIntentMapper;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.voiceaiserver.model.po.AiChatPO;
import com.yirendai.voiceaiserver.service.AiIntentService;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: AI媒体文件转换实现类
 * @Author: yuanbo
 * @Date: 2024/6/14 13:53
 * @Version: 1.0
 **/
@Slf4j
@Service
@RefreshScope
public class AiIntentServiceImpl implements AiIntentService {
    @Resource
    private AiIntentMapper aiIntentMapper;

    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;

    @Value("${chat-api.url}")
    private String openai_url;

    @Value("${chat-api.key}")
    private String openai_key;

    // qwen-turbo、llama3-turbo、gpt系列
    private static final String ai_model = "qwen-turbo";
    private static final int limit = 1000;
    private static final int connectionTimeout = 15000;
    private static final int readTimeout = 60000;
    private static final List<String> ranges;

    static {
        // 初始化意图标签
        List<String> tempList = new ArrayList<>();
        tempList.add("疑虑");
        tempList.add("认可");
        tempList.add("询问价格");
        tempList.add("询问产品");
        tempList.add("询问收益");
        tempList.add("高购买意图");
        tempList.add("中购买意图");
        tempList.add("低购买意图");
        tempList.add("其他");
        ranges = Collections.unmodifiableList(tempList);
    }

    @Override
    public void chatIntentHandle() {
        long count = aiIntentMapper.getHandleCount();
        // 计算当前需要分多少批去查询
        int batchNum = (int) Math.ceil((double) count / limit);
        for (int i = 0; i < batchNum; i++) {
            List<AiChatPO> list = aiIntentMapper.getHandleList(limit);
            list.forEach(po -> {
                String intention;
                if (QwMsgTypeEnum.TEXT.getType().equals(po.getOriginType())) {
                    intention = this.getIntentFromText(po.getOriginContent());
                } else {
                    if (QwMsgTypeEnum.MEETING_VOICE_CALL.getType().equals(po.getOriginType())
                            || QwMsgTypeEnum.AVAYA.getType().equals(po.getOriginType())) {
                        if (StrUtil.isBlank(po.getProcessedContent())) {
                            return;
                        }
                        // 提取客户的话术
                        List<String> customerChats = this.getCustomerChat(po.getProcessedContent());
                        // 意图分析打标
                        List<String> tags = customerChats.stream()
                                .map(this::getIntentFromText)
                                .collect(Collectors.toList());
                        intention = String.join(",", tags);
                    } else {
                        intention = this.getIntentFromText(po.getProcessedContent());
                    }
                }
                if (StrUtil.isBlank(intention)) {
                    return;
                }
                // 更新语料记录
                po.setMsgTag(intention);
                this.updateProcessed(po);
            });
        }
    }

    // 提取语音通话中客户的话术
    private List<String> getCustomerChat(String content) {
        List<String> msgList = new ArrayList<>();
        // 正则表达式匹配以“客户:”开头的行
        Pattern pattern = Pattern.compile("(?m)^客户:(.*)");
        Matcher matcher = pattern.matcher(content.replace(" ", ""));

        while (matcher.find()) {
            // 提取匹配到的内容，并去掉前面的“客户:”部分
            msgList.add(matcher.group(1).trim());
        }
        return msgList;
    }

    @Override
    public String getIntentFromText(String content) {
        if (StrUtil.isBlank(content)) {
            return StrUtil.EMPTY;
        }
        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + openai_key);
        headers.put("Content-Type", "application/json");

        // 编辑提示词
        String prompt =
                "你是一个意图分析的专家。" +
                        "请对发送的内容进行意图分析，根据意图词库返回一个预置好的意图。" +
                        "注意不要脱离意图的词库的范围，下面是一个意图的词库：" +
                        "[" + String.join(",", ranges) + "]" +
                        "直接返回词库中的内容，不要增加额外的修饰词语。" +
                        "如果无法分析就返回：其他";

        // 创建JSON对象作为请求体
        JSONObject jsonBody = buildRequestBody(content, prompt);

        try {
            // 发送POST请求并获取响应
            HttpResponse response = HttpRequest.post(openai_url + "/v1/chat/completions")
                    .addHeaders(headers)
                    .body(jsonBody.toJSONString())
                    .setConnectionTimeout(connectionTimeout)
                    .setReadTimeout(readTimeout)
                    .execute();

            if (response.isOk()) {
                log.info("getIntentFromText HttpResponse is : {}", response.body());
                JSONObject result = JSONObject.parseObject(response.body());
                return result
                        .getJSONArray("choices")
                        .getJSONObject(0)
                        .getJSONObject("message")
                        .getString("content");
            } else {
                log.error("getIntentFromText HttpResponse error. HTTP Status:{}", response.getStatus());
            }
        } catch (Exception e) {
            log.error("getIntentFromText error :{}", ExceptionUtil.getMessage(e), e);
        }
        return StrUtil.EMPTY;
    }

    private JSONObject buildRequestBody(String content, String prompt) {
        List<JSONObject> messages = new ArrayList<>();

        JSONObject system = new JSONObject();
        system.put("role", "system");
        system.put("content", prompt);
        messages.add(system);

        JSONObject user = new JSONObject();
        user.put("role", "user");
        user.put("content", content);
        messages.add(user);

        JSONObject jsonBody = new JSONObject();
        jsonBody.put("model", ai_model);
        jsonBody.put("messages", messages);
        return jsonBody;
    }

    /**
     * 更新处理结果到语料表
     *
     * @param po 处理对象
     */
    private void updateProcessed(AiChatPO po) {
        AiPlannerChatContact entity = new AiPlannerChatContact();
        entity.setId(po.getId());
        entity.setMsgTag(po.getMsgTag());
        aiPlannerChatContactMapper.updateById(entity);
    }
}
