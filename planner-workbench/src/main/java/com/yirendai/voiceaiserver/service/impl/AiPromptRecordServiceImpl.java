package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.workbench.mapper.AiPromptRecordMapper;
import com.yirendai.workbench.entity.AiPromptRecord;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.vo.request.PromptConfigReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AiPromptRecordServiceImpl extends ServiceImpl<AiPromptRecordMapper, AiPromptRecord> implements AiPromptRecordService {

    @Resource
    AiPromptRecordMapper aiPromptRecordMapper;

    @Override
    public AiPromptRecordVo getLatestPromptRecordByScene(int sceneType) {
        LambdaQueryWrapper<AiPromptRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiPromptRecord::getScene, sceneType)
                .orderByDesc(AiPromptRecord::getCreateTime)
                .last("LIMIT 1");
        AiPromptRecord aiPromptRecord = aiPromptRecordMapper.selectOne(queryWrapper);
        return Optional.ofNullable(aiPromptRecord)
                .map(this::convertToVo)
                .orElseThrow(() ->new AiServerException(ResultCode.NO_EXIST.getCode(), "未配置当前场景参数"));
    }

    @Override
    public IPage<AiPromptRecordVo> getHistoryPromptRecord(PromptConfigReq promptConfigReq) {
        LambdaQueryWrapper<AiPromptRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiPromptRecord::getScene, promptConfigReq.getScene())
                .eq(StringUtils.isNotBlank(promptConfigReq.getModel()), AiPromptRecord::getModel, promptConfigReq.getModel())
                .eq(Objects.nonNull(promptConfigReq.getTemperature()), AiPromptRecord::getTemperature, promptConfigReq.getTemperature())
                .ge(StringUtils.isNotBlank(promptConfigReq.getStartTime()), AiPromptRecord::getCreateTime, promptConfigReq.getStartTime())
                .le(StringUtils.isNotBlank(promptConfigReq.getEndTime()), AiPromptRecord::getCreateTime, promptConfigReq.getEndTime())
                .orderByDesc(AiPromptRecord::getCreateTime);
        IPage<AiPromptRecord> page = this.page(new Page<>(promptConfigReq.getCurrent(), promptConfigReq.getSize()), queryWrapper);
        Page<AiPromptRecordVo> pageResult = new Page<>();
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setCurrent(page.getCurrent());
        pageResult.setSize(page.getSize());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            pageResult.setRecords(Collections.EMPTY_LIST);
        }
        List<AiPromptRecordVo> list = page.getRecords()
                .stream()
                .map(r -> convertToVo(r))
                .collect(Collectors.toList());
        pageResult.setRecords(list);
        return pageResult;
    }

    @Override
    public boolean validatePromptConfig(PromptConfigReq promptConfigReq) {
        if (Objects.isNull(promptConfigReq) || Objects.isNull(promptConfigReq.getScene())) {
            return false;
        }
        Integer sceneType = promptConfigReq.getScene();
        if (sceneType.equals(PromptSceneEnum.SUMMARY_SIGNAL_AVAYA_CONTENT.getCode())) {
           return checkSummaryConfig(promptConfigReq);
        }else if (sceneType.equals(PromptSceneEnum.VOICE_TO_TEXT.getCode())) {
           return checkVoiceToTextConfig(promptConfigReq);
        }else if (sceneType.equals(PromptSceneEnum.DENOISE.getCode())) {
            return checkDenoiseConfig(promptConfigReq);
        }else if (sceneType.equals(PromptSceneEnum.VOICE_SEARCH.getCode())) {
            return checkVoiceSearchConfig(promptConfigReq);
        }else if (sceneType.equals(PromptSceneEnum.USER_TAG.getCode())) {
            return checkUserTagConfig(promptConfigReq);
        } else if (PromptSceneEnum.CALL_CENTER_TRANSLATE.getCode().equals(sceneType)) {
            return checkCallCanterTranslateConfig(promptConfigReq);
        } else if (sceneType.equals(PromptSceneEnum.HOUYU_VOICE_TO_TEXT.getCode())) {
            return checkVoiceToTextConfig(promptConfigReq);
        }else if (sceneType.equals(PromptSceneEnum.HOUYU_DENOISE.getCode())) {
            return checkDenoiseConfig(promptConfigReq);
        }else if (sceneType.equals(PromptSceneEnum.HOUYU_VOICE_SEARCH.getCode())) {
            return checkVoiceSearchConfig(promptConfigReq);
        }
        return true;
    }

    private boolean checkUserTagConfig(PromptConfigReq promptConfigReq) {
        if (StringUtils.isBlank(promptConfigReq.getPrompt()) || StringUtils.isBlank(promptConfigReq.getModel()) || Objects.isNull(promptConfigReq.getTemperature())) {
            log.info("用户标签打标配置校验未通过,提示词,模型或温度不能为空。配置为:{}", JSON.toJSONString(promptConfigReq));
            return false;
        }
        return true;
    }


    private AiPromptRecordVo convertToVo(AiPromptRecord aiPromptRecord) {
        AiPromptRecordVo aiPromptRecordVo = new AiPromptRecordVo();
        BeanUtils.copyProperties(aiPromptRecord, aiPromptRecordVo);
        return aiPromptRecordVo;
    }

    private boolean checkSummaryConfig(PromptConfigReq promptConfigReq) {
        if (StringUtils.isBlank(promptConfigReq.getPrompt()) || StringUtils.isBlank(promptConfigReq.getModel()) || Objects.isNull(promptConfigReq.getTemperature())) {
            log.info("单条avaya聊天内容小结配置校验未通过,提示词,模型或温度不能为空。配置为:{}", JSON.toJSONString(promptConfigReq));
            return false;
        }
        return true;
    }

    private boolean checkVoiceToTextConfig(PromptConfigReq promptConfigReq) {
        if (StringUtils.isBlank(promptConfigReq.getHotWords()) || StringUtils.isBlank(promptConfigReq.getModel())) {
            log.info("语音转文字配置校验未通过,热词或模型不能为空。配置为:{}", JSON.toJSONString(promptConfigReq));
            return false;
        }
        return true;
    }

    private boolean checkDenoiseConfig(PromptConfigReq promptConfigReq) {
        if (promptConfigReq.getOpen() && Objects.isNull(promptConfigReq.getSampleRate())) {
            log.info("降噪配置校验未通过, 开启降噪时, 采样率不能为空。配置为:{}", JSON.toJSONString(promptConfigReq));
            return false;
        }else if (!promptConfigReq.getOpen() && Objects.nonNull(promptConfigReq.getSampleRate())) {
            log.info("降噪配置校验未通过, 关闭降噪时, 采样率必须为空。配置为:{}", JSON.toJSONString(promptConfigReq));
            return false;
        }
        return true;
    }

    private boolean checkVoiceSearchConfig(PromptConfigReq promptConfigReq) {
        boolean existModel = Objects.nonNull(promptConfigReq.getModel());
        boolean existDist1 = Objects.nonNull(promptConfigReq.getFirstRoundDist());
        boolean existDist2 = Objects.nonNull(promptConfigReq.getSecondRoundDist());
        if (promptConfigReq.getOpen() && existModel && existDist1 && existDist2) {
            return true;
        }else if (!promptConfigReq.getOpen() && !existModel && !existDist1 && !existDist2) {
            return true;
        }
        log.info("声纹检测配置校验未通过,配置为:{}", JSON.toJSONString(promptConfigReq));
        return false;
    }

    private boolean checkCallCanterTranslateConfig(PromptConfigReq promptConfigReq) {
        return Objects.nonNull(promptConfigReq.getOpen()) && Objects.nonNull(promptConfigReq.getSegmentLong())
                && checkDenoiseConfig(promptConfigReq) && checkVoiceToTextConfig(promptConfigReq);
    }
}
