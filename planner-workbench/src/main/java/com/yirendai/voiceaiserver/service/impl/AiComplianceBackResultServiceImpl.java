package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.AiComplianceBackResult;
import com.yirendai.voiceaiserver.service.AiComplianceBackResultService;
import com.yirendai.workbench.mapper.AiComplianceBackResultMapper;
import org.springframework.stereotype.Service;

/**
 * AI合规回访结果表(AiComplianceBackResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14 16:21:39
 */
@Service
public class AiComplianceBackResultServiceImpl extends ServiceImpl<AiComplianceBackResultMapper, AiComplianceBackResult> implements AiComplianceBackResultService {

}

