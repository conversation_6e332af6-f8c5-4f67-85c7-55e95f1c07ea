package com.yirendai.voiceaiserver.service.impl;

import java.io.File;
import java.util.Objects;
import javax.annotation.Resource;

import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.voiceaiserver.vo.response.AudioToTextVo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @time 2024/5/17 13:29
 **/
@Service
@Slf4j
@RefreshScope
public class AiVoiceToTextService {

    @Value("${chat-api.url}")
    private String url;

    @Value("${chat-api.key}")
    private String key;

    @Resource
    RestTemplate restTemplateConcurrent;

    public String voiceToText(MultipartFile file) {
        String filePath = RandomUtil.randomString(10) + "_" + file.getOriginalFilename();
        File saveFile = FileUtil.saveUploadFile(file, filePath);
        try {
            String text = voiceToText(saveFile, "funasr");
            if (Objects.nonNull(text)) {
                text = text.replaceAll("[\\p{Punct}，。！？；：“”‘’、（）【】《》]", "");
            }
            return text;
        } catch (Exception e) {
            log.error("voiceToText error", e);
            return null;
        } finally {
            if (Objects.nonNull(saveFile) && saveFile.exists()) {
                saveFile.delete();
            }
        }
    }

    public String voiceToText(File file, String model) {

        try {
            JSONObject jsonObject = voiceToTextJson(file, model, null, null, null, null);
            if (Objects.isNull(jsonObject)) {
                return null;
            }
            return jsonObject.getString("text");
        } catch (Exception e) {
            log.error("voiceToText error", e);
            return null;
        }
    }

    /**
     * 调用音转文接口获取片段列表
     * @param file 音频文件
     * @param model 模型名称
     * @param hotwords 热词
     * @param prompt 提示词
     * @param temperature 温度
     * @return 音转文结果信息
     */
    public AudioToTextVo getVoiceSegments(File file, String model, String hotwords, String prompt, Float temperature) {
        JSONObject jsonObject = voiceToTextJson(file, model, 2, hotwords, prompt, temperature);
        if (Objects.isNull(jsonObject)) {
            return null;
        }

        AudioToTextVo textVo = JSONObject.parseObject(JSON.toJSONString(jsonObject), AudioToTextVo.class);
        if (Objects.isNull(textVo)) {
            return null;
        }
        return textVo;
    }

    public JSONObject voiceToTextJson(File file, String model, Integer num, String hotwords, String prompt, Float temperature) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.add("Authorization", key);

            MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
            form.add("file", new FileSystemResource(file));
            form.add("version", "v3");
            form.add("response_format", "json");
            form.add("model", model);
            form.add("hotwords", StrUtil.isBlank(hotwords) ? "固收,类固收,小额001,大额001,宜人,大固,小固,指旺,安易盈,新手专享,新手标,优选,宜优选,认购,起投,券码,福卡,晒单," +
                    "宜人币,奖励金,积分,宜信,逛逛,宜享分,续投,起拍,转投,1688会员,399会员,特供,募集,竞拍,长盈宝,AI会员,小金罐,派息,付息,信贷,日日盈,618," +
                    "信美,首购,代金券,债权,旅居,福袋,长险,药诊卡,提额,旅居卡,宜路相伴,退休金,臻享,宜脉传承,翔旅保,臻选,宜脉相承,宝贝存钱罐,宜脉相传,弘康," +
                    "抽积分,智选,会员日,初冬礼遇,传家有道,宜人优选,健康节,创信,宜她保,社群" : hotwords);
            if (Objects.nonNull(num)) {
                form.add("oracle_num", num);
            }
            if (StrUtil.isNotBlank(prompt)) {
                form.add("prompt", prompt);
            }
            if (Objects.nonNull(temperature)) {
                form.add("temperature", temperature);
            }
            HttpEntity requestEntity = new HttpEntity<>(form, headers);
            log.info("调用ChatAPI接口进行音转文开始,model={},fileName={}", model, file.getName());
            String response = restTemplateConcurrent.postForObject(url + "/v1/audio/transcriptions", requestEntity, String.class);
            log.info("调用ChatAPI接口进行音转文结束,model={},fileName={}", model, file.getName());
            if (StrUtil.isBlank(response)) {
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (Objects.isNull(jsonObject) || Objects.nonNull(jsonObject.getInteger("code")) || StrUtil.isNotBlank(jsonObject.getString("error_message"))) {
                log.info("调用ChatAPI接口进行音转文response={}", response);
                return null;
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("调用ChatAPI接口进行音转文发生异常,异常原因为", e);
            return null;
        }
    }

    public String voiceToSplitText(MultipartFile file) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.add("Authorization", key);

            MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
            ByteArrayResource fileAsResource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }

                @Override
                public long contentLength() {
                    return file.getSize();
                }

                @Override
                public String getDescription() {
                    return file.getName();
                }
            };
            form.add("file", fileAsResource);
            form.add("response_format", "json");
            form.add("version", "v3");
            form.add("model", "ce-asr-1");
            form.add("hotwords", "固收,类固收,小额001,大额001,宜人,大固,小固,指旺,安易盈,新手专享,新手标,优选,宜优选,认购,起投,券码,福卡,晒单," +
                    "宜人币,奖励金,积分,宜信,逛逛,宜享分,续投,起拍,转投,1688会员,399会员,特供,募集,竞拍,长盈宝,AI会员,小金罐,派息,付息,信贷,日日盈,618," +
                    "信美,首购,代金券,债权,旅居,福袋,长险,药诊卡,提额,旅居卡,宜路相伴,退休金,臻享,宜脉传承,翔旅保,臻选,宜脉相承,宝贝存钱罐,宜脉相传,弘康," +
                    "抽积分,智选,会员日,初冬礼遇,传家有道,宜人优选,健康节,创信,宜她保,社群");
            HttpEntity requestEntity = new HttpEntity<>(form, headers);
            log.info("voiceToText start, fileName={}", file.getName());
            String response = restTemplateConcurrent.postForObject(url + "/v1/audio/translations", requestEntity, String.class);
            log.info("voiceToText end, fileName={}, result={}", file.getName(), response);
            AudioToTextVo textVo = JSONObject.parseObject(response, AudioToTextVo.class);
            if (Objects.isNull(textVo) || Objects.nonNull(textVo.getCode()) || StrUtil.isNotBlank(textVo.getError_message())
                    || CollUtil.isEmpty(textVo.getSegments())) {
                return null;
            }

            StringBuilder stringBuilder = new StringBuilder();
            for (AudioToTextVo.Segment segment : textVo.getSegments()) {
                if (Integer.valueOf(1).equals(segment.getSpk())) {
                    stringBuilder.append("客户:").append(segment.getText()).append("\n");
                } else {
                    stringBuilder.append("理财师:").append(segment.getText()).append("\n");
                }
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            log.error("voiceToText error", e);
            return null;
        }
    }
}
