package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationSummaryVO;
import com.yirendai.workbench.entity.AiConversationSummary;

import java.util.List;

/**
 * ai对话内容小结表(AiConversationSummary)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-28 14:59:28
 */
public interface AiConversationSummaryService extends IService<AiConversationSummary> {

    AiConversationSummaryVO selectByChatContactId(String uuid, Integer systemType);

    Boolean feedback(AiFeedbackReq req);

    AiConversationSummaryVO selectByUserId(String userId);
}

