package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.service.AiCustomerTagService;
import com.yirendai.voiceaiserver.service.IAiUserTagCategoryService;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiCustomerTagVO;
import com.yirendai.workbench.entity.AiCustomerTag;
import com.yirendai.workbench.entity.AiUserTagCategory;
import com.yirendai.workbench.mapper.AiCustomerTagMapper;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.UserBasicInfoFacadeService;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.res.UserBasicInfoResponse;
import com.yirendai.workbench.wrapper.CustomerInfoWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
* <AUTHOR>
* @description 针对表【ai_customer_tag(ai客户与标签关联表)】的数据库操作Service实现
* @createDate 2024-11-13 18:52:30
*/
@Slf4j
@Service
public class AiCustomerTagServiceImpl extends ServiceImpl<AiCustomerTagMapper, AiCustomerTag>
implements AiCustomerTagService {

    @Resource
    private AiCustomerTagMapper aiCustomerTagMapper;
    @Resource
    private IAiUserTagCategoryService iAiUserTagCategoryService;
    @Resource
    private TenantServiceHolder tenantServiceHolder;

    @Override
    public List<AiCustomerTagVO> listByUserId(String userId) {
        CustomerInfoWrapper customerInfoWrapper = tenantServiceHolder.getCustomerInfoWrapper();
        SimpleUserInfoDto userBasicInfoByUserId = customerInfoWrapper.getUserBasicInfoByUserId(userId, OwnAuthUtil.getTenantId());
        if (userBasicInfoByUserId == null) {
            log.error("listByUserId failed, user not found, userId: {}", userId);
            throw new AiServerException(ResultCode.USER_NOT_EXIST);
        }
        AiUserTagCategory lastTopTagCategory = iAiUserTagCategoryService.getLastTopTagCategory();
        LambdaQueryWrapper<AiCustomerTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCustomerTag::getUserId, userId);
        queryWrapper.eq(AiCustomerTag::getUserPhone, userBasicInfoByUserId.getMobileNo());
        List<AiCustomerTag> aiCustomerTags = aiCustomerTagMapper.selectList(queryWrapper);
        HashMap<Long, AiCustomerTagVO> map = new HashMap<>();
        if (aiCustomerTags != null && !aiCustomerTags.isEmpty()) {
            for (AiCustomerTag aiCustomerTag : aiCustomerTags) {
                Integer feedbackStatus = aiCustomerTag.getFeedbackStatus();
                // 不显示反馈为不准确的标签
                if (feedbackStatus != null && feedbackStatus == 2) {
                    continue;
                }
                Long topTagCategoryId = aiCustomerTag.getTopTagCategoryId();
                String colorValue = aiCustomerTag.getColorValue();
                String topTagCategoryName = aiCustomerTag.getTopTagCategoryName();
                if (Objects.isNull(topTagCategoryId)) {
                    topTagCategoryId = lastTopTagCategory.getId();
                    topTagCategoryName = lastTopTagCategory.getCategoryName();
                    colorValue = lastTopTagCategory.getColorValue();
                }
                if (map.get(topTagCategoryId) == null) {
                    AiCustomerTagVO aiCustomerTagVO = new AiCustomerTagVO();
                    aiCustomerTagVO.setTopTagCategoryId(topTagCategoryId);
                    aiCustomerTagVO.setTopTagCategoryName(topTagCategoryName);
                    aiCustomerTagVO.setUserId(userId);
                    List<AiCustomerTagVO.AiCustomerTagDetail> aiCustomerTagDetails = new ArrayList<>();
                    aiCustomerTagDetails.add(buildAiCustomerTagDetail(aiCustomerTag, colorValue));
                    aiCustomerTagVO.setAiCustomerTagDetail(aiCustomerTagDetails);
                    aiCustomerTagVO.setTotalWeight(1);
                    map.put(topTagCategoryId, aiCustomerTagVO);
                } else {
                    if (Objects.isNull(topTagCategoryId)) {
                        topTagCategoryId = lastTopTagCategory.getId();
                        colorValue = lastTopTagCategory.getColorValue();
                    }
                    AiCustomerTagVO aiCustomerTagVO = map.get(topTagCategoryId);
                    aiCustomerTagVO.setTotalWeight(aiCustomerTagVO.getTotalWeight() + 1);
                    aiCustomerTagVO.getAiCustomerTagDetail().add(buildAiCustomerTagDetail(aiCustomerTag, colorValue));
                }
            }
            List<AiCustomerTagVO> aiCustomerTagVOList = new ArrayList<>(map.values());
            if (!aiCustomerTagVOList.isEmpty()) {
                aiCustomerTagVOList.sort(Comparator.comparing(AiCustomerTagVO::getTopTagCategoryId));
                return aiCustomerTagVOList;
            }
        }
        return Collections.emptyList();
    }

    private AiCustomerTagVO.AiCustomerTagDetail buildAiCustomerTagDetail(AiCustomerTag aiCustomerTag, String colorValue) {
        return AiCustomerTagVO.AiCustomerTagDetail.builder()
                .id(aiCustomerTag.getId())
                .tagId(aiCustomerTag.getTagId())
                .tagName(aiCustomerTag.getTagName())
                .tagCategoryId(aiCustomerTag.getTagCategoryId())
                .tagCategoryName(aiCustomerTag.getTagCategoryName())
                .weight(1)
                .colorValue(colorValue)
                .feedbackStatus(aiCustomerTag.getFeedbackStatus())
                .build();
    }

    @Override
    public Boolean feedback(AiFeedbackReq req) {
        LambdaQueryWrapper<AiCustomerTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCustomerTag::getId, req.getId());
        AiCustomerTag aiCustomerTag = aiCustomerTagMapper.selectOne(queryWrapper);
        if (aiCustomerTag != null) {
            aiCustomerTag.setFeedbackStatus(req.getFeedbackStatus());
            aiCustomerTag.setFeedbackDetails(req.getFeedbackDetails());
            BladeUser user = OwnAuthUtil.getUser();
            if (user == null) {
                log.error("feedback failed, user not found");
                throw new AiServerException(ResultCode.NO_LOGIN);
            }
            aiCustomerTag.setFeedbackUserId(String.valueOf(user.getUserId()));
            aiCustomerTag.setFeedbackUserName(user.getUserName());
            aiCustomerTag.setFeedbackTime(LocalDateTime.now());
            aiCustomerTag.setUpdateTime(LocalDateTime.now());
            aiCustomerTagMapper.updateById(aiCustomerTag);
            return true;
        }
        log.error("feedback failed, aiCustomerTag not found, id: {}", req.getId());
        throw new AiServerException(ResultCode.NO_USER_TAG);
    }
}
