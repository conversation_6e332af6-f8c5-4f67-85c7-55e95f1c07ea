package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.enums.QuestionContextTypeEnum;
import com.yirendai.workbench.mapper.AiQuestionBehaviorContextMapper;
import com.yirendai.workbench.entity.AiQuestionBehaviorContext;
import com.yirendai.voiceaiserver.service.AiQuestionBehaviorContextService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * ai问题-用户行为-问题&答案 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
public class AiQuestionBehaviorContextServiceImpl extends ServiceImpl<AiQuestionBehaviorContextMapper, AiQuestionBehaviorContext> implements AiQuestionBehaviorContextService {

    @Override
    public AiQuestionBehaviorContext save(String content, Integer type, Long relationId) {
        AiQuestionBehaviorContext context = new AiQuestionBehaviorContext();
        context.setType(type);
        context.setContext(content);
        if (QuestionContextTypeEnum.QUESTION.getCode().equals(type) && Objects.nonNull(relationId)) {
            context.setRelationId(relationId);
        }
        save(context);
        return context;
    }
}
