package com.yirendai.voiceaiserver.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.enums.AiConversationTagTypeEnum;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationTagExportDto;
import com.yirendai.workbench.entity.AiConversationTag;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_tag(ai对话与标签关联表)】的数据库操作Service
* @createDate 2024-11-13 18:58:45
*/
public interface AiConversationTagService extends IService<AiConversationTag> {

    /**
     * 插入AI对话标签
     * @param chatContactId 对话ID
     * @param plannerNo 理财师工号
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param processId 生成标签处理ID
     * @param tagCategoryId 标签分类ID
     * @param tagId 标签ID
     * @param category 标签分类名
     * @param name 标签名
     * @param matchStatus 标签匹配状态
     */
    void insertAiConversationTag(Long chatContactId, String plannerNo, String userId, String tenantId, Long processId, Long tagCategoryId, Long tagId, String category, String name, AiConversationTagTypeEnum matchStatus);


    /**
     * 插入AI对话标签
     * @param req 对话处理请求
     * @param processId 生成标签处理ID
     * @param tagCategoryId 标签分类ID
     * @param tagId 标签ID
     * @param category 标签分类名
     * @param name 标签名
     * @param matchStatus 标签匹配状态
     */
    void insertAiConversationTag(AiConversationProcessReq req, Long processId, Long tagCategoryId, Long tagId, String category, String name, AiConversationTagTypeEnum matchStatus);

    List<AiConversationTagExportDto> getAllDataByMatchStatus(Integer matchStatus);

    void deleteByChatContactId(@NotNull(message = "对话ID不可为空") Long chatContactId);
}
