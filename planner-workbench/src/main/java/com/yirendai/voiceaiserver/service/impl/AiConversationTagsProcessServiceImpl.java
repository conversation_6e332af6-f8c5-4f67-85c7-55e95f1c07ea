package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.*;
import com.yirendai.voiceaiserver.service.*;
import com.yirendai.voiceaiserver.task.conversation.AbstractAiConversationProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【ai_conversation_tags_process(ai对话标签处理过程表)】的数据库操作Service实现
 * @createDate 2024-11-13 18:58:40
 */
@Slf4j
@Service
public class AiConversationTagsProcessServiceImpl extends ServiceImpl<AiConversationTagsProcessMapper, AiConversationTagsProcess>
        implements AiConversationTagsProcessService {

}
