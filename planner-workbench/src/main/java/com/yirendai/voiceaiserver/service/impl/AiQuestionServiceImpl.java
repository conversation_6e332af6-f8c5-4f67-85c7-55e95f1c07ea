package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.QuestionContextTypeEnum;
import com.yirendai.voiceaiserver.enums.QuestionSourceEnum;
import com.yirendai.workbench.mapper.AiQuestionMapper;
import com.yirendai.workbench.entity.AiQuestion;
import com.yirendai.workbench.entity.AiQuestionBehavior;
import com.yirendai.workbench.entity.AiQuestionBehaviorContext;
import com.yirendai.voiceaiserver.service.AiQuestionBehaviorContextService;
import com.yirendai.voiceaiserver.service.AiQuestionBehaviorService;
import com.yirendai.voiceaiserver.service.AiQuestionService;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.vo.request.BasePageReq;
import com.yirendai.voiceaiserver.vo.request.QuestionBehaviorReq;
import com.yirendai.voiceaiserver.vo.request.QuestionSearchReq;
import com.yirendai.voiceaiserver.vo.response.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.enums.BehaviorTypeEnum;
import com.yirendai.voiceaiserver.enums.QuestionTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.yirendai.voiceaiserver.job.TodayQuestionJob.KEY_RANDOM_QUESTION_AND_ANSWER_CACHE;

/**
 * <p>
 * ai问题 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
@Transactional
public class AiQuestionServiceImpl extends ServiceImpl<AiQuestionMapper, AiQuestion> implements AiQuestionService {

    @Resource
    ChatService chatService;
    @Resource
    AiQuestionBehaviorContextService aiQuestionBehaviorContextService;
    @Resource
    AiQuestionBehaviorService aiQuestionBehaviorService;
    @Resource
    JedisCluster jedisCluster;

    @Override
    public R<QuestionSearchRes> getTopicOrRecommendation(Integer size, Long parentId, Integer type) {
        LambdaQueryWrapper<AiQuestion> wrapper = new LambdaQueryWrapper<>();
        if (QuestionTypeEnum.RECOMMENDATION.getCode().equals(type)) {
            wrapper.select(AiQuestion::getId).eq(AiQuestion::getParentId, parentId);
        }
        wrapper.eq(AiQuestion::getType, type);
        List<AiQuestion> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return R.data(QuestionSearchRes.builder().questionList(Collections.emptyList()).build());
        }

        if (QuestionTypeEnum.TOPIC.getCode().equals(type)) {
            Collections.shuffle(list);
            return R.data(QuestionSearchRes.builder().questionList(list).build());
        }
        return R.data(QuestionSearchRes.builder().questionList(getRandomByIds(list, size)).build());
    }

    @Override
    public R<QuestionAndAnswerListRes> getToday() {
        String cache = jedisCluster.get(KEY_RANDOM_QUESTION_AND_ANSWER_CACHE);
        if (StringUtils.isBlank(cache)) {
            return R.fail(ResultCode.FAILURE);
        }
        List<QuestionAndAnswerInfo> resList = JSON.parseArray(cache, QuestionAndAnswerInfo.class);
        if (CollectionUtils.isEmpty(resList)) {
            return R.fail(ResultCode.FAILURE);
        }
        return R.data(QuestionAndAnswerListRes.builder().list(resList).build());
    }

    @Override
    public R<QuestionListRes> getSummaryList(Integer size, String content) {
        LambdaQueryWrapper<AiQuestion> summaryWrapper = new LambdaQueryWrapper<>();
        summaryWrapper.select(AiQuestion::getId)
                .eq(AiQuestion::getType, QuestionTypeEnum.SUMMARY.getCode()).like(AiQuestion::getContext, content);
        List<AiQuestion> summaryList = list(summaryWrapper);
        if (CollectionUtils.isNotEmpty(summaryList)) {
            return R.data(QuestionListRes.builder().questionList(getRandomByIds(summaryList, size)).build());
        }

        LambdaQueryWrapper<AiQuestion> recommendationWrapper = new LambdaQueryWrapper<>();
        recommendationWrapper.eq(AiQuestion::getType, QuestionTypeEnum.RECOMMENDATION.getCode()).eq(AiQuestion::getContext, content);
        AiQuestion recommendation = getOne(recommendationWrapper);
        if (Objects.isNull(recommendation)) {
            return R.data(QuestionListRes.builder().questionList(Collections.emptyList()).build());
        }

        LambdaQueryWrapper<AiQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(AiQuestion::getId)
                .eq(AiQuestion::getType, QuestionTypeEnum.SUMMARY.getCode()).eq(AiQuestion::getParentId, recommendation.getId());
        List<AiQuestion> resList = list(wrapper);
        if (CollectionUtils.isEmpty(resList)) {
            return R.data(QuestionListRes.builder().questionList(Collections.emptyList()).build());
        }
        return R.data(QuestionListRes.builder().questionList(getRandomByIds(resList, size)).build());
    }

    private List<AiQuestion> getRandomByIds(List<AiQuestion> list, Integer size) {
        List<Long> idList = list.stream().map(AiQuestion::getId).distinct().collect(Collectors.toList());
        Collections.shuffle(idList);
        return this.listByIds(idList.subList(0, Math.min(size, idList.size())));
    }

    @Override
    public R<QuestionAndAnswerListRes> search(QuestionSearchReq request) {
        Long relationId = handleSearchCount(request);

        String content = String.format(ChatServiceImpl.CHAT_GET_QUESTION_MSG,
                ChatServiceImpl.CHINESE_NUMBERS[request.getSize() - 1], request.getContent());
        List<String> questionList = chatService.chatQuestion(content, request.getSize(), request.getAccount());

        List<QuestionAndAnswerInfo> res = new ArrayList<>();
        for (String question : questionList) {
            res.add(QuestionAndAnswerInfo.builder().question(question).relationId(relationId).build());
        }
        return R.data(QuestionAndAnswerListRes.builder().list(res).build());
    }

    /**
     * 处理搜索次数，仅记录总结或命中的相关推荐的搜索次数
     * @param request 数据来源+搜索内容
     * @return 关联内容id（总结id或相关推荐id）
     */
    private Long handleSearchCount(QuestionSearchReq request) {
        Set<Long> searchIdSet = new HashSet<>();
        Long relationId = null;
        if (QuestionSourceEnum.MATCHING_LIST.getCode().equals(request.getSource())) {
            AiQuestion summary = getById(Long.valueOf(request.getContent()));
            if (Objects.isNull(summary)) {
                throw new AiServerException(ResultCode.PARAM_INVALID);
            }
            searchIdSet.add(summary.getId());
            request.setContent(summary.getContext());
            relationId = summary.getId();
        } else {
            LambdaQueryWrapper<AiQuestion> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AiQuestion::getType, QuestionTypeEnum.SUMMARY.getCode()).eq(AiQuestion::getContext, request.getContent())
                    .or().eq(AiQuestion::getType, QuestionTypeEnum.RECOMMENDATION.getCode()).eq(AiQuestion::getContext, request.getContent());
            List<AiQuestion> list = list(wrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                if (list.size() == 1) {
                    searchIdSet.add(list.get(0).getId());
                    relationId = list.get(0).getId();
                } else {
                    for (AiQuestion aiQuestion : list) {
                        if (QuestionTypeEnum.SUMMARY.getCode().equals(aiQuestion.getType())) {
                            searchIdSet.add(aiQuestion.getId());
                            relationId = aiQuestion.getId();
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(searchIdSet)) {
            LambdaUpdateWrapper<AiQuestion> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.setSql("search_count = search_count + 1").in(AiQuestion::getId, searchIdSet);
            update(updateWrapper);
        }
        return relationId;
    }

    @Override
    public R<QuestionAndAnswerInfo> answer(String question, Long relationId, String account) {
        QuestionAndAnswerInfo info = QuestionAndAnswerInfo.builder().question(question).relationId(relationId).build();
        String content = question.endsWith("?") || question.endsWith("？") ? question : question + "？";
        String answer = chatService.chatQuestion(String.format(ChatServiceImpl.CHAT_GET_ANSWER_MSG, content), 1, account).get(0);
        info.setAnswerList(Collections.singletonList(QuestionAnswerInfo.builder().answer(answer).build()));
        return R.data(info);
    }

    @Override
    public R<Object> behavior(QuestionBehaviorReq request) {
        LambdaQueryWrapper<AiQuestionBehaviorContext> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiQuestionBehaviorContext::getContext, request.getQuestion())
                .eq(AiQuestionBehaviorContext::getType, QuestionContextTypeEnum.QUESTION.getCode());
        if (Objects.nonNull(request.getRelationId())) {
            wrapper.eq(AiQuestionBehaviorContext::getRelationId, request.getRelationId());
        }
        wrapper.or().eq(AiQuestionBehaviorContext::getContext, request.getAnswer())
                .eq(AiQuestionBehaviorContext::getType, QuestionContextTypeEnum.ANSWER.getCode());
        List<AiQuestionBehaviorContext> list = aiQuestionBehaviorContextService.list(wrapper);
        AiQuestionBehaviorContext question = null, answer = null;
        if (CollectionUtils.isNotEmpty(list)) {
            Optional<AiQuestionBehaviorContext> questionOptional =
                    list.stream().filter(q -> QuestionContextTypeEnum.QUESTION.getCode().equals(q.getType())).findFirst();
            question = questionOptional.orElseGet(() -> aiQuestionBehaviorContextService.save(
                    request.getQuestion(), QuestionContextTypeEnum.QUESTION.getCode(), request.getRelationId()));
            Optional<AiQuestionBehaviorContext> answerOptional =
                    list.stream().filter(q -> QuestionContextTypeEnum.ANSWER.getCode().equals(q.getType())).findFirst();
            answer = answerOptional.orElseGet(() -> aiQuestionBehaviorContextService.save(request.getAnswer(), QuestionContextTypeEnum.ANSWER.getCode(), null));
        } else {
            if (BehaviorTypeEnum.CANCEL_STAR.getCode().equals(request.getBehavior())) {
                return R.fail(ResultCode.PARAM_INVALID);
            }
            question = aiQuestionBehaviorContextService.save(
                    request.getQuestion(), QuestionContextTypeEnum.QUESTION.getCode(), request.getRelationId());
            answer = aiQuestionBehaviorContextService.save(
                    request.getAnswer(), QuestionContextTypeEnum.ANSWER.getCode(), null);
        }

        LambdaQueryWrapper<AiQuestionBehavior> behaviorWrapper = new LambdaQueryWrapper<>();
        behaviorWrapper.eq(AiQuestionBehavior::getQuestionId, question.getId())
                .eq(AiQuestionBehavior::getAnswerId, answer.getId()).eq(AiQuestionBehavior::getUserAccount, request.getAccount());
        AiQuestionBehavior behavior = aiQuestionBehaviorService.getOne(behaviorWrapper);
        if (Objects.isNull(behavior)) {
            if (BehaviorTypeEnum.CANCEL_STAR.getCode().equals(request.getBehavior())) {
                throw new AiServerException(ResultCode.PARAM_INVALID);
            }
            AiQuestionBehavior newBehavior = new AiQuestionBehavior();
            newBehavior.setQuestionId(question.getId());
            newBehavior.setAnswerId(answer.getId());
            newBehavior.setUserAccount(request.getAccount());
            if (BehaviorTypeEnum.STAR.getCode().equals(request.getBehavior())) {
                newBehavior.setIsStar(Boolean.TRUE);
                newBehavior.setStarTime(LocalDateTime.now());
            } else {
                newBehavior.setLikeOrDown(request.getBehavior());
                newBehavior.setLikeOrDownTime(LocalDateTime.now());
            }
            aiQuestionBehaviorService.save(newBehavior);
        } else {
            if (BehaviorTypeEnum.getLikeOrDown().contains(request.getBehavior())) {
                if (!behavior.getLikeOrDown().equals(0)) {
                    return R.data(null);
                }
                behavior.setLikeOrDown(request.getBehavior());
                behavior.setLikeOrDownTime(LocalDateTime.now());
            } else {
                if (BehaviorTypeEnum.STAR.getCode().equals(request.getBehavior()) && behavior.getIsStar()
                        || BehaviorTypeEnum.CANCEL_STAR.getCode().equals(request.getBehavior()) && !behavior.getIsStar()) {
                    return R.data(null);
                }
                behavior.setIsStar(BehaviorTypeEnum.STAR.getCode().equals(request.getBehavior()));
                behavior.setStarTime(LocalDateTime.now());
            }
            aiQuestionBehaviorService.updateById(behavior);
        }

        if (Objects.nonNull(request.getRelationId()) && !BehaviorTypeEnum.CANCEL_STAR.getCode().equals(request.getBehavior())) {
            LambdaUpdateWrapper<AiQuestion> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiQuestion::getId, request.getRelationId());
            if (BehaviorTypeEnum.LIKE.getCode().equals(request.getBehavior())) {
                updateWrapper.setSql("like_count = like_count + 1");
            } else if (BehaviorTypeEnum.DOWN.getCode().equals(request.getBehavior())) {
                updateWrapper.setSql("down_count = down_count + 1");
            } else {
                updateWrapper.setSql("star_count = star_count + 1");
            }
            this.update(updateWrapper);
        }

        return R.data(null);
    }

    @Override
    public R<IPage<AiQuestionBehaviorStarVO>> getPage(BasePageReq request) {
        Page<AiQuestionBehavior> page = new Page<>(request.getCurrent(), request.getSize());
        LambdaQueryWrapper<AiQuestionBehavior> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiQuestionBehavior::getUserAccount, request.getAccount())
                .eq(AiQuestionBehavior::getIsStar, Boolean.TRUE).orderByDesc(AiQuestionBehavior::getStarTime);
        IPage<AiQuestionBehavior> list = aiQuestionBehaviorService.page(page, wrapper);

        Page<AiQuestionBehaviorStarVO> pageResult = new Page<>();
        pageResult.setTotal(list.getTotal());
        pageResult.setPages(list.getPages());
        pageResult.setCurrent(list.getCurrent());
        pageResult.setSize(list.getSize());
        if (CollectionUtils.isEmpty(list.getRecords())) {
            pageResult.setRecords(Collections.emptyList());
        } else {
            Set<Long> contextIdSet = new HashSet<>();
            list.getRecords().forEach(b -> {
                contextIdSet.add(b.getQuestionId());
                contextIdSet.add(b.getAnswerId());
            });
            List<AiQuestionBehaviorContext> contextList = aiQuestionBehaviorContextService.listByIds(contextIdSet);
            if (contextList.size() != contextIdSet.size()) {
                throw new AiServerException(ResultCode.FAILURE);
            }
            Map<Long, String> contextMap = contextList.stream().collect(Collectors.toMap(
                    AiQuestionBehaviorContext::getId, AiQuestionBehaviorContext::getContext));

            Map<Long, List<AiQuestionBehavior>> behaviorMap = list.getRecords().stream()
                    .collect(Collectors.groupingBy(AiQuestionBehavior::getQuestionId));
            List<AiQuestionBehaviorStarVO> res = new ArrayList<>();
            for (Long questionId : behaviorMap.keySet()) {
                AiQuestionBehaviorStarVO starVO = new AiQuestionBehaviorStarVO();
                starVO.setQuestion(contextMap.get(questionId));
                List<AiQuestionBehaviorVO> behaviorList = new ArrayList<>();
                for (AiQuestionBehavior behavior : behaviorMap.get(questionId)) {
                    AiQuestionBehaviorVO behaviorVO = new AiQuestionBehaviorVO();
                    BeanUtils.copyProperties(behavior, behaviorVO);
                    behaviorVO.setAnswer(contextMap.get(behavior.getAnswerId()));
                    behaviorList.add(behaviorVO);
                }
                starVO.setBehaviorList(behaviorList);
                res.add(starVO);
            }
            pageResult.setRecords(res);
        }
        return R.data(pageResult);
    }
}
