package com.yirendai.voiceaiserver.service;

import com.yirendai.workbench.entity.AiPlannerVoice;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.vo.response.VoiceSearchResponse;
import com.yirendai.voiceaiserver.vo.response.AiPlannerVoiceListRes;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * <p>
 * 理财师声音信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
public interface AiPlannerVoiceService extends IService<AiPlannerVoice> {

    String saveOrUpdate(String account, String staffId, MultipartFile file);

    void autoUpdateStatus(String account);

    AiPlannerVoice get(String account);

    List<AiPlannerVoiceListRes> list(String email, Integer status);

    void audit(String account, Long id, Integer status);

    String voiceRegister(AiPlannerVoice aiPlannerVoice, AiPromptRecordVo aiPromptRecordVo);

    File deNoise(File file, Integer sampleRate);

    VoiceSearchResponse search(File file, String account, String model);
}
