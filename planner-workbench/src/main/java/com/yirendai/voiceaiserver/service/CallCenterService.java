package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.vo.db.PlannerAchievementInfo;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.vo.response.ChatTranslateStatusVO;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.enums.callcenter.anmi.AnmiConstants;
import org.springframework.web.multipart.MultipartFile;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface CallCenterService {

    /**
     * 新增通话记录
     * @param uuid 通话uuid
     * @param ringTime 响铃时长
     */
    void addRecord(String uuid, Long ringTime);

    /**
     * 挂断通知
     * @param uuid 通话uuid
     */
    void finishNotify(String uuid);

    /**
     * 语音同步至AI聊天内容
     */
    AiPlannerChatContact syncChat(CallcenterCallRecord record, String content, AnmiConstants.AnmiDeviceType anmiDeviceType, LocalDateTime finishNotifyTime);

    void syncPlannerUser(CallcenterCallRecord record);
    /**
     * 音转文
     * @param mqBaseDto mq消息内容
     */
    void translate(MqBaseDto mqBaseDto);

    /**
     * 处理当天音转文失败片段
     */
    void handleTodayFailSegment();

    /**
     * 获取理财师列表
     * @return 理财师列表
     */
    List<PlannerAchievementInfo> getPlannerList();

    /**
     * 同步指定日期至今的通话记录任务
     * @param startDate 开始日期
     */
    void syncRecord(LocalDate startDate);
    void handleComplete(AiPlannerChatContact chatContact, AiPromptRecordVo config, Long startTime, Long endTime, AnmiConstants.AnmiDeviceType anmiDeviceType);

    /**
     * 处理指定时间范围内的音转文失败音频任务
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void handleFailComplete(LocalDate startDate, LocalDate endDate);

    /**
     * 对比切割处理结果
     * @param file 上传文件
     * @param direction 呼叫方向
     */
    void compareSplit(MultipartFile file, Integer direction);

    /**
     * 根据originType+busId查询消息音转文处理状态
     * @return 音转文处理状态
     */
    ChatTranslateStatusVO getTranslateStatus(String busId, String originType);

    /**
     * 当天音转文失败次数已达上限告警任务
     */
    void notifyTodayFailLimit();

    /**
     * 清理脏数据和异常数据告警任务
     */
    void clearDirtyAndNotify();
}
