package com.yirendai.voiceaiserver.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationTagTypeEnum;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.service.AiSummaryContentService;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.voiceaiserver.vo.response.ChatApiRes;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.mapper.AiPlannerTopOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AiSummaryContentServiceImpl implements AiSummaryContentService {
    @Resource
    private ChatService chatService;
    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;
    @Resource
    private AiPlannerTopOrderMapper aiPlannerTopOrderMapper;
    @Resource
    private AiPromptRecordService aiPromptRecordService;
    @Resource
    private AIConversationProcessService aiConversationProcessService;

    private static final int BATCH_SIZE = 1000;

    private static final int THREAD_COUNT = 8;

    private static final String EMPTY_AVAYA_CONTENT = "[avaya语音]";
    public static final String PROMPT_SUMMARY_AVAYA =
            "以下是一段关于理财师和客户的通话记录。在文本中不区分理财师和客户的角色，且通话记录中可能包含错别字和背景杂音。" +
                    "请忽略这些错别字和杂音，将记录中重要的关键信息进行提取和总结，不需要提取客户和理财师的信息，并条理化的输出结果，输出结果中不要包含任何表情和任何前缀信息。\n\n"
                    + "通话记录如下：%s\n\n"
                    + "请直接输出结果为：";

    private static final Integer MIN_LENGTH_AVAYA_CONTENT = 30;

    private static final String NO_SUMMARY_CONTENT = "通话内容不包含关键信息";

    private static final ThreadPoolExecutor summaryContentPoolExecutor = new ThreadPoolExecutor(THREAD_COUNT,
            THREAD_COUNT, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(50),
            new ThreadFactoryBuilder().setNamePrefix("get-summary-thread").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());


    @Override
    public void summaryContentFull() {
        AiPromptRecordVo latestConfig = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.SUMMARY_SIGNAL_AVAYA_CONTENT.getCode());
        LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                .isNotNull(AiPlannerChatContact::getProcessingContent)
                .ne(AiPlannerChatContact::getProcessingContent, EMPTY_AVAYA_CONTENT)
                .isNull(AiPlannerChatContact::getSummaryContent);

        long totalCount = aiPlannerChatContactMapper.selectCount(queryWrapper);
        int totalPages = (int) Math.ceil((double) totalCount / BATCH_SIZE);

        for (int i = 1; i <= totalPages; i++) {
            IPage<AiPlannerChatContact> page = new Page<>(i, BATCH_SIZE);
            List<AiPlannerChatContact> records = aiPlannerChatContactMapper.selectPage(page, queryWrapper).getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            List<List<AiPlannerChatContact>> processedList = splitList(records);
            CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
            for (List<AiPlannerChatContact> subList : processedList) {
                summaryContentPoolExecutor.submit(() -> {
                    try {
                        getSummaryContentAndUpdate(subList, latestConfig.getPrompt(), latestConfig.getModel(), latestConfig.getTemperature());
                    } catch (Exception e) {
                        log.error("线程{}获取聊天内容总结失败", Thread.currentThread().getName(), e);
                    } finally {
                        latch.countDown();
                    }
                });
            }
            try {
                latch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Main thread interrupted");
            }
        }
        summaryContentPoolExecutor.shutdown();
        try {
            if (!summaryContentPoolExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                summaryContentPoolExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            summaryContentPoolExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public void avayaSummaryContentPart(int minContentLength, int maxContentLength, int size) {
        AiPromptRecordVo latestConfig = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.SUMMARY_SIGNAL_AVAYA_CONTENT.getCode());
        if (StrUtil.isBlank(latestConfig.getPrompt()) ||
                StrUtil.isBlank(latestConfig.getModel()) || Objects.isNull(latestConfig.getTemperature())) {
            log.info("当前通话总结配置无效:{}", JSON.toJSONString(latestConfig));
            return;
        }
        //取出top理财师
        List<Integer> topPlannerNoList = aiPlannerTopOrderMapper.selectList(null)
                .stream()
                .map(AiPlannerTopOrder::getPlannerNo)
                .distinct()
                .collect(Collectors.toList());
        //top理财师对话,类型为avaya,按照时间排序前size条
        IPage<AiPlannerChatContact> page = new Page<>(1, size);
        LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AiPlannerChatContact::getPlannerNo, topPlannerNoList)
                .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                .isNotNull(AiPlannerChatContact::getProcessingContent)
                .ne(AiPlannerChatContact::getProcessingContent, EMPTY_AVAYA_CONTENT)
                .isNull(AiPlannerChatContact::getSummaryContent)
                .orderByDesc(AiPlannerChatContact::getMsgTime);
        //通话内容长度范围在minLength和maxLength之间
        if (minContentLength != -1 && maxContentLength != -1 && minContentLength < maxContentLength) {
            queryWrapper.apply("CHAR_LENGTH(processing_content) >= {0} AND CHAR_LENGTH(processing_content) <= {1}",
                    minContentLength, maxContentLength);
        }
        List<AiPlannerChatContact> records = aiPlannerChatContactMapper.selectPage(page, queryWrapper).getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.info("无可处理的数据");
            return;
        }
        List<List<AiPlannerChatContact>> processedList = splitList(records);
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        for (List<AiPlannerChatContact> subList : processedList) {
            summaryContentPoolExecutor.submit(() -> {
                try {
                    getSummaryContentAndUpdate(subList, latestConfig.getPrompt(), latestConfig.getModel(), latestConfig.getTemperature());
                } catch (Exception e) {
                    log.error("线程{}获取聊天内容总结失败", Thread.currentThread().getName(), e);
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Main thread interrupted");
        }
        summaryContentPoolExecutor.shutdown();
        try {
            if (!summaryContentPoolExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                summaryContentPoolExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            summaryContentPoolExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 调用接口获取总结内容并更新数据库
     *
     * @param subRecords  需要处理的数据
     * @param prompt      调用接口时的提示词
     * @param model       调用接口时的模型
     * @param temperature 调用接口时的温度
     */
    private void getSummaryContentAndUpdate(List<AiPlannerChatContact> subRecords, String prompt, String model, Float temperature) {
        if (CollectionUtils.isEmpty(subRecords) || StrUtil.isBlank(prompt)) {
            return;
        }
        for (AiPlannerChatContact record : subRecords) {
            String question = null;
            String originType = record.getOriginType();
            if (QwMsgTypeEnum.AVAYA.getType().equals(originType)) {
                question = String.format(prompt, record.getProcessingContent());
            } else if (QwMsgTypeEnum.MEETING_VOICE_CALL.getType().equals(originType) || QwMsgTypeEnum.VIDEO.getType().equals(originType)) {
                question = String.format(prompt, record.getProcessedContent());
            }
            if (record.getProcessingContent().length() <= MIN_LENGTH_AVAYA_CONTENT) {
                record.setSummaryContent(NO_SUMMARY_CONTENT);
                aiPlannerChatContactMapper.updateById(record);
                continue;
            }
            String response = chatService.chat(question, model, temperature);
            if (StrUtil.isBlank(response)) {
                throw new AiServerException(ResultCode.FAILURE);
            }
            ChatApiRes chatApiRes = JSON.parseObject(response, ChatApiRes.class);
            if (Objects.isNull(chatApiRes) || CollectionUtils.isEmpty(chatApiRes.getChoices())
                    || Objects.isNull(chatApiRes.getChoices().get(0).getMessage())
                    || StrUtil.isBlank(chatApiRes.getChoices().get(0).getMessage().getContent())) {
                throw new AiServerException(ResultCode.FAILURE);
            }
            String summaryContent = chatApiRes.getChoices().get(0).getMessage().getContent();
            log.info("理财师客户聊天记录ID为:{}, 获取总结内容为:{}", record.getId(), summaryContent);
            record.setSummaryContent(summaryContent);
            aiPlannerChatContactMapper.updateById(record);
        }
    }


    /**
     * 将通话记录List平均拆分成THREAD_CONTENT份
     *
     * @param records 总通话记录List
     * @return 返回拆分后的List
     */
    private List<List<AiPlannerChatContact>> splitList(List<AiPlannerChatContact> records) {
        List<List<AiPlannerChatContact>> processedList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return processedList;
        }
        int totalSize = records.size();
        int chunkSize = totalSize / THREAD_COUNT;
        if (totalSize < THREAD_COUNT) {
            processedList.add(records);
            return processedList;
        }
        int restSize = totalSize % THREAD_COUNT;
        int start = 0;
        for (int i = 0; i < THREAD_COUNT; i++) {
            int end = Math.min(start + chunkSize + (i < restSize ? 1 : 0), totalSize);
            processedList.add(records.subList(start, end));
            start = end;
        }
        return processedList;
    }


}
