package com.yirendai.voiceaiserver.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiCustomerTagVO;
import com.yirendai.workbench.entity.AiCustomerTag;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_customer_tag(ai客户与标签关联表)】的数据库操作Service
* @createDate 2024-11-13 18:52:30
*/
public interface AiCustomerTagService extends IService<AiCustomerTag> {

    /**
     * 根据用户ID查询用户标签列表
     * @param userId 用户ID
     * @return 用户标签列表
     */
    List<AiCustomerTagVO> listByUserId(String userId);

    Boolean feedback(AiFeedbackReq req);
}
