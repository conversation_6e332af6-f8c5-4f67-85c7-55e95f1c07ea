package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.enums.PrimaryIntentEnum;
import com.yirendai.voiceaiserver.task.conversation.sub.HongKongInsuranceIntentProcess;
import com.yirendai.voiceaiserver.task.conversation.sub.HongKongInsuranceTagsProcess;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 港险流程管理服务
 * 负责协调主意图识别和标签识别两个步骤的执行
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class HongKongInsuranceFlowService {

    @Resource
    private HongKongInsuranceIntentProcess intentProcess;
    
    @Resource
    private HongKongInsuranceTagsProcess tagsProcess;
    
    /**
     * 执行港险流程分析
     * 先进行主意图识别，如果识别到有效意图则继续执行标签识别
     * 
     * @param req 对话处理请求
     */
    public void executeHongKongInsuranceFlow(AiConversationProcessReq req) {
        log.info("开始执行港险流程分析，对话ID: {}", req.getChatContactId());
        
        try {
            // 第一步：执行主意图识别
            executeIntentRecognition(req);
            
            // 第二步：根据主意图识别结果决定是否继续执行标签识别
            if (intentProcess.hasValidIntent()) {
                String detectedIntent = intentProcess.getDetectedIntent();
                log.info("检测到有效主意图: {}，继续执行标签识别", detectedIntent);
                executeTagsRecognition(req);
            } else {
                log.info("未检测到有效主意图，跳过标签识别。对话ID: {}", req.getChatContactId());
            }
            
            log.info("港险流程分析完成，对话ID: {}", req.getChatContactId());
            
        } catch (Exception e) {
            log.error("港险流程分析异常，对话ID: {}", req.getChatContactId(), e);
            throw e;
        } finally {
            // 清理线程变量
            intentProcess.cleanThreadLocal();
            tagsProcess.cleanThreadLocal();
        }
    }
    
    /**
     * 执行主意图识别
     */
    private void executeIntentRecognition(AiConversationProcessReq req) {
        log.info("开始执行主意图识别，对话ID: {}", req.getChatContactId());
        
        // 获取主意图识别的提示词配置
        AiPromptRecordVo intentPromptConfig = intentProcess.processBefore(req);
        
        // 执行主意图识别
        intentProcess.process(req, intentPromptConfig);
        
        log.info("主意图识别完成，对话ID: {}", req.getChatContactId());
    }
    
    /**
     * 执行标签识别
     */
    private void executeTagsRecognition(AiConversationProcessReq req) {
        log.info("开始执行标签识别，对话ID: {}", req.getChatContactId());
        
        // 获取标签识别的提示词配置
        AiPromptRecordVo tagsPromptConfig = tagsProcess.processBefore(req);
        
        // 执行标签识别
        tagsProcess.process(req, tagsPromptConfig);
        
        log.info("标签识别完成，对话ID: {}", req.getChatContactId());
    }
    
    /**
     * 检查是否检测到有效的主意图
     * @return true表示检测到有效意图，false表示无意图
     */
    public boolean hasValidIntent() {
        return intentProcess.hasValidIntent();
    }
    
    /**
     * 获取检测到的主意图代码
     * @return 主意图代码，如果没有检测到则返回null
     */
    public String getDetectedIntent() {
        return intentProcess.getDetectedIntent();
    }
}