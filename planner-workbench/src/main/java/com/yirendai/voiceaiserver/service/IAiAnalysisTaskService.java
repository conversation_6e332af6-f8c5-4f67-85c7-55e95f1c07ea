package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.vo.request.AiAnalysisTaskReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskVO;
import com.yirendai.workbench.entity.AiAnalysisTask;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ai分析任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface IAiAnalysisTaskService extends IService<AiAnalysisTask> {

    /**
     * 根据id和用户id查询
     * @param aiAnalysisTaskReq
     * @return
     */
    AiAnalysisTaskVO getByIdAndUserId(AiAnalysisTaskReq aiAnalysisTaskReq);

    /**
     * 分页查询
     * @param page
     * @param aiAnalysisTaskReq
     * @return
     */
    IPage<AiAnalysisTaskVO> aiAnalysisTaskPageList(IPage<AiAnalysisTaskVO> page, AiAnalysisTaskReq aiAnalysisTaskReq);
}
