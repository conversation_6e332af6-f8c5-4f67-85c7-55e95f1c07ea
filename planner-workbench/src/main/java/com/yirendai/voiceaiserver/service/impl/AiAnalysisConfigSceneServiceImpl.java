package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.service.IAiAnalysisConfigSceneResultService;
import com.yirendai.voiceaiserver.service.IAiAnalysisConfigSceneService;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneResultVO;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneVO;
import com.yirendai.workbench.entity.AiAnalysisConfigScene;
import com.yirendai.workbench.mapper.AiAnalysisConfigSceneMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <p>
 * ai分析场景配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
public class AiAnalysisConfigSceneServiceImpl extends ServiceImpl<AiAnalysisConfigSceneMapper, AiAnalysisConfigScene> implements IAiAnalysisConfigSceneService {

    @Autowired
    private IAiAnalysisConfigSceneResultService aiAnalysisConfigSceneResultService;
    /**
     * 根据用户ID查询场景列表
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @Override
    public List<AiAnalysisConfigScene> listByUserId(String tenantId, Long userId) {
        return baseMapper.listByUserId(tenantId, userId);
    }

    /**
     * 根据ID和用户ID查询场景
     * @param id
     * @param userId
     * @return
     */
    @Override
    public AiAnalysisConfigScene getByIdAndUser(Long id, Long userId) {
        return baseMapper.getByIdAndUser(id, userId);
    }

    /**
     * 查询场景列表(带结果选项)
     * @param tenantId
     * @param userId
     * @return
     */
    @Override
    public List<AiAnalysisConfigSceneVO> listAll(String tenantId, Long userId) {
        List<AiAnalysisConfigScene> list = listByUserId(tenantId, userId);
        List<AiAnalysisConfigSceneVO> voList = new ArrayList<>();
        // 使用迭代器循环，如果场景下没有结果则从list里边移出
        Iterator<AiAnalysisConfigScene> iterator = list.iterator();
        while (iterator.hasNext()) {
            AiAnalysisConfigScene aiAnalysisConfigScene = iterator.next();
            List<AiAnalysisConfigSceneResultVO> resultList = aiAnalysisConfigSceneResultService.listBySceneId(aiAnalysisConfigScene.getId());
            if (CollectionUtils.isNotEmpty(resultList)) {
                AiAnalysisConfigSceneVO vo = new AiAnalysisConfigSceneVO();
                BeanUtil.copyProperties(aiAnalysisConfigScene, vo);
                vo.setResultList(resultList);
                voList.add( vo);

            }
        }
        return voList;
    }

    @Override
    public int selectCountBySceneName(String sceneName, String tenantId, Long userId,Long excludeId) {
        return baseMapper.selectCountBySceneName(sceneName, tenantId, userId,excludeId);
    }
}
