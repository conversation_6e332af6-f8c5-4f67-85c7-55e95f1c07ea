package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.Result;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.MessageTypeEnum;
import com.yirendai.workbench.mapper.AiMessageMapper;
import com.yirendai.workbench.entity.AiMessage;
import com.yirendai.voiceaiserver.service.AiMessageService;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.vo.response.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * ai信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Slf4j
@Service
@RefreshScope
public class AiMessageServiceImpl extends ServiceImpl<AiMessageMapper, AiMessage> implements AiMessageService {

    @Value("${owm-finance.base.url}")
    private String baseUrl;

    @Value("${owm-finance.user.info.url}")
    private String userInfoUrl;

    public static final String PROMPT =
            "你是一个有丰富工作经验的理财师，今天你需要与一位客户进行通话，你了解客户的基本信息和当前的投资情况，你擅长分析客户的投资偏好，在沟通时擅长使用今日金融新闻作为切入点展开话题，建立信任并引导进一步的投资讨论，最终向客户推荐平台目前正在进行的活动。\n" +
            "现在，你已经了解了客户的基本信息、持仓情况、今日重要金融新闻以及平台当前举行的活动，请根据这些信息，生成一段开场话术、沟通话术、结尾话术，都要超过两千字。\n" +
            "\n" +
            "一、客户基本信息：\n" +
            "%s\n\n" +
            "二、持仓信息：\n" +
            "%s\n\n" +
            "三、今日新闻：\n" +
            "%s\n\n" +
            "四、平台活动信息：\n" +
            "%s\n\n";

    @Resource
    RestTemplate restTemplateVoice;
    @Resource
    ChatService chatService;

    @Override
    public R<AiMessageVO> saveOrUpdate(Integer type, String content) {
        LambdaUpdateWrapper<AiMessage> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(AiMessage::getEnable, Boolean.FALSE).eq(AiMessage::getType, type).eq(AiMessage::getEnable, Boolean.TRUE);
        this.update(wrapper);

        AiMessage aiMessage = new AiMessage();
        aiMessage.setContent(content);
        aiMessage.setType(type);
        this.save(aiMessage);

        AiMessage message = this.getById(aiMessage.getId());
        AiMessageVO res = new AiMessageVO();
        BeanUtils.copyProperties(message, res);
        res.setMessage(message.getContent().replaceAll("\\r?\\n|\\r", "</br>"));
        return R.data(res);
    }

    @Override
    public R<AiMessageVO> get(Integer type) {
        LambdaQueryWrapper<AiMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiMessage::getType, type).eq(AiMessage::getEnable, Boolean.TRUE);
        AiMessage aiMessage = this.getOne(wrapper);

        if (Objects.isNull(aiMessage)) {
            return R.data(null);
        }
        AiMessageVO res = new AiMessageVO();
        BeanUtils.copyProperties(aiMessage, res);
        res.setMessage(aiMessage.getContent().replaceAll("\\r?\\n|\\r", "</br>"));
        return R.data(res);
    }

    @Override
    public R<SuggestionInfo> getSuggestion(Long userId) {
        UserInfo userInfo = getUserInfo(userId);
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getUserInfo()) || StringUtils.isBlank(userInfo.getOrderInfo())) {
            throw new AiServerException(ResultCode.NO_CUSTOMER);
        }

        AiMessage news = null, activityRule = null;
        List<AiMessage> list = getNewsAndActivityRule();
        for (AiMessage message : list) {
            if (MessageTypeEnum.NEWS.getCode().equals(message.getType())) {
                news = message;
            } else {
                activityRule = message;
            }
        }
        if (Objects.isNull(news) || Objects.isNull(activityRule)) {
            log.info("未查询到今日新闻或活动规则");
            throw new AiServerException(ResultCode.NO_CONFIG);
        }

        String chatRes = chatService.chat(
                String.format(PROMPT, userInfo.getUserInfo(), userInfo.getOrderInfo(), news.getContent(), activityRule.getContent()),
                ChatServiceImpl.MODEL_PLUS, null);
        log.info("聊天接口返回结果为{}", chatRes);
        if (StringUtils.isBlank(chatRes)) {
            throw new AiServerException(ResultCode.FAILURE);
        }
        ChatApiRes chatApiRes = JSON.parseObject(chatRes, ChatApiRes.class);
        if (Objects.isNull(chatApiRes) || CollectionUtils.isEmpty(chatApiRes.getChoices())
                || Objects.isNull(chatApiRes.getChoices().get(0).getMessage())
                || StringUtils.isBlank(chatApiRes.getChoices().get(0).getMessage().getContent())) {
            throw new AiServerException(ResultCode.FAILURE);
        }
        return R.data(SuggestionInfo.builder().content(chatApiRes.getChoices().get(0).getMessage().getContent().replaceAll("\\r?\\n|\\r", "</br>")).build());
    }

    public UserInfo getUserInfo(Long userId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, Long> param = new LinkedMultiValueMap<>();
        param.add("userId", userId);
        HttpEntity<MultiValueMap<String, Long>> requestEntity = new HttpEntity<>(param, headers);

        UserInfo userInfo = null;
        try {
            String response = restTemplateVoice.postForEntity(baseUrl + userInfoUrl, requestEntity, String.class).getBody();
            log.info("请求own-finance服务获取客户信息为{}", response);
            if (StringUtils.isBlank(response)) {
                throw new AiServerException(ResultCode.FAILURE);
            }
            Result<UserInfo> result = JSON.parseObject(response, new TypeReference<Result<UserInfo>>(){});
            if (Objects.isNull(result) || !"0000".equals(result.getResultCode())) {
                throw new AiServerException(ResultCode.FAILURE);
            }
            userInfo = result.getData();
        } catch (Exception e) {
            log.error("请求own-finance服务获取客户信息发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE);
        }
        return userInfo;
    }

    public List<AiMessage> getNewsAndActivityRule() {
        LambdaQueryWrapper<AiMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiMessage::getEnable, Boolean.TRUE);
        List<AiMessage> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            log.info("未查询到今日新闻或活动规则");
            throw new AiServerException(ResultCode.NO_CONFIG);
        }
        return list;
    }

    @Override
    public R<SuggestionListRes> getBatchSuggestion(Long userId, Integer count) {
        UserInfo userInfo = getUserInfo(userId);
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getUserInfo()) || StringUtils.isBlank(userInfo.getOrderInfo())) {
            throw new AiServerException(ResultCode.NO_CUSTOMER);
        }

        AiMessage news = null, activityRule = null;
        List<AiMessage> list = getNewsAndActivityRule();
        for (AiMessage message : list) {
            if (MessageTypeEnum.NEWS.getCode().equals(message.getType())) {
                news = message;
            } else {
                activityRule = message;
            }
        }
        if (Objects.isNull(news) || Objects.isNull(activityRule)) {
            log.info("未查询到今日新闻或活动规则");
            throw new AiServerException(ResultCode.NO_CONFIG);
        }

        Random random = new Random();
        Set<Float> randomTemperature = new HashSet<>();
        for (int i = 0; i < count * 3; i++) {
            randomTemperature.add(Math.round(random.nextFloat() * 10) / 10f);
            if (randomTemperature.size() >= count) {
                break;
            }
        }
        List<Float> temperatureList = new ArrayList<>(randomTemperature);
        if (temperatureList.size() < count) {
            for (int i = 0; i < count - temperatureList.size(); i++) {
                temperatureList.add(Math.round(random.nextFloat() * 10) / 10f);
            }
        }
        String content = String.format(PROMPT, userInfo.getUserInfo(), userInfo.getOrderInfo(), news.getContent(), activityRule.getContent());
        CountDownLatch latch = new CountDownLatch(count);
        List<CompletableFuture<String>> res = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            try {
                res.add(chatService.chatAsync(content, temperatureList.get(i)));
            } catch (Exception e) {
                throw new AiServerException(ResultCode.FAILURE);
            } finally {
                latch.countDown();
            }
        }
        try {
            boolean latchRes = latch.await(1, TimeUnit.MINUTES);
            if (!latchRes) {
                log.error("主线程等待失败");
                throw new AiServerException(ResultCode.FAILURE);
            }
        } catch (Exception e) {
            log.error("主线程等待异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE);
        }

        List<String> answerList = res.stream().map(r -> {
            try {
                return r.get();
            } catch (Exception e) {
                log.error("获取内容失败");
                throw new AiServerException(ResultCode.FAILURE);
            }
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(answerList)) {
            throw new AiServerException(ResultCode.FAILURE);
        }
        List<SuggestionInfo> contentList = answerList.stream()
                .map(a -> SuggestionInfo.builder().content(a.replaceAll("\\r?\\n|\\r", "</br>")).build()).collect(Collectors.toList());
        return R.data(SuggestionListRes.builder()
                .userInvestmentInfo(userInfo.getOrderInfo().replaceAll("\\r?\\n|\\r", "</br>")).contentList(contentList).build());
    }
}
