package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.vo.request.ChatLikeReq;
import com.yirendai.voiceaiserver.vo.request.ChatMessagesStreamReq;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @time 2023/11/13 17:14
 **/
public interface AicsService {

    SseEmitter chatMessageStream(ChatMessagesStreamReq chatMessagesReq);

    R<Boolean> like(ChatLikeReq chatLikeReq);

}
