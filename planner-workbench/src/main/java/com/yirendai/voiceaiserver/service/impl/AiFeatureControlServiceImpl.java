/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yirendai.voiceaiserver.constant.AiConstant;
import com.yirendai.voiceaiserver.enums.AiFeatureControlEnum;
import com.yirendai.voiceaiserver.service.IAiFeatureControlService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.AiFeatureControl;
import com.yirendai.workbench.mapper.AiFeatureControlMapper;
import org.springframework.stereotype.Service;

/**
 * AI功能控 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class AiFeatureControlServiceImpl extends ServiceImpl<AiFeatureControlMapper, AiFeatureControl> implements IAiFeatureControlService {

    @Override
    public List<AiFeatureControl> listByTenantId(String tenantId) {
        List<AiFeatureControl> list = baseMapper.selectList(new QueryWrapper<AiFeatureControl>().eq("tenant_id", tenantId));
        if(CollectionUtils.isEmpty(list)){
            list = baseMapper.selectList(new QueryWrapper<AiFeatureControl>().eq("tenant_id", AiConstant.ADMIN_TENANT_ID));
            for(AiFeatureControl aiFeatureControl:list){
                aiFeatureControl.setTenantId(tenantId);
                aiFeatureControl.setCreateTime(LocalDateTime.now());
                aiFeatureControl.setUpdateTime(LocalDateTime.now());
                aiFeatureControl.setId(null);
                baseMapper.insert(aiFeatureControl);
            }
        }
        return baseMapper.selectList(new QueryWrapper<AiFeatureControl>().eq("tenant_id", tenantId));
    }

    @Override
    public boolean updateStatusById(Long id, Integer status,Long userId) {
        AiFeatureControl aiFeatureControl = new AiFeatureControl();
        aiFeatureControl.setId(id);
        aiFeatureControl.setStatus(status);
        aiFeatureControl.setUpdateTime(LocalDateTime.now());
        aiFeatureControl.setUpdateUser(userId);
        return baseMapper.updateById(aiFeatureControl) > 0;
    }

    /**
     * 根据 AiFeatureControlEnum 和 tenantId 查询一条 AiFeatureControl 记录
     *
     * @param featureEnum 功能枚举
     * @param tenantId 租户ID
     * @return 查询到的 AiFeatureControl 记录，如果没有找到则返回 null
     */
    @Override
    public AiFeatureControl getByEnumAndTenantId(AiFeatureControlEnum featureEnum, String tenantId) {
        return baseMapper.selectOne(new QueryWrapper<AiFeatureControl>()
                .eq("code", featureEnum.getCode())
                .eq("tenant_id", tenantId));
    }

    /**
     * 根据 AiFeatureControlEnum 和 tenantId 查询一条 AiFeatureControl 记录，并判断其状态
     *
     * @param featureEnum 功能枚举
     * @param tenantId 租户ID
     * @return 如果记录存在且状态为启用，则返回 true；否则返回 false
     */
    @Override
    public boolean isFeatureEnabled(AiFeatureControlEnum featureEnum, String tenantId) {
        AiFeatureControl aiFeatureControl = getByEnumAndTenantId(featureEnum, tenantId);
        return aiFeatureControl != null && aiFeatureControl.getStatus() == 1; // 假设 1 表示启用状态
    }

    /**
     * 批量修改状态
     * @param list
     * @param userId
     * @return
     */
    @Override
    public boolean batchUpdateStatus(List<AiFeatureControl> list, Long userId){
        try{
            for(AiFeatureControl aiFeatureControl:list){
                updateStatusById(aiFeatureControl.getId(), aiFeatureControl.getStatus(),userId);
            }
            return true;
        }catch (Exception e){
            return false;
        }
    }
}
