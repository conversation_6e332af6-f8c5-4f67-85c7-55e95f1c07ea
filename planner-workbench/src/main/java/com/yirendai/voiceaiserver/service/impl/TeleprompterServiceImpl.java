package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.voiceaiserver.enums.ResponseModeEnum;
import com.yirendai.voiceaiserver.listener.TeleprompterEventAgentSourceListener;
import com.yirendai.voiceaiserver.listener.TeleprompterEventSourceListener;
import com.yirendai.voiceaiserver.listener.TeleprompterModelEventSourceListener;
import com.yirendai.voiceaiserver.service.ChatService;
import com.yirendai.voiceaiserver.service.TeleprompterService;
import com.yirendai.voiceaiserver.util.HttpUtil;
import com.yirendai.voiceaiserver.vo.request.AiChatMessageReq;
import com.yirendai.voiceaiserver.vo.request.ChatApiReq;
import com.yirendai.voiceaiserver.vo.request.ChatMessagesStreamReq;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class TeleprompterServiceImpl implements TeleprompterService {

    private static final String KEY_TELEPROMPTER_USER_DATA = "voice:ai:server:teleprompter:user:data:";

    private static final String KEY_TELEPROMPTER_USER_QUESTION_DATA = "voice:ai:server:teleprompter:user:question:data:";

    public static final String PROMPT = "问题: %s\n候选类别: 咨询某个产品\n了解时事新闻\n询问某个活动的活动规则\n常见操作问题\n";

    @Value("${chat.key.teleprompter}")
    private String chatKey;
    @Value("${chat.url}")
    private String chatUrl;
    @Value("${chat-api.url}")
    private String chatApiBaseUrl;
    @Value("${chat-api.key}")
    private String chatApiKey;
    @Value("${chat-api.chat.url}")
    private String chatApiChatUrl;
    @Value("${chat.key.agent}")
    private String chatAgentKey;

    @Resource
    ChatService chatService;
    @Resource
    JedisCluster jedisCluster;

    @Override
    public String getTeleprompter(String content, String account) {
        String message = appendUserContent(account, content);
        String judgeRes = chatService.chatJudge(String.format(PROMPT, message), ChatServiceImpl.MODEL_TURBO, Float.valueOf("0.8"));
        String userRedisKey = KEY_TELEPROMPTER_USER_DATA + account;
        if (StringUtils.isBlank(judgeRes) || judgeRes.contains("不符合")) {
            String ref = jedisCluster.get(userRedisKey);
            if (StringUtils.isNotBlank(ref) && ref.length() >= 50) {
                jedisCluster.del(userRedisKey);
            }
            return null;
        }
        List<String> list = Arrays.stream(judgeRes.split("\n")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        jedisCluster.del(userRedisKey);

        StringBuilder judge = new StringBuilder();
        if (list.size() == 1) {
            judge.append(list.get(0).trim());
        } else {
            boolean flag = false;
            for (String str : list) {
                if (str.contains("命中的候选类别：") || str.contains("命中的候选类别:")) {
                    judge.append(str.replaceAll("命中的候选类别：", "").replaceAll("命中的候选类别:", "").trim()).append("：");
                    flag = true;
                    break;
                }
            }
            if (flag) {
                flag = false;
                for (String str : list) {
                    if (str.contains("提问的关键内容：") || str.contains("提问的关键内容:")) {
                        judge.append(str.replaceAll("提问的关键内容：", "").replaceAll("提问的关键内容:", "").trim());
                        flag = true;
                        break;
                    }
                }
            }
            if (!flag) {
                return null;
            }
        }

        String userQuestionRedisKey = KEY_TELEPROMPTER_USER_QUESTION_DATA + account;
        String questionRef = jedisCluster.get(userQuestionRedisKey);
        if (StringUtils.isBlank(questionRef) || !questionRef.contentEquals(judge)) {
            jedisCluster.set(userQuestionRedisKey, judge.toString());
            jedisCluster.expire(userQuestionRedisKey, 60 * 60);
            return judge.toString();
        } else {
            return null;
        }
    }

    public String appendUserContent(String account, String content) {
        String redisKey = KEY_TELEPROMPTER_USER_DATA + account;
        if (jedisCluster.exists(redisKey)) {
            jedisCluster.append(redisKey, content);
        } else {
            long result = jedisCluster.setnx(redisKey, content);
            if (result == 0) {
                jedisCluster.append(redisKey, content);
            }
        }
        jedisCluster.expire(redisKey, 60 * 60);
        return jedisCluster.get(redisKey);
    }

    @Override
    public void finish(String account) {
        if (jedisCluster.exists(KEY_TELEPROMPTER_USER_DATA + account)) {
            jedisCluster.del(KEY_TELEPROMPTER_USER_DATA + account);
        }
        if (jedisCluster.exists(KEY_TELEPROMPTER_USER_QUESTION_DATA + account)) {
            jedisCluster.del(KEY_TELEPROMPTER_USER_QUESTION_DATA + account);
        }
    }

    @Override
    public SseEmitter chatAicsMessageStream(ChatMessagesStreamReq chatMessagesReq) {
        SseEmitter sseEmitter = new SseEmitter();
        AiChatMessageReq aiChatMessageReq = new AiChatMessageReq();
        aiChatMessageReq.setQuery(chatMessagesReq.getQuery() + "\n请将输出结果分段表示");
        aiChatMessageReq.setResponse_mode(ResponseModeEnum.STREAMING.getValue());
        HttpUtil.stream(chatKey, chatUrl + "/chat-messages", JSONUtil.toJsonStr(aiChatMessageReq),
                new TeleprompterEventSourceListener(sseEmitter, chatMessagesReq.getAccount(), aiChatMessageReq.getQuery()));
        return sseEmitter;
    }

    @Override
    public SseEmitter chatModelMessageStream(ChatMessagesStreamReq chatMessagesReq) {
        SseEmitter sseEmitter = new SseEmitter();
        ChatApiReq chatApiReq = new ChatApiReq(ChatServiceImpl.ASSISTANT);
        ChatApiReq.Message message = new ChatApiReq.Message();
        message.setRole("user");
        message.setContent(chatMessagesReq.getQuery() + "\n请将输出结果分段表示");
        chatApiReq.setModel(ChatServiceImpl.MODEL_TURBO);
        chatApiReq.getMessages().add(message);
        chatApiReq.setTemperature(Float.valueOf("0.2"));
        chatApiReq.setStream(Boolean.TRUE);

        HttpUtil.stream(chatApiKey, chatApiBaseUrl + chatApiChatUrl, JSONUtil.toJsonStr(chatApiReq),
                new TeleprompterModelEventSourceListener(sseEmitter, chatMessagesReq.getAccount(), message.getContent()));
        return sseEmitter;
    }

    @Override
    public SseEmitter chatAgentMessageStream(ChatMessagesStreamReq chatMessagesReq) {
        SseEmitter sseEmitter = new SseEmitter();
        AiChatMessageReq aiChatMessageReq = new AiChatMessageReq();
        aiChatMessageReq.setQuery(chatMessagesReq.getQuery() + "\n请将输出结果分段表示，不要输出分析过程，结果不要json格式，直接输出结果");
        aiChatMessageReq.setResponse_mode(ResponseModeEnum.STREAMING.getValue());
        HttpUtil.stream(chatAgentKey, chatUrl + "/chat-messages", JSONUtil.toJsonStr(aiChatMessageReq),
                new TeleprompterEventAgentSourceListener(sseEmitter, chatMessagesReq.getAccount(), aiChatMessageReq.getQuery()));
        return sseEmitter;
    }
}
