package com.yirendai.voiceaiserver.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.entity.AiConversationTagsProcess;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_tags_process(ai对话标签处理过程表)】的数据库操作Service
* @createDate 2024-11-13 18:58:40
*/
public interface AiConversationTagsProcessService extends IService<AiConversationTagsProcess> {
}
