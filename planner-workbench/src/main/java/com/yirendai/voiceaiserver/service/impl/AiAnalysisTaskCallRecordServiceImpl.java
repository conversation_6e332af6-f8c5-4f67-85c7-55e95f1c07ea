package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.yirendai.robot.modules.call.dto.CellDto;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yirendai.voiceaiserver.enums.AiConversationProcessStatusEnum;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.enums.CustomerDataSourceTypeEnum;
import com.yirendai.voiceaiserver.service.*;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.util.ExcelUtil;
import com.yirendai.voiceaiserver.util.TaskIdUtil;
import com.yirendai.voiceaiserver.vo.request.CallRecordAnalysisReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskCallRecordPageVO;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskCallRecordVO;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskResultVO;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.enums.AnalysisTaskCallRecordStatusEnum;
import com.yirendai.workbench.enums.callcenter.TaskTypeEnum;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.mapper.AiAnalysisTaskCallRecordMapper;
import com.yirendai.workbench.mapper.AiAnalysisTaskResultMapper;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.IAiConversationChatSummaryService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.workbench.service.callcenter.CustomerDataService;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.vo.res.callcenter.CustomerBasicInfoVo;
import com.yirendai.workbench.util.DesensitizationUtil;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.analysis.AiAnalysisTaskCallRecordReq;
import com.yirendai.workbench.vo.res.callcenter.CallRecordSearchRes;
import com.yirendai.workbench.vo.res.callcenter.CustomerBusinessInfoVo;
import com.yirendai.workbench.wrapper.CustomerInfoWrapper;
import com.yirendai.workbench.wrapper.TaskScheduleWrapper;
import com.yirendai.workbench.wrapper.dto.CallCenterTask;
import com.yirendai.workbench.wrapper.dto.CallCenterTaskCreateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.temporal.Temporal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yirendai.workbench.constant.TaskScheduleConstants.AI_ANALYSIS_EXPORT_TASK;

/**
 * <p>
 * ai分析任务通话记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
@Slf4j
public class AiAnalysisTaskCallRecordServiceImpl extends ServiceImpl<AiAnalysisTaskCallRecordMapper, AiAnalysisTaskCallRecord> implements IAiAnalysisTaskCallRecordService {

    @Resource
    private AiAnalysisTaskResultMapper aiAnalysisTaskResultMapper;

    @Autowired
    private IAiAnalysisTaskResultService aiAnalysisTaskResultService;


    @Autowired
    private CallcenterCallRecordService callRecordService;

    @Autowired
    private IAiAnalysisTaskService aiAnalysisTaskService;

    @Autowired
    private IAiAnalysisTaskSceneService aiAnalysisTaskSceneService;

    @Autowired
    private IAiAnalysisConfigSceneService aiAnalysisConfigSceneService;

    @Autowired
    private IAiAnalysisConfigSceneResultService aiAnalysisConfigSceneResultService;

    @Autowired
    private IAiPlannerChatContactService aiPlannerChatContactService;

    @Autowired
    private TaskScheduleWrapper taskScheduleWrapper;
    @Resource
    TenantServiceHolder tenantServiceHolder;
    @Autowired
    private CallcenterProperty callcenterProperty;

    @Autowired
    private CustomerDataService customerDataService;

    @Autowired
    private AiConversationProcessTaskService aiConversationProcessTaskService;

    @Autowired
    private IAiConversationChatSummaryService aiConversationChatSummaryService;

    @Autowired
    private AiConversationWechatSummaryService aiConversationWechatSummaryService;

    @Autowired
    private IAiAnalysisTaskDataService aiAnalysisTaskDataService;

    @Override
    public AiAnalysisTaskCallRecordPageVO pageList(IPage<AiAnalysisTaskCallRecordVO> page, AiAnalysisTaskCallRecordReq req) {
        fixParam(req);
        if (req.getRecordType() == 0) {
            // 1. 查询分页数据
            IPage<AiAnalysisTaskCallRecordVO> pages = baseMapper.pageList(page, req);

            // 2. 获取请求参数中的筛选条件
//        List<Long> configSceneIds = req.getConfigSceneIds();
//        List<Long> configSceneResultIds = req.getConfigSceneResultIds();

            // 3. 如果有 VO 数据，则加载分析结果并设置到 aiAnalysisTaskResultList
            if (pages.getRecords() != null && !pages.getRecords().isEmpty()) {
                for (AiAnalysisTaskCallRecordVO recordVO : pages.getRecords()) {
                    Long callRecordId = recordVO.getId(); // 当前通话记录ID
                    //手机号脱敏
                    recordVO.setPhone(DesensitizationUtil.hidePhone(recordVO.getPhone(), 3, 4));
                    // 示例：调用自定义方法，传入通话记录ID和可选筛选条件
                    List<AiAnalysisTaskResultVO> results = aiAnalysisTaskResultMapper.listByCallRecordIdAndConditions(callRecordId, null, null);

                    recordVO.setAiAnalysisTaskResultList(results);
                    StringBuffer resultName = new StringBuffer();
                    StringBuffer resultDesc = new StringBuffer();
                    if (CollectionUtils.isNotEmpty(results)) {
                        for (AiAnalysisTaskResultVO result : results) {
                            resultName.append(result.getSceneName() + "-");
                            resultName.append(result.getResultName() + ",");
                            resultDesc.append(result.getSceneName() + "-");
                            resultDesc.append(result.getResultDes() + ",");
                        }
                        resultName.deleteCharAt(resultName.length() - 1);
                        resultDesc.deleteCharAt(resultDesc.length() - 1);
                    }
                    recordVO.setTaskResult(resultName.toString());
                    recordVO.setResultDesc(resultDesc.toString());
                }
            }

            return new AiAnalysisTaskCallRecordPageVO(pages);
        } else if (req.getRecordType() == 1) {
            // 客户分析：按客户维度合并 ai_analysis_task_data
            Page<AiAnalysisTaskCallRecordVO> resultPage = new Page<>(page.getCurrent(), page.getSize());

            // 构建查询条件
            LambdaQueryWrapper<AiAnalysisTaskData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiAnalysisTaskData::getAnalysisTaskId, req.getAnalysisTaskId())
                    .eq(AiAnalysisTaskData::getIsDeleted, 0);

            if (req.getCustomerName() != null && !req.getCustomerName().trim().isEmpty()) {
                queryWrapper.eq(AiAnalysisTaskData::getCustomerName, req.getCustomerName().trim());
            }
            if (req.getCustomerId() != null && !req.getCustomerId().trim().isEmpty()) {
                queryWrapper.eq(AiAnalysisTaskData::getCustomerId, req.getCustomerId().trim());
            }

            if (req.getStatus() != null && !req.getStatus().isEmpty()) {
                queryWrapper.in(AiAnalysisTaskData::getStatus, req.getStatus());
            }

//            if (CollectionUtils.isNotEmpty(req.getConfigSceneIds())) {
//                queryWrapper.in(AiAnalysisTaskData::getSceneId, req.getConfigSceneIds());
//            }

            List<Long> configSceneResultIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(req.getConfigSceneResultIds())) {
                configSceneResultIds.addAll(req.getConfigSceneResultIds());
            } else if (CollectionUtils.isNotEmpty(req.getConfigIds())) {
                for (String configId : req.getConfigIds()) {
                    if (configId == null) {
                        continue;
                    }
                    String trimmedId = configId.trim();
                    if (!trimmedId.startsWith("result_")) {
                        continue;
                    }
                    String idPart = trimmedId.substring("result_".length());
                    if (idPart.isEmpty()) {
                        continue;
                    }
                    try {
                        configSceneResultIds.add(Long.parseLong(idPart));
                    } catch (NumberFormatException e) {
                        log.warn("结果筛选参数解析失败 configId:{}", trimmedId);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(configSceneResultIds)) {
                List<Long> distinctIds = configSceneResultIds.stream().distinct().collect(Collectors.toList());
                List<AiAnalysisConfigSceneResult> sceneResultList = aiAnalysisConfigSceneResultService.listByIds(distinctIds);
                List<String> resultNames = sceneResultList.stream()
                        .map(AiAnalysisConfigSceneResult::getResultName)
                        .filter(name -> name != null && !name.trim().isEmpty())
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(resultNames)) {
                    resultPage.setRecords(Collections.emptyList());
                    resultPage.setTotal(0);
                    resultPage.setPages(0);
                    return new AiAnalysisTaskCallRecordPageVO(resultPage);
                }
                queryWrapper.and(wrapper -> {
                    for (String resultName : resultNames) {
                        wrapper.or().like(AiAnalysisTaskData::getResultName, resultName);
                    }
                });
            }

            queryWrapper.orderByDesc(AiAnalysisTaskData::getCreateTime);

            List<AiAnalysisTaskData> taskDataList = aiAnalysisTaskDataService.list(queryWrapper);
            if (CollectionUtils.isEmpty(taskDataList)) {
                resultPage.setRecords(Collections.emptyList());
                resultPage.setTotal(0);
                resultPage.setPages(0);
                return new AiAnalysisTaskCallRecordPageVO(resultPage);
            }

            Map<String, CustomerAggregate> aggregateMap = new LinkedHashMap<>();
            Map<Long, String> dataIdToCustomerKey = new HashMap<>();
            for (AiAnalysisTaskData taskData : taskDataList) {
                String customerKey = buildCustomerKey(taskData);
                CustomerAggregate aggregate = aggregateMap.computeIfAbsent(customerKey,
                        key -> new CustomerAggregate(taskData));
                aggregate.merge(taskData);
                dataIdToCustomerKey.put(taskData.getId(), customerKey);
            }

            List<Long> dataIds = new ArrayList<>(dataIdToCustomerKey.keySet());
            if (CollectionUtils.isNotEmpty(dataIds)) {
                List<AiAnalysisTaskResultVO> resultList = new ArrayList<>();
                for (List<Long> partition : Lists.partition(dataIds, 1000)) {
                    resultList.addAll(aiAnalysisTaskResultMapper.listByDataIds(partition));
                }
                for (AiAnalysisTaskResultVO result : resultList) {
                    String customerKey = dataIdToCustomerKey.get(result.getAnalysisTaskDataId());
                    if (customerKey == null) {
                        continue;
                    }
                    CustomerAggregate aggregate = aggregateMap.get(customerKey);
                    if (aggregate == null) {
                        continue;
                    }
                    if (CollectionUtils.isNotEmpty(req.getConfigSceneIds())
                            && (result.getSceneId() == null || !req.getConfigSceneIds().contains(result.getSceneId()))) {
                        continue;
                    }
                    if (CollectionUtils.isNotEmpty(configSceneResultIds)
                            && (result.getSceneResultId() == null || !configSceneResultIds.contains(result.getSceneResultId()))) {
                        continue;
                    }
                    aggregate.addResult(result);
                }
            }

            List<AiAnalysisTaskCallRecordVO> aggregatedList = aggregateMap.values().stream()
                    .peek(CustomerAggregate::finalizeResult)
                    .map(CustomerAggregate::getVo)
                    .collect(Collectors.toList());

            long total = aggregatedList.size();
            long current = page.getCurrent();
            long pageSize = page.getSize();
            long fromIndex = (current - 1) * pageSize;
            List<AiAnalysisTaskCallRecordVO> pageRecords;
            if (fromIndex >= total || pageSize <= 0) {
                pageRecords = Collections.emptyList();
            } else {
                long toIndex = Math.min(fromIndex + pageSize, total);
                pageRecords = aggregatedList.subList((int) fromIndex, (int) toIndex);
            }

            resultPage.setRecords(pageRecords);
            resultPage.setTotal(total);
            resultPage.setPages(pageSize <= 0 ? 0 : (total + pageSize - 1) / pageSize);

            return new AiAnalysisTaskCallRecordPageVO(resultPage);
        } else {
            return new AiAnalysisTaskCallRecordPageVO(null);
        }
    }

    private static void fixParam(AiAnalysisTaskCallRecordReq req) {
        if (CollectionUtils.isNotEmpty(req.getConfigIds())) {
            List<Long> configSceneIds = new ArrayList<>();
            List<Long> configSceneResultIds = new ArrayList<>();
            for (String configId : req.getConfigIds()) {
                String[] idStr = configId.split("_");
                if (idStr.length == 2) {
                    if ("scene".equals(idStr[0])) {
                        configSceneIds.add(Long.parseLong(idStr[1]));
                    } else if ("result".equals(idStr[0])) {
                        configSceneResultIds.add(Long.parseLong(idStr[1]));
                    }
                }

            }
            req.setConfigSceneIds(configSceneIds);
            req.setConfigSceneResultIds(configSceneResultIds);
        }
    }

    /**
     * 呼叫记录发起AI分析
     *
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async("commonThreadPoolExecutor")
    public void analysis(CallRecordAnalysisReq request, Long userId, String userName, String tenantId) {
        try {
            LocalDateTime now = LocalDateTime.now();
            Long taskId = createTask(request, userId, userName, tenantId, now);
            List<AiAnalysisConfigScene> aiAnalysisConfigSceneList = aiAnalysisConfigSceneService.listByIds(request.getSceneIds());
            log.info("开始执行AI分析任务，taskId: {}, taskName: {}, analysisType: {}, customerIdList: {}, uuidList: {}, plannerNoList: {}, phone: {}, startTime: {}, endTime: {}",
                    taskId, request.getTaskName(), request.getAnalysisType(), request.getCustomerIdList(), request.getUuidList(), request.getPlannerNoList(), request.getPhone(), request.getStartTime(), request.getEndTime());
            // 根据分析类型进行不同的处理
            if (request.getAnalysisType() != null && request.getAnalysisType() == 1) {
                // 客户ID类型的分析
                processCustomerIdAnalysis(request, taskId, aiAnalysisConfigSceneList, userId, userName, tenantId, now);
            } else {
                // 录音ID类型的分析（原有逻辑）
                processRecordIdAnalysis(request, taskId, aiAnalysisConfigSceneList, userId, userName, tenantId, now);
            }
        } catch (Exception e) {
            log.error("AI分析任务执行失败，taskName: {}, analysisType: {}, error: {}", 
                    request.getTaskName(), request.getAnalysisType(), e.getMessage(), e);
        }
    }

    /**
     * 处理录音ID类型的分析（原有逻辑）
     */
    private void processRecordIdAnalysis(CallRecordAnalysisReq request, Long taskId,
                                         List<AiAnalysisConfigScene> aiAnalysisConfigSceneList,
                                         Long userId, String userName, String tenantId, LocalDateTime now) {
        int pageSize = 100;
        int pageNo = 1;
        //循环写入任务
        IPage<CallRecordSearchRes> callRecordPage;
        request.setPageSize(pageSize);
        while (true) {
            request.setPageNo(pageNo);
            callRecordPage = callRecordService.findPageByTenantId(request, request.getPlannerNoList(), tenantId);
            List<CallRecordSearchRes> records = callRecordPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            for (CallRecordSearchRes record : records) {
                //  创建通话记录与任务关联记录
                Long chatContactId = aiPlannerChatContactService.getChatContactIdByUuid(record.getUuid());
                AiAnalysisTaskCallRecord taskCallRecord = new AiAnalysisTaskCallRecord();
                taskCallRecord.setAnalysisTaskId(taskId);
                taskCallRecord.setCallRecordId(record.getId());
                // 通话记录类型
                taskCallRecord.setRecordType(0);
                if (chatContactId == null) {
                    taskCallRecord.setStatus(AnalysisTaskCallRecordStatusEnum.FAILED.getCode());
                } else {
                    // 初始状态：未开始
                    taskCallRecord.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode());
                }
                taskCallRecord.setCreateTime(now);
                taskCallRecord.setUpdateTime(now);
                taskCallRecord.setCreateUser(userId);
                taskCallRecord.setCreateUserName(userName);
                taskCallRecord.setUpdateUser(userId);
                taskCallRecord.setTenantId(tenantId);
                taskCallRecord.setPlannerNo(record.getPlannerId());
                taskCallRecord.setAgentName(record.getAgentName());
                taskCallRecord.setUuid(record.getUuid());
                taskCallRecord.setCallType(record.getCallType());
                taskCallRecord.setPhone(record.getPhone());
                taskCallRecord.setAgentDept(record.getAgentDept());
                taskCallRecord.setChatContactId(chatContactId);
                taskCallRecord.setRecordUrl(record.getRecordUrl());
                baseMapper.insert(taskCallRecord);
                //   绑定任务与场景（写入 AiAnalysisTaskScene）
                if (CollectionUtils.isNotEmpty(aiAnalysisConfigSceneList)) {
                    List<AiAnalysisTaskResult> taskResults = aiAnalysisConfigSceneList.stream()
                            .map(scene -> {
                                AiAnalysisTaskResult result = new AiAnalysisTaskResult();
                                result.setAnalysisTaskId(taskId);
                                result.setAnalysisTaskCallRecordId(taskCallRecord.getId());
                                result.setChatContactId(chatContactId);
                                result.setSceneId(scene.getId());
                                result.setSceneName(scene.getSceneName());
                                result.setStatus(0);
                                result.setCreateTime(now);
                                result.setUpdateTime(now);
                                result.setCreateUser(userId);
                                result.setCreateUserName(userName);
                                result.setUpdateUser(userId);
                                result.setTenantId(tenantId);
                                return result;
                            }).collect(Collectors.toList());
                    aiAnalysisTaskResultService.saveBatch(taskResults);
                }
            }
            pageNo++;
        }
    }

    /**
     * 处理客户ID类型的分析
     * 新流程：
     * 1. 查询场景列表
     * 2. 为每个客户和场景创建母表记录 (ai_analysis_task_data)
     * 3. 为每个数据源类型创建 call_record 记录，并创建分析任务
     */
    private void processCustomerIdAnalysis(CallRecordAnalysisReq request, Long taskId,
                                          List<AiAnalysisConfigScene> aiAnalysisConfigSceneList,
                                          Long userId, String userName, String tenantId, LocalDateTime now) {
        log.info("处理客户ID类型的分析taskId={}；场景列表：{}", taskId, aiAnalysisConfigSceneList);
        if (CollectionUtils.isEmpty(request.getCustomerIdList())) {
            log.warn("客户ID列表为空");
            throw new BusinessException(ResultCode.FAILURE, "客户ID列表为空");
        }

        if (CollectionUtils.isEmpty(aiAnalysisConfigSceneList)) {
            log.warn("场景列表为空");
            throw new BusinessException(ResultCode.FAILURE, "场景列表为空");
        }

        if (request.getCustomerIdList().size() > 100) {
            log.warn("客户ID列表长度超过100，请分批次处理");
            throw new BusinessException(ResultCode.FAILURE, "客户ID列表长度超过100，请分批次处理");
        }

        String[] dataSourceTypes = request.getAnalysisContent().split(",");

        // 遍历每个客户
        for (String customerId : request.getCustomerIdList()) {
            if (Strings.isNullOrEmpty(customerId)) {
                log.warn("客户ID为空");
                continue;
            }
            // 遍历每个场景，创建母表记录
//            String customerName = "测试用户";
            String customerName = "";
            CustomerInfoWrapper customerInfoWrapper = tenantServiceHolder.getCustomerInfoWrapper(tenantId);
            SimpleUserInfoDto response = customerInfoWrapper.getUserBasicInfoByUserId(customerId, tenantId);
            if(response != null && response.getUserName() != null){
                customerName = response.getUserName();
            }
            String sceneIds = aiAnalysisConfigSceneList.stream().map(AiAnalysisConfigScene::getId).map(String::valueOf).collect(Collectors.joining(","));
            AiAnalysisTaskData taskData = new AiAnalysisTaskData();
            taskData.setAnalysisTaskId(taskId);
            taskData.setSceneIds(sceneIds);
            taskData.setCustomerId(customerId);
            taskData.setCustomerName(customerName);
            taskData.setTotalSubTaskCount(0);
            taskData.setFinishedSubTaskCount(0);
            taskData.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode());
            taskData.setCreateTime(now);
            taskData.setUpdateTime(now);
            taskData.setCreateUser(userId);
            taskData.setCreateUserName(userName);
            taskData.setUpdateUser(userId);
            taskData.setTenantId(tenantId);
            taskData.setIsDeleted(0);
            aiAnalysisTaskDataService.save(taskData);
            Long taskDataId = taskData.getId();
            for (AiAnalysisConfigScene scene : aiAnalysisConfigSceneList) {
                // 1. 创建母表记录 (ai_analysis_task_data)

                // 2. 为每个数据源类型创建 call_record 记录
                for (String sourceType : dataSourceTypes) {
                    sourceType = sourceType.trim();
                    CustomerDataSourceTypeEnum dataSourceEnum = CustomerDataSourceTypeEnum.getByCode(sourceType);
                    if (dataSourceEnum == null) {
                        log.warn("无效的数据源类型: {}", sourceType);
                        continue;
                    }
                    log.info("处理数据源类型: {}", dataSourceEnum);

                    switch (dataSourceEnum) {
                        case CALL_RECORD:
                            processCallRecordsByCustomerId(customerId, request, taskId, taskDataId, scene.getId(), userId, userName, tenantId, now);
                            break;
                        case SMS_RECORD:
                            processSmsRecordsByCustomerId(customerId, taskId, taskDataId, scene.getId(), userId, userName, tenantId, now);
                            break;
                        case WECHAT_RECORD:
                            processWechatRecordsByCustomerId(customerId, request, taskId, taskDataId, scene.getId(), userId, userName, tenantId, now);
                            break;
                        case ENTERPRISE_WECHAT_RECORD:
                            processEnterpriseWechatRecordsByCustomerId(customerId, request, taskId, taskDataId, scene.getId(), userId, userName, tenantId, now);
                            break;
                        case CUSTOMER_BASIC_INFO:
                            processCustomerBasicInfo(customerId, taskId, taskDataId, scene.getId(), userId, userName, tenantId, now);
                            break;
                        case CUSTOMER_BUSINESS_DATA:
                            processCustomerBusinessData(customerId, taskId, taskDataId, scene.getId(), userId, userName, tenantId, now, request.getStartTime(), request.getEndTime());
                            break;
                        default:
                            log.warn("未处理的数据源类型: {}", sourceType);
                            break;
                    }
                }
            }

            // 3. 更新母表的总子任务数
            Integer totalCallRecordCount = baseMapper.selectCount(
                    new LambdaQueryWrapper<AiAnalysisTaskCallRecord>()
                            .eq(AiAnalysisTaskCallRecord::getAnalysisTaskDataId, taskDataId)
                            .eq(AiAnalysisTaskCallRecord::getIsDeleted, 0)
            );

            taskData.setTotalSubTaskCount(totalCallRecordCount);
            if (totalCallRecordCount == 0) {
                taskData.setStatus(AnalysisTaskCallRecordStatusEnum.COMPLETED.getCode());
            }
            aiAnalysisTaskDataService.updateById(taskData);

        }
    }

    private Long createTask(CallRecordAnalysisReq request, Long userId, String userName, String tenantId, LocalDateTime now) {
        // Step 1: 创建 AI 分析任务
        AiAnalysisTask aiAnalysisTask = new AiAnalysisTask();
        aiAnalysisTask.setTaskName(request.getTaskName());
        // 初始状态：未开始
        aiAnalysisTask.setStatus(0);
        aiAnalysisTask.setAnalysisType(request.getAnalysisType());
        aiAnalysisTask.setAnalysisContent(request.getAnalysisContent());
        
        // 保存时间范围（客户分析时使用）
        if (request.getAnalysisType() != null && request.getAnalysisType() == 1) {
            aiAnalysisTask.setStartTime(request.getStartTime());
            aiAnalysisTask.setEndTime(request.getEndTime());
        }
        
        aiAnalysisTask.setCreateTime(now);
        aiAnalysisTask.setUpdateTime(now);
        aiAnalysisTask.setCreateUser(userId);
        aiAnalysisTask.setUpdateUser(userId);
        aiAnalysisTask.setCreateUserName(userName);
        aiAnalysisTask.setTenantId(tenantId);
        aiAnalysisTask.setSourcePage(request.getSourcePage());
        aiAnalysisTaskService.save(aiAnalysisTask);
        Long taskId = aiAnalysisTask.getId();
        if (CollectionUtils.isNotEmpty(request.getSceneIds())) {
            List<AiAnalysisTaskScene> taskScenes = request.getSceneIds().stream()
                    .map(sceneId -> {
                        AiAnalysisTaskScene taskScene = new AiAnalysisTaskScene();
                        taskScene.setTaskId(taskId);
                        taskScene.setSceneId(sceneId);
                        return taskScene;
                    }).collect(Collectors.toList());
            aiAnalysisTaskSceneService.saveBatch(taskScenes);
        }
        return taskId;
    }

    /**
     * 批量导出
     *
     * @param req
     * @return
     */
    @Override
    public CallCenterTask exportBatch(AiAnalysisTaskCallRecordReq req) {
        fixParam(req);
        String paramString = null;
        paramString = JsonUtilExt.beanToJson(req);

        CallCenterTaskCreateDTO create = new CallCenterTaskCreateDTO()
                .setModule("会话分析任务")
                .setTaskName("会话分析任务")
                .setTaskType(TaskTypeEnum.EXPORT.getCode())
                .setBizCode(AI_ANALYSIS_EXPORT_TASK)
                .setFilePath("")
                .setFileName("")
                .setServerName("")
                .setUrl("")
                .setParam(paramString)
                .setUserId(OwnAuthUtil.getUserId())
                .setCreatUser(OwnAuthUtil.getRealName())
                .setTenantId(OwnAuthUtil.getTenantId());
        return taskScheduleWrapper.submitToTaskSchedule(create);
    }

    @Override
    public R<String> analysis(MultipartFile file, CallRecordAnalysisReq request, Long userId, String userName, String tenantId) throws IOException {
        //校验文件必需是 xlsx格式
        if (!(file.getOriginalFilename().endsWith(".xlsx") || file.getOriginalFilename().endsWith(".xls"))) {
            return R.fail("请上传一个Excel文件");
        }
        //文件大小不能超过5M，否则提示【文件不可超过5M】
        if (file.getSize() > 5 * 1024 * 1024) {
            return R.fail("文件不可超过5M");
        }
        //不能为空，否则提示【请上传有数据的文件】
        List<List<CellDto>> rows = ExcelUtil.importExcel(file);
        if (CollectionUtils.isEmpty(rows) || rows.size() <= 1) {
            return R.fail("请上传有数据的文件");
        }
        
        // 根据分析类型判断表头和处理逻辑
        List<CellDto> header = rows.get(0);
        if (CollectionUtils.isEmpty(header)) {
            return R.fail("请下载模版并按照模版填充数据后上传");
        }
        
        String expectedHeader;
        if (request.getAnalysisType() != null && request.getAnalysisType() == 1) {
            // 客户ID分析
            expectedHeader = "客户ID";
        } else {
            // 录音ID分析（默认）
            expectedHeader = "录音ID";
        }
        
        if (!header.get(0).getValue().equals(expectedHeader)) {
            return R.fail("请下载模版并按照模版填充数据后上传");
        }
        
        List<String> idList = rows.stream().skip(1).map(row -> row.get(0).getValue()).collect(Collectors.toList());
        
        // 根据分析类型调用不同的处理方法
        if (request.getAnalysisType() != null && request.getAnalysisType() == 1) {
            // 客户ID分析
            return processCustomerIdImport(idList, request, userId, userName, tenantId);
        } else {
            // 录音ID分析（原有逻辑）
            return processRecordIdImport(idList, request, userId, userName, tenantId);
        }
    }
    
    /**
     * 处理客户ID导入分析
     */
    private R<String> processCustomerIdImport(List<String> customerIdList, CallRecordAnalysisReq request, Long userId, String userName, String tenantId) {
        if (CollectionUtils.isEmpty(customerIdList)) {
            return R.fail("客户ID列表为空");
        }
        
        if (customerIdList.size() > 100) {
            return R.fail("客户ID数量不能超过100个");
        }
        
        // 验证分析内容必填
        if (request.getAnalysisContent() == null || request.getAnalysisContent().trim().isEmpty()) {
            return R.fail("客户ID分析必须选择至少一种分析内容");
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 创建任务
        Long taskId = createTask(request, userId, userName, tenantId, now);
        List<AiAnalysisConfigScene> aiAnalysisConfigSceneList = aiAnalysisConfigSceneService.listByIds(request.getSceneIds());
        
        if (CollectionUtils.isEmpty(aiAnalysisConfigSceneList)) {
            return R.fail("场景配置不存在");
        }
        
        // 设置客户ID列表到request
        request.setCustomerIdList(customerIdList);
        
        // 调用现有的客户ID分析处理逻辑
        processCustomerIdAnalysis(request, taskId, aiAnalysisConfigSceneList, userId, userName, tenantId, now);
        
        // 检查是否有数据被创建
        Integer count = aiAnalysisTaskDataService.count(
                new LambdaQueryWrapper<AiAnalysisTaskData>()
                        .eq(AiAnalysisTaskData::getAnalysisTaskId, taskId)
                        .eq(AiAnalysisTaskData::getIsDeleted, 0)
        );
        
        if (count == 0) {
            // 如果没有创建任何数据，删除任务
            aiAnalysisTaskService.removeById(taskId);
            return R.fail("没有符合条件的客户数据，任务创建失败");
        }
        
        return R.success("任务创建成功，共处理" + customerIdList.size() + "个客户，创建" + count + "条分析数据");
    }
    
    /**
     * 处理录音ID导入分析（原有逻辑）
     */
    private R<String> processRecordIdImport(List<String> uuidList, CallRecordAnalysisReq request, Long userId, String userName, String tenantId) {
        LocalDateTime now = LocalDateTime.now();
        //把 uuidList分成 100 个一组
        int pageSize = 100;
        List<List<String>> uuidListList = Lists.partition(uuidList, pageSize);

        Long taskId = createTask(request, userId, userName, tenantId, now);
        List<AiAnalysisConfigScene> aiAnalysisConfigSceneList = aiAnalysisConfigSceneService.listByIds(request.getSceneIds());
         /*
        数据权限范围内的理财师
         */
        List<String> scopePlannerIds = tenantServiceHolder.getScopePlanners(callcenterProperty.getYumengCallrecordMenuCode());

        int successCount = 0;
        for (List<String> uuidGroup : uuidListList) {
            List<AiAnalysisTaskCallRecord> taskCallRecords = callRecordService.findByUUIDList(uuidGroup, tenantId, scopePlannerIds);
            if (CollectionUtils.isEmpty(taskCallRecords)) {
                break;
            }
            for (AiAnalysisTaskCallRecord taskCallRecord : taskCallRecords) {
                taskCallRecord.setAnalysisTaskId(taskId);
                if (taskCallRecord.getChatContactId() == null) {
                    taskCallRecord.setStatus(AnalysisTaskCallRecordStatusEnum.FAILED.getCode());
                } else {
                    taskCallRecord.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode()); // 初始状态：未开始
                }
                taskCallRecord.setCreateTime(now);
                taskCallRecord.setUpdateTime(now);
                taskCallRecord.setCreateUser(userId);
                taskCallRecord.setCreateUserName(userName);
                taskCallRecord.setUpdateUser(userId);
                taskCallRecord.setTenantId(tenantId);
                baseMapper.insert(taskCallRecord);
                successCount++;
                //   绑定任务与场景（写入 AiAnalysisTaskScene）
                if (CollectionUtils.isNotEmpty(aiAnalysisConfigSceneList)) {
                    List<AiAnalysisTaskResult> taskResults = aiAnalysisConfigSceneList.stream()
                            .map(scene -> {
                                AiAnalysisTaskResult result = new AiAnalysisTaskResult();
                                result.setAnalysisTaskId(taskId);
                                result.setAnalysisTaskCallRecordId(taskCallRecord.getId());
                                result.setChatContactId(taskCallRecord.getChatContactId());
                                result.setSceneId(scene.getId());
                                result.setSceneName(scene.getSceneName());
                                result.setStatus(0);
                                result.setCreateTime(now);
                                result.setUpdateTime(now);
                                result.setCreateUser(userId);
                                result.setCreateUserName(userName);
                                result.setUpdateUser(userId);
                                result.setTenantId(tenantId);
                                return result;
                            }).collect(Collectors.toList());
                    aiAnalysisTaskResultService.saveBatch(taskResults);
                }
            }
        }
        if (successCount == 0) {
            this.baseMapper.deleteById(taskId);
            log.info("符合条件的录音ID数量为0，不可新增任务");
            return R.fail("符合条件的录音ID数量为0，不可新增任务");
        }

        return R.success("任务创建成功");
    }

    /**
     * 处理通话记录（根据客户ID，支持时间范围）
     */
    private void processCallRecordsByCustomerId(String customerId, CallRecordAnalysisReq request, Long taskId, Long taskDataId, Long sceneId,
                                               Long userId, String userName, String tenantId, LocalDateTime now) {
        try {
            // 查询该客户的所有通话记录
            LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiPlannerChatContact::getUserId, customerId)
                    .eq(AiPlannerChatContact::getTenantId, tenantId)
                    .isNotNull(AiPlannerChatContact::getProcessedContent)
                    .in(AiPlannerChatContact::getOriginType, "avaya", "call_center")
                    .ne(AiPlannerChatContact::getProcessedContent, "")
                    .ne(AiPlannerChatContact::getProcessedContent, "[新呼叫中心语音]")
                    .ne(AiPlannerChatContact::getProcessedContent, "[avaya语音]")
                    .orderByDesc(AiPlannerChatContact::getMsgTime)
                    .last("limit 100");

            // 添加时间范围过滤
            if (request != null) {
                if (request.getStartTime() != null) {
                    queryWrapper.ge(AiPlannerChatContact::getMsgTime, request.getStartTime());
                }
                if (request.getEndTime() != null) {
                    queryWrapper.le(AiPlannerChatContact::getMsgTime, request.getEndTime());
                }
            }

            List<AiPlannerChatContact> chatContacts = aiPlannerChatContactService.list(queryWrapper);

            for (AiPlannerChatContact contact : chatContacts) {
                // 创建call_record记录
                AiAnalysisTaskCallRecord callRecord = new AiAnalysisTaskCallRecord();
                callRecord.setAnalysisTaskId(taskId);
                callRecord.setAnalysisTaskDataId(taskDataId);
                callRecord.setSceneId(sceneId);
                callRecord.setCustomerId(customerId);
                callRecord.setDataSourceType(CustomerDataSourceTypeEnum.CALL_RECORD.getCode());
                callRecord.setDataSourceId(contact.getId());
                callRecord.setChatContactId(contact.getId());
                callRecord.setPlannerNo(contact.getPlannerNo());
                callRecord.setRecordType(1);
                callRecord.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode());
                callRecord.setCreateTime(now);
                callRecord.setUpdateTime(now);
                callRecord.setCreateUser(userId);
                callRecord.setCreateUserName(userName);
                callRecord.setUpdateUser(userId);
                callRecord.setTenantId(tenantId);
                callRecord.setIsDeleted(0);
                baseMapper.insert(callRecord);

                // 创建分析任务，使用callRecordId
                String taskIdStr = TaskIdUtil.generateTaskId(
                        String.valueOf(AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode()),
                        String.valueOf(CustomerDataSourceTypeEnum.CALL_RECORD.getCode()),
                        callRecord.getId()
                );

                aiConversationProcessTaskService.createProcessTask(
                        taskIdStr,
                        callRecord.getId(),
                        AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode(),
                        AiConversationProcessStatusEnum.LOW_PRIORITY_INIT.getCode(),
                        tenantId,
                        String.valueOf(CustomerDataSourceTypeEnum.CALL_RECORD.getCode())
                );

                log.info("创建通话记录分析任务: customerId={}, callRecordId={}, taskId={}",
                        customerId, callRecord.getId(), taskIdStr);
            }
        } catch (Exception e) {
            log.error("处理通话记录失败: customerId={}", customerId, e);
        }
    }

    /**
     * 处理短信记录（根据客户ID）
     */
    private void processSmsRecordsByCustomerId(String customerId, Long taskId, Long taskDataId, Long sceneId,
                                              Long userId, String userName, String tenantId, LocalDateTime now) {
        try {
            // 创建call_record记录 - 短信记录直接用客户ID，不需要具体的dataSourceId
            AiAnalysisTaskCallRecord callRecord = new AiAnalysisTaskCallRecord();
            callRecord.setAnalysisTaskId(taskId);
            callRecord.setAnalysisTaskDataId(taskDataId);
            callRecord.setSceneId(sceneId);
            callRecord.setCustomerId(customerId);
            callRecord.setDataSourceType(CustomerDataSourceTypeEnum.SMS_RECORD.getCode());
            // 短信记录使用客户ID，不需要具体的dataSourceId
            callRecord.setDataSourceId(null);
            callRecord.setRecordType(1);
            callRecord.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode());
            callRecord.setCreateTime(now);
            callRecord.setUpdateTime(now);
            callRecord.setCreateUser(userId);
            callRecord.setCreateUserName(userName);
            callRecord.setUpdateUser(userId);
            callRecord.setTenantId(tenantId);
            callRecord.setIsDeleted(0);
            baseMapper.insert(callRecord);

            // 创建分析任务，使用callRecordId
            String taskIdStr = TaskIdUtil.generateTaskId(
                    String.valueOf(AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode()),
                    String.valueOf(CustomerDataSourceTypeEnum.SMS_RECORD.getCode()),
                    callRecord.getId()
            );

            aiConversationProcessTaskService.createProcessTask(
                    taskIdStr,
                    callRecord.getId(),
                    AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode(),
                    AiConversationProcessStatusEnum.LOW_PRIORITY_INIT.getCode(),
                    tenantId,
                    String.valueOf(CustomerDataSourceTypeEnum.SMS_RECORD.getCode())
            );

            log.info("创建短信记录分析任务: customerId={}, callRecordId={}, taskId={}", customerId, callRecord.getId(), taskIdStr);
        } catch (Exception e) {
            log.error("处理短信记录失败: customerId={}", customerId, e);
        }
    }

    /**
     * 处理微信记录（根据客户ID）
     */
    private void processWechatRecordsByCustomerId(String customerId, CallRecordAnalysisReq request, Long taskId, Long taskDataId, Long sceneId,
                                                  Long userId, String userName, String tenantId, LocalDateTime now) {
        try {
            // 查询该客户的微信聊天小结
            LambdaQueryWrapper<AiConversationChatSummary> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiConversationChatSummary::getCustomerId, customerId)
                    .eq(AiConversationChatSummary::getTenantId, tenantId)
                    .orderByDesc(AiConversationChatSummary::getCreateTime);

            // 添加时间范围过滤
            if (request.getStartTime() != null) {
                queryWrapper.ge(AiConversationChatSummary::getChatDate, request.getStartTime().toLocalDate());
            }
            if (request.getEndTime() != null) {
                queryWrapper.le(AiConversationChatSummary::getChatDate, request.getEndTime().toLocalDate());
            }

            queryWrapper.last("limit 100");

            List<AiConversationChatSummary> chatSummaries = aiConversationChatSummaryService.list(queryWrapper);

            for (AiConversationChatSummary summary : chatSummaries) {
                // 创建call_record记录
                AiAnalysisTaskCallRecord callRecord = new AiAnalysisTaskCallRecord();
                callRecord.setAnalysisTaskId(taskId);
                callRecord.setAnalysisTaskDataId(taskDataId);
                callRecord.setSceneId(sceneId);
                callRecord.setCustomerId(customerId);
                callRecord.setDataSourceType(CustomerDataSourceTypeEnum.WECHAT_RECORD.getCode());
                callRecord.setDataSourceId(summary.getId());
                callRecord.setChatContactId(summary.getId());
                callRecord.setPlannerNo(summary.getPlannerNo());
                callRecord.setRecordType(1);
                callRecord.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode());
                callRecord.setCreateTime(now);
                callRecord.setUpdateTime(now);
                callRecord.setCreateUser(userId);
                callRecord.setCreateUserName(userName);
                callRecord.setUpdateUser(userId);
                callRecord.setTenantId(tenantId);
                callRecord.setIsDeleted(0);
                baseMapper.insert(callRecord);

                // 创建分析任务，使用callRecordId
                String taskIdStr = TaskIdUtil.generateTaskId(
                        String.valueOf(AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode()),
                        String.valueOf(CustomerDataSourceTypeEnum.WECHAT_RECORD.getCode()),
                        callRecord.getId()
                );

                aiConversationProcessTaskService.createProcessTask(
                        taskIdStr,
                        callRecord.getId(),
                        AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode(),
                        AiConversationProcessStatusEnum.LOW_PRIORITY_INIT.getCode(),
                        tenantId,
                        String.valueOf(CustomerDataSourceTypeEnum.WECHAT_RECORD.getCode())
                );

                log.info("创建微信记录分析任务: customerId={}, callRecordId={}, taskId={}",
                        customerId, callRecord.getId(), taskIdStr);
            }
        } catch (Exception e) {
            log.error("处理微信记录失败: customerId={}", customerId, e);
        }
    }

    /**
     * 处理企微记录（根据客户ID）
     */
    private void processEnterpriseWechatRecordsByCustomerId(String customerId, CallRecordAnalysisReq request, Long taskId, Long taskDataId, Long sceneId,
                                                            Long userId, String userName, String tenantId, LocalDateTime now) {
        try {
            // 查询该客户的企微聊天小结
            LambdaQueryWrapper<AiConversationWechatSummary> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiConversationWechatSummary::getUserId, customerId)
                    .eq(AiConversationWechatSummary::getTenantId, tenantId)
                    .orderByDesc(AiConversationWechatSummary::getCreateTime);

            // 添加时间范围过滤
            if (request.getStartTime() != null) {
                queryWrapper.ge(AiConversationWechatSummary::getCreateTime, request.getStartTime());
            }
            if (request.getEndTime() != null) {
                queryWrapper.le(AiConversationWechatSummary::getCreateTime, request.getEndTime());
            }

            queryWrapper.last("limit 100");

            List<AiConversationWechatSummary> wechatSummaries = aiConversationWechatSummaryService.list(queryWrapper);

            for (AiConversationWechatSummary summary : wechatSummaries) {
                // 创建call_record记录
                AiAnalysisTaskCallRecord callRecord = new AiAnalysisTaskCallRecord();
                callRecord.setAnalysisTaskId(taskId);
                callRecord.setAnalysisTaskDataId(taskDataId);
                callRecord.setSceneId(sceneId);
                callRecord.setCustomerId(customerId);
                callRecord.setDataSourceType(CustomerDataSourceTypeEnum.ENTERPRISE_WECHAT_RECORD.getCode());
                callRecord.setDataSourceId(summary.getId());
                callRecord.setChatContactId(summary.getId());
                callRecord.setPlannerNo(summary.getPlannerId());
                callRecord.setRecordType(1);
                callRecord.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode());
                callRecord.setCreateTime(now);
                callRecord.setUpdateTime(now);
                callRecord.setCreateUser(userId);
                callRecord.setCreateUserName(userName);
                callRecord.setUpdateUser(userId);
                callRecord.setTenantId(tenantId);
                callRecord.setIsDeleted(0);
                baseMapper.insert(callRecord);

                // 创建分析任务，使用callRecordId
                String taskIdStr = TaskIdUtil.generateTaskId(
                        String.valueOf(AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode()),
                        String.valueOf(CustomerDataSourceTypeEnum.ENTERPRISE_WECHAT_RECORD.getCode()),
                        callRecord.getId()
                );

                aiConversationProcessTaskService.createProcessTask(
                        taskIdStr,
                        callRecord.getId(),
                        AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode(),
                        AiConversationProcessStatusEnum.LOW_PRIORITY_INIT.getCode(),
                        tenantId,
                        String.valueOf(CustomerDataSourceTypeEnum.ENTERPRISE_WECHAT_RECORD.getCode())
                );

                log.info("创建企微记录分析任务: customerId={}, callRecordId={}, taskId={}",
                        customerId, callRecord.getId(), taskIdStr);
            }
        } catch (Exception e) {
            log.error("处理企微记录失败: customerId={}", customerId, e);
        }
    }

    /**
     * 处理客户基本信息
     */
    private void processCustomerBasicInfo(String customerId, Long taskId, Long taskDataId, Long sceneId,
                                         Long userId, String userName, String tenantId, LocalDateTime now) {
        try {
            // 查询客户基本信息
            CustomerBasicInfoVo customerBasicInfo = customerDataService.getCustomerBasicInfo(customerId, tenantId);
            if (customerBasicInfo == null) {
                log.warn("未找到客户基本信息: customerId={}", customerId);
                return;
            }

            // 转换为JSON字符串（附带字段描述）
            String basicInfoJson = buildJsonWithApiDescription(customerBasicInfo);

            // 尝试从customerAndAssetResp中获取客户姓名，更新母表
            String customerName = null;
            if (customerBasicInfo.getCustomerAndAssetResp() != null
                    && customerBasicInfo.getCustomerAndAssetResp().getUser() != null) {
                customerName = customerBasicInfo.getCustomerAndAssetResp().getUser().getName();
            }
            if (customerName != null) {
                AiAnalysisTaskData taskData = aiAnalysisTaskDataService.getById(taskDataId);
                if (taskData != null && taskData.getCustomerName() == null) {
                    taskData.setCustomerName(customerName);
                    aiAnalysisTaskDataService.updateById(taskData);
                }
            }

            // 创建call_record记录
            AiAnalysisTaskCallRecord callRecord = new AiAnalysisTaskCallRecord();
            callRecord.setAnalysisTaskId(taskId);
            callRecord.setAnalysisTaskDataId(taskDataId);
            callRecord.setSceneId(sceneId);
            callRecord.setCustomerId(customerId);
            callRecord.setDataSourceType(CustomerDataSourceTypeEnum.CUSTOMER_BASIC_INFO.getCode());
            callRecord.setDataSourceId(null);
            callRecord.setCustomerBaseData(basicInfoJson);
            callRecord.setRecordType(1);
            callRecord.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode());
            callRecord.setCreateTime(now);
            callRecord.setUpdateTime(now);
            callRecord.setCreateUser(userId);
            callRecord.setCreateUserName(userName);
            callRecord.setUpdateUser(userId);
            callRecord.setTenantId(tenantId);
            callRecord.setIsDeleted(0);
            baseMapper.insert(callRecord);

            // 创建分析任务，使用callRecordId
            String taskIdStr = TaskIdUtil.generateTaskId(
                    String.valueOf(AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode()),
                    String.valueOf(CustomerDataSourceTypeEnum.CUSTOMER_BASIC_INFO.getCode()),
                    callRecord.getId()
            );

            aiConversationProcessTaskService.createProcessTask(
                    taskIdStr,
                    callRecord.getId(),
                    AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode(),
                    AiConversationProcessStatusEnum.LOW_PRIORITY_INIT.getCode(),
                    tenantId,
                    String.valueOf(CustomerDataSourceTypeEnum.CUSTOMER_BASIC_INFO.getCode())
            );

            log.info("创建客户基本信息分析任务: customerId={}, callRecordId={}, taskId={}", customerId, callRecord.getId(), taskIdStr);
        } catch (Exception e) {
            log.error("处理客户基本信息失败: customerId={}", customerId, e);
        }
    }

    /**
     * 处理客户业务数据
     */
    private void processCustomerBusinessData(String customerId, Long taskId, Long taskDataId, Long sceneId,
                                            Long userId, String userName, String tenantId, LocalDateTime now,
                                             LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.info("处理客户业务数据: customerId={}", customerId);

            CustomerBusinessInfoVo customerBusinessInfo = customerDataService.getCustomerBusinessInfo(customerId, tenantId, startTime, endTime);
            if (customerBusinessInfo == null) {
                log.warn("未找到客户业务信息: customerId={}", customerId);
                return;
            }

            // 创建call_record记录
            AiAnalysisTaskCallRecord callRecord = new AiAnalysisTaskCallRecord();
            callRecord.setAnalysisTaskId(taskId);
            callRecord.setAnalysisTaskDataId(taskDataId);
            callRecord.setSceneId(sceneId);
            callRecord.setCustomerId(customerId);
            callRecord.setDataSourceType(CustomerDataSourceTypeEnum.CUSTOMER_BUSINESS_DATA.getCode());
            callRecord.setDataSourceId(null);
            callRecord.setCustomerBusinessData(buildJsonWithApiDescription(customerBusinessInfo));
            callRecord.setRecordType(1);
            callRecord.setStatus(AnalysisTaskCallRecordStatusEnum.NOT_STARTED.getCode());
            callRecord.setCreateTime(now);
            callRecord.setUpdateTime(now);
            callRecord.setCreateUser(userId);
            callRecord.setCreateUserName(userName);
            callRecord.setUpdateUser(userId);
            callRecord.setTenantId(tenantId);
            callRecord.setIsDeleted(0);
            baseMapper.insert(callRecord);

            // 创建分析任务，使用callRecordId
            String taskIdStr = TaskIdUtil.generateTaskId(
                    String.valueOf(AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode()),
                    String.valueOf(CustomerDataSourceTypeEnum.CUSTOMER_BUSINESS_DATA.getCode()),
                    callRecord.getId()
            );

            aiConversationProcessTaskService.createProcessTask(
                    taskIdStr,
                    callRecord.getId(),
                    AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.getCode(),
                    AiConversationProcessStatusEnum.LOW_PRIORITY_INIT.getCode(),
                    tenantId,
                    String.valueOf(CustomerDataSourceTypeEnum.CUSTOMER_BUSINESS_DATA.getCode())
            );

            log.info("创建客户业务数据分析任务: customerId={}, callRecordId={}, taskId={}", customerId, callRecord.getId(), taskIdStr);
        } catch (Exception e) {
            log.error("处理客户业务数据失败: customerId={}", customerId, e);
        }
    }
    private String buildCustomerKey(AiAnalysisTaskData taskData) {
        if (taskData == null) {
            return "UNKNOWN";
        }
        String customerId = taskData.getCustomerId();
        if (customerId != null) {
            String trimmed = customerId.trim();
            if (!trimmed.isEmpty()) {
                return trimmed;
            }
        }
        return "DATA_" + taskData.getId();
    }

    /**
     * 构建包含 @ApiModelProperty 描述信息的 JSON 字符串。
     * 若解析失败则回退为原始 JSON 序列化，以避免影响业务流程。
     */
    private String buildJsonWithApiDescription(Object source) {
        if (source == null) {
            return null;
        }
        try {
            Object enriched = enrichObjectWithDescription(source, new IdentityHashMap<>());
            return JSON.toJSONString(enriched);
        } catch (Exception ex) {
            log.warn("构建带描述的JSON失败，使用原始序列化，class:{}", source.getClass().getName(), ex);
            try {
                return JSON.toJSONString(source);
            } catch (Exception jsonEx) {
                log.warn("JSON 序列化失败，改用 toString，class:{}", source.getClass().getName(), jsonEx);
                return source.toString();
            }
        }
    }

    private Object enrichObjectWithDescription(Object value, IdentityHashMap<Object, Object> visited) {
        if (value == null) {
            return null;
        }
        Class<?> clazz = value.getClass();
        if (isSimpleValueType(clazz)) {
            return value;
        }

        Object cached = visited.get(value);
        if (cached != null) {
            return cached;
        }
        if (visited.containsKey(value)) {
            return null;
        }
        // 占位，防止循环引用
        visited.put(value, null);

        Object result;
        if (clazz.isArray()) {
            int length = Array.getLength(value);
            List<Object> list = new ArrayList<>(length);
            for (int i = 0; i < length; i++) {
                Object element = Array.get(value, i);
                list.add(enrichObjectWithDescription(element, visited));
            }
            result = list;
        } else if (value instanceof Collection) {
            Collection<?> collection = (Collection<?>) value;
            List<Object> list = new ArrayList<>(collection.size());
            for (Object element : collection) {
                list.add(enrichObjectWithDescription(element, visited));
            }
            result = list;
        } else if (value instanceof Map) {
            Map<?, ?> originalMap = (Map<?, ?>) value;
            Map<Object, Object> map = new LinkedHashMap<>(originalMap.size());
            for (Map.Entry<?, ?> entry : originalMap.entrySet()) {
                Object key = entry.getKey();
                Object mapKey = (key == null || isSimpleValueType(key.getClass())) ? key : String.valueOf(key);
                map.put(mapKey, enrichObjectWithDescription(entry.getValue(), visited));
            }
            result = map;
        } else {
            Map<String, Object> map = new LinkedHashMap<>();
            for (Field field : getAllFields(clazz)) {
                if (Modifier.isStatic(field.getModifiers())) {
                    continue;
                }
                try {
                    ReflectionUtils.makeAccessible(field);
                    Object fieldValue = ReflectionUtils.getField(field, value);
                    Object convertedValue = enrichObjectWithDescription(fieldValue, visited);
                    map.put(field.getName(), convertedValue);
                    ApiModelProperty property = field.getAnnotation(ApiModelProperty.class);
                    if (property != null) {
                        String desc = property.value();
                        if (desc != null && !desc.trim().isEmpty()) {
                            map.put(field.getName() + "_desc", desc);
                        }
                    }
                } catch (Exception ex) {
                    log.warn("处理字段失败: {}.{}, error:{}", clazz.getSimpleName(), field.getName(), ex.getMessage());
                }
            }
            result = map;
        }

        visited.put(value, result);
        return result;
    }

    private static class CustomerAggregate {
        private final AiAnalysisTaskCallRecordVO vo;
        private final LinkedHashSet<String> resultNames = new LinkedHashSet<>();
        private final LinkedHashSet<String> resultDescs = new LinkedHashSet<>();

        CustomerAggregate(AiAnalysisTaskData taskData) {
            this.vo = new AiAnalysisTaskCallRecordVO();
            this.vo.setId(taskData.getId());
            this.vo.setAnalysisTaskId(taskData.getAnalysisTaskId());
            this.vo.setAnalysisTaskDataId(taskData.getId());
            this.vo.setCustomerId(taskData.getCustomerId());
            this.vo.setCustomerName(taskData.getCustomerName());
            this.vo.setAgentName(taskData.getCustomerName());
            this.vo.setStatus(taskData.getStatus());
            this.vo.setAnalysisTime(taskData.getAnalysisTime());
            this.vo.setCreateTime(taskData.getCreateTime());
            this.vo.setUpdateTime(taskData.getUpdateTime());
            this.vo.setRecordType(1);
            addValues(taskData.getResultName(), resultNames);
            addValues(taskData.getResultDes(), resultDescs);
        }

        void merge(AiAnalysisTaskData taskData) {
            if (taskData == null) {
                return;
            }
            if (this.vo.getCustomerName() == null || this.vo.getCustomerName().trim().isEmpty()) {
                this.vo.setCustomerName(taskData.getCustomerName());
                this.vo.setAgentName(taskData.getCustomerName());
            }
            if (taskData.getStatus() != null) {
                if (this.vo.getStatus() == null || taskData.getStatus() > this.vo.getStatus()) {
                    this.vo.setStatus(taskData.getStatus());
                }
            }
            if (taskData.getAnalysisTime() != null) {
                if (this.vo.getAnalysisTime() == null || taskData.getAnalysisTime().isAfter(this.vo.getAnalysisTime())) {
                    this.vo.setAnalysisTime(taskData.getAnalysisTime());
                }
            }
            if (taskData.getUpdateTime() != null) {
                if (this.vo.getUpdateTime() == null || taskData.getUpdateTime().isAfter(this.vo.getUpdateTime())) {
                    this.vo.setUpdateTime(taskData.getUpdateTime());
                }
            }
            if (taskData.getCreateTime() != null) {
                if (this.vo.getCreateTime() == null || taskData.getCreateTime().isBefore(this.vo.getCreateTime())) {
                    this.vo.setCreateTime(taskData.getCreateTime());
                }
            }

            addValues(taskData.getResultName(), resultNames);
            addValues(taskData.getResultDes(), resultDescs);
        }

        void addResult(AiAnalysisTaskResultVO result) {
            if (result == null) {
                return;
            }
            addValues(result.getResultName(), resultNames);
            addValues(result.getResultDes(), resultDescs);
        }

        void finalizeResult() {
            if (!resultNames.isEmpty()) {
                this.vo.setTaskResult(String.join(",", resultNames));
            }
            if (!resultDescs.isEmpty()) {
                this.vo.setResultDesc(String.join(",", resultDescs));
            }
        }

        AiAnalysisTaskCallRecordVO getVo() {
            return vo;
        }

        private void addValues(String raw, LinkedHashSet<String> target) {
            if (raw == null) {
                return;
            }
            String[] parts = raw.split("[;；]");
            for (String part : parts) {
                if (part == null) {
                    continue;
                }
                String trimmed = part.trim();
                if (trimmed.isEmpty()) {
                    continue;
                }
                target.add(trimmed);
            }
        }
    }

    private List<Field> getAllFields(Class<?> type) {
        List<Field> fields = new ArrayList<>();
        Class<?> current = type;
        while (current != null && current != Object.class) {
            fields.addAll(Arrays.asList(current.getDeclaredFields()));
            current = current.getSuperclass();
        }
        return fields;
    }

    private boolean isSimpleValueType(Class<?> clazz) {
        if (clazz.isPrimitive() || clazz.isEnum()) {
            return true;
        }
        return CharSequence.class.isAssignableFrom(clazz)
                || Number.class.isAssignableFrom(clazz)
                || Boolean.class.isAssignableFrom(clazz)
                || Character.class.isAssignableFrom(clazz)
                || Date.class.isAssignableFrom(clazz)
                || Temporal.class.isAssignableFrom(clazz)
                || BigDecimal.class.isAssignableFrom(clazz)
                || BigInteger.class.isAssignableFrom(clazz);
    }
}
