package com.yirendai.voiceaiserver.service;

import com.yirendai.workbench.entity.AdsCsgBigFixedAchievementDf;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 业绩达成-类固收相关-大额001 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public interface AdsCsgBigFixedAchievementDfService extends IService<AdsCsgBigFixedAchievementDf> {

    List<AdsCsgBigFixedAchievementDf> getPlannerAchievements();

    List<AdsCsgBigFixedAchievementDf> getPlannerUsers();
}
