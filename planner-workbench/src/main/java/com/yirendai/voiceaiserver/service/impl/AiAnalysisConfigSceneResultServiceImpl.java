package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.service.IAiAnalysisConfigSceneResultService;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisConfigSceneResultVO;
import com.yirendai.workbench.entity.AiAnalysisConfigSceneResult;
import com.yirendai.workbench.mapper.AiAnalysisConfigSceneResultMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * ai分析场景分析结果选项配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Service
public class AiAnalysisConfigSceneResultServiceImpl extends ServiceImpl<AiAnalysisConfigSceneResultMapper, AiAnalysisConfigSceneResult> implements IAiAnalysisConfigSceneResultService {

    /**
     * 根据场景ID查询场景分析结果选项
     *
     * @param sceneId 场景ID
     * @return
     */
    @Override
    public List<AiAnalysisConfigSceneResultVO> listBySceneId(Long sceneId) {
        return baseMapper.selectBySceneId(sceneId);
    }

    @Override
    public int selectCountBySceneResultName(String resultName, Long sceneId,Long excludeId) {
        return baseMapper.selectCountBySceneResultName(resultName, sceneId,excludeId);
    }
}
