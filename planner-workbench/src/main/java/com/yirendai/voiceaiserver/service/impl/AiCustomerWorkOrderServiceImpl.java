package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.service.AiCustomerWorkOrderService;
import com.yirendai.workbench.entity.AiCustomerWorkOrder;
import com.yirendai.workbench.mapper.AiCustomerWorkOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AI客服工单表(AiCustomerWorkOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03 17:00:08
 */
@Slf4j
@Service
public class AiCustomerWorkOrderServiceImpl extends ServiceImpl<AiCustomerWorkOrderMapper, AiCustomerWorkOrder> implements AiCustomerWorkOrderService {

    @Override
    public AiCustomerWorkOrder getLatestAiCustomerWorkOrder(String voiceId, Long templateId, String tenantId, Integer type) {
        return this.lambdaQuery().eq(AiCustomerWorkOrder::getTenantId, tenantId)
                .eq(AiCustomerWorkOrder::getCallUuid, voiceId)
                .eq(AiCustomerWorkOrder::getIsDeleted, false)
                .eq(AiCustomerWorkOrder::getTemplateId, templateId)
                .eq(AiCustomerWorkOrder::getType, type)
                .orderByDesc(AiCustomerWorkOrder::getId)
                .last("limit 1")
                .one();
    }

    @Override
    public List<AiCustomerWorkOrder> findAiCustomerWorkOrder(String voiceId, Long templateId, String tenantId, Integer type) {
        return this.lambdaQuery().eq(AiCustomerWorkOrder::getTenantId, tenantId)
                .eq(AiCustomerWorkOrder::getCallUuid, voiceId)
                .eq(AiCustomerWorkOrder::getIsDeleted, false)
                .eq(AiCustomerWorkOrder::getTemplateId, templateId)
                .eq(AiCustomerWorkOrder::getType, type)
                .orderByDesc(AiCustomerWorkOrder::getId)
                .list();
    }
}
