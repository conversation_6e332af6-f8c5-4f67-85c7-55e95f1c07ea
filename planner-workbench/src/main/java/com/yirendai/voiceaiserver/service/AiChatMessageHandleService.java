package com.yirendai.voiceaiserver.service;


import java.io.File;

/**
 * 聊天消息转换接口
 *
 * <AUTHOR>
 */
public interface AiChatMessageHandleService {

    /**
     * 图片类型转换成文字,并更新数据库
     */
    void pictureHandle();

    /**
     * 分批处理图片转文字
     */
    void pictureSplitHandle(Long start, Long end);

    /**
     * 混合类型转换成文字,并更新数据库
     */
    void mixedHandle();

    /**
     * 调用远程接口提取图片中的文字
     * @param file 图片文件
     * @return 拼接后的文字
     */
    String pictureToText(File file);
}
