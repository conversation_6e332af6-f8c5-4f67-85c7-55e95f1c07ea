package com.yirendai.voiceaiserver.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.MsgSendDirectionEnum;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.enums.VoiceStatusEnum;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.entity.*;
import com.yirendai.voiceaiserver.model.owmuc.UserVerifyInfo;
import com.yirendai.voiceaiserver.model.owmuc.YrZwUserRelation;
import com.yirendai.voiceaiserver.service.*;
import com.yirendai.voiceaiserver.util.AudioSplitUtil;
import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.voiceaiserver.util.HttpClientProxyUtil;
import com.yirendai.voiceaiserver.util.CommonUtil;
import com.yirendai.voiceaiserver.vo.db.AvayaGroupVO;
import com.yirendai.voiceaiserver.vo.db.AvayaPlannerSeq;
import com.yirendai.voiceaiserver.vo.db.AvayaPlannerUserSeq;
import com.yirendai.voiceaiserver.vo.db.PlannerAchievementInfo;
import com.yirendai.voiceaiserver.vo.request.PromptReq;
import com.yirendai.voiceaiserver.vo.request.SendRobotReq;
import com.yirendai.voiceaiserver.vo.response.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yirendai.workbench.service.AdsCsgDOrderDetailDfService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import com.yirendai.workbench.service.IPlannerUserBindService;
import com.yirendai.workbench.service.impl.PlannerUserBindServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import redis.clients.jedis.JedisCluster;

/**
 * <AUTHOR>
 * @time 2024/6/14 14:14
 **/
@Service
@Slf4j
@RefreshScope
public class AvayaVoiceService {

    @Resource
    JdbcTemplate voiceJbcTemplate;

    @Resource
    JdbcTemplate avayaJdbcTemplate;

    @Resource
    RestTemplate restTemplateVoice;

    @Resource
    IAiPlannerChatContactService iAiPlannerChatContactService;

    @Resource
    IAiPlannerTopUserService aiPlannerTopUserService;

    @Resource
    AiVoiceToTextService aiVoiceToTextService;

    @Resource
    ChatService chatService;

    @Resource
    EmailSender emailSender;

    @Resource
    JedisCluster jedisCluster;

    @Resource
    AdsCsgDOrderDetailDfService adsCsgDOrderDetailDfService;

    @Resource
    YrZwUserRelationService yrZwUserRelationService;

    @Resource
    UserVerifyInfoService userVerifyInfoService;

    @Resource
    AiPlannerVoiceService aiPlannerVoiceService;

    @Resource
    AiChatAvayaDistService aiChatAvayaDistService;

    @Resource
    AiPromptRecordService aiPromptRecordService;

    @Resource
    IPlannerUserBindService iPlannerUserBindService;

    @Resource
    AIConversationProcessService aiConversationProcessService;

    @Resource
    @Qualifier("avayaSplitCrmThreadPoolExecutor")
    ExecutorService avayaSplitCrmThreadPoolExecutor;

    @Resource
    @Qualifier("avayaAddThreadPoolExecutor")
    ExecutorService avayaAddThreadPoolExecutor;

    @Value("${nas.path}")
    private String path;

    @Value("${nas.url}")
    private String nasUrl;

    @Value("${spring.mail.mail.to}")
    private String mailTo;

    @Value("${qw.robot.memory.url}")
    private String robotMemoryUrl;

    @Value("${qw.robot.model.url}")
    private String robotModelUrl;

    @Value("${qw.robot.sync.url}")
    private String robotSyncUrl;

    @Value("${scrm.url}")
    private String scrmUrl;

    @Value("${user.info.url}")
    private String userInfoUrl;

    @Value("${yr.tenant.id}")
    private String yrTenantId;

    /**
     * avaya语音-分割大模型提示词key
     */
    public static final String KEY_AVAYA_SPLIT_MODEL_PROMPT = "voice:ai:server:avaya:split:model:prompst";

    /**
     * avaya语音-分割大模型告警次数key
     */
    public static final String KEY_AVAYA_SPLIT_MODEL_ERR_NOTICE = "voice:ai:server:avaya:split:model:err:notice";

    /**
     * avaya语音-分割大模型默认提示词
     */
    public static final String QUESTION =
            "以下是理财师和客户的完整对话内容，请把完整对话内容按照理财师和客户两个人一问一答的形式进行格式处理，并且在每句话句首拼接对应的说话人，格式为：\"理财师:\"或\"客户:\"，保留完整对话内容，仅做内容拆分，无需总结，只输出拆分结果。\n"
                    + "完整对话内容为：%s\n\n"
                    + "请直接输出拆分结果为：";

    /**
     * avaya语音-识别说话人提示词
     */
    public static final String QUESTION_RECOGNITION =
            "以下是理财师和客户的完整对话内容，可能会有杂音，现在不知道哪个说话人是理财师、哪个是客户，请根据全部对话内容识别出哪个是理财师，结果输出示例为“说话人x”。\n\n"
                    + "完整对话内容为：\n%s\n\n"
                    + "请直接输出分析结果为：";

    public static final String MODEL_MAX = "qwen-max";

    public static final LocalTime SEVEN = LocalTime.of(7, 0, 0);

    public static final LocalTime TWENTY = LocalTime.of(20, 0, 0);

    public static final String KEY_AVAYA_MODEL_SPLIT_BATCH = "voice:ai:server:avaya:model:split:batch";

    /**
     * 全量crm客户数据处理批次
     */
    private static final String KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH = "voice:ai:server:all:crm:customer:handle:batch";

    /**
     * 全量crm客户数据处理批次id
     */
    private static final String KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH_ID = "voice:ai:server:all:crm:customer:handle:batch:id";

    /**
     * 切割增量Avaya语料任务开始处理的消息idKey
     */
    private static final String KEY_AVAYA_SPLIT_ADD_START_ID = "voice:ai:server:avaya:split:add:start:id";

    /**
     * 切割指定时间段内的Avaya语料任务当前批次开始处理的消息idKey
     */
    private static final String KEY_AVAYA_SPLIT_TIME_PERIOD_BATCH_START_ID = "voice:ai:server:avaya:split:time:period:batch:start:id";

    /**
     * 切割指定时间段内的Avaya语料任务当前批次处理的消息id范围
     */
    private static final String KEY_AVAYA_SPLIT_TIME_PERIOD_BATCH_ID_RANGE = "voice:ai:server:avaya:split:time:period:batch:id:range";

    /**
     * 切割指定时间段内的Avaya语料任务当前处理的id索引
     */
    private static final String KEY_AVAYA_SPLIT_TIME_PERIOD_HANDLE_INDEX = "voice:ai:server:avaya:split:time:period:handle:index";

    private static final Pattern INTEGER_PATTERN = Pattern.compile("^-?\\d+$");

    public List<AvayaGroupVO> getGroupsUser() {
        return aiPlannerTopUserService.getGroups();
    }

    @Async("avayaSyncThreadPoolExecutor")
    public void syncAvayaVoice(Integer start, Integer end) {
        log.info("同步avaya通话记录开始, start={}, end={}", start, end);
        Integer handleStart = start;
        while (start.compareTo(end) < 0) {
            LambdaQueryWrapper<AiPlannerTopUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(AiPlannerTopUser::getId, start).le(AiPlannerTopUser::getId, end)
                    .orderByAsc(AiPlannerTopUser::getId).last("limit 100");
            List<AiPlannerTopUser> list = aiPlannerTopUserService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                log.info("syncAvayaVoice is over");
                break;
            }
            for (AiPlannerTopUser topUser : list) {
                dealAiPlannerTopUser(topUser);

                // 检查内存空间，剩余可用空间不足15%时发邮件&企微通知
                File file = new File(path);
                long usableSize = file.getUsableSpace();
                long allSize = file.getTotalSpace();
                double usableRate = ((double) usableSize) / ((double) allSize);
                if (usableRate <= 0.15) {
                    double usable = usableSize / 1024.0 / 1024.0 / 1024.0;
                    String str = String.format("nas可用空间剩余 %.2f GB，不足百分之15，请及时处理。", usable);

                    emailSender.sendEmail("[理财师AI助手]-[可用空间不足预警]", mailTo, null, str);

                    SendRobotReq.Content content = new SendRobotReq.Content();
                    content.setContent(str);
                    SendRobotReq sendRobotReq = new SendRobotReq();
                    sendRobotReq.setText(content);
                    sendRobotReq.setMsgtype("text");
                    try {
                        HttpClientProxyUtil.getInstance().postForJSon(robotMemoryUrl, JSON.toJSONString(sendRobotReq));
                    } catch (Exception e) {
                        log.error("发送企微群机器人通知失败", e);
                    }

                    log.info("nas可用空间不足，同步avaya通话记录结束, start={}, end={}", handleStart, end);
                    return;
                }
            }
            if (list.size() < 100) {
                break;
            }
            start = list.get(list.size() - 1).getId();
        }
        log.info("同步avaya通话记录结束, start={}, end={}", handleStart, end);
    }

    private void dealAiPlannerTopUser(AiPlannerTopUser topUser) {
        String staffId = topUser.getPlannerNo();
        String userId = topUser.getUserId();
        LocalDateTime maxMsgTime = iAiPlannerChatContactService.getMaxMsgTime(staffId, userId);
        LocalDateTime startTime = Objects.isNull(maxMsgTime) ?
                Objects.isNull(topUser.getEarliestOrderTime()) ?
                        topUser.getCreateTime() : topUser.getEarliestOrderTime().minusMonths(6)
                : maxMsgTime;
        LocalDateTime endTime = LocalDateTime.now();

        LocalDateTime tempEnd = startTime.plusYears(1L);
        while (tempEnd.isBefore(endTime)) {
            String voiceSql = "select VOICE_ID, caller, callee, CUS_NAME cusName, CUS_ID cusId, voice_direct_id as voiceDirectId from CX_BAS_VOICE_SEQ"
                    + " where CUS_ID = '" + userId + "' and staffid = '" + staffId
                    + "' and ring_time >= '" + DateUtil.formatLocalDateTime(startTime)
                    + "' and ring_time <= '" + DateUtil.formatLocalDateTime(tempEnd)
                    + "' and long_time > 1 order by ring_time asc";
            log.info("voiceSql {}", voiceSql);
            List<VoiceSeq> voiceSeqList = avayaJdbcTemplate.query(voiceSql, new BeanPropertyRowMapper<>(VoiceSeq.class));
            for (VoiceSeq voiceSeq : voiceSeqList) {
                dealSingleVoiceSeq(staffId, userId, voiceSeq,
                        DateUtil.formatLocalDateTime(startTime), DateUtil.formatLocalDateTime(tempEnd));
            }
            startTime = tempEnd.plusSeconds(1L);
            tempEnd = tempEnd.plusYears(1L);
        }

        String voiceSql = "select VOICE_ID, caller, callee, CUS_NAME cusName, CUS_ID cusId, voice_direct_id as voiceDirectId from CX_BAS_VOICE_SEQ"
                + " where CUS_ID = '" + userId + "' and staffid = '" + staffId
                + "' and ring_time >= '" + DateUtil.formatLocalDateTime(startTime)
                + "' and ring_time <= '" + DateUtil.formatLocalDateTime(endTime)
                + "' and long_time > 1 order by ring_time asc";
        log.info("voiceSql {}", voiceSql);
        List<VoiceSeq> voiceSeqList = avayaJdbcTemplate.query(voiceSql, new BeanPropertyRowMapper<>(VoiceSeq.class));
        for (VoiceSeq voiceSeq : voiceSeqList) {
            dealSingleVoiceSeq(staffId, userId, voiceSeq, DateUtil.formatLocalDateTime(startTime), DateUtil.formatLocalDateTime(endTime));
        }
    }

    private void dealSingleVoiceSeq(String plannerNo, String userId, VoiceSeq voiceSeq, String startTime, String endTime) {
        log.info("voiceSeq {}", voiceSeq);
        int amount = 300;
        String start = DateUtil.formatDateTime(
                DateUtil.offsetSecond(DateUtil.offsetHour(DateUtil.parseDateTime(startTime), -8), -amount));
        String end = DateUtil.formatDateTime(
                DateUtil.offsetSecond(DateUtil.offsetHour(DateUtil.parseDateTime(endTime), -8), amount));
        String voiceCaseIdSql =
                "select VOICE_CASE_ID,CALL_START_TIME from INTERACTIONS where CALLING_PARTY='" + voiceSeq.getCaller()
                        + "' and CALLED_PARTY='" + voiceSeq.getCallee() + "' and CALL_START_TIME BETWEEN '" + start + "' and '" + end + "'"
                        + " union select VOICE_CASE_ID,CALL_START_TIME from INTERACTIONS_HISTORY where CALLING_PARTY='" + voiceSeq.getCaller()
                        + "' and CALLED_PARTY='" + voiceSeq.getCallee() + "' and CALL_START_TIME BETWEEN '" + start + "' and '" + end + "'";
        log.info("voiceCaseIdSql {}", voiceCaseIdSql);
        List<Map<String, Object>> list = voiceJbcTemplate.queryForList(voiceCaseIdSql);
        if (CollUtil.isNotEmpty(list)) {
            List<String> voiceCaseIdList = list.stream().map(v -> MapUtil.getStr(v, "VOICE_CASE_ID")).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(AiPlannerChatContact::getBusId, voiceCaseIdList)
                    .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType());
            List<AiPlannerChatContact> aiPlannerChatContactList = iAiPlannerChatContactService.list(queryWrapper);
            if (CollUtil.isNotEmpty(aiPlannerChatContactList)) {
                List<String> busIdList = aiPlannerChatContactList.stream().map(AiPlannerChatContact::getBusId).collect(Collectors.toList());
                list = list.stream().filter(v -> !busIdList.contains(MapUtil.getStr(v, "VOICE_CASE_ID"))).collect(Collectors.toList());
            }

            String cusPhone = getCusPhoneByCallType(voiceSeq.getVoiceDirectId(), voiceSeq.getCallee(), voiceSeq.getCaller());
            for (Map<String, Object> id : list) {
                Object voice_case_id = id.get("VOICE_CASE_ID");
                Object callStartTime = id.get("CALL_START_TIME");
                dealSingleVoice(plannerNo, userId, voice_case_id.toString(), callStartTime.toString(), voiceSeq.getVoiceId(), cusPhone);
            }
        }
    }

    private void dealSingleVoice(String plannerNo, String userId, String voiceCaseId, String callStartTime,
                                 String voiceId, String cusPhone) {
        log.info("voiceInfo {}", voiceCaseId);
        String fileName = "avaya/" + voiceCaseId + ".wav";
        String fileBakName = "avayacp/" + voiceCaseId + ".wav";
        try {
            String url = "http://10.135.3.24:33023/awfos/decodeVoiceCaseId?voiceCaseId=" + voiceCaseId
                    + "&audioOutputFormat=wav&userLoginId=supervisor&tenantName=yirendai";
            String response = restTemplateVoice.getForEntity(url, String.class).getBody();
            if (StrUtil.isBlank(response)) {
                return;
            }
            String substring = response.substring(response.indexOf("callURL=") + 8);
            String wavUrl = substring.substring(0, substring.indexOf("&"));
            wavUrl = URLDecoder.decode(URLDecoder.decode(wavUrl, "UTF-8"), "UTF-8");
            ResponseEntity<byte[]> responseEntity = restTemplateVoice.getForEntity(wavUrl, byte[].class);
            String filePath = FileUtil.byteToFileCheckExist(responseEntity.getBody(), path, fileName, fileBakName);
            AiPlannerChatContact aiPlannerChatContact = new AiPlannerChatContact();
            aiPlannerChatContact.setPlannerNo(plannerNo);
            aiPlannerChatContact.setUserId(userId);
            aiPlannerChatContact.setDirection(MsgSendDirectionEnum.FROM_CRM.getCode());
            aiPlannerChatContact.setNasPath(path + filePath);
            aiPlannerChatContact.setOriginContent(nasUrl + filePath);
            aiPlannerChatContact.setOriginType(QwMsgTypeEnum.AVAYA.getType());
            aiPlannerChatContact.setPhone(cusPhone);
            aiPlannerChatContact.setMsgTime(
                    LocalDateTime.parse(callStartTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S")).plusHours(8L));
            aiPlannerChatContact.setBusId(voiceCaseId);
            aiPlannerChatContact.setAvayaId(voiceId);
            try {
                long duration = Math.round(AudioSplitUtil.getWavDuration(new File(path + filePath)));
                aiPlannerChatContact.setLongTime(duration);
            } catch (Exception e) {
                log.error("获取音频时长失败，失败原因为", e);
            }
            iAiPlannerChatContactService.save(aiPlannerChatContact);
        } catch (Exception e) {
            log.error("dealSingleVoice error", e);
        }
    }

    public List<AvayaGroupVO> getGroups() {
        return iAiPlannerChatContactService.getGroups();
    }

    @Async("avayaTranslateThreadPoolExecutor")
    public void avayaVoice2text(Long start, Long end) {
        log.info("avaya语音音转文开始，start={}, end={}", start, end);
        Long handleStart = start;
        while (start.compareTo(end) < 0) {
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(AiPlannerChatContact::getId, start).le(AiPlannerChatContact::getId, end)
                    .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                    .isNull(AiPlannerChatContact::getProcessingContent)
                    .isNotNull(AiPlannerChatContact::getNasPath)
                    .orderByAsc(AiPlannerChatContact::getId).last("limit 100");
            List<AiPlannerChatContact> aiPlannerChatContactList = iAiPlannerChatContactService.list(wrapper);
            if (CollectionUtils.isEmpty(aiPlannerChatContactList)) {
                log.info("avayaVoice2text is over");
                break;
            }

            for (AiPlannerChatContact aiPlannerChatContact : aiPlannerChatContactList) {
                try {
                    File file = new File(aiPlannerChatContact.getNasPath());
                    String text = getTextPer(file);
                    if (Objects.isNull(text)) {
                        continue;
                    }
                    aiPlannerChatContact.setProcessingContent(text);
                    if ("[avaya语音]".equals(text)) {
                        aiPlannerChatContact.setProcessedContent(text);
                    }
                    iAiPlannerChatContactService.saveOrUpdate(aiPlannerChatContact);
                } catch (Exception e) {
                    log.error("avayaVoice2text error", e);
                }
            }

            if (aiPlannerChatContactList.size() < 100) {
                break;
            }
            start = aiPlannerChatContactList.get(aiPlannerChatContactList.size() - 1).getId();
        }
        log.info("avaya语音音转文结束, start={},end={}", handleStart, end);
    }

    public String getTextPer(File file) {
        List<File> fileList = new ArrayList<>();
        try {
            fileList.addAll(FileUtil.splitAudioFile(file, 30 * 60, 0));
            StringBuilder stringBuilder = new StringBuilder();
            for (File perFile : fileList) {
                String text = aiVoiceToTextService.voiceToText(perFile, "ce-asr");
                if (Objects.isNull(text) || "null".equals(text)) {
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "调用音转文接口返回解析数据为null");
                }
                stringBuilder.append(text);
            }
            if (StringUtils.isNotBlank(stringBuilder.toString())) {
                return stringBuilder.toString();
            } else {
                return "[avaya语音]";
            }
        } catch (Exception e) {
            log.error("getTextPer error", e);
        } finally {
            if (!CollectionUtils.isEmpty(fileList)) {
                for (File perFile : fileList) {
                    perFile.delete();
                }
            }
        }
        return null;
    }

    public List<AvayaGroupVO> getHandleList(Integer size) {
        String cache = jedisCluster.get(KEY_AVAYA_MODEL_SPLIT_BATCH);
        List<AvayaGroupVO> list = null;
        if (StrUtil.isBlank(cache)) {
            list = iAiPlannerChatContactService.getGroupsModel();
        } else {
            list = JSON.parseArray(cache, AvayaGroupVO.class);
            if (CollUtil.isEmpty(list)) {
                list = iAiPlannerChatContactService.getGroupsModel();
            }
        }
        List<AvayaGroupVO> handleList = list.subList(0, Math.min(list.size(), size));
        if (handleList.size() >= list.size()) {
            jedisCluster.del(KEY_AVAYA_MODEL_SPLIT_BATCH);
        } else {
            list = list.subList(handleList.size(), list.size());
            jedisCluster.setex(KEY_AVAYA_MODEL_SPLIT_BATCH, 24 * 60 * 60, JSON.toJSONString(list));
        }
        return handleList;
    }

    @Async("avayaSplitThreadPoolExecutor")
    public void splitText(Long start, Long end, boolean isNight) {
        log.info("切割Avaya语料开始, start={}, end={}", start, end);
        String prompt = jedisCluster.get(KEY_AVAYA_SPLIT_MODEL_PROMPT);
        if (StrUtil.isBlank(prompt)) {
            prompt = QUESTION;
        }
        Long handleStart = start;
        while (start.compareTo(end) < 0) {
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(AiPlannerChatContact::getId, start).le(AiPlannerChatContact::getId, end)
                    .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                    .and(w -> w.isNull(AiPlannerChatContact::getProcessedContent).or().eq(AiPlannerChatContact::getProcessedContent, ""))
                    .isNotNull(AiPlannerChatContact::getProcessingContent).ne(AiPlannerChatContact::getProcessingContent, "")
                    .orderByAsc(AiPlannerChatContact::getId).last("limit 100");
            List<AiPlannerChatContact> contactList = iAiPlannerChatContactService.list(wrapper);
            if (CollectionUtils.isEmpty(contactList)) {
                break;
            }

            for (AiPlannerChatContact contact : contactList) {
                if (StringUtils.isBlank(contact.getProcessingContent()) || StringUtils.isNotBlank(contact.getProcessedContent())) {
                    continue;
                }

                LocalTime now = LocalTime.now();
                if (isNight && now.isAfter(SEVEN) && now.isBefore(TWENTY)) {
                    log.info("切割Avaya语料结束, 执行时间已到, start={}, end={}", handleStart, end);
                    return;
                }
                if (!isNight && (now.isBefore(SEVEN) || now.isAfter(TWENTY))) {
                    log.info("切割Avaya语料结束, 执行时间已到, start={}, end={}", handleStart, end);
                    return;
                }

                try {
                    List<List<String>> resList = new ArrayList<>();
                    int count = (int) Math.ceil(((double) contact.getProcessingContent().length()) / 2500.0);
                    for (int i = 0; i < count; i++) {
                        String handle = contact.getProcessingContent().substring(2500 * i, Math.min(2500 * (i + 1), contact.getProcessingContent().length()));
                        String question = String.format(prompt, handle);
                        String response = chatService.chat(question, MODEL_MAX, null);
                        if (StringUtils.isBlank(response)) {
                            throw new AiServerException(ResultCode.FAILURE.getCode(), "生成拆分结果失败");
                        }
                        // 模型异常告警（偶发异常告警，1分钟内连续告警3次时10分钟内不告警）
                        if (response.contains("调用OpenAI失败")) {
                            String countErr = jedisCluster.get(KEY_AVAYA_SPLIT_MODEL_ERR_NOTICE);
                            if (StrUtil.isBlank(countErr) || Integer.parseInt(countErr) < 3 && jedisCluster.ttl(KEY_AVAYA_SPLIT_MODEL_ERR_NOTICE) >= 10 * 60) {
                                SendRobotReq.Content content = new SendRobotReq.Content();
                                content.setContent("模型返回结果为" + response + "\n请协助检查模型运行情况，谢谢！！");
                                if (StrUtil.isBlank(countErr) || Integer.parseInt(countErr) < 2) {
                                    content.setMentioned_list(Collections.singletonList("yanli239"));
                                } else {
                                    content.setMentioned_list(Arrays.asList("congwang", "penglei3", "yanli239"));
                                }
                                SendRobotReq sendRobotReq = new SendRobotReq();
                                sendRobotReq.setText(content);
                                sendRobotReq.setMsgtype("text");
                                try {
                                    HttpClientProxyUtil.getInstance().postForJSon(robotModelUrl, JSON.toJSONString(sendRobotReq));
                                } catch (Exception e) {
                                    log.error("发送企微群机器人通知失败", e);
                                }

                                if (StrUtil.isBlank(countErr)) {
                                    jedisCluster.setex(KEY_AVAYA_SPLIT_MODEL_ERR_NOTICE, 11 * 60, "1");
                                } else {
                                    jedisCluster.incr(KEY_AVAYA_SPLIT_MODEL_ERR_NOTICE);
                                }
                            } else {
                                throw new AiServerException(ResultCode.FAILURE.getCode(), "大模型异常");
                            }
                        }
                        ChatApiRes chatApiRes = JSON.parseObject(response, ChatApiRes.class);
                        String answer = chatApiRes.getChoices().get(0).getMessage().getContent().replaceAll("\\*", "");
                        if (answer.contains("后续内容") && answer.contains("省略")) {
                            throw new AiServerException(ResultCode.FAILURE.getCode(), "拆分失败：部分内容省略");
                        }
                        String[] arr = answer.split("(\\r?\\n)+");
                        List<String> finalList = Arrays.stream(arr).map(String::trim).collect(Collectors.toList());
                        OptionalInt optionalIndex = IntStream.range(0, finalList.size())
                                .filter(t -> !finalList.get(t).startsWith("理财师:") && !finalList.get(t).startsWith("客户:")).findFirst();
                        List<String> list = optionalIndex.isPresent() ?
                                optionalIndex.getAsInt() >= finalList.size() / 2 ?
                                        finalList.subList(0, optionalIndex.getAsInt())
                                        : finalList.subList(optionalIndex.getAsInt() + 1, finalList.size())
                                : finalList;
                        list  = list.stream().filter(s -> s.startsWith("理财师:") && s.length() > 4 || s.startsWith("客户:") && s.length() > 3).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(list) || String.join("", list).length() < handle.length()
                                || (String.join("", list).length() - 4 * list.size()) > handle.length() * 1.3) {
                            throw new AiServerException(ResultCode.FAILURE.getCode(), "拆分失败：处理结果不满足拆分要求");
                        }
                        List<String> check = list.stream().filter(s -> s.startsWith("客户:")).collect(Collectors.toList());
                        if (answer.length() > 500 && (CollectionUtils.isEmpty(check) || check.size() < 4)) {
                            throw new AiServerException(ResultCode.FAILURE.getCode(), "拆分失败：未完全拆分");
                        }
                        resList.add(list);
                    }

                    StringBuilder res = new StringBuilder();
                    res.append(String.join("\n", resList.get(0)));
                    for (int i = 1; i < resList.size(); i++) {
                        if (resList.get(i - 1).get(resList.get(i - 1).size() - 1).startsWith("理财师:") && resList.get(i).get(0).startsWith("理财师:")
                                || resList.get(i - 1).get(resList.get(i - 1).size() - 1).startsWith("客户:") && resList.get(i).get(0).startsWith("客户:")) {
                            res.append(resList.get(i).get(0).substring(resList.get(i).get(0).indexOf(":") + 1).trim()).append("\n")
                                    .append(String.join("\n", resList.get(i).subList(1, resList.get(i).size())));
                        } else {
                            res.append("\n").append(String.join("\n", resList.get(i)));
                        }
                    }
                    contact.setProcessedContent(res.toString());
                    iAiPlannerChatContactService.updateById(contact);
                } catch (Exception e) {
                    log.error("切割语料id={}失败，失败原因为", contact.getId(), e);
                }
            }
            if (contactList.size() < 100) {
                break;
            }
            start = contactList.get(contactList.size() - 1).getId();
        }
        log.info("切割Avaya语料结束, start={}, end={}", handleStart, end);
    }

    public int countOccurrences(String str, String subStr) {
        int count = 0;
        int idx = 0;

        // 检查子字符串是否为空
        if (subStr == null || subStr.length() == 0) {
            return 0;
        }

        // 循环查找子字符串并更新计数器
        while ((idx = str.indexOf(subStr, idx)) != -1) {
            count++;
            idx += subStr.length(); // 移动到下一个可能的出现位置
        }

        return count;
    }

    public BigDecimal countAvayaVoice() {
        LambdaQueryWrapper<AiPlannerChatContact> chatWrapper = new LambdaQueryWrapper<>();
        chatWrapper.eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                .isNotNull(AiPlannerChatContact::getOriginContent).ne(AiPlannerChatContact::getOriginContent, "")
                .orderByDesc(AiPlannerChatContact::getId).last("LIMIT 1");
        AiPlannerChatContact chat = iAiPlannerChatContactService.getOne(chatWrapper);
        if (Objects.isNull(chat)) {
            log.warn("数据异常：未查询到avaya通话语音");
            return null;
        }
        log.info("当前avaya通话记录最大id为{}", chat.getId());
        LambdaQueryWrapper<AiPlannerTopUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(AiPlannerTopUser::getPlannerNo, chat.getPlannerNo()).eq(AiPlannerTopUser::getUserId, chat.getUserId());
        AiPlannerTopUser user = aiPlannerTopUserService.getOne(userWrapper);
        if (Objects.isNull(user)) {
            log.warn("数据异常：未查询到avaya通话语音对应的理财师-客户");
            return null;
        }

        BigDecimal count = BigDecimal.ZERO;
        Integer id = user.getId();
        while (true) {
            LambdaQueryWrapper<AiPlannerTopUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(AiPlannerTopUser::getId, id).orderByAsc(AiPlannerTopUser::getId).last(" limit 100");
            List<AiPlannerTopUser> list = aiPlannerTopUserService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                log.info("统计结束");
                break;
            }
            for (AiPlannerTopUser topUser : list) {
                String staffId = topUser.getPlannerNo();
                String userId = topUser.getUserId();
                LocalDateTime maxMsgTime = iAiPlannerChatContactService.getMaxMsgTime(staffId, userId);
                LocalDateTime startTime = Objects.isNull(maxMsgTime) ?
                        Objects.isNull(topUser.getEarliestOrderTime()) ?
                                topUser.getCreateTime() : topUser.getEarliestOrderTime().minusMonths(6)
                        : maxMsgTime;
                LocalDateTime endTime = LocalDateTime.now();

                String voiceSql = "select sum(long_time) from CX_BAS_VOICE_SEQ"
                        + " where CUS_ID = '" + userId + "' and staffid = '" + staffId
                        + "' and ring_time >= '" + DateUtil.formatLocalDateTime(startTime)
                        + "' and ring_time <= '" + DateUtil.formatLocalDateTime(endTime)
                        + "' and long_time > 0";
                BigDecimal sumLongTime = avayaJdbcTemplate.queryForObject(voiceSql, BigDecimal.class);
                if (Objects.nonNull(sumLongTime)) {
                    count = count.add(sumLongTime);
                }
            }

            if (list.size() < 100) {
                break;
            }
            id = list.get(list.size() - 1).getId();
            log.info("当前处理理财师&客户关系id为{}，已统计量为{}秒", id, count);
        }

        log.info("待同步avaya语音的通讯时长共{}秒", count);
        return count;
    }

    public void updateModelPrompt(PromptReq req) {
        if (!req.getContent().contains("%s")) {
            throw new AiServerException(ResultCode.PARAM_INVALID);
        }
        jedisCluster.set(KEY_AVAYA_SPLIT_MODEL_PROMPT, req.getContent());
    }

    public List<PlannerAchievementInfo> getPlannerList() {
        String sql = "select distinct STAFFID as staffId, AGENT_NAME as agentName from cx_bas_voice_seq "
                + "where ring_time > '2020-12-01' and long_time > 1 and STAFFID is not null and STAFFID != '' and STAFFID != 'admin' "
                + "and AGENT_NAME is not null and AGENT_NAME != '' and AGENT_NAME != 'null'";
        List<AvayaPlannerSeq> avayaList = avayaJdbcTemplate.query(sql, new BeanPropertyRowMapper<>(AvayaPlannerSeq.class));
        return avayaList.stream().filter(a -> StrUtil.isNotBlank(a.getStaffId()) && StrUtil.isNotBlank(a.getAgentName()))
                .map(a -> PlannerAchievementInfo.builder().plannerNo(a.getStaffId()).plannerName(a.getAgentName()).build())
                .collect(Collectors.toList());
    }

    /**
     * 从avaya语音记录中同步理财师+客户关系
     * @param isInit 用于区分是否初始同步
     */
    public void syncPlannerUserWithAvaya(Boolean isInit) {
        log.info("从avaya语音记录中同步理财师+客户关系开始");
        LocalDateTime startTime = null;
        if (Boolean.FALSE.equals(isInit)) {
            startTime = iAiPlannerChatContactService.getAllMaxMsgTime();
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        while (true) {
            StringBuilder sql = new StringBuilder("select distinct STAFFID as staffId, CUS_ID as cusId, RING_TIME as ringTime from cx_bas_voice_seq where ");
            if (Objects.isNull(startTime)) {
                sql.append("RING_TIME > '2020-12-01'");
            } else {
                sql.append("RING_TIME >= '").append(DateUtil.formatLocalDateTime(startTime)).append("'");
            }
            sql.append(" and LONG_TIME > 1 and CUS_ID is not null and CUS_ID != '' and STAFFID is not null and STAFFID != '' and STAFFID != 'admin' ")
                    .append("order by RING_TIME asc limit 100");
            List<AvayaPlannerUserSeq> avayaList = avayaJdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(AvayaPlannerUserSeq.class));
            if (CollUtil.isEmpty(avayaList)) {
                break;
            }

            List<AvayaPlannerUserSeq> handleList = avayaList.stream()
                    .filter(a -> StrUtil.isNotBlank(a.getStaffId()) && NumberUtil.isNumber(a.getCusId()) && !a.getCusId().contains("E")).collect(Collectors.toList());
            List<AvayaPlannerUserSeq> zwList = avayaList.stream()
                    .filter(a -> StrUtil.isNotBlank(a.getStaffId()) && StrUtil.isNotBlank(a.getCusId())
                            && a.getCusId().startsWith("ZW") && !a.getCusId().equals("ZW")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(zwList)) {
                Set<Long> zwIdSet = zwList.stream().map(a -> Long.parseLong(a.getCusId().replaceAll("ZW", ""))).collect(Collectors.toSet());
                LambdaQueryWrapper<YrZwUserRelation> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(YrZwUserRelation::getZwUserId, zwIdSet);
                List<YrZwUserRelation> relationList = yrZwUserRelationService.list(wrapper);
                if (CollUtil.isNotEmpty(relationList)) {
                    Map<Long, Long> transferMap = relationList.stream()
                            .collect(Collectors.toMap(YrZwUserRelation::getZwUserId, YrZwUserRelation::getYrUserId));
                    for (AvayaPlannerUserSeq seq : zwList) {
                        Long zwId = Long.parseLong(seq.getCusId().replaceAll("ZW", ""));
                        if (transferMap.containsKey(zwId)) {
                            seq.setCusId(String.valueOf(transferMap.get(zwId)));
                            handleList.add(seq);
                        }
                    }
                }
            }
            if (CollUtil.isEmpty(handleList)) {
                continue;
            }

            Map<String, Set<String>> avayaMap = handleList.stream().collect(Collectors.groupingBy(AvayaPlannerUserSeq::getStaffId,
                    Collectors.mapping(AvayaPlannerUserSeq::getCusId, Collectors.toSet())));
            for (Map.Entry<String, Set<String>> entry : avayaMap.entrySet()) {
                Set<String> userIdSet = entry.getValue();
                LambdaQueryWrapper<AiPlannerTopUser> wrapper = new LambdaQueryWrapper<>();
                wrapper.select(AiPlannerTopUser::getUserId)
                        .eq(AiPlannerTopUser::getPlannerNo, entry.getKey())
                        .in(AiPlannerTopUser::getUserId, userIdSet)
                        .eq(AiPlannerTopUser::getTenantId, yrTenantId);
                List<AiPlannerTopUser> list = aiPlannerTopUserService.list(wrapper);
                if (CollUtil.isNotEmpty(list)) {
                    Set<String> topUserSet = list.stream().map(AiPlannerTopUser::getUserId).collect(Collectors.toSet());
                    userIdSet.removeAll(topUserSet);
                    avayaMap.put(entry.getKey(), userIdSet);
                }
            }
            avayaMap = avayaMap.entrySet().stream().filter(a -> CollUtil.isNotEmpty(a.getValue()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            LocalDateTime now = LocalDateTimeUtil.now();
            if (MapUtil.isNotEmpty(avayaMap)) {
                Map<String, PlannerVO> plannerMap = new HashMap<>();
                try {
                    plannerMap = adsCsgDOrderDetailDfService.getPlannerMap(new ArrayList<>(avayaMap.keySet()));
                } catch (Exception e) {
                    log.info("调用scrm接口查询理财师信息发生异常，异常原因为", e);
                }
                List<String> userIdList = avayaMap.values().stream().flatMap(Set::stream).distinct().collect(Collectors.toList());
                Map<String, YrUserVO> userMap = new HashMap<>();
                Map<String, String> ucUserMap = new HashMap<>();
                int roundUser = (int) Math.ceil(((double) userIdList.size()) / 100.0);
                for (int i = 0; i < roundUser; i++) {
                    List<String> subList = userIdList.subList(i * 100, Math.min((i + 1) * 100, userIdList.size()));
                    Map<String, List<String>> param = new HashMap<>();
                    param.put("yrUserIdList", subList);
                    String response = restTemplateVoice.postForEntity(scrmUrl + userInfoUrl, param, String.class).getBody();
                    if (StrUtil.isBlank(response)) {
                        log.error("调用Scrm接口查询宜人用户信息返回为空，宜人用户id为[{}]", String.join(",", subList));
                    } else {
                        R<List<YrUserVO>> result = JSON.parseObject(response, new TypeReference<R<List<YrUserVO>>>() {});
                        if (!result.isSuccess() || CollUtil.isEmpty(result.getData())) {
                            log.error("调用Scrm接口查询宜人用户信息失败，宜人用户id为[{}]", String.join(",", subList));
                        } else {
                            for (YrUserVO user : result.getData()) {
                                userMap.put(user.getYrUserId(), user);
                            }
                        }
                    }

                    List<Long> userIdsVerify = subList.stream().filter(id -> !userMap.containsKey(id))
                            .map(Long::valueOf).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(userIdsVerify)) {
                        LambdaQueryWrapper<UserVerifyInfo> wrapper = new LambdaQueryWrapper<>();
                        wrapper.in(UserVerifyInfo::getUserId, userIdsVerify).eq(UserVerifyInfo::getStatus, "1");
                        List<UserVerifyInfo> verifyList = userVerifyInfoService.list(wrapper);
                        if (CollUtil.isNotEmpty(verifyList)) {
                            for (UserVerifyInfo v : verifyList) {
                                if (StrUtil.isNotBlank(v.getUserName())) {
                                    ucUserMap.put(String.valueOf(v.getUserId()), v.getUserName());
                                }
                            }
                        }
                    }
                }

                for (Map.Entry<String, Set<String>> entry : avayaMap.entrySet()) {
                    String plannerNo = entry.getKey();
                    for (String cusId : entry.getValue()) {
                        AiPlannerTopUser topUser = new AiPlannerTopUser();
                        topUser.setPlannerNo(plannerNo);
                        if (plannerMap.containsKey(plannerNo)) {
                            topUser.setPlannerName(plannerMap.get(plannerNo).getPlannerName());
                            topUser.setPlannerQwNo(plannerMap.get(plannerNo).getWxUserId());
                        }
                        topUser.setUserId(cusId);
                        if (userMap.containsKey(cusId) || ucUserMap.containsKey(cusId)) {
                            topUser.setUserName(userMap.containsKey(cusId) && StrUtil.isNotBlank(userMap.get(cusId).getName()) ?
                                    userMap.get(cusId).getName() : ucUserMap.getOrDefault(cusId, null));
                            if (userMap.containsKey(cusId)) {
                                topUser.setUserUnionId(userMap.get(cusId).getUnionid());
                                topUser.setCreateTime(Objects.isNull(userMap.get(cusId).getRegisterTime()) ?
                                        Objects.isNull(userMap.get(cusId).getCreatedAt()) ?
                                                now
                                                : userMap.get(cusId).getCreatedAt().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime()
                                        : userMap.get(cusId).getRegisterTime().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
                            }
                        }
                        aiPlannerTopUserService.save(topUser);
                    }
                }
            }

            if (avayaList.size() < 100) {
                break;
            }
            startTime = LocalDateTime.parse(avayaList.get(avayaList.size() - 1).getRingTime(), formatter);
        }
        log.info("从avaya语音记录中同步理财师+客户关系结束");
    }

    /**
     * 同步全量/增量avaya语音
     * @param start 开始批次id
     * @param startTime 开始同步时间
     */
    public void syncAllAvaya(Integer start, LocalDateTime startTime) {
        StringBuilder strStart = new StringBuilder("同步");
        StringBuilder strEnd = new StringBuilder("同步");
        StringBuilder strFail = new StringBuilder("同步");
        if (Objects.nonNull(startTime)) {
            strStart.append("增量");
            strEnd.append("增量");
            strFail.append("增量");
        } else {
            strStart.append("全量");
            strEnd.append("全量");
            strFail.append("全量");
        }
        strStart.append("avaya语音开始");
        strEnd.append("avaya语音结束");
        strFail.append("avaya语音发生异常");
        if (Objects.nonNull(start)) {
            strStart.append("，开始批次id为").append(start);
            strEnd.append("，开始批次id为").append(start);
            strFail.append("，开始批次id为").append(start);
        }
        LocalDateTime now = LocalDate.now().atStartOfDay();
        strStart.append("avaya语音开始，截止时间为").append(now);
        strEnd.append("avaya语音结束，截止时间为").append(now);
        strFail.append("avaya语音发生异常，截止时间为").append(now);

        SendRobotReq.Content content = new SendRobotReq.Content();
        content.setContent(strStart.toString());
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }

        try {
            String startId = "";
            while (true) {
                StringBuilder sql = new StringBuilder("select VOICE_ID as voiceId, CUS_ID as cusId, STAFFID as staffId," +
                        " caller, callee, START_TIME as startTime, voice_direct_id as voiceDirectId from cx_bas_voice_seq where ");
                if (StrUtil.isNotBlank(startId)) {
                    sql.append("VOICE_ID > '").append(startId).append("' and ");
                }
                if (Objects.isNull(startTime)) {
                    sql.append("RING_TIME >= '2020-12-01'");
                } else {
                    sql.append("RING_TIME >= '").append(DateUtil.formatLocalDateTime(startTime)).append("'");
                }
                sql.append(" and RING_TIME < '").append(DateUtil.formatLocalDateTime(now))
                        .append("' and long_time > 1 and STAFFID is not null and cus_id is not null")
                        .append(" order by VOICE_ID asc limit ");
                if (Objects.nonNull(start)) {
                    sql.append("200");
                } else {
                    sql.append("100");
                }
                List<VoiceSeq> avayaBatchList = avayaJdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(VoiceSeq.class));
                if (CollUtil.isEmpty(avayaBatchList)) {
                    break;
                }
                List<VoiceSeq> avayaList;
                if (Objects.isNull(start)) {
                    avayaList = avayaBatchList;
                } else {
                    if (start.equals(1)) {
                        avayaList = avayaBatchList.subList(0, Math.min(100, avayaBatchList.size()));
                    } else {
                        if (avayaBatchList.size() <= 100) {
                            break;
                        }
                        avayaList = avayaBatchList.subList(100, avayaBatchList.size());
                    }
                }

                List<VoiceSeq> handleList = avayaList.stream()
                        .filter(a -> StrUtil.isNotBlank(a.getStaffId()) && NumberUtil.isNumber(a.getCusId()) && !a.getCusId().contains("E")).collect(Collectors.toList());
                List<VoiceSeq> zwList = avayaList.stream().filter(a ->
                        StrUtil.isNotBlank(a.getStaffId()) && StrUtil.isNotBlank(a.getCusId()) && a.getCusId().startsWith("ZW")).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(zwList)) {
                    Set<Long> zwIdSet = zwList.stream().map(z -> Long.parseLong(z.getCusId().replaceAll("ZW", ""))).collect(Collectors.toSet());
                    LambdaQueryWrapper<YrZwUserRelation> wrapper = new LambdaQueryWrapper<>();
                    wrapper.in(YrZwUserRelation::getZwUserId, zwIdSet);
                    List<YrZwUserRelation> relationList = yrZwUserRelationService.list(wrapper);
                    if (CollUtil.isNotEmpty(relationList)) {
                        Map<Long, Long> relationMap = relationList.stream().collect(Collectors.toMap(YrZwUserRelation::getZwUserId, YrZwUserRelation::getYrUserId));
                        for (VoiceSeq seq : zwList) {
                            Long zwId = Long.parseLong(seq.getCusId().replaceAll("ZW", ""));
                            if (relationMap.containsKey(zwId)) {
                                seq.setCusId(String.valueOf(relationMap.get(zwId)));
                                handleList.add(seq);
                            }
                        }
                    }
                }
                if (CollUtil.isEmpty(handleList)) {
                    continue;
                }

                Map<String, Set<String>> avayaMap = handleList.stream().collect(
                        Collectors.groupingBy(VoiceSeq::getStaffId, Collectors.mapping(VoiceSeq::getCusId, Collectors.toSet())));
                for (Map.Entry<String, Set<String>> entry : avayaMap.entrySet()) {
                    Set<String> userIdSet = entry.getValue();
                    LambdaQueryWrapper<AiPlannerTopUser> wrapper = new LambdaQueryWrapper<>();
                    wrapper.select(AiPlannerTopUser::getUserId)
                            .eq(AiPlannerTopUser::getPlannerNo, entry.getKey()).in(AiPlannerTopUser::getUserId, userIdSet)
                            .eq(AiPlannerTopUser::getTenantId, yrTenantId);
                    List<AiPlannerTopUser> list = aiPlannerTopUserService.list(wrapper);
                    if (CollUtil.isEmpty(list)) {
                        avayaMap.put(entry.getKey(), Collections.emptySet());
                    } else {
                        avayaMap.put(entry.getKey(), list.stream().map(AiPlannerTopUser::getUserId).collect(Collectors.toSet()));
                    }
                }
                avayaMap = avayaMap.entrySet().stream().filter(a -> CollUtil.isNotEmpty(a.getValue()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                Map<String, Set<String>> finalAvayaMap = avayaMap;
                List<VoiceSeq> list = handleList.stream()
                        .filter(a -> finalAvayaMap.containsKey(a.getStaffId())
                                && finalAvayaMap.get(a.getStaffId()).contains(a.getCusId()))
                        .collect(Collectors.toList());
                for (VoiceSeq seq : list) {
                    dealSingleVoiceSeq(seq.getStaffId(), seq.getCusId(), seq, seq.getStartTime(), seq.getStartTime());
                }

                if (avayaList.size() < 100) {
                    break;
                }
                startId = avayaBatchList.get(avayaBatchList.size() - 1).getVoiceId();
            }

            content.setContent(strEnd.toString());
            try {
                HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
            } catch (Exception e) {
                log.error("发送企微群机器人通知失败", e);
            }
        } catch (Exception e) {
            log.error("同步全量/增量语音发生异常，异常原因为", e);
            strFail.append("\n异常原因为").append(e.getMessage());
            content.setContent(strFail.toString());
            try {
                HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
            } catch (Exception exception) {
                log.error("发送企微群机器人通知失败", exception);
            }
            throw e;
        }
    }

    @Async("avayaSplitThreadPoolExecutor")
    public void splitTextNew(Long start, Long end, boolean isNight) {
        log.info("切割Avaya语料开始, start={}, end={}", start, end);

        Long handleStart = start;
        while (start.compareTo(end) < 0) {
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(AiPlannerChatContact::getId, start).le(AiPlannerChatContact::getId, end)
                    .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                    .and(w -> w.isNull(AiPlannerChatContact::getProcessedContent).or().eq(AiPlannerChatContact::getProcessedContent, ""))
                    .isNotNull(AiPlannerChatContact::getNasPath).ne(AiPlannerChatContact::getNasPath, "")
                    .orderByAsc(AiPlannerChatContact::getId).last("limit 100");
            List<AiPlannerChatContact> contactList = iAiPlannerChatContactService.list(wrapper);
            if (CollectionUtils.isEmpty(contactList)) {
                break;
            }

            for (AiPlannerChatContact contact : contactList) {
                if (StrUtil.isNotBlank(contact.getProcessedContent()) || StrUtil.isBlank(contact.getNasPath())) {
                    continue;
                }

                LocalTime now = LocalTime.now();
                if (isNight && now.isAfter(SEVEN) && now.isBefore(TWENTY)) {
                    log.info("切割Avaya语料结束, 执行时间已到, start={}, end={}", handleStart, end);
                    return;
                }
                if (!isNight && (now.isBefore(SEVEN) || now.isAfter(TWENTY))) {
                    log.info("切割Avaya语料结束, 执行时间已到, start={}, end={}", handleStart, end);
                    return;
                }

                try {
                    String content = splitWithVoiceRecognition(contact);
                    LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.set(AiPlannerChatContact::getProcessedContent, content)
                            .eq(AiPlannerChatContact::getId, contact.getId());
                    iAiPlannerChatContactService.update(updateWrapper);
                    if ("[avaya语音]".equals(content)) {
                        LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                        wrapperText.set(AiPlannerChatContact::getProcessingContent, content)
                                .eq(AiPlannerChatContact::getId, contact.getId())
                                .and(w -> w.isNull(AiPlannerChatContact::getProcessingContent)
                                        .or().eq(AiPlannerChatContact::getProcessingContent, ""));
                        iAiPlannerChatContactService.update(wrapperText);
                    }
                } catch (Exception e) {
                    log.error("切割语料id={}失败，失败原因为", contact.getId(), e);
                }
            }
            if (contactList.size() < 100) {
                break;
            }
            start = contactList.get(contactList.size() - 1).getId();
        }
        log.info("切割Avaya语料结束, start={}, end={}", handleStart, end);
    }

    /**
     * 音转文+声纹检测->切割avaya语料
     * 1.降噪
     * 2.按照30分钟切割音频文件
     * 3.音转文+内容切割
     * 4.获取音频切割信息
     * 5.切割文件+拼接文件
     * 6.声纹检测
     * 7.说话人处理
     * @param contact avaya语音记录
     * @return 切割结果
     */
    private String splitWithVoiceRecognition(AiPlannerChatContact contact) {
        File file = new File(contact.getNasPath());
        try {
            double duration = AudioSplitUtil.getWavDuration(file);
            if (duration <= 1) {
                return "[avaya语音]";
            }
        } catch (Exception e) {
            log.error("获取音频时长发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "校验音频长短失败");
        }

        File fileDeNoise = aiPlannerVoiceService.deNoise(file, null);
        List<File> fileList = new ArrayList<>();
        try {
            fileList.addAll(FileUtil.splitAudioFile(fileDeNoise, 30 * 60, 5 * 60));
            StringBuilder stringBuilder = new StringBuilder();
            StringBuilder text = new StringBuilder();
            for (File perFile : fileList) {
                AudioToTextVo audioToTextVo = aiVoiceToTextService.getVoiceSegments(perFile, "ce-asr-1", null, null, null);
                if (Objects.isNull(audioToTextVo)) {
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "调用音转文接口处理发生异常");
                }

                text.append(audioToTextVo.getText());
                List<SegmentVo> segmentVoList = getVoiceSplitInfo(audioToTextVo.getSegments());
                if (CollUtil.isNotEmpty(segmentVoList)) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    String checkFilePath = path + LocalDate.now().format(formatter) + "_" + RandomUtil.randomString(10) + ".wav";
                    AudioSplitUtil.splitAndConcatenateAudio(contact.getNasPath(), checkFilePath, segmentVoList);

                    File checkFile = new File(checkFilePath);
                    try {
                        LambdaQueryWrapper<AiPlannerVoice> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(AiPlannerVoice::getStaffId, contact.getPlannerNo())
                                .eq(AiPlannerVoice::getStatus, VoiceStatusEnum.APPROVED.getCode())
                                .isNotNull(AiPlannerVoice::getVoiceId);
                        AiPlannerVoice voice = aiPlannerVoiceService.getOne(wrapper);
                        if (Objects.isNull(voice)) {
                            throw new AiServerException(ResultCode.FAILURE.getCode(), "理财师声纹未注册，暂不支持声纹检测");
                        }
                        VoiceSearchResponse response = aiPlannerVoiceService.search(checkFile, voice.getAccount(), null);

                        Integer spk = segmentVoList.get(0).getSpk();
                        if (BigDecimal.valueOf(0.8).compareTo(response.getDist()) <= 0) {
                            for (AudioToTextVo.Segment segment : audioToTextVo.getSegments()) {
                                if (StrUtil.isNotBlank(segment.getText())) {
                                    if (spk.equals(segment.getSpk())) {
                                        stringBuilder.append("理财师:").append(segment.getText()).append("\n");
                                    } else {
                                        stringBuilder.append("客户:").append(segment.getText()).append("\n");
                                    }
                                }
                            }
                        } else {
                            for (AudioToTextVo.Segment segment : audioToTextVo.getSegments()) {
                                if (StrUtil.isNotBlank(segment.getText())) {
                                    if (spk.equals(segment.getSpk())) {
                                        stringBuilder.append("客户:").append(segment.getText()).append("\n");
                                    } else {
                                        stringBuilder.append("理财师:").append(segment.getText()).append("\n");
                                    }
                                }
                            }
                        }
                    } finally {
                        checkFile.delete();
                    }
                }
            }

            if (StrUtil.isNotBlank(text.toString()) && StrUtil.isBlank(contact.getProcessingContent())) {
                LambdaUpdateWrapper<AiPlannerChatContact> wrapper = new LambdaUpdateWrapper<>();
                wrapper.set(AiPlannerChatContact::getProcessingContent, text.toString())
                        .eq(AiPlannerChatContact::getId, contact.getId());
                iAiPlannerChatContactService.update(wrapper);
            }
            if (StringUtils.isNotBlank(stringBuilder.toString())) {
                return stringBuilder.toString();
            } else {
                return "[avaya语音]";
            }
        } catch (Exception e) {
            log.error("根据声纹识别切割avaya语料发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE);
        } finally {
            fileDeNoise.delete();
            if (!CollectionUtils.isEmpty(fileList)) {
                for (File perFile : fileList) {
                    perFile.delete();
                }
            }
        }
    }

    /**
     * 获取音频切割信息
     * 1.拼接不同spk的连续片段
     * 2.取片段总时长最大的spk对应的片段列表
     * 3.取时长最大的几个片段，以便拼接为20s以上的音频
     * @param segmentList 音频片段列表
     * @return 用于声纹检测的片段信息列表
     */
    private List<SegmentVo> getVoiceSplitInfo(List<AudioToTextVo.Segment> segmentList) {
        if (CollUtil.isEmpty(segmentList)) {
            return new ArrayList<>();
        }
        segmentList = segmentList.stream().filter(s -> StrUtil.isNotBlank(s.getText())).collect(Collectors.toList());
        if (CollUtil.isEmpty(segmentList)) {
            return new ArrayList<>();
        }

        Map<Integer, List<SegmentVo>> spkMap = new HashMap<>();
        Integer curSpk = 0;
        for (AudioToTextVo.Segment s : segmentList) {
            if (Objects.isNull(s.getSpk())) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "音转文切割接口返回时部分片段无对应说话人");
            }
            if (spkMap.containsKey(s.getSpk()) && curSpk.equals(s.getSpk())) {
                spkMap.get(s.getSpk()).get(spkMap.get(s.getSpk()).size() - 1)
                        .setEnd(s.getTimestamp().get(s.getTimestamp().size() - 1).get(1));
            } else {
                List<SegmentVo> segmentVoList = spkMap.getOrDefault(s.getSpk(), new ArrayList<>());
                segmentVoList.add(SegmentVo.builder().spk(s.getSpk()).start(s.getTimestamp().get(0).get(0))
                        .end(s.getTimestamp().get(s.getTimestamp().size() - 1).get(1)).build());
                spkMap.put(s.getSpk(), segmentVoList);
                curSpk = s.getSpk();
            }
        }

        Map.Entry<Integer, List<SegmentVo>> maxEntry = spkMap.entrySet().stream().max(Comparator.comparingLong(entry ->
                entry.getValue().stream().map(SegmentVo::getDuration).mapToLong(Long::longValue).sum())).orElse(null);
        if (Objects.isNull(maxEntry)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "未获取到片段量最大的spk对应的片段列表");
        }

        List<SegmentVo> list = maxEntry.getValue().stream().sorted(Comparator.comparing(SegmentVo::getDuration).reversed()).collect(Collectors.toList());
        Long sumDuration = 0L;
        List<SegmentVo> res = new ArrayList<>();
        for (SegmentVo s : list) {
            long temp = sumDuration + s.getDuration();
            if (temp >= 20 * 1000) {
                if (temp > 30 * 1000) {
                    s.setEnd(s.getStart() + 30 * 1000 - sumDuration);
                }
                res.add(s);
                break;
            }
            res.add(s);
            sumDuration += s.getDuration();
        }
        return res;
    }

    @Async("commonThreadPoolExecutor")
    public void batchHandle(List<Long> idList) {
        String ids = idList.stream().map(String::valueOf).collect(Collectors.joining(","));
        SendRobotReq.Content msg = new SendRobotReq.Content();

        Map<Long, String> failMap = new HashMap<>();
        int success = 0, fail = 0;
        try {
            AiPromptRecordVo deNoiseRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.DENOISE.getCode());
            AiPromptRecordVo translateRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_TO_TEXT.getCode());
            AiPromptRecordVo searchRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_SEARCH.getCode());

            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(AiPlannerChatContact::getId, idList);
            List<AiPlannerChatContact> list = iAiPlannerChatContactService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "输入消息id无效，未获取到消息内容");
            }

            for (AiPlannerChatContact contact : list) {
                try {
                    if (StrUtil.isBlank(contact.getNasPath())) {
                        throw new AiServerException(ResultCode.PARAM_INVALID.getCode(), "avaya文件不存在");
                    }

                    String content = getCrmWithVoiceWithDesignatedPlanner(contact, null, deNoiseRecord, translateRecord, searchRecord);
                    if ("[avaya语音]".equals(content)) {
                        LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                        wrapperText.set(AiPlannerChatContact::getProcessingContent, content)
                                .set(AiPlannerChatContact::getProcessedContent, content)
                                .eq(AiPlannerChatContact::getId, contact.getId());
                        iAiPlannerChatContactService.update(wrapperText);
                    }
                    success++;
                } catch (Exception e) {
                    log.error("消息id={}处理失败，失败原因为", contact.getId(), e);
                    fail++;
                    failMap.put(contact.getId(), e.getMessage());
                }
            }

            StringBuilder str = new StringBuilder();
            str.append(String.format("批量处理消息结束, 成功处理数量:%s, 失败数量:%s, 消息id列表为[%s]\n", success, fail, ids));
            if (MapUtil.isNotEmpty(failMap)) {
                str.append("详细失败信息为").append(JSON.toJSONString(failMap));
            }
            msg.setContent(str.toString());
        } catch (Exception e) {
            msg.setContent("消息处理失败，失败原因为" + e.getMessage());
        }

        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(msg);
        sendRobotReq.setMsgtype("text");
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }
    }

    public void compareSplit(Integer size, boolean isLong) {
        LambdaQueryWrapper<AiPlannerVoice> voiceWrapper = new LambdaQueryWrapper<>();
        voiceWrapper.select(AiPlannerVoice::getStaffId)
                .eq(AiPlannerVoice::getStatus, VoiceStatusEnum.APPROVED.getCode())
                .isNotNull(AiPlannerVoice::getVoiceId);
        List<AiPlannerVoice> voiceList = aiPlannerVoiceService.list(voiceWrapper);
        if (CollUtil.isEmpty(voiceList)) {
            return;
        }
        List<String> voiceStaffIdList = voiceList.stream().map(AiPlannerVoice::getStaffId).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(voiceStaffIdList)) {
            return;
        }

        LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AiPlannerChatContact::getPlannerNo, voiceStaffIdList)
                .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                .isNotNull(AiPlannerChatContact::getNasPath).apply("LENGTH(TRIM(nas_path)) != 0")
                .and(w -> w.isNull(AiPlannerChatContact::getVoiceCrmSpk).or().apply("LENGTH(TRIM(voice_crm_spk)) = 0"))
                .and(w -> w.isNull(AiPlannerChatContact::getModelCrmSpk).or().apply("LENGTH(TRIM(model_crm_spk)) = 0"))
                .orderByDesc(AiPlannerChatContact::getMsgTime).last("limit " + size);
        List<AiPlannerChatContact> contactList = iAiPlannerChatContactService.list(wrapper);
        if (CollectionUtils.isEmpty(contactList)) {
            return;
        }

        for (AiPlannerChatContact contact : contactList) {
            if (StrUtil.isBlank(contact.getNasPath()) || StrUtil.isNotBlank(contact.getModelCrmSpk()) || StrUtil.isNotBlank(contact.getVoiceCrmSpk())) {
                continue;
            }
            try {
                String text = getCrmWithVoice(contact, isLong);
                if ("[avaya语音]".equals(text)) {
                    LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                    wrapperText.set(AiPlannerChatContact::getProcessingContent, text)
                            .set(AiPlannerChatContact::getProcessedContent, text)
                            .eq(AiPlannerChatContact::getId, contact.getId());
                    iAiPlannerChatContactService.update(wrapperText);
                }
            } catch (Exception e) {
                log.error("对比Avaya语料[id = {}]切割结果发生异常，异常原因为", contact.getId(), e);
            }
        }
    }

    public void compareSplitWithDesignatedPlanner(String plannerNo, Integer size, Boolean isLong) {
        AiPromptRecordVo deNoiseRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.DENOISE.getCode());
        AiPromptRecordVo translateRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_TO_TEXT.getCode());
        AiPromptRecordVo searchRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_SEARCH.getCode());

        LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerChatContact::getPlannerNo, plannerNo)
                .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                .isNotNull(AiPlannerChatContact::getNasPath).apply("LENGTH(TRIM(nas_path)) != 0")
                .and(w -> w.isNull(AiPlannerChatContact::getUnknownSpkContent).or().apply("LENGTH(TRIM(unknown_spk_content)) = 0"))
                .orderByDesc(AiPlannerChatContact::getMsgTime).last("limit " + size);
        List<AiPlannerChatContact> contactList = iAiPlannerChatContactService.list(wrapper);
        if (CollectionUtils.isEmpty(contactList)) {
            return;
        }

        for (AiPlannerChatContact contact : contactList) {
            if (StrUtil.isBlank(contact.getNasPath()) || StrUtil.isNotBlank(contact.getUnknownSpkContent())) {
                continue;
            }
            try {
                String text = getCrmWithVoiceWithDesignatedPlanner(contact, isLong, deNoiseRecord, translateRecord, searchRecord);
                if ("[avaya语音]".equals(text)) {
                    LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                    wrapperText.set(AiPlannerChatContact::getProcessingContent, text)
                            .set(AiPlannerChatContact::getProcessedContent, text)
                            .eq(AiPlannerChatContact::getId, contact.getId());
                    iAiPlannerChatContactService.update(wrapperText);
                }
            } catch (Exception e) {
                log.error("对比Avaya语料[id = {}]切割结果发生异常，异常原因为", contact.getId(), e);
            }
        }
    }

    private String getCrmWithVoice(AiPlannerChatContact contact, boolean isLong) {
        File file = new File(contact.getNasPath());
        try {
            double duration = AudioSplitUtil.getWavDuration(file);
            if (duration <= 1) {
                return "[avaya语音]";
            }
            if (isLong && (duration < 30 * 60 || duration > 60 * 60) || !isLong && duration > 10 * 60) {
                return "";
            }
        } catch (Exception e) {
            log.error("获取音频时长发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), "校验音频长短失败");
        }

        File fileDeNoise = aiPlannerVoiceService.deNoise(file, null);
        try {
            AudioToTextVo audioToTextVo = aiVoiceToTextService.getVoiceSegments(fileDeNoise, "ce-asr-1", null, null, null);
            if (Objects.isNull(audioToTextVo)) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "调用音转文接口处理发生异常");
            }
            if (StrUtil.isBlank(audioToTextVo.getText())) {
                return "[avaya语音]";
            }
            if (StrUtil.isBlank(contact.getProcessingContent())) {
                LambdaUpdateWrapper<AiPlannerChatContact> wrapper = new LambdaUpdateWrapper<>();
                wrapper.set(AiPlannerChatContact::getProcessingContent, audioToTextVo.getText())
                        .eq(AiPlannerChatContact::getId, contact.getId());
                iAiPlannerChatContactService.update(wrapper);
            }

            List<SegmentVo> segmentVoList = getVoiceSplitInfo(audioToTextVo.getSegments());
            if (CollUtil.isEmpty(segmentVoList)) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "未获取到片段量最大的spk对应的片段列表");
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String checkFilePath = path + LocalDate.now().format(formatter) + "_" + RandomUtil.randomString(10) + ".wav";
            AudioSplitUtil.splitAndConcatenateAudio(contact.getNasPath(), checkFilePath, segmentVoList);

            File checkFile = new File(checkFilePath);
            try {
                LambdaQueryWrapper<AiPlannerVoice> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AiPlannerVoice::getStaffId, contact.getPlannerNo())
                        .eq(AiPlannerVoice::getStatus, VoiceStatusEnum.APPROVED.getCode())
                        .isNotNull(AiPlannerVoice::getVoiceId);
                AiPlannerVoice voice = aiPlannerVoiceService.getOne(wrapper);
                if (Objects.isNull(voice)) {
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "理财师声纹未注册，暂不支持声纹检测");
                }
                log.info("调用ChatAPI接口进行声纹检测 account={} checkSpk={} file={}", voice.getAccount(), segmentVoList.get(0).getSpk(), checkFilePath);
                VoiceSearchResponse response = aiPlannerVoiceService.search(checkFile, voice.getAccount(), null);
                Integer voiceCrmSpk = segmentVoList.get(0).getSpk();
                if (BigDecimal.valueOf(0.7).compareTo(response.getDist()) > 0) {
                    Integer finalVoiceCrmSpk = voiceCrmSpk;
                    Map<Integer, Long> spkCountMap = audioToTextVo.getSegments().stream()
                            .filter(s -> StrUtil.isNotBlank(s.getText()) && !finalVoiceCrmSpk.equals(s.getSpk()))
                            .collect(Collectors.groupingBy(AudioToTextVo.Segment::getSpk, Collectors.counting()));
                    Map.Entry<Integer, Long> maxEntry = spkCountMap.entrySet().stream().max(Map.Entry.comparingByValue()).orElse(null);
                    if (Objects.isNull(maxEntry)) {
                        voiceCrmSpk = -1;
                    } else {
                        voiceCrmSpk = maxEntry.getKey();
                    }
                }
                LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(AiPlannerChatContact::getVoiceCrmSpk, String.valueOf(voiceCrmSpk))
                        .eq(AiPlannerChatContact::getId, contact.getId());
                iAiPlannerChatContactService.update(updateWrapper);

                String temp = audioToTextVo.getSegments().stream().filter(s -> StrUtil.isNotBlank(s.getText()))
                        .map(item -> "说话人" + item.getSpk() + ":" + item.getText())
                        .collect(Collectors.joining("\n"));
                if (StrUtil.isBlank(contact.getUnknownSpkContent())) {
                    LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                    wrapperText.set(AiPlannerChatContact::getUnknownSpkContent, temp)
                            .eq(AiPlannerChatContact::getId, contact.getId());
                    iAiPlannerChatContactService.update(wrapperText);
                }

                String prompt = String.format(QUESTION_RECOGNITION, temp.length() > 21 * 1000 ? temp.substring(0, 21 * 1000) : temp);
                String responseModel = chatService.chat(prompt, MODEL_MAX, 0.9F);
                if (StringUtils.isBlank(responseModel)) {
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "生成分析结果失败");
                }
                ChatApiRes chatApiRes = JSON.parseObject(responseModel, ChatApiRes.class);
                String answer = chatApiRes.getChoices().get(0).getMessage().getContent().replaceAll("[\\pP\\p{Punct}]", "");
                if (CommonUtil.countSubstring(answer, "说话人") != 1
                        || !INTEGER_PATTERN.matcher(answer.replaceAll("说话人", "")).matches()
                        || !temp.contains(answer + ":")) {
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "生成分析结果失败");
                }
                LambdaUpdateWrapper<AiPlannerChatContact> updateModelWrapper = new LambdaUpdateWrapper<>();
                updateModelWrapper.set(AiPlannerChatContact::getModelCrmSpk, answer.replaceAll("说话人", ""))
                        .eq(AiPlannerChatContact::getId, contact.getId());
                iAiPlannerChatContactService.update(updateModelWrapper);
            } finally {
                checkFile.delete();
            }
            return "";
        } catch (Exception e) {
            log.error("根据声纹识别切割avaya语料发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE);
        } finally {
            fileDeNoise.delete();
        }
    }

    public String getCrmWithVoiceWithDesignatedPlanner(AiPlannerChatContact contact, Boolean isLong,
            AiPromptRecordVo deNoiseRecord, AiPromptRecordVo translateRecord, AiPromptRecordVo searchRecord) {
        log.info("消息id={}音转文&说话人识别处理开始", contact.getId());
        File file = new File(contact.getNasPath());
        if (Objects.isNull(contact.getLongTime())) {
            try {
                contact.setLongTime(Math.round(AudioSplitUtil.getWavDuration(file)));
            } catch (Exception e) {
                log.error("获取音频时长发生异常，异常原因为", e);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "获取音频时长发生异常");
            }
        }
        if (contact.getLongTime() <= 1) {
            return QwMsgTypeEnum.HOUYU.getType().equals(contact.getOriginType()) ? "[厚予语音]" : "[avaya语音]";
        }
        if (Boolean.TRUE.equals(isLong) && (contact.getLongTime() < 30 * 60 || contact.getLongTime() > 60 * 60)
                || Boolean.FALSE.equals(isLong) && contact.getLongTime() > 10 * 60) {
            return "";
        }

        if (deNoiseRecord.getOpen()) {
            file = aiPlannerVoiceService.deNoise(file, deNoiseRecord.getSampleRate());
        }

        try {
            AudioToTextVo audioToTextVo = aiVoiceToTextService.getVoiceSegments(file, translateRecord.getModel(),
                    translateRecord.getHotWords(), translateRecord.getPrompt(), translateRecord.getTemperature());
            if (Objects.isNull(audioToTextVo)) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "调用音转文接口处理发生异常");
            }
            if (StrUtil.isBlank(audioToTextVo.getText())) {
                return QwMsgTypeEnum.HOUYU.getType().equals(contact.getOriginType()) ? "[厚予语音]" : "[avaya语音]";
            }

            List<AudioSegmentVo> segmentVoList = audioToTextVo.getSegments().stream()
                    .map(s -> AudioSegmentVo.builder().text(s.getText()).spk(s.getSpk()).start(s.getTimestamp().get(0).get(0))
                            .end(s.getTimestamp().get(s.getTimestamp().size() - 1).get(1)).build())
                    .sorted(Comparator.comparing(AudioSegmentVo::getStart))
                    .collect(Collectors.toList());
            LambdaUpdateWrapper<AiPlannerChatContact> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(AiPlannerChatContact::getId, contact.getId());
            boolean needUpdate = false;
            if (StrUtil.isBlank(contact.getProcessingContent()) || StrUtil.isBlank(contact.getUnknownSpkContent())) {
                needUpdate = true;
                if (StrUtil.isBlank(contact.getProcessingContent())) {
                    wrapper.set(AiPlannerChatContact::getProcessingContent, audioToTextVo.getText());
                }
                if (StrUtil.isBlank(contact.getUnknownSpkContent())) {
                    wrapper.set(AiPlannerChatContact::getUnknownSpkContent, JSON.toJSONString(segmentVoList));
                }
            }

            if (searchRecord.getOpen()) {
                Integer voiceCrmSpk = getVoiceResult(audioToTextVo.getSegments(), contact, searchRecord.getModel(),
                        searchRecord.getFirstRoundDist(), searchRecord.getSecondRoundDist());
                StringBuilder str = new StringBuilder();
                for (AudioSegmentVo seg : segmentVoList) {
                    str.append(seg.getSpk().equals(voiceCrmSpk) ? "理财师:" : "客户:").append(seg.getText()).append("\n");
                }
                needUpdate = true;
                wrapper.set(AiPlannerChatContact::getVoiceCrmSpk, String.valueOf(voiceCrmSpk))
                        .set(AiPlannerChatContact::getProcessedContent, str.toString());
            }
            if (needUpdate) {
                iAiPlannerChatContactService.update(wrapper);
            }

            /*
            String temp = audioToTextVo.getSegments().stream().filter(s -> StrUtil.isNotBlank(s.getText()))
                    .map(item -> "说话人" + item.getSpk() + ":" + item.getText()).collect(Collectors.joining("\n"));
            String prompt = String.format(QUESTION_RECOGNITION, temp.length() > 21 * 1000 ? temp.substring(0, 21 * 1000) : temp);
            String responseModel = chatService.chat(prompt, MODEL_MAX, 0.9F);
            if (StringUtils.isBlank(responseModel)) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "生成分析结果失败");
            }
            ChatApiRes chatApiRes = JSON.parseObject(responseModel, ChatApiRes.class);
            String answer = chatApiRes.getChoices().get(0).getMessage().getContent().replaceAll("[\\pP\\p{Punct}]", "");
            if (CommonUtil.countSubstring(answer, "说话人") != 1
                    || !INTEGER_PATTERN.matcher(answer.replaceAll("说话人", "")).matches()
                    || !temp.contains(answer + ":")) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "生成分析结果失败");
            }
            LambdaUpdateWrapper<AiPlannerChatContact> updateModelWrapper = new LambdaUpdateWrapper<>();
            updateModelWrapper.set(AiPlannerChatContact::getModelCrmSpk, answer.replaceAll("说话人", ""))
                    .eq(AiPlannerChatContact::getId, contact.getId());
            iAiPlannerChatContactService.update(updateModelWrapper);
             */
            return "";
        } catch (Exception e) {
            log.error("根据声纹识别切割avaya语料发生异常，异常原因为", e);
            throw new AiServerException(ResultCode.FAILURE.getCode(), e.getMessage());
        } finally {
            if (deNoiseRecord.getOpen()) {
                file.delete();
            }
            log.info("消息id={}音转文&说话人识别处理结束", contact.getId());
        }
    }

    private Integer getVoiceResult(List<AudioToTextVo.Segment> segmentList, AiPlannerChatContact contact, String model,
                                   BigDecimal firstRoundDist, BigDecimal secondRoundDist) throws IOException {
        if (CollUtil.isEmpty(segmentList)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "片段为空");
        }
        segmentList = segmentList.stream().filter(s -> StrUtil.isNotBlank(s.getText())).collect(Collectors.toList());
        if (CollUtil.isEmpty(segmentList)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "片段为空");
        }

        Map<Integer, List<SegmentVo>> spkMap = new HashMap<>();
        Integer curSpk = 0;
        for (AudioToTextVo.Segment s : segmentList) {
            if (Objects.isNull(s.getSpk())) {
                throw new AiServerException(ResultCode.FAILURE.getCode(), "音转文切割接口返回时部分片段无对应说话人");
            }
            if (spkMap.containsKey(s.getSpk()) && curSpk.equals(s.getSpk())) {
                spkMap.get(s.getSpk()).get(spkMap.get(s.getSpk()).size() - 1)
                        .setEnd(s.getTimestamp().get(s.getTimestamp().size() - 1).get(1));
            } else {
                List<SegmentVo> segmentVoList = spkMap.getOrDefault(s.getSpk(), new ArrayList<>());
                segmentVoList.add(SegmentVo.builder().spk(s.getSpk()).start(s.getTimestamp().get(0).get(0))
                        .end(s.getTimestamp().get(s.getTimestamp().size() - 1).get(1)).build());
                spkMap.put(s.getSpk(), segmentVoList);
                curSpk = s.getSpk();
            }
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String date = LocalDate.now().format(formatter);
        String fileName = date + "_" + RandomUtil.randomString(10) + contact.getNasPath().substring(contact.getNasPath().lastIndexOf(".")),
                fileSecondName = date + "_" + RandomUtil.randomString(10) + contact.getNasPath().substring(contact.getNasPath().lastIndexOf("."));
        try {
            // 第一轮：说话量最大+匹配指定理财师声纹
            List<SegmentVo> segmentVoList = getSegmentInfoList(spkMap, 1L);
            AudioSplitUtil.splitAndConcatenateAudio(contact.getNasPath(), path + "check/" + fileName, segmentVoList);
            BigDecimal dist = getDist(contact.getPlannerNo(), segmentVoList.get(0).getSpk(), fileName, contact.getId(), 1, model);
            if (firstRoundDist.compareTo(dist) <= 0) {
                return segmentVoList.get(0).getSpk();
            }

            // 第一轮：说话量第二大+匹配指定理财师声纹
            List<SegmentVo> segmentVoSecondList = getSegmentInfoList(spkMap, 2L);
            AudioSplitUtil.splitAndConcatenateAudio(contact.getNasPath(), path + "check/" + fileSecondName, segmentVoSecondList);
            BigDecimal distSecond = getDist(contact.getPlannerNo(), segmentVoSecondList.get(0).getSpk(), fileSecondName, contact.getId(), 1, model);
            if (firstRoundDist.compareTo(distSecond) <= 0) {
                return segmentVoSecondList.get(0).getSpk();
            }

            // 第二轮：匹配渠道声纹
            BigDecimal distThird = getDist(null, segmentVoList.get(0).getSpk(), fileName, contact.getId(), 2, model);
            BigDecimal distForth = getDist(null, segmentVoSecondList.get(0).getSpk(), fileSecondName, contact.getId(), 2, model);
            if (distThird.compareTo(distForth) > 0 && secondRoundDist.compareTo(distThird) <= 0) {
                return segmentVoList.get(0).getSpk();
            }
            if (distThird.compareTo(distForth) < 0 && secondRoundDist.compareTo(distForth) <= 0) {
                return segmentVoSecondList.get(0).getSpk();
            }
            throw new AiServerException(ResultCode.FAILURE.getCode(), "使用声纹检测未识别出说话人spk");
        } finally {
            File checkFile = new File(path + "check/" + fileName);
            if (checkFile.exists()) {
                checkFile.delete();
            }
            File checkSecondFile = new File(path + "check/" + fileSecondName);
            if (checkSecondFile.exists()) {
                checkSecondFile.delete();
            }
        }
    }

    private List<SegmentVo> getSegmentInfoList(Map<Integer, List<SegmentVo>> spkMap, Long count) {
        Map<Integer, Long> durationMap = spkMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                entry -> entry.getValue().stream().mapToLong(SegmentVo::getDuration).sum()));
        Map.Entry<Integer, Long> maxEntry = durationMap.entrySet().stream()
                .sorted(Map.Entry.<Integer, Long>comparingByValue().reversed()).skip(count - 1).findFirst().orElse(null);
        if (Objects.isNull(maxEntry)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "未获取到片段量第" + count + "大的spk对应的片段列表");
        }

        List<SegmentVo> list = spkMap.get(maxEntry.getKey()).stream().sorted(Comparator.comparing(SegmentVo::getDuration).reversed()).collect(Collectors.toList());
        Long sumDuration = 0L;
        List<SegmentVo> res = new ArrayList<>();
        for (SegmentVo s : list) {
            long temp = sumDuration + s.getDuration();
            if (temp >= 20 * 1000) {
                if (temp > 30 * 1000) {
                    s.setEnd(s.getStart() + 30 * 1000 - sumDuration);
                }
                res.add(s);
                break;
            }
            res.add(s);
            sumDuration += s.getDuration();
        }
        if (CollUtil.isEmpty(res)) {
            throw new AiServerException(ResultCode.FAILURE.getCode(), "未获取到片段量第" + count + "大的spk对应的片段列表");
        }
        return res;
    }

    private BigDecimal getDist(String account, Integer spk, String fileName, Long chatId, Integer round, String model) {
        File checkFile = new File(path + "check/" + fileName);
        log.info("调用ChatAPI接口进行声纹检测 account={} checkSpk={} file={}", account, spk, fileName);
        VoiceSearchResponse response = aiPlannerVoiceService.search(checkFile, account, model);
        BigDecimal dist = response.getDist().setScale(8, BigDecimal.ROUND_HALF_UP);

        AiChatAvayaDist avayaDist = new AiChatAvayaDist();
        avayaDist.setChatId(chatId);
        avayaDist.setModel(model);
        avayaDist.setSpk(spk);
        avayaDist.setDist(dist);
        avayaDist.setRound(round);
        if (StrUtil.isNotBlank(response.getUuid())) {
            avayaDist.setUuid(response.getUuid());
        }
        avayaDist.setFileUrl(nasUrl + "check/" + fileName);
        aiChatAvayaDistService.save(avayaDist);

        return dist;
    }

    public void syncAvayaId(Integer count) {
        log.info("同步Avaya语音id开始...");
        String startId = "";
        while (true) {
            StringBuilder sql = new StringBuilder("select VOICE_ID as voiceId, caller, callee, START_TIME as startTime from cx_bas_voice_seq where ");
            if (StrUtil.isNotBlank(startId)) {
                sql.append("VOICE_ID > '").append(startId).append("' and ");
            }
            sql.append("RING_TIME >= '2020-12-01' and long_time > 1 and STAFFID is not null and cus_id is not null order by VOICE_ID asc limit ");
            if (Objects.isNull(count)) {
                sql.append("100");
            } else {
                sql.append(count);
            }
            List<VoiceSeq> avayaList = avayaJdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(VoiceSeq.class));
            if (CollUtil.isEmpty(avayaList)) {
                break;
            }

            for (VoiceSeq seq : avayaList) {
                log.info("voiceSeq {}", seq);
                int amount = 300;
                String start = DateUtil.formatDateTime(
                        DateUtil.offsetSecond(DateUtil.offsetHour(DateUtil.parseDateTime(seq.getStartTime()), -8), -amount));
                String end = DateUtil.formatDateTime(
                        DateUtil.offsetSecond(DateUtil.offsetHour(DateUtil.parseDateTime(seq.getStartTime()), -8), amount));
                String voiceCaseIdSql =
                        "select VOICE_CASE_ID from INTERACTIONS where CALLING_PARTY='" + seq.getCaller()
                                + "' and CALLED_PARTY='" + seq.getCallee() + "' and CALL_START_TIME BETWEEN '" + start + "' and '" + end + "'"
                                + " union select VOICE_CASE_ID from INTERACTIONS_HISTORY where CALLING_PARTY='" + seq.getCaller()
                                + "' and CALLED_PARTY='" + seq.getCallee() + "' and CALL_START_TIME BETWEEN '" + start + "' and '" + end + "'";
                List<Map<String, Object>> list = voiceJbcTemplate.queryForList(voiceCaseIdSql);
                if (CollUtil.isEmpty(list)) {
                    continue;
                }
                List<String> voiceCaseIds = list.stream().map(id -> MapUtil.getStr(id, "VOICE_CASE_ID")).distinct().collect(Collectors.toList());
                LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(AiPlannerChatContact::getAvayaId, seq.getVoiceId())
                        .in(AiPlannerChatContact::getBusId, voiceCaseIds)
                        .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                        .isNull(AiPlannerChatContact::getAvayaId);
                iAiPlannerChatContactService.update(updateWrapper);
            }

            if (avayaList.size() < 100) {
                break;
            }
            startId = avayaList.get(avayaList.size() - 1).getVoiceId();
            if (Objects.nonNull(count)) {
                break;
            }
        }
        log.info("同步Avaya语音id结束");
    }

    public void syncAvayaPhone(Integer count) {
        log.info("同步Avaya通话电话开始...");
        String startId = "";
        while (true) {
            StringBuilder sql = new StringBuilder("select VOICE_ID as voiceId, caller, callee, voice_direct_id as voiceDirectId from cx_bas_voice_seq where ");
            if (StrUtil.isNotBlank(startId)) {
                sql.append("VOICE_ID > '").append(startId).append("' and ");
            }
            sql.append("RING_TIME >= '2020-12-01' and long_time > 1 and STAFFID is not null and cus_id is not null order by VOICE_ID asc limit ");
            if (Objects.isNull(count)) {
                sql.append("100");
            } else {
                sql.append(count);
            }
            List<VoiceSeq> avayaList = avayaJdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(VoiceSeq.class));
            if (CollUtil.isEmpty(avayaList)) {
                break;
            }
            Map<String, String> map = avayaList.stream().collect(Collectors.toMap(VoiceSeq::getVoiceId,
                    v -> getCusPhoneByCallType(v.getVoiceDirectId(), v.getCallee(), v.getCaller())));

            List<String> voiceIdList = avayaList.stream().map(VoiceSeq::getVoiceId).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(AiPlannerChatContact::getId, AiPlannerChatContact::getAvayaId)
                    .in(AiPlannerChatContact::getAvayaId, voiceIdList).eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                    .and(w -> w.isNull(AiPlannerChatContact::getPhone).or().apply("length(trim(phone)) = 0"));
            List<AiPlannerChatContact> handleList = iAiPlannerChatContactService.list(wrapper);
            if (CollUtil.isNotEmpty(handleList)) {
                for (AiPlannerChatContact chatContact : handleList) {
                    if (StrUtil.isNotBlank(map.get(chatContact.getAvayaId()))) {
                        LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(AiPlannerChatContact::getPhone, map.get(chatContact.getAvayaId()))
                                .eq(AiPlannerChatContact::getId, chatContact.getId());
                        iAiPlannerChatContactService.update(updateWrapper);
                    }
                }
            }

            if (Objects.nonNull(count)) {
                break;
            }
            if (avayaList.size() < 100) {
                break;
            }
            startId = avayaList.get(avayaList.size() - 1).getVoiceId();
        }
        log.info("同步Avaya通话电话结束");
    }

    public void handleAvayaLongTime(Integer count) {
        log.info("处理Avaya语音时长开始...");
        Long startId = null;
        while (true) {
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                    .isNotNull(AiPlannerChatContact::getNasPath).isNull(AiPlannerChatContact::getLongTime);
            if (Objects.nonNull(startId)) {
                wrapper.gt(AiPlannerChatContact::getId, startId);
            }
            wrapper.orderByAsc(AiPlannerChatContact::getId);
            if (Objects.nonNull(count)) {
                wrapper.last("Limit " + count);
            } else {
                wrapper.last("Limit 100");
            }
            List<AiPlannerChatContact> list = iAiPlannerChatContactService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            for (AiPlannerChatContact chat : list) {
                try {
                    long duration = Math.round(AudioSplitUtil.getWavDuration(new File(chat.getNasPath())));
                    LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.set(AiPlannerChatContact::getLongTime, duration).eq(AiPlannerChatContact::getId, chat.getId());
                    iAiPlannerChatContactService.update(updateWrapper);
                } catch (Exception e) {
                    log.error("获取消息id={}的音频的时长发生异常，异常原因为", chat.getId(), e);
                }
            }

            if (Objects.nonNull(count)) {
                break;
            }
            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }

        log.info("处理Avaya语音时长结束");
    }

    public void splitWithAllCrmCustomer(LocalDateTime endTime) {
        log.info("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务开始");
        AiPromptRecordVo deNoiseRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.DENOISE.getCode());
        AiPromptRecordVo translateRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_TO_TEXT.getCode());
        AiPromptRecordVo searchRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_SEARCH.getCode());

        SendRobotReq.Content content = new SendRobotReq.Content();
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");

        while (true) {
            LocalTime now = LocalTime.now();
            if (now.isAfter(LocalTime.of(6, 0, 0)) && now.isBefore(LocalTime.of(20, 30, 0))) {
                log.info("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务结束, 主线程执行时间已到");
                break;
            }

            String cache = jedisCluster.get(KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH);
            if (StrUtil.isBlank(cache)) {
                List<Integer> idList = iPlannerUserBindService.getCrmGroups();
                if (CollUtil.isEmpty(idList)) {
                    log.info("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务：未获取到Crm客户分组列表");
                    break;
                }
                cache = JSON.toJSONString(idList);
                jedisCluster.setex(KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH, 7 * 24 * 60 * 60, cache);
            }
            List<Integer> idList = JSON.parseArray(cache, Integer.class);
            if (CollUtil.isEmpty(idList)) {
                log.info("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务未获取到Crm客户分组列表");
                break;
            }
            boolean isExist = jedisCluster.exists(KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH_ID);
            Long id = jedisCluster.incr(KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH_ID);
            log.info("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务，当前批次id为{}", id);
            if (!isExist) {
                jedisCluster.expire(KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH_ID, 7 * 24 * 60 * 60);
            }
            if (id > idList.size()) {
                log.info("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务：数据已全部处理");
                content.setContent("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务：数据已全部处理");
                jedisCluster.del(KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH);
                jedisCluster.del(KEY_ALL_CRM_CUSTOMER_HANDLE_BATCH_ID);
                break;
            }

            LambdaQueryWrapper<PlannerUserBind> bindWrapper = new LambdaQueryWrapper<>();
            bindWrapper.select(PlannerUserBind::getPlannerId, PlannerUserBind::getUserId)
                    .ge(PlannerUserBind::getId, idList.get(Math.toIntExact(id - 1)));
            if (id < idList.size()) {
                bindWrapper.lt(PlannerUserBind::getId, idList.get(Math.toIntExact(id)));
            }
            bindWrapper.eq(PlannerUserBind::getResource, PlannerUserBindServiceImpl.SOURCE_CRM);
            List<PlannerUserBind> list = iPlannerUserBindService.list(bindWrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            List<AiPlannerTopUser> userList = new ArrayList<>();
            Map<String, Set<String>> bindMap = list.stream()
                    .collect(Collectors.groupingBy(PlannerUserBind::getPlannerId,
                            Collectors.mapping(PlannerUserBind::getUserId, Collectors.toSet())));
            for (Map.Entry<String, Set<String>> entry : bindMap.entrySet()) {
                LambdaQueryWrapper<AiPlannerTopUser> userWrapper = new LambdaQueryWrapper<>();
                userWrapper.eq(AiPlannerTopUser::getPlannerNo, entry.getKey()).in(AiPlannerTopUser::getUserId, entry.getValue())
                        .eq(AiPlannerTopUser::getTenantId, yrTenantId);
                List<AiPlannerTopUser> users = aiPlannerTopUserService.list(userWrapper);
                if (CollUtil.isNotEmpty(users)) {
                    userList.addAll(users);
                }
            }

            List<AiPlannerChatContact> chatList = new ArrayList<>();
            if (CollUtil.isNotEmpty(userList)) {
                for (AiPlannerTopUser user : userList) {
                    LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(AiPlannerChatContact::getPlannerNo, user.getPlannerNo())
                            .eq(AiPlannerChatContact::getUserId, user.getUserId())
                            .lt(AiPlannerChatContact::getMsgTime, endTime)
                            .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                            .orderByDesc(AiPlannerChatContact::getMsgTime).last("limit 1");
                    AiPlannerChatContact contact = iAiPlannerChatContactService.getOne(wrapper);
                    if (Objects.isNull(contact) || StrUtil.isEmpty(contact.getNasPath()) || StrUtil.isNotBlank(contact.getProcessedContent())) {
                        continue;
                    }
                    chatList.add(contact);
                }
            }

            if (CollUtil.isNotEmpty(chatList)) {
                CountDownLatch latch = new CountDownLatch(chatList.size());
                Map<Long, Future<?>> futures = new HashMap<>();
                for (AiPlannerChatContact contact : chatList) {
                    futures.put(contact.getId(), avayaSplitCrmThreadPoolExecutor.submit(() -> {
                        try {
                            LocalTime nowTime = LocalTime.now();
                            if (nowTime.isAfter(LocalTime.of(6, 0, 0)) && nowTime.isBefore(LocalTime.of(20, 30, 0))) {
                                log.info("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务结束, 异步线程执行时间已到");
                                return;
                            }

                            String text = getCrmWithVoiceWithDesignatedPlanner(contact, null, deNoiseRecord, translateRecord, searchRecord);
                            if ("[avaya语音]".equals(text)) {
                                LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                                wrapperText.set(AiPlannerChatContact::getProcessingContent, text)
                                        .set(AiPlannerChatContact::getProcessedContent, text)
                                        .eq(AiPlannerChatContact::getId, contact.getId());
                                iAiPlannerChatContactService.update(wrapperText);
                            }
                            aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(contact.getId()).build());
                        } catch (Exception e) {
                            log.error("切割Avaya语料[id = {}]发生异常，异常原因为", contact.getId(), e);
                            throw e;
                        } finally {
                            latch.countDown();
                        }
                    }));
                }

                try {
                    latch.await();
                } catch (Exception e) {
                    log.error("主线程等待异常，异常原因为", e);
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "主线程等待异常");
                }

                Map<Long, String> err = new HashMap<>();
                for (Map.Entry<Long, Future<?>> future : futures.entrySet()) {
                    try {
                        future.getValue().get();
                    } catch (Exception e) {
                        Throwable cause = e.getCause();
                        if (cause instanceof AiServerException) {
                            err.put(future.getKey(), cause.getMessage());
                        }
                    }
                }
                if (MapUtil.isNotEmpty(err)) {
                    content.setContent(
                            "【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料：当前批次中" + err.size() + "条语料处理失败，详细失败信息如下：\n" +
                            err.entrySet().stream().map(e -> e.getKey() + "    " + e.getValue()).collect(Collectors.joining("\n")));
                    try {
                        HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                    } catch (Exception e) {
                        log.error("发送企微群机器人通知失败", e);
                    }
                }
            }

            if (list.size() < 100) {
                break;
            }
        }

        if (!"【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务：数据已全部处理".equals(content.getContent())) {
            content.setContent("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务结束");
        }
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }
        log.info("【全量Crm客户&每人处理指定时间前最新一条数据】切割Avaya语料任务结束");
    }

    public void splitAdd(LocalDateTime startTime) {
        log.info("切割增量Avaya语料任务开始");
        AiPromptRecordVo deNoiseRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.DENOISE.getCode());
        AiPromptRecordVo translateRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_TO_TEXT.getCode());
        AiPromptRecordVo searchRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_SEARCH.getCode());

        SendRobotReq.Content content = new SendRobotReq.Content();
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");

        LocalDateTime compareTime = LocalDateTime.now();
        Long startId = null;
        String startCache = jedisCluster.get(KEY_AVAYA_SPLIT_ADD_START_ID);
        if (StrUtil.isNotBlank(startCache)) {
            startId = Long.parseLong(startCache);
        }
        AtomicReference<Long> startIdNext = new AtomicReference<>(null);
        while (true) {
            Duration duration = Duration.between(compareTime, LocalDateTime.now());
            if (duration.toMinutes() >= (23 * 60 + 30)) {
                log.info("切割增量Avaya语料任务结束, 主线程执行时间已到");
                break;
            }

            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.ge(AiPlannerChatContact::getMsgTime, startTime)
                    .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                    .isNotNull(AiPlannerChatContact::getNasPath).apply("LENGTH(TRIM(nas_path)) != 0")
                    .and(w -> w.isNull(AiPlannerChatContact::getProcessedContent).or().apply("LENGTH(TRIM(processed_content)) = 0"));
            if (Objects.nonNull(startId)) {
                wrapper.gt(AiPlannerChatContact::getId, startId);
            }
            wrapper.orderByAsc(AiPlannerChatContact::getId).last("limit 100");
            List<AiPlannerChatContact> list = iAiPlannerChatContactService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            CountDownLatch latch = new CountDownLatch(list.size());
            Map<Long, Future<?>> futures = new HashMap<>();
            for (AiPlannerChatContact contact : list) {
                Long finalStartId = startId;
                futures.put(contact.getId(), avayaAddThreadPoolExecutor.submit(() -> {
                    try {
                        Duration handleDuration = Duration.between(compareTime, LocalDateTime.now());
                        if (handleDuration.toMinutes() >= (23 * 60 + 30)) {
                            log.info("切割增量Avaya语料任务结束, 异步线程执行时间已到");
                            if (Objects.isNull(startIdNext.get()) || startIdNext.get().compareTo(finalStartId) > 0) {
                                startIdNext.set(finalStartId);
                            }
                            return;
                        }

                        String text = getCrmWithVoiceWithDesignatedPlanner(contact, null, deNoiseRecord, translateRecord, searchRecord);
                        if ("[avaya语音]".equals(text)) {
                            LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                            wrapperText.set(AiPlannerChatContact::getProcessingContent, text)
                                    .set(AiPlannerChatContact::getProcessedContent, text)
                                    .eq(AiPlannerChatContact::getId, contact.getId());
                            iAiPlannerChatContactService.update(wrapperText);
                        }
                        aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(contact.getId()).build());
                    } catch (Exception e) {
                        log.error("切割Avaya语料[id = {}]发生异常，异常原因为", contact.getId(), e);
                        throw e;
                    } finally {
                        latch.countDown();
                    }
                }));
            }
            try {
                latch.await();
            } catch (Exception e) {
                log.error("主线程等待异常，异常原因为", e);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "主线程等待异常");
            }

            Map<Long, String> err = new HashMap<>();
            for (Map.Entry<Long, Future<?>> future : futures.entrySet()) {
                try {
                    future.getValue().get();
                } catch (Exception e) {
                    Throwable cause = e.getCause();
                    if (cause instanceof AiServerException) {
                        err.put(future.getKey(), cause.getMessage());
                    }
                }
            }
            if (MapUtil.isNotEmpty(err)) {
                content.setContent(
                        "切割增量Avaya语料：当前批次中" + err.size() + "条语料处理失败，详细失败信息如下：\n" +
                        err.entrySet().stream().map(e -> e.getKey() + "    " + e.getValue()).collect(Collectors.joining("\n")));
                try {
                    HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                } catch (Exception e) {
                    log.error("发送企微群机器人通知失败", e);
                }
            }

            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }

        if (Objects.isNull(startIdNext.get())) {
            startIdNext.set(startId);
        }
        if (Objects.nonNull(startIdNext.get())) {
            jedisCluster.setex(KEY_AVAYA_SPLIT_ADD_START_ID, 3 * 24 * 60 * 60, String.valueOf(startIdNext.get()));
        }
        content.setContent("切割增量Avaya语料任务结束，下一次任务执行时开始处理的消息id为" + startIdNext.get());
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }
        log.info("切割增量Avaya语料任务结束，下一次任务执行时开始处理的消息id为{}", startIdNext.get());
    }

    public String getCusPhoneByCallType(String voiceDirectId, String callee, String caller) {
        if ("CX_BAS_CHANNEL_DIR_INBOUND".equals(voiceDirectId)) {
            return caller;
        }
        if ("CX_BAS_CHANNEL_DIR_OUTBOUND".equals(voiceDirectId) || "CX_BAS_CHANNEL_DIR_YRD_DIANXIAO".equals(voiceDirectId)) {
            return callee;
        }
        return "";
    }

    /**
     * 切割指定时间段内的avaya语料
     * @param startTime 开始时间（大于等于）
     * @param endTime 结束时间（小于）
     * @param size 批次大小
     */
    public void splitTimePeriod(LocalDateTime startTime, LocalDateTime endTime, Integer size) {
        log.info("切割指定时间段内的Avaya语料任务开始");
        AiPromptRecordVo deNoiseRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.DENOISE.getCode());
        AiPromptRecordVo translateRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_TO_TEXT.getCode());
        AiPromptRecordVo searchRecord = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.VOICE_SEARCH.getCode());

        SendRobotReq.Content content = new SendRobotReq.Content();
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");

        String cache = jedisCluster.get(KEY_AVAYA_SPLIT_TIME_PERIOD_BATCH_START_ID);
        Long batchStartId = StrUtil.isBlank(cache) ? null : Long.parseLong(cache);
        log.info("切割指定时间段内的Avaya语料任务，当前批次开始处理的消息id={}", batchStartId);
        String cacheList = jedisCluster.get(KEY_AVAYA_SPLIT_TIME_PERIOD_BATCH_ID_RANGE);
        List<Long> idRange = null;
        if (StrUtil.isNotBlank(cacheList)) {
            idRange = JSON.parseArray(cacheList, Long.class);
        } else {
            idRange = iAiPlannerChatContactService.getIdRangeWithinTimePeriod(batchStartId, startTime, endTime, size);
            if (CollUtil.isEmpty(idRange)) {
                log.info("切割指定时间段内的Avaya语料任务结束，未查询到当前时间段{}-{}存在待处理的语料", startTime, endTime);
                content.setContent("切割指定时间段内的Avaya语料任务结束，未查询到当前时间段" + startTime + "-" + endTime + "存在待处理的语料");
                try {
                    HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                } catch (Exception e) {
                    log.error("发送企微群机器人通知失败", e);
                }
                return;
            }
            jedisCluster.setex(KEY_AVAYA_SPLIT_TIME_PERIOD_BATCH_ID_RANGE, 10 * 60 * 60, JSON.toJSONString(idRange));
        }

        AtomicReference<Long> startIdNext = new AtomicReference<>(null);
        Long index = 1L;
        while (true) {
            LocalTime now = LocalTime.now();
            if (now.isAfter(LocalTime.of(6, 0, 0)) && now.isBefore(LocalTime.of(20, 30, 0))) {
                log.info("切割指定时间段内的Avaya语料任务结束, 主线程执行时间已到");
                break;
            }

            boolean isExist = jedisCluster.exists(KEY_AVAYA_SPLIT_TIME_PERIOD_HANDLE_INDEX);
            index = jedisCluster.incr(KEY_AVAYA_SPLIT_TIME_PERIOD_HANDLE_INDEX);
            if (!isExist) {
                jedisCluster.expire(KEY_AVAYA_SPLIT_TIME_PERIOD_HANDLE_INDEX, 10 * 60 * 60);
            }
            if (index > idRange.size()) {
                log.info("切割指定时间段内的Avaya语料任务结束：当前批次数据已全部处理");
                content.setContent("切割指定时间段内的Avaya语料任务结束：当前批次数据已全部处理");
                jedisCluster.del(KEY_AVAYA_SPLIT_TIME_PERIOD_BATCH_ID_RANGE);
                jedisCluster.del(KEY_AVAYA_SPLIT_TIME_PERIOD_HANDLE_INDEX);
                break;
            }

            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.le(AiPlannerChatContact::getId, idRange.get(Math.toIntExact(index - 1)));
            if (index < idRange.size()) {
                wrapper.gt(AiPlannerChatContact::getId, idRange.get(Math.toIntExact(index)));
            }
            wrapper.ge(AiPlannerChatContact::getMsgTime, startTime)
                    .lt(AiPlannerChatContact::getMsgTime, endTime)
                    .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.AVAYA.getType())
                    .isNotNull(AiPlannerChatContact::getNasPath).apply("TRIM(nas_path) != ''")
                    .and(w -> w.isNull(AiPlannerChatContact::getProcessedContent).or().apply("TRIM(processed_content) = ''"))
                    .orderByDesc(AiPlannerChatContact::getId);
            List<AiPlannerChatContact> list = iAiPlannerChatContactService.list(wrapper);
            if (CollUtil.isEmpty(list)) {
                continue;
            }

            CountDownLatch latch = new CountDownLatch(list.size());
            Map<Long, Future<?>> futures = new HashMap<>();
            for (AiPlannerChatContact contact : list) {
                futures.put(contact.getId(), avayaSplitCrmThreadPoolExecutor.submit(() -> {
                    try {
                        LocalTime nowTime = LocalTime.now();
                        if (nowTime.isAfter(LocalTime.of(6, 0, 0)) && nowTime.isBefore(LocalTime.of(20, 30, 0))) {
                            log.info("切割指定时间段内的Avaya语料任务结束, 异步线程执行时间已到");
                            if (Objects.isNull(startIdNext.get()) || startIdNext.get().compareTo(contact.getId()) < 0) {
                                startIdNext.set(contact.getId());
                            }
                            return;
                        }

                        String text = getCrmWithVoiceWithDesignatedPlanner(contact, null, deNoiseRecord, translateRecord, searchRecord);
                        if ("[avaya语音]".equals(text)) {
                            LambdaUpdateWrapper<AiPlannerChatContact> wrapperText = new LambdaUpdateWrapper<>();
                            wrapperText.set(AiPlannerChatContact::getProcessingContent, text)
                                    .set(AiPlannerChatContact::getProcessedContent, text)
                                    .eq(AiPlannerChatContact::getId, contact.getId());
                            iAiPlannerChatContactService.update(wrapperText);
                        }
                        aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(contact.getId()).build());
                    } catch (Exception e) {
                        log.error("切割Avaya语料[id = {}]发生异常，异常原因为", contact.getId(), e);
                        throw e;
                    } finally {
                        latch.countDown();
                    }
                }));
            }

            try {
                latch.await();
            } catch (Exception e) {
                log.error("主线程等待异常，异常原因为", e);
                throw new AiServerException(ResultCode.FAILURE.getCode(), "主线程等待异常");
            }

            Map<Long, String> err = new HashMap<>();
            for (Map.Entry<Long, Future<?>> future : futures.entrySet()) {
                try {
                    future.getValue().get();
                } catch (Exception e) {
                    Throwable cause = e.getCause();
                    if (cause instanceof AiServerException) {
                        err.put(future.getKey(), cause.getMessage());
                    }
                }
            }
            if (MapUtil.isNotEmpty(err)) {
                content.setContent(
                        "切割指定时间段内的Avaya语料：当前批次中" + err.size() + "条语料处理失败，详细失败信息如下：\n" +
                                err.entrySet().stream().map(e -> e.getKey() + "    " + e.getValue()).collect(Collectors.joining("\n")));
                try {
                    HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                } catch (Exception e) {
                    log.error("发送企微群机器人通知失败", e);
                }
            }
        }

        if (Objects.isNull(startIdNext.get())) {
            startIdNext.set(idRange.get(Math.toIntExact(Math.min(index, idRange.size()) - 1)));
        }
        jedisCluster.setex(KEY_AVAYA_SPLIT_TIME_PERIOD_BATCH_START_ID, 3 * 24 * 60 * 60, String.valueOf(startIdNext.get()));
        if (!"切割指定时间段内的Avaya语料任务结束：当前批次数据已全部处理".equals(content.getContent())) {
            content.setContent("切割指定时间段内的Avaya语料任务结束，下一批次开始处理的消息id为" + startIdNext.get());
        }
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }
        log.info("切割指定时间段内的Avaya语料任务结束，下一批次开始处理的消息id为{}", startIdNext.get());
    }
}
