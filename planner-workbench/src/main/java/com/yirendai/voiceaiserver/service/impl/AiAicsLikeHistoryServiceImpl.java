package com.yirendai.voiceaiserver.service.impl;

import com.yirendai.workbench.mapper.AiAicsLikeHistoryMapper;
import com.yirendai.workbench.entity.AiAicsLikeHistory;
import com.yirendai.voiceaiserver.service.IAiAicsLikeHistoryService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 基金ai客服历史对话点赞记录表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2024-01-22
 */
@Service
public class AiAicsLikeHistoryServiceImpl extends ServiceImpl<AiAicsLikeHistoryMapper, AiAicsLikeHistory>
        implements IAiAicsLikeHistoryService {

    @Autowired
    AiAicsLikeHistoryMapper aiAicsLikeHistoryMapper;

    @Override
    public IPage<AiAicsLikeHistory> listLikeHistoryPage(String customerNo, String startTime, String endTime,
            int pageNum, int pageSize) {

        Page<AiAicsLikeHistory> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<AiAicsLikeHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotEmpty(customerNo), AiAicsLikeHistory::getCustomerNo, customerNo)
                .ge(StrUtil.isNotEmpty(startTime), AiAicsLikeHistory::getCreateTime, startTime)
                .le(StrUtil.isNotEmpty(endTime), AiAicsLikeHistory::getCreateTime, endTime)
                .orderByDesc(AiAicsLikeHistory::getCreateTime);

        return aiAicsLikeHistoryMapper.selectPage(page, queryWrapper);

    }
}
