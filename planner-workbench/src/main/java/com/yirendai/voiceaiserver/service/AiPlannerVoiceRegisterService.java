package com.yirendai.voiceaiserver.service;

import com.yirendai.workbench.entity.AiPlannerVoiceRegister;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 理财师声纹注册信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface AiPlannerVoiceRegisterService extends IService<AiPlannerVoiceRegister> {

    void register(String tenantId, String model);

    void registerPlanner(String tenantId, String plannerNo, String model);
}
