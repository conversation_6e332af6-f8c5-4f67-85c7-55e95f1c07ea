package com.yirendai.voiceaiserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Strings;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.*;
import com.yirendai.voiceaiserver.mq.dto.MqBaseDto;
import com.yirendai.voiceaiserver.mq.send.MqSendUtil;
import com.yirendai.voiceaiserver.mq.send.TopicEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.service.AiConversationProcessTaskService;
import com.yirendai.voiceaiserver.task.AiConversationRetryableTask;
import com.yirendai.voiceaiserver.task.AiRetryableTask;
import com.yirendai.voiceaiserver.task.conversation.factory.AiConversationRetryableTaskFactory;
import com.yirendai.voiceaiserver.task.priority.PriorityThreadEnum;
import com.yirendai.voiceaiserver.task.priority.PriorityThreadPoolExecutor;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.entity.AiConversationProcessTask;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiConversationProcessTaskMapper;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.workbench.mapper.CallcenterVisitRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class AIConversationProcessServiceImpl implements AIConversationProcessService {

    @Resource
    private AiConversationProcessTaskMapper aiConversationProcessTaskMapper;
    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;

    @Resource
    CallcenterVisitRecordMapper callcenterVisitRecordMapper;

    @Resource
    private AiConversationProcessTaskService aiConversationProcessTaskService;
    @Resource
    private MqSendUtil mqSendUtil;


    @Value("${ai.text.process.retry}")
    private int textProcessRetry;

    @Value("${third.access.tenantId.kaishi}")
    private String tenantIdKaishi;
    @Value("${third.access.tenantId.yiren}")
    private String tenantIdYiren;
    /**
     * 是否开启AI文本处理MQ任务
     * 0:关闭 1:开启
     */
    @Value("${ai.text.mq.open}")
    private int aiTextMqOpen;

    @Value("${ai.text.compliance.enabled}")
    private boolean aiTextComplianceEnabled;

    @Value("${ai.text.customer.tag.enabled}")
    private boolean aiTextCustomerTagEnabled;

    @Resource
    private JedisCluster jedisCluster;

    @Override
    public void submitMQTasks(AiConversationProcessReq requestData) {
        try {
            log.info("处理消息id={}事务提交后，发送MQ触发AI解析任务", requestData.getChatContactId());
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        log.info("处理消息id={}事务已经提交，开始发送MQ触发AI解析任务", requestData.getChatContactId());
                        submitMQTasksAfterCommit(requestData);
                    }
                });
            } else {
                submitMQTasksAfterCommit(requestData);
            }
        } catch (Exception e) {
            log.error("处理消息id={}事务提交后，发送MQ触发AI解析任务发生异常，异常原因为", requestData.getChatContactId(), e);
        }
    }

    private void submitMQTasksAfterCommit(AiConversationProcessReq requestData) {
        log.info("AI Conversation Process Task aiTextMqOpen:{} submitted: {}", aiTextMqOpen, requestData);
        if (aiTextMqOpen == 0) {
            return;
        }
        MqBaseDto mqBaseDto = new MqBaseDto();
        mqBaseDto.setChatContactId(requestData.getChatContactId());
        // 创建AI解析任务
        mqSendUtil.sendMessage(TopicEnum.SUMMARY, mqBaseDto);
        mqSendUtil.sendMessage(TopicEnum.USER_TAG, mqBaseDto);
        // 凯石临时的合规回访任务
        AiPlannerChatContact aiPlannerChatContact = aiPlannerChatContactMapper.selectById(requestData.getChatContactId());
        if (aiTextComplianceEnabled && aiPlannerChatContact != null && Objects.equals(tenantIdKaishi, aiPlannerChatContact.getTenantId())) {
            mqBaseDto.setType(AiConversationProcessTypeEnum.AI_CALL_USER_COMPLIANCE_RESULT.getCode());
            mqSendUtil.sendMessage(AiConversationProcessTypeEnum.AI_CALL_USER_COMPLIANCE_RESULT.getMqTopic(), mqBaseDto);
        }
        // 宜人新增标签识别任务
        if (aiTextCustomerTagEnabled && aiPlannerChatContact != null && Objects.equals(tenantIdYiren, aiPlannerChatContact.getTenantId())) {
            MqBaseDto mqYrBaseDto = new MqBaseDto();
            String nodeTaskId = AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode() + "-" + AiConversationDataSourceEnum.CALL_CENTER.getCode() + "_" + requestData.getChatContactId();
            mqYrBaseDto.setTaskId(nodeTaskId);
            mqYrBaseDto.setChatContactId(requestData.getChatContactId());
            mqYrBaseDto.setType(AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getCode());
            mqSendUtil.sendMessage(AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.getMqTopic(), mqYrBaseDto);
        }
//        mqSendUtil.sendMessage(TopicEnum.CONVERSATION_INSPECT, mqBaseDto);
//        mqSendUtil.sendMessage(TopicEnum.CALL_RESERVATION, mqBaseDto);
    }

    @Resource
    private PriorityThreadPoolExecutor aiConversationProcessThreadPool;

    @Resource
    private AiConversationRetryableTaskFactory aiConversationRetryableTaskFactory;

    // http拉文本->存到chat-contact，当前对话结束触发这个方法
    @Override
    public void submitTasks(AiConversationProcessReq requestData) {
        // 创建AI解析任务
        for (int i = 0; i < 4; i++) {
            requestData.setTaskType(AiConversationProcessTypeEnum.getByCode(i));
            submitTask(requestData);
        }
    }

    @Override
    public void submitTask(AiConversationProcessReq requestData) {
        // 准备提交数据
        AiConversationProcessTypeEnum taskType = requestData.getTaskType();
        if (taskType == null) {
            log.error("Ai Conversation Process Task type is null");
            return;
        }
        AiConversationProcessTask taskData = prepareSubmitData(requestData, taskType);
        if (taskData == null) {
            log.error("Ai Conversation Process Task data is null");
            return;
        }
        // 创建一个新的任务对象
        AiRetryableTask task = aiConversationRetryableTaskFactory.createTask(taskData, requestData);
        try {
            log.info("Ai Conversation Process Task submitted: {}, type: {}", taskData.getTaskId(), taskType);
            if (taskType == AiConversationProcessTypeEnum.CONTENT_SUMMARY || taskType == AiConversationProcessTypeEnum.AI_SERVICE_WORK_ORDER) {
                // 任务提交到高优先级线程池
                aiConversationProcessThreadPool.execute(task, PriorityThreadEnum.HIGH);
            } else {
                // 任务提交到普通优先级线程池
                aiConversationProcessThreadPool.execute(task, PriorityThreadEnum.MEDIUM);
            }
        } catch (RejectedExecutionException e) {
            // 任务被拒绝，已经由拒绝策略处理
            log.error("Ai Conversation Process Task rejected: {}, type: {}", taskData.getTaskId(), taskType);
        }
    }

    /**
     * 准备提交数据
     */
    private AiConversationProcessTask prepareSubmitData(AiConversationProcessReq requestData, AiConversationProcessTypeEnum taskType) {
        String taskId = requestData.getTaskId();
        // 默认使用通用模型
        requestData.setModelType(AiModelType.COMMON_MODEL);
        Long chatContactId = requestData.getChatContactId();
        if (Objects.isNull(chatContactId)) {
            if (Strings.isNullOrEmpty(taskId)) {
                log.error("AI Conversation Process Task TaskId is empty");
                throw new AiServerException(ResultCode.PARAM_INVALID);
            }
            Long contactFromTaskId = aiConversationProcessTaskService.extractChatContactFromTaskId(taskId);
            if (Objects.isNull(contactFromTaskId)) {
                log.error("AI对话来源记录不存在，taskID: {}", requestData.getTaskId());
                throw new AiServerException(ResultCode.NO_AI_CONVERSATION_SOURCE);
            }
            requestData.setChatContactId(contactFromTaskId);
            chatContactId = contactFromTaskId;
        } else if (Strings.isNullOrEmpty(taskId)){
            taskId = aiConversationProcessTaskService.buildTaskId(chatContactId, taskType);
            if (Strings.isNullOrEmpty(requestData.getTaskId())) {
                requestData.setTaskId(taskId);
            }
        }
        String taskBusId = null;
        String originType = null;
        if (taskType.getDataClass() == AiPlannerChatContact.class) {
            // 来源是chat-contact表的特殊处理
            AiPlannerChatContact aiPlannerChatContact = aiPlannerChatContactMapper.selectById(chatContactId);
            if (Objects.isNull(aiPlannerChatContact)) {
                throw new AiServerException(ResultCode.CHAT_CONTENT_EMPTY);
            }
            log.info("Ai Conversation Process Task aiPlannerChatContact: {}", aiPlannerChatContact);
            requestData.setPlannerNo(aiPlannerChatContact.getPlannerNo());
            requestData.setUserId(aiPlannerChatContact.getUserId());
            requestData.setContent(aiPlannerChatContact.getProcessedContent());
            requestData.setCallTime(aiPlannerChatContact.getMsgTime());
            requestData.setTenantId(aiPlannerChatContact.getTenantId());
            requestData.setUserPhone(aiPlannerChatContact.getPhone());
            originType = aiPlannerChatContact.getOriginType();
            if (QwMsgTypeEnum.CALL_CENTER.getType().equals(aiPlannerChatContact.getOriginType())) {
                taskBusId = QwMsgTypeEnum.CALL_CENTER.getType() +  ":" + aiPlannerChatContact.getBusId();
            } else if (QwMsgTypeEnum.AVAYA.getType().equals(aiPlannerChatContact.getOriginType())){
                taskBusId = QwMsgTypeEnum.AVAYA.getType() +  ":" + aiPlannerChatContact.getAvayaId();
            }
        }

        LambdaQueryWrapper<AiConversationProcessTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationProcessTask::getTaskId, taskId);
        AiConversationProcessTask taskData = aiConversationProcessTaskMapper.selectOne(queryWrapper);
        if (taskData != null && Objects.equals(taskData.getTaskStatus(), AiConversationProcessStatusEnum.FINISHED.getCode())) {
            // 任务已存在并执行完成，跳过
            log.info("Ai Conversation Process Task already finished: {}, type: {}", taskId, taskType);
            return null;
        } else if (taskData == null) {
            taskData = new AiConversationProcessTask();
            taskData.setTaskId(taskId);
            taskData.setTaskType(taskType.getCode());
            taskData.setMaxRetry(textProcessRetry);
            taskData.setRetryCount(0);
            // 查询小结的缓存，其他不用
            if (!Strings.isNullOrEmpty(taskBusId)) {
                if (AiConversationProcessTypeEnum.CONTENT_SUMMARY.equals(taskType)) {
                    if (jedisCluster.exists(taskBusId)) {
                        jedisCluster.setex(taskBusId, 30, "1");
                    }
                }
                taskData.setTaskData(taskBusId);
            }
            taskData.setTaskStatus(AiConversationProcessStatusEnum.NOT_START.getCode());
            taskData.setCreateTime(LocalDateTime.now());
            taskData.setUpdateTime(LocalDateTime.now());
            taskData.setTenantId(requestData.getTenantId());
            // 如果是12类型，默认宜人租户
            if (AiConversationProcessTypeEnum.HONG_KONG_INSURANCE_INTENT.equals(taskType)) {
                taskData.setTenantId(tenantIdYiren);
            }
            // ！！！AVAYA 第一次仅初始化，不执行任务，设置状态为待处理，后续通过xxlJob调度执行，因为avaya同步并发太高 ！！！
            // !!! 针对任务对话分析文本处理，不直接执行任务，首次设置状态为待处理，后续通过xxlJob调度执行
            if (QwMsgTypeEnum.AVAYA.getType().equals(originType)) {
                taskData.setTaskStatus(AiConversationProcessStatusEnum.INIT_WAITING.getCode());
            }
            if (AiConversationProcessTypeEnum.AI_TASK_CONTENT_ANALYSIS.equals(taskType) || AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.equals(taskType)) {
                taskData.setTaskStatus(AiConversationProcessStatusEnum.LOW_PRIORITY_INIT.getCode());
            }
            aiConversationProcessTaskMapper.insert(taskData);
            if (QwMsgTypeEnum.AVAYA.getType().equals(originType)
                    || AiConversationProcessTypeEnum.AI_TASK_CONTENT_ANALYSIS.equals(taskType)
                    || AiConversationProcessTypeEnum.CUSTOMER_DATA_ANALYSIS.equals(taskType)) {
                log.info("Ai Conversation Process Task taskData init task: {}", taskData);
                return null;
            }
        } else {
            log.info("Ai Conversation Process Task taskStatus: {}", taskData.getTaskStatus());
            if (Objects.equals(taskData.getTaskStatus(), AiConversationProcessStatusEnum.FAILED.getCode()) && Objects.equals(ResultCode.AI_REPLY_INVALID_JSON.getMessage(), taskData.getLastFailReason())) {
                // 此处为默认的大参数模型，用于处理失败的任务
                requestData.setModelType(AiModelType.LARGE_MODEL);
            }
            taskData.setTaskStatus(AiConversationProcessStatusEnum.NOT_START.getCode());
            taskData.setUpdateTime(LocalDateTime.now());
            aiConversationProcessTaskMapper.updateById(taskData);
        }
        return taskData;
    }


    @Override
    public void submitSerialTask(AiConversationProcessReq requestData, AiConversationProcessTypeEnum taskType) {
        // 准备提交数据
        AiConversationProcessTask taskData = prepareSubmitData(requestData, taskType);
        if (taskData == null) {
            return;
        }
        // 创建一个新的任务对象
        AiConversationRetryableTask task = aiConversationRetryableTaskFactory.createTask(taskData, requestData);
        log.info("Ai Conversation Process Serial Task submitted: {}, type: {}", taskData.getTaskId(), taskType);
        task.run();
    }
}
