package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.AiCustomerWorkOrder;

import java.util.List;

/**
 * AI客服工单表(AiCustomerWorkOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03 17:00:05
 */
public interface AiCustomerWorkOrderService extends IService<AiCustomerWorkOrder> {

    AiCustomerWorkOrder getLatestAiCustomerWorkOrder(String voiceId, Long templateId, String tenantId, Integer type);

    List<AiCustomerWorkOrder> findAiCustomerWorkOrder(String voiceId, Long templateId, String tenantId, Integer type);
}
