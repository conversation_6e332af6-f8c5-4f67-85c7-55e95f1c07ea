package com.yirendai.voiceaiserver.service.impl;


import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.workbench.mapper.AiMediaMapper;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.voiceaiserver.model.po.AiChatPO;
import com.yirendai.voiceaiserver.service.AiChatMessageHandleService;
import com.yirendai.voiceaiserver.service.AiMediaService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RefreshScope
public class AiChatMessageHandleServiceImpl implements AiChatMessageHandleService {

    @Resource
    private AiMediaMapper aiMediaMapper;
    @Resource
    AiMediaService aiMediaService;
    @Resource
    private AiPlannerChatContactMapper aiPlannerChatContactMapper;
    @Resource
    IAiPlannerChatContactService aiPlannerChatContactService;

    @Value("${chat-api.key}")
    private String apiKey;

    @Value("${chat-api.url}")
    private String baseUrl;
    private static final int LIMIT = 1000;
    private static final String IMAGE = "image";
    private static final String MIXED = "mixed";
    private static final String EMOTION = "emotion";
    private static final String TEXT = "text";
    private static final String EMOTION_CONTENT = "这是一个表情包";


    @Override
    public void pictureHandle() {
        long count = aiMediaMapper.getHandleCount(IMAGE);
        int batchNum = (int) Math.ceil((double) count / LIMIT);
        for (int i = 0; i < batchNum; i++) {
            List<AiChatPO> aiMediaPoList = aiMediaMapper.getHandleList(LIMIT, IMAGE);
            aiMediaPoList.forEach(po -> {
                File sourceFile = null;
                String processedText = null;
                try {
                    String originContent = po.getOriginContent();
                    //根据URL下载文件至sourceFile
                    sourceFile =  aiMediaService.urlToFile(originContent, ".png");
                    if (Objects.isNull(sourceFile)) {
                        return;
                    }
                    //调用远程接口提取文字
                    processedText = this.pictureToText(sourceFile);
                    if (Objects.isNull(processedText)) {
                        return;
                    }
                    //更新数据库
                    po.setProcessedContent(processedText);
                    this.updateProcessed(po);
                } finally {
                    this.deleteFile(sourceFile);
                }
            });
        }
    }

    @Override
    @Async("commonThreadPoolExecutor")
    public void pictureSplitHandle(Long start, Long end) {
        log.info("开始处理图片批次为start={},end={}...", start, end);
        Long handleStart = start;
        try {
            while (start.compareTo(end) < 0) {
                LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
                wrapper.gt(AiPlannerChatContact::getId, start).le(AiPlannerChatContact::getId, end)
                        .eq(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.IMAGE.getType())
                        .isNotNull(AiPlannerChatContact::getOriginContent).ne(AiPlannerChatContact::getOriginContent, "")
                        .and(w -> w.isNull(AiPlannerChatContact::getProcessedContent).or().eq(AiPlannerChatContact::getProcessedContent, ""))
                        .orderByAsc(AiPlannerChatContact::getId).last("LIMIT 100");
                List<AiPlannerChatContact> list = aiPlannerChatContactService.list(wrapper);
                if (CollUtil.isEmpty(list)) {
                    break;
                }

                list.forEach(po -> {
                    File sourceFile = null;
                    try {
                        sourceFile = aiMediaService.urlToFile(po.getOriginContent(), ".png");
                        if (Objects.isNull(sourceFile)) {
                            return;
                        }
                        String processedText = this.pictureToText(sourceFile);
                        if (StrUtil.isEmpty(processedText)) {
                            return;
                        }
                        LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(AiPlannerChatContact::getProcessedContent, processedText).eq(AiPlannerChatContact::getId, po.getId());
                        aiPlannerChatContactService.update(updateWrapper);
                    } finally {
                        this.deleteFile(sourceFile);
                    }
                });

                if (list.size() < 100) {
                    break;
                }
                start = list.get(list.size() - 1).getId();
            }
            log.info("处理图片批次为start={},end={}结束", handleStart, end);
        } catch (Exception e) {
            log.error("处理图片批次为start={},end={}发生异常，异常原因为", handleStart, end, e);
        }
    }

    @Override
    public void mixedHandle() {
        long count = aiMediaMapper.getHandleCount(MIXED);
        int batchNum = (int) Math.ceil((double) count / LIMIT);
        for (int i = 0; i < batchNum; i++) {
            List<AiChatPO> aiMediaPoList = aiMediaMapper.getHandleList(LIMIT, MIXED);
            aiMediaPoList.forEach(po -> {
                String originContent = po.getOriginContent();
                //提取mixed消息类型的文字并拼接
                String processedText = this.getTextFromOriginContent(originContent);
                po.setProcessedContent(processedText);
                this.updateProcessed(po);
            });
        }
    }


    @Override
    public String pictureToText(File file) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + apiKey);
        Map<String, Object> formParams = new HashMap<>();
        formParams.put("file", file);
        formParams.put("fileType", "image");

        log.info("远程调用OCR识别图片开始, 请求参数为:{}", JSONUtil.toJsonStr(formParams));
        try {
            HttpRequest request = HttpUtil.createPost(baseUrl + "/ocr/recognition")
                    .addHeaders(headers)
                    .form(formParams);
            HttpResponse response = request.execute();
            if (response.isOk()) {
                log.info("调用OCR识别图片文字成功, 返回值为: {}", response.body());
                return convertResponseToText(response.body());
            }else {
                log.info("调用OCR识别图片文字失败, 返回状态为: {}", response.getStatus());
            }
        } catch (Exception e) {
            log.error("OCR识别图片文字失败", e);
        }
        return null;
    }


    /**
     * 将OCR请求后的结果进行拼接
     *
     * @param response OCR请求返回内容
     * @return 拼接后的text
     */
    private String convertResponseToText(String response) {
        //拼接后的字符串
        StringBuilder concatenatedText = new StringBuilder();
        JSONObject initJsonObject = JSONUtil.parseObj(response);
        JSONArray jsonArray = initJsonObject.getByPath("data.text", JSONArray.class);
        if (jsonArray.size() == 1 && "null".equals(jsonArray.get(0).toString())) {
            return "[图片]";
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray outerArray = jsonArray.getJSONArray(i);
            for (int j = 0; j < outerArray.size(); j++) {
                JSONArray innerArray = outerArray.getJSONArray(j);
                if (innerArray.size() > 1) {
                    concatenatedText.append(innerArray.getJSONArray(1).getStr(0)).append(" ");
                }
            }
        }
        return concatenatedText.toString();
    }

    /**
     * 更新处理结果到语料表
     *
     * @param po 处理对象
     */
    private void updateProcessed(AiChatPO po) {
        AiPlannerChatContact entity = new AiPlannerChatContact();
        entity.setId(po.getId());
        entity.setProcessedContent(po.getProcessedContent());
        aiPlannerChatContactMapper.updateById(entity);
    }

    /**
     * 删除临时文件
     *
     * @param sourceFile 临时文件
     */
    private void deleteFile(File sourceFile) {
        if (sourceFile != null && sourceFile.exists()) {
            boolean isDeleted = sourceFile.delete();
            if (isDeleted) {
                log.info("sourceFile isDeleted!");
            }
        }
    }


    /**
     * 从初始内容提取文字并拼接(mixed)
     * @param originContent 初始内容
     * @return 处理后内容
     */
    private String getTextFromOriginContent(String originContent) {
        //拼接后的字符串
        StringBuilder concatenatedText = new StringBuilder();
        JSONArray jsonArray = JSONUtil.parseArray(originContent);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String type = jsonObject.getStr("type");
            if (StrUtil.isEmpty(type)) {
                continue;
            }
            switch (type) {
                case TEXT:
                    concatenatedText.append(jsonObject.getStr("content")).append("\n");
                    break;
                case EMOTION:
                    concatenatedText.append(EMOTION_CONTENT).append("\n");
                    break;
                case IMAGE:
                    String pictureUrl = jsonObject.getStr("content");
                    File file = null;
                    try {
                        //下载图片
                        file = aiMediaService.urlToFile(pictureUrl, ".png");
                        if (Objects.isNull(file)) {
                            continue;
                        }
                        //调用远程接口提取文字
                        String extractedText = this.pictureToText(file);
                        //拼接字符串
                        concatenatedText.append(extractedText).append("\n");
                    } finally {
                        //删除临时文件
                        this.deleteFile(file);
                    }
                    break;
                default:
                    log.info("mixed类型提取文字时遇到未知类型, 类型为:{}", type);
            }
        }
        //删除最后的换行符
        concatenatedText.deleteCharAt(concatenatedText.length() - 1);
        return concatenatedText.toString();
    }
}
