package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.vo.request.ChatMessagesStreamReq;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 提示词Service
 */
public interface TeleprompterService {

    String getTeleprompter(String content, String account);

    void finish(String account);

    SseEmitter chatAicsMessageStream(ChatMessagesStreamReq chatMessagesReq);

    SseEmitter chatModelMessageStream(ChatMessagesStreamReq chatMessagesReq);

    SseEmitter chatAgentMessageStream(ChatMessagesStreamReq chatMessagesReq);
}
