package com.yirendai.voiceaiserver.service;

import com.yirendai.workbench.entity.AiQuestionBehaviorContext;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ai问题-用户行为-问题&答案 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
public interface AiQuestionBehaviorContextService extends IService<AiQuestionBehaviorContext> {

    AiQuestionBehaviorContext save(String content, Integer type, Long relationId);
}
