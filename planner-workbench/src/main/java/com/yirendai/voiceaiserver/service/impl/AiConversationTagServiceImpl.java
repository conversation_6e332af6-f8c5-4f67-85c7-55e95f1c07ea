package com.yirendai.voiceaiserver.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.enums.AiConversationTagTypeEnum;
import com.yirendai.voiceaiserver.service.AiConversationTagService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationTagExportDto;
import com.yirendai.workbench.entity.AiConversationTag;
import com.yirendai.workbench.mapper.AiConversationTagMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_tag(ai对话与标签关联表)】的数据库操作Service实现
* @createDate 2024-11-13 18:58:45
*/
@Service
public class AiConversationTagServiceImpl extends ServiceImpl<AiConversationTagMapper, AiConversationTag>
implements AiConversationTagService {

    @Resource
    private AiConversationTagMapper aiConversationTagMapper;

    @Override
    public void insertAiConversationTag(Long chatContactId, String plannerNo, String userId, String tenantId, Long processId, Long tagCategoryId, Long tagId, String category, String name, AiConversationTagTypeEnum matchStatus) {
        AiConversationTag aiConversationTag = new AiConversationTag();
        aiConversationTag.setChatContactId(chatContactId);
        aiConversationTag.setPlannerNo(plannerNo);
        aiConversationTag.setUserId(userId);
        aiConversationTag.setProcessId(processId);
        aiConversationTag.setTenantId(tenantId);
        if (tagCategoryId != null && tagId != null) {
            aiConversationTag.setTagId(tagId);
            aiConversationTag.setTagCategoryId(tagCategoryId);
        }
        aiConversationTag.setTagName(name);
        aiConversationTag.setTagCategoryName(category);
        aiConversationTag.setMatchStatus(matchStatus.getCode());
        aiConversationTag.setCreateTime(LocalDateTime.now());
        aiConversationTag.setUpdateTime(LocalDateTime.now());
        aiConversationTagMapper.insert(aiConversationTag);
    }

    @Override
    public void insertAiConversationTag(AiConversationProcessReq req, Long processId, Long tagCategoryId, Long tagId, String category, String name, AiConversationTagTypeEnum matchStatus) {
        insertAiConversationTag(req.getChatContactId(), req.getPlannerNo(), req.getUserId(), req.getTenantId(), processId, tagCategoryId, tagId, category, name, matchStatus);
    }

    @Override
    public List<AiConversationTagExportDto> getAllDataByMatchStatus(Integer matchStatus) {
        return aiConversationTagMapper.selectChatContacts(matchStatus);
    }

    @Override
    public void deleteByChatContactId(Long chatContactId) {
        LambdaQueryWrapper<AiConversationTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiConversationTag::getChatContactId, chatContactId);
        aiConversationTagMapper.delete(queryWrapper);
    }
}
