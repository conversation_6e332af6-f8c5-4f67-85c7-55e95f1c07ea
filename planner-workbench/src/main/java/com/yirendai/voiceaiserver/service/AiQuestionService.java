package com.yirendai.voiceaiserver.service;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.entity.AiQuestion;
import com.yirendai.voiceaiserver.vo.request.BasePageReq;
import com.yirendai.voiceaiserver.vo.request.QuestionBehaviorReq;
import com.yirendai.voiceaiserver.vo.request.QuestionSearchReq;
import com.yirendai.voiceaiserver.vo.response.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ai问题 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
public interface AiQuestionService extends IService<AiQuestion> {

    R<QuestionSearchRes> getTopicOrRecommendation(Integer size, Long parentId, Integer type);

    R<QuestionAndAnswerListRes> getToday();

    R<QuestionListRes> getSummaryList(Integer size, String content);

    R<QuestionAndAnswerListRes> search(QuestionSearchReq request);

    R<QuestionAndAnswerInfo> answer(String question, Long relationId, String account);

    R<Object> behavior(QuestionBehaviorReq request);

    R<IPage<AiQuestionBehaviorStarVO>> getPage(BasePageReq request);
}
