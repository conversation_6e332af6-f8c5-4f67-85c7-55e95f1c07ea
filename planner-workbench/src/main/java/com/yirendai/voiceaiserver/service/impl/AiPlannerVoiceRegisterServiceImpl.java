package com.yirendai.voiceaiserver.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.workbench.mapper.AiPlannerVoiceRegisterMapper;
import com.yirendai.workbench.entity.AiPlannerVoice;
import com.yirendai.workbench.entity.AiPlannerVoiceInfo;
import com.yirendai.workbench.entity.AiPlannerVoiceRegister;
import com.yirendai.voiceaiserver.service.AiPlannerVoiceInfoService;
import com.yirendai.voiceaiserver.service.AiPlannerVoiceRegisterService;
import com.yirendai.voiceaiserver.service.AiPlannerVoiceService;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.util.HttpClientProxyUtil;
import com.yirendai.voiceaiserver.vo.request.SendRobotReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 理财师声纹注册信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
@RefreshScope
@Transactional
public class AiPlannerVoiceRegisterServiceImpl extends ServiceImpl<AiPlannerVoiceRegisterMapper, AiPlannerVoiceRegister> implements AiPlannerVoiceRegisterService {

    @Value("${qw.robot.sync.url}")
    private String robotSyncUrl;

    @Value("${yr.tenant.id}")
    private String yrTenantId;

    @Resource
    AiPlannerVoiceInfoService aiPlannerVoiceInfoService;
    @Resource
    AiPlannerVoiceService aiPlannerVoiceService;
    @Resource
    AiPromptRecordService aiPromptRecordService;

    @Override
    @Async("commonThreadPoolExecutor")
    public void register(String tenantId, String model) {
        log.info("{}全量/剩余声纹注册到指定模型[{}]下开始", StrUtil.isBlank(tenantId) ? "" : "租户[" + tenantId + "]下的", model);
        SendRobotReq.Content content = new SendRobotReq.Content();
        content.setContent(String.format("%s全量/剩余声纹注册到指定模型[%s]下开始",
                StrUtil.isBlank(tenantId) ? "" : "租户[" + tenantId + "]下的", model));
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }

        LambdaQueryWrapper<AiPlannerVoiceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerVoiceInfo::getIsUse, Boolean.TRUE);
        if (StrUtil.isNotBlank(tenantId)) {
            wrapper.eq(AiPlannerVoiceInfo::getChannelId, tenantId);
        }
        List<AiPlannerVoiceInfo> list = aiPlannerVoiceInfoService.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            throw new AiServerException(ResultCode.NO_EXIST.getCode(), "未查询到可用的理财师声纹");
        }

        List<Long> idList = list.stream().map(AiPlannerVoiceInfo::getId).collect(Collectors.toList());
        LambdaQueryWrapper<AiPlannerVoiceRegister> registerWrapper = new LambdaQueryWrapper<>();
        registerWrapper.eq(AiPlannerVoiceRegister::getModel, model).in(AiPlannerVoiceRegister::getVoiceInfoId, idList);
        List<AiPlannerVoiceRegister> registerList = list(registerWrapper);
        if (CollUtil.isNotEmpty(registerList)) {
            List<Long> registerIdList = registerList.stream().map(AiPlannerVoiceRegister::getVoiceInfoId).distinct().collect(Collectors.toList());
            list = list.stream().filter(i -> !registerIdList.contains(i.getId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(list)) {
                log.info("{}全量/剩余声纹已全部注册到指定模型[{}]下", StrUtil.isBlank(tenantId) ? "" : "租户[" + tenantId + "]下的", model);
                return;
            }
        }

        AiPromptRecordVo recordVo = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.DENOISE.getCode());

        List<AiPlannerVoiceRegister> saveList = new ArrayList<>();
        for (AiPlannerVoiceInfo info : list) {
            AiPlannerVoice voice = new AiPlannerVoice();
            voice.setAccount(info.getPlannerNo());
            voice.setFilePath(info.getFilePath());
            voice.setVoiceUuid(UUID.randomUUID().toString());
            voice.setModel(model);
            String uuid = null;
            try {
                uuid = aiPlannerVoiceService.voiceRegister(voice, recordVo);
            } catch (Exception e) {
                log.error("注册理财师[{}]声纹失败，失败原因为", info.getPlannerNo(), e);
                continue;
            }
            AiPlannerVoiceRegister register = new AiPlannerVoiceRegister();
            register.setVoiceInfoId(info.getId());
            register.setModel(model);
            register.setVoiceUuid(voice.getVoiceUuid());
            register.setUuid(uuid);
            saveList.add(register);
        }
        saveBatch(saveList);

        content.setContent(String.format("%s全量/剩余声纹注册到指定模型[%s]下结束，%s条声纹注册成功，%s条声纹注册失败",
                StrUtil.isBlank(tenantId) ? "" : "租户[" + tenantId + "]下的", model, saveList.size(), list.size() - saveList.size()));
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }
        log.info("{}全量/剩余声纹注册到指定模型[{}]下结束，{}条声纹注册成功，{}条声纹注册失败",
                StrUtil.isBlank(tenantId) ? "" : "租户[" + tenantId + "]下的", model, saveList.size(), list.size() - saveList.size());
    }

    @Override
    public void registerPlanner(String tenantId, String plannerNo, String model) {
        tenantId = StrUtil.isBlank(tenantId) ? yrTenantId : tenantId;
        LambdaQueryWrapper<AiPlannerVoiceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerVoiceInfo::getPlannerNo, plannerNo).eq(AiPlannerVoiceInfo::getChannelId, tenantId)
                .eq(AiPlannerVoiceInfo::getIsUse, Boolean.TRUE);
        AiPlannerVoiceInfo info = aiPlannerVoiceInfoService.getOne(wrapper);
        if (Objects.isNull(info)) {
            throw new AiServerException(ResultCode.NO_EXIST.getCode(), "当前理财师无可用声纹");
        }

        LambdaQueryWrapper<AiPlannerVoiceRegister> registerWrapper = new LambdaQueryWrapper<>();
        registerWrapper.eq(AiPlannerVoiceRegister::getVoiceInfoId, info.getId()).eq(AiPlannerVoiceRegister::getModel, model);
        AiPlannerVoiceRegister register = getOne(registerWrapper);
        if (Objects.nonNull(register)) {
            throw new AiServerException(ResultCode.EXIST.getCode(), "当前理财师声纹已注册到指定模型下");
        }

        AiPromptRecordVo recordVo = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.DENOISE.getCode());

        AiPlannerVoice voice = new AiPlannerVoice();
        voice.setAccount(plannerNo);
        voice.setFilePath(info.getFilePath());
        voice.setVoiceUuid(UUID.randomUUID().toString());
        voice.setModel(model);
        String uuid = aiPlannerVoiceService.voiceRegister(voice, recordVo);
        AiPlannerVoiceRegister registerNew = new AiPlannerVoiceRegister();
        registerNew.setVoiceInfoId(info.getId());
        registerNew.setModel(model);
        registerNew.setVoiceUuid(voice.getVoiceUuid());
        registerNew.setUuid(uuid);
        save(registerNew);
    }
}
