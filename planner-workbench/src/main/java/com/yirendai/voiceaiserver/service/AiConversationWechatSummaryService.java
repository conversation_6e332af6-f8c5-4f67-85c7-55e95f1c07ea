package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.vo.request.AiFeedbackReq;
import com.yirendai.voiceaiserver.vo.response.AiConversationSummaryVO;
import com.yirendai.workbench.entity.AiConversationWechatSummary;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_conversation_wechat_summary(ai对话企微小结表)】的数据库操作Service
* @createDate 2024-12-04 17:11:24
*/
public interface AiConversationWechatSummaryService extends IService<AiConversationWechatSummary> {


    Boolean initConversationWechatSummary(List<AiPlannerChatContact> chatContactList);

    AiConversationSummaryVO selectByChatContactId(String plannerId, String userId, String date);

    Boolean feedback(AiFeedbackReq req);

    AiConversationWechatSummary selectByOnlyKey(String plannerId, String userId, String date);
}
