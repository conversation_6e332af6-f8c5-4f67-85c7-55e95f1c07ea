/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.voiceaiserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.voiceaiserver.vo.response.AiTagCategoryWithTags;
import com.yirendai.workbench.entity.AiUserTagDictionary;

import java.util.List;

/**
 * ai用户标签字典 服务类
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface IAiUserTagDictionaryService extends IService<AiUserTagDictionary> {


    /**
     * 查询所有标签分类及标签
     * @return List<AiTagCategoryWithTags>
     */
    List<AiTagCategoryWithTags> findAllCategoriesWithTags(String tenantId);

    AiUserTagDictionary selectByTagNameAndCategoryId(String name, Long categoryId, String tenantId);

    AiUserTagDictionary selectByTagName(String tagName, String tenantId);
}
