/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai;

import org.mybatis.spring.annotation.MapperScan;
import org.springblade.core.launch.BladeApplication;
import org.springframework.boot.CommandLineRunner;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Demo启动器
 *
 * <AUTHOR>
 */
@EnableFeignClients({"org.springblade", "com.yirendai.workbench.controller", "com.yirendai.voiceaiserver.controller"})
@SpringCloudApplication
@EnableAsync
@EnableScheduling
@EnableRetry
public class WorkbenchAdminApplication implements CommandLineRunner {

    public static void main(String[] args) {
        String appName = "planner-workbench"; // 默认值
        for (String arg : args) {
            System.out.println("arg=" + arg);
            if (arg.startsWith("--app-name=")) {
                appName = arg.substring("--app-name=".length());
                break;
            }
        }
        BladeApplication.run(appName, WorkbenchAdminApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
    }
}

