package com.yirendai.workbench.vo.res.callcenter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value="客户及资产信息", description="客户及资产信息")
public class CustomerAndAssetResp {
    @ApiModelProperty(value = "客户基本信息")
    private UserBasicInfo user;
    @ApiModelProperty(value = "资产统计信息")
    private AssetInfo assetInfo;
    @ApiModelProperty(value = "资产配置信息")
    private List<AssetDesc> holdAsset;


    @Data
    @ApiModel(value="客户基本信息", description="客户基本信息")
    public static class UserBasicInfo {
        @ApiModelProperty(value = "客户姓名")
        private String name;//  name
        @ApiModelProperty(value = "性别")
        private String gender;//  gnd_cd
        @ApiModelProperty(value = "年龄")
        private Integer age;// age
        @ApiModelProperty(value = "生日")
        private LocalDate birthday;// birthday    1  date  yyyy-MM-dd
        @ApiModelProperty(value = "手机")
        private String phone;// phone        1   string

        @ApiModelProperty(value = "加密后的手机号")
        private String encryptPhone;//

        @ApiModelProperty(value = "手机归属地省份")
        private String mobileProvince;// mobile_province   1  string
        @ApiModelProperty(value = "手机归属地城市")
        private String mobileCity;// mobile_city     1     string
        @ApiModelProperty(value = "微信号")
        private String userWechatNum;//   user_wechat_num  1    string
        @ApiModelProperty(value = "钱包深度")
        private String kycWalletPredictResult;//  kyc_wallet_predict_result
        @ApiModelProperty(value = "宜人财富用户在宜人金科的ID")
        private String userId;//	 id user_id
        @ApiModelProperty(value = "注册日期")
        private LocalDate registerDate;// regist_date  1    datetime   yyyy-MM-dd HH:mm:ss
        @ApiModelProperty(value = "最近一次登录宜人财富APP时间")
        private LocalDate lastActiveDate;//  格式  last_active_date

        @ApiModelProperty(value = "场景信息")
        private String sceneInfo;
    }

    @Data
    @ApiModel(value="资产统计信息", description="资产统计信息")
    public static class AssetInfo {
        @ApiModelProperty(value = "当前在投金额")
        private String platformAum;//   platform_aum
        @ApiModelProperty(value = "历史最高在投金额")
        private String platformMaxAum;//   platform_max_aum
        @ApiModelProperty(value = "最近一笔到期日期")
        private LocalDate nextOverdueDate;//最近一笔到期日期 最近一笔到期：“小额类固收未来最近一次退出时间”和“大额类固收下一次退出预计到期日期”比较，取距离当前时间最近的
        @ApiModelProperty(value = "最近一笔到期金额")
        private String nextOverdueAmt;// 取最近一笔到期对应的“大额类固收下一次退出金额”或“小额类固收未来最近一次退出金额”
        @ApiModelProperty(value = "最近一笔购买金额")
        private String lastSubscriptionAmt;
        @ApiModelProperty(value = "最近一笔购买日期")
        private LocalDate lastSubscriptionDate;//  最近一笔购买：“小额类固收历史最近一次购买时间”和“大额类固收最近一次购买日期”比较，取距离当前时间最近的

        private LocalDate axyRecentTradeDate;  //小额类固收历史最近一次购买时间   axy_recent_trade_date
        private LocalDate sfixedRecentTradeDate;  //大额类固收最近一次购买日期   sfixed_recent_trade_date

        private LocalDate axyNextExitDate; //小额类固收未来最近一次退出时间  axy_next_exit_date
        private LocalDate sfixedNextOverdueDate; //大额类固收下一次退出预计到期日期   sfixed_next_overdue_date

        private String sfixedNextOverdueAmount; //大额类固收下一次退出金额  sfixed_next_overdue_amount
        private String axyNextExitAmount; //小额类固收未来最近一次退出金额  axy_next_exit_amount

        /**
         * 初始化最新到期和最近购买
         */
        public void intNextOverdue(){
            /*
              1、最近到期时间、到期金额
             */
            if (axyNextExitDate != null && sfixedNextOverdueDate != null){
                if (axyNextExitDate.isBefore(sfixedNextOverdueDate)){
                    nextOverdueAmt = axyNextExitAmount;
                    nextOverdueDate = axyNextExitDate;
                }else {
                    nextOverdueAmt = sfixedNextOverdueAmount;
                    nextOverdueDate = sfixedNextOverdueDate;
                }
            }else if (axyNextExitDate != null){
                nextOverdueDate = axyNextExitDate;
                nextOverdueAmt = axyNextExitAmount;
            }else if (sfixedNextOverdueDate != null){
                nextOverdueDate = sfixedNextOverdueDate;
                nextOverdueAmt = sfixedNextOverdueAmount;
            }
            /*
            2、最近购买时间
             */
            if (axyRecentTradeDate != null && sfixedRecentTradeDate != null){
                lastSubscriptionDate = axyRecentTradeDate.isAfter(sfixedRecentTradeDate)? axyRecentTradeDate:sfixedRecentTradeDate;
            }else if (axyRecentTradeDate != null){
                lastSubscriptionDate = axyRecentTradeDate;
            }else if (sfixedRecentTradeDate != null){
                lastSubscriptionDate = sfixedRecentTradeDate;
            }
        }
    }

    @Data
    @ApiModel(value="资产配置信息", description="资产配置信息")
    public static class HoldAsset {
        @ApiModelProperty(value = "银行理财持仓")
        private String curAmt;//银行理财持仓 cur_amt
        @ApiModelProperty(value = "小额类固收")
        private String axyCurrAmount;//小额类固收   axy_curr_amount
        @ApiModelProperty(value = "大额类固收")
        private String sfixedCurrAmount;//大额类固收 sfixed_curr_amount   1  double
        @ApiModelProperty(value = "保险持仓")
        private String insuranceAum;//保险持仓 insurance_aum    1  double

        public List<AssetDesc> toHoldAssetDesc(){
            List<AssetDesc> assetDescList = new ArrayList<>();
            assetDescList.add(AssetDesc.builder().name("银行理财").value(formatAmt(curAmt)).field("curAmt").build());
            assetDescList.add(AssetDesc.builder().name("小额001").value(formatAmt(axyCurrAmount)).field("axyCurrAmount").build());
            assetDescList.add(AssetDesc.builder().name("大额001").value(formatAmt(sfixedCurrAmount)).field("sfixedCurrAmount").build());
            assetDescList.add(AssetDesc.builder().name("保险").value(formatAmt(insuranceAum)).field("insuranceAum").build());
            return assetDescList;
        }
    }

    private static String formatAmt(String amt){
        if (StringUtils.isBlank(amt)){
            return "0.0";
        }
        return amt;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    private static class AssetDesc{
        @ApiModelProperty(value = "资产名")
        private String name;
        @ApiModelProperty(value = "资产")
        private String value;
        @ApiModelProperty(value = "字段名")
        private String field;
    }

}


