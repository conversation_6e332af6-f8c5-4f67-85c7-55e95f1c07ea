package com.yirendai.workbench.vo.req;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("开始节点DTO")
public class CallcenterSaleProcessNodeStartReq extends CallcenterSaleProcessFlowReq {

    @NotNull(message = "节点ID不可为空")
    @ApiModelProperty(value = "节点ID", required = true)
    private String nodeId;
}
