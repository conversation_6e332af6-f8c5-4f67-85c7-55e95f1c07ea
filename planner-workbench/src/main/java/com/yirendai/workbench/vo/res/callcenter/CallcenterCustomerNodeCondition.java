package com.yirendai.workbench.vo.res.callcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户销售流程节点事项详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterCustomerNodeCondition对象", description="客户销售流程节点事项详情")
public class CallcenterCustomerNodeCondition implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "节点事项名称")
    private String conditionName;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime finishTime;

    @ApiModelProperty(value = "完成时间")
    private String finishTimeStr;

    @ApiModelProperty(value = "完成类型")
    private String finishType;

    @ApiModelProperty(value = "完成类型&附加信息")
    private String otherInfo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "完成人")
    private String operater;


}
