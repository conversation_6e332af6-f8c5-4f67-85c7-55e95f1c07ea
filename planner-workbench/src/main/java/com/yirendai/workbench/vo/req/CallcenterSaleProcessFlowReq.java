package com.yirendai.workbench.vo.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CallcenterSaleProcessFlowReq {

    @NotNull(message = "销售流程id不能为空")
    @ApiModelProperty(value = "销售流程id", required = true)
    private Long saleProcessId;

    @NotBlank(message = "流程图位置数据不可为空")
    @ApiModelProperty(value = "流程图位置数据", required = true)
    private String nodeText;
}
