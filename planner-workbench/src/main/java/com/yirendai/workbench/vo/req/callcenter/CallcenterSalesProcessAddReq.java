package com.yirendai.workbench.vo.req.callcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 销售流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterSalesProcess对象", description="销售流程表")
public class CallcenterSalesProcessAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    private String tenantId;

    @ApiModelProperty(value = "流程名称")
    @NotEmpty(message = "流程名称不能为空")
    private String processName;

    @ApiModelProperty(value = "产品类型")
    private Integer productType;

    @ApiModelProperty(value = "结点进度计算方式")
    private Integer nodeCalType;

    @ApiModelProperty(value = "流程版本状态")
    private Integer processStatus;

    @ApiModelProperty(value = "复制时，复制bean的id")
    private String copyId;

}
