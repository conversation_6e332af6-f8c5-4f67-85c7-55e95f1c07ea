package com.yirendai.workbench.vo.req;

import java.util.List;

import com.yirendai.workbench.enums.ConditionOperator;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2025/10/9 15:23
 **/
@Data
public class ComplexConditionData {

    @ApiModelProperty(value = "条件操作符 and or")
    private ConditionOperator operator;

    @ApiModelProperty(value = "递归支持嵌套条件")
    private List<UnifiedConditionReq> conditions;
}
