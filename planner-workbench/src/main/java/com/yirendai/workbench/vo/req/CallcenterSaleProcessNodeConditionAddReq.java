package com.yirendai.workbench.vo.req;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 销售流程节点条件配置表
 * </p>
 * <AUTHOR>
 * @since 2025-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CallcenterSaleProcessNodeCondition对象", description = "销售流程节点条件配置表")
public class CallcenterSaleProcessNodeConditionAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "节点id")
    private String nodeId;

    @ApiModelProperty(value = "条件项名称")
    private String name;

    @ApiModelProperty(value = "条件项进度值")
    private BigDecimal progressValue;

    @ApiModelProperty(value = "1 条件 2 条件组")
    private Integer type;

    @ApiModelProperty(value = "条件项达标规则")
    private UnifiedConditionReq conditions;

    @ApiModelProperty(value = "是否可见 1 可见 2 不可见")
    private Integer visible;
}
