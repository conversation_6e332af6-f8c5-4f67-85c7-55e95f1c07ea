package com.yirendai.workbench.vo.req;

import com.yirendai.workbench.enums.SimpleOperator;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2025/10/9 15:23
 **/
@Data
public class SimpleConditionData {

    @ApiModelProperty(value = "条件项映射id")
    private Long id;

    @ApiModelProperty(value = "简单条件项操作符")
    private SimpleOperator operator;

    @ApiModelProperty(value = "条件项值")
    private Object value;
}
