package com.yirendai.workbench.vo.res.callcenter;


import com.yirendai.workbench.vo.res.UserContactListRes;
import com.yirendai.workbench.vo.res.UserKycDetailRes;
import com.yirendai.workbench.vo.res.UserRemarkDetailRes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerBasicInfoVo {
    /**
     * 客户策略
     */
    private List<ReminderVo> reminderVoList;

    /**
     * 联系记录
     */
    private ContactRecord contactRecord;

    /**
     * 活动报名信息
     */
    private List<String> activityDetailList = new ArrayList<>();

    /**
     * 客户福利
     */
    private CustomerWelfare customerWelfare;

    /**
     * 客户基本信息及在持资产查询
     */
    private CustomerAndAssetResp customerAndAssetResp;

    /**
     * 账户信息
     */
    private List<UserContactListRes> userContactListResList = new ArrayList<>();

    /**
     * 客户备注
     */
    private UserRemarkDetailRes userRemarkDetailRes;

    /**
     * 客户KYC明细
     */
    private UserKycDetailRes  userKycDetailRes;
}
