package com.yirendai.workbench.vo.req;

import javax.validation.constraints.NotNull;

import com.yirendai.robot.modules.robot.dto.RobotFlowDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("修改边DTO")
public class CallcenterSaleProcessNodeEdgeUpdateReq extends CallcenterSaleProcessFlowReq {

    @NotNull(message = "边ID不可为空")
    @ApiModelProperty(value = "边ID", required = true)
    private Long id;

    @NotNull(message = "起始节点ID不可为空")
    @ApiModelProperty(value = "起始节点ID", required = true)
    private String fromNodeId;

    @NotNull(message = "目标节点ID不可为空")
    @ApiModelProperty(value = "目标节点ID", required = true)
    private String toNodeId;

    @NotNull(message = "是否连线不可为空")
    @ApiModelProperty(value = "是否连线，0=断开，1=连线", required = true)
    private Boolean isConnect;
}