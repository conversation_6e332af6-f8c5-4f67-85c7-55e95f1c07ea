package com.yirendai.workbench.vo.res;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 销售流程条件配置与接口映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterSaleProcessConditionApiMapping对象", description="销售流程条件配置与接口映射表")
public class CallcenterSaleProcessConditionApiMappingListRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "参数类型")
    private Integer paramType;

    @ApiModelProperty(value = "条件名称")
    private String name;

    @ApiModelProperty(value = "操作符")
    private List<ParamValue> operators;

    @ApiModelProperty(value = "参数值，选项需要")
    private List<ParamValue> paramValueList;

    @Data
    @AllArgsConstructor
    public static class ParamValue{
        private String code;
        private String name;
    }
}
