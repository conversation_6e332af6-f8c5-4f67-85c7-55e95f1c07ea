package com.yirendai.workbench.vo.res.callcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户销售流程进度记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterCustomerSaleProcessRecordResp对象", description="客户销售流程进度记录表")
public class CallcenterCustomerSaleProcessRecordResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "销售流程id")
    private Long saleProcessId;

    @ApiModelProperty(value = "销售流程编号")
    private String processNo;

    @ApiModelProperty(value = "销售流程名称")
    private String processName;

    @ApiModelProperty(value = "销售流程对应产品类型")
    private String productType;

    @ApiModelProperty(value = "当前正在进行中的节点ID")
    private String currentNodeId;

    @ApiModelProperty(value = "当前正在进行中的节点名称")
    private String nodeName;

    @ApiModelProperty(value = "进度值")
    private BigDecimal progressValue;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "最近更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "完成状态 0-进行中 1-已赢单 2-已败单")
    private Integer status;

    @ApiModelProperty(value = "开始时间")
    private String startTimeStr;

    @ApiModelProperty(value = "最近更新时间")
    private String updateTimeStr;

    @ApiModelProperty(value = "完成状态 1 完成 2 未完成")
    private String statusStr;


}
