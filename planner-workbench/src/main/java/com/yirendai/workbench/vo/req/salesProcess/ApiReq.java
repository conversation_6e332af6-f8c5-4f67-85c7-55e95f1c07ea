package com.yirendai.workbench.vo.req.salesProcess;

import java.time.LocalDateTime;

import com.yirendai.workbench.enums.SimpleOperator;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @time 2025/10/13 10:23
 **/
@Data
public class ApiReq {

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "统计开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "简单条件项操作符")
    private SimpleOperator operator;

    @ApiModelProperty(value = "条件项值")
    private String expectedValue;

}
