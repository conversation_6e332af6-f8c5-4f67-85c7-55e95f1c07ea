package com.yirendai.workbench.vo.res;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @time 2025/10/13 15:21
 **/
@Accessors(chain = true)
@Data
public class ApiRes {

    /**
     * 结果
     */
    private Boolean result;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
}
