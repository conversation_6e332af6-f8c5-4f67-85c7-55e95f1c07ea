package com.yirendai.workbench.vo.req.callcenter;

import com.yirendai.workbench.vo.req.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 销售流程列表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterSalesProcessListReq对象", description="销售流程列表查询")
public class CallcenterSalesProcessListReq  extends BasePageReq implements Serializable{

    private static final long serialVersionUID = 1L;

    private String tenantId;

    @ApiModelProperty(value = "流程名称")
    private String processName;

    @ApiModelProperty(value = "产品类型")
    private Integer productType;

}
