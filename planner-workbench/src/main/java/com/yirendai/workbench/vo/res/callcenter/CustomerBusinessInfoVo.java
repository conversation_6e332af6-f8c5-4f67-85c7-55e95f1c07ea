package com.yirendai.workbench.vo.res.callcenter;

import com.yirendai.workbench.wrapper.dto.life.SimpleWelfareOrderInfoVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerBusinessInfoVo {
    /**
     * 资产信息
     */
    private CustomerAssetChartResp customerAssetChartResp;

    /**
     * 小固订单列表
     */
    private List<XgOrderInfo> xgOrderInfoList = new ArrayList<>();

    /**
     * 大固订单列表
     */
    private List<DgOrderInfo> dgOrderInfoList = new ArrayList<>();

    /**
     * 奖励金
     */
    private List<RewardCashflowInfo> rewardCashflowInfoList = new ArrayList<>();

    /**
     * 宜人币
     */
    private List<YrbCashflowInfo> yrbCashflowInfoList = new ArrayList<>();

    /**
     * 福卡用户
     */
    private List<FcardInfoRes> fcardInfoResList = new ArrayList<>();

    /**
     * 福卡加息记录
     */
    private List<FcardRecordInfoRes> fcardRecordInfoResList = new ArrayList<>();

    /**
     * 一起赚
     */
    private YqzInvestPageRes yqzInvestPageRes;

    /**
     * D类
     */
    private List<Object> dTypeRecordList = new ArrayList<>();

    /**
     * 长险
     */
    private List<Object> longRecordList = new ArrayList<>();

    /**
     * 信美医疗&家财险
     */
    private List<Object> xinMeiRecordList = new ArrayList<>();

    /**
     * 小金罐2.0
     */
    private List<Object> xiaoJinGuanRecordList = new ArrayList<>();

    /**
     * 会员
     */
    private List<Object> memberRecordList = new ArrayList<>();

    /**
     * 生活商城订单
     */
    private List<SimpleWelfareOrderInfoVo> lifeMallOrderList = new ArrayList<>();

    /**
     * 逛逛商城订单
     */
    private List<SimpleWelfareOrderInfoVo> guangGuangMallOrderList =  new ArrayList<>();
}
