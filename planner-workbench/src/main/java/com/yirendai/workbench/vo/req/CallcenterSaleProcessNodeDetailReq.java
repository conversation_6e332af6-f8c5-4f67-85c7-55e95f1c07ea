package com.yirendai.workbench.vo.req;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 销售流程节点基础信息表
 * </p>
 * <AUTHOR>
 * @since 2025-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CallcenterSaleProcessNode对象", description = "销售流程节点基础信息表")
public class CallcenterSaleProcessNodeDetailReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;
}
