package com.yirendai.workbench.vo.req.callcenter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 包括画布内容text
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterSalesProcessSaveFlowReq对象", description="保存画布内容text")
public class CallcenterSalesProcessSaveFlowReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流程id")
    private String processId;

    @ApiModelProperty(value = "画布节点内容")
    private String nodeText;

}
