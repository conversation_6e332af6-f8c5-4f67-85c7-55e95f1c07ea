package com.yirendai.workbench.vo.res.salesProcess;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="客户通话概要统计", description="客户通话概要统计")
public class CustomerCallSummaryInfo {
    @ApiModelProperty(value = "通话数量")
    private Integer callTimes;
    @ApiModelProperty(value = "接通数量")
    private Integer answerTimes;
    @ApiModelProperty(value = "通话总时长")
    private Integer totalBillSec;
}
