package com.yirendai.workbench.vo.req.callcenter;

import com.yirendai.workbench.vo.req.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(value = "客户列表查询参数", description = "客户列表查询参数")
public class CustomerListReq extends BasePageReq {

    @ApiModelProperty(value = "员工编号")
    private String plannerId;
    @ApiModelProperty(value = "员工姓名")
    private String plannerName;
    @ApiModelProperty(value = "所属部门ID")
    private String plannerDeptId;
    @ApiModelProperty(value = "所属部门")
    private String userId;
    @ApiModelProperty(value = "客户姓名")
    private String userName;
    @ApiModelProperty(value = "客户类型 枚举：1:流量, 2:CRM")
    private String userType;
    @ApiModelProperty(value = "上次沟通时间，前含后含 格式：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastCommunicationTimeFrom;
    @ApiModelProperty(value = "上次沟通时间，前含后含 格式：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastCommunicationTimeTo;
    @ApiModelProperty(value = "一级状态码")
    private String visitParent;
    @ApiModelProperty(value = "二级状态码")
    private String visitType;
    @ApiModelProperty(value = "营销分类")
    private String isSale;
    @ApiModelProperty(value = "预约沟通时间， 格式：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appointmentTimeFrom;
    @ApiModelProperty(value = "预约沟通时间， 格式：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appointmentTimeTo;
    @ApiModelProperty(hidden = true)
    private List<String> plannerIds;

    @ApiModelProperty(hidden = true)
    private List<String> deptPlannerIds;

    @ApiModelProperty(hidden = true)
    private String tenantId;
    @ApiModelProperty("手机号")
    private String phone;
    @ApiModelProperty("跟进状态:1:跟进,2:未跟进")
    private Integer flowUpStatus;

    @ApiModelProperty("场景编号")
    private String sceneNo;


    @ApiModelProperty(value = "排序类型：1-上次沟通时间, 2-上次接通时间")
    private Integer sortType;

    @ApiModelProperty(value = "排序规则：ASC-正序, DESC-倒序")
    private String sortOrder;

}
