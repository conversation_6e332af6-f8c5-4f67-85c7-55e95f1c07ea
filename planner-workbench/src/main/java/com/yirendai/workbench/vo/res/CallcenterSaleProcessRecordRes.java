package com.yirendai.workbench.vo.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 销售流程进度记录表
 * </p>
 * <AUTHOR>
 * @since 2025-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CallcenterSaleProcessRecordRes对象", description = "用户销售流程进度")
public class CallcenterSaleProcessRecordRes {

    @ApiModelProperty(value = "客户id")
    private String userId;

    @ApiModelProperty(value = "员工编号")
    private String plannerId;

    @ApiModelProperty(value = "员工姓名")
    private String plannerName;

    @ApiModelProperty(value = "所属组别")
    private String groupName;

    @ApiModelProperty(value = "当前进度值")
    private String progressValue;

    @ApiModelProperty(value = "当前所属节点")
    private String currentNodeId;

    @ApiModelProperty(value = "销售流程id")
    private String saleProcessId;

    @ApiModelProperty(value = "销售流程版本号")
    private String versionNo;

    @ApiModelProperty(value = "当前所属节点名字")
    private String currentNodeName;

    @ApiModelProperty(value = "流程开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "流程结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "流程开始时间")
    private String startTimeStr;

    @ApiModelProperty(value = "流程结束时间")
    private String endTimeStr;

}
