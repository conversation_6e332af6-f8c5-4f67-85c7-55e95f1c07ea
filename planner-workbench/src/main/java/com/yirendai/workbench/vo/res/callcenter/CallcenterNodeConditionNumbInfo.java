package com.yirendai.workbench.vo.res.callcenter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 节点、事项数信息bean
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterNodeConditionNumbInfo对象", description="节点、事项数信息bean")
public class CallcenterNodeConditionNumbInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "node主键id")
    private String nodeId;

    @ApiModelProperty(value = "已完成项数")
    private Integer conditionNumb;


}
