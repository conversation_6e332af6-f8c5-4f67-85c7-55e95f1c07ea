package com.yirendai.workbench.vo.req.callcenter;

import com.yirendai.workbench.vo.req.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 用户销售流程列表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterUserSalesProcessReq对象", description="用户销售流程列表查询")
public class CallcenterUserSalesProcessReq extends BasePageReq implements Serializable{

    private static final long serialVersionUID = 1L;

    private String tenantId;

    @ApiModelProperty(value = "流程编号")
    @NotEmpty(message = "流程编号")
    private String processNo;

    @ApiModelProperty(value = "流程节点id")
    private Integer currentNodeId;

}
