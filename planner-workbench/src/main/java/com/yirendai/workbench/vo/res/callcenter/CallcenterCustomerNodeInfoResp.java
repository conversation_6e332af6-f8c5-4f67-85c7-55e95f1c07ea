package com.yirendai.workbench.vo.res.callcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 客户销售流程节点事项列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterCustomerNodeInfoResp对象", description="客户销售流程节点事项列表")
public class CallcenterCustomerNodeInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "节点id")
    private String id;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "节点待办事项")
    private List<CallcenterCustomerNodeCondition> unDoConditionList;

    @ApiModelProperty(value = "节点已完成事项")
    private List<CallcenterCustomerNodeCondition> doConditionList;


}
