package com.yirendai.workbench.vo.res;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 客户销售流程列表
 * </p>
 * <AUTHOR>
 * @since 2025-10-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CallCenterSalesProcessRecordExportRes对象", description = "客户销售流程列表")
public class CallCenterSalesProcessRecordExportRes implements Serializable {

    @ApiModelProperty(value = "客户id")
    @ExcelProperty(value = "客户id", order = 3)
    private String userId;

    @ApiModelProperty(value = "员工编号")
    @ExcelProperty(value = "员工编号", order = 4)
    private String plannerId;

    @ApiModelProperty(value = "员工姓名")
    @ExcelProperty(value = "员工姓名", order = 5)
    private String plannerName;

    @ApiModelProperty(value = "所属组别")
    @ExcelProperty(value = "所属组别", order = 6)
    private String groupName;

    @ApiModelProperty(value = "销售流程版本号")
    @ExcelProperty(value = "销售流程版本号", order = 7)
    private String versionNo;

    @ApiModelProperty(value = "当前进度值")
    @ExcelProperty(value = "当前进度值", order = 8)
    private String progressValue;

    @ApiModelProperty(value = "当前所属节点名字")
    @ExcelProperty(value = "当前所属节点名字", order = 9)
    private String currentNodeName;

    @ApiModelProperty(value = "流程开始时间")
    @ExcelProperty(value = "流程开始时间", order = 10)
    private String startTimeStr;

    @ApiModelProperty(value = "流程结束时间")
    @ExcelProperty(value = "流程结束时间", order = 11)
    private String endTimeStr;

}
