package com.yirendai.workbench.vo.req;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 手动推进条件项请求
 * <AUTHOR>
 * @time 2025/10/15 17:42
 */
@Data
@ApiModel(value = "手动推进条件项请求")
public class ManualAdvanceConditionReq {

    @ApiModelProperty(value = "流程ID", required = true)
    @NotNull(message = "流程ID不能为空")
    private Long processId;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @ApiModelProperty(value = "条件项列表", required = true)
    @NotEmpty(message = "条件项列表不能为空")
    private List<ConditionItemReq> conditionItems;

    /**
     * 条件项请求
     */
    @Data
    @ApiModel(value = "条件项请求")
    public static class ConditionItemReq {

        @ApiModelProperty(value = "条件ID", required = true)
        @NotNull(message = "条件ID不能为空")
        private Long conditionId;

        @ApiModelProperty(value = "条件备注")
        private String remark;

        @ApiModelProperty(value = "附件URL列表")
        private List<String> attachmentUrls;
    }

}
