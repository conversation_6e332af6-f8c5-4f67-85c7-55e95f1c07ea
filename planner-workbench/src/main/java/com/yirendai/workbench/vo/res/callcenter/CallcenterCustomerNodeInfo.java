package com.yirendai.workbench.vo.res.callcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 客户销售流程进度详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterCustomerSaleProcessInfoResp对象", description="客户销售流程进度详情")
public class CallcenterCustomerNodeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "node主键id")
    private String id;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "已完成项数")
    private Integer finishedConditionNumb;

    @ApiModelProperty(value = "总事项数")
    private Integer totalConditionNumb;

    @ApiModelProperty(value = "节点描述")
    private String nodeDesc;

    @ApiModelProperty(value = "是否是当前节点")
    private boolean isCurrentNode = false;

    @ApiModelProperty(value = "进入节点时间")
    private String nodeStartTimeStr;

    @ApiModelProperty(value = "完成节点时间")
    private String nodeEndTimeStr;

    @ApiModelProperty(value = "节点完成状态 1 完成 2 未完成")
    private Integer nodeStatus;


}
