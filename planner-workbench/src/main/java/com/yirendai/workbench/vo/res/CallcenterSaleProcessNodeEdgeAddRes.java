package com.yirendai.workbench.vo.res;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 销售流程节点边信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterSaleProcessNodeEdge对象", description="销售流程节点边信息表")
public class CallcenterSaleProcessNodeEdgeAddRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "销售流程id")
    private Long saleProcessId;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

    @ApiModelProperty(value = "起始节点id")
    private String fromNodeId;

    @ApiModelProperty(value = "目标节点id")
    private String toNodeId;

    @ApiModelProperty(value = "操作id 备用 支持多节点条件配置")
    private Long operationId;


}
