package com.yirendai.workbench.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2025/10/9 15:24
 **/
@Data
public class UnifiedConditionReq {

    @ApiModelProperty(value = "1 条件 2 条件组")
    private Integer type;

    @ApiModelProperty(value = "简单条件数据")
    // 简单条件数据
    private SimpleConditionData simpleCondition;

    @ApiModelProperty(value = "复杂条件数据")
    // 复杂条件数据
    private ComplexConditionData complexCondition;
}
