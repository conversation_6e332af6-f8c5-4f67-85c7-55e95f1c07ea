package com.yirendai.workbench.vo.res.callcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 客户销售流程进度详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterCustomerSaleProcessInfoResp对象", description="客户销售流程进度详情")
public class CallcenterCustomerSaleProcessInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "销售流程id")
    private Long saleProcessId;

    @ApiModelProperty(value = "销售流程名称")
    private String processName;

    @ApiModelProperty(value = "销售流程编号")
    private String processNo;

    @ApiModelProperty(value = "销售流程版本号")
    private String processVersion;

    @ApiModelProperty(value = "销售流程产品类型")
    private Integer productType;

    @ApiModelProperty(value = "进度值")
    private BigDecimal progressValue;

    @ApiModelProperty(value = "节点列表")
    private List<CallcenterCustomerNodeInfo> nodeInfoList;

    @ApiModelProperty(value = "手动结束原因")
    private String manualFinishReason;


}
