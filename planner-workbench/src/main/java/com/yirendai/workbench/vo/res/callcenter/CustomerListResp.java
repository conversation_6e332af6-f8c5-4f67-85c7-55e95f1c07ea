package com.yirendai.workbench.vo.res.callcenter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yirendai.workbench.config.annotation.SensitiveData;
import com.yirendai.workbench.enums.SensitiveType;
import com.yirendai.workbench.enums.callcenter.UserFollowUpStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "客户列表返回字段", description = "客户列表返回字段")
public class CustomerListResp {
    @ApiModelProperty(value = "员工编号")
    private String plannerId;
    @ApiModelProperty(value = "员工姓名")
    private String plannerName;
    @ApiModelProperty(value = "所属部门")
    private String plannerDept;
    @ApiModelProperty(value = "用户ID")
    private String userId;
    @ApiModelProperty(value = "客户姓名")
    private String userName;
    @ApiModelProperty(value = "客户类型 枚举：CRM/流量；")
    private String userType;
    @ApiModelProperty(value = "场景编码")
    private String sceneNo;

    @ApiModelProperty(value = "场景id")
    private Long sceneId;
    @ApiModelProperty(value = "上次接通时间")
    private LocalDateTime lastCallTime;
    @ApiModelProperty(value = "上次沟通时间")
    private LocalDateTime lastCommunicationTime;
    @ApiModelProperty(value = "一级状态码")
    private String visitParent;
    @ApiModelProperty(value = "二级状态码")
    private String visitType;
    @ApiModelProperty(value = "营销分类")
    private String isSale;
    @ApiModelProperty(value = "预约沟通时间")
    private LocalDateTime appointmentTime;

    @ApiModelProperty("手机号")
    @SensitiveData(type=SensitiveType.PHONE)
    private String phone;

    @ApiModelProperty(value = "跟进状态:1:未跟进,2:跟进", hidden = true)
    private Integer flowUpStatus;

    @ApiModelProperty("跟进状态")
    public String getFlowUpStatusText() {
        return UserFollowUpStatusEnum.getNameByCode(flowUpStatus);
    }

    @ApiModelProperty("开发周期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public LocalDateTime developStartTime;

    @ApiModelProperty("开发周期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public LocalDateTime developEndTime;

    @ApiModelProperty("锁定状态")
    private String lockType;

    @ApiModelProperty(value = "是否被锁定")
    private Boolean locked;

    /**
     * 以下取自crm接口，从标签库查询来
     */
    @ApiModelProperty(value = "用户分层")
    private String dxUserLevel;
    @ApiModelProperty(value = "在投AUM")
    private String totalAum;
    @ApiModelProperty(value = "最高在投金额")
    private String platformMaxAum;
    @ApiModelProperty(value = "钱包深度")
    private String kycWalletPredictResult;
    @ApiModelProperty(value = "是否添加微信")
    private String wechatFlag;
    @ApiModelProperty(value = "微信号")
    private String userWechatNum;


}
