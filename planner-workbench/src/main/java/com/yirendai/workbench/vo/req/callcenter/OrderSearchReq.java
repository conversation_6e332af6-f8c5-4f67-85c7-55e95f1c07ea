package com.yirendai.workbench.vo.req.callcenter;

import com.yirendai.workbench.vo.req.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value="订单列表查询req", description="订单列表查询req")
public class OrderSearchReq extends BasePageReq {

    @ApiModelProperty(value = "客户UID")
    private String customerUid;

    @ApiModelProperty(value = "交易中心单号")
    private String applyId;

    @ApiModelProperty(value = "订单状态 ")
    private String status;

    @ApiModelProperty(value = "奖励金类型 cashFlowType - r发奖，t税费扣除，w提现")
    private String type;

    @ApiModelProperty(value = "时间范围-开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "时间范围-结束时间")
    private LocalDateTime endTime;
}
