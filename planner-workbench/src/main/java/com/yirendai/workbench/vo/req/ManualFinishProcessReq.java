package com.yirendai.workbench.vo.req;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @time 2025/10/15 15:54
 **/
@Data
@ApiModel(value = "手动结束流程请求")
public class ManualFinishProcessReq {

    @ApiModelProperty(value = "流程ID", required = true)
    @NotNull(message = "流程ID不能为空")
    private Long processId;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    @ApiModelProperty(value = "完成类型：1-完成（已赢单），2-已败单", required = true)
    @NotNull(message = "完成类型不能为空")
    private Integer finishStatus;

    @ApiModelProperty(value = "手动结束原因", required = true)
    @NotBlank(message = "手动结束原因")
    private String manualFinishReason;
}
