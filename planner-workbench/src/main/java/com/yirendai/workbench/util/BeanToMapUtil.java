package com.yirendai.workbench.util;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Bean转LinkedHashMap工具类
 * 将Java Bean对象转换为LinkedHashMap<String, Object>，保持字段顺序
 * 
 * <AUTHOR>
 * @date 2024
 */
public class BeanToMapUtil {

    /**
     * 将Bean对象转换为LinkedHashMap
     * 
     * @param bean 要转换的Bean对象
     * @return LinkedHashMap<String, Object>，key为字段名，value为字段值
     */
    public static LinkedHashMap<String, Object> beanToMap(Object bean) {
        if (bean == null) {
            return new LinkedHashMap<>();
        }
        
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        Class<?> clazz = bean.getClass();
        
        // 获取所有字段，包括父类字段
        while (clazz != null && clazz != Object.class) {
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                try {
                    // 设置字段可访问
                    field.setAccessible(true);
                    
                    // 获取字段名
                    String fieldName = field.getName();
                    
                    // 获取字段值
                    Object fieldValue = field.get(bean);
                    
                    // 处理字段值
                    Object value = processFieldValue(fieldValue);
                    
                    map.put(fieldName, value);
                    
                } catch (IllegalAccessException e) {
                    // 如果无法访问字段，跳过该字段
                    System.err.println("无法访问字段: " + field.getName() + " - " + e.getMessage());
                }
            }
            
            // 获取父类
            clazz = clazz.getSuperclass();
        }
        
        return map;
    }
    
    /**
     * 处理字段值，处理null值和特殊类型
     * 
     * @param fieldValue 原始字段值
     * @return 处理后的值
     */
    private static Object processFieldValue(Object fieldValue) {
        if (fieldValue == null) {
            return null;
        }
        
        // 如果是基本类型或包装类型，直接返回
        if (isPrimitiveOrWrapper(fieldValue.getClass())) {
            return fieldValue;
        }
        
        // 如果是String类型，直接返回
        if (fieldValue instanceof String) {
            return fieldValue;
        }
        
        // 如果是其他复杂对象，可以递归转换（可选）
        // 这里为了简单起见，直接返回toString()
        return fieldValue.toString();
    }
    
    /**
     * 判断是否为基本类型或包装类型
     * 
     * @param clazz 类型
     * @return 是否为基本类型或包装类型
     */
    private static boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() || 
               clazz == Boolean.class || 
               clazz == Byte.class || 
               clazz == Character.class || 
               clazz == Short.class || 
               clazz == Integer.class || 
               clazz == Long.class || 
               clazz == Float.class || 
               clazz == Double.class;
    }
    
    /**
     * 将Bean对象转换为Map（不保持顺序）
     * 
     * @param bean 要转换的Bean对象
     * @return Map<String, Object>
     */
    public static Map<String, Object> beanToHashMap(Object bean) {
        return new LinkedHashMap<>(beanToMap(bean));
    }
    
    /**
     * 将Bean对象转换为Map，并过滤null值
     * 
     * @param bean 要转换的Bean对象
     * @param excludeNull 是否排除null值
     * @return LinkedHashMap<String, Object>
     */
    public static LinkedHashMap<String, Object> beanToMap(Object bean, boolean excludeNull) {
        LinkedHashMap<String, Object> map = beanToMap(bean);
        
        if (excludeNull) {
            LinkedHashMap<String, Object> filteredMap = new LinkedHashMap<>();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getValue() != null) {
                    filteredMap.put(entry.getKey(), entry.getValue());
                }
            }
            return filteredMap;
        }
        
        return map;
    }
}
