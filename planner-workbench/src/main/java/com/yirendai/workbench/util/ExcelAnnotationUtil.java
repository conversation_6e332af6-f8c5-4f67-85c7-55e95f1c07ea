package com.yirendai.workbench.util;

import com.alibaba.excel.annotation.ExcelProperty;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Excel注解工具类
 * 用于获取@ExcelProperty注解的配置值
 * 
 * <AUTHOR>
 * @date 2024
 */
public class ExcelAnnotationUtil {

    /**
     * 获取Bean类中所有字段的@ExcelProperty注解配置
     * 
     * @param clazz Bean类
     * @return LinkedHashMap<String, String>，key为字段名，value为@ExcelProperty的value值，按order排序
     */
    public static LinkedHashMap<String, String> getExcelPropertyMap(Class<?> clazz) {
        if (clazz == null) {
            return new LinkedHashMap<>();
        }
        
        // 收集所有字段信息，包括order值
        List<FieldInfo> fieldInfoList = new ArrayList<>();
        
        // 获取所有字段，包括父类字段
        while (clazz != null && clazz != Object.class) {
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                // 获取@ExcelProperty注解
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                
                if (excelProperty != null) {
                    String fieldName = field.getName();
                    String[] values = excelProperty.value();
                    int order = excelProperty.order();
                    
                    // 处理注解的value值
                    String propertyValue = processExcelPropertyValue(values);
                    
                    if (propertyValue != null && !propertyValue.trim().isEmpty()) {
                        fieldInfoList.add(new FieldInfo(fieldName, propertyValue, order));
                    }
                }
            }
            
            // 获取父类
            clazz = clazz.getSuperclass();
        }
        
        // 按order排序
        fieldInfoList.sort(Comparator.comparingInt(FieldInfo::getOrder));
        
        // 转换为LinkedHashMap
        LinkedHashMap<String, String> propertyMap = new LinkedHashMap<>();
        for (FieldInfo fieldInfo : fieldInfoList) {
            propertyMap.put(fieldInfo.getFieldName(), fieldInfo.getPropertyValue());
        }
        
        return propertyMap;
    }
    
    /**
     * 获取Bean对象中所有字段的@ExcelProperty注解配置
     * 
     * @param bean Bean对象
     * @return LinkedHashMap<String, String>，key为字段名，value为@ExcelProperty的value值
     */
    public static LinkedHashMap<String, String> getExcelPropertyMap(Object bean) {
        if (bean == null) {
            return new LinkedHashMap<>();
        }
        
        return getExcelPropertyMap(bean.getClass());
    }
    
    /**
     * 获取指定字段的@ExcelProperty注解配置
     * 
     * @param clazz Bean类
     * @param fieldName 字段名
     * @return @ExcelProperty的value值，如果没有注解或值为空则返回null
     */
    public static String getExcelPropertyValue(Class<?> clazz, String fieldName) {
        if (clazz == null || fieldName == null || fieldName.trim().isEmpty()) {
            return null;
        }
        
        try {
            Field field = clazz.getDeclaredField(fieldName);
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            
            if (excelProperty != null) {
                String[] values = excelProperty.value();
                return processExcelPropertyValue(values);
            }
        } catch (NoSuchFieldException e) {
            // 字段不存在，返回null
        }
        
        return null;
    }
    
    /**
     * 获取指定字段的@ExcelProperty注解配置
     * 
     * @param bean Bean对象
     * @param fieldName 字段名
     * @return @ExcelProperty的value值，如果没有注解或值为空则返回null
     */
    public static String getExcelPropertyValue(Object bean, String fieldName) {
        if (bean == null) {
            return null;
        }
        
        return getExcelPropertyValue(bean.getClass(), fieldName);
    }
    
    /**
     * 处理@ExcelProperty注解的value值
     * 
     * @param values 注解的value数组
     * @return 处理后的字符串值
     */
    private static String processExcelPropertyValue(String[] values) {
        if (values == null || values.length == 0) {
            return null;
        }
        
        // 如果只有一个值，直接返回
        if (values.length == 1) {
            return values[0];
        }
        
        // 如果有多个值，用逗号连接（可以根据需要调整）
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < values.length; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(values[i]);
        }
        
        return sb.toString();
    }
    
    /**
     * 获取Bean类中所有字段的@ExcelProperty注解配置，并过滤空值
     * 
     * @param clazz Bean类
     * @param excludeEmpty 是否排除空值
     * @return LinkedHashMap<String, String>，按order排序
     */
    public static LinkedHashMap<String, String> getExcelPropertyMap(Class<?> clazz, boolean excludeEmpty) {
        LinkedHashMap<String, String> propertyMap = getExcelPropertyMap(clazz);
        
        if (excludeEmpty) {
            LinkedHashMap<String, String> filteredMap = new LinkedHashMap<>();
            for (Map.Entry<String, String> entry : propertyMap.entrySet()) {
                String value = entry.getValue();
                if (value != null && !value.trim().isEmpty()) {
                    filteredMap.put(entry.getKey(), value);
                }
            }
            return filteredMap;
        }
        
        return propertyMap;
    }
    
    /**
     * 获取Bean对象中所有字段的@ExcelProperty注解配置，并过滤空值
     * 
     * @param bean Bean对象
     * @param excludeEmpty 是否排除空值
     * @return LinkedHashMap<String, String>，按order排序
     */
    public static LinkedHashMap<String, String> getExcelPropertyMap(Object bean, boolean excludeEmpty) {
        if (bean == null) {
            return new LinkedHashMap<>();
        }
        
        return getExcelPropertyMap(bean.getClass(), excludeEmpty);
    }
    
    /**
     * 检查字段是否有@ExcelProperty注解
     * 
     * @param clazz Bean类
     * @param fieldName 字段名
     * @return 是否有@ExcelProperty注解
     */
    public static boolean hasExcelProperty(Class<?> clazz, String fieldName) {
        if (clazz == null || fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }
        
        try {
            Field field = clazz.getDeclaredField(fieldName);
            return field.getAnnotation(ExcelProperty.class) != null;
        } catch (NoSuchFieldException e) {
            return false;
        }
    }
    
    /**
     * 检查字段是否有@ExcelProperty注解
     * 
     * @param bean Bean对象
     * @param fieldName 字段名
     * @return 是否有@ExcelProperty注解
     */
    public static boolean hasExcelProperty(Object bean, String fieldName) {
        if (bean == null) {
            return false;
        }
        
        return hasExcelProperty(bean.getClass(), fieldName);
    }
    
    /**
     * 字段信息内部类
     */
    private static class FieldInfo {
        private String fieldName;
        private String propertyValue;
        private int order;
        
        public FieldInfo(String fieldName, String propertyValue, int order) {
            this.fieldName = fieldName;
            this.propertyValue = propertyValue;
            this.order = order;
        }
        
        public String getFieldName() {
            return fieldName;
        }
        
        public String getPropertyValue() {
            return propertyValue;
        }
        
        public int getOrder() {
            return order;
        }
    }
}
