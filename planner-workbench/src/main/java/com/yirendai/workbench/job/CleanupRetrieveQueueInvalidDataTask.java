package com.yirendai.workbench.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.workbench.config.ThirdAccessProperty;
import com.yirendai.workbench.service.callcenter.RetrieveService;
import com.yirendai.workbench.util.OwnAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CleanupRetrieveQueueInvalidDataTask {

    @Autowired
    private RetrieveService retrieveService;

    @Resource
    private ThirdAccessProperty thirdAccessProperty;

    /**
     * 清理队列中无效的用户数据
     * 每天凌晨2点执行一次
     */
    @XxlJob("cleanupRetrieveQueueInvalidUsers")
    public void cleanupRetrieveQueueInvalidUsers() {
        try {
            log.info("Start cleaning up invalid users from queue");
            long startTime = System.currentTimeMillis();
            String tenantId = thirdAccessProperty.getTenantId().get("yiren");
            retrieveService.validateAndCleanupAllGroupQueues(tenantId);
            long endTime = System.currentTimeMillis();
            log.info("Finished cleaning up invalid users from queue, time taken: {}ms", endTime - startTime);
        } catch (Exception e) {
            log.error("Error while cleaning up invalid users from queue", e);
            throw e;
        }
    }
}