package com.yirendai.workbench.job;

import java.util.List;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.workbench.anno.LogTrace;
import com.yirendai.workbench.entity.CallcenterSalesProcess;
import com.yirendai.workbench.enums.callcenter.SalesProcessStatusEnum;
import com.yirendai.workbench.readonly.entity.PlannerUserBindReadonly;
import com.yirendai.workbench.readonly.service.IPlannerUserBindReadonlyService;
import com.yirendai.workbench.service.ICallcenterSaleProcessRecordService;
import com.yirendai.workbench.service.callcenter.CallcenterSalesProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR>
 * @time 2025/10/10 10:11
 **/
@Component
@Slf4j
@RestController
public class SaleProcessJob {

    @Resource
    ICallcenterSaleProcessRecordService iCallcenterSaleProcessRecordService;

    @Resource
    CallcenterSalesProcessService callcenterSalesProcessService;

    @Resource
    IPlannerUserBindReadonlyService iPlannerUserBindReadonlyService;

    @Resource
    private JedisCluster jedisCluster;

    // Redis锁的过期时间（秒）
    public static final int LOCK_EXPIRE_TIME = 300; // 5分钟

    // 锁前缀
    private static final String SALE_PROCESS_USER_LOCK_PREFIX = "sale_process_user_lock:";

    @XxlJob("saleProcessJob")
    @LogTrace
    @GetMapping("/saleProcess")
    public void saleProcess() {
        log.info("saleProcessJob start");
        //查询所有发布流程
        List<CallcenterSalesProcess> callcenterSalesProcessList = callcenterSalesProcessService.lambdaQuery()
                .eq(CallcenterSalesProcess::getProcessStatus, SalesProcessStatusEnum.PUBLISH.getCode())
                .orderByAsc(CallcenterSalesProcess::getId).list();
        if (CollUtil.isEmpty(callcenterSalesProcessList)) {
            log.info("saleProcessJob end");
            return;
        }

        for (CallcenterSalesProcess callcenterSalesProcess : callcenterSalesProcessList) {
            int id = 0;
            int limit = 100;
            while (true) {
                //查询所有理财师绑定客户（未完成相同流程编号的记录）
                List<PlannerUserBindReadonly> plannerUserBindList = iPlannerUserBindReadonlyService.selectUncompletedByProcessNo(
                        id, callcenterSalesProcess.getProcessNo(), limit);
                if (CollUtil.isEmpty(plannerUserBindList)) {
                    log.info("saleProcessJob end processId {}", callcenterSalesProcess.getId());
                    break; // 使用break而不是return，继续处理下一个流程
                }

                for (PlannerUserBindReadonly plannerUserBind : plannerUserBindList) {
                    processUserWithLock(callcenterSalesProcess, plannerUserBind);
                }

                // 更新id为当前批次的最后一个id
                id = plannerUserBindList.get(plannerUserBindList.size() - 1).getId();
            }
        }
    }

    /**
     * 为单个用户处理添加Redis锁
     */
    private void processUserWithLock(CallcenterSalesProcess callcenterSalesProcess,
            PlannerUserBindReadonly plannerUserBind) {
        log.info("开始处理用户: processId={}, processNo={}, userId={}, plannerName={}",
                callcenterSalesProcess.getId(),
                callcenterSalesProcess.getProcessNo(),
                plannerUserBind.getUserId(),
                plannerUserBind.getPlannerName());
        // 构建锁的key：流程ID + 用户ID
        String lockKey = getLockKey(callcenterSalesProcess.getId(), plannerUserBind.getUserId());
        try {
            SetParams setParams = new SetParams().nx().ex(LOCK_EXPIRE_TIME);
            String result = jedisCluster.set(lockKey, "1", setParams);

            if (!"OK".equals(result)) {
                log.info("锁已存在，跳过处理，processId: {}, userId: {}", callcenterSalesProcess.getId(),
                        plannerUserBind.getUserId());
                return;
            }

            // 执行业务逻辑
            iCallcenterSaleProcessRecordService.saleProcess(callcenterSalesProcess, plannerUserBind);

            log.info("用户处理完成，processId: {}, userId: {}", callcenterSalesProcess.getId(),
                    plannerUserBind.getUserId());
        } catch (Exception e) {
            log.error("saleProcessJob error processId {}, userId {}", callcenterSalesProcess.getId(),
                    plannerUserBind.getUserId(), e);
        } finally {
            // 直接删除锁
            try {
                jedisCluster.del(lockKey);
                log.info("释放锁成功，processId: {}, userId: {}", callcenterSalesProcess.getId(),
                        plannerUserBind.getUserId());
            } catch (Exception e) {
                log.error("释放Redis锁失败，lockKey: {}", lockKey, e);
            }
        }
    }

    public static String getLockKey(Long saleProcessId, String userId) {
        return SALE_PROCESS_USER_LOCK_PREFIX + saleProcessId + ":" + userId;
    }
}