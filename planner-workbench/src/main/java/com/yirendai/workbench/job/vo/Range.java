package com.yirendai.workbench.job.vo;

import io.swagger.annotations.ApiModelProperty; /**
 * 范围值对象，用于BETWEEN操作
 */
public class Range<T> {

    @ApiModelProperty(value = "最小值")
    private T min;

    @ApiModelProperty(value = "最大值")
    private T max;

    public Range() {}

    public Range(T min, T max) {
        this.min = min;
        this.max = max;
    }

    public T getMin() {
        return min;
    }

    public void setMin(T min) {
        this.min = min;
    }

    public T getMax() {
        return max;
    }

    public void setMax(T max) {
        this.max = max;
    }

    @Override
    public String toString() {
        return "[" + min + ", " + max + "]";
    }
}
