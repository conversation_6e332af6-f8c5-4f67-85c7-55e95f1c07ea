package com.yirendai.workbench.job.callcenter;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.workbench.anno.LogTrace;
import com.yirendai.workbench.entity.callcenter.UserDevelopInfo;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.callcenter.CallcenterUserService;
import com.yirendai.workbench.service.callcenter.ICallcenterSumupInfoService;
import com.yirendai.workbench.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 变更用户状态为跟进
 */
@Slf4j
@Component
public class UnFollowToFollowTask {

    @Autowired
    private CallcenterUserService callcenterUserService;
    @Autowired
    private ICallcenterSumupInfoService callcenterSumupInfoService;
    @Autowired
    private CallcenterCallRecordService callcenterCallRecordService;

    @LogTrace
    @XxlJob("unFollowToFollowTask")
    public void pullSceneTagUserTaskT0() {
        String traceId = LogUtil.initTrace();
        XxlJobHelper.log(traceId + "unFollowToFollowTask start");
        Long offsetId = null;
        String tenantId = XxlJobHelper.getJobParam();
        int total = 0;
        try {
            log.info("unFollowToFollowTask to change user  start :{}", tenantId);
            List<UserDevelopInfo> unFollowUpUserInfos;
            do {
                unFollowUpUserInfos = callcenterUserService.getUnFollowUserList(offsetId, tenantId, DEFAULT_BATCH_SIZE);
                if (CollectionUtils.isEmpty(unFollowUpUserInfos)){
                    break;
                }
                total += unFollowUpUserInfos.size();
                doChangeFollowUpState(unFollowUpUserInfos);
                offsetId = unFollowUpUserInfos.get(unFollowUpUserInfos.size() - 1).getId();
            }while (CollectionUtils.isNotEmpty(unFollowUpUserInfos) && unFollowUpUserInfos.size() >= DEFAULT_BATCH_SIZE);

            log.info("unFollowToFollowTask to change user  end total:{}", total);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.log(traceId + "unFollowToFollowTask error, error: " + e.getMessage());
            log.error("unFollowToFollowTask error", e);
            XxlJobHelper.handleFail();
        } finally {
            XxlJobHelper.log("unFollowToFollowTask end , total:{}", total);
            LogUtil.clearTrace();
        }
    }

    private void doChangeFollowUpState(List<UserDevelopInfo> unFollowUpUserInfos){
        for (UserDevelopInfo unFollowUpUserInfo : unFollowUpUserInfos) {
            log.info("unFollowToFollowTask to change user followstate , useid:{}, virtual id:{}", unFollowUpUserInfo.getUserId(), unFollowUpUserInfo.getVirtualUserId());
            boolean hasSumupWithFollow = callcenterSumupInfoService.hasSumupWithFollow(unFollowUpUserInfo.availUserId(), unFollowUpUserInfo.getDevelopStartTime(), unFollowUpUserInfo.getDevelopEndTime());
            if (!hasSumupWithFollow){
                log.info("unFollowToFollowTask to change user has no follow sumupIfo , useid:{}, virtual id:{}", unFollowUpUserInfo.getUserId(), unFollowUpUserInfo.getVirtualUserId());
                continue;
            }
            /*
            都满足：有“一级结束码=跟进”，累计接通通讯时长≥1分。 userId和virtualId都有的话，选userId进行更新
             */
            int rows = callcenterUserService.setCustomerToFollowUp(unFollowUpUserInfo.getUserId(),
                    StringUtils.isBlank(unFollowUpUserInfo.getUserId())? unFollowUpUserInfo.getVirtualUserId() : null, unFollowUpUserInfo.getTenantId());
            log.info("unFollowToFollowTask to change user  status success , useid:{}, virtual id:{},  affect rows:{}",
                    unFollowUpUserInfo.getUserId(), unFollowUpUserInfo.getVirtualUserId(), rows);
            if (rows > 1){
                log.info("unFollowToFollowTask to change user affect more than one rows useid:{}, virtual id:{}", unFollowUpUserInfo.getUserId(), unFollowUpUserInfo.getVirtualUserId());
            }

        }
    }

    private static final int DEFAULT_BATCH_SIZE = 100;
    private static final long CALL_AVAILABLE_DURATION_SECONDS = 60L;
}
