package com.yirendai.workbench.job.vo;

import com.yirendai.workbench.entity.CallcenterSaleProcessNode;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeRecord;
import com.yirendai.workbench.entity.CallcenterSaleProcessRecord;
import lombok.Data;

/**
 * 初始化流程结果
 */
@Data
public class InitProcessResult {

    private CallcenterSaleProcessNode node;
    private CallcenterSaleProcessRecord processRecord;
    private CallcenterSaleProcessNodeRecord nodeRecord;

    public static InitProcessResult create(
            CallcenterSaleProcessNode node,
            CallcenterSaleProcessRecord processRecord,
            CallcenterSaleProcessNodeRecord nodeRecord) {
        InitProcessResult result = new InitProcessResult();
        result.setNode(node);
        result.setProcessRecord(processRecord);
        result.setNodeRecord(nodeRecord);
        return result;
    }
}