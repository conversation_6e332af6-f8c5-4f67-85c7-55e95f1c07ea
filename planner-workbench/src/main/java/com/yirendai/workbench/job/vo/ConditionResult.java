package com.yirendai.workbench.job.vo;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 条件处理结果，包含结果和最晚完成时间
 */
public class ConditionResult {
    private boolean result;
    private String remark;
    private LocalDateTime latestCompleteTime;    // 最晚完成时间
    private List<LocalDateTime> allCompleteTimes; // 所有涉及的完成时间

    public ConditionResult(boolean result) {
        this.result = result;
        this.allCompleteTimes = new ArrayList<>();
    }

    public ConditionResult(boolean result, String remark, LocalDateTime completeTime) {
        this.result = result;
        setRemark(remark);
        this.latestCompleteTime = completeTime;
        this.allCompleteTimes = new ArrayList<>();
        if (completeTime != null) {
            this.allCompleteTimes.add(completeTime);
        }
    }

    public boolean isResult() {
        return result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    public LocalDateTime getLatestCompleteTime() {
        return latestCompleteTime;
    }

    public void setLatestCompleteTime(LocalDateTime latestCompleteTime) {
        this.latestCompleteTime = latestCompleteTime;
    }

    public List<LocalDateTime> getAllCompleteTimes() {
        return allCompleteTimes;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        if (this.remark != null) {
            this.remark += "|" + remark;
        }else {
            this.remark = remark;
        }
    }

    public void addCompleteTime(LocalDateTime time) {
        if (time != null) {
            this.allCompleteTimes.add(time);
            updateLatestTime(time);
        }
    }

    /**
     * 合并其他结果的完成时间
     */
    public void mergeCompleteTimes(ConditionResult other) {
        if (other != null && other.getAllCompleteTimes() != null) {
            for (LocalDateTime time : other.getAllCompleteTimes()) {
                addCompleteTime(time);
            }
        }
    }

    /**
     * 更新最晚时间
     */
    private void updateLatestTime(LocalDateTime time) {
        if (latestCompleteTime == null || time.isAfter(latestCompleteTime)) {
            latestCompleteTime = time;
        }
    }

    @Override
    public String toString() {
        return "ConditionResult{" +
                "result=" + result +
                ", latestCompleteTime=" + latestCompleteTime +
                ", totalTimes=" + allCompleteTimes.size() +
                '}';
    }
}
