package com.yirendai.workbench.job.vo;

/**
 * <AUTHOR>
 * @time 2025/10/10 17:57
 **/
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 值与完成时间的包装类
 */
public class ValueWithTime {
    private Object value;           // 实际值
    private LocalDateTime completeTime;  // 完成时间

    public ValueWithTime() {}

    public ValueWithTime(Object value, LocalDateTime completeTime) {
        this.value = value;
        this.completeTime = completeTime;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public LocalDateTime getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(LocalDateTime completeTime) {
        this.completeTime = completeTime;
    }

    @Override
    public String toString() {
        return "ValueWithTime{" +
                "value=" + value +
                ", completeTime=" + completeTime +
                '}';
    }
}
