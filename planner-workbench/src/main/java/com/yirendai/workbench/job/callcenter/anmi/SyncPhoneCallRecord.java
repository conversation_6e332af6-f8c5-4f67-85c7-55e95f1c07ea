package com.yirendai.workbench.job.callcenter.anmi;

import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yirendai.voiceaiserver.enums.PromptSceneEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.service.AiPromptRecordService;
import com.yirendai.voiceaiserver.service.CallCenterService;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.voiceaiserver.vo.response.AiPromptRecordVo;
import com.yirendai.workbench.anno.LogTrace;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.enums.callcenter.anmi.AnmiConstants;
import com.yirendai.workbench.service.CallcenterCallRecordService;
import com.yirendai.workbench.service.callcenter.AnMiCallService;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.wrapper.AnmiApiWrapper;
import com.yirendai.workbench.wrapper.dto.anmi.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.yirendai.workbench.service.callcenter.impl.MakeCallServiceImpl.UUID_PREFIX;


@Slf4j
@Component
public class SyncPhoneCallRecord {

    @Autowired
    private AnmiApiWrapper anmiApiWrapper;
    @Resource
    private JedisCluster jedisCluster;
    @Autowired
    private AnMiCallService anMiCallService;

    @Autowired
    private CallcenterCallRecordService callcenterCallRecordService;
    @Autowired
    private CallCenterService callCenterService;
    @Autowired
    private AiPromptRecordService aiPromptRecordService;
    @Autowired
    private AIConversationProcessService aiConversationProcessService;

    @LogTrace
    @XxlJob("syncPhoneCallRecord")
    public void syncPhoneCallRecord() {
        log.info("syncPhoneCallRecord start");
        try {
            /*
            默认查询前10分钟的
             */
            SyncPhoneCallRecordParam syncPhoneCallRecordParam =  getSyncParam();
            doSyncPhoneCallRecord(syncPhoneCallRecordParam);
            /*
            再同步1小时前
             */
            syncPhoneCallRecordParam.setSpecifyParam(true);
            syncPhoneCallRecordParam.setStartTime(syncPhoneCallRecordParam.getStartTime().minusHours(1));
            doSyncPhoneCallRecord(syncPhoneCallRecordParam);
            /*
            再同步2小时前
             */
            syncPhoneCallRecordParam.setStartTime(syncPhoneCallRecordParam.getStartTime().minusHours(2));
            doSyncPhoneCallRecord(syncPhoneCallRecordParam);
        } catch (Exception e) {
            log.error("syncPhoneCallRecord error", e);
        }
        log.info("syncPhoneCallRecord end");
    }

    private void doSyncPhoneCallRecord(SyncPhoneCallRecordParam syncPhoneCallRecordParam) {
        try {
            log.info("syncPhoneCallRecordParam: {}", JsonUtilExt.beanToJson(syncPhoneCallRecordParam));
            long operationTimeStart = syncPhoneCallRecordParam.startTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
            long operationTimeEnd = operationTimeStart + 1000L * 60 * 10;
            AnmiCallResponse<AnmiPageResponse<AnmiCallRecordResponse>>  anmiCallRecords = null;
            int page = 1;
            do {
                AnmiCallRecordRequest anmiCallRecordRequest = AnmiCallRecordRequest.builder()
                        .operationTimeStart(operationTimeStart).operationTimeEnd(operationTimeEnd)
                        .limit(MAX_PAGE_SIZE).page(page).userNo(syncPhoneCallRecordParam.userNo).build();
                anmiCallRecords = anmiApiWrapper.getPhoneCallRecord(anmiCallRecordRequest, syncPhoneCallRecordParam.getTenantId());
                if (anmiCallRecords == null || anmiCallRecords.getData() == null||anmiCallRecords.getData().getPages()<page){
                    break;
                }
                page++;
                List<AnmiCallRecordCallBackInfo> anmiCallRecordCallBackInfos = anmiCallRecords.getData().getList().stream().map(record -> {
                    AnmiCallRecordCallBackInfo anmiCallRecordCallBackInfo = new AnmiCallRecordCallBackInfo();
                    BeanUtils.copyProperties(record, anmiCallRecordCallBackInfo);
                    anmiCallRecordCallBackInfo.setCallDuration(record.getDuration() == null? null : record.getDuration().longValue());
                    anmiCallRecordCallBackInfo.setPickupTime(record.getCallTime() == null? null : record.getCallTime());
                    //单位是秒
                    anmiCallRecordCallBackInfo.setRingDuration(record.getRingDuration() == null? null : record.getRingDuration().intValue());
                    return anmiCallRecordCallBackInfo;
                }).collect(Collectors.toList());
                List<CallcenterCallRecord> callcenterCallRecords = anMiCallService.workPhoneCallRecordCallBack(anmiCallRecordCallBackInfos, syncPhoneCallRecordParam.getTenantId());
                setAiConversationProcess(callcenterCallRecords);
            }while (CollectionUtils.isNotEmpty(anmiCallRecords.getData().getList())
                    && anmiCallRecords.getData().getList().size() >= MAX_PAGE_SIZE);

            if (!syncPhoneCallRecordParam.isSpecifyParam){
                recordLastSyncTime(operationTimeEnd);
            }
        } catch (Exception e) {
            log.error("syncPhoneCallRecord error", e);
        }
    }
    /**
     * 音转文，并做呼叫小结
     * @param callcenterCallRecords
     */
    private void setAiConversationProcess(List<CallcenterCallRecord> callcenterCallRecords ){
        if (CollectionUtils.isEmpty(callcenterCallRecords)){
            return;
        }
        for (CallcenterCallRecord record : callcenterCallRecords) {
            try {
                if (record.getCsBillsec() == null || record.getCsBillsec() < 1 || StringUtils.isBlank(record.getRecordUrl())){
                    continue;
                }
                if (shouldPassThisRecord(record)){
                    continue;
                }

                AnmiConstants.AnmiDeviceType anmiDeviceType = AnmiConstants.AnmiDeviceType.getByModel(record.getDeviceModel());
                AiPlannerChatContact chatContact
                        = callCenterService.syncChat(record, Long.valueOf(1L).compareTo(record.getCsBillsec()) > 0 ? "[新呼叫中心语音]" : null, anmiDeviceType, null);
                callCenterService.syncPlannerUser(record);
                if (Long.valueOf(0L).compareTo(record.getCsBillsec()) < 0) {
                    AiPromptRecordVo config = aiPromptRecordService.getLatestPromptRecordByScene(PromptSceneEnum.CALL_CENTER_TRANSLATE.getCode());

                    callCenterService.handleComplete(chatContact, config, record.getCsRing(), record.getCsDuration(), anmiDeviceType);
                } else {
                    aiConversationProcessService.submitMQTasks(AiConversationProcessReq.builder().chatContactId(chatContact.getId()).build());
                }
            }catch (DuplicateKeyException duplicateKeyException){
                log.error("安米同步通话记录, 音转文已存在，ai_planner_chat_contact  uuid={}", record.getUuid());
            }catch (Exception e){
                log.error("安米同步通话记录, 音转文失败，record:{} 失败原因", record, e);
            }catch (Error error){
                log.error("安米同步通话记录, 音转文失败，record:{} 错误原因", record, error);
            }
        }
    }

    /**
     * uuid不是语盟系统生成的，判断是否已经存在了相同call_uuid的 系统发起的呼叫
     */
    private boolean shouldPassThisRecord(CallcenterCallRecord record){
        if (record.getUuid().startsWith(UUID_PREFIX)){
            return false;
        }

        List<CallcenterCallRecord> sameCallUuidRecord = callcenterCallRecordService.lambdaQuery()
                .eq(CallcenterCallRecord::getPlannerId, record.getPlannerId())
                .eq(CallcenterCallRecord::getCustomerUid, record.getCustomerUid())
                .eq(CallcenterCallRecord::getCallUuid, record.getCallUuid())
                .ne(CallcenterCallRecord::getUuid, record.getUuid()).list();
        return CollectionUtils.isNotEmpty(sameCallUuidRecord);
    }

    private SyncPhoneCallRecordParam getSyncParam() {
        String params = XxlJobHelper.getJobParam();
        log.info("syncPhoneCallRecord params:{}", params);
        SyncPhoneCallRecordParam param = JsonUtilExt.jsonToBean(params, SyncPhoneCallRecordParam.class);
        if (param.getStartTime() == null){
            param.startTime = getLastSyncTime();
            param.isSpecifyParam = false;
        }else {
            param.isSpecifyParam = true;
        }
        return param;
    }

    private LocalDateTime getLastSyncTime() {
        LocalDateTime defaultStartTime = LocalDateTime.now().minusMinutes(11);
        String lastSyncTime = jedisCluster.get(SYNC_PHONE_TIME_KEY);
        if (lastSyncTime == null){
            return defaultStartTime;
        }
        Long lastSyncTimeLong = Long.parseLong(lastSyncTime);
        LocalDateTime savedDate = DateUtil.toLocalDateTime(new Date(lastSyncTimeLong));

        return defaultStartTime.isAfter(savedDate)? savedDate : defaultStartTime;
    }

    private void recordLastSyncTime(long lastSyncTime) {
        jedisCluster.setex(SYNC_PHONE_TIME_KEY, 3600*24, lastSyncTime+"");
    }

    private static final String SYNC_PHONE_TIME_KEY = "anmi_sync_phone_time";
    private static final int MAX_PAGE_SIZE = 200;
    @Data
    private static class SyncPhoneCallRecordParam{
        private LocalDateTime startTime;
        @ApiModelProperty(value = "员⼯编号")
        private String userNo;
        /**
         * 是否指定参数
         */
        private boolean isSpecifyParam;
        private String tenantId;
    }
}
