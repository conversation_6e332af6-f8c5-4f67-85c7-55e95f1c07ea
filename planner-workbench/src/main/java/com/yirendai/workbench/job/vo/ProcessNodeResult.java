package com.yirendai.workbench.job.vo;

import com.yirendai.workbench.entity.CallcenterSaleProcessNode;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeRecord;
import lombok.Data;

/**
 * 节点处理结果
 */
@Data
public class ProcessNodeResult {

    /**
     * 是否继续处理下一个节点
     */
    private boolean shouldContinue;

    /**
     * 下一个要处理的节点
     */
    private CallcenterSaleProcessNode nextNode;

    /**
     * 下一个节点的记录
     */
    private CallcenterSaleProcessNodeRecord nextNodeRecord;

    /**
     * 创建停止处理的结果
     */
    public static ProcessNodeResult stop() {
        ProcessNodeResult result = new ProcessNodeResult();
        result.setShouldContinue(false);
        return result;
    }

    /**
     * 创建继续处理的结果
     */
    public static ProcessNodeResult continueWith(
            CallcenterSaleProcessNode node,
            CallcenterSaleProcessNodeRecord record) {
        ProcessNodeResult result = new ProcessNodeResult();
        result.setShouldContinue(true);
        result.setNextNode(node);
        result.setNextNodeRecord(record);
        return result;
    }
}
