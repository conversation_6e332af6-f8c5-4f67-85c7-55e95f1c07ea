package com.yirendai.workbench.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.vo.request.AiAnalysisTaskReq;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskVO;
import com.yirendai.workbench.entity.AiAnalysisTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * ai分析任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface AiAnalysisTaskMapper extends BaseMapper<AiAnalysisTask> {

    /**
     * 根据id和用户id查询
     * @param req
     * @return
     */
    AiAnalysisTask selectByIdAndUserId(@Param("req") AiAnalysisTaskReq req);

    IPage<AiAnalysisTaskVO> aiAnalysisTaskPageList(@Param("page") IPage<AiAnalysisTaskVO> page, @Param("req") AiAnalysisTaskReq aiAnalysisTaskReq);

    List<String> selectTenantId();

    /**
     * 根据租户id查询每个用户一条数据
     * @param tenantId
     * @return
     */
    List<AiAnalysisTask> selectByTenantIdGroupByUser(String tenantId);
}
