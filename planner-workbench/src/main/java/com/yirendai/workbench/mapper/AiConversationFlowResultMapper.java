package com.yirendai.workbench.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.workbench.entity.AiConversationFlowResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI对话流程结果表(按节点维度) Mapper 接口
 */
public interface AiConversationFlowResultMapper extends BaseMapper<AiConversationFlowResult> {

    /**
     * 根据来源类型和来源ID查询结果
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @param tenantId 租户ID
     * @return 结果列表
     */
    List<AiConversationFlowResult> selectBySource(@Param("sourceType") String sourceType,
                                                  @Param("sourceId") Long sourceId,
                                                  @Param("tenantId") String tenantId);

    /**
     * 批量插入结果
     * @param list 结果列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<AiConversationFlowResult> list);

    /**
     * 查询指定用户节点在起始时间后的首条触达记录
     */
    AiConversationFlowResult selectReachedResult(@Param("userId") String userId,
                                                 @Param("nodeName") String nodeName,
                                                 @Param("startTime") java.time.LocalDateTime startTime);
}
