package com.yirendai.workbench.mapper;

import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskResultVO;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskSceneCountVO;
import com.yirendai.voiceaiserver.vo.response.AiAnalysisTaskSceneResultCountVO;
import com.yirendai.workbench.entity.AiAnalysisTaskResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * ai分析任务通话记录分析结果表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface AiAnalysisTaskResultMapper extends BaseMapper<AiAnalysisTaskResult> {

    /**
     * 查询场景数量
     * @param taskId
     * @return
     */
    List<AiAnalysisTaskSceneCountVO> selectSceneCount(Long taskId);

    /**
     * 查询场景结果数量
     * @param taskId
     * @param sceneId
     * @return
     */
    List<AiAnalysisTaskSceneResultCountVO> selectSceneResultCount(Long taskId,Long sceneId);

    /**
     * 根据通话记录ID和可选场景/结果ID筛选分析结果
     */
    List<AiAnalysisTaskResultVO> listByCallRecordIdAndConditions(
            @Param("callRecordId") Long callRecordId,
            @Param("sceneIds") List<Long> sceneIds,
            @Param("resultIds") List<Long> resultIds
    );

    /**
     * 根据母表ID集合查询分析结果
     */
    List<AiAnalysisTaskResultVO> listByDataIds(@Param("dataIds") List<Long> dataIds);

    /**
     * 根据通话记录ID查询分析结果
     * @param callRecordId
     * @param statusList
     * @return
     */
    List<AiAnalysisTaskResult> listByCallRecordId(Long callRecordId ,List<Integer> statusList);
}
