package com.yirendai.workbench.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.workbench.entity.AiAnalysisTaskData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * ai分析任务数据母表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface AiAnalysisTaskDataMapper extends BaseMapper<AiAnalysisTaskData> {

    /**
     * 根据任务ID和状态查询数据记录（Top100）
     *
     * @param analysisTaskId 任务ID
     * @param statusList     状态列表
     * @return 数据记录列表
     */
    List<AiAnalysisTaskData> selectTop100ByTaskId(@Param("analysisTaskId") Long analysisTaskId,
                                                   @Param("statusList") List<Integer> statusList);

    /**
     * 统计任务的数据总数
     *
     * @param analysisTaskId 任务ID
     * @return 总数
     */
    Integer countByTaskId(@Param("analysisTaskId") Long analysisTaskId);

    /**
     * 统计任务下的去重客户数量
     */
    Integer countDistinctCustomerByTaskId(@Param("analysisTaskId") Long analysisTaskId);
}
