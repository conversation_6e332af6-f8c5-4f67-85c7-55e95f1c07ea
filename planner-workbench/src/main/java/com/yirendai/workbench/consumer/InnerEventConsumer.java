package com.yirendai.workbench.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yirendai.workbench.consumer.bean.CallCenterUserChangeEvent;
import com.yirendai.workbench.consumer.bean.CallCenterWechatMessageChangeEvent;
import com.yirendai.workbench.consumer.bean.WechatContactUpdateEvent;
import com.yirendai.workbench.consumer.bean.WechatMessageBizRecordEvent;
import com.yirendai.workbench.entity.PlannerUserBind;
import com.yirendai.workbench.entity.callcenter.CallcenterUser;
import com.yirendai.workbench.entity.callcenter.CallcenterUserHis;
import com.yirendai.workbench.service.ICallCenterWechatMessageService;
import com.yirendai.workbench.service.IPlannerUserBindService;
import com.yirendai.workbench.service.callcenter.CallcenterUserHisService;
import com.yirendai.workbench.service.callcenter.CallcenterUserService;
import com.yirendai.workbench.service.callcenter.ICallcenterWechatCustomerMappingService;
import com.yirendai.workbench.service.callcenter.IWechatMessageBizRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toMap;


/**
 * 处理内部时间
 * author zly
 * time 2025/2/20 10:24
 */
@Component
@Slf4j
public class InnerEventConsumer {

    @Resource
    private CallcenterUserHisService callcenterUserHisService;

    @Resource
    private CallcenterUserService callcenterUserService;

    @Resource
    private IPlannerUserBindService plannerUserBindService;

    @Resource
    private JedisCluster jedisCluster;

    @Resource
    private ICallCenterWechatMessageService callCenterWechatMessageService;

    @Resource
    private ICallcenterWechatCustomerMappingService wechatCustomerMappingService;

    @Resource
    private IWechatMessageBizRecordService wechatMessageBizRecordService;

    public static final String CALL_CENTER_USER_HIS_CREATE_REDIS_KEY = "workbench-service:event:consumer:%s";

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void receiveCallCenterUserChangeEvent(CallCenterUserChangeEvent event) throws InterruptedException {
        log.info("event: {}", JSON.toJSONString(event));
        if (CollectionUtils.isEmpty(event.getCallCenterUserId())) {
            return;
        }
        List<CallcenterUser> callCenterUsers = callcenterUserService.listByIds(event.getCallCenterUserId());
        if (CollectionUtils.isEmpty(callCenterUsers)) {
            return;
        }
        Long userId = Lists.newArrayList(event.getCallCenterUserId()).get(0);
        String lockE = event.getTransactionId() != null ? event.getTransactionId() + "-" + userId : userId + "";
        log.info("lockE: {}", lockE);
        String redisKey = String.format(CALL_CENTER_USER_HIS_CREATE_REDIS_KEY, lockE);
        if (jedisCluster.exists(redisKey)) {
            log.info("receiveCallCenterUserChangeEvent 重复消费，event:{}", event);
            return;
        }
        List<CallcenterUserHis> hisList = callCenterUsers.stream().map(e -> {
            CallcenterUserHis newCallcenterUserHis = new CallcenterUserHis();
            BeanUtils.copyProperties(e, newCallcenterUserHis);
            newCallcenterUserHis.setInboundTime(e.getCreateTime());
            newCallcenterUserHis.setCallcenterUserId(e.getId());
            return newCallcenterUserHis;
        }).collect(Collectors.toList());

        List<String> userIds = hisList.stream().map(CallcenterUserHis::getUserId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> virtualUserId = hisList.stream().map(CallcenterUserHis::getVirtualUserId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        userIds.addAll(virtualUserId);
        List<PlannerUserBind> userBinds = plannerUserBindService.lambdaQuery().in(PlannerUserBind::getUserId, userIds).list();
        Map<String, PlannerUserBind> bindMap = Optional.ofNullable(userBinds).map(Collection::stream).orElseGet(Stream::empty)
                .collect(toMap(PlannerUserBind::getUserId, e -> e, (v1, v2) -> v1));

        for (CallcenterUserHis callcenterUserHis : hisList) {
            PlannerUserBind bind = bindMap.containsKey(callcenterUserHis.getUserId()) ? bindMap.get(callcenterUserHis.getUserId()) : bindMap.get(callcenterUserHis.getVirtualUserId());
            if (null != bind) {
                callcenterUserHis.setPlannerId(bind.getPlannerId());
                callcenterUserHis.setBindTime(bind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
        }

        callcenterUserHisService.saveBatch(hisList);
        jedisCluster.set(redisKey, "true");
        jedisCluster.expire(redisKey, 2);
    }


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void receiveCallCenterMessageChangeEvent(CallCenterWechatMessageChangeEvent event) {
        log.info("event: {}", JSON.toJSONString(event));
        callCenterWechatMessageService.handlerVoiceToText(event.getCallCenterWechatMessage());
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void receiveWechatContactUpdateChangeEvent(WechatContactUpdateEvent event) {
        log.info("event: {}", JSON.toJSONString(event));
        wechatCustomerMappingService.receiveWechatContactUpdateChangeEvent(event.getWechatContacts(), event.getTenantId());
    }

    /**
     * 处理微信消息业务记录事件
     * 在事务提交后异步处理，解析消息内容并保存到业务记录表
     */
    @EventListener()
    public void receiveWechatMessageBizRecordEvent(WechatMessageBizRecordEvent event) {
        log.info("收到微信消息业务记录事件: plannerNo={}, userId={}, source={}", event.getPlannerNo(), event.getUserIds(), event.getSource());
        try {
            wechatMessageBizRecordService.handleWechatMessageBizRecordEvent(event);
        } catch (Exception e) {
            log.error("处理微信消息业务记录事件失败", e);
        }
    }

}
