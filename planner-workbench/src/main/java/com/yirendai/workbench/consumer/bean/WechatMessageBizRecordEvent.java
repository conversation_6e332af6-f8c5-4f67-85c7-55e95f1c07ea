package com.yirendai.workbench.consumer.bean;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;

import java.util.Set;

/**
 * 微信消息业务记录事件
 * 用于在聊天消息插入时触发业务解析
 */
@Slf4j
@Getter
@Setter
@Accessors(chain = true)
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class WechatMessageBizRecordEvent extends ApplicationEvent {

    /**
     * 微信消息ID
     */
    private Long messageId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 理财师工号
     */
    private String plannerNo;

    /**
     * 客户ID
     */
    private Set<String> userIds;

    /**
     * 来源：1-企微，2-个微
     */
    private Integer source;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 业务类型（可选，根据业务规则判断）
     */
    private Integer bizType;

    public WechatMessageBizRecordEvent(Object source) {
        super(source);
    }
}
