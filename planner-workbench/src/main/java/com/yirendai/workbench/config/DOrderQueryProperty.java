package com.yirendai.workbench.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 宜享花呼叫服务参数配置
 */
@Data
@Configuration
@RefreshScope
public class DOrderQueryProperty {
    @Value("${dOrder.url.endpoint:http://dc-business-server-test.dcbx.paas.test}")
    private String dOrderEndPoint;
    @Value("${dOrder.url.queryUser:yr-sale-process}")
    private String dOrderQueryUser;


}
