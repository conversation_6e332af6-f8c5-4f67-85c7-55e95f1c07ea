package com.yirendai.workbench.config;

import javax.sql.DataSource;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.yirendai.workbench.handler.LocalDateTimeTypeHandler;
import com.yirendai.workbench.handler.LocalDateTypeHandler;
import com.yirendai.workbench.handler.LocalTimeTypeHandler;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandler;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

/**
 * CRM只读备库数据源配置
 * 用于查询操作，减轻主库压力
 * 
 * <AUTHOR>
 * @time 2025/01/27
 **/
@Configuration
@MapperScan(basePackages = {
        "com.yirendai.workbench.readonly.mapper"}, sqlSessionTemplateRef = "crmReadOnlySqlSessionTemplate")
public class CrmReadOnlyDataSourceConfig {

    @Bean(name = "crmReadOnlyHikariConfig")
    @ConfigurationProperties(prefix = "spring.datasource.crm-readonly")
    public HikariConfig crmReadOnlyHikariConfig(){
        HikariConfig config = new HikariConfig();
        // 设置只读属性到数据源属性中
        config.addDataSourceProperty("readOnly", "true");
        return config;
    }

    /**
     * CRM只读数据源
     * @return DataSource
     */
    @Bean(name = "crmReadOnlyDataSource")
    public DataSource crmReadOnlyDataSource(@Qualifier("crmReadOnlyHikariConfig") HikariConfig hikariConfig) {
        // 在创建HikariDataSource之前设置只读属性
        hikariConfig.setReadOnly(true);
        return new HikariDataSource(hikariConfig);
    }

    /**
     * CRM只读JdbcTemplate
     * @param dataSource 只读数据源
     * @return JdbcTemplate
     */
    @Bean(name = "crmReadOnlyJdbcTemplate")
    public JdbcTemplate crmReadOnlyJdbcTemplate(@Qualifier("crmReadOnlyDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    /**
     * CRM只读MyBatis Plus拦截器
     * @return MybatisPlusInterceptor
     */
    @Bean(name = "crmReadOnlyMybatisPlusInterceptor")
    public MybatisPlusInterceptor crmReadOnlyMybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        paginationInnerInterceptor.setOverflow(true);
        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        return interceptor;
    }

    /**
     * CRM只读SqlSessionFactory
     * @param dataSource 只读数据源
     * @param mybatisPlusInterceptor MyBatis Plus拦截器
     * @return SqlSessionFactory
     * @throws Exception 异常
     */
    @Bean(name = "crmReadOnlySqlSessionFactory")
    public SqlSessionFactory crmReadOnlySqlSessionFactory(@Qualifier("crmReadOnlyDataSource") DataSource dataSource,
            @Qualifier("crmReadOnlyMybatisPlusInterceptor") MybatisPlusInterceptor mybatisPlusInterceptor)
            throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        PathMatchingResourcePatternResolver pmrpr = new PathMatchingResourcePatternResolver();

        // 只读数据源的mapper文件路径
        String readonlyResources = "classpath*:readonly/mapper/*.xml";
        
        // 加载只读mapper资源
        Resource[] mapperLocations = pmrpr.getResources(readonlyResources);
        
        Interceptor[] plugins = new Interceptor[1];
        plugins[0] = mybatisPlusInterceptor;
        bean.setPlugins(plugins);
        bean.setMapperLocations(mapperLocations);
        bean.setTypeHandlers(new TypeHandler[] {new LocalDateTimeTypeHandler(), new LocalDateTypeHandler(),
                new LocalTimeTypeHandler()});

        // MyBatis-Plus 3.x 全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        bean.setGlobalConfig(globalConfig);

        return bean.getObject();
    }

    /**
     * CRM只读SqlSessionTemplate
     * @param sqlSessionFactory 只读SqlSessionFactory
     * @return SqlSessionTemplate
     * @throws Exception 异常
     */
    @Bean(name = "crmReadOnlySqlSessionTemplate")
    public SqlSessionTemplate crmReadOnlySqlSessionTemplate(
            @Qualifier("crmReadOnlySqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * CRM只读事务管理器
     * 注意：只读数据源通常不需要事务管理器，但为了完整性提供
     * @param dataSource 只读数据源
     * @return PlatformTransactionManager
     */
    @Bean(name = "crmReadOnlyTransactionManager")
    public PlatformTransactionManager crmReadOnlyTransactionManager(@Qualifier("crmReadOnlyDataSource") DataSource dataSource){
        return new DataSourceTransactionManager(dataSource);
    }
}
