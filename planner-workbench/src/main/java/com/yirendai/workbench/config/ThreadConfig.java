package com.yirendai.workbench.config;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.yirendai.voiceaiserver.config.AiConversationTaskRejectedHandler;
import com.yirendai.voiceaiserver.task.priority.PriorityThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Slf4j
@Configuration
@RefreshScope
public class ThreadConfig {

    @Value("${callCenter.realTime.coreSize}")
    private int coreSizeCCR;

    @Value("${callCenter.realTime.maxSize}")
    private int maxSizCCR;

    @Value("${callCenter.realTime.queueCapacity}")
    private int qCapacityCCR;

    @Value("${ai.text.threadPool.miniSize}")
    private int aiTextThreadPoolMiniSize;

    @Value("${ai.text.threadPool.maxSize}")
    private int aiTextThreadPoolMaxSize;

    /**
     * 线程池
     * @return
     */
    @Qualifier("dataInitThreadPool")
    @Bean
    public ThreadPoolTaskExecutor accountThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setKeepAliveSeconds(600);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 业务线程池
     * @return
     */
    @Bean(name = "commonThreadPoolExecutor")
    public ExecutorService commonThreadPoolExecutor() {

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(8, 16, 60L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder().setNamePrefix("common-pool-").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        log.info("init commonThreadPoolExecutor");
        return threadPoolExecutor;
    }

    @Bean(name = "avayaSyncThreadPoolExecutor")
    public ExecutorService avayaSyncThreadPoolExecutor() {

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 16, 60L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(50), new ThreadFactoryBuilder().setNamePrefix("avaya-sync-pool-").build(),
                new ThreadPoolExecutor.AbortPolicy());
        log.info("init avayaSyncThreadPoolExecutor");
        return threadPoolExecutor;
    }

    @Bean(name = "avayaTranslateThreadPoolExecutor")
    public ExecutorService avayaTranslateThreadPoolExecutor() {

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(20, 50, 60L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(50), new ThreadFactoryBuilder().setNamePrefix("avaya-translate-pool-").build(),
                new ThreadPoolExecutor.AbortPolicy());
        log.info("init avayaTranslateThreadPoolExecutor");
        return threadPoolExecutor;
    }

    @Bean(name = "avayaSplitThreadPoolExecutor")
    public ExecutorService avayaSplitThreadPoolExecutor() {

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 70, 60L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(50), new ThreadFactoryBuilder().setNamePrefix("avaya-split-pool-").build(),
                new ThreadPoolExecutor.AbortPolicy());
        log.info("init avayaSplitThreadPoolExecutor");
        return threadPoolExecutor;
    }

    @Bean(name = "avayaSplitCrmThreadPoolExecutor")
    public ExecutorService avayaSplitCrmThreadPoolExecutor() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(8, 10, 60L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder().setNamePrefix("avaya-split-crm-pool-").build(),
                new  ThreadPoolExecutor.AbortPolicy());
        log.info("init avayaSplitCrmThreadPoolExecutor");
        return threadPoolExecutor;
    }

    @Bean(name = "avayaAddThreadPoolExecutor")
    public ExecutorService avayaAddThreadPoolExecutor() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(2, 8, 60L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder().setNamePrefix("avaya-add-pool-").build(),
                new  ThreadPoolExecutor.AbortPolicy());
        log.info("init avayaAddThreadPoolExecutor");
        return threadPoolExecutor;
    }

    @Bean(name = "aiConversationProcessThreadPool")
    public PriorityThreadPoolExecutor aiConversationThreadPoolExecutor() {
        int corePoolSize = aiTextThreadPoolMiniSize;
        int maxPoolSize = aiTextThreadPoolMaxSize;
        long keepAliveTime = 60L;
        int queueCapacity = 500;

        PriorityThreadPoolExecutor executor = new PriorityThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                new PriorityBlockingQueue<>(queueCapacity),
                new ThreadFactoryBuilder().setNamePrefix("ai-conversation-process-pool-").build(),
                new AiConversationTaskRejectedHandler()
        );

        executor.allowCoreThreadTimeOut(true);
        return executor;
    }

    @Bean(name = "callCenterRealTimeThreadPoolExecutor")
    public ExecutorService callCenterRealTimeThreadPoolExecutor() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(coreSizeCCR, maxSizCCR, 60L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(qCapacityCCR),
                new ThreadFactoryBuilder().setNamePrefix("callCenter-realTime-pool-").build(),
                new ThreadPoolExecutor.AbortPolicy());
        log.info("init callCenterRealTimeThreadPoolExecutor");
        return threadPoolExecutor;
    }

    /**
     * 实时asr线程池
     * @return
     */
    @Qualifier("asrOnlineThreadPool")
    @Bean
    public ThreadPoolTaskExecutor asrOnlineThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setKeepAliveSeconds(600);
        executor.setMaxPoolSize(30);
        executor.setQueueCapacity(200);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 实时子处理asr线程池
     * @return
     */
    @Qualifier("asrOnlineChannelThreadPool")
    @Bean(name = "asrOnlineChannelThreadPool")
    public ThreadPoolTaskExecutor asrOnlineChannelThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(15);
        executor.setKeepAliveSeconds(600);
        executor.setMaxPoolSize(40);
        executor.setQueueCapacity(300);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean(name = "aiRobotTtsThreadPoolExecutor")
    public ExecutorService aiRobotTtsThreadPoolExecutor() {
        int corePoolSize = 3;
        int maxPoolSize = 6;
        long keepAliveTime = 60L;
        int queueCapacity = 500;

        return new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueCapacity),
                new ThreadFactoryBuilder().setNamePrefix("ai-robot-tts-pool-").build(),
                new ThreadPoolExecutor.AbortPolicy()
        );
    }

    @Bean(name = "appointmentRemindThreadPoolExecutor")
    public ExecutorService appointmentRemindThreadPoolExecutor() {

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 16, 60L,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(50), new ThreadFactoryBuilder().setNamePrefix("appointmentRemind-pool-").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        log.info("init appointmentRemindPoolExecutor");
        return threadPoolExecutor;
    }

    @Bean(name = "syncLinkWechatPoolExecutor")
    public ExecutorService syncLinkWechatPoolExecutor() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(8, 10, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder().setNamePrefix("sync-linkWechat-thread").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        log.info("init syncLinkWechatPoolExecutor");
        return threadPoolExecutor;
    }
}