package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeCondition;
import com.yirendai.workbench.mapper.CallcenterSaleProcessNodeConditionMapper;
import com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessNodeConditionReadonlyMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeConditionService;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerNodeInfo;
import com.yirendai.workbench.vo.res.callcenter.CallcenterNodeConditionNumbInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售流程节点条件配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
@Service
public class CallcenterSaleProcessNodeConditionServiceImpl extends ServiceImpl<CallcenterSaleProcessNodeConditionMapper, CallcenterSaleProcessNodeCondition> implements ICallcenterSaleProcessNodeConditionService {

}
