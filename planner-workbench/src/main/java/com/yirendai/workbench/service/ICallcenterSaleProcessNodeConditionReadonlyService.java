package com.yirendai.workbench.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeCondition;
import com.yirendai.workbench.vo.res.callcenter.CallcenterNodeConditionNumbInfo;

import java.util.List;

/**
 * <p>
 * 销售流程节点条件配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
public interface ICallcenterSaleProcessNodeConditionReadonlyService extends IService<CallcenterSaleProcessNodeCondition> {

    List<CallcenterSaleProcessNodeCondition> selectListByIdsReadonly(List<String> ids);

    List<CallcenterSaleProcessNodeCondition> selectListByNodeIdReadonly(String nodeId);

    List<CallcenterNodeConditionNumbInfo> countNumberByNodeIdsReadonly(List<String> nodeIds);
}
