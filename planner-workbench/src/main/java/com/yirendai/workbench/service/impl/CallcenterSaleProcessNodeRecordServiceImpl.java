package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeRecord;
import com.yirendai.workbench.mapper.CallcenterSaleProcessNodeRecordMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 * 销售流程节点进度记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
@Service
public class CallcenterSaleProcessNodeRecordServiceImpl extends ServiceImpl<CallcenterSaleProcessNodeRecordMapper, CallcenterSaleProcessNodeRecord> implements ICallcenterSaleProcessNodeRecordService {

}
