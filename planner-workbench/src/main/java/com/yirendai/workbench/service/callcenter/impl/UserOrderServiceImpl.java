package com.yirendai.workbench.service.callcenter.impl;

import com.alibaba.excel.util.DateUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yirendai.voiceaiserver.model.owmuc.UserVerifyInfo;
import com.yirendai.voiceaiserver.service.UserVerifyInfoService;
import com.yirendai.workbench.enums.callcenter.*;
import com.yirendai.workbench.service.callcenter.UserOrderService;
import com.yirendai.workbench.util.DesensitizationUtil;
import com.yirendai.workbench.vo.req.callcenter.OrderSearchReq;
import com.yirendai.workbench.vo.res.callcenter.*;
import com.yirendai.workbench.wrapper.*;
import com.yirendai.workbench.wrapper.dto.*;
import com.yirendai.workbench.wrapper.dto.tradeCenter.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserOrderServiceImpl implements UserOrderService {
    @Autowired
    private TradeCenterApiWrapper tradeCenterApiWrapper;
    @Autowired
    private PrizeAccountApiWrapper prizeAccountApiWrapper;
    @Autowired
    private YrbApiWrapper yrbApiWrapper;
    @Autowired
    private FCardApiWrapper fCardApiWrapper;
    @Autowired
    private YqzApiWrapper yqzApiWrapper;
    @Resource
    private UserVerifyInfoService userVerifyInfoService;
    @Autowired
    private OwmFinanceWrapper owmFinanceWrapper;

    @Override
    public IPage<XgOrderInfo> getXgOrderList(OrderSearchReq orderSearchReq) {
        Page<XgOrderInfo> resPage = new  Page<>();
        UserVerifyInfo userVerifyInfo = userVerifyInfoService.lambdaQuery().eq(UserVerifyInfo::getUserId, orderSearchReq.getCustomerUid()).one();
        if (userVerifyInfo == null || userVerifyInfo.getEcifId() == null){
            return resPage;
        }

        TradeCenterPagedOrderRequest tradeCenterPagedOrderRequest = new TradeCenterPagedOrderRequest();
        tradeCenterPagedOrderRequest.setCurrent(orderSearchReq.getPageNo());
        tradeCenterPagedOrderRequest.setSize(orderSearchReq.getPageSize());
        tradeCenterPagedOrderRequest.setEcifId(userVerifyInfo.getEcifId()+"");
        tradeCenterPagedOrderRequest.setProductCategory(2);
        if(StringUtils.isNotEmpty(orderSearchReq.getApplyId())){
            tradeCenterPagedOrderRequest.setApplyId(orderSearchReq.getApplyId());
        }
        if(StringUtils.isNotEmpty(orderSearchReq.getStatus())){
            tradeCenterPagedOrderRequest.setStatusArray(orderSearchReq.getStatus());
        }
        if (Objects.nonNull(orderSearchReq.getStartTime())) {
            tradeCenterPagedOrderRequest.setApplyTimeBeg(orderSearchReq.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (Objects.nonNull(orderSearchReq.getEndTime())) {
            tradeCenterPagedOrderRequest.setApplyTimeEnd(orderSearchReq.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        TradeCenterPagedResponse<TradeCenterOrderInfoResponse> tradeCenterOrderPage = tradeCenterApiWrapper.getUserPagedOrders(tradeCenterPagedOrderRequest);
        if(null != tradeCenterOrderPage){
            BeanUtils.copyProperties(tradeCenterOrderPage, resPage);
            List<String> applyIds = new ArrayList<>();
            List<XgOrderInfo> orderInfoList = Optional.ofNullable(tradeCenterOrderPage.getRecords()).orElse(new ArrayList<>()).stream().map(bean->{
                applyIds.add(bean.getApplyId());
                XgOrderInfo xgOrderInfo = new XgOrderInfo();
                BeanUtils.copyProperties(bean, xgOrderInfo);
                xgOrderInfo.setStatus(XgOrderStatusEnum.getDescByCode(bean.getStatus()));
                xgOrderInfo.setTurnStatus(OrderTurnStatusEnum.getDescByCode(bean.getTurnStatus()));
                xgOrderInfo.setChannelCustomerPhone(StringUtils.isBlank(bean.getChannelCustomerPhone()) ? null : DesensitizationUtil.hidePhone(bean.getChannelCustomerPhone()));
                xgOrderInfo.setPayBankCardNo(StringUtils.isBlank(bean.getPayBankCardNo()) ? null : DesensitizationUtil.hideBankCardNo(bean.getPayBankCardNo()));
                return xgOrderInfo;
            }).collect(Collectors.toList());
            /*
            查询转投单的状态
             */
            Map<String, TradeCenterTurnOrderStatusResponse> turnOrderStatusMap = tradeCenterApiWrapper.getTurnOrderStatusBatch(applyIds);
            Map<String, RewardRecord> rewardRecordMap = owmFinanceWrapper.getRewardList(StringUtils.join(applyIds, ","));
            for (XgOrderInfo xgOrderInfo : orderInfoList) {
                TradeCenterTurnOrderStatusResponse tradeCenterTurnOrderStatusResponse = turnOrderStatusMap.get(xgOrderInfo.getApplyId());
                if (tradeCenterTurnOrderStatusResponse != null) {
                    xgOrderInfo.setTurnedOrderStatus(XgOrderStatusEnum.getDescByCode(tradeCenterTurnOrderStatusResponse.getStatus()));
                    xgOrderInfo.setTurnedOrderId(tradeCenterTurnOrderStatusResponse.getApplyIdTo());
                }
                RewardRecord rewardRecord = rewardRecordMap == null? null : rewardRecordMap.get(xgOrderInfo.getApplyId());
                if (rewardRecord != null) {
                    xgOrderInfo.setRewardInfo(rewardRecord.toOrderRewardInfo());
                }

            }

            resPage.setRecords(orderInfoList);
        }
        return resPage;
    }

    @Override
    public IPage<DgOrderInfo> getDgOrderList(OrderSearchReq orderSearchReq) {
        Page<DgOrderInfo> resPage = new  Page<>();
        UserVerifyInfo userVerifyInfo = userVerifyInfoService.lambdaQuery().eq(UserVerifyInfo::getUserId, orderSearchReq.getCustomerUid()).one();
        if (userVerifyInfo == null || userVerifyInfo.getEcifId() == null){
            return resPage;
        }

        TradeCenterPagedOrderRequest tradeCenterPagedOrderRequest = new TradeCenterPagedOrderRequest();
        tradeCenterPagedOrderRequest.setCurrent(orderSearchReq.getPageNo());
        tradeCenterPagedOrderRequest.setSize(orderSearchReq.getPageSize());
        tradeCenterPagedOrderRequest.setEcifId(userVerifyInfo.getEcifId()+"");
        tradeCenterPagedOrderRequest.setProductCategory(0);
        if(StringUtils.isNotEmpty(orderSearchReq.getApplyId())){
            tradeCenterPagedOrderRequest.setApplyId(orderSearchReq.getApplyId());
        }
        if(StringUtils.isNotEmpty(orderSearchReq.getStatus())){
            tradeCenterPagedOrderRequest.setStatusArray(orderSearchReq.getStatus());
        }
        if (Objects.nonNull(orderSearchReq.getStartTime())) {
            tradeCenterPagedOrderRequest.setApplyTimeBeg(orderSearchReq.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (Objects.nonNull(orderSearchReq.getEndTime())) {
            tradeCenterPagedOrderRequest.setApplyTimeEnd(orderSearchReq.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        TradeCenterPagedResponse<TradeCenterOrderInfoResponse> tradeCenterOrderPage = tradeCenterApiWrapper.getUserPagedOrders(tradeCenterPagedOrderRequest);
        if(null != tradeCenterOrderPage){
            List<String> applyIds = new ArrayList<>();
            BeanUtils.copyProperties(tradeCenterOrderPage, resPage);
            List<DgOrderInfo> orderInfoList = Optional.ofNullable(tradeCenterOrderPage.getRecords()).orElse(new ArrayList<>()).stream().map(bean->{
                applyIds.add(bean.getApplyId());
                DgOrderInfo dgOrderInfo = new DgOrderInfo();
                BeanUtils.copyProperties(bean, dgOrderInfo);
                dgOrderInfo.setStatus(DgOrderStatusEnum.getDescByCode(bean.getStatus()));
                dgOrderInfo.setChannelCustomerPhone(StringUtils.isBlank(bean.getChannelCustomerPhone()) ? null : DesensitizationUtil.hidePhone(bean.getChannelCustomerPhone()));
                // 查询回款方式与回款周期
                try {
                    ProductResponse productResponse = PRODUCT_CACHE.get(bean.getProductNo());
                    if (null !=productResponse){
                        dgOrderInfo.setPayInterestType(ProductPayInterestTypeEnum.getDescByType(productResponse.getSettleMethod()));
                        dgOrderInfo.setPayInterestCycle(productResponse.getCpFreqNmonth());
                    }
                }catch (Exception e){
                    log.error("productNo :{} 查询回款信息异常，",bean.getProductNo(),e);
                }
                return dgOrderInfo;
            }).collect(Collectors.toList());
            /*
            查询转投单的状态
             */
            Map<String, TradeCenterTurnOrderStatusResponse> turnOrderStatusMap = tradeCenterApiWrapper.getTurnOrderStatusBatch(applyIds);
            Map<String, RewardRecord> rewardRecordMap = owmFinanceWrapper.getRewardList(StringUtils.join(applyIds, ","));
            for (DgOrderInfo dgOrderInfo : orderInfoList) {
                TradeCenterTurnOrderStatusResponse tradeCenterTurnOrderStatusResponse = turnOrderStatusMap.get(dgOrderInfo.getApplyId());
                if (tradeCenterTurnOrderStatusResponse != null) {
                    dgOrderInfo.setTurnedOrderStatus(DgOrderStatusEnum.getDescByCode(tradeCenterTurnOrderStatusResponse.getStatus()));
                    dgOrderInfo.setTurnedOrderId(tradeCenterTurnOrderStatusResponse.getApplyIdTo());
                }
                RewardRecord rewardRecord = rewardRecordMap == null? null : rewardRecordMap.get(dgOrderInfo.getApplyId());
                if (rewardRecord != null) {
                    dgOrderInfo.setRewardInfo(rewardRecord.toOrderRewardInfo());
                }
            }
            resPage.setRecords(orderInfoList);
        }
        return resPage;
    }

    private final LoadingCache<String, ProductResponse> PRODUCT_CACHE = CacheBuilder.newBuilder()
            //cache的初始容量
            .initialCapacity(20)
            //cache最大缓存数
            .maximumSize(100)
            //设置写缓存后n秒钟过期
            .expireAfterWrite(60*60, TimeUnit.SECONDS)
            .build(new CacheLoader<String, ProductResponse>() {
                @Override
                public ProductResponse load(String key) throws Exception {
                    return getProductResponse(key);
                }
            });

    public ProductResponse getProductResponse(String productNo){
        ProductResponse productResponse = tradeCenterApiWrapper.getProductByCode(productNo);
        return productResponse;
    }

    @Override
    public IPage<RewardCashflowInfo> getPrizeOrderList(OrderSearchReq orderSearchReq){
        Page<RewardCashflowInfo> resPage = new  Page<>();
        RewardPageModel<RewardCashflow> rewardPageResp = prizeAccountApiWrapper.getRewardAccountCashFlow(orderSearchReq.getCustomerUid(),orderSearchReq.getType(),orderSearchReq.getPageNo(),orderSearchReq.getPageSize(),orderSearchReq.getStartTime(), orderSearchReq.getEndTime());
        if(null != rewardPageResp){
            resPage.setCurrent(rewardPageResp.getPageNum());
            resPage.setSize(rewardPageResp.getPageSize());
            resPage.setTotal(rewardPageResp.getTotalCount());
            List<RewardCashflowInfo> rewardInfoList = Optional.ofNullable(rewardPageResp.getData()).orElse(new ArrayList<>()).stream().map(bean->{
                RewardCashflowInfo rewardInfo = new RewardCashflowInfo();
                BeanUtils.copyProperties(bean,rewardInfo);
                if(!StringUtils.isEmpty(bean.getLockValue())){
                    rewardInfo.setRemark("账户含锁定金额："+bean.getLockValue());
                }
                if(bean.getCreateTime()!=null){
                    rewardInfo.setCreateTimeShow(DateUtils.format(bean.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                return  rewardInfo;
            }).collect(Collectors.toList());
            resPage.setRecords(rewardInfoList);
        }
        return resPage;
    }

    @Override
    public IPage<YrbCashflowInfo> getYrbOrderList(OrderSearchReq orderSearchReq){
        Page<YrbCashflowInfo> resPage = new  Page<>();
        YrbPageResult<YrbSendRecordInfo> yrbListResult = yrbApiWrapper.yrbSendRecordList(orderSearchReq.getCustomerUid(),orderSearchReq.getStatus(),orderSearchReq.getPageNo(),orderSearchReq.getPageSize());
        if(null != yrbListResult){
            resPage.setCurrent(yrbListResult.getPageNum());
            resPage.setSize(yrbListResult.getPageSize());
            resPage.setTotal(yrbListResult.getTotal());
            List<YrbCashflowInfo> yrbList = Optional.ofNullable(yrbListResult.getData()).orElse(new ArrayList<>()).stream().map(bean->{
                YrbCashflowInfo yrbInfo = new YrbCashflowInfo();
                BeanUtils.copyProperties(bean,yrbInfo);
                if(bean.getCreateTime()!=null){
                    yrbInfo.setCreateTimeShow(DateUtils.format(bean.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(bean.getUpdateTime()!=null){
                    yrbInfo.setUpdateTimeShow(DateUtils.format(bean.getUpdateTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(bean.getExpectExpireTime()!=null){
                    yrbInfo.setRealExpireTime(DateUtils.format(bean.getExpectExpireTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(bean.getRealExpireTime()!=null){
                    yrbInfo.setRealExpireTime(DateUtils.format(bean.getRealExpireTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(StringUtils.isNotEmpty(bean.getBusinessTypeValue())){
                    yrbInfo.setBusinessTypeValue(StringUtils.replace(bean.getBusinessTypeValue(),"类固收","001"));
                }
                return  yrbInfo;
            }).collect(Collectors.toList());
            resPage.setRecords(yrbList);
        }
        return resPage;
    }

    @Override
    public IPage<FcardInfoRes> getFcardList(OrderSearchReq orderSearchReq){
        Page<FcardInfoRes> resPage = new  Page<>();
        FcardPageResult<FcardInfo> fcardPageResult = fCardApiWrapper.getFcardList(orderSearchReq.getCustomerUid(),orderSearchReq.getPageNo(),orderSearchReq.getPageSize());
        if(null != fcardPageResult){
            resPage.setCurrent(fcardPageResult.getPageNum());
            resPage.setSize(fcardPageResult.getPageSize());
            resPage.setTotal(fcardPageResult.getTotal());
            List<FcardInfoRes> fcardInfoResList = Optional.ofNullable(fcardPageResult.getData()).orElse(new ArrayList<>()).stream().map(bean->{
                FcardInfoRes fcardInfo = new FcardInfoRes();
                BeanUtils.copyProperties(bean,fcardInfo);
                if(null != bean.getBindCardTime()){
                    fcardInfo.setBindCardTime(DateUtils.format(bean.getBindCardTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(null != bean.getEndTime()){
                    fcardInfo.setEndTime(DateUtils.format(bean.getEndTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(null != bean.getCardType()){
                    fcardInfo.setCardType(FcardTypeEnum.of(bean.getCardType()).getValue());
                }
                return fcardInfo;
            }).collect(Collectors.toList());
            resPage.setRecords(fcardInfoResList);
        }
        return resPage;
    }

    @Override
    public IPage<FcardRecordInfoRes> getFcardRecordList(OrderSearchReq orderSearchReq){
        Page<FcardRecordInfoRes> resPage = new  Page<>();
        FcardPageResult<FcardRecordInfo> fcardPageResult = fCardApiWrapper.getFcardRecordList(orderSearchReq.getCustomerUid(),orderSearchReq.getStatus(),orderSearchReq.getPageNo(),orderSearchReq.getPageSize());
        if(null != fcardPageResult){
            resPage.setCurrent(fcardPageResult.getPageNum());
            resPage.setSize(fcardPageResult.getPageSize());
            resPage.setTotal(fcardPageResult.getTotal());
            List<FcardRecordInfoRes> fcardInfoResList = Optional.ofNullable(fcardPageResult.getData()).orElse(new ArrayList<>()).stream().map(bean->{
                FcardRecordInfoRes fcardRecordInfo = new FcardRecordInfoRes();
                BeanUtils.copyProperties(bean,fcardRecordInfo);
                if(null != bean.getCreateTime()){
                    fcardRecordInfo.setCreateTime(DateUtils.format(bean.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(null != bean.getSendTime()){
                    fcardRecordInfo.setSendTime(DateUtils.format(bean.getSendTime(),"yyyy-MM-dd HH:mm:ss"));
                }
                if(null != bean.getBusinessType()){
                    fcardRecordInfo.setBusinessTypeDesc(BusinessTypeEnum.of(bean.getBusinessType()).getValue());
                }
                if(null != bean.getCardType()){
                    fcardRecordInfo.setCardType(FcardTypeEnum.of(bean.getCardType()).getValue());
                }
                if(null != bean.getStatus()){
                    fcardRecordInfo.setStatus(FcardRecordStatusEnum.of(bean.getStatus()).getValue());
                }
                return fcardRecordInfo;
            }).collect(Collectors.toList());
            resPage.setRecords(fcardInfoResList);
        }
        return resPage;
    }

    @Override
    public YqzInvestPageRes getYqzInfo(OrderSearchReq orderSearchReq){
        YqzInvestPageRes res = new YqzInvestPageRes();
        YqzMemberDetailDto memberDetailDto = yqzApiWrapper.getMemberDetail(orderSearchReq.getCustomerUid());
        if(null != memberDetailDto && memberDetailDto.getInviter()!=null){
            res.setInvestUserId(memberDetailDto.getInviter().getUserId().toString());
            res.setInvestUserName(memberDetailDto.getInviter().getName());
            res.setInvestUserPhone(memberDetailDto.getInviter().getPhone());
        }
        YqzInvestPageRespDto investPageRespDto = yqzApiWrapper.getMemberInvestList(orderSearchReq.getCustomerUid(),orderSearchReq.getPageNo(),orderSearchReq.getPageSize());
        if(null != investPageRespDto){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            Optional.ofNullable(investPageRespDto.getRecords()).orElse(new ArrayList<>()).stream().forEach(investBean ->{
                LocalDateTime time = LocalDateTime.parse(investBean.getCreatedAt());
                investBean.setCreatedAt(time.format(formatter));
            });
            res.setCurrent(investPageRespDto.getCurrent());
            res.setPages(investPageRespDto.getPages());
            res.setSize(investPageRespDto.getSize());
            res.setTotal(investPageRespDto.getTotal());
            res.setRecords(investPageRespDto.getRecords());
        }
        return res;
    }


}
