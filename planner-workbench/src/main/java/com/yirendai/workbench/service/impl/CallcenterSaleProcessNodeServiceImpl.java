package com.yirendai.workbench.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.robot.enums.RobotResult;
import com.yirendai.robot.exception.RobotException;
import com.yirendai.workbench.entity.CallcenterSaleProcessNode;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeCondition;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeEdge;
import com.yirendai.workbench.entity.CallcenterSalesProcess;
import com.yirendai.workbench.enums.SaleProcessNodeTypeEnum;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.mapper.CallcenterSaleProcessNodeMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeConditionService;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeEdgeService;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeService;
import com.yirendai.workbench.service.callcenter.CallcenterSalesProcessService;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeAddReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeConditionAddReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeDelReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeDetailReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeEdgeUpdateReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeStartReq;
import com.yirendai.workbench.vo.req.UnifiedConditionReq;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessNodeAddRes;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessNodeConditionAddRes;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessNodeEdgeAddRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.yirendai.workbench.exception.BusinessError.NO_PERMISSION;
import static com.yirendai.workbench.exception.BusinessError.PARAM_INVALID;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_COPY_FAIL;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_EDGE_NOT_EXIST;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_MULTIPLE_START_NODES;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_NODE_IS_NOT_EXIST;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_NODE_NOT_CONNECTED;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_NO_END_NODE;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_NO_START_NODE;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_START_NODE_NOT_END;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_START_NODE_ONLY_ONE;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_UPDATE_NODE_TEXT_ERROR;

/**
 * <p>
 * 销售流程节点基础信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2025-10-09
 */
@Service
@Slf4j
public class CallcenterSaleProcessNodeServiceImpl
        extends ServiceImpl<CallcenterSaleProcessNodeMapper, CallcenterSaleProcessNode>
        implements ICallcenterSaleProcessNodeService {

    @Resource
    ICallcenterSaleProcessNodeConditionService iCallcenterSaleProcessNodeConditionService;

    @Resource
    ICallcenterSaleProcessNodeEdgeService iCallcenterSaleProcessNodeEdgeService;

    @Resource
    CallcenterSalesProcessService callcenterSalesProcessService;

    @Override
    @Transactional
    public CallcenterSaleProcessNodeAddRes add(CallcenterSaleProcessNodeAddReq req) {
        //保存图
        callcenterSalesProcessService.updateNodeTextById(req.getSaleProcessId(), req.getNodeText());
        String tenantId = OwnAuthUtil.getTenantId();
        CallcenterSaleProcessNode node = BeanUtil.copyProperties(req, CallcenterSaleProcessNode.class, "conditionList");
        node.setTenantId(tenantId);
        this.save(node);
        CallcenterSaleProcessNodeAddRes res = BeanUtil.copyProperties(node, CallcenterSaleProcessNodeAddRes.class);
        //结束节点无边
        if(node.getType() != SaleProcessNodeTypeEnum.END.getCode()){
            CallcenterSaleProcessNodeEdge edge = new CallcenterSaleProcessNodeEdge();
            edge.setSaleProcessId(req.getSaleProcessId());
            edge.setTenantId(tenantId);
            edge.setFromNodeId(node.getId());
            edge.setOperationId(0L);
            iCallcenterSaleProcessNodeEdgeService.save(edge);
            List<CallcenterSaleProcessNodeEdgeAddRes> edgeList = new ArrayList<>();
            edgeList.add(BeanUtil.copyProperties(edge, CallcenterSaleProcessNodeEdgeAddRes.class));
            res.setEdgeList(edgeList);
        }
        return res;
    }

    @Override
    @Transactional
    public CallcenterSaleProcessNodeAddRes edit(CallcenterSaleProcessNodeAddReq req) {
        CallcenterSaleProcessNode dbNode = this.getById(req.getId());
        if (Objects.isNull(dbNode) || dbNode.getIsDeleted() == 1) {
            throw new BusinessException(SALE_PROCESS_NODE_IS_NOT_EXIST);
        }
        //保存图
        callcenterSalesProcessService.updateNodeTextById(req.getSaleProcessId(), req.getNodeText());
        String tenantId = OwnAuthUtil.getTenantId();
        CallcenterSaleProcessNode node = BeanUtil.copyProperties(req, CallcenterSaleProcessNode.class, "conditionList",
                "is_deleted", "create_time");
        node.setTenantId(tenantId);
        node.setUpdateTime(LocalDateTime.now());
        this.saveOrUpdate(node);
        CallcenterSaleProcessNodeAddRes res = BeanUtil.copyProperties(node, CallcenterSaleProcessNodeAddRes.class);
        if (CollUtil.isNotEmpty(req.getConditionList())) {
            // 1. 查询数据库中该节点的所有未删除的条件项
            List<CallcenterSaleProcessNodeCondition> dbConditionList = iCallcenterSaleProcessNodeConditionService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeCondition::getNodeId, node.getId())
                    .eq(CallcenterSaleProcessNodeCondition::getIsDeleted, 0)
                    .list();

            // 2. 收集前端传来的条件项ID（排除null）
            Set<Long> reqConditionIds = req.getConditionList().stream()
                    .map(CallcenterSaleProcessNodeConditionAddReq::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 3. 找出需要删除的条件项（数据库中存在但前端没有传来的）
            List<Long> toDeleteIds = dbConditionList.stream()
                    .map(CallcenterSaleProcessNodeCondition::getId)
                    .filter(id -> !reqConditionIds.contains(id))
                    .collect(Collectors.toList());

            // 4. 批量标记为已删除
            if (CollUtil.isNotEmpty(toDeleteIds)) {
                iCallcenterSaleProcessNodeConditionService.lambdaUpdate()
                        .set(CallcenterSaleProcessNodeCondition::getIsDeleted, 1)
                        .set(CallcenterSaleProcessNodeCondition::getUpdateTime, LocalDateTime.now())
                        .in(CallcenterSaleProcessNodeCondition::getId, toDeleteIds)
                        .update();
                log.info("标记删除条件项，nodeId: {}, 删除数量: {}", node.getId(), toDeleteIds.size());
            }

            // 5. 处理新增和更新的条件项
            List<CallcenterSaleProcessNodeCondition> conditionList = req.getConditionList().stream()
                    .map(conditionReq -> {
                        CallcenterSaleProcessNodeCondition dbCondition = null;
                        // 如果有ID，则查询数据库记录
                        if (Objects.nonNull(conditionReq.getId())) {
                            dbCondition = iCallcenterSaleProcessNodeConditionService.getById(
                                    conditionReq.getId());
                        }

                        CallcenterSaleProcessNodeCondition condition;
                        if (Objects.nonNull(dbCondition)) {
                            // 更新场景
                            condition = BeanUtil.copyProperties(conditionReq, CallcenterSaleProcessNodeCondition.class,
                                    "is_deleted", "create_time");
                        }
                        else {
                            // 新增场景
                            condition = BeanUtil.copyProperties(conditionReq, CallcenterSaleProcessNodeCondition.class,
                                    "id", "is_deleted", "create_time");
                        }
                        condition.setConditions(JsonUtilExt.beanToJson(conditionReq.getConditions()));
                        condition.setNodeId(node.getId());
                        condition.setTenantId(tenantId);
                        condition.setUpdateTime(LocalDateTime.now());
                        return condition;
                    }).collect(Collectors.toList());

            // 6. 分别处理新增和更新
            List<CallcenterSaleProcessNodeCondition> toInsert = conditionList.stream()
                    .filter(c -> c.getId() == null)
                    .collect(Collectors.toList());
            List<CallcenterSaleProcessNodeCondition> toUpdate = conditionList.stream()
                    .filter(c -> c.getId() != null)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(toInsert)) {
                iCallcenterSaleProcessNodeConditionService.saveBatch(toInsert);
            }
            if (CollUtil.isNotEmpty(toUpdate)) {
                iCallcenterSaleProcessNodeConditionService.updateBatchById(toUpdate);
            }

            res.setConditionList(conditionList.stream().map(condition -> {
                CallcenterSaleProcessNodeConditionAddRes conditionRes = BeanUtil.copyProperties(condition,
                        CallcenterSaleProcessNodeConditionAddRes.class, "conditions");
                conditionRes.setConditions(
                        JsonUtilExt.jsonToBean(condition.getConditions(), UnifiedConditionReq.class));
                return conditionRes;
            }).collect(Collectors.toList()));
        }
        List<CallcenterSaleProcessNodeEdge> dbEdgeList = iCallcenterSaleProcessNodeEdgeService.list(
                new LambdaQueryWrapper<>(CallcenterSaleProcessNodeEdge.class).eq(
                        CallcenterSaleProcessNodeEdge::getFromNodeId, node.getId()));
        res.setEdgeList(BeanUtil.copyToList(dbEdgeList, CallcenterSaleProcessNodeEdgeAddRes.class));
        return res;
    }

    @Override
    @Transactional
    public Boolean del(CallcenterSaleProcessNodeDelReq req) {
        CallcenterSaleProcessNode dbNode = this.getById(req.getId());
        if (Objects.isNull(dbNode)) {
            throw new BusinessException(SALE_PROCESS_NODE_IS_NOT_EXIST);
        }
        //保存图
        callcenterSalesProcessService.updateNodeTextById(req.getSaleProcessId(), req.getNodeText());
        dbNode.setIsDeleted(1);
        this.updateById(dbNode);
        iCallcenterSaleProcessNodeConditionService.lambdaUpdate()
                .set(CallcenterSaleProcessNodeCondition::getIsDeleted, 1)
                .eq(CallcenterSaleProcessNodeCondition::getNodeId, req.getId()).update();
        iCallcenterSaleProcessNodeEdgeService.lambdaUpdate().set(CallcenterSaleProcessNodeEdge::getIsDeleted, 1)
                .eq(CallcenterSaleProcessNodeEdge::getFromNodeId, req.getId()).update();
        iCallcenterSaleProcessNodeEdgeService.lambdaUpdate().set(CallcenterSaleProcessNodeEdge::getToNodeId, null)
                .eq(CallcenterSaleProcessNodeEdge::getToNodeId, req.getId()).update();
        return true;
    }

    @Override
    public CallcenterSaleProcessNodeAddRes detail(CallcenterSaleProcessNodeDetailReq req) {
        CallcenterSaleProcessNode dbNode = this.getById(req.getId());
        if (Objects.isNull(dbNode) || dbNode.getIsDeleted() == 1) {
            throw new BusinessException(SALE_PROCESS_NODE_IS_NOT_EXIST);
        }
        CallcenterSaleProcessNodeAddRes res = BeanUtil.copyProperties(dbNode, CallcenterSaleProcessNodeAddRes.class,
                "is_deleted", "create_time");
        List<CallcenterSaleProcessNodeCondition> conditionList = iCallcenterSaleProcessNodeConditionService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeCondition::getNodeId, req.getId())
                .eq(CallcenterSaleProcessNodeCondition::getIsDeleted, 0).list();
        if (CollUtil.isNotEmpty(conditionList)) {
            List<CallcenterSaleProcessNodeConditionAddRes> retConditionList = conditionList.stream().map(condition -> {
                CallcenterSaleProcessNodeConditionAddRes conditionRes = BeanUtil.copyProperties(condition,
                        CallcenterSaleProcessNodeConditionAddRes.class, "conditions");
                conditionRes.setConditions(
                        JsonUtilExt.jsonToBean(condition.getConditions(), UnifiedConditionReq.class));
                return conditionRes;
            }).collect(Collectors.toList());
            res.setConditionList(retConditionList);
        }
        List<CallcenterSaleProcessNodeEdge> dbEdgeList = iCallcenterSaleProcessNodeEdgeService.list(
                new LambdaQueryWrapper<>(CallcenterSaleProcessNodeEdge.class).eq(
                                CallcenterSaleProcessNodeEdge::getFromNodeId, dbNode.getId())
                        .eq(CallcenterSaleProcessNodeEdge::getIsDeleted, 0));
        res.setEdgeList(BeanUtil.copyToList(dbEdgeList, CallcenterSaleProcessNodeEdgeAddRes.class));
        return res;
    }

    @Override
    @Transactional
    public Boolean updateEdge(CallcenterSaleProcessNodeEdgeUpdateReq req) {
        //保存图
        String tenantId = OwnAuthUtil.getTenantId();
        callcenterSalesProcessService.updateNodeTextById(req.getSaleProcessId(), req.getNodeText());
        CallcenterSaleProcessNodeEdge edge = iCallcenterSaleProcessNodeEdgeService.getById(req.getId());
        if (Objects.isNull(edge) || Boolean.TRUE.equals(edge.getIsDeleted())) {
            throw new BusinessException(SALE_PROCESS_EDGE_NOT_EXIST);
        }
        if (!tenantId.equals(edge.getTenantId())) {
            throw new BusinessException(NO_PERMISSION);
        }
        if (!req.getFromNodeId().equals(edge.getFromNodeId())) {
            throw new BusinessException(PARAM_INVALID.getCode(), "起始节点与当前边的起始节点不一致");
        }
        if (Boolean.TRUE.equals(req.getIsConnect())) {
            if (Objects.nonNull(edge.getToNodeId())) {
                if (edge.getToNodeId().equals(req.getToNodeId())) {
                    return true;
                }
                else {
                    throw new BusinessException(PARAM_INVALID.getCode(), "暂不支持将边从指向A节点直接切换到指向B节点");
                }
            }
        }
        else if (!req.getToNodeId().equals(edge.getToNodeId())) {
            throw new BusinessException(PARAM_INVALID.getCode(), "当前边未指向目标节点，无法断开此边");
        }

        if (Boolean.TRUE.equals(req.getIsConnect())) {
            CallcenterSaleProcessNode node = getById(req.getToNodeId());
            if (Objects.isNull(node) || node.getIsDeleted() == 1 || !Objects.equals(edge.getSaleProcessId(),
                    node.getSaleProcessId()) || !tenantId.equals(node.getTenantId())) {
                throw new BusinessException(PARAM_INVALID.getCode(), "目标节点不存在或其版本号&租户与当前边不一致");
            }

            LambdaQueryWrapper<CallcenterSaleProcessNodeEdge> edgeWrapper = new LambdaQueryWrapper<>();
            edgeWrapper.eq(CallcenterSaleProcessNodeEdge::getSaleProcessId, edge.getSaleProcessId())
                    .isNotNull(CallcenterSaleProcessNodeEdge::getToNodeId)
                    .eq(CallcenterSaleProcessNodeEdge::getIsDeleted, Boolean.FALSE);
            List<CallcenterSaleProcessNodeEdge> list = iCallcenterSaleProcessNodeEdgeService.list(edgeWrapper);
            Map<String, Set<String>> graph = list.stream().collect(
                    Collectors.groupingBy(CallcenterSaleProcessNodeEdge::getFromNodeId,
                            Collectors.mapping(CallcenterSaleProcessNodeEdge::getToNodeId, Collectors.toSet())));
            if (checkPath(graph, req.getToNodeId(), req.getFromNodeId(), new HashSet<>())) {
                throw new RobotException(RobotResult.ROBOT_SUBORDINATE_NODE_CANNOT_RECONNECT);
            }
        }

        LambdaUpdateWrapper<CallcenterSaleProcessNodeEdge> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CallcenterSaleProcessNodeEdge::getId, req.getId());
        if (Boolean.TRUE.equals(req.getIsConnect())) {
            wrapper.set(CallcenterSaleProcessNodeEdge::getToNodeId, req.getToNodeId());
        }
        else {
            wrapper.set(CallcenterSaleProcessNodeEdge::getToNodeId, null);
        }
        iCallcenterSaleProcessNodeEdgeService.update(wrapper);
        return true;
    }

    @Override
    @Transactional
    public Boolean updateStart(CallcenterSaleProcessNodeStartReq req) {
        //保存图
        callcenterSalesProcessService.updateNodeTextById(req.getSaleProcessId(), req.getNodeText());
        String tenantId = OwnAuthUtil.getTenantId();

        CallcenterSaleProcessNode node = getById(req.getNodeId());
        if (Objects.isNull(node) || node.getIsDeleted() == 1) {
            return true;
        }
        if (!tenantId.equals(node.getTenantId())) {
            throw new BusinessException(NO_PERMISSION);
        }

        LambdaUpdateWrapper<CallcenterSaleProcessNode> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CallcenterSaleProcessNode::getId, req.getNodeId())
                .set(CallcenterSaleProcessNode::getType, SaleProcessNodeTypeEnum.START.getCode());
        update(wrapper);
        return true;
    }

    private boolean checkPath(Map<String, Set<String>> graph, String cur, String end, Set<String> visited) {
        if (cur.equals(end)) {
            return true;
        }
        if (visited.contains(cur)) {
            return false;
        }
        visited.add(cur);
        Set<String> neighbors = graph.getOrDefault(cur, new HashSet<>());
        for (String neighbor : neighbors) {
            if (checkPath(graph, neighbor, end, visited)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 复制销售流程及其相关数据
     * @param originalSaleProcessId 原流程ID
     * @param newSaleProcessId 新流程ID
     * @param newVersionNo 新版本号
     * @return 复制是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean copySaleProcessData(Long originalSaleProcessId, Long newSaleProcessId, String newVersionNo) {
        try {
            // 1. 复制节点数据
            Map<String, String> nodeIdMapping = copyProcessNodes(originalSaleProcessId, newSaleProcessId, newVersionNo);

            // 2. 复制节点条件数据
            copyProcessNodeConditions(originalSaleProcessId, nodeIdMapping);

            // 3. 复制边数据
            copyProcessNodeEdges(originalSaleProcessId, newSaleProcessId, nodeIdMapping);

            // 4. 更新流程表的node_text字段
            updateProcessNodeText(newSaleProcessId, nodeIdMapping);

            log.info("成功复制销售流程数据，原流程ID: {}, 新流程ID: {}, 新版本号: {}",
                    originalSaleProcessId, newSaleProcessId, newVersionNo);
            return true;

        } catch (Exception e) {
            log.error("复制销售流程数据失败，原流程ID: {}, 新流程ID: {}", originalSaleProcessId, newSaleProcessId, e);
            throw new BusinessException(SALE_PROCESS_COPY_FAIL);
        }
    }

    /**
     * 更新流程表的node_text字段，将其中的节点ID替换为新的节点ID
     * @param newSaleProcessId 新流程ID
     * @param nodeIdMapping 节点ID映射关系 (原节点ID -> 新节点ID)
     */
    private void updateProcessNodeText(Long newSaleProcessId, Map<String, String> nodeIdMapping) {
        if (nodeIdMapping.isEmpty()) {
            return;
        }

        try {
            // 1. 获取新流程的node_text
            CallcenterSalesProcess newProcess = callcenterSalesProcessService.getById(newSaleProcessId);
            if (Objects.isNull(newProcess) || StringUtils.isBlank(newProcess.getNodeText())) {
                log.warn("新流程的node_text为空，流程ID: {}", newSaleProcessId);
                return;
            }

            String originalNodeText = newProcess.getNodeText();
            String updatedNodeText = replaceNodeIdsInNodeText(originalNodeText, nodeIdMapping);

            // 2. 如果node_text有变化，则更新
            if (!originalNodeText.equals(updatedNodeText)) {
                newProcess.setNodeText(updatedNodeText);
                newProcess.setUpdateTime(LocalDateTime.now());
                callcenterSalesProcessService.updateById(newProcess);

                log.info("成功更新流程node_text，流程ID: {}", newSaleProcessId);
                log.debug("原node_text: {} 新node_text: {}", originalNodeText, updatedNodeText);
            }

        } catch (Exception e) {
            log.error("更新流程node_text失败，流程ID: {}", newSaleProcessId, e);
            throw new BusinessException(SALE_PROCESS_UPDATE_NODE_TEXT_ERROR);
        }
    }

    /**
     * 替换node_text中的节点ID
     * @param nodeText 原始node_text内容
     * @param nodeIdMapping 节点ID映射关系
     * @return 替换后的node_text内容
     */
    private String replaceNodeIdsInNodeText(String nodeText, Map<String, String> nodeIdMapping) {
        if (StringUtils.isBlank(nodeText)) {
            return nodeText;
        }

        String result = nodeText;

        // 方式1: 如果node_text是JSON格式，解析后替换
        if (isJsonFormat(nodeText)) {
            result = replaceNodeIdsInJson(nodeText, nodeIdMapping);
        } else {
            // 方式2: 如果是其他格式，使用字符串替换
            result = replaceNodeIdsInString(nodeText, nodeIdMapping);
        }

        return result;
    }

    /**
     * 判断字符串是否为JSON格式
     */
    private boolean isJsonFormat(String text) {
        try {
            JsonUtilExt.jsonToBean(text, Object.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 在JSON格式的node_text中替换节点ID
     */
    private String replaceNodeIdsInJson(String nodeText, Map<String, String> nodeIdMapping) {
        try {
            // 解析JSON
            Object jsonObject = JsonUtilExt.jsonToBean(nodeText, Object.class);

            // 递归替换JSON中的节点ID
            Object updatedJsonObject = replaceNodeIdsInJsonObject(jsonObject, nodeIdMapping);

            // 转回JSON字符串
            return JsonUtilExt.beanToJson(updatedJsonObject);

        } catch (Exception e) {
            log.warn("JSON格式替换失败，使用字符串替换方式，错误: {}", e.getMessage());
            return replaceNodeIdsInString(nodeText, nodeIdMapping);
        }
    }

    /**
     * 递归替换JSON对象中的节点ID
     */
    private Object replaceNodeIdsInJsonObject(Object obj, Map<String, String> nodeIdMapping) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) obj;
            Map<String, Object> newMap = new HashMap<>();

            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 递归处理值
                Object newValue = replaceNodeIdsInJsonObject(value, nodeIdMapping);

                // 如果key包含nodeId相关字段，也需要替换值
                if (isNodeIdField(key) && value instanceof String) {
                    String strValue = (String) value;
                    String newNodeId = nodeIdMapping.get(strValue);
                    if (newNodeId != null) {
                        newValue = newNodeId;
                    }
                }

                newMap.put(key, newValue);
            }
            return newMap;

        } else if (obj instanceof List) {
            List<Object> list = (List<Object>) obj;
            List<Object> newList = new ArrayList<>();

            for (Object item : list) {
                newList.add(replaceNodeIdsInJsonObject(item, nodeIdMapping));
            }
            return newList;

        } else if (obj instanceof String) {
            String strValue = (String) obj;
            // 检查是否是节点ID
            String newNodeId = nodeIdMapping.get(strValue);
            return newNodeId != null ? newNodeId : strValue;

        } else {
            return obj;
        }
    }

    /**
     * 判断字段名是否为节点ID相关字段
     */
    private boolean isNodeIdField(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return false;
        }

        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("cell") ||
                lowerFieldName.contains("port") ||
                lowerFieldName.equals("id");
    }

    /**
     * 在字符串格式的node_text中替换节点ID
     */
    private String replaceNodeIdsInString(String nodeText, Map<String, String> nodeIdMapping) {
        String result = nodeText;

        // 按照节点ID长度降序排列，避免短ID替换长ID的问题
        List<String> sortedOriginalNodeIds = nodeIdMapping.keySet().stream()
                .sorted((a, b) -> Integer.compare(b.length(), a.length()))
                .collect(Collectors.toList());

        for (String originalNodeId : sortedOriginalNodeIds) {
            String newNodeId = nodeIdMapping.get(originalNodeId);
            if (newNodeId != null) {
                // 使用正则表达式确保完整匹配，避免部分替换
                // 匹配独立的节点ID（前后为非字母数字字符或字符串边界）
                String pattern = "\\b" + Pattern.quote(originalNodeId) + "\\b";
                result = result.replaceAll(pattern, newNodeId);
            }
        }

        return result;
    }

    /**
     * 复制流程节点数据
     * @param originalSaleProcessId 原流程ID
     * @param newSaleProcessId 新流程ID
     * @param newVersionNo 新版本号
     * @return 节点ID映射关系 (原节点ID -> 新节点ID)
     */
    private Map<String, String> copyProcessNodes(Long originalSaleProcessId, Long newSaleProcessId, String newVersionNo) {
        // 1. 查询原流程的所有节点
        List<CallcenterSaleProcessNode> originalNodes = this.lambdaQuery()
                .eq(CallcenterSaleProcessNode::getSaleProcessId, originalSaleProcessId)
                .eq(CallcenterSaleProcessNode::getIsDeleted, 0)
                .list();

        if (originalNodes.isEmpty()) {
            log.warn("原流程没有找到节点数据，流程ID: {}", originalSaleProcessId);
            return new HashMap<>();
        }

        // 2. 创建节点ID映射关系
        Map<String, String> nodeIdMapping = new HashMap<>();
        List<CallcenterSaleProcessNode> newNodes = new ArrayList<>();

        for (CallcenterSaleProcessNode originalNode : originalNodes) {
            String newNodeId = UUID.randomUUID().toString();
            nodeIdMapping.put(originalNode.getId(), newNodeId);

            // 创建新节点对象
            CallcenterSaleProcessNode newNode = new CallcenterSaleProcessNode();
            BeanUtil.copyProperties(originalNode, newNode);

            // 设置新的属性
            newNode.setId(newNodeId);
            newNode.setSaleProcessId(newSaleProcessId);
            newNode.setCreateTime(LocalDateTime.now());
            newNode.setUpdateTime(LocalDateTime.now());

            newNodes.add(newNode);
        }

        // 3. 批量保存新节点
        this.saveBatch(newNodes);

        log.info("成功复制 {} 个节点，流程ID: {} -> {}", newNodes.size(), originalSaleProcessId, newSaleProcessId);
        return nodeIdMapping;
    }

    /**
     * 复制节点条件数据
     * @param originalSaleProcessId 原流程ID
     * @param nodeIdMapping 节点ID映射关系
     */
    private void copyProcessNodeConditions(Long originalSaleProcessId, Map<String, String> nodeIdMapping) {
        if (nodeIdMapping.isEmpty()) {
            return;
        }

        // 1. 查询原流程的所有节点条件
        List<String> originalNodeIds = new ArrayList<>(nodeIdMapping.keySet());
        List<CallcenterSaleProcessNodeCondition> originalConditions = iCallcenterSaleProcessNodeConditionService.lambdaQuery()
                .in(CallcenterSaleProcessNodeCondition::getNodeId, originalNodeIds)
                .eq(CallcenterSaleProcessNodeCondition::getIsDeleted, 0)
                .list();

        if (originalConditions.isEmpty()) {
            log.info("原流程没有找到节点条件数据，流程ID: {}", originalSaleProcessId);
            return;
        }

        // 2. 创建新的节点条件
        List<CallcenterSaleProcessNodeCondition> newConditions = new ArrayList<>();

        for (CallcenterSaleProcessNodeCondition originalCondition : originalConditions) {
            String newNodeId = nodeIdMapping.get(originalCondition.getNodeId());
            if (newNodeId != null) {
                CallcenterSaleProcessNodeCondition newCondition = new CallcenterSaleProcessNodeCondition();
                BeanUtil.copyProperties(originalCondition, newCondition);

                // 设置新的属性
                newCondition.setId(null); // 让数据库自动生成新ID
                newCondition.setNodeId(newNodeId);
                newCondition.setCreateTime(LocalDateTime.now());
                newCondition.setUpdateTime(LocalDateTime.now());

                newConditions.add(newCondition);
            }
        }

        // 3. 批量保存新条件
        if (!newConditions.isEmpty()) {
            iCallcenterSaleProcessNodeConditionService.saveBatch(newConditions);
            log.info("成功复制 {} 个节点条件", newConditions.size());
        }
    }

    /**
     * 复制节点边数据
     * @param originalSaleProcessId 原流程ID
     * @param newSaleProcessId 新流程ID
     * @param nodeIdMapping 节点ID映射关系
     */
    private void copyProcessNodeEdges(Long originalSaleProcessId, Long newSaleProcessId, Map<String, String> nodeIdMapping) {
        if (nodeIdMapping.isEmpty()) {
            return;
        }

        // 1. 查询原流程的所有边
        List<CallcenterSaleProcessNodeEdge> originalEdges = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getSaleProcessId, originalSaleProcessId)
                .eq(CallcenterSaleProcessNodeEdge::getIsDeleted, false)
                .list();

        if (originalEdges.isEmpty()) {
            log.info("原流程没有找到边数据，流程ID: {}", originalSaleProcessId);
            return;
        }

        // 2. 创建新的边
        List<CallcenterSaleProcessNodeEdge> newEdges = new ArrayList<>();

        for (CallcenterSaleProcessNodeEdge originalEdge : originalEdges) {
            String newFromNodeId = nodeIdMapping.get(originalEdge.getFromNodeId());
            String newToNodeId = nodeIdMapping.get(originalEdge.getToNodeId());

            // 只有当起始节点和目标节点都存在映射时才创建新边
            if (newFromNodeId != null && newToNodeId != null) {
                CallcenterSaleProcessNodeEdge newEdge = new CallcenterSaleProcessNodeEdge();
                BeanUtil.copyProperties(originalEdge, newEdge);

                // 设置新的属性
                newEdge.setId(null); // 让数据库自动生成新ID
                newEdge.setSaleProcessId(newSaleProcessId);
                newEdge.setFromNodeId(newFromNodeId);
                newEdge.setToNodeId(newToNodeId);
                newEdge.setCreateTime(LocalDateTime.now());
                newEdge.setUpdateTime(LocalDateTime.now());

                newEdges.add(newEdge);
            } else {
                log.warn("边的节点映射不完整，跳过复制。原边ID: {}, fromNodeId: {}, toNodeId: {}",
                        originalEdge.getId(), originalEdge.getFromNodeId(), originalEdge.getToNodeId());
            }
        }

        // 3. 批量保存新边
        if (!newEdges.isEmpty()) {
            iCallcenterSaleProcessNodeEdgeService.saveBatch(newEdges);
            log.info("成功复制 {} 个节点边", newEdges.size());
        }
    }

    @Override
    public void validateNode(Long saleProcessId) {
        if (Objects.isNull(saleProcessId)) {
            throw new BusinessException(PARAM_INVALID.getCode(), "流程ID不能为空");
        }

        // 1. 查询流程的所有节点
        List<CallcenterSaleProcessNode> allNodes = this.lambdaQuery()
                .eq(CallcenterSaleProcessNode::getSaleProcessId, saleProcessId)
                .eq(CallcenterSaleProcessNode::getIsDeleted, 0)
                .list();

        if (allNodes.isEmpty()) {
            throw new BusinessException(PARAM_INVALID.getCode(), "流程中没有配置节点");
        }

        // 2. 检查开始节点和结束节点
        List<CallcenterSaleProcessNode> startNodes = allNodes.stream()
                .filter(node -> node.getType().equals(SaleProcessNodeTypeEnum.START.getCode()))
                .collect(Collectors.toList());

        List<CallcenterSaleProcessNode> endNodes = allNodes.stream()
                .filter(node -> node.getType().equals(SaleProcessNodeTypeEnum.END.getCode()))
                .collect(Collectors.toList());

        // 2.1 必须有且只有一个开始节点
        if (startNodes.isEmpty()) {
            throw new BusinessException(SALE_PROCESS_NO_START_NODE);
        }
        if (startNodes.size() > 1) {
            throw new BusinessException(SALE_PROCESS_MULTIPLE_START_NODES);
        }

        // 2.2 必须有至少一个结束节点
        if (endNodes.isEmpty()) {
            throw new BusinessException(SALE_PROCESS_NO_END_NODE);
        }

        CallcenterSaleProcessNode startNode = startNodes.get(0);

        // 3. 查询所有边
        List<CallcenterSaleProcessNodeEdge> allEdges = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getSaleProcessId, saleProcessId)
                .eq(CallcenterSaleProcessNodeEdge::getIsDeleted, false)
                .list();

        if (allEdges.isEmpty()) {
            throw new BusinessException(SALE_PROCESS_NODE_NOT_CONNECTED);
        }

        // 4. 构建节点ID集合，方便查询
        Set<String> allNodeIds = allNodes.stream()
                .map(CallcenterSaleProcessNode::getId)
                .collect(Collectors.toSet());

        // 5. 检查每个节点（除结束节点外）是否都有出边
        Set<String> nodesWithOutgoingEdge = allEdges.stream()
                .map(CallcenterSaleProcessNodeEdge::getFromNodeId)
                .collect(Collectors.toSet());

        List<String> nodesWithoutOutgoingEdge = new ArrayList<>();
        for (CallcenterSaleProcessNode node : allNodes) {
            // 结束节点不需要出边
            if (node.getType().equals(SaleProcessNodeTypeEnum.END.getCode())) {
                continue;
            }
            // 其他节点必须有出边
            if (!nodesWithOutgoingEdge.contains(node.getId())) {
                nodesWithoutOutgoingEdge.add(node.getId() + "(" + node.getName() + ")");
            }
        }

        if (!nodesWithoutOutgoingEdge.isEmpty()) {
            throw new BusinessException(PARAM_INVALID.getCode(),
                    "以下节点没有连接到其他节点: " + String.join(", ", nodesWithoutOutgoingEdge));
        }

        // 6. 检查是否有结束节点有出边（结束节点不应该有出边）
        List<String> endNodesWithOutgoingEdge = endNodes.stream()
                .map(CallcenterSaleProcessNode::getId)
                .filter(nodesWithOutgoingEdge::contains)
                .map(nodeId -> {
                    CallcenterSaleProcessNode node = allNodes.stream()
                            .filter(n -> n.getId().equals(nodeId))
                            .findFirst()
                            .orElse(null);
                    return nodeId + "(" + (node != null ? node.getName() : "未知") + ")";
                })
                .collect(Collectors.toList());

        if (!endNodesWithOutgoingEdge.isEmpty()) {
            throw new BusinessException(PARAM_INVALID.getCode(),
                    "以下结束节点不应该有出边: " + String.join(", ", endNodesWithOutgoingEdge));
        }

        // 7. 构建图结构，检查是否存在孤立节点（无法从开始节点到达）
        Map<String, Set<String>> graph = buildGraph(allEdges);
        Set<String> reachableNodes = findReachableNodes(graph, startNode.getId());

        // 7.1 检查是否所有节点都可达
        List<String> unreachableNodes = new ArrayList<>();
        for (CallcenterSaleProcessNode node : allNodes) {
            if (!reachableNodes.contains(node.getId())) {
                unreachableNodes.add(node.getId() + "(" + node.getName() + ")");
            }
        }

        if (!unreachableNodes.isEmpty()) {
            throw new BusinessException(PARAM_INVALID.getCode(),
                    "以下节点无法从开始节点到达（孤立节点）: " + String.join(", ", unreachableNodes));
        }

        // 8. 检查是否至少有一条路径从开始节点到结束节点
        boolean hasPathToEnd = endNodes.stream()
                .anyMatch(endNode -> reachableNodes.contains(endNode.getId()));

        if (!hasPathToEnd) {
            throw new BusinessException(PARAM_INVALID.getCode(),
                    "流程配置错误：没有从开始节点到结束节点的路径");
        }

        // 9. 检查边的有效性（边的起始和结束节点都必须存在）
        List<String> invalidEdges = new ArrayList<>();
        for (CallcenterSaleProcessNodeEdge edge : allEdges) {
            if (!allNodeIds.contains(edge.getFromNodeId()) ||
                    !allNodeIds.contains(edge.getToNodeId())) {
                invalidEdges.add("边ID: " + edge.getId() +
                        " (from: " + edge.getFromNodeId() + ", to: " + edge.getToNodeId() + ")");
            }
        }

        if (!invalidEdges.isEmpty()) {
            throw new BusinessException(PARAM_INVALID.getCode(),
                    "以下边引用了不存在的节点: " + String.join(", ", invalidEdges));
        }

        log.info("流程节点校验通过，流程ID: {}, 节点数: {}, 边数: {}",
                saleProcessId, allNodes.size(), allEdges.size());
        // 10. 验证节点条件？
        validateNodeConditions(allNodes, saleProcessId);
    }

    /**
     * 校验节点条件项配置
     * 普通节点和关键节点必须有条件项，开始节点和结束节点不需要条件项
     *
     * @param allNodes 所有节点
     * @param saleProcessId 流程ID
     */
    private void validateNodeConditions(List<CallcenterSaleProcessNode> allNodes, Long saleProcessId) {
        // 1. 筛选出需要配置条件项的节点（开始节点、普通节点和关键节点）
        List<CallcenterSaleProcessNode> nodesNeedingConditions = allNodes.stream()
                .filter(node -> !node.getType().equals(SaleProcessNodeTypeEnum.END.getCode()))
                .collect(Collectors.toList());

        if (nodesNeedingConditions.isEmpty()) {
            // 如果没有需要条件项的节点，直接返回
            throw new BusinessException(PARAM_INVALID.getCode(), "缺少条件项");
        }

        // 2. 查询所有节点的条件项
        List<String> nodeIdsNeedingConditions = nodesNeedingConditions.stream()
                .map(CallcenterSaleProcessNode::getId)
                .collect(Collectors.toList());

        List<CallcenterSaleProcessNodeCondition> allConditions =
                iCallcenterSaleProcessNodeConditionService.lambdaQuery()
                        .in(CallcenterSaleProcessNodeCondition::getNodeId, nodeIdsNeedingConditions)
                        .eq(CallcenterSaleProcessNodeCondition::getIsDeleted, 0)
                        .list();

        // 3. 构建节点ID到条件列表的映射
        Map<String, List<CallcenterSaleProcessNodeCondition>> nodeConditionsMap = allConditions.stream()
                .collect(Collectors.groupingBy(CallcenterSaleProcessNodeCondition::getNodeId));

        // 4. 检查哪些节点没有配置条件项
        List<String> nodesWithoutConditions = new ArrayList<>();
        for (CallcenterSaleProcessNode node : nodesNeedingConditions) {
            List<CallcenterSaleProcessNodeCondition> conditions = nodeConditionsMap.get(node.getId());

            if (conditions == null || conditions.isEmpty()) {
                String nodeTypeDesc = Objects.requireNonNull(SaleProcessNodeTypeEnum.getByCode(node.getType())).getMessage();
                nodesWithoutConditions.add(node.getId() + "(" + node.getName() + " - " + nodeTypeDesc + ")");
            }
        }

        if (!nodesWithoutConditions.isEmpty()) {
            throw new BusinessException(PARAM_INVALID.getCode(),
                    "以下节点没有配置条件项: " + String.join(", ", nodesWithoutConditions));
        }

        // 5. 可选：检查条件项的配置是否合法（例如条件配置的JSON格式是否正确）
        List<String> nodesWithInvalidConditions = new ArrayList<>();
        for (CallcenterSaleProcessNode node : nodesNeedingConditions) {
            List<CallcenterSaleProcessNodeCondition> conditions = nodeConditionsMap.get(node.getId());

            if (conditions != null) {
                for (CallcenterSaleProcessNodeCondition condition : conditions) {
                    // 检查条件配置是否为空或无效
                    if (StringUtils.isBlank(condition.getConditions())) {
                        nodesWithInvalidConditions.add(
                                node.getId() + "(" + node.getName() + ") - 条件项ID: " + condition.getId() +
                                        " (" + condition.getName() + ") 的条件配置为空");
                    } else {
                        // 检查条件配置的JSON格式是否正确
                        try {
                            UnifiedConditionReq conditionReq = JsonUtilExt.jsonToBean(
                                    condition.getConditions(), UnifiedConditionReq.class);
                            if (conditionReq == null) {
                                nodesWithInvalidConditions.add(
                                        node.getId() + "(" + node.getName() + ") - 条件项ID: " + condition.getId() +
                                                " (" + condition.getName() + ") 的条件配置格式错误");
                            }
                        } catch (Exception e) {
                            nodesWithInvalidConditions.add(
                                    node.getId() + "(" + node.getName() + ") - 条件项ID: " + condition.getId() +
                                            " (" + condition.getName() + ") 的条件配置JSON格式错误: " + e.getMessage());
                        }
                    }
                }
            }
        }

        if (!nodesWithInvalidConditions.isEmpty()) {
            throw new BusinessException(PARAM_INVALID.getCode(),
                    "以下节点的条件项配置无效: " + String.join("; ", nodesWithInvalidConditions));
        }

        log.info("节点条件项校验通过，流程ID: {}, 需要条件项的节点数: {}, 条件项总数: {}",
                saleProcessId, nodesNeedingConditions.size(), allConditions.size());
    }

    /**
     * 构建图结构（邻接表）
     * @param edges 边列表
     * @return 图结构 Map<起始节点ID, Set<目标节点ID>>
     */
    private Map<String, Set<String>> buildGraph(List<CallcenterSaleProcessNodeEdge> edges) {
        Map<String, Set<String>> graph = new HashMap<>();

        for (CallcenterSaleProcessNodeEdge edge : edges) {
            String fromNodeId = edge.getFromNodeId();
            String toNodeId = edge.getToNodeId();

            if (StringUtils.isNotBlank(fromNodeId) && StringUtils.isNotBlank(toNodeId)) {
                graph.computeIfAbsent(fromNodeId, k -> new HashSet<>()).add(toNodeId);
            }
        }

        return graph;
    }

    /**
     * 从起始节点开始，查找所有可达的节点（BFS广度优先搜索）
     * @param graph 图结构
     * @param startNodeId 起始节点ID
     * @return 所有可达节点ID的集合
     */
    private Set<String> findReachableNodes(Map<String, Set<String>> graph, String startNodeId) {
        Set<String> reachable = new HashSet<>();
        Set<String> visited = new HashSet<>();
        List<String> queue = new ArrayList<>();

        queue.add(startNodeId);
        visited.add(startNodeId);
        reachable.add(startNodeId);

        while (!queue.isEmpty()) {
            String currentNodeId = queue.remove(0);
            Set<String> neighbors = graph.getOrDefault(currentNodeId, new HashSet<>());

            for (String neighbor : neighbors) {
                if (!visited.contains(neighbor)) {
                    visited.add(neighbor);
                    reachable.add(neighbor);
                    queue.add(neighbor);
                }
            }
        }

        return reachable;
    }
}
