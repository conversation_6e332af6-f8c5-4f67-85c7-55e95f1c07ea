package com.yirendai.workbench.service.callcenter.impl;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @since : 2025/9/15 13:29
 */

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.yirendai.voiceaiserver.mapper.owmuc.UserRegistryInfoMapper;
import com.yirendai.voiceaiserver.model.owmuc.UserRegistryInfo;
import com.yirendai.workbench.entity.CallCenterWechatContact;
import com.yirendai.workbench.entity.callcenter.CallcenterWechatCustomerMapping;
import com.yirendai.workbench.mapper.callcenter.CallcenterWechatCustomerMappingMapper;
import com.yirendai.workbench.service.callcenter.ICallcenterWechatCustomerMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 微信客户id映射关系表 服务实现类
 * </p>
 */
@Slf4j
@Service
public class CallcenterWechatCustomerMappingServiceImpl extends ServiceImpl<CallcenterWechatCustomerMappingMapper, CallcenterWechatCustomerMapping>
        implements ICallcenterWechatCustomerMappingService {


    @Resource
    private UserRegistryInfoMapper userRegistryInfoMapper;

    // 客户ID匹配的正则表达式模式（按优先级排序）
    private static final List<Pattern> CUSTOMER_ID_PATTERNS = Arrays.asList(
            // 1. 独立的长数字（5-12位），前后有分隔符
            Pattern.compile("(?:^|[^\\d])(\\d{3,12})(?=[^\\d]|$)"),
            // 2. 宜人贷等关键词后的数字
            Pattern.compile("(?:宜人贷|宜人|客户).*?(\\d{3,12})"),
            // 3. 纯数字模式，但要排除明显的年龄、电话号码等
            Pattern.compile("(\\d{8,11})")
    );

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveWechatContactUpdateChangeEvent(List<CallCenterWechatContact> wechatContacts, String tenantId) {
        if (CollectionUtils.isEmpty(wechatContacts)) {
            log.info("没有符合条件的微信联系人数据需要处理映射关系");
            return;
        }

        log.info("开始处理微信客户映射关系匹配，租户ID: {}, 数据量: {}", tenantId, wechatContacts.size());

        // 1. 分离备注为空和有备注的数据
        Map<Boolean, List<CallCenterWechatContact>> partitionedContacts = wechatContacts.stream()
                .filter(contact -> StringUtils.isNotBlank(contact.getCustomerWechatId()))
                .collect(Collectors.partitioningBy(contact -> StringUtils.isNotBlank(contact.getCustomerRemark())));

        List<CallCenterWechatContact> contactsWithRemark = partitionedContacts.get(true);
        List<CallCenterWechatContact> contactsWithoutRemark = partitionedContacts.get(false);

        // 2. 处理备注为空的数据 - 清空映射关系
        if (!contactsWithoutRemark.isEmpty()) {
            handleEmptyRemarkContacts(contactsWithoutRemark, tenantId);
        }

        // 3. 处理有备注的数据 - 正常映射关系处理
        if (!contactsWithRemark.isEmpty()) {
            handleContactsWithRemark(contactsWithRemark, tenantId);
        }

        log.info("微信客户映射关系处理完成");
    }

    /**
     * 处理备注为空的联系人 - 清空对应的映射关系
     */
    private void handleEmptyRemarkContacts(List<CallCenterWechatContact> contactsWithoutRemark, String tenantId) {
        log.info("开始处理备注为空的微信联系人，数量: {}", contactsWithoutRemark.size());
        // 获取所有备注为空的微信ID
        Set<String> emptyRemarkWechatIds = contactsWithoutRemark.stream()
                .map(CallCenterWechatContact::getCustomerWechatId)
                .collect(Collectors.toSet());

        if (emptyRemarkWechatIds.isEmpty()) {
            return;
        }

        // 查询这些微信ID对应的所有映射关系
        List<CallcenterWechatCustomerMapping> existingMappings = this.list(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CallcenterWechatCustomerMapping>()
                        .eq(CallcenterWechatCustomerMapping::getTenantId, tenantId)
                        .in(CallcenterWechatCustomerMapping::getWechatOriginalId, emptyRemarkWechatIds)
        );

        if (existingMappings.isEmpty()) {
            log.info("备注为空的微信ID没有对应的映射关系，无需清理");
            return;
        }

        // 找出所有非手动标注的映射关系进行删除
        List<Long> idsToDelete = existingMappings.stream()
                .filter(mapping -> !Boolean.TRUE.equals(mapping.getIsMarked()))
                .map(CallcenterWechatCustomerMapping::getId)
                .collect(Collectors.toList());

        // 统计手动标注的映射关系（保留）
        Map<String, Long> manualMarkedCount = existingMappings.stream()
                .filter(mapping -> Boolean.TRUE.equals(mapping.getIsMarked()))
                .collect(Collectors.groupingBy(
                        CallcenterWechatCustomerMapping::getWechatOriginalId,
                        Collectors.counting()
                ));

        // 执行删除操作
        if (!idsToDelete.isEmpty()) {
            this.removeByIds(idsToDelete);
        }
    }

    /**
     * 处理有备注的联系人 - 正常映射关系处理
     */
    private void handleContactsWithRemark(List<CallCenterWechatContact> contactsWithRemark, String tenantId) {
        log.info("开始处理有备注的微信联系人，数量: {}", contactsWithRemark.size());

        // 2. 批量查询现有的映射关系
        Set<String> wechatIds = contactsWithRemark.stream()
                .map(CallCenterWechatContact::getCustomerWechatId)
                .collect(Collectors.toSet());

        Map<String, List<CallcenterWechatCustomerMapping>> existingMappingsMap = this.list(
                        new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CallcenterWechatCustomerMapping>()
                                .eq(CallcenterWechatCustomerMapping::getTenantId, tenantId)
                                .in(CallcenterWechatCustomerMapping::getWechatOriginalId, wechatIds))
                .stream()
                .collect(Collectors.groupingBy(CallcenterWechatCustomerMapping::getWechatOriginalId));

        // 3. 提取所有潜在的客户ID并批量验证有效性
        Set<String> allExtractedCustomerIds = new HashSet<>();
        Map<String, List<String>> contactToCustomerIdsMap = new HashMap<>();

        for (CallCenterWechatContact contact : contactsWithRemark) {
            List<String> extractedCustomerIds = extractAllCustomerIdsFromRemark(contact.getCustomerRemark());
            if (!extractedCustomerIds.isEmpty()) {
                contactToCustomerIdsMap.put(contact.getCustomerWechatId(), extractedCustomerIds);
                allExtractedCustomerIds.addAll(extractedCustomerIds);
            }
        }

        // 4. 批量查询用户注册信息，验证客户ID有效性
        Set<String> validCustomerIds = batchValidateCustomerIds(allExtractedCustomerIds);

        // 5. 处理每个联系人的映射关系
        for (CallCenterWechatContact contact : contactsWithRemark) {
            String wechatId = contact.getCustomerWechatId();
            List<CallcenterWechatCustomerMapping> existingMappings = existingMappingsMap.getOrDefault(wechatId, Collections.emptyList());

            // 获取该联系人的客户ID列表，并过滤出有效的客户ID
            List<String> extractedCustomerIds = contactToCustomerIdsMap.getOrDefault(wechatId, Collections.emptyList());
            List<String> validExtractedCustomerIds = extractedCustomerIds.stream()
                    .filter(validCustomerIds::contains)
                    .collect(Collectors.toList());

            if (validExtractedCustomerIds.isEmpty()) {
                // 如果无法提取有效客户ID，删除已存在的非手动标注映射关系
                List<Long> idsToDelete = existingMappings.stream()
                        .filter(mapping -> !Boolean.TRUE.equals(mapping.getIsMarked()))
                        .map(CallcenterWechatCustomerMapping::getId)
                        .collect(Collectors.toList());

                if (!idsToDelete.isEmpty()) {
                    this.removeByIds(idsToDelete);
                }
                continue;
            }
            // 处理多客户ID映射（支持在手动标注基础上新增映射）
            processMultipleCustomerIdMappings(contact, validExtractedCustomerIds, existingMappings, tenantId);
        }
    }

    /**
     * 处理备注为空的情况，清理非手动标注的映射关系
     */
    private void handleEmptyRemarkCleanup(String wechatId, List<CallcenterWechatCustomerMapping> existingMappings, String currentRemark) {
        if (CollectionUtils.isEmpty(existingMappings)) {
            log.debug("微信ID: {} 没有现有映射关系，无需清理", wechatId);
            return;
        }

        // 找出所有非手动标注的映射关系进行删除
        List<Long> idsToDelete = existingMappings.stream()
                .filter(mapping -> !Boolean.TRUE.equals(mapping.getIsMarked()))
                .map(CallcenterWechatCustomerMapping::getId)
                .collect(Collectors.toList());

        if (!idsToDelete.isEmpty()) {
            this.removeByIds(idsToDelete);
            log.info("清理微信备注为空的映射关系，微信ID: {}, 删除数量: {}, 当前备注: '{}'",
                    wechatId, idsToDelete.size(), StringUtils.isBlank(currentRemark) ? "空" : currentRemark);
        } else {
            log.debug("微信ID: {} 的所有映射关系都是手动标注，保留不删除", wechatId);
        }

        // 统计保留的手动标注映射关系数量
        long manualMarkedCount = existingMappings.stream()
                .filter(mapping -> Boolean.TRUE.equals(mapping.getIsMarked()))
                .count();

        if (manualMarkedCount > 0) {
            log.info("微信ID: {} 保留手动标注的映射关系 {} 条", wechatId, manualMarkedCount);
        }
    }

    /**
     * 批量验证客户ID的有效性
     */
    private Set<String> batchValidateCustomerIds(Set<String> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Collections.emptySet();
        }
        try {
            // 将字符串客户ID转换为Long类型进行查询
            List<Long> customerIdLongs = customerIds.stream()
                    .map(id -> {
                        try {
                            return Long.parseLong(id);
                        } catch (NumberFormatException e) {
                            log.warn("客户ID格式错误，无法转换为数字: {}", id);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (customerIdLongs.isEmpty()) {
                return Collections.emptySet();
            }

            // 批量查询用户注册信息
            List<UserRegistryInfo> userRegistryInfos = userRegistryInfoMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<UserRegistryInfo>()
                            .in(UserRegistryInfo::getUserId, customerIdLongs)
                            .eq(UserRegistryInfo::getUserStatus, "1") // 只查询状态为"已激活"的用户
            );

            // 将查询结果转换为字符串集合
            return userRegistryInfos.stream()
                    .map(UserRegistryInfo::getUserId)
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.toSet());

        } catch (Exception e) {
            log.error("批量验证客户ID时发生异常", e);
            return Collections.emptySet();
        }
    }

    private void processMultipleCustomerIdMappings(CallCenterWechatContact contact, List<String> validCustomerIds, List<CallcenterWechatCustomerMapping> existingMappings, String tenantId) {
        String wechatId = contact.getCustomerWechatId();
        // 将现有映射按客户ID分组
        Map<String, CallcenterWechatCustomerMapping> existingByCustomerId = existingMappings.stream()
                .collect(Collectors.toMap(
                        CallcenterWechatCustomerMapping::getCustomerId,
                        mapping -> mapping,
                        (e1, e2) -> e2
                ));

        // 需要新增的映射
        List<CallcenterWechatCustomerMapping> mappingsToAdd = new ArrayList<>();
        // 需要更新的映射
        List<CallcenterWechatCustomerMapping> mappingsToUpdate = new ArrayList<>();
        // 需要删除的映射ID
        List<Long> mappingsToDelete = new ArrayList<>();
        // 处理每个有效的客户ID
        for (String customerId : validCustomerIds) {
            CallcenterWechatCustomerMapping existingMapping = existingByCustomerId.get(customerId);
            if (existingMapping != null) {
                // 只处理非手动标注的映射关系
                if (!Boolean.TRUE.equals(existingMapping.getIsMarked()) && needsUpdate(existingMapping, contact, customerId)) {
                    CallcenterWechatCustomerMapping mappingToUpdate = new CallcenterWechatCustomerMapping();
                    mappingToUpdate.setId(existingMapping.getId());
                    mappingToUpdate.setWechatOriginalId(contact.getCustomerWechatId());
                    mappingToUpdate.setWechatNo(contact.getCustomerWechatNo());
                    mappingToUpdate.setWechatRemark(contact.getCustomerRemark());
                    mappingToUpdate.setCustomerId(customerId);
                    mappingToUpdate.setTenantId(tenantId);
                    mappingToUpdate.setIsMarked(false);
                    mappingToUpdate.setUpdateTime(LocalDateTime.now());
                    mappingsToUpdate.add(mappingToUpdate);
                }
            } else {
                // 新增映射关系
                CallcenterWechatCustomerMapping mappingToAdd = new CallcenterWechatCustomerMapping();
                mappingToAdd.setWechatOriginalId(contact.getCustomerWechatId());
                mappingToAdd.setWechatNo(contact.getCustomerWechatNo());
                mappingToAdd.setWechatRemark(contact.getCustomerRemark());
                mappingToAdd.setCustomerId(customerId);
                mappingToAdd.setTenantId(tenantId);
                mappingToAdd.setIsMarked(false);
                mappingToAdd.setCreateTime(LocalDateTime.now());
                mappingToAdd.setUpdateTime(LocalDateTime.now());
                mappingsToAdd.add(mappingToAdd);
            }
        }

        // 找出需要删除的映射（存在于数据库但不在有效客户ID中，且非手动标注）
        Set<String> validCustomerIdSet = new HashSet<>(validCustomerIds);
        for (CallcenterWechatCustomerMapping existingMapping : existingMappings) {
            if (!Boolean.TRUE.equals(existingMapping.getIsMarked()) && !validCustomerIdSet.contains(existingMapping.getCustomerId())) {
                mappingsToDelete.add(existingMapping.getId());
                log.debug("标记删除映射关系，微信ID: {}, 客户ID: {}", wechatId, existingMapping.getCustomerId());
            }
        }

        // 执行数据库操作
        if (!mappingsToAdd.isEmpty()) {
            this.saveBatch(mappingsToAdd);
            log.info("新增微信客户映射关系: {} 条，微信ID: {}", mappingsToAdd.size(), wechatId);
        }

        if (!mappingsToUpdate.isEmpty()) {
            this.updateBatchById(mappingsToUpdate);
            log.info("更新微信客户映射关系: {} 条，微信ID: {}", mappingsToUpdate.size(), wechatId);
        }

        if (!mappingsToDelete.isEmpty()) {
            this.removeByIds(mappingsToDelete);
            log.info("删除微信客户映射关系: {} 条，微信ID: {}", mappingsToDelete.size(), wechatId);
        }
    }

    /**
     * 判断映射关系是否需要更新
     */
    private boolean needsUpdate(CallcenterWechatCustomerMapping existingMapping,
                                CallCenterWechatContact contact,
                                String extractedCustomerId) {
        // 如果是手动标注的，不允许自动更新
        if (Boolean.TRUE.equals(existingMapping.getIsMarked())) {
            return false;
        }

        // 检查是否有任何字段发生变化
        boolean customerIdChanged = !Objects.equals(extractedCustomerId, existingMapping.getCustomerId());
        boolean remarkChanged = !Objects.equals(contact.getCustomerRemark(), existingMapping.getWechatRemark());
        boolean wechatNoChanged = !Objects.equals(contact.getCustomerWechatNo(), existingMapping.getWechatNo());

        return customerIdChanged || remarkChanged || wechatNoChanged;
    }

    /**
     * 从备注中提取所有客户ID
     *
     * @param remark 微信备注内容
     * @return 提取到的客户ID列表，如果未找到返回空列表
     */
    private List<String> extractAllCustomerIdsFromRemark(String remark) {
        if (StringUtils.isBlank(remark)) {
            return Collections.emptyList();
        }

        try {
            Set<String> customerIds = new LinkedHashSet<>(); // 使用LinkedHashSet保持顺序并去重

            // 预处理：清理备注内容
            String cleanRemark = preprocessRemark(remark);

            // 按优先级匹配客户ID
            for (Pattern pattern : CUSTOMER_ID_PATTERNS) {
                List<String> candidates = findAllMatches(pattern, cleanRemark);

                for (String candidate : candidates) {
                    if (isValidCustomerId(candidate)) {
                        customerIds.add(candidate);
                        log.debug("成功提取客户ID: {} from remark: {}", candidate, remark);
                    }
                }
            }

            return new ArrayList<>(customerIds);
        } catch (Exception e) {
            log.warn("提取客户ID时发生异常，备注: {}, 异常: {}", remark, e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 预处理备注内容
     */
    private String preprocessRemark(String remark) {
        return remark.trim()
                .replaceAll("\\s+", " ") // 多个空格替换为单个空格
                .replaceAll("[，。；：！？]", ","); // 统一标点符号
    }

    /**
     * 查找所有匹配项
     */
    private List<String> findAllMatches(Pattern pattern, String text) {
        List<String> matches = new ArrayList<>();
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            String match = matcher.group(1); // 获取第一个捕获组
            if (match != null && !match.isEmpty()) {
                matches.add(match);
            }
        }
        return matches;
    }

    /**
     * 验证是否为有效的客户ID
     */
    private boolean isValidCustomerId(String candidate) {
        if (StringUtils.isBlank(candidate)) {
            return false;
        }

        // 基础长度检查：修改为3-12位
        if (candidate.length() < 3 || candidate.length() > 12) {
            return false;
        }
        try {
            long id = Long.parseLong(candidate);

            // 排除明显的年龄数字（18-99）
            if (id >= 18 && id <= 99) {
                return false;
            }

            // 排除手机号码（使用正则表达式匹配）
            if (isMobileNumber(candidate)) {
                return false;
            }


            // 排除固定电话（7-8位，或以0开头的10-11位）
            if (candidate.startsWith("0") && candidate.length() >= 10 && candidate.length() <= 11) {
                return false;
            }

            // 排除400和800开头的服务电话
            if ((candidate.startsWith("400") && candidate.length() >= 7 && candidate.length() <= 11) ||
                    (candidate.startsWith("800") && candidate.length() >= 7 && candidate.length() <= 11)) {
                return false;
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为手机号码
     * 手机号码规则：11位，以1开头，第二位为3-9
     */
    private boolean isMobileNumber(String number) {
        if (number.length() != 11) {
            return false;
        }
        // 手机号码正则：1[3-9]\d{9}
        Pattern mobilePattern = Pattern.compile("^1[3-9]\\d{9}$");
        return mobilePattern.matcher(number).matches();
    }

    @Override
    public String getCustomerIdByWechatOriginalId(String wechatOriginalId) {
        if (StringUtils.isBlank(wechatOriginalId)) {
            return null;
        }

        CallcenterWechatCustomerMapping mapping = this.getOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CallcenterWechatCustomerMapping>()
                        .eq(CallcenterWechatCustomerMapping::getWechatOriginalId, wechatOriginalId)
                        .orderByDesc(CallcenterWechatCustomerMapping::getUpdateTime)
                        .last("limit 1")
        );

        return mapping != null ? mapping.getCustomerId() : null;
    }

    @Override
    public String getCustomerIdByWechatNo(String wechatNo) {
        if (StringUtils.isBlank(wechatNo)) {
            return null;
        }

        CallcenterWechatCustomerMapping mapping = this.getOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CallcenterWechatCustomerMapping>()
                        .eq(CallcenterWechatCustomerMapping::getWechatNo, wechatNo)
                        .orderByDesc(CallcenterWechatCustomerMapping::getUpdateTime)
                        .last("limit 1")
        );

        return mapping != null ? mapping.getCustomerId() : null;
    }

    @Override
    public Set<String> getCustomerIdsByWechatNo(String wechatNo) {
        List<CallcenterWechatCustomerMapping> list = this.lambdaQuery().eq(CallcenterWechatCustomerMapping::getWechatOriginalId, wechatNo).list();
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptySet();
        }
        return list.stream().map(CallcenterWechatCustomerMapping::getCustomerId).collect(Collectors.toSet());
    }
}
