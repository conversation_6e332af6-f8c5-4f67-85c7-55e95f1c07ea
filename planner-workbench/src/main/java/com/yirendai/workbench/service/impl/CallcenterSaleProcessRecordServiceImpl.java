package com.yirendai.workbench.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterSaleProcessNode;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeCondition;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeConditionRecord;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeEdge;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeRecord;
import com.yirendai.workbench.entity.CallcenterSaleProcessRecord;
import com.yirendai.workbench.entity.CallcenterSalesProcess;
import com.yirendai.workbench.enums.SaleProcessNodeClearanceRuleEnum;
import com.yirendai.workbench.enums.SaleProcessNodeTypeEnum;
import com.yirendai.workbench.enums.SaleProcessStatusEnum;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.job.SaleProcessJob;
import com.yirendai.workbench.job.vo.ConditionResult;
import com.yirendai.workbench.job.vo.InitProcessResult;
import com.yirendai.workbench.job.vo.ProcessNodeResult;
import com.yirendai.workbench.mapper.CallcenterSaleProcessRecordMapper;
import com.yirendai.workbench.readonly.entity.PlannerUserBindReadonly;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeConditionRecordService;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeConditionService;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeEdgeService;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeRecordService;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeService;
import com.yirendai.workbench.service.ICallcenterSaleProcessRecordService;
import com.yirendai.workbench.service.callcenter.CallcenterSalesProcessService;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.ManualAdvanceConditionReq;
import com.yirendai.workbench.vo.req.UnifiedConditionReq;
import com.yirendai.workbench.vo.req.salesProcess.ApiReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.params.SetParams;

import static com.yirendai.workbench.exception.BusinessError.MANUAL_ADVANCE_CONDITION_FAIL;
import static com.yirendai.workbench.exception.BusinessError.MANUAL_FINISH_PROCESS_ERROR;
import static com.yirendai.workbench.exception.BusinessError.MANUAL_FINISH_PROCESS_STATUS_ERROR;
import static com.yirendai.workbench.exception.BusinessError.PARAM_NULL_ERROR;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_IS_END;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_IS_NOT_EXIST;
import static com.yirendai.workbench.exception.BusinessError.SALE_PROCESS_IS_PROCESSING;

/**
 * <p>
 * 销售流程进度记录表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2025-10-10
 */
@Service
@Slf4j
@RefreshScope
public class CallcenterSaleProcessRecordServiceImpl
        extends ServiceImpl<CallcenterSaleProcessRecordMapper, CallcenterSaleProcessRecord>
        implements ICallcenterSaleProcessRecordService {

    @Resource
    CallcenterSalesProcessService callcenterSalesProcessService;

    @Resource
    ICallcenterSaleProcessNodeService iCallcenterSaleProcessNodeService;

    @Resource
    ICallcenterSaleProcessNodeConditionService iCallcenterSaleProcessNodeConditionService;

    @Resource
    ICallcenterSaleProcessNodeRecordService iCallcenterSaleProcessNodeRecordService;

    @Resource
    ICallcenterSaleProcessNodeConditionRecordService iCallcenterSaleProcessNodeConditionRecordService;


    @Resource
    ConditionProcessorService conditionProcessorService;

    @Resource
    ICallcenterSaleProcessNodeEdgeService iCallcenterSaleProcessNodeEdgeService;

    @Resource
    ICallcenterSaleProcessRecordService iCallcenterSaleProcessRecordService;

    @Resource
    private JedisCluster jedisCluster;

    @Resource
    private ICallcenterSaleProcessRecordService self;

    @Value("${sale.process.startTime}")
    private String startTime;


    @Override
    public void saleProcess(CallcenterSalesProcess callcenterSalesProcess, PlannerUserBindReadonly plannerUserBind) {
        long startTime = System.currentTimeMillis();

        log.info("[流程处理-开始] userId={}, plannerName={}, processId={}, processNo={}, versionNo={}, processName={}",
                plannerUserBind.getUserId(),
                plannerUserBind.getPlannerName(),
                callcenterSalesProcess.getId(),
                callcenterSalesProcess.getProcessNo(),
                callcenterSalesProcess.getVersionNo(),
                callcenterSalesProcess.getProcessName());

        try {
            // 1. 查询用户是否已有该流程编号的记录
            CallcenterSaleProcessRecord existingRecord = this.lambdaQuery()
                    .eq(CallcenterSaleProcessRecord::getProcessNo, callcenterSalesProcess.getProcessNo())
                    .eq(CallcenterSaleProcessRecord::getUserId, plannerUserBind.getUserId())
                    .eq(CallcenterSaleProcessRecord::getTenantId, callcenterSalesProcess.getTenantId())
                    .one();

            // 2. 判断是否处理该流程版本
            if (existingRecord != null) {
                log.debug("[版本判断] 用户已有流程记录: recordId={}, recordProcessId={}, recordStatus={}, currentNodeId={}, progressValue={}",
                        existingRecord.getId(),
                        existingRecord.getSaleProcessId(),
                        existingRecord.getStatus(),
                        existingRecord.getCurrentNodeId(),
                        existingRecord.getProgressValue());

                // 用户已进入流程
                if (!existingRecord.getSaleProcessId().equals(callcenterSalesProcess.getId())) {
                    // 用户在其他版本中，跳过当前版本
                    log.info("[流程处理-跳过] 用户已在其他版本: userId={}, processNo={}, userProcessId={}, userStatus={}, currentProcessId={}, reason=版本不匹配",
                            plannerUserBind.getUserId(),
                            callcenterSalesProcess.getProcessNo(),
                            existingRecord.getSaleProcessId(),
                            getStatusName(existingRecord.getStatus()),
                            callcenterSalesProcess.getId());
                    return;
                }

                // 用户在当前版本中，继续处理
                log.info("[版本判断-继续] 用户在当前版本: userId={}, processNo={}, versionNo={}, recordStatus={}, currentNode={}, progress={}%",
                        plannerUserBind.getUserId(),
                        callcenterSalesProcess.getProcessNo(),
                        callcenterSalesProcess.getVersionNo(),
                        getStatusName(existingRecord.getStatus()),
                        existingRecord.getCurrentNodeId(),
                        existingRecord.getProgressValue());

            } else {
                log.debug("[版本判断] 用户无流程记录，判断是否使用最新版本: isDefault={}",
                        callcenterSalesProcess.getDefaultProcess());

                // 用户未进入流程，只处理最新版本
                if (callcenterSalesProcess.getDefaultProcess() != 1) {
                    log.info("[流程处理-跳过] 用户未进入流程且非最新版本: userId={}, processNo={}, versionNo={}, isDefault={}, reason=非最新版本",
                            plannerUserBind.getUserId(),
                            callcenterSalesProcess.getProcessNo(),
                            callcenterSalesProcess.getVersionNo(),
                            callcenterSalesProcess.getDefaultProcess());
                    return;
                }

                log.info("[版本判断-新用户] 用户首次进入最新版本: userId={}, processNo={}, versionNo={}, processName={}",
                        plannerUserBind.getUserId(),
                        callcenterSalesProcess.getProcessNo(),
                        callcenterSalesProcess.getVersionNo(),
                        callcenterSalesProcess.getProcessName());
            }

            // 3. 初始化流程和节点记录
            log.debug("[初始化] 开始初始化流程和节点记录: userId={}, processId={}",
                    plannerUserBind.getUserId(),
                    callcenterSalesProcess.getId());

            InitProcessResult initResult = self.initProcessAndNodeRecord(callcenterSalesProcess, plannerUserBind);

            if (initResult == null) {
                // 流程已完成，无需处理
                if (existingRecord != null) {
                    log.info("[流程处理-完成] 流程已结束，无需处理: userId={}, processNo={}, versionNo={}, finalStatus={}, endTime={}, progress={}%",
                            plannerUserBind.getUserId(),
                            callcenterSalesProcess.getProcessNo(),
                            callcenterSalesProcess.getVersionNo(),
                            getStatusName(existingRecord.getStatus()),
                            existingRecord.getEndTime(),
                            existingRecord.getProgressValue());
                } else {
                    log.info("[流程处理-完成] 流程已结束: userId={}, processNo={}, versionNo={}",
                            plannerUserBind.getUserId(),
                            callcenterSalesProcess.getProcessNo(),
                            callcenterSalesProcess.getVersionNo());
                }
                return;
            }

            log.debug("[初始化成功] 流程记录初始化完成: userId={}, recordId={}, nodeId={}, nodeName={}, nodeType={}",
                    plannerUserBind.getUserId(),
                    initResult.getProcessRecord().getId(),
                    initResult.getNode().getId(),
                    initResult.getNode().getName(),
                    initResult.getNode().getType());

            // 4. 处理流程节点
            log.debug("[节点处理] 开始处理流程节点: userId={}, processId={}, currentNode={}",
                    plannerUserBind.getUserId(),
                    callcenterSalesProcess.getId(),
                    initResult.getNode().getName());

            dealSaleProcess(
                    callcenterSalesProcess,
                    initResult.getNode(),
                    initResult.getProcessRecord(),
                    initResult.getNodeRecord(),
                    plannerUserBind
            );

            // 5. 查询最新状态记录处理结果
            CallcenterSaleProcessRecord finalRecord = this.getById(initResult.getProcessRecord().getId());
            long costTime = System.currentTimeMillis() - startTime;

            if (finalRecord.getStatus() == SaleProcessStatusEnum.PROCESSING.getCode()) {
                log.info("[流程处理-进行中] userId={}, processNo={}, versionNo={}, currentNode={}, progress={}%, costTime={}ms",
                        plannerUserBind.getUserId(),
                        callcenterSalesProcess.getProcessNo(),
                        callcenterSalesProcess.getVersionNo(),
                        finalRecord.getCurrentNodeId(),
                        finalRecord.getProgressValue(),
                        costTime);
            } else {
                log.info("[流程处理-完成] userId={}, processNo={}, versionNo={}, finalStatus={}, progress={}%, costTime={}ms",
                        plannerUserBind.getUserId(),
                        callcenterSalesProcess.getProcessNo(),
                        callcenterSalesProcess.getVersionNo(),
                        getStatusName(finalRecord.getStatus()),
                        finalRecord.getProgressValue(),
                        costTime);
            }

        } catch (Exception e) {
            long costTime = System.currentTimeMillis() - startTime;
            log.error("[流程处理-异常] userId={}, plannerName={}, processId={}, processNo={}, versionNo={}, costTime={}ms",
                    plannerUserBind.getUserId(),
                    plannerUserBind.getPlannerName(),
                    callcenterSalesProcess.getId(),
                    callcenterSalesProcess.getProcessNo(),
                    callcenterSalesProcess.getVersionNo(),
                    costTime,
                    e);
            throw e;  // 重新抛出异常，让上层处理
        }
    }

    /**
     * 初始化流程和节点记录（事务保护）
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InitProcessResult initProcessAndNodeRecord(
            CallcenterSalesProcess callcenterSalesProcess,
            PlannerUserBindReadonly plannerUserBind) {

        log.debug("[初始化流程] userId={}, processId={}, processNo={}",
                plannerUserBind.getUserId(),
                callcenterSalesProcess.getId(),
                callcenterSalesProcess.getProcessNo());

        // 1. 查询或创建流程记录
        CallcenterSaleProcessRecord callcenterSaleProcessRecord = this.lambdaQuery()
                .eq(CallcenterSaleProcessRecord::getProcessNo, callcenterSalesProcess.getProcessNo())
                .eq(CallcenterSaleProcessRecord::getUserId, plannerUserBind.getUserId())
                .eq(CallcenterSaleProcessRecord::getTenantId, callcenterSalesProcess.getTenantId())
                .one();

        if (Objects.isNull(callcenterSaleProcessRecord)) {
            // 创建新的流程记录
            log.info("[创建流程记录] userId={}, processId={}, processNo={}",
                    plannerUserBind.getUserId(),
                    callcenterSalesProcess.getId(),
                    callcenterSalesProcess.getProcessNo());

            callcenterSaleProcessRecord = new CallcenterSaleProcessRecord();
            callcenterSaleProcessRecord.setTenantId(callcenterSalesProcess.getTenantId());
            callcenterSaleProcessRecord.setUserId(plannerUserBind.getUserId());
            callcenterSaleProcessRecord.setSaleProcessId(callcenterSalesProcess.getId());
            callcenterSaleProcessRecord.setProcessNo(callcenterSalesProcess.getProcessNo());
            callcenterSaleProcessRecord.setProgressValue(BigDecimal.ZERO);

            if (StringUtils.isNotBlank(startTime)) {
                callcenterSaleProcessRecord.setStartTime(
                        LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } else {
                callcenterSaleProcessRecord.setStartTime(LocalDateTime.now());
            }

            callcenterSaleProcessRecord.setStatus(SaleProcessStatusEnum.PROCESSING.getCode());

            CallcenterSaleProcessNode startNode = iCallcenterSaleProcessNodeService.lambdaQuery()
                    .eq(CallcenterSaleProcessNode::getSaleProcessId, callcenterSalesProcess.getId())
                    .eq(CallcenterSaleProcessNode::getIsDeleted, 0)
                    .eq(CallcenterSaleProcessNode::getType, SaleProcessNodeTypeEnum.START.getCode())
                    .one();

            if (startNode == null) {
                log.error("[初始化失败] 未找到开始节点: userId={}, processId={}",
                        plannerUserBind.getUserId(),
                        callcenterSalesProcess.getId());
                throw new RuntimeException("流程开始节点不存在");
            }

            callcenterSaleProcessRecord.setCurrentNodeId(startNode.getId());
            this.save(callcenterSaleProcessRecord);

            log.info("[流程记录创建成功] recordId={}, userId={}, startNodeId={}, startTime={}",
                    callcenterSaleProcessRecord.getId(),
                    plannerUserBind.getUserId(),
                    startNode.getId(),
                    callcenterSaleProcessRecord.getStartTime());
        } else {
            log.debug("[流程记录已存在] recordId={}, status={}, currentNode={}, progress={}%",
                    callcenterSaleProcessRecord.getId(),
                    getStatusName(callcenterSaleProcessRecord.getStatus()),
                    callcenterSaleProcessRecord.getCurrentNodeId(),
                    callcenterSaleProcessRecord.getProgressValue());
        }

        // 2. 检查流程状态
        if (callcenterSaleProcessRecord.getStatus() != SaleProcessStatusEnum.PROCESSING.getCode()) {
            log.info("[流程已结束] recordId={}, userId={}, status={}, endTime={}",
                    callcenterSaleProcessRecord.getId(),
                    plannerUserBind.getUserId(),
                    getStatusName(callcenterSaleProcessRecord.getStatus()),
                    callcenterSaleProcessRecord.getEndTime());
            return null;  // 流程已完成，返回null
        }

        // 3. 查询或创建节点记录
        CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord =
                iCallcenterSaleProcessNodeRecordService.lambdaQuery()
                        .eq(CallcenterSaleProcessNodeRecord::getSaleProcessId, callcenterSalesProcess.getId())
                        .eq(CallcenterSaleProcessNodeRecord::getTenantId, callcenterSalesProcess.getTenantId())
                        .eq(CallcenterSaleProcessNodeRecord::getUserId, plannerUserBind.getUserId())
                        .eq(CallcenterSaleProcessNodeRecord::getStatus, 2)  // 未完成
                        .one();

        CallcenterSaleProcessNode callcenterSaleProcessNode = null;

        if (Objects.isNull(callcenterSaleProcessNodeRecord)) {
            // 创建开始节点记录
            log.debug("[创建节点记录] userId={}, processId={}, 创建开始节点",
                    plannerUserBind.getUserId(),
                    callcenterSalesProcess.getId());

            callcenterSaleProcessNodeRecord = new CallcenterSaleProcessNodeRecord();
            callcenterSaleProcessNodeRecord.setTenantId(callcenterSalesProcess.getTenantId());
            callcenterSaleProcessNodeRecord.setUserId(plannerUserBind.getUserId());
            callcenterSaleProcessNodeRecord.setSaleProcessId(callcenterSalesProcess.getId());

            callcenterSaleProcessNode = iCallcenterSaleProcessNodeService.lambdaQuery()
                    .eq(CallcenterSaleProcessNode::getSaleProcessId, callcenterSalesProcess.getId())
                    .eq(CallcenterSaleProcessNode::getType, SaleProcessNodeTypeEnum.START.getCode())
                    .one();

            if (callcenterSaleProcessNode == null) {
                log.error("[初始化失败] 未找到开始节点配置: processId={}", callcenterSalesProcess.getId());
                throw new RuntimeException("流程开始节点配置不存在");
            }

            callcenterSaleProcessNodeRecord.setNodeId(callcenterSaleProcessNode.getId());
            callcenterSaleProcessNodeRecord.setStatus(2);  // 未完成
            iCallcenterSaleProcessNodeRecordService.save(callcenterSaleProcessNodeRecord);

            log.info("[节点记录创建成功] nodeRecordId={}, userId={}, nodeId={}, nodeName={}",
                    callcenterSaleProcessNodeRecord.getId(),
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    callcenterSaleProcessNode.getName());
        } else {
            callcenterSaleProcessNode = iCallcenterSaleProcessNodeService.getById(
                    callcenterSaleProcessNodeRecord.getNodeId());

            log.debug("[节点记录已存在] nodeRecordId={}, nodeId={}, nodeName={}, status={}",
                    callcenterSaleProcessNodeRecord.getId(),
                    callcenterSaleProcessNode.getId(),
                    callcenterSaleProcessNode.getName(),
                    callcenterSaleProcessNodeRecord.getStatus());
        }

        // 4. 返回初始化结果
        log.debug("[初始化完成] userId={}, processRecordId={}, nodeRecordId={}, currentNode={}",
                plannerUserBind.getUserId(),
                callcenterSaleProcessRecord.getId(),
                callcenterSaleProcessNodeRecord.getId(),
                callcenterSaleProcessNode.getName());

        return InitProcessResult.create(
                callcenterSaleProcessNode,
                callcenterSaleProcessRecord,
                callcenterSaleProcessNodeRecord
        );
    }

    private void dealSaleProcess(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord, PlannerUserBindReadonly plannerUserBind) {
        //循环处理节点数据
        while (true) {
            ProcessNodeResult result = self.processNodeInTransaction(
                    callcenterSalesProcess,
                    callcenterSaleProcessNode,
                    callcenterSaleProcessRecord,
                    callcenterSaleProcessNodeRecord,
                    plannerUserBind
            );

            // 根据结果决定是否继续
            if (!result.isShouldContinue()) {
                break;
            }

            // 更新下一次循环的节点
            callcenterSaleProcessNode = result.getNextNode();
            callcenterSaleProcessNodeRecord = result.getNextNodeRecord();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProcessNodeResult processNodeInTransaction(
            CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord,
            PlannerUserBindReadonly plannerUserBind) {

        // 1. 处理节点
        boolean isDone = dealNode(callcenterSalesProcess, callcenterSaleProcessNode,
                callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord, plannerUserBind);

        if (!isDone) {
            return ProcessNodeResult.stop();
        }

        // 2. 校验流程完成状态
        CallcenterSaleProcessRecord updatedRecord = this.getById(callcenterSaleProcessRecord.getId());
        if (updatedRecord.getStatus() != SaleProcessStatusEnum.PROCESSING.getCode()) {
            return ProcessNodeResult.stop();
        }

        // 3. 查找下一个未完成的节点
        NodeSearchResult nodeSearchResult = findNextUnfinishedNode(
                callcenterSalesProcess, callcenterSaleProcessNode, updatedRecord);

        if (nodeSearchResult == null) {
            return ProcessNodeResult.stop();
        }

        // 4. 返回下一个节点信息
        return ProcessNodeResult.continueWith(
                nodeSearchResult.getNode(),
                nodeSearchResult.getNodeRecord()
        );
    }

    /**
     * 查找下一个未完成的节点
     * @param callcenterSalesProcess 销售流程
     * @param currentNode 当前节点
     * @param callcenterSaleProcessRecord 流程记录
     * @return 节点搜索结果，如果没有找到返回null
     */
    private NodeSearchResult findNextUnfinishedNode(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode currentNode, CallcenterSaleProcessRecord callcenterSaleProcessRecord) {

        CallcenterSaleProcessNode nextNode = currentNode;

        // 循环查找下一个未完成的节点
        while (true) {
            CallcenterSaleProcessNodeEdge callcenterSaleProcessNodeEdge = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeEdge::getFromNodeId, nextNode.getId()).one();

            if (Objects.isNull(callcenterSaleProcessNodeEdge)) {
                // 没有下一个节点，返回null
                return null;
            }

            nextNode = iCallcenterSaleProcessNodeService.getById(callcenterSaleProcessNodeEdge.getToNodeId());
            // 检查下一个节点是否已经有记录
            CallcenterSaleProcessNodeRecord existingRecord = iCallcenterSaleProcessNodeRecordService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeRecord::getSaleProcessId, callcenterSalesProcess.getId())
                    .eq(CallcenterSaleProcessNodeRecord::getTenantId, callcenterSalesProcess.getTenantId())
                    .eq(CallcenterSaleProcessNodeRecord::getUserId, callcenterSaleProcessRecord.getUserId())
                    .eq(CallcenterSaleProcessNodeRecord::getNodeId, nextNode.getId()).one();

            if (Objects.isNull(existingRecord)) {
                // 节点记录不存在，创建未完成记录
                CallcenterSaleProcessNodeRecord newNodeRecord = new CallcenterSaleProcessNodeRecord();
                newNodeRecord.setTenantId(callcenterSalesProcess.getTenantId());
                newNodeRecord.setUserId(callcenterSaleProcessRecord.getUserId());
                newNodeRecord.setSaleProcessId(callcenterSalesProcess.getId());
                newNodeRecord.setNodeId(nextNode.getId());
                newNodeRecord.setStatus(2);
                iCallcenterSaleProcessNodeRecordService.save(newNodeRecord);

                // 更新流程记录的当前节点ID
                callcenterSaleProcessRecord.setCurrentNodeId(nextNode.getId());
                callcenterSaleProcessRecord.setUpdateTime(LocalDateTime.now());
                iCallcenterSaleProcessRecordService.updateById(callcenterSaleProcessRecord);

                return new NodeSearchResult(nextNode, newNodeRecord);
            }
            else if (existingRecord.getStatus() == 2) {
                // 节点记录存在且未完成，使用现有记录
                // 更新流程记录的当前节点ID
                callcenterSaleProcessRecord.setCurrentNodeId(nextNode.getId());
                callcenterSaleProcessRecord.setUpdateTime(LocalDateTime.now());
                iCallcenterSaleProcessRecordService.updateById(callcenterSaleProcessRecord);

                return new NodeSearchResult(nextNode, existingRecord);
            }
            else if (existingRecord.getStatus() == 1) {
                // 节点已完成，继续查找下一个节点
                continue;
            }
        }
    }

    private Boolean dealNode(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord, PlannerUserBindReadonly plannerUserBind) {
        switch (SaleProcessNodeClearanceRuleEnum.getByCode(callcenterSaleProcessNode.getClearanceRule()).get()) {
            case ALL:
                return dealAll(callcenterSalesProcess, callcenterSaleProcessNode, callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord, plannerUserBind);
            case ALL_OR_NEXT_CRITICAL:
                // 检查节点条件项均达标 OR 下级关键节点准出达标
                boolean allConditionsMet = dealAll(callcenterSalesProcess, callcenterSaleProcessNode, callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord, plannerUserBind);
                if (allConditionsMet) {
                    return true;
                }
                return dealNextCritical(callcenterSalesProcess, callcenterSaleProcessNode, callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord, plannerUserBind);
            default:
                return false;
        }
    }

    private Boolean dealAll(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord,
            PlannerUserBindReadonly plannerUserBind) {

        log.debug("[ALL规则-开始] userId={}, nodeId={}, nodeName={}", 
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId(),
                callcenterSaleProcessNode.getName());

        // 1. 批量查询所有条件
        List<CallcenterSaleProcessNodeCondition> conditionList =
                iCallcenterSaleProcessNodeConditionService.lambdaQuery()
                        .eq(CallcenterSaleProcessNodeCondition::getNodeId, callcenterSaleProcessNodeRecord.getNodeId())
                        .eq(CallcenterSaleProcessNodeCondition::getIsDeleted, 0)
                        .list();

        if (CollUtil.isEmpty(conditionList)) {
            log.warn("[ALL规则-无条件] userId={}, nodeId={}, nodeName={}, reason=节点无条件配置", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    callcenterSaleProcessNode.getName());
            return false;
        }
        
        log.info("[ALL规则-条件查询] userId={}, nodeId={}, conditionCount={}",
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId(),
                conditionList.size());

        // 2. 批量查询所有条件记录（一次查询）
        List<Long> conditionIds = conditionList.stream()
                .map(CallcenterSaleProcessNodeCondition::getId)
                .collect(Collectors.toList());

        List<CallcenterSaleProcessNodeConditionRecord> existingRecords =
                iCallcenterSaleProcessNodeConditionRecordService.lambdaQuery()
                        .eq(CallcenterSaleProcessNodeConditionRecord::getNodeId, callcenterSaleProcessNodeRecord.getNodeId())
                        .eq(CallcenterSaleProcessNodeConditionRecord::getTenantId, callcenterSaleProcessNodeRecord.getTenantId())
                        .eq(CallcenterSaleProcessNodeConditionRecord::getUserId, callcenterSaleProcessRecord.getUserId())
                        .in(CallcenterSaleProcessNodeConditionRecord::getConditionId, conditionIds)
                        .eq(CallcenterSaleProcessNodeConditionRecord::getIsDeleted, 0)
                        .list();
        
        log.info("[ALL规则-记录查询] userId={}, nodeId={}, existingRecordCount={}",
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId(),
                existingRecords.size());

        // 3. 构建Map便于查找
        Map<Long, CallcenterSaleProcessNodeConditionRecord> recordMap = existingRecords.stream()
                .collect(Collectors.toMap(
                        CallcenterSaleProcessNodeConditionRecord::getConditionId,
                        record -> record
                ));

        // 4. 找出需要新建的记录
        List<CallcenterSaleProcessNodeConditionRecord> newRecords = new ArrayList<>();
        for (CallcenterSaleProcessNodeCondition condition : conditionList) {
            if (!recordMap.containsKey(condition.getId())) {
                CallcenterSaleProcessNodeConditionRecord newRecord = new CallcenterSaleProcessNodeConditionRecord();
                newRecord.setTenantId(callcenterSaleProcessRecord.getTenantId());
                newRecord.setUserId(callcenterSaleProcessRecord.getUserId());
                newRecord.setSaleProcessId(callcenterSaleProcessRecord.getSaleProcessId());
                newRecord.setNodeId(callcenterSaleProcessNodeRecord.getNodeId());
                newRecord.setConditionId(condition.getId());
                newRecord.setStatus(2);
                newRecords.add(newRecord);
                recordMap.put(condition.getId(), newRecord);
            }
        }

        // 5. 批量插入新记录
        if (!newRecords.isEmpty()) {
            iCallcenterSaleProcessNodeConditionRecordService.saveBatch(newRecords);
            log.info("[ALL规则-创建记录] userId={}, nodeId={}, newRecordCount={}", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    newRecords.size());
        }

        // 6. 批量更新需要完成的记录
        List<CallcenterSaleProcessNodeConditionRecord> toUpdateRecords = new ArrayList<>();
        int completedCount = 0;
        int newCompletedCount = 0;

        for (CallcenterSaleProcessNodeCondition condition : conditionList) {
            CallcenterSaleProcessNodeConditionRecord record = recordMap.get(condition.getId());

            // 已完成的条件直接计数
            if (record.getStatus() == 1) {
                completedCount++;
                log.info("[ALL规则-条件已完成] userId={}, conditionId={}, conditionName={}, finishTime={}",
                        plannerUserBind.getUserId(),
                        condition.getId(),
                        condition.getName(),
                        record.getFinishTime());
                continue;
            }

            // 未完成的条件进行检查
            if (record.getStatus() == 2) {
                try {
                    UnifiedConditionReq conditionReq = JsonUtilExt.jsonToBean(
                            condition.getConditions(), UnifiedConditionReq.class);
                    ApiReq req = getApiReq(callcenterSaleProcessRecord, callcenterSaleProcessNode);
                    ConditionResult result = conditionProcessorService.processConditionWithTime(conditionReq, req);

                    if (result.isResult()) {
                        record.setFinisher(plannerUserBind.getPlannerName() + "_" + plannerUserBind.getPlannerId());
                        record.setStatus(1);
                        record.setExtraInfo(result.getRemark());
                        record.setFinishTime(result.getLatestCompleteTime());
                        toUpdateRecords.add(record);
                        completedCount++;
                        newCompletedCount++;
                        
                        log.info("[ALL规则-条件新完成] userId={}, conditionId={}, conditionName={}, finishTime={}, remark={}", 
                                plannerUserBind.getUserId(),
                                condition.getId(),
                                condition.getName(),
                                result.getLatestCompleteTime(),
                                result.getRemark());
                    } else {
                        log.info("[ALL规则-条件未完成] userId={}, conditionId={}, conditionName={}",
                                plannerUserBind.getUserId(),
                                condition.getId(),
                                condition.getName());
                    }
                } catch (Exception e) {
                    log.error("[ALL规则-条件检查异常] userId={}, conditionId={}, conditionName={}", 
                            plannerUserBind.getUserId(),
                            condition.getId(),
                            condition.getName(),
                            e);
                }
            }
        }

        // 7. 批量更新
        if (!toUpdateRecords.isEmpty()) {
            iCallcenterSaleProcessNodeConditionRecordService.updateBatchById(toUpdateRecords);
            log.info("[ALL规则-更新记录] userId={}, nodeId={}, nodeName={}, updateCount={}", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    callcenterSaleProcessNode.getName(),
                    toUpdateRecords.size());
        }

        // 8. 判断是否全部完成
        log.info("[ALL规则-完成统计] userId={}, nodeId={}, nodeName={}, 已完成={}/{}, 本次新完成={}", 
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId(),
                callcenterSaleProcessNode.getName(),
                completedCount,
                conditionList.size(),
                newCompletedCount);
        
        if (completedCount == conditionList.size()) {
            log.info("[ALL规则-节点准出] userId={}, nodeId={}, nodeName={}, reason=所有条件已完成", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    callcenterSaleProcessNode.getName());
            return completeCurrentNode(callcenterSalesProcess, callcenterSaleProcessNode,
                    callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord);
        }

        log.info("[ALL规则-节点未完成] userId={}, nodeId={}, nodeName={}, 剩余条件={}",
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId(),
                callcenterSaleProcessNode.getName(),
                conditionList.size() - completedCount);
        
        return false;
    }

    private ApiReq getApiReq(CallcenterSaleProcessRecord callcenterSaleProcessRecord, CallcenterSaleProcessNode callcenterSaleProcessNode) {
        ApiReq req = new ApiReq();
        req.setTenantId(callcenterSaleProcessRecord.getTenantId());
        req.setCustomerId(callcenterSaleProcessRecord.getUserId());
        req.setStartTime(getLastNodeFinishTime(callcenterSaleProcessNode, callcenterSaleProcessRecord));
        return req;
    }

    /**
     * 获取上一个节点的完成时间
     * 普通节点：取流程开始时间
     * 关键节点：取上级关键节点完成时间，若没有上级关键节点，默认取流程开始时间
     */
    private LocalDateTime getLastNodeFinishTime(CallcenterSaleProcessNode currentNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord) {

        // 如果是普通节点，直接返回流程开始时间
        if (!currentNode.getType().equals(SaleProcessNodeTypeEnum.CRITICAL.getCode())) {
            return callcenterSaleProcessRecord.getStartTime();
        }

        // 如果是关键节点，查找上级关键节点的完成时间
        LocalDateTime previousCriticalFinishTime = findPreviousCriticalNodeFinishTime(currentNode, callcenterSaleProcessRecord);

        // 如果找到上级关键节点完成时间，返回该时间；否则返回流程开始时间
        return Objects.nonNull(previousCriticalFinishTime) ? previousCriticalFinishTime : callcenterSaleProcessRecord.getStartTime();
    }

    /**
     * 查找上级关键节点的完成时间
     */
    private LocalDateTime findPreviousCriticalNodeFinishTime(CallcenterSaleProcessNode currentNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord) {

        // 1. 找到指向当前节点的边（即上一个节点）
        CallcenterSaleProcessNodeEdge incomingEdge = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getToNodeId, currentNode.getId()).one();

        if (Objects.isNull(incomingEdge)) {
            // 没有上级节点，可能是开始节点
            return null;
        }

        // 2. 获取上级节点
        CallcenterSaleProcessNode previousNode = iCallcenterSaleProcessNodeService.getById(incomingEdge.getFromNodeId());

        // 3. 递归查找上级关键节点
        return findPreviousCriticalNodeFinishTimeRecursive(previousNode, callcenterSaleProcessRecord);
    }

    /**
     * 递归查找上级关键节点的完成时间
     */
    private LocalDateTime findPreviousCriticalNodeFinishTimeRecursive(CallcenterSaleProcessNode currentNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord) {

        // 如果是开始节点，返回null（表示没有找到上级关键节点）
        if (currentNode.getType().equals(SaleProcessNodeTypeEnum.START.getCode())) {
            return null;
        }

        // 如果是关键节点，检查其是否已完成
        if (currentNode.getType().equals(SaleProcessNodeTypeEnum.CRITICAL.getCode())) {
            CallcenterSaleProcessNodeRecord criticalNodeRecord = iCallcenterSaleProcessNodeRecordService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeRecord::getSaleProcessId, callcenterSaleProcessRecord.getSaleProcessId())
                    .eq(CallcenterSaleProcessNodeRecord::getTenantId, callcenterSaleProcessRecord.getTenantId())
                    .eq(CallcenterSaleProcessNodeRecord::getUserId, callcenterSaleProcessRecord.getUserId())
                    .eq(CallcenterSaleProcessNodeRecord::getNodeId, currentNode.getId())
                    .eq(CallcenterSaleProcessNodeRecord::getStatus, 1) // 已完成
                    .one();

            // 如果关键节点已完成，返回其完成时间
            if (Objects.nonNull(criticalNodeRecord) && Objects.nonNull(criticalNodeRecord.getFinishTime())) {
                return criticalNodeRecord.getFinishTime();
            }
        }

        // 继续向上查找
        CallcenterSaleProcessNodeEdge incomingEdge = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getToNodeId, currentNode.getId()).one();

        if (Objects.isNull(incomingEdge)) {
            return null;
        }

        CallcenterSaleProcessNode previousNode = iCallcenterSaleProcessNodeService.getById(incomingEdge.getFromNodeId());
        return findPreviousCriticalNodeFinishTimeRecursive(previousNode, callcenterSaleProcessRecord);
    }

    private Boolean dealNextCritical(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord, PlannerUserBindReadonly plannerUserBind) {

        log.debug("[NEXT_CRITICAL-开始] userId={}, currentNodeId={}, currentNodeName={}, nodeType={}", 
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId(),
                callcenterSaleProcessNode.getName(),
                getNodeTypeName(callcenterSaleProcessNode.getType()));

        // 1. 找到当前节点的下一个节点
        CallcenterSaleProcessNodeEdge callcenterSaleProcessNodeEdge = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getFromNodeId, callcenterSaleProcessNode.getId()).one();

        if (Objects.isNull(callcenterSaleProcessNodeEdge)) {
            log.warn("[NEXT_CRITICAL-无下级] userId={}, currentNodeId={}, currentNodeName={}, reason=未找到下级节点", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    callcenterSaleProcessNode.getName());
            return false;
        }

        // 2. 获取下一个节点信息
        CallcenterSaleProcessNode nextNode = iCallcenterSaleProcessNodeService.getById(
                callcenterSaleProcessNodeEdge.getToNodeId());
        
        log.debug("[NEXT_CRITICAL-下级节点] userId={}, currentNodeId={}, nextNodeId={}, nextNodeName={}, nextNodeType={}", 
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId(),
                nextNode.getId(),
                nextNode.getName(),
                getNodeTypeName(nextNode.getType()));

        // 3. 检查下一个节点是否是关键节点
        if (!nextNode.getType().equals(SaleProcessNodeTypeEnum.CRITICAL.getCode())) {
            // 如果下一个节点不是关键节点，继续查找后续的关键节点
            log.info("[NEXT_CRITICAL-递归查找] userId={}, currentNodeId={}, nextNodeId={}, nextNodeName={}, reason=下级非关键节点继续递归", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    nextNode.getId(),
                    nextNode.getName());
            
            boolean criticalNodeCompleted = findAndCheckNextCriticalNode(callcenterSalesProcess, nextNode, callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord, plannerUserBind);
            
            if (criticalNodeCompleted) {
                // 只完成**当前节点A**，不完成中间的节点B、C
                log.info("[NEXT_CRITICAL-递归成功] userId={}, currentNodeId={}, currentNodeName={}, result=递归找到的关键节点已完成，当前节点准出", 
                        plannerUserBind.getUserId(),
                        callcenterSaleProcessNode.getId(),
                        callcenterSaleProcessNode.getName());
                return completeCurrentNode(callcenterSalesProcess, callcenterSaleProcessNode,
                        callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord);
            } else {
                log.info("[NEXT_CRITICAL-递归失败] userId={}, currentNodeId={}, currentNodeName={}, result=递归查找的关键节点未完成", 
                        plannerUserBind.getUserId(),
                        callcenterSaleProcessNode.getId(),
                        callcenterSaleProcessNode.getName());
                return false;
            }
        }
        
        log.info("[NEXT_CRITICAL-找到关键节点] userId={}, currentNodeId={}, criticalNodeId={}, criticalNodeName={}", 
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId(),
                nextNode.getId(),
                nextNode.getName());

        // 4. 检查关键节点是否已经完成
        CallcenterSaleProcessNodeRecord nextNodeRecord = iCallcenterSaleProcessNodeRecordService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeRecord::getSaleProcessId, callcenterSalesProcess.getId())
                .eq(CallcenterSaleProcessNodeRecord::getTenantId, callcenterSalesProcess.getTenantId())
                .eq(CallcenterSaleProcessNodeRecord::getUserId, callcenterSaleProcessRecord.getUserId())
                .eq(CallcenterSaleProcessNodeRecord::getNodeId, nextNode.getId()).one();

        // 5. 如果关键节点记录不存在，需要创建记录并检查是否能完成
        if (Objects.isNull(nextNodeRecord)) {
            log.debug("[NEXT_CRITICAL-创建记录] userId={}, criticalNodeId={}, criticalNodeName={}, reason=关键节点记录不存在", 
                    plannerUserBind.getUserId(),
                    nextNode.getId(),
                    nextNode.getName());
            
            // 创建关键节点记录
            nextNodeRecord = new CallcenterSaleProcessNodeRecord();
            nextNodeRecord.setTenantId(callcenterSalesProcess.getTenantId());
            nextNodeRecord.setUserId(callcenterSaleProcessRecord.getUserId());
            nextNodeRecord.setSaleProcessId(callcenterSalesProcess.getId());
            nextNodeRecord.setNodeId(nextNode.getId());
            nextNodeRecord.setStatus(2); // 未完成状态
            iCallcenterSaleProcessNodeRecordService.save(nextNodeRecord);
            
            log.debug("[NEXT_CRITICAL-记录已创建] userId={}, criticalNodeId={}, recordId={}, status=未完成", 
                    plannerUserBind.getUserId(),
                    nextNode.getId(),
                    nextNodeRecord.getId());

            // 检查关键节点是否能够完成
            log.debug("[NEXT_CRITICAL-检查完成] userId={}, criticalNodeId={}, criticalNodeName={}", 
                    plannerUserBind.getUserId(),
                    nextNode.getId(),
                    nextNode.getName());
            
            boolean criticalNodeCompleted = checkCriticalNodeCompletion(callcenterSalesProcess, nextNode,
                    callcenterSaleProcessRecord, nextNodeRecord, plannerUserBind);

            if (criticalNodeCompleted) {
                log.info("[NEXT_CRITICAL-关键节点完成] userId={}, currentNodeId={}, criticalNodeId={}, criticalNodeName={}, result=关键节点已完成，当前节点准出", 
                        plannerUserBind.getUserId(),
                        callcenterSaleProcessNode.getId(),
                        nextNode.getId(),
                        nextNode.getName());
                return completeCurrentNode(callcenterSalesProcess, callcenterSaleProcessNode,
                        callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord);
            } else {
                // 关键节点未完成，删除刚创建的未完成记录
                iCallcenterSaleProcessNodeRecordService.removeById(nextNodeRecord.getId());
                log.info("[NEXT_CRITICAL-关键节点未完成] userId={}, currentNodeId={}, criticalNodeId={}, criticalNodeName={}, recordId={}, action=删除未完成记录", 
                        plannerUserBind.getUserId(),
                        callcenterSaleProcessNode.getId(),
                        nextNode.getId(),
                        nextNode.getName(),
                        nextNodeRecord.getId());
            }
            return false;
        }
        
        log.debug("[NEXT_CRITICAL-记录已存在] userId={}, criticalNodeId={}, recordId={}, recordStatus={}", 
                plannerUserBind.getUserId(),
                nextNode.getId(),
                nextNodeRecord.getId(),
                nextNodeRecord.getStatus());

        // 6. 如果关键节点记录存在且状态为完成(1)，则当前节点也完成
        if (nextNodeRecord.getStatus() == 1) {
            log.info("[NEXT_CRITICAL-记录已完成] userId={}, currentNodeId={}, criticalNodeId={}, criticalNodeName={}, finishTime={}, result=关键节点已完成，当前节点准出", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    nextNode.getId(),
                    nextNode.getName(),
                    nextNodeRecord.getFinishTime());
            return completeCurrentNode(callcenterSalesProcess, callcenterSaleProcessNode,
                    callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord);
        }

        // 7. 如果关键节点记录存在但未完成，尝试处理关键节点
        log.debug("[NEXT_CRITICAL-尝试处理] userId={}, criticalNodeId={}, criticalNodeName={}, reason=记录存在但未完成", 
                plannerUserBind.getUserId(),
                nextNode.getId(),
                nextNode.getName());
        
        boolean criticalNodeCompleted = checkCriticalNodeCompletion(callcenterSalesProcess, nextNode,
                callcenterSaleProcessRecord, nextNodeRecord, plannerUserBind);

        if (criticalNodeCompleted) {
            log.info("[NEXT_CRITICAL-处理成功] userId={}, currentNodeId={}, criticalNodeId={}, criticalNodeName={}, result=关键节点处理完成，当前节点准出", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    nextNode.getId(),
                    nextNode.getName());
            return completeCurrentNode(callcenterSalesProcess, callcenterSaleProcessNode,
                    callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord);
        } else {
            // 关键节点最终未完成，删除未完成记录
            iCallcenterSaleProcessNodeRecordService.removeById(nextNodeRecord.getId());
            log.info("[NEXT_CRITICAL-处理失败] userId={}, currentNodeId={}, criticalNodeId={}, criticalNodeName={}, recordId={}, action=删除未完成记录", 
                    plannerUserBind.getUserId(),
                    callcenterSaleProcessNode.getId(),
                    nextNode.getId(),
                    nextNode.getName(),
                    nextNodeRecord.getId());
        }

        log.debug("[NEXT_CRITICAL-结束] userId={}, currentNodeId={}, result=当前节点未准出", 
                plannerUserBind.getUserId(),
                callcenterSaleProcessNode.getId());
        
        return false;
    }

    /**
     * 获取节点类型名称
     */
    private String getNodeTypeName(Integer nodeType) {
        if (nodeType == null) {
            return "未知";
        }
        SaleProcessNodeTypeEnum typeEnum = SaleProcessNodeTypeEnum.getByCode(nodeType);
        return typeEnum != null ? typeEnum.getMessage() : "未知(" + nodeType + ")";
    }

    private Boolean checkCriticalNodeCompletion(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode criticalNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord criticalNodeRecord, PlannerUserBindReadonly plannerUserBind) {

        // 根据关键节点的准出规则来检查是否完成
        SaleProcessNodeClearanceRuleEnum clearanceRule = SaleProcessNodeClearanceRuleEnum
                .getByCode(criticalNode.getClearanceRule()).orElse(null);

        if (Objects.isNull(clearanceRule)) {
            return false;
        }
        return dealNode(callcenterSalesProcess, criticalNode, callcenterSaleProcessRecord, criticalNodeRecord, plannerUserBind);
    }

    private Boolean completeCurrentNode(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord) {

        //更新销售流程、节点完成进度 =节点进度 or 节点条件累加
        BigDecimal progressValue = callcenterSaleProcessNode.getProgressValue();
        LocalDateTime nodeFinishTime = LocalDateTime.now(); // 默认完成时间
        log.info("更新前销售流程进度，processId: {}, userId {}, progressValue: {}", callcenterSalesProcess.getId(),
                callcenterSaleProcessRecord.getUserId(), callcenterSaleProcessRecord.getProgressValue());
        if (callcenterSalesProcess.getNodeCalType() == 2) {
            List<CallcenterSaleProcessNodeCondition> conditionList = iCallcenterSaleProcessNodeConditionService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeCondition::getNodeId, callcenterSaleProcessNodeRecord.getNodeId())
                    .eq(CallcenterSaleProcessNodeCondition::getIsDeleted, 0).list();
            progressValue = conditionList.stream().map(CallcenterSaleProcessNodeCondition::getProgressValue)
                    .filter(java.util.Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            progressValue = callcenterSaleProcessRecord.getProgressValue().add(progressValue);
        }

        // 根据节点的准出规则确定完成时间
        SaleProcessNodeClearanceRuleEnum clearanceRule = SaleProcessNodeClearanceRuleEnum
                .getByCode(callcenterSaleProcessNode.getClearanceRule()).orElse(null);

        if (Objects.nonNull(clearanceRule)) {
            switch (clearanceRule) {
                case ALL:
                    // 所有条件都完成，取最后一个完成条件的时间
                    nodeFinishTime = getLastConditionFinishTime(callcenterSaleProcessNodeRecord.getNodeId());
                    break;
                case ALL_OR_NEXT_CRITICAL:
                    // 需要判断是通过ALL条件完成还是通过NEXT_CRITICAL完成
                    nodeFinishTime = getAllOrNextCriticalFinishTime(callcenterSaleProcessNode, callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord);
                    break;
                default:
                    nodeFinishTime = LocalDateTime.now();
                    break;
            }
        }
        callcenterSaleProcessRecord.setProgressValue(progressValue);

        // 设置节点完成时间
        callcenterSaleProcessNodeRecord.setFinishTime(nodeFinishTime);
        callcenterSaleProcessNodeRecord.setStatus(1);
        callcenterSaleProcessNodeRecord.setUpdateTime(LocalDateTime.now());
        iCallcenterSaleProcessNodeRecordService.updateById(callcenterSaleProcessNodeRecord);
        log.info("更新后销售流程进度，processId: {}, userId {}, nodeId {}, progressValue: {}",
                callcenterSalesProcess.getId(), callcenterSaleProcessRecord.getUserId(),
                callcenterSaleProcessNode.getId(), progressValue);

        //更新流程表 是否已经完成
        CallcenterSaleProcessNodeEdge callcenterSaleProcessNodeEdge = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getFromNodeId, callcenterSaleProcessNodeRecord.getNodeId())
                .one();

        if (Objects.nonNull(callcenterSaleProcessNodeEdge)) {
            CallcenterSaleProcessNode nextNode = iCallcenterSaleProcessNodeService.getById(
                    callcenterSaleProcessNodeEdge.getToNodeId());
            if (nextNode.getType() == SaleProcessNodeTypeEnum.END.getCode()) {
                callcenterSaleProcessRecord.setStatus(1);
                callcenterSaleProcessRecord.setCurrentNodeId(nextNode.getId());
                // 流程完成时间设置为节点完成时间
                callcenterSaleProcessRecord.setEndTime(nodeFinishTime);
            }
        }

        callcenterSaleProcessRecord.setUpdateTime(LocalDateTime.now());
        iCallcenterSaleProcessRecordService.updateById(callcenterSaleProcessRecord);

        return true;
    }

    /**
     * 获取ALL_OR_NEXT_CRITICAL规则的完成时间
     * 优先检查ALL条件，如果ALL条件满足则取最后条件完成时间，否则取关键节点完成时间
     */
    private LocalDateTime getAllOrNextCriticalFinishTime(CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord) {

        // 1. 先检查是否所有条件都完成
        List<CallcenterSaleProcessNodeCondition> conditionList = iCallcenterSaleProcessNodeConditionService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeCondition::getNodeId, callcenterSaleProcessNodeRecord.getNodeId())
                .eq(CallcenterSaleProcessNodeCondition::getIsDeleted, 0).list();

        if (!conditionList.isEmpty()) {
            // 检查所有条件是否都完成
            long completedCount = conditionList.stream()
                    .mapToLong(condition -> iCallcenterSaleProcessNodeConditionRecordService.lambdaQuery()
                             .eq(CallcenterSaleProcessNodeConditionRecord::getNodeId, callcenterSaleProcessNodeRecord.getNodeId())
                             .eq(CallcenterSaleProcessNodeConditionRecord::getConditionId, condition.getId())
                             .eq(CallcenterSaleProcessNodeConditionRecord::getStatus, 1)
                             .eq(CallcenterSaleProcessNodeConditionRecord::getIsDeleted, 0)
                             .count())
                    .sum();

            // 如果所有条件都完成，返回最后完成条件的时间
            if (completedCount == conditionList.size()) {
                return getLastConditionFinishTime(callcenterSaleProcessNodeRecord.getNodeId());
            }
        }

        // 2. 如果ALL条件未满足，则是通过NEXT_CRITICAL完成的，获取关键节点完成时间
        return getNextCriticalNodeFinishTime(callcenterSaleProcessNode, callcenterSaleProcessRecord);
    }

    /**
     * 获取下级关键节点的完成时间
     */
    private LocalDateTime getNextCriticalNodeFinishTime(CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord) {

        // 1. 找到当前节点的下一个节点
        CallcenterSaleProcessNodeEdge callcenterSaleProcessNodeEdge = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getFromNodeId, callcenterSaleProcessNode.getId()).one();

        if (Objects.isNull(callcenterSaleProcessNodeEdge)) {
            return LocalDateTime.now();
        }

        // 2. 递归查找关键节点
        CallcenterSaleProcessNode nextNode = iCallcenterSaleProcessNodeService.getById(
                callcenterSaleProcessNodeEdge.getToNodeId());

        return findCriticalNodeFinishTime(nextNode, callcenterSaleProcessRecord);
    }

    /**
     * 递归查找关键节点的完成时间
     */
    private LocalDateTime findCriticalNodeFinishTime(CallcenterSaleProcessNode currentNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord) {

        // 如果是关键节点，获取其完成时间
        if (currentNode.getType().equals(SaleProcessNodeTypeEnum.CRITICAL.getCode())) {
            CallcenterSaleProcessNodeRecord criticalNodeRecord = iCallcenterSaleProcessNodeRecordService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeRecord::getSaleProcessId, callcenterSaleProcessRecord.getSaleProcessId())
                    .eq(CallcenterSaleProcessNodeRecord::getTenantId, callcenterSaleProcessRecord.getTenantId())
                    .eq(CallcenterSaleProcessNodeRecord::getUserId, callcenterSaleProcessRecord.getUserId())
                    .eq(CallcenterSaleProcessNodeRecord::getNodeId, currentNode.getId())
                    .eq(CallcenterSaleProcessNodeRecord::getStatus, 1) // 已完成
                    .one();

            if (Objects.nonNull(criticalNodeRecord) && Objects.nonNull(criticalNodeRecord.getFinishTime())) {
                return criticalNodeRecord.getFinishTime();
            }
        }

        // 如果不是关键节点或未完成，继续查找下一个节点
        CallcenterSaleProcessNodeEdge edge = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getFromNodeId, currentNode.getId()).one();

        if (Objects.isNull(edge)) {
            return LocalDateTime.now();
        }

        CallcenterSaleProcessNode nextNode = iCallcenterSaleProcessNodeService.getById(edge.getToNodeId());

        // 如果到达结束节点仍未找到关键节点
        if (nextNode.getType().equals(SaleProcessNodeTypeEnum.END.getCode())) {
            return LocalDateTime.now();
        }

        return findCriticalNodeFinishTime(nextNode, callcenterSaleProcessRecord);
    }



    // 获取最后一个完成条件的时间（ALL规则）
    private LocalDateTime getLastConditionFinishTime(String nodeId) {
        List<CallcenterSaleProcessNodeConditionRecord> completedConditions =
                iCallcenterSaleProcessNodeConditionRecordService.lambdaQuery()
                        .eq(CallcenterSaleProcessNodeConditionRecord::getNodeId, nodeId)
                        .eq(CallcenterSaleProcessNodeConditionRecord::getStatus, 1)
                        .eq(CallcenterSaleProcessNodeConditionRecord::getIsDeleted, 0)
                        .orderByDesc(CallcenterSaleProcessNodeConditionRecord::getFinishTime)
                        .list();

        if (!completedConditions.isEmpty() && Objects.nonNull(completedConditions.get(0).getFinishTime())) {
            return completedConditions.get(0).getFinishTime();
        }
        return LocalDateTime.now();
    }

    private Boolean findAndCheckNextCriticalNode(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode currentNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord, PlannerUserBindReadonly plannerUserBind) {

        // 递归查找下一个关键节点
        CallcenterSaleProcessNodeEdge edge = iCallcenterSaleProcessNodeEdgeService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeEdge::getFromNodeId, currentNode.getId()).one();

        if (Objects.isNull(edge)) {
            return false;
        }

        CallcenterSaleProcessNode nextNode = iCallcenterSaleProcessNodeService.getById(edge.getToNodeId());

        // 如果是关键节点，检查其完成状态
        if (nextNode.getType().equals(SaleProcessNodeTypeEnum.CRITICAL.getCode())) {
            CallcenterSaleProcessNodeRecord nextNodeRecord = iCallcenterSaleProcessNodeRecordService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeRecord::getSaleProcessId, callcenterSalesProcess.getId())
                    .eq(CallcenterSaleProcessNodeRecord::getTenantId, callcenterSalesProcess.getTenantId())
                    .eq(CallcenterSaleProcessNodeRecord::getUserId, callcenterSaleProcessRecord.getUserId())
                    .eq(CallcenterSaleProcessNodeRecord::getNodeId, nextNode.getId()).one();

            // 如果记录不存在，创建并检查
            if (Objects.isNull(nextNodeRecord)) {
                nextNodeRecord = new CallcenterSaleProcessNodeRecord();
                nextNodeRecord.setTenantId(callcenterSalesProcess.getTenantId());
                nextNodeRecord.setUserId(callcenterSaleProcessRecord.getUserId());
                nextNodeRecord.setSaleProcessId(callcenterSalesProcess.getId());
                nextNodeRecord.setNodeId(nextNode.getId());
                nextNodeRecord.setStatus(2);
                iCallcenterSaleProcessNodeRecordService.save(nextNodeRecord);

                // 检查关键节点是否能够完成
                Boolean isComplete = checkCriticalNodeCompletion(callcenterSalesProcess, nextNode,
                        callcenterSaleProcessRecord, nextNodeRecord, plannerUserBind);
                if (isComplete) {
                    return true;
                } else {
                    // 关键节点未完成，删除刚创建的未完成记录
                    iCallcenterSaleProcessNodeRecordService.removeById(nextNodeRecord.getId());
                    log.debug("递归查找：关键节点未完成，删除未完成记录，nodeId: {}, recordId: {}",
                            nextNode.getId(), nextNodeRecord.getId());
                    return false;
                }
            }

            // 如果已完成，返回true
            if (nextNodeRecord.getStatus() == 1) {
                return true;
            }

            // 如果记录存在但未完成，尝试处理
            Boolean isComplete = checkCriticalNodeCompletion(callcenterSalesProcess, nextNode,
                    callcenterSaleProcessRecord, nextNodeRecord, plannerUserBind);
            if (isComplete) {
                return true;
            } else {
                // 关键节点最终未完成，删除未完成记录
                iCallcenterSaleProcessNodeRecordService.removeById(nextNodeRecord.getId());
                log.debug("递归查找：关键节点最终未完成，删除未完成记录，nodeId: {}, recordId: {}",
                        nextNode.getId(), nextNodeRecord.getId());
                return false;
            }
        }

        // 如果不是关键节点，继续递归查找
        if (nextNode.getType().equals(SaleProcessNodeTypeEnum.END.getCode())) {
            // 到达结束节点仍未找到关键节点
            return false;
        }

        return findAndCheckNextCriticalNode(callcenterSalesProcess, nextNode, callcenterSaleProcessRecord, callcenterSaleProcessNodeRecord, plannerUserBind);
    }

    private boolean tryAcquireLock(String lockKey) {
        try {
            SetParams setParams = new SetParams().nx().ex(SaleProcessJob.LOCK_EXPIRE_TIME);
            String result = jedisCluster.set(lockKey, "1", setParams);

            return "OK".equals(result);

        } catch (Exception e) {
            log.error("尝试获取锁失败，lockKey: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 手动结束流程
     * @param processId 流程ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean manualFinishProcess(Long processId, String userId,  Integer finishStatus, String manualFinishReason) {
        // 1. 校验参数
        if (Objects.isNull(processId) || StringUtils.isBlank(userId)) {
            throw new BusinessException(PARAM_NULL_ERROR);
        }

        if (Objects.isNull(finishStatus) || (finishStatus != SaleProcessStatusEnum.SUCCESS.getCode()
                && finishStatus != SaleProcessStatusEnum.FAIL.getCode())) {
            throw new BusinessException(MANUAL_FINISH_PROCESS_STATUS_ERROR);
        }

        // 2. 构建锁的key
        String lockKey = SaleProcessJob.getLockKey(processId, userId);

        // 3. 检查并获取锁
        boolean lockAcquired = tryAcquireLock(lockKey);
        if (!lockAcquired) {
            throw new BusinessException(SALE_PROCESS_IS_PROCESSING);
        }

        try {
            // 4. 查询流程记录
            CallcenterSaleProcessRecord processRecord = this.lambdaQuery()
                    .eq(CallcenterSaleProcessRecord::getSaleProcessId, processId)
                    .eq(CallcenterSaleProcessRecord::getUserId, userId)
                    .one();

            if (Objects.isNull(processRecord)) {
                throw new BusinessException(SALE_PROCESS_IS_NOT_EXIST);
            }

            // 5. 检查流程状态
            if (processRecord.getStatus() != SaleProcessStatusEnum.PROCESSING.getCode()) {
                log.warn("流程已完成，无需手动结束，processId: {}, userId: {}", processId, userId);
                return true;
            }

            // 6. 手动结束流程、赢单修改进度值
            if(finishStatus == SaleProcessStatusEnum.SUCCESS.getCode()){
                processRecord.setProgressValue(BigDecimal.valueOf(100));
                // 查询结束节点并更新currentNodeId
                CallcenterSaleProcessNode endNode = iCallcenterSaleProcessNodeService.lambdaQuery()
                        .eq(CallcenterSaleProcessNode::getSaleProcessId, processId)
                        .eq(CallcenterSaleProcessNode::getType, SaleProcessNodeTypeEnum.END.getCode())
                        .eq(CallcenterSaleProcessNode::getIsDeleted, 0)
                        .one();
                if (Objects.nonNull(endNode)) {
                    processRecord.setCurrentNodeId(endNode.getId());
                    log.info("[手动完成流程-更新节点] 赢单设置当前节点为结束节点: processId={}, userId={}, endNodeId={}, endNodeName={}",
                            processId, userId, endNode.getId(), endNode.getName());
                } else {
                    log.warn("[手动完成流程-警告] 未找到结束节点，保持当前节点: processId={}, currentNodeId={}",
                            processId, processRecord.getCurrentNodeId());
                }
            }
            processRecord.setStatus(finishStatus);
            processRecord.setManualFinishReason(manualFinishReason);
            processRecord.setEndTime(LocalDateTime.now());
            processRecord.setUpdateTime(LocalDateTime.now());

            // 7. 更新流程记录
            boolean updateResult = this.updateById(processRecord);

            // 8. 只有在已赢单的情况下才处理节点记录
            if (finishStatus == SaleProcessStatusEnum.SUCCESS.getCode()) {
                finishLeftNodeRecord(processId, userId);
                log.info("手动结束流程成功（已赢单），已处理节点记录，processId: {}, userId: {}", processId, userId);
            } else {
                log.info("手动结束流程成功（已败单），未处理节点记录，processId: {}, userId: {}", processId, userId);
            }

            if (!updateResult) {
                log.error("手动结束流程失败，processId: {}, userId: {}, finishType: {}", processId, userId, finishStatus);
            }

            return updateResult;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("手动结束流程异常，processId: {}, userId: {}", processId, userId, e);
            throw new BusinessException(MANUAL_FINISH_PROCESS_ERROR);
        } finally {
            // 直接删除锁
            releaseProcessLock(lockKey);
        }
    }

    /**
     * 结束当前未完成的节点记录，并为未生成记录的节点创建完成记录
     * @param processId 流程ID
     * @param userId 用户ID
     */
    private void finishLeftNodeRecord(Long processId, String userId) {
        try {
            // 1. 查询流程的所有节点
            List<CallcenterSaleProcessNode> allNodes = iCallcenterSaleProcessNodeService.lambdaQuery()
                    .eq(CallcenterSaleProcessNode::getSaleProcessId, processId)
                    .eq(CallcenterSaleProcessNode::getIsDeleted, 0)
                    .list();

            if (allNodes.isEmpty()) {
                log.warn("未找到流程节点，processId: {}", processId);
                return;
            }

            // 2. 查询已存在的节点记录
            List<CallcenterSaleProcessNodeRecord> existingNodeRecords = iCallcenterSaleProcessNodeRecordService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeRecord::getSaleProcessId, processId)
                    .eq(CallcenterSaleProcessNodeRecord::getUserId, userId)
                    .list();

            // 3. 创建已存在节点记录的映射（nodeId -> record）
            Map<String, CallcenterSaleProcessNodeRecord> existingRecordMap = existingNodeRecords.stream()
                    .collect(Collectors.toMap(
                            CallcenterSaleProcessNodeRecord::getNodeId,
                            record -> record,
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

            LocalDateTime now = LocalDateTime.now();
            List<CallcenterSaleProcessNodeRecord> recordsToUpdate = new ArrayList<>();
            List<CallcenterSaleProcessNodeRecord> recordsToInsert = new ArrayList<>();

            // 4. 遍历所有节点，处理记录
            for (CallcenterSaleProcessNode node : allNodes) {
                CallcenterSaleProcessNodeRecord existingRecord = existingRecordMap.get(node.getId());

                if (existingRecord != null) {
                    // 已存在记录，如果未完成则更新为完成
                    if (existingRecord.getStatus() == 2) {
                        existingRecord.setStatus(1); // 完成状态
                        existingRecord.setFinishTime(now);
                        existingRecord.setUpdateTime(now);
                        recordsToUpdate.add(existingRecord);
                    }
                } else {
                    // 不存在记录，创建完成记录
                    CallcenterSaleProcessNodeRecord newRecord = new CallcenterSaleProcessNodeRecord();
                    newRecord.setTenantId(OwnAuthUtil.getTenantId()); // 需要获取租户ID
                    newRecord.setUserId(userId);
                    newRecord.setSaleProcessId(processId);
                    newRecord.setNodeId(node.getId());
                    newRecord.setStatus(1); // 完成状态
                    newRecord.setFinishTime(now);
                    newRecord.setCreateTime(now);
                    newRecord.setUpdateTime(now);
                    newRecord.setIsDeleted(0);
                    recordsToInsert.add(newRecord);
                }
            }

            // 5. 批量更新已存在的未完成记录
            if (!recordsToUpdate.isEmpty()) {
                boolean updateResult = iCallcenterSaleProcessNodeRecordService.updateBatchById(recordsToUpdate);
                if (updateResult) {
                    log.info("成功更新 {} 个未完成节点记录为完成状态，processId: {}, userId: {}",
                            recordsToUpdate.size(), processId, userId);
                } else {
                    log.warn("更新未完成节点记录失败，processId: {}, userId: {}", processId, userId);
                }
            }

            // 6. 批量插入新的完成记录
            if (!recordsToInsert.isEmpty()) {
                boolean insertResult = iCallcenterSaleProcessNodeRecordService.saveBatch(recordsToInsert);
                if (insertResult) {
                    log.info("成功创建 {} 个节点完成记录，processId: {}, userId: {}",
                            recordsToInsert.size(), processId, userId);
                } else {
                    log.warn("创建节点完成记录失败，processId: {}, userId: {}", processId, userId);
                }
            }

            log.info("节点记录处理完成，更新: {}, 新增: {}, processId: {}, userId: {}",
                    recordsToUpdate.size(), recordsToInsert.size(), processId, userId);

        } catch (Exception e) {
            log.error("结束当前节点记录异常，processId: {}, userId: {}", processId, userId, e);
        }
    }

    /**
     * 批量手动推进条件项
     * @param processId 流程ID
     * @param userId 用户ID
     * @param conditionItems 条件项列表
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean batchManualAdvanceCondition(Long processId, String userId,
            List<ManualAdvanceConditionReq.ConditionItemReq> conditionItems) {

        // 1. 校验参数
        if (Objects.isNull(processId) || StringUtils.isBlank(userId) ||
                CollUtil.isEmpty(conditionItems)) {
            throw new BusinessException(PARAM_NULL_ERROR);
        }

        // 2. 构建锁的key
        String lockKey = SaleProcessJob.getLockKey(processId, userId);

        // 3. 检查并获取锁
        boolean lockAcquired = tryAcquireLock(lockKey);
        if (!lockAcquired) {
            throw new BusinessException(SALE_PROCESS_IS_PROCESSING);
        }

        try {

            // 4. 查询流程记录
            CallcenterSaleProcessRecord processRecord = iCallcenterSaleProcessRecordService.lambdaQuery()
                    .eq(CallcenterSaleProcessRecord::getSaleProcessId, processId)
                    .eq(CallcenterSaleProcessRecord::getUserId, userId)
                    .eq(CallcenterSaleProcessRecord::getIsDeleted, 0)
                    .one();

            if (Objects.isNull(processRecord)) {
                throw new BusinessException(SALE_PROCESS_IS_NOT_EXIST);
            }

            if (processRecord.getStatus() != 0) {
                throw new BusinessException(SALE_PROCESS_IS_END);
            }

            // 5. 批量处理条件项
            Set<String> affectedNodeIds = new HashSet<>();
            LocalDateTime now = LocalDateTime.now();

            for (ManualAdvanceConditionReq.ConditionItemReq item : conditionItems) {
                // 查询条件配置
                CallcenterSaleProcessNodeCondition condition = iCallcenterSaleProcessNodeConditionService.getById(item.getConditionId());
                if (Objects.isNull(condition)) {
                    log.warn("条件配置不存在，conditionId: {}", item.getConditionId());
                    continue;
                }

                // 查询或创建条件记录
                CallcenterSaleProcessNodeConditionRecord conditionRecord = iCallcenterSaleProcessNodeConditionRecordService.lambdaQuery()
                        .eq(CallcenterSaleProcessNodeConditionRecord::getSaleProcessId, processId)
                        .eq(CallcenterSaleProcessNodeConditionRecord::getUserId, userId)
                        .eq(CallcenterSaleProcessNodeConditionRecord::getConditionId, item.getConditionId())
                        .eq(CallcenterSaleProcessNodeConditionRecord::getIsDeleted, 0)
                        .one();

                if (Objects.isNull(conditionRecord)) {
                    // 创建新的条件记录
                    conditionRecord = new CallcenterSaleProcessNodeConditionRecord();
                    conditionRecord.setTenantId(processRecord.getTenantId());
                    conditionRecord.setUserId(userId);
                    conditionRecord.setSaleProcessId(processId);
                    conditionRecord.setNodeId(condition.getNodeId());
                    conditionRecord.setConditionId(item.getConditionId());
                    conditionRecord.setCreateTime(now);
                }

                // 更新条件记录
                conditionRecord.setStatus(1); // 标记为完成
                conditionRecord.setFinishTime(now); // 记录完成时间
                conditionRecord.setFinishType(1); // 完成类型=手动推进
                conditionRecord.setUpdateTime(now);

                // 处理备注
                if (StringUtils.isNotBlank(item.getRemark())) {
                    conditionRecord.setRemark(item.getRemark());
                }

                // 处理附件 - 字符串拼接存储
                if (CollUtil.isNotEmpty(item.getAttachmentUrls())) {
                    String attachmentStr = String.join(",", item.getAttachmentUrls());
                    conditionRecord.setExtraInfo(attachmentStr);
                }

                // 保存或更新条件记录
                if (Objects.isNull(conditionRecord.getId())) {
                    iCallcenterSaleProcessNodeConditionRecordService.save(conditionRecord);
                } else {
                    iCallcenterSaleProcessNodeConditionRecordService.updateById(conditionRecord);
                }

                // 记录受影响的节点ID
                affectedNodeIds.add(condition.getNodeId());
            }

            // 6. 检查受影响节点是否完成，并判断流程是否完成
            for (String nodeId : affectedNodeIds) {
                checkAndCompleteNodeOnly(processRecord, nodeId);
            }

            log.info("批量手动推进条件项成功，processId: {}, userId: {}, 处理条件数: {}",
                    processId, userId, conditionItems.size());

            return true;

        } catch (Exception e) {
            log.error("批量手动推进条件项失败，processId: {}, userId: {}", processId, userId, e);
            throw new BusinessException(MANUAL_ADVANCE_CONDITION_FAIL);
        } finally {
            // 直接删除锁
            releaseProcessLock(lockKey);
        }
    }

    /**
     * 检查并完成节点（仅完成当前节点，不继续处理后续节点）
     */
    private void checkAndCompleteNodeOnly(CallcenterSaleProcessRecord processRecord, String nodeId) {
        // 查询节点配置
        CallcenterSaleProcessNode node = iCallcenterSaleProcessNodeService.getById(nodeId);
        if (Objects.isNull(node)) {
            return;
        }

        // 查询节点记录
        CallcenterSaleProcessNodeRecord nodeRecord = iCallcenterSaleProcessNodeRecordService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeRecord::getSaleProcessId, processRecord.getSaleProcessId())
                .eq(CallcenterSaleProcessNodeRecord::getUserId, processRecord.getUserId())
                .eq(CallcenterSaleProcessNodeRecord::getNodeId, nodeId)
                .eq(CallcenterSaleProcessNodeRecord::getIsDeleted, 0)
                .one();

        if (Objects.isNull(nodeRecord) || nodeRecord.getStatus() == 1) {
            return; // 节点记录不存在或已完成
        }

        // 查询流程配置
        CallcenterSalesProcess salesProcess = callcenterSalesProcessService.getById(processRecord.getSaleProcessId());
        if (Objects.isNull(salesProcess)) {
            return;
        }

        // 检查节点是否满足完成条件
        boolean isNodeComplete = checkAllConditionsComplete(nodeId, processRecord.getUserId());

        if (isNodeComplete) {
            // 仅完成当前节点，不继续处理后续节点,包含流程处理过程
            completeCurrentNode(salesProcess, node, processRecord, nodeRecord);
            // 当前节点完成，流程没完成时，需要生成下一节点未完成记录
            if (processRecord.getStatus() == SaleProcessStatusEnum.PROCESSING.getCode()) {
                createNextUnfinishedNodeRecord(salesProcess, node, processRecord);
            }
        }
    }

    /**
     * 创建下一个未完成节点记录
     * @param callcenterSalesProcess 销售流程
     * @param currentNode 当前已完成的节点
     * @param callcenterSaleProcessRecord 流程记录
     */
    private void createNextUnfinishedNodeRecord(CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode currentNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord) {

        try {
            // 查找下一个未完成的节点
            NodeSearchResult nodeSearchResult = findNextUnfinishedNode(callcenterSalesProcess,
                    currentNode, callcenterSaleProcessRecord);

            if (nodeSearchResult != null) {
                log.info("已为流程创建下一个未完成节点记录，processId: {}, userId: {}, nodeId: {}",
                        callcenterSalesProcess.getId(),
                        callcenterSaleProcessRecord.getUserId(),
                        nodeSearchResult.getNode().getId());
            } else {
                log.info("流程已到达末尾，无需创建下一节点记录，processId: {}, userId: {}",
                        callcenterSalesProcess.getId(),
                        callcenterSaleProcessRecord.getUserId());
            }
        } catch (Exception e) {
            log.error("创建下一个未完成节点记录失败，processId: {}, userId: {}, currentNodeId: {}",
                    callcenterSalesProcess.getId(),
                    callcenterSaleProcessRecord.getUserId(),
                    currentNode.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 检查所有条件是否完成
     */
    private boolean checkAllConditionsComplete(String nodeId, String userId) {
        List<CallcenterSaleProcessNodeCondition> conditionList = iCallcenterSaleProcessNodeConditionService.lambdaQuery()
                .eq(CallcenterSaleProcessNodeCondition::getNodeId, nodeId)
                .eq(CallcenterSaleProcessNodeCondition::getIsDeleted, 0)
                .list();

        if (conditionList.isEmpty()) {
            return true; // 没有条件则认为完成
        }

        for (CallcenterSaleProcessNodeCondition condition : conditionList) {
            CallcenterSaleProcessNodeConditionRecord conditionRecord = iCallcenterSaleProcessNodeConditionRecordService.lambdaQuery()
                    .eq(CallcenterSaleProcessNodeConditionRecord::getUserId, userId)
                    .eq(CallcenterSaleProcessNodeConditionRecord::getConditionId, condition.getId())
                    .eq(CallcenterSaleProcessNodeConditionRecord::getIsDeleted, 0)
                    .one();

            if (Objects.isNull(conditionRecord) || conditionRecord.getStatus() != 1) {
                return false; // 有条件未完成
            }
        }

        return true; // 所有条件都完成
    }

    /**
     * 释放流程处理锁
     */
    private void releaseProcessLock(String lockKey) {
        try {
            jedisCluster.del(lockKey);
            log.debug("释放手动推进处理锁成功，lockKey: {}", lockKey);
        } catch (Exception e) {
            log.error("释放手动推进处理锁失败，lockKey: {}", lockKey, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "进行中";
            case 1:
                return "已赢单";
            case 2:
                return "已败单";
            default:
                return "未知(" + status + ")";
        }
    }

    /**
     * 节点搜索结果内部类
     */
    private static class NodeSearchResult {
        private final CallcenterSaleProcessNode node;
        private final CallcenterSaleProcessNodeRecord nodeRecord;

        public NodeSearchResult(CallcenterSaleProcessNode node, CallcenterSaleProcessNodeRecord nodeRecord) {
            this.node = node;
            this.nodeRecord = nodeRecord;
        }

        public CallcenterSaleProcessNode getNode() {
            return node;
        }

        public CallcenterSaleProcessNodeRecord getNodeRecord() {
            return nodeRecord;
        }
    }


}
