package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterSaleProcessConditionApiMapping;
import com.yirendai.workbench.mapper.CallcenterSaleProcessConditionApiMappingMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessConditionApiMappingService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 销售流程条件配置与接口映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-13
 */
@Service
public class CallcenterSaleProcessConditionApiMappingServiceImpl extends ServiceImpl<CallcenterSaleProcessConditionApiMappingMapper, CallcenterSaleProcessConditionApiMapping> implements ICallcenterSaleProcessConditionApiMappingService {

}
