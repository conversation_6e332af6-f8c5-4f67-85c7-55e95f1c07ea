package com.yirendai.workbench.service.impl;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yirendai.voiceaiserver.util.HttpClientUtil;
import com.yirendai.workbench.entity.CallcenterSaleProcessApiConfig;
import com.yirendai.workbench.entity.CallcenterSaleProcessConditionApiMapping;
import com.yirendai.workbench.enums.ConditionOperator;
import com.yirendai.workbench.enums.SimpleOperator;
import com.yirendai.workbench.job.vo.ConditionResult;
import com.yirendai.workbench.job.vo.Range;
import com.yirendai.workbench.service.ICallcenterSaleProcessApiConfigService;
import com.yirendai.workbench.service.ICallcenterSaleProcessConditionApiMappingService;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.vo.req.salesProcess.ApiReq;
import com.yirendai.workbench.vo.req.ComplexConditionData;
import com.yirendai.workbench.vo.req.SimpleConditionData;
import com.yirendai.workbench.vo.req.UnifiedConditionReq;
import com.yirendai.workbench.vo.res.ApiRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;

/**
 * 条件处理器 - 递归处理简单条件和复杂条件
 * <AUTHOR>
 * @time 2025/10/10 17:17
 */
@Component
@Slf4j
public class ConditionProcessorService {

    @Resource
    ICallcenterSaleProcessConditionApiMappingService iCallcenterSaleProcessConditionApiMappingService;

    @Resource
    ICallcenterSaleProcessApiConfigService iCallcenterSaleProcessApiConfigService;

    /**
     * 递归处理统一条件请求（带完成时间追踪）
     * @param condition 统一条件请求对象
     * @param apiReq 个人信息对象，用于API请求中的参数替换
     * @return 条件结果（包含结果和最晚完成时间）
     */
    public ConditionResult processConditionWithTime(UnifiedConditionReq condition, ApiReq apiReq) {
        if (condition == null) {
            return new ConditionResult(true);
        }

        // type = 1: 简单条件
        if (condition.getType() == 1) {
            return processSimpleCondition(condition.getSimpleCondition(), apiReq);
        }
        // type = 2: 复杂条件（条件组）
        else if (condition.getType() == 2) {
            return processComplexCondition(condition.getComplexCondition(), apiReq);
        }

        log.error("未知的条件类型: {}", condition.getType());
        return new ConditionResult(false);
    }

    /**
     * 兼容旧接口：只返回布尔结果
     */
    public boolean processCondition(UnifiedConditionReq condition) {
        return processConditionWithTime(condition, null).isResult();
    }

    /**
     * 重载方法：支持传入用户信息
     */
    public boolean processCondition(UnifiedConditionReq condition, ApiReq apiReq) {
        return processConditionWithTime(condition, apiReq).isResult();
    }

    /**
     * 处理简单条件
     */
    private ConditionResult processSimpleCondition(SimpleConditionData simpleCondition, ApiReq apiReq) {
        if (simpleCondition == null) {
            return new ConditionResult(true);
        }
        ApiRes apiRes = getApiResById(simpleCondition, apiReq);
//        boolean result = evaluateSimpleCondition(actualValue, operator, expectedValue);
        return new ConditionResult(apiRes.getResult(), apiRes.getRemark(), apiRes.getFinishTime());
    }

    /**
     * 处理复杂条件（条件组）
     */
    private ConditionResult processComplexCondition(ComplexConditionData complexCondition, ApiReq apiReq) {
        if (complexCondition == null || complexCondition.getConditions() == null) {
            return new ConditionResult(true);
        }

        List<UnifiedConditionReq> conditions = complexCondition.getConditions();
        ConditionOperator operator = complexCondition.getOperator();

        if (conditions.isEmpty()) {
            return new ConditionResult(true);
        }

        ConditionResult finalResult = new ConditionResult(false);

        // 根据操作符（AND/OR）处理条件列表
        if (operator == ConditionOperator.AND) {
            // AND操作：所有条件都必须为true
            finalResult.setResult(true);
            for (UnifiedConditionReq subCondition : conditions) {
                ConditionResult subResult = processConditionWithTime(subCondition, apiReq); // 递归调用
                finalResult.mergeCompleteTimes(subResult); // 合并完成时间

                if (!subResult.isResult()) {
                    finalResult.setResult(false);
                    // 即使结果为false，也要继续收集所有条件的完成时间
                    // 如果只关心成功条件的时间，可以在这里break
                    break;
                }
            }
        }
        else if (operator == ConditionOperator.OR) {
            // OR操作：至少一个条件为true
            finalResult.setResult(false);
            for (UnifiedConditionReq subCondition : conditions) {
                ConditionResult subResult = processConditionWithTime(subCondition, apiReq); // 递归调用
                finalResult.mergeCompleteTimes(subResult); // 合并完成时间

                if (subResult.isResult()) {
                    finalResult.setResult(true);
                    // 继续收集所有条件的完成时间
                    break;
                }
            }
        }
        else {
            log.error("未知的条件操作符: {}", operator);
        }

        return finalResult;
    }

    /**
     * 根据简单操作符评估条件
     */
    private boolean evaluateSimpleCondition(Object actualValue, SimpleOperator operator, Object expectedValue) {
        if (operator == null) {
            return true;
        }

        switch (operator) {
            case EQUALS:
                return Objects.equals(actualValue, expectedValue);
            case NOT_EQUALS:
                return !Objects.equals(actualValue, expectedValue);
            case GREATER_THAN:
                return compareValues(actualValue, expectedValue) > 0;
            case GREATER_THAN_OR_EQUALS:
                return compareValues(actualValue, expectedValue) >= 0;
            case LESS_THAN:
                return compareValues(actualValue, expectedValue) < 0;
            case LESS_THAN_OR_EQUALS:
                return compareValues(actualValue, expectedValue) <= 0;
            case CONTAINS:
                return actualValue != null && actualValue.toString().contains(expectedValue.toString());
            case NOT_CONTAINS:
                return actualValue == null || !actualValue.toString().contains(expectedValue.toString());
            case IN:
                return isIn(actualValue, expectedValue);
            case NOT_IN:
                return !isIn(actualValue, expectedValue);
            case IS_NULL:
                return actualValue == null;
            case IS_NOT_NULL:
                return actualValue != null;
            case BETWEEN:
                return isBetween(actualValue, expectedValue);
            case NOT_BETWEEN:
                return !isBetween(actualValue, expectedValue);
            default:
                throw new IllegalArgumentException("不支持的操作符: " + operator);
        }
    }

    @SuppressWarnings("unchecked")
    private int compareValues(Object actual, Object expected) {
        if (actual == null || expected == null) {
            throw new IllegalArgumentException("无法比较null值");
        }

        if (actual instanceof Number && expected instanceof Number) {
            double actualDouble = ((Number) actual).doubleValue();
            double expectedDouble = ((Number) expected).doubleValue();
            return Double.compare(actualDouble, expectedDouble);
        }

        if (actual instanceof Comparable && expected instanceof Comparable) {
            try {
                return ((Comparable) actual).compareTo(expected);
            } catch (ClassCastException e) {
                throw new IllegalArgumentException(
                        "值类型不匹配，无法比较: " + actual.getClass() + " vs " + expected.getClass());
            }
        }

        throw new IllegalArgumentException("值不支持比较操作");
    }

    private boolean isIn(Object actualValue, Object expectedValue) {
        if (expectedValue instanceof Collection) {
            return ((Collection<?>) expectedValue).contains(actualValue);
        }
        if (expectedValue instanceof Object[]) {
            return Arrays.asList((Object[]) expectedValue).contains(actualValue);
        }
        return false;
    }

    private boolean isBetween(Object actualValue, Object expectedValue) {
        if (actualValue == null) {
            return false;
        }

        Object[] range = extractRange(expectedValue);
        if (range == null || range.length != 2) {
            throw new IllegalArgumentException("BETWEEN操作符需要两个值作为范围");
        }

        Object min = range[ 0 ];
        Object max = range[ 1 ];

        if (min == null || max == null) {
            throw new IllegalArgumentException("范围值不能为null");
        }

        if (actualValue instanceof Number && min instanceof Number && max instanceof Number) {
            double actualDouble = ((Number) actualValue).doubleValue();
            double minDouble = ((Number) min).doubleValue();
            double maxDouble = ((Number) max).doubleValue();
            return actualDouble >= minDouble && actualDouble <= maxDouble;
        }

        if (actualValue instanceof Date && min instanceof Date && max instanceof Date) {
            Date actualDate = (Date) actualValue;
            Date minDate = (Date) min;
            Date maxDate = (Date) max;
            return !actualDate.before(minDate) && !actualDate.after(maxDate);
        }

        if (actualValue instanceof LocalDate && min instanceof LocalDate && max instanceof LocalDate) {
            LocalDate actualDate = (LocalDate) actualValue;
            LocalDate minDate = (LocalDate) min;
            LocalDate maxDate = (LocalDate) max;
            return !actualDate.isBefore(minDate) && !actualDate.isAfter(maxDate);
        }

        if (actualValue instanceof LocalDateTime && min instanceof LocalDateTime && max instanceof LocalDateTime) {
            LocalDateTime actualDateTime = (LocalDateTime) actualValue;
            LocalDateTime minDateTime = (LocalDateTime) min;
            LocalDateTime maxDateTime = (LocalDateTime) max;
            return !actualDateTime.isBefore(minDateTime) && !actualDateTime.isAfter(maxDateTime);
        }

        if (actualValue instanceof Comparable) {
            return compareValues(actualValue, min) >= 0 && compareValues(actualValue, max) <= 0;
        }

        throw new IllegalArgumentException("不支持的范围比较类型: " + actualValue.getClass());
    }

    private Object[] extractRange(Object expectedValue) {
        if (expectedValue == null) {
            return null;
        }

        if (expectedValue instanceof Object[]) {
            return (Object[]) expectedValue;
        }

        if (expectedValue instanceof int[]) {
            int[] arr = (int[]) expectedValue;
            return new Object[] {arr[ 0 ], arr[ 1 ]};
        }
        if (expectedValue instanceof long[]) {
            long[] arr = (long[]) expectedValue;
            return new Object[] {arr[ 0 ], arr[ 1 ]};
        }
        if (expectedValue instanceof double[]) {
            double[] arr = (double[]) expectedValue;
            return new Object[] {arr[ 0 ], arr[ 1 ]};
        }

        if (expectedValue instanceof List) {
            List<?> list = (List<?>) expectedValue;
            if (list.size() >= 2) {
                return new Object[] {list.get(0), list.get(1)};
            }
        }

        if (expectedValue instanceof Range) {
            Range<?> range = (Range<?>) expectedValue;
            return new Object[] {range.getMin(), range.getMax()};
        }

        if (expectedValue instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) expectedValue;
            Object min = map.get("min");
            Object max = map.get("max");
            if (min != null && max != null) {
                return new Object[] {min, max};
            }
        }

        return null;
    }

    /**
     * 根据id获取值和完成时间
     */
    private ApiRes getApiResById(SimpleConditionData simpleCondition, ApiReq apiReq) {
        try {
            apiReq.setOperator(simpleCondition.getOperator());
            apiReq.setExpectedValue(simpleCondition.getValue() + "");
            // 1. 获取条件API映射配置
            CallcenterSaleProcessConditionApiMapping mapping = iCallcenterSaleProcessConditionApiMappingService.getById(
                    simpleCondition.getId());
            if (Objects.isNull(mapping)) {
                log.warn("未找到条件API映射配置，id: {}", simpleCondition.getId());
                return createFailedApiRes("条件API映射配置不存在");
            }

            // 2. 获取API配置信息
            Long apiId = mapping.getApiId();
            CallcenterSaleProcessApiConfig apiConfig = iCallcenterSaleProcessApiConfigService.getById(apiId);
            if (Objects.isNull(apiConfig)) {
                log.warn("未找到API配置信息，apiId: {}", apiId);
                return createFailedApiRes("API配置信息不存在");
            }

            // 3. 构建请求参数
            Map<String, Object> requestParams = buildRequestParams(apiConfig, apiReq, mapping);

            // 4. 根据请求方式和传参方式发送HTTP请求
            String responseBody = sendHttpRequestWithClient(apiConfig, requestParams);

            // 5. 解析响应结果
            return parseApiResponse(responseBody, mapping, simpleCondition);

        } catch (Exception e) {
            log.error("调用API获取条件结果失败，conditionId: {}", simpleCondition.getId(), e);
            return createFailedApiRes("API调用失败：" + e.getMessage());
        }
    }

    /**
     * 构建请求参数
     */
    private Map<String, Object> buildRequestParams(CallcenterSaleProcessApiConfig apiConfig,
            ApiReq apiReq, CallcenterSaleProcessConditionApiMapping mapping) {

        Map<String, Object> params = new HashMap<>();

        // 解析配置的请求参数模板
        try {
            Map<String, Object> paramTemplate = null;
            if (StringUtils.isNotBlank(apiConfig.getRequestParams())) {
                paramTemplate = JsonUtilExt.jsonToBean(apiConfig.getRequestParams(), new TypeReference<Map<String, Object>>() {

                });
            }
            // 替换参数中的占位符
            if (Objects.nonNull(apiReq)) {
                params.put("customerId", apiReq.getCustomerId());
                params.put("tenantId", apiReq.getTenantId());
                params.put("startTime", DateUtil.formatLocalDateTime(apiReq.getStartTime()));
                params.put("operator", apiReq.getOperator());
                params.put("expectedValue", apiReq.getExpectedValue());
                if(MapUtils.isNotEmpty(paramTemplate)){
                    params.putAll(paramTemplate);
                }
            }
        } catch (Exception e) {
            log.warn("解析请求参数模板失败，使用默认参数", e);
        }



        return params;
    }

    /**
     * 使用HttpClientUtil发送HTTP请求
     */
    private String sendHttpRequestWithClient(CallcenterSaleProcessApiConfig apiConfig, Map<String, Object> requestParams) {
        String requestMethod = apiConfig.getRequestMethod().toUpperCase();
        String contentType = apiConfig.getContentType();
        String url = apiConfig.getUrl();

        HttpClientUtil httpUtil = HttpClientUtil.getInstance();

        try {
            switch (requestMethod) {
                case "GET":
                    return sendGetRequestWithClient(httpUtil, url, requestParams);
                case "POST":
                    if ("application/json".equalsIgnoreCase(contentType)) {
                        return sendPostJsonRequestWithClient(httpUtil, url, requestParams);
                    } else if ("application/x-www-form-urlencoded".equalsIgnoreCase(contentType)) {
                        return sendPostFormRequestWithClient(httpUtil, url, requestParams);
                    } else {
                        throw new IllegalArgumentException("不支持的Content-Type: " + contentType);
                    }
                default:
                    throw new IllegalArgumentException("不支持的请求方式: " + requestMethod);
            }
        } catch (Exception e) {
            log.error("发送HTTP请求失败，url: {}, method: {}", url, requestMethod, e);
            throw new RuntimeException("HTTP请求失败", e);
        }
    }

    /**
     * 使用HttpClientUtil发送GET请求
     */
    private String sendGetRequestWithClient(HttpClientUtil httpUtil, String url, Map<String, Object> params) throws IOException {
        // 构建URL参数
        if (MapUtils.isNotEmpty(params)) {
            StringBuilder urlBuilder = new StringBuilder(url);
            if (url.contains("?")) {
                urlBuilder.append("&");
            } else {
                urlBuilder.append("?");
            }

            boolean first = true;
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (!first) {
                    urlBuilder.append("&");
                }
                if (Objects.nonNull(entry.getValue())) {
                    urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                }
                first = false;
            }
            url = urlBuilder.toString();
        }

        return httpUtil.get(url);
    }

    /**
     * 使用HttpClientUtil发送POST JSON请求
     */
    private String sendPostJsonRequestWithClient(HttpClientUtil httpUtil, String url, Map<String, Object> params) throws Exception {
        String jsonBody = JsonUtilExt.beanToJson(params);
        return httpUtil.postForJSon(url, jsonBody);
    }

    /**
     * 使用HttpClientUtil发送POST Form请求
     */
    private String sendPostFormRequestWithClient(HttpClientUtil httpUtil, String url, Map<String, Object> params) throws
            IOException {
        List<NameValuePair> nameValuePairs = new ArrayList<>();

        if (MapUtils.isNotEmpty(params)) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (Objects.nonNull(entry.getValue())) {
                    nameValuePairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
                }
            }
        }

        return httpUtil.post(url, nameValuePairs);
    }

    /**
     * 解析API响应结果
     */
    private ApiRes parseApiResponse(String responseBody, CallcenterSaleProcessConditionApiMapping mapping,
            SimpleConditionData simpleCondition) {

        if (StringUtils.isBlank(responseBody)) {
            return createFailedApiRes("API响应为空");
        }

        try {
            // 解析JSON响应
            R<ApiRes> response = JsonUtilExt.jsonToBean(responseBody, new TypeReference<R<ApiRes>>() {

            });

            // 根据映射配置获取目标字段值
//            String relationColumn = mapping.getApiRelationColumn();

            if(response == null){
                return createFailedApiRes("API响应为空");
            }
            return response.getData();

        } catch (Exception e) {
            log.error("解析API响应失败，responseBody: {}", responseBody, e);
            return createFailedApiRes("解析API响应失败：" + e.getMessage());
        }
    }

    /**
     * 从响应中获取指定字段的值（支持嵌套路径，如 data.result.count）
     */
    private Object getValueFromResponse(Map<String, Object> responseMap, String fieldPath) {
        if (StringUtils.isBlank(fieldPath)) {
            return null;
        }

        String[] paths = fieldPath.split("\\.");
        Object currentValue = responseMap;

        for (String path : paths) {
            if (currentValue instanceof Map) {
                currentValue = ((Map<?, ?>) currentValue).get(path);
            } else {
                return null;
            }
        }

        return currentValue;
    }

    /**
     * 创建失败的API响应
     */
    private ApiRes createFailedApiRes(String errorMessage) {
        ApiRes apiRes = new ApiRes();
        apiRes.setResult(false);
        apiRes.setRemark(errorMessage);
        apiRes.setFinishTime(LocalDateTime.now());
        return apiRes;
    }
}


