package com.yirendai.workbench.service.callcenter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.entity.callcenter.CallcenterUser;
import com.yirendai.workbench.entity.callcenter.UserDevelopInfo;
import com.yirendai.workbench.enums.callcenter.UserOutbindTypeEnum;
import com.yirendai.workbench.model.CallcenterUserDto;
import com.yirendai.workbench.model.PlannerInfoDto;
import com.yirendai.workbench.model.UserAllocateInfo;
import com.yirendai.workbench.vo.req.callcenter.DelayDevelopCycleReq;
import com.yirendai.workbench.vo.res.callcenter.BatchDelayDevelopCycleRes;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2025/2/20 09:50
 **/
public interface CallcenterUserService extends IService<CallcenterUser> {

    CallcenterUserDto getCenterUserPhoneInfo(String userId, String userPhone);

    CallcenterUserDto saveCallCenterUser(CallcenterUserDto callcenterUserDto);

    Boolean userInfoIsExist(String userId);

    boolean isVirtualUser(Integer source, String userId);

    /**
     * 领取客户更新状态信息
     */
    UserAllocateInfo updateUserStatusWhenRetrieve(Long id, int developCycle);

    /**
     * 获取未分配未锁定用户列表
     */
    List<CallcenterUserDto> getUnAllocateUnLockUserList(Long groupId, Long offsetId, int size, String tenantId);

    List<UserDevelopInfo> getUnFollowUserList(Long offsetId, String tenantId, int size);

    /**
     * 查询已分配的流量客户，且开发周期在某时间范围内
     */
    List<UserDevelopInfo> getAllocatedNoCrmList(Long offsetId, int size, String tenantId, LocalDateTime developStartDate, LocalDateTime developEndDate);
    /**
     * 更新客户状态为已跟进
     */
    int setCustomerToFollowUp(String userId, String virtualId, String tenantId);
    int batchSetCustomerToFollowUp(List<UserDevelopInfo> unFollowUpUserInfo);
    /**
     * 执行用户入库
     */
    Boolean processUserInbound(List<InBoundUserInfo> inBoundUsers, String sceneId);

    int setCustomerToCrm(String userId, String tenantId);

    /**
     * 存量用户分配
     */
    Boolean processUserAllocation();

    /**
     * 执行出库
     */
    Boolean processUserOutbound(UserOutboundInfo userOutboundInfo);

    /**
     * 根据用户ID查询用户信息
     */
    CallcenterUserDto getCenterUserByUserId(String userId, String tenantId);

    Boolean delayDevelopCycle(DelayDevelopCycleReq req);

    List<BatchDelayDevelopCycleRes> batchDelayDevelopCycle(MultipartFile file);

    /**
     * 分配理财师更新用户数据
     */
    void allocationPlannerUpdateUser(String userId, Long groupId, Long sceneId, String tenantId);

    void processUserOutbound(UserOutbindTypeEnum userOutbindTypeEnum, CallcenterUserDto callcenterUserDto, String operatorId);

    /**
     * 注册更新虚拟用户userId
     */
    Boolean registerUpdateVirtualUserId(String phone, String userId, String tenantId);

    /**
     * 变更手机号
     */
    Boolean changePhone(String userId, String phone, String tenantId);

    /**
     * 处理升库逻辑
     */
    Boolean changeUserType(String userId);

    /**
     * 执行预分配入库
     */
    void processPreAllocationInbound(List<InBoundUserInfo> inBoundUserInfos, CallcenterSceneConfig callcenterSceneConfig);


    /**
     * 分配组入库
     */
    CallcenterUserDto processAllocationGroupInbound(InBoundUserInfo userInfo, CallcenterSceneConfig callcenterSceneConfig);

    /**
     * 分配理财师入库
     */
    CallcenterUserDto processBindPlannerInbound(InBoundUserInfo userInfo, CallcenterSceneConfig callcenterSceneConfig);

    /**
     * 判断场景是否正在分配
     */
    boolean isSceneAllocating(Long sceneId);

    /**
     * 自动出库
     */
    void autoOutbound();

    Boolean syncHistoryData(Set<Long> ids);

    void processHistoryDataSync(List<PlannerUserBind> bindList, CallcenterSceneConfig crmScene);

    Boolean handlerUserHistory(Set<Long> ids);

    Boolean changePlannerHistory(Set<Long> ids);

    Boolean changeBindEndTime(Set<Long> ids);

    Boolean changesFollowUpStatus(String userId);

    void updateUserDeptId(BladeUser bladeUser, PlannerInfoDto plannerInfoDto);

    List<CallcenterUserDto> batchGetCenterUserInfo(List<String> batchUserIds, String tenantId);
}
