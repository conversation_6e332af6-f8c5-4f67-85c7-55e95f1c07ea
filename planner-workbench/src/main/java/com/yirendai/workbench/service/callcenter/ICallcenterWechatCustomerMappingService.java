package com.yirendai.workbench.service.callcenter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.CallCenterWechatContact;
import com.yirendai.workbench.entity.callcenter.CallcenterWechatCustomerMapping;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 微信客户id映射关系表 服务类
 * </p>
 */
public interface ICallcenterWechatCustomerMappingService extends IService<CallcenterWechatCustomerMapping> {

    /**
     * 处理微信客户映射关系
     */
    void receiveWechatContactUpdateChangeEvent(List<CallCenterWechatContact> wechatContacts, String tenantId);

    /**
     * 根据微信原生ID获取客户ID
     * @param wechatOriginalId 微信原生ID
     * @return 客户ID
     */
    String getCustomerIdByWechatOriginalId(String wechatOriginalId);

    /**
     * 根据微信号获取客户ID
     * @param wechatNo 微信号
     * @return 客户ID
     */
    String getCustomerIdByWechatNo(String wechatNo);

    /**
     * 根据接收人获取客户ID集合
     */
    Set<String> getCustomerIdsByWechatNo(String wechatNo);
}