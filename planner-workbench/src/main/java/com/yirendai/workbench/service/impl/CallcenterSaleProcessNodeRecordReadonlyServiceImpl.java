package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeRecord;
import com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessNodeRecordReadonlyMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeRecordReadonlyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 销售流程节点进度记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
@Service
public class CallcenterSaleProcessNodeRecordReadonlyServiceImpl extends ServiceImpl<CallcenterSaleProcessNodeRecordReadonlyMapper, CallcenterSaleProcessNodeRecord> implements ICallcenterSaleProcessNodeRecordReadonlyService {

    @Resource
    private CallcenterSaleProcessNodeRecordReadonlyMapper saleProcessNodeRecordReadonlyMapper;


    @Override
    public CallcenterSaleProcessNodeRecord getByNodeIdReadonly(String nodeId,String userId){
        LambdaQueryWrapper<CallcenterSaleProcessNodeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallcenterSaleProcessNodeRecord::getNodeId, nodeId);
        queryWrapper.eq(CallcenterSaleProcessNodeRecord::getUserId,userId);
        return saleProcessNodeRecordReadonlyMapper.selectOne(queryWrapper);
    }

}
