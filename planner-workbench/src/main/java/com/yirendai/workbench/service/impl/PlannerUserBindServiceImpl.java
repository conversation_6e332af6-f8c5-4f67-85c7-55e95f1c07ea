package com.yirendai.workbench.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.app.fortune.common.util.DateUtil;
import com.yirendai.app.fortune.common.util.JsonUtils;
import com.yirendai.workbench.avaya.mapper.CxBasStaffMapper;
import com.yirendai.workbench.avaya.mapper.CxListStatusMapper;
import com.yirendai.workbench.avaya.mapper.CxOutActivityMapper;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.consumer.bean.CxListStatusData;
import com.yirendai.workbench.consumer.bean.PlannerToUserData;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.enums.LockTypeEnum;
import com.yirendai.workbench.enums.callcenter.CustomerAllocateTypeEnum;
import com.yirendai.workbench.enums.callcenter.UserTypeEnum;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.mapper.*;
import com.yirendai.workbench.model.CallcenterUserDto;
import com.yirendai.workbench.model.PlannerInfoDto;
import com.yirendai.workbench.model.UserAllocateInfo;
import com.yirendai.workbench.service.IBladeUserService;
import com.yirendai.workbench.service.IPlannerUserBindService;
import com.yirendai.workbench.service.UserBasicInfoFacadeService;
import com.yirendai.workbench.service.callcenter.CacheService;
import com.yirendai.workbench.service.callcenter.CallcenterUserService;
import com.yirendai.workbench.service.callcenter.ICallcenterUserLockService;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.util.DataScopeUtil;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.PlannerUserBindReq;
import com.yirendai.workbench.vo.req.callcenter.CustomerListReq;
import com.yirendai.workbench.vo.res.UserBasicInfoResponse;
import com.yirendai.workbench.vo.res.callcenter.CustomerListResp;
import com.yirendai.workbench.wrapper.BladeApiWrapper;
import com.yirendai.workbench.wrapper.dto.BladeUserVo;
import groovy.lang.Lazy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yirendai.workbench.constant.CallServiceConstant.ALL_SECOND_MAP;
import static com.yirendai.workbench.constant.CallServiceConstant.FIRST_END_CODE_MAP;
import static com.yirendai.workbench.enums.UserSourceEnum.AVAYA;
import static com.yirendai.workbench.enums.callcenter.CustomerAllocateTypeEnum.PLANNER_RETRIEVE;
import static com.yirendai.workbench.exception.CallCenterBusinessError.RETRIEVE_USER_ERROR;
import static com.yirendai.workbench.util.DateUtil.toDate;
import static java.util.stream.Collectors.toMap;

@Service
@Slf4j
public class PlannerUserBindServiceImpl extends ServiceImpl<PlannerUserBindMapper, PlannerUserBind> implements IPlannerUserBindService {

    private static final String SOURCE_AVAYA = "1";
    public static final String SOURCE_CRM = "2";
    private static final String UNBIND_STATUS = "回退";
    private static final String CRM_BIND_CODE = "CRM_LCS";
    private static final String SYSTEM_USER = "0";

    @Autowired
    @Qualifier("dataInitThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    PlannerUserBindMapper plannerUserBindMapper;
    @Resource
    CxBasStaffMapper cxBasStaffMapper;
    @Resource
    CxListStatusMapper cxListStatusMapper;
    @Resource
    PlannerUserBindHistoryMapper plannerUserBindHistoryMapper;
    @Resource
    FinancialPlannerMapper financialPlannerMapper;
    @Resource
    PlannerToUserMapper plannerToUserMapper;
    @Resource
    PlannerToUserLogMapper plannerToUserLogMapper;
    @Resource
    UserBasicInfoFacadeService userBasicInfoFacadeService;
    @Resource
    CsStaffInfoMapper csStaffInfoMapper;
    @Resource
    CxOutActivityMapper cxOutActivityMapper;
    @Resource
    BladeApiWrapper bladeApiWrapper;
    @Autowired
    private CacheService cacheService;
    @Resource
    @Lazy
    private CallcenterUserService callcenterUserService;

    @Resource
    @Lazy
    private ICallcenterUserLockService userLockService;
    @Autowired
    private DataScopeUtil dataScopeUtil;

    @Resource
    private CallcenterProperty callcenterProperty;

    @Resource
    private IBladeUserService iBladeUserService;

    @Override
    public R<IPage<PlannerUserBindVo>> page(PlannerUserBindReq req) {

        Page<PlannerUserBind> page = new Page<>(req.getPageNo(), req.getPageSize());
        LambdaQueryWrapper<PlannerUserBind> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(req.getUserName()), PlannerUserBind::getUserName, req.getUserName());
        wrapper.eq(Objects.nonNull(req.getUserId()), PlannerUserBind::getUserId, req.getUserId());
        wrapper.eq(Objects.nonNull(req.getPlannerId()), PlannerUserBind::getPlannerId, req.getPlannerId());
        wrapper.eq(Objects.nonNull(req.getPlannerName()), PlannerUserBind::getPlannerName, req.getPlannerName());
        wrapper.eq(Objects.nonNull(req.getGroupName()), PlannerUserBind::getGroupName, req.getGroupName());
        wrapper.eq(Objects.nonNull(req.getResource()), PlannerUserBind::getResource, req.getResource());
        wrapper.eq(Objects.nonNull(req.getRemark()), PlannerUserBind::getRemark, req.getRemark());
        wrapper.and(wrapper2 -> {
            wrapper2.isNull(PlannerUserBind::getEndTime);
            wrapper2.or().ge(PlannerUserBind::getEndTime, new Date());
        });
        wrapper.orderByDesc(PlannerUserBind::getId);
        IPage<PlannerUserBind> plannerUserBindIPage = page(page, wrapper);
        List<PlannerUserBindVo> plannerUserBindVoList = Optional.ofNullable(plannerUserBindIPage.getRecords()).orElse(new ArrayList<>()).stream().map(bean -> {
            PlannerUserBindVo plannerUserBindVo = new PlannerUserBindVo();
            BeanUtils.copyProperties(bean, plannerUserBindVo);
            // 查询当前组别 并set值
            plannerUserBindVo.setCurGroupName(getCurGroupName(bean.getPlannerId()));
            // 如果来源是avaya 还需查询场景值名称
            if (SOURCE_AVAYA.equals(bean.getResource()) && StringUtils.isNotEmpty(bean.getRemark())) {
                LambdaQueryWrapper<CxOutActivity> activityWrapper = new LambdaQueryWrapper<>();
                activityWrapper.eq(true, CxOutActivity::getActivityNo, bean.getRemark());
                CxOutActivity cxOutActivity = cxOutActivityMapper.selectOne(activityWrapper);
                plannerUserBindVo.setRemark2(cxOutActivity == null ? "" : cxOutActivity.getActivityName());
            }
            return plannerUserBindVo;
        }).collect(Collectors.toList());

        Page<PlannerUserBindVo> pageResult = new Page<>();
        pageResult.setTotal(plannerUserBindIPage.getTotal());
        pageResult.setPages(plannerUserBindIPage.getPages());
        pageResult.setCurrent(plannerUserBindIPage.getCurrent());
        pageResult.setSize(plannerUserBindIPage.getSize());
        pageResult.setRecords(plannerUserBindVoList);
        return R.data(pageResult);
    }

    @Override
    public R<PlannerUserBindVo> detail(Long id) {
        try {
            PlannerUserBind bean = getById(id);
            PlannerUserBindVo plannerUserBindVo = new PlannerUserBindVo();
            BeanUtils.copyProperties(bean, plannerUserBindVo);
            // 查询当前组别 并set值
            plannerUserBindVo.setCurGroupName(getCurGroupName(bean.getPlannerId()));
            // 如果来源是avaya 还需查询场景值名称
            if (SOURCE_AVAYA.equals(bean.getResource()) && StringUtils.isNotEmpty(bean.getRemark())) {
                LambdaQueryWrapper<CxOutActivity> activityWrapper = new LambdaQueryWrapper<>();
                activityWrapper.eq(true, CxOutActivity::getActivityNo, bean.getRemark());
                CxOutActivity cxOutActivity = cxOutActivityMapper.selectOne(activityWrapper);
                plannerUserBindVo.setRemark2(cxOutActivity == null ? "" : cxOutActivity.getActivityName());
            }
            return R.data(plannerUserBindVo);
        } catch (Exception e) {
            log.error(" PlannerUserBindServiceImpl detail error:", e);
            return R.fail(1001, "关系不存在，请刷新列表后再试");
        }
    }

    @Override
    public List<PlannerUserBindVo> list(PlannerUserBindReq req) {
        LambdaQueryWrapper<PlannerUserBind> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(req.getUserName()), PlannerUserBind::getUserName, req.getUserName());
        wrapper.eq(Objects.nonNull(req.getUserId()), PlannerUserBind::getUserId, req.getUserId());
        wrapper.eq(Objects.nonNull(req.getPlannerId()), PlannerUserBind::getPlannerId, req.getPlannerId());
        wrapper.eq(Objects.nonNull(req.getPlannerName()), PlannerUserBind::getPlannerName, req.getPlannerName());
        wrapper.eq(Objects.nonNull(req.getGroupName()), PlannerUserBind::getGroupName, req.getGroupName());
        wrapper.eq(Objects.nonNull(req.getResource()), PlannerUserBind::getResource, req.getResource());
        wrapper.eq(Objects.nonNull(req.getRemark()), PlannerUserBind::getRemark, req.getRemark());
        wrapper.and(wrapper2 -> {
            wrapper2.isNull(PlannerUserBind::getEndTime);
            wrapper2.or().ge(PlannerUserBind::getEndTime, new Date());
        });
        wrapper.orderByDesc(PlannerUserBind::getId);
        List<PlannerUserBind> list = list(wrapper);
        List<PlannerUserBindVo> plannerUserBindVoList = Optional.ofNullable(list).orElse(new ArrayList<>()).stream().map(bean -> {
            PlannerUserBindVo plannerUserBindVo = new PlannerUserBindVo();
            BeanUtils.copyProperties(bean, plannerUserBindVo);
            // 查询当前组别 并set值
            plannerUserBindVo.setCurGroupName(getCurGroupName(bean.getPlannerId()));
            // 如果来源是avaya 还需查询场景值名称
            if (SOURCE_AVAYA.equals(bean.getResource()) && StringUtils.isNotEmpty(bean.getRemark())) {
                LambdaQueryWrapper<CxOutActivity> activityWrapper = new LambdaQueryWrapper<>();
                activityWrapper.eq(true, CxOutActivity::getActivityNo, bean.getRemark());
                CxOutActivity cxOutActivity = cxOutActivityMapper.selectOne(activityWrapper);
                plannerUserBindVo.setRemark2(cxOutActivity == null ? "" : cxOutActivity.getActivityName());
            }
            if (SOURCE_AVAYA.equals(bean.getResource())) {
                plannerUserBindVo.setResource("avaya流量库");
            } else {
                plannerUserBindVo.setResource("crm库");
            }
            return plannerUserBindVo;
        }).collect(Collectors.toList());
        return plannerUserBindVoList;
    }

    @Override
    public R<List<String>> getGroupList() {
        List<String> groupList = csStaffInfoMapper.groupList();
        return R.data(groupList);
    }

    @Override
    public IPage<CustomerListResp> searchCustomerList(CustomerListReq req) {
        List<String> plannersIds = dataScopeUtil.getUserMenuDataScope(callcenterProperty.getYumengCustomerListMenuCode(), TenantServiceHolder.SCOPE_PLANNER_FIELD);
        req.setPlannerIds(plannersIds);
        Integer totalCustomerNum = plannerUserBindMapper.countCustomerList(req);
        totalCustomerNum = totalCustomerNum == null ? 0 : totalCustomerNum;
        if (totalCustomerNum < 1) {
            return new Page<>();
        }

        List<PlannerCustomerList> plannerUserBindList = plannerUserBindMapper.searchCustomerList(req);
        List<String> plannerIds = plannerUserBindList.stream().map(PlannerCustomerList::getPlannerId).collect(Collectors.toList());
        List<PlannerInfoDto> plannerInfos = iBladeUserService.findPlannerInfoByPlannerIds(AuthUtil.getTenantId(), plannerIds);
        Map<String, PlannerInfoDto> bladeUserVoMap = plannerInfos.stream().collect(toMap(PlannerInfoDto::getPlannerId,e->e, (k1, k2) -> k2));
        List<String> userIds = plannerUserBindList.stream().map(PlannerCustomerList::getUserId).collect(Collectors.toList());
        Map<String, CallcenterUserLock> userLockMap = Optional.ofNullable(userLockService.lambdaQuery()
                .in(CallcenterUserLock::getUserId, userIds).eq(CallcenterUserLock::getIsDeleted, BladeConstant.DB_NOT_DELETED)
                .eq(CallcenterUserLock::getTenantId, OwnAuthUtil.getTenantId()).list())
                .map(Collection::stream).orElse(Stream.empty()).collect(toMap(CallcenterUserLock::getUserId, e -> e, (k1, k2) -> k2));

        List<CustomerListResp> customerListRespList = plannerUserBindList.stream().map(bean -> {
            CustomerListResp customerListResp = new CustomerListResp();
            BeanUtils.copyProperties(bean, customerListResp);
            customerListResp.setUserType(UserTypeEnum.getEnumsByCode(bean.getUserType()).getName());
            customerListResp.setSceneNo(bean.getSceneNo());
            customerListResp.setLockType(!userLockMap.containsKey(bean.getUserId()) ? "无" : LockTypeEnum.getValueByCode(userLockMap.get(bean.getUserId()).getLockType()));
            customerListResp.setLocked(userLockMap.containsKey(bean.getUserId()));
            customerListResp.setVisitParent(FIRST_END_CODE_MAP.getOrDefault(bean.getVisitParent(), bean.getVisitParent()));
            customerListResp.setVisitType(ALL_SECOND_MAP.getOrDefault(bean.getVisitType(), bean.getVisitType()));
            if(bladeUserVoMap.containsKey(bean.getPlannerId())&&StringUtils.isNotBlank(bladeUserVoMap.get(bean.getPlannerId()).getGroupName())){
                String groupName = bladeUserVoMap.get(bean.getPlannerId()).getGroupName();
                customerListResp.setPlannerDept(groupName);
            }

            return customerListResp;
        }).collect(Collectors.toList());
        return new Page<CustomerListResp>(req.getPageNo(), req.getPageSize(), totalCustomerNum).setRecords(customerListRespList);
    }

    @Override
    public long getBindCount(String plannerId, String resourceType) {
        LambdaQueryWrapper<PlannerUserBind> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlannerUserBind::getPlannerId, plannerId)
                .eq(StringUtils.isNotBlank(resourceType), PlannerUserBind::getResource, resourceType);
        return this.count(queryWrapper);
    }


    @Override
    public void initAvayaData() {
        Long allCount = cxListStatusMapper.selectCountAll();
        log.info(" initAvayaData allCount:{}", allCount);
        Integer pageSize = 3000;
        int pages = allCount.intValue() / pageSize;
        int lastPage = allCount.intValue() % pageSize > 0 ? 1 : 0;
        int allPages = pages + lastPage;

        for (int pageNo = 0; pageNo < allPages; pageNo++) {
            Integer pageStart = pageNo * pageSize;
            log.info("initAvayaData pageNo:{} start ", pageNo);
            threadPoolTaskExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Map<String, Integer> resultMap = new HashMap<>();
                        List<CxListStatus> listStatuses = cxListStatusMapper.selectListByPage(pageStart, pageSize);
                        int successNumber = 0;
                        for (CxListStatus bean : listStatuses) {
                            try {
                                boolean result = bindByAvayaRecord(bean, true);
                                if (result) {
                                    successNumber++;
                                }
                            } catch (Exception e) {
                                log.error(" initAvayaData.bindByAvayaRecord error userId:{},plannerId:{},msg:{} ", bean.getStrategyId(), bean.getGetUser(), e);
                            }
                        }
                        resultMap.put("dealCount", listStatuses.size());
                        resultMap.put("successNumber", successNumber);
                        resultMap.put("theMaxId", listStatuses.get(listStatuses.size() - 1).getStatusId());
                        log.info("initAvayaData -> page:{}, resultMap:{}", pageStart / pageSize, JsonUtils.beanToJson(resultMap));
                    } catch (Exception e) {
                        log.error(" initAvayaData error:{}", e);
                    }
                }
            });
        }
    }

    @Override
    public void initCrmData() {
        Integer allCount = plannerToUserMapper.selectCountAll();
        log.info(" initCrmData allCount:{}", allCount);
        Integer pageSize = 5000;
        int pages = allCount / pageSize;
        int lastPage = allCount % pageSize > 0 ? 1 : 0;
        int allPages = pages + lastPage;
        Map<Integer, CompletableFuture<String>> allResultInfos = new HashMap<>(allPages);
        for (int pageNo = 0; pageNo < allPages; pageNo++) {
            Integer pageStart = pageNo * pageSize;
            log.info("initCrmData pageNo:{} start ", pageNo);
            threadPoolTaskExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    Map<String, Integer> resultMap = new HashMap<>();
                    List<PlannerToUser> plannerToUserList = plannerToUserMapper.selectListByPage(pageStart, pageSize);
                    int successNumber = 0;
                    for (PlannerToUser bean : plannerToUserList) {
                        try {
                            boolean result = bindByCrmRecord(bean, true);
                            if (result) {
                                successNumber++;
                            }
                        } catch (Exception e) {
                            log.error(" initCrmData.bindByCrmRecord error userId:{},plannerId:{},msg:{} ", bean.getUserId(), bean.getPlannerId(), e);
                        }
                    }
                    resultMap.put("dealCount", plannerToUserList.size());
                    resultMap.put("successNumber", successNumber);
                    resultMap.put("maxId", plannerToUserList.get(plannerToUserList.size() - 1).getId().intValue());
                    log.info("initCrmData -> page:{}, resultMap:{}", pageStart / pageSize, JsonUtils.beanToJson(resultMap));
                }
            });
        }
    }

    @Override
    public void updateCrmData() {
        Integer allCount = plannerUserBindMapper.selectCountAll();
        log.info(" updateCrmData allCount:{}", allCount);
        Integer pageSize = 3000;
        int pages = allCount / pageSize;
        int lastPage = allCount % pageSize > 0 ? 1 : 0;
        int allPages = pages + lastPage;
        Map<Integer, CompletableFuture<String>> allResultInfos = new HashMap<>(allPages);
        for (int pageNo = 0; pageNo < allPages; pageNo++) {
            Integer pageStart = pageNo * pageSize;
            Integer pageNo2 = pageNo;
            log.info("updateCrmData pageNo:{} start ", pageNo);
            threadPoolTaskExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    Map<String, Integer> resultMap = new HashMap<>();
                    Page<PlannerUserBind> page = new Page<>(pageNo2, pageSize);
                    LambdaQueryWrapper<PlannerUserBind> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(true, PlannerUserBind::getResource, SOURCE_CRM);
                    wrapper.le(true, PlannerUserBind::getId, 197510);
                    wrapper.orderByAsc(PlannerUserBind::getId);
                    IPage<PlannerUserBind> plannerUserBindIPage = page(page, wrapper);

                    //List<PlannerUserBind> plannerUserBindList = plannerUserBindMapper.selectListByPage(pageStart,pageSize);
                    List<PlannerUserBind> plannerUserBindList = plannerUserBindIPage.getRecords();
                    int successNumber = 0;
                    for (PlannerUserBind bean : plannerUserBindList) {
                        try {
                            // 查询绑定场景值
                            PlannerToUserLog plannerToUserLog = plannerToUserLogMapper.getBindBeanLog(Long.parseLong(bean.getUserId()));
                            if (null != plannerToUserLog) {
                                bean.setRemark2(plannerToUserLog.getUserType());
                            }
                            // 查询用户姓名接口
                            UserBasicInfoResponse basicInfoResponse = userBasicInfoFacadeService.getMobileNoByUserId(
                                    bean.getUserId() + "");
                            if (null != basicInfoResponse) {
                                bean.setUserName(basicInfoResponse.getUserName());
                            }
                            bean.setRemark(CRM_BIND_CODE);
                            boolean flag = plannerUserBindMapper.updateById(bean) > 0;
                            if (flag) {
                                successNumber++;
                            }
                        } catch (Exception e) {
                            log.error(" updateCrmData error Id:{},msg:{} ", bean.getId(), e);
                        }
                    }
                    resultMap.put("dealCount", plannerUserBindList.size());
                    resultMap.put("successNumber", successNumber);
                    resultMap.put("maxId", plannerUserBindList.get(plannerUserBindList.size() - 1).getId().intValue());
                    log.info("updateCrmData -> page:{}, resultMap:{}", pageStart / pageSize, JsonUtils.beanToJson(resultMap));
                }
            });
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean bindByAvayaRecord(CxListStatus bean, boolean isInit) {
        boolean flag = false;
        // 校验用户id 、理财师id必填，否则打印日志 返回
        if (bean.getStrategyId() == null || StringUtils.isEmpty(bean.getGetUser())) {
            log.info(" bindByAvayaRecord error: 缺少必要参数，userId:{},plannerId:{}", bean.getStrategyId(), bean.getGetUser());
            return false;
        }
        CxBasStaff cxBasStaff = cxBasStaffMapper.selectOne(
                new LambdaQueryWrapper<CxBasStaff>().eq(CxBasStaff::getStaffId, bean.getGetUser()));

        LambdaQueryWrapper<PlannerUserBind> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlannerUserBind::getUserId, bean.getStrategyId());
        List<PlannerUserBind> plannerUserBindList = list(queryWrapper);
        // 数据库已存在该用户的绑定记录
        if (CollectionUtils.isNotEmpty(plannerUserBindList)) {
            if (isInit) {
                // 初始化时，需要判断是否有crm的更早的绑定记录，若存在，则已crm为准,此条avaya的绑定不处理
                PlannerUserBind crmBindBean = plannerUserBindList.stream().filter(bindBean -> (SOURCE_CRM.equals(bindBean.getResource()) && bindBean.getBindTime().compareTo(bean.getGetTime()) < 0)).findFirst().orElse(null);
                if (null != crmBindBean) {
                    log.error(" bindByAvayaRecord error: has have crm early bind ,userId:{}", bean.getStrategyId());
                    return false;
                }
            } else {
                // 监听变更时，需增加判断是否是同一个绑定记录的变更 若是，则不处理；
                PlannerUserBind theSameBindBean = plannerUserBindList.stream().filter(bindBean -> (SOURCE_AVAYA.equals(bindBean.getResource()) && bindBean.getPlannerId().equals(bean.getGetUser()) && bindBean.getEndTime().compareTo(bean.getEndTime()) == 0)).findFirst().orElse(null);
                if (null != theSameBindBean) {
                    log.error(" bindByAvayaRecord error: have theSameBindBean ,userId:{},plannerId:{}", bean.getStrategyId(), bean.getGetUser());
                    return false;
                }
            }
            // 需要按当前记录绑定，则把历史的绑定记录均解绑，再按新的记录绑定
            for (PlannerUserBind oldBind : plannerUserBindList) {
                unbind(oldBind);
            }
            PlannerUserBind newBind = PlannerUserBind.builder().userId(bean.getStrategyId() + "").userName(bean.getUserName()).plannerId(bean.getGetUser()).plannerName(cxBasStaff == null ? "" : cxBasStaff.getStaffName()).bindTime(bean.getGetTime()).endTime(bean.getEndTime()).remark(bean.getKeyPointCd()).orgId(bean.getStatusId().toString()).resource(SOURCE_AVAYA).build();
            flag = bind(newBind);
        } else {
            // 调用绑定流程
            PlannerUserBind newBind = PlannerUserBind.builder().userId(bean.getStrategyId() + "").userName(bean.getUserName()).plannerId(bean.getGetUser()).plannerName(cxBasStaff == null ? "" : cxBasStaff.getStaffName()).bindTime(bean.getGetTime()).endTime(bean.getEndTime()).remark(bean.getKeyPointCd()).orgId(bean.getStatusId().toString()).resource(SOURCE_AVAYA).build();
            flag = bind(newBind);
        }
        if (!flag) {
            log.error(" bindByAvayaRecord error: userId:{},plannerId:{}", bean.getStrategyId(), bean.getGetUser());
        }
        return flag;
    }

    private String getCurGroupName(String plannerId) {
        String groupName = "";
        LambdaQueryWrapper<CsStaffInfo> csStaffInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        csStaffInfoLambdaQueryWrapper.eq(CsStaffInfo::getMemberExternalNumber, plannerId);
        CsStaffInfo csStaffInfo = csStaffInfoMapper.selectOne(csStaffInfoLambdaQueryWrapper);
        if (null != csStaffInfo) {
            groupName = csStaffInfo.getGroupsName();
        }
        return groupName;
    }

    public boolean bind(PlannerUserBind plannerUserBind) {
        try {
            //判断用户中心是否存在该用户
            Boolean isExist = callcenterUserService.userInfoIsExist(plannerUserBind.getUserId());
            if (!isExist) {
                List<PlannerUserBind> binds=new ArrayList<>();
                binds.add(plannerUserBind);
               callcenterUserService.processHistoryDataSync(binds,null);
            }

            // avaya绑定关系表会在凌晨有变更用户uid的定时任务，因此 若为avaya的凌晨时的绑定增加特殊判断
            LocalTime currentTime = LocalTime.now();
            LocalTime theTime = LocalTime.parse("00:05");
            if (SOURCE_AVAYA.equals(plannerUserBind.getResource()) && currentTime.isBefore(theTime)) {
                PlannerUserBind oldBind = plannerUserBindMapper.getByOrgIdAndResource(plannerUserBind.getOrgId(), SOURCE_AVAYA);
                if (null != oldBind && oldBind.getPlannerId().equals(plannerUserBind.getPlannerId()) && !oldBind.getUserId().equals(plannerUserBind.getUserId())) {
                    plannerUserBind.setId(oldBind.getId());
                    plannerUserBindMapper.updateById(plannerUserBind);
                    PlannerUserBindHistory history = new PlannerUserBindHistory();
                    BeanUtils.copyProperties(plannerUserBind, history, "id");
                    UpdateWrapper<PlannerUserBindHistory> wrapper = new UpdateWrapper<>();
                    wrapper.eq("bind_id", oldBind.getId());
                    plannerUserBindHistoryMapper.update(history, wrapper);
                    return true;
                }
            }
            // 根据理财师工号查询理财师所在组
            plannerUserBind.setGroupName(getCurGroupName(plannerUserBind.getPlannerId()));
            plannerUserBindMapper.insertSelective(plannerUserBind);
            PlannerUserBindHistory history = new PlannerUserBindHistory();
            BeanUtils.copyProperties(plannerUserBind, history, "id");
            history.setId(null);
            history.setBindId(plannerUserBind.getId());
            history.setBindStatus(1);
            plannerUserBindHistoryMapper.insert(history);
        } catch (Exception e) {
            log.info(" bind error userId:{},plannerId:{},resource:{},errorMsg:", plannerUserBind.getUserId(), plannerUserBind.getPlannerId(), plannerUserBind.getResource(), e);
            return false;
        }
        return true;
    }


    public boolean unbind(PlannerUserBind plannerUserBind) {
        try {
            LambdaQueryWrapper<PlannerUserBindHistory> queryHistoryWrapper = new LambdaQueryWrapper<>();
            queryHistoryWrapper.eq(PlannerUserBindHistory::getBindId, plannerUserBind.getId());
            queryHistoryWrapper.eq(PlannerUserBindHistory::getUserId, plannerUserBind.getUserId());
            queryHistoryWrapper.eq(PlannerUserBindHistory::getPlannerId, plannerUserBind.getPlannerId());
            queryHistoryWrapper.eq(PlannerUserBindHistory::getBindStatus, 1);
            PlannerUserBindHistory historyBean = plannerUserBindHistoryMapper.selectOne(queryHistoryWrapper);
            if (null != historyBean) {
                historyBean.setBindStatus(2);
                historyBean.setEndTime(historyBean.getResource().equals(SOURCE_CRM) ? new Date() : plannerUserBind.getEndTime());
                // 更新解绑流水记录
                plannerUserBindHistoryMapper.updateById(historyBean);
            }
            // 删除绑定表记录
            removeById(plannerUserBind.getId());
        } catch (Exception e) {
            log.info(" unbind error userId:{},plannerId:{},resource:{},errorMsg:", plannerUserBind.getUserId(), plannerUserBind.getPlannerId(), plannerUserBind.getResource(), e);
            return false;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean bindByCrmRecord(PlannerToUser bean, boolean isInit) {
        boolean flag = false;
        // 校验用户id 、理财师id必填，否则打印日志 返回
        if (bean.getUserId() == null || bean.getPlannerId() == null) {
            log.error(" bindByCrmRecord error 缺少必要参数，userId:{},plannerId:{}", bean.getUserId(), bean.getPlannerId());
            return false;
        }
        FinancialPlanner financialPlanner = financialPlannerMapper.selectById(bean.getPlannerId());
        if (financialPlanner == null) {
            log.error(" bindByCrmRecord error: FinancialPlanner.id:{} search result is null ", bean.getPlannerId());
            return false;
        }
        // 查询绑定场景值
        String remark = "";
        PlannerToUserLog plannerToUserLog = plannerToUserLogMapper.getBindBeanLog(bean.getUserId());
        if (null != plannerToUserLog) {
            remark = plannerToUserLog.getUserType();
        }
        // 查询用户姓名接口
        String userName = "";
        UserBasicInfoResponse basicInfoResponse = userBasicInfoFacadeService.getMobileNoByUserId(
                bean.getUserId() + "");
        if (null != basicInfoResponse) {
            userName = basicInfoResponse.getUserName();
        }

        LambdaQueryWrapper<PlannerUserBind> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlannerUserBind::getUserId, bean.getUserId());
        List<PlannerUserBind> plannerUserBindList = list(queryWrapper);
        // 数据库已存在该用户的绑定记录
        if (CollectionUtils.isNotEmpty(plannerUserBindList)) {
            if (isInit) {
                // 初始化时，需要判断是否有avaya的更早的绑定记录，若存在，则已avaya为准,此条crm的绑定不处理
                PlannerUserBind avayaBindBean = plannerUserBindList.stream().filter(bindBean -> (SOURCE_AVAYA.equals(bindBean.getResource()) && bindBean.getBindTime().compareTo(bean.getUpdateTime()) < 0)).findFirst().orElse(null);
                if (null != avayaBindBean) {
                    log.error(" bindByAvayaRecord error: has have avaya early bind ,userId:{}", bean.getUserId());
                    return false;
                }
            }
            // 需要按当前记录绑定，则把历史的绑定记录均解绑，再按新的记录绑定
            for (PlannerUserBind oldBind : plannerUserBindList) {
                unbind(oldBind);
            }
            PlannerUserBind newBind = PlannerUserBind.builder().userId(bean.getUserId() + "").userName(userName).plannerId(financialPlanner.getDxEmployeeNumber() + "").plannerName(financialPlanner.getName()).bindTime(bean.getUpdateTime()).orgId(bean.getId().toString()).remark(CRM_BIND_CODE).remark2(remark).resource(SOURCE_CRM).build();
            flag = bind(newBind);
        } else {
            // 调用绑定流程
            PlannerUserBind newBind = PlannerUserBind.builder().userId(bean.getUserId() + "").userName(userName).plannerId(financialPlanner.getDxEmployeeNumber() + "").plannerName(financialPlanner.getName()).bindTime(bean.getUpdateTime()).orgId(bean.getId().toString()).remark(CRM_BIND_CODE).remark2(remark).resource(SOURCE_CRM).build();
            flag = bind(newBind);
        }
        if (!flag) {
            log.error(" bindByCrmRecord error: userId:{},plannerId:{}", bean.getUserId(), bean.getPlannerId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refreshAvaya(CxListStatusData cxListStatusData) {
        boolean flag = false;
        // 按时间初步过滤一下数据
        if (null == cxListStatusData.getEnd_time() || "9999-12-31".equals(cxListStatusData.getEnd_time()) || ("9999-12-01".equals(cxListStatusData.getEnd_time()))) {
            log.error(" refreshAvaya param error, endTime is error,statusId:{}", cxListStatusData.getStatus_id());
            return false;
        }
        Date endTime = DateUtil.formatFrom(cxListStatusData.getEnd_time(), DateUtil.DateFormat.YYYY_MM_DD);

        // 若为回退状态，只处理绑定结束时间大于当前的记录
        if (UNBIND_STATUS.equals(cxListStatusData.getList_status()) && endTime.compareTo(new Date()) < 0) {
            return false;
        }
        CxListStatus cxListStatus = CxListStatus.builder().strategyId(cxListStatusData.getStrategy_id()).getUser(cxListStatusData.getGetuser()).userName(cxListStatusData.getUser_name()).getTime(DateUtil.formatFrom(cxListStatusData.getGettime(), DateUtil.DateFormat.YYYY_MM_DD_HH_MM_SS)).endTime(endTime).keyPointCd(cxListStatusData.getKey_point_cd()).listStatus(cxListStatusData.getList_status()).setvaliduser(cxListStatusData.getSetvaliduser()).statusId(Integer.valueOf(cxListStatusData.getStatus_id())).build();
        // 监听到 delete 且 endTime>now时，做解绑操作
        if ("d".equalsIgnoreCase(cxListStatusData.getOptType()) && endTime.compareTo(new Date()) > 0) {
            LambdaQueryWrapper<PlannerUserBind> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PlannerUserBind::getUserId, cxListStatusData.getStrategy_id());
            queryWrapper.eq(PlannerUserBind::getPlannerId, cxListStatusData.getGetuser());
            queryWrapper.eq(PlannerUserBind::getEndTime, endTime);
            queryWrapper.eq(PlannerUserBind::getResource, SOURCE_AVAYA);
            PlannerUserBind plannerUserBind = plannerUserBindMapper.selectOne(queryWrapper);
            flag = Objects.isNull(plannerUserBind) || unbind(plannerUserBind);
        } else {
            // insert  or update 操作
            if (!UNBIND_STATUS.equals(cxListStatusData.getList_status()) && endTime.compareTo(new Date()) > 0 && StringUtils.isNotEmpty(cxListStatus.getGetUser()) && !"null".equals(cxListStatus.getGetUser())) {
                // 走绑定逻辑
                flag = bindByAvayaRecord(cxListStatus, false);
            } else {
                //若绑定表里存在avaya数据源的用户与理财师的绑定关系，则解绑，否则不处理
                LambdaQueryWrapper<PlannerUserBind> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(PlannerUserBind::getUserId, cxListStatusData.getStrategy_id());
                // 回退解绑 理财师工号取Setvaliduser字段
                if (UNBIND_STATUS.equals(cxListStatus.getListStatus()) && StringUtils.isNotEmpty(cxListStatusData.getSetvaliduser())) {
                    queryWrapper.eq(PlannerUserBind::getPlannerId, cxListStatusData.getSetvaliduser());
                    queryWrapper.eq(PlannerUserBind::getBindTime, cxListStatus.getGetTime());
                    queryWrapper.eq(PlannerUserBind::getResource, SOURCE_AVAYA);
                    PlannerUserBind plannerUserBind = plannerUserBindMapper.selectOne(queryWrapper);
                    if (null != plannerUserBind) {
                        // 解绑时根据avaya中的绑定截止时间 更新流水表中的绑定截止时间
                        plannerUserBind.setEndTime(endTime);
                        flag = unbind(plannerUserBind);
                    } else {
                        flag = true;
                    }
                } else if (StringUtils.isNotEmpty(cxListStatusData.getGetuser())) {
                    // 正常解绑逻辑 取正常理财师工号字段
                    queryWrapper.eq(PlannerUserBind::getPlannerId, cxListStatusData.getGetuser());
                    queryWrapper.eq(PlannerUserBind::getBindTime, cxListStatus.getGetTime());
                    queryWrapper.eq(PlannerUserBind::getResource, SOURCE_AVAYA);
                    PlannerUserBind plannerUserBind = plannerUserBindMapper.selectOne(queryWrapper);
                    if (null != plannerUserBind) {
                        // 解绑时根据avaya中的绑定截止时间 更新流水表中的绑定截止时间
                        plannerUserBind.setEndTime(endTime);
                        flag = unbind(plannerUserBind);
                    } else {
                        flag = true;
                    }
                }
            }
        }
        if (!flag) {
            log.error(" refreshAvaya error: userId:{} plannerId:{},cxListStatusData.statusId:{} ", cxListStatusData.getStrategy_id(), cxListStatusData.getGetuser(), cxListStatusData.getStatus_id());
        }

        return flag;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refreshCrm(PlannerToUserData plannerToUserData) {
        boolean flag = false;
        PlannerToUser plannerToUser = PlannerToUser.builder().id(plannerToUserData.getId()).userId(plannerToUserData.getUser_id()).plannerId(plannerToUserData.getPlanner_id()).updateTime(plannerToUserData.getUpdate_time()).build();
        if ("d".equalsIgnoreCase(plannerToUserData.getOptType())) {
            // 删除操作 解绑 若绑定表里存在crm数据源的用户与理财师的绑定关系，则解绑，否则不处理
            FinancialPlanner financialPlanner = financialPlannerMapper.selectById(plannerToUserData.getPlanner_id());
            if (financialPlanner == null) {
                log.error(" refreshCrm error: FinancialPlanner.id:{} search result is null ", plannerToUserData.getPlanner_id());
                return false;
            }
            LambdaQueryWrapper<PlannerUserBind> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PlannerUserBind::getUserId, plannerToUserData.getUser_id());
            queryWrapper.eq(PlannerUserBind::getPlannerId, financialPlanner.getDxEmployeeNumber());
            queryWrapper.eq(PlannerUserBind::getResource, SOURCE_CRM);
            List<PlannerUserBind> plannerUserBindList = list(queryWrapper);
            if (CollectionUtils.isNotEmpty(plannerUserBindList)) {
                flag = unbind(plannerUserBindList.get(0));
            } else {
                return true;
            }
        } else {
            // 插入、更新操作  绑定
            flag = bindByCrmRecord(plannerToUser, false);
        }
        if (!flag) {
            log.error(" refreshCrm error: userId:{} plannerId:{} ", plannerToUserData.getUser_id(), plannerToUserData.getPlanner_id());
        }
        return flag;
    }

    @Override
    public List<Integer> getCrmGroups() {
        return baseMapper.getCrmGroups();
    }

    @Override
    public List<PlannerUserBind> listByTenantIdAndUserIds(String tenantId, List<String> userIds) {
        return this.lambdaQuery()
                .eq(PlannerUserBind::getTenantId, tenantId)
                .in(PlannerUserBind::getUserId, userIds)
                .isNotNull(PlannerUserBind::getPlannerId)
                .list();
    }

    @Override
    public boolean retrieveToBindCustomer(CallcenterUserDto user, PlannerInfoDto plannerInfo, UserAllocateInfo userAllocateInfo) {
        try {
            PlannerUserBind plannerUserBind = PlannerUserBind.builder().userId(user.getCurUserId())
                    .userName(user.getUserName()).plannerId(plannerInfo.getPlannerId())
                    .plannerName(plannerInfo.getPlannerName()).bindTime(toDate(userAllocateInfo.getBindTime()))
                    .endTime(toDate(userAllocateInfo.getEndTime())).resource(AVAYA.getCode())
                    .allocateType(PLANNER_RETRIEVE.getCode())
                    .tenantId(plannerInfo.getTenantId()).groupName(plannerInfo.getGroupName()).build();
            return this.save(plannerUserBind);
        } catch (DuplicateKeyException e) {
            throw new BusinessException(RETRIEVE_USER_ERROR, "用户已被绑定，用户id:" + user.getCurUserId());
        }
    }

    @Override
    public int countPlannerRetrieveToday(String plannerId, String tenantId) {
        return this.lambdaQuery().eq(PlannerUserBind::getPlannerId, plannerId).eq(PlannerUserBind::getTenantId, tenantId)
                .eq(PlannerUserBind::getAllocateType, PLANNER_RETRIEVE.getCode())
                .gt(PlannerUserBind::getBindTime, LocalDate.now().atStartOfDay()).count();
    }

    /**
     * 变更理财师
     */
    @Override
    public Boolean changesPlanner(PlannerUserBind plannerUserBind, PlannerInfoDto plannerInfoDto) {
        //更新bind表
        this.lambdaUpdate().set(PlannerUserBind::getPlannerId, plannerInfoDto.getPlannerId())
                .set(PlannerUserBind::getPlannerName, plannerInfoDto.getPlannerName())
                .set(PlannerUserBind::getGroupName,plannerInfoDto.getGroupName())
                .set(PlannerUserBind::getBindTime,DateUtil.toDate(LocalDateTime.now()))
                .set(PlannerUserBind::getAllocateType, CustomerAllocateTypeEnum.BACKGROUND_planner_CHANGE.getCode())
                .eq(PlannerUserBind::getId, plannerUserBind.getId())
                .update();

        //记录历史
        PlannerUserBindHistory history = new PlannerUserBindHistory();
        BeanUtils.copyProperties(plannerUserBind, history, "id");
        history.setId(null);
        history.setBindId(plannerUserBind.getId());
        history.setBindStatus(2);
        plannerUserBindHistoryMapper.insert(history);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindPlanner(PlannerUserBind plannerUserBind) {
        this.save(plannerUserBind);
    }


}
