package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeCondition;
import com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessNodeConditionReadonlyMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeConditionReadonlyService;
import com.yirendai.workbench.vo.res.callcenter.CallcenterNodeConditionNumbInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 销售流程节点条件配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
@Service
public class CallcenterSaleProcessNodeConditionReadonlyServiceImpl extends ServiceImpl<CallcenterSaleProcessNodeConditionReadonlyMapper, CallcenterSaleProcessNodeCondition> implements ICallcenterSaleProcessNodeConditionReadonlyService {

    @Resource
    private CallcenterSaleProcessNodeConditionReadonlyMapper saleProcessNodeConditionReadonlyMapper;

    @Override
    public List<CallcenterSaleProcessNodeCondition> selectListByIdsReadonly(List<String> ids){
        return saleProcessNodeConditionReadonlyMapper.selectListByIds(ids);
    }

    @Override
    public List<CallcenterSaleProcessNodeCondition> selectListByNodeIdReadonly(String nodeId){
        return saleProcessNodeConditionReadonlyMapper.selectListByNodeId(nodeId);
    }

    @Override
    public List<CallcenterNodeConditionNumbInfo> countNumberByNodeIdsReadonly(List<String> nodeIds){
        return saleProcessNodeConditionReadonlyMapper.countNumberByNodeIds(nodeIds);
    }

}
