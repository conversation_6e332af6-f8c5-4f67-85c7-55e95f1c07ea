package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterSaleProcessApiConfig;
import com.yirendai.workbench.mapper.CallcenterSaleProcessApiConfigMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessApiConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 销售流程接口配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-13
 */
@Service
public class CallcenterSaleProcessApiConfigServiceImpl extends ServiceImpl<CallcenterSaleProcessApiConfigMapper, CallcenterSaleProcessApiConfig> implements ICallcenterSaleProcessApiConfigService {

}
