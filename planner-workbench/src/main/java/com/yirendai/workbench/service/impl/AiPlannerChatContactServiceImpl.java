package com.yirendai.workbench.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.voiceaiserver.config.AiServerException;
import com.yirendai.voiceaiserver.enums.MsgSendDirectionEnum;
import com.yirendai.voiceaiserver.enums.QwMsgTypeEnum;
import com.yirendai.voiceaiserver.job.PlannerAchievementRankJob;
import com.yirendai.voiceaiserver.mapper.linkwechat.WeChatContactMsgMapper;
import com.yirendai.voiceaiserver.mapper.linkwechat.WeCustomerMapper;
import com.yirendai.voiceaiserver.model.linkwechat.WeChatContactMsg;
import com.yirendai.voiceaiserver.model.linkwechat.WeCustomer;
import com.yirendai.voiceaiserver.util.HttpClientProxyUtil;
import com.yirendai.voiceaiserver.vo.db.AiPlannerTopUserPO;
import com.yirendai.voiceaiserver.vo.db.AvayaGroupVO;
import com.yirendai.voiceaiserver.vo.db.PlannerAchievementInfo;
import com.yirendai.voiceaiserver.vo.request.PlannerUserChatListReq;
import com.yirendai.voiceaiserver.vo.request.PlannerUserChatReq;
import com.yirendai.voiceaiserver.vo.request.SendRobotReq;
import com.yirendai.voiceaiserver.vo.response.PlannerVO;
import com.yirendai.workbench.consumer.bean.WechatMessageBizRecordEvent;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.AiPlannerTopUser;
import com.yirendai.workbench.enums.WechatMessageSourceEnum;
import com.yirendai.workbench.mapper.AiPlannerChatContactMapper;
import com.yirendai.workbench.service.AdsCsgDOrderDetailDfService;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import com.yirendai.workbench.vo.req.PlannerChatContactReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 语料表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@Service
@RefreshScope
public class AiPlannerChatContactServiceImpl extends ServiceImpl<AiPlannerChatContactMapper, AiPlannerChatContact> implements IAiPlannerChatContactService {

    @Value("${qw.robot.sync.url}")
    private String robotSyncUrl;

    @Value("${yr.tenant.id}")
    private String yrTenantId;

    //存在更多内容
    private static final Integer MORE_CONTENT = 1;

    //不存在更多内容
    private static final Integer NO_MORE_CONTENT = 0;

    //初次展示处理后内容的最大数量
    private static final Integer INIT_PROCESS_CONTENT_NUMBER = 300;

    @Resource
    AiPlannerChatContactMapper aiPlannerChatContactMapper;
    @Resource
    IAiPlannerTopUserService aiPlannerTopUserService;
    @Resource
    WeCustomerMapper weCustomerMapper;
    @Resource
    WeChatContactMsgMapper weChatContactMsgMapper;
    @Resource
    JedisCluster jedisCluster;
    @Resource
    AdsCsgDOrderDetailDfService adsCsgDOrderDetailDfService;

    @Resource
    @Qualifier("syncLinkWechatPoolExecutor")
    ExecutorService syncLinkWechatPoolExecutor;

    @Resource
    private ApplicationContext applicationContext;
    @Override
    public IPage<AiPlannerChatContact> getPartialContent(PlannerChatContactReq plannerChatContactReq) {
       QueryWrapper<AiPlannerChatContact> queryWrapper = new QueryWrapper<>();
       queryWrapper.eq("planner_no", plannerChatContactReq.getPlannerNo())
               .eq("user_id", plannerChatContactReq.getUserId())
               .ge("msg_time", plannerChatContactReq.getStartTime())
               .le("msg_time", plannerChatContactReq.getEndTime())
               .orderByAsc("msg_time")
               .select(
                       "id",
                       "planner_no",
                       "user_id",
                       "direction",
                       "origin_content",
                       "origin_type",
                       "msg_time",
                       "msg_tag",
                       "bus_id",
                       "is_revoke",
                       "substring(processed_content, 1, 300) as processed_content"
               );

        Page<AiPlannerChatContact> aiPlannerChatContactPage = aiPlannerChatContactMapper.selectPage(new Page<>(plannerChatContactReq.getPageNo(), plannerChatContactReq.getPageSize()), queryWrapper);
        List<AiPlannerChatContact> records = aiPlannerChatContactPage.getRecords();
        //判断是否存在更多内容
        records = records.stream().map(record -> {
            if (StringUtils.isNoneEmpty(record.getProcessedContent()) && record.getProcessedContent().length() >= INIT_PROCESS_CONTENT_NUMBER) {
                record.setMoreContent(MORE_CONTENT);
            }else {
                record.setMoreContent(NO_MORE_CONTENT);
            }
            return record;
        }).collect(Collectors.toList());
        aiPlannerChatContactPage.setRecords(records);
        return aiPlannerChatContactPage;
    }

    @Override
    public String getProcessedContentById(Long id) {
        AiPlannerChatContact aiPlannerChatContact = aiPlannerChatContactMapper.selectById(id);
        if (Objects.isNull(aiPlannerChatContact) || StringUtils.isEmpty(aiPlannerChatContact.getProcessedContent())) {
            return null;
        }
        return aiPlannerChatContact.getProcessedContent();
    }

    @Override
    public void syncQwMsg(boolean isAll) {
        log.info("同步企微消息开始...");
        Integer start = 0;

        StringBuilder strStart = new StringBuilder("同步");
        StringBuilder strEnd = new StringBuilder("同步");
        if (isAll) {
            strStart.append("全量");
            strEnd.append("全量");
        } else {
            strStart.append("增量");
            strEnd.append("增量");
        }
        strStart.append("企微消息开始");
        strEnd.append("企微消息结束");
        SendRobotReq.Content content = new SendRobotReq.Content();
        content.setContent(strStart.toString());
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }

        Map<String, Map<String, String>> chatRecordMap = new HashMap<>();
        if (!isAll) {
            String cache = jedisCluster.get(PlannerAchievementRankJob.KEY_PLANNER_ACHIEVEMENT_RANK);
            if (StrUtil.isNotBlank(cache)) {
                List<PlannerAchievementInfo> list = JSON.parseArray(cache, PlannerAchievementInfo.class);
                List<String> staffIdList = list.stream().map(PlannerAchievementInfo::getPlannerNo).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(staffIdList)) {
                    for (String staffId : staffIdList) {
                        List<AiPlannerChatContact> chatContactList = aiPlannerChatContactMapper.getPerMaxBusId(staffId);
                        Map<String, String> userChatMap = CollUtil.isEmpty(chatContactList) ? null
                                : chatContactList.stream().collect(Collectors.toMap(AiPlannerChatContact::getUserId, AiPlannerChatContact::getBusId));
                        if (MapUtil.isNotEmpty(userChatMap)) {
                            chatRecordMap.put(staffId, userChatMap);
                        }
                    }
                }
            }
        }

        AtomicBoolean existErr = new AtomicBoolean(false);
        try {
            while (true) {
                LambdaQueryWrapper<AiPlannerTopUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.gt(AiPlannerTopUser::getId, start)
                        .isNotNull(AiPlannerTopUser::getPlannerQwNo).ne(AiPlannerTopUser::getPlannerQwNo, "")
                        .isNotNull(AiPlannerTopUser::getUserUnionId).ne(AiPlannerTopUser::getUserUnionId, "")
                        .orderByAsc(AiPlannerTopUser::getId).last("LIMIT 100");
                List<AiPlannerTopUser> aiPlannerTopUserList = aiPlannerTopUserService.list(queryWrapper);
                if (CollectionUtils.isEmpty(aiPlannerTopUserList)) {
                    log.info("无可使用的客户与理财师关系");
                    break;
                }
                List<AiPlannerTopUser> topUserList = aiPlannerTopUserList.stream()
                        .filter(o -> StrUtil.isNotBlank(o.getPlannerQwNo()) && StrUtil.isNotBlank(o.getUserUnionId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(topUserList)) {
                    log.info("去除空的理财师企微号和空的客户unionId后，无可使用的客户与理财师关系");
                    continue;
                }

                List<String> userUnionIdList = topUserList.stream().map(AiPlannerTopUser::getUserUnionId).distinct().collect(Collectors.toList());
                LambdaQueryWrapper<WeCustomer> wrapper = new LambdaQueryWrapper<>();
                wrapper.select(WeCustomer::getExternalUserid, WeCustomer::getUnionid)
                        .in(WeCustomer::getUnionid, userUnionIdList).eq(WeCustomer::getDelFlag, 0).isNotNull(WeCustomer::getExternalUserid);
                List<WeCustomer> weCustomerList = weCustomerMapper.selectList(wrapper);
                if (CollectionUtils.isEmpty(weCustomerList)) {
                    log.info("根据unionId未匹配到客户的externalUserId");
                    continue;
                }
                Map<String, Set<String>> weCustomerMap = weCustomerList.stream().collect(Collectors.groupingBy(WeCustomer::getUnionid,
                        Collectors.mapping(WeCustomer::getExternalUserid, Collectors.toSet())));
                Map<String, String> customerMap = weCustomerMap.entrySet().stream().filter(entry -> entry.getValue().size() == 1)
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().iterator().next()));
                if (MapUtil.isEmpty(customerMap)) {
                    log.info("去除空的、对应unionId重复的客户externalUserId后，无可使用的客户externalUserId");
                    continue;
                }

                List<AiPlannerTopUserPO> topUserPOList = new ArrayList<>();
                for (AiPlannerTopUser topUser : topUserList) {
                    if (customerMap.containsKey(topUser.getUserUnionId())) {
                        AiPlannerTopUserPO topUserPO = new AiPlannerTopUserPO();
                        BeanUtil.copyProperties(topUser, topUserPO);
                        topUserPO.setUserExternalUserid(customerMap.get(topUser.getUserUnionId()));
                        topUserPOList.add(topUserPO);
                    }
                }

                CountDownLatch latch = new CountDownLatch(topUserPOList.size());
                for (AiPlannerTopUserPO topUserPO : topUserPOList) {
                    syncLinkWechatPoolExecutor.submit(() -> {
                        try {
                            handlePer(topUserPO, chatRecordMap.containsKey(topUserPO.getPlannerNo()) && chatRecordMap.get(topUserPO.getPlannerNo()).containsKey(topUserPO.getUserId())
                                    ? Long.valueOf(chatRecordMap.get(topUserPO.getPlannerNo()).get(topUserPO.getUserId())) : null);
                        } catch (Exception e) {
                            log.error("同步客户userId={}与理财师plannerNo={}的聊天记录发生异常，异常原因为", topUserPO.getUserId(), topUserPO.getPlannerNo(), e);
                            existErr.set(true);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                try {
                    latch.await();
                } catch (Exception e) {
                    log.error("主线程等待异常，异常原因为", e);
                    throw new AiServerException(ResultCode.FAILURE.getCode(), "主线程等待异常");
                }

                if (aiPlannerTopUserList.size() < 100) {
                    break;
                }
                start = aiPlannerTopUserList.get(aiPlannerTopUserList.size() - 1).getId();
            }
        } catch (Exception e) {
            log.error("同步企微消息主线程执行发生异常，异常原因为", e);
            strEnd.append("，主线程执行发生异常");
        }

        if (existErr.get()) {
            strEnd.append("，部分理财师&客户的企微消息同步失败");
        }
        content.setContent(strEnd.toString());
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }
        log.info("同步企微消息结束");
    }

    public void handlePer(AiPlannerTopUserPO topUserPO, Long start) {
        log.info("同步客户userId={}与理财师plannerNo={}的聊天记录开始...", topUserPO.getUserId(), topUserPO.getPlannerNo());
        while (true) {
            LambdaQueryWrapper<WeChatContactMsg> msgWrapper = new LambdaQueryWrapper<>();
            msgWrapper.isNotNull(WeChatContactMsg::getContact).ne(WeChatContactMsg::getContact, "").eq(WeChatContactMsg::getDelFlg, 0);
            if (Objects.nonNull(start)) {
                msgWrapper.gt(WeChatContactMsg::getId, start);
            }
            msgWrapper.and(w -> w.eq(WeChatContactMsg::getFromId, topUserPO.getPlannerQwNo()).eq(WeChatContactMsg::getToList, topUserPO.getUserExternalUserid())
                    .or().eq(WeChatContactMsg::getFromId, topUserPO.getUserExternalUserid()).eq(WeChatContactMsg::getToList, topUserPO.getPlannerQwNo()));
            msgWrapper.and(w -> w.isNull(WeChatContactMsg::getRoomId).or().eq(WeChatContactMsg::getRoomId, ""));
            msgWrapper.orderByAsc(WeChatContactMsg::getId).last("LIMIT 100");
            List<WeChatContactMsg> list = weChatContactMsgMapper.selectList(msgWrapper);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            List<WeChatContactMsg> msgList = new ArrayList<>(list);

            List<String> msgIdList = msgList.stream().map(m -> String.valueOf(m.getId())).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(AiPlannerChatContact::getBusId, msgIdList).notIn(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.nonQwMsgTypeList);
            List<AiPlannerChatContact> existList = list(wrapper);
            if (CollUtil.isNotEmpty(existList)) {
                List<Long> existIdList = existList.stream().map(e -> Long.parseLong(e.getBusId())).collect(Collectors.toList());
                msgList = msgList.stream().filter(m -> !existIdList.contains(m.getId())).collect(Collectors.toList());
            }

            if (CollUtil.isNotEmpty(msgList)) {
                for (WeChatContactMsg msg : msgList) {
                    AiPlannerChatContact chatContact = new AiPlannerChatContact();
                    chatContact.setPlannerNo(topUserPO.getPlannerNo());
                    chatContact.setUserId(topUserPO.getUserId());
                    chatContact.setDirection(topUserPO.getPlannerQwNo().equals(msg.getFromId())
                            ? MsgSendDirectionEnum.FROM_CRM.getCode() : MsgSendDirectionEnum.FROM_CUS.getCode());
                    chatContact.setOriginContent(getOriginContent(msg));
                    chatContact.setOriginType(msg.getMsgType());
                    chatContact.setProcessedContent(getProcessedContent(msg));
                    chatContact.setMsgTime(msg.getMsgTime());
                    chatContact.setBusId(String.valueOf(msg.getId()));
                    if (StrUtil.isNotBlank(chatContact.getOriginContent())) {
                        try {
                            this.save(chatContact);
                            publishWechatMessageBizRecordEvent(msg.getId(),chatContact.getOriginContent(), chatContact.getPlannerNo(),
                                    chatContact.getUserId(), WechatMessageSourceEnum.ENTERPRISE_WECHAT.getCode(), chatContact.getTenantId());
                        } catch (DuplicateKeyException e) {
                            log.info("当前企微消息已同步，busId={}", msg.getId());
                        }
                    }
                }
            }

            if (list.size() < 100) {
                break;
            }
            start = list.get(list.size() - 1).getId();
        }
        log.info("同步客户userId={}与理财师plannerNo={}的聊天记录结束", topUserPO.getUserId(), topUserPO.getPlannerNo());
    }

    private String getOriginContent(WeChatContactMsg msg) {
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(msg.getContact())) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(msg.getContact());
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.TEXT.getType().equals(msg.getMsgType())) {
            return jsonObject.getString("content");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.IMAGE.getType().equals(msg.getMsgType()) || com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.EMOTION.getType().equals(msg.getMsgType())
                || com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.VOICE.getType().equals(msg.getMsgType()) || com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.MEETING_VOICE_CALL.getType().equals(msg.getMsgType())
                || com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.VIDEO.getType().equals(msg.getMsgType())) {
            return jsonObject.getString("attachment");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.LINK.getType().equals(msg.getMsgType())) {
            Map<String, String> content = new HashMap<>();
            content.put("url", jsonObject.getString("link_url"));
            content.put("title", jsonObject.getString("title"));
            return JSON.toJSONString(content);
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.WEAPP.getType().equals(msg.getMsgType())) {
            return jsonObject.getString("title");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.REVOKE.getType().equals(msg.getMsgType())) {
            String msgId = jsonObject.getString("pre_msgid");
            LambdaQueryWrapper<WeChatContactMsg> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WeChatContactMsg::getMsgId, msgId);
            WeChatContactMsg contactMsg = weChatContactMsgMapper.selectOne(wrapper);
            if (Objects.nonNull(contactMsg)) {
                LambdaUpdateWrapper<AiPlannerChatContact> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(AiPlannerChatContact::getBusId, String.valueOf(contactMsg.getId()))
                        .notIn(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.nonQwMsgTypeList)
                        .set(AiPlannerChatContact::getIsRevoke, Boolean.TRUE);
                this.update(updateWrapper);
            }
            return null;
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.EXTERNAL_REDPACKET.getType().equals(msg.getMsgType()) || com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.REDPACKET.getType().equals(msg.getMsgType())) {
            return jsonObject.getString("wish");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.FILE.getType().equals(msg.getMsgType())) {
            Map<String, String> content = new HashMap<>();
            content.put("filename", jsonObject.getString("filename"));
            content.put("url", jsonObject.getString("attachment"));
            return JSON.toJSONString(content);
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.SPHFEED.getType().equals(msg.getMsgType())) {
            return jsonObject.getString("feed_desc");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.MIXED.getType().equals(msg.getMsgType())) {
            List<String> array = JSON.parseArray(jsonObject.getString("item"), String.class);
            if (CollectionUtils.isEmpty(array)) {
                return null;
            }
            array = array.stream().filter(com.baomidou.mybatisplus.core.toolkit.StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(array)) {
                return null;
            }
            List<Map<String, String>> list = new ArrayList<>();
            for (String json : array) {
                JSONObject object = JSON.parseObject(json);
                if (Objects.isNull(object)) {
                    continue;
                }
                Map<String, String> content = new HashMap<>();
                content.put("type", object.getString("type"));
                JSONObject contentMsg = JSON.parseObject(object.getString("content"));
                if (Objects.isNull(contentMsg)) {
                    continue;
                }
                content.put("content", contentMsg.getString(com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.TEXT.getType().equals(content.get("type")) ? "content" : "attachment"));
                list.add(content);
            }
            return CollectionUtils.isEmpty(list) ? null : JSON.toJSONString(list);
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.CHATRECORD.getType().equals(msg.getMsgType())) {
            List<String> array = JSON.parseArray(jsonObject.getString("item"), String.class);
            if (CollectionUtils.isEmpty(array)) {
                return null;
            }
            array = array.stream().filter(com.baomidou.mybatisplus.core.toolkit.StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(array)) {
                return null;
            }
            List<Map<String, String>> list = new ArrayList<>();
            for (String json : array) {
                JSONObject object = JSON.parseObject(json);
                if (Objects.isNull(object) || com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(object.getString("type")) || com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(object.getString("content"))) {
                    continue;
                }
                WeChatContactMsg contactMsg = new WeChatContactMsg();
                String type = object.getString("type").replace("ChatRecord", "").replace("_", "").toLowerCase();
                contactMsg.setMsgType("externalredpacket".equals(type) ? com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.EXTERNAL_REDPACKET.getType()
                        : "meetingvoicecall".equals(type) ? com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.MEETING_VOICE_CALL.getType() : type);
                contactMsg.setContact(object.getString("content"));
                String res = getOriginContent(contactMsg);

                Map<String, String> item = new HashMap<>();
                item.put("type", contactMsg.getMsgType());
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(res)) {
                    item.put("content", res);
                }
                list.add(item);
            }
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }

            Map<String, Object> content = new HashMap<>();
            content.put("title", jsonObject.getString("title"));
            content.put("item", list);
            return JSON.toJSONString(content);
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.CARD.getType().equals(msg.getMsgType())) {
            return jsonObject.getString("corpname");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.LOCATION.getType().equals(msg.getMsgType())) {
            return jsonObject.getString("address");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.MEETING.getType().equals(msg.getMsgType())) {
            return jsonObject.getString("topic");
        }
        return null;
    }

    private String getProcessedContent(WeChatContactMsg msg) {
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(msg.getContact())) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(msg.getContact());
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.LINK.getType().equals(msg.getMsgType())) {
            return "分享链接，链接内容标题为" + jsonObject.getString("title");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.WEAPP.getType().equals(msg.getMsgType())) {
            return "分享小程序，小程序标题为" + jsonObject.getString("title");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.EXTERNAL_REDPACKET.getType().equals(msg.getMsgType()) || com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.REDPACKET.getType().equals(msg.getMsgType())) {
            return "发红包，红包标题为" + jsonObject.getString("wish");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.FILE.getType().equals(msg.getMsgType())) {
            return "发送文件，文件名为" + jsonObject.getString("filename");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.SPHFEED.getType().equals(msg.getMsgType())) {
            return "分享视频号，视频号标题为" + jsonObject.getString("feed_desc");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.CHATRECORD.getType().equals(msg.getMsgType())) {
            return "转发聊天记录，聊天记录标题为" + jsonObject.getString("title");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.CARD.getType().equals(msg.getMsgType())) {
            return "发送名片，名片所属公司为" + jsonObject.getString("corpname");
        } else if (com.yirendai.voiceaiserver.enums.QwMsgTypeEnum.LOCATION.getType().equals(msg.getMsgType())) {
            return "分享地址，具体地址为" + jsonObject.getString("address");
        } else if (QwMsgTypeEnum.MEETING.getType().equals(msg.getMsgType())) {
            return "开启了会议，会议标题为" + jsonObject.getString("topic");
        }
        return null;
    }

    @Override
    public LocalDateTime getMaxMsgTime(String plannerNo, String userId) {
        return baseMapper.getMaxMsgTime(plannerNo, userId);
    }

    @Override
    public IPage<AiPlannerChatContact> userChatMsgPage(PlannerUserChatReq req) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiPlannerChatContact::getPlannerNo, req.getPlannerNo()).eq(AiPlannerChatContact::getUserId, req.getUserId());
        if (StrUtil.isNotBlank(req.getStartTime())) {
            wrapper.ge(AiPlannerChatContact::getMsgTime, LocalDateTime.parse(req.getStartTime(), formatter));
        }
        if (StrUtil.isNotBlank(req.getEndTime())) {
            wrapper.le(AiPlannerChatContact::getMsgTime, LocalDateTime.parse(req.getEndTime(), formatter));
        }
        if (StrUtil.isNotBlank(req.getEndTime()) && StrUtil.isBlank(req.getStartTime())) {
            wrapper.orderByDesc(AiPlannerChatContact::getMsgTime).orderByDesc(AiPlannerChatContact::getId);
        } else {
            wrapper.orderByAsc(AiPlannerChatContact::getMsgTime).orderByAsc(AiPlannerChatContact::getId);
        }
        Page<AiPlannerChatContact> page = new Page<>(req.getCurrent(),
                Objects.nonNull(req.getStartId()) || Objects.nonNull(req.getEndId()) ? req.getSize() * 2 : req.getSize());
        IPage<AiPlannerChatContact> result = this.page(page, wrapper);
        result.setSize(req.getSize());
        if (CollUtil.isEmpty(result.getRecords())) {
            return result;
        }

        if (Objects.nonNull(req.getStartId()) || Objects.nonNull(req.getEndId())) {
            LocalDateTime paramTime = StrUtil.isNotBlank(req.getStartTime()) ?
                    LocalDateTime.parse(req.getStartTime(), formatter) : LocalDateTime.parse(req.getEndTime(), formatter);
            if (!paramTime.truncatedTo(ChronoUnit.SECONDS)
                    .equals(result.getRecords().get(0).getMsgTime().truncatedTo(ChronoUnit.SECONDS))) {
                throw new AiServerException(ResultCode.PARAM_INVALID);
            }

            Long checkId = Objects.nonNull(req.getStartId()) ? req.getStartId() : req.getEndId();
            OptionalInt findIndex = IntStream.range(0, page.getRecords().size())
                    .filter(i -> checkId.equals(result.getRecords().get(i).getId())).findFirst();
            // 默认一秒钟内发不了20及以上条消息
            if (findIndex.isPresent()) {
                int index = findIndex.getAsInt();
                if (index >= result.getRecords().size() - 1) {
                    result.setRecords(Collections.emptyList());
                    result.setTotal(0L);
                } else {
                    result.setRecords(result.getRecords().subList(index + 1,
                            Math.min(index + 1 + req.getSize(), result.getRecords().size())));
                    result.setTotal(result.getTotal() - index - 1);
                }
            } else {
                result.setRecords(result.getRecords().subList(0, Math.min(req.getSize(), result.getRecords().size())));
            }
            result.setPages((long) Math.ceil(((double) result.getTotal()) / result.getSize()));
        }
        if (StrUtil.isNotBlank(req.getEndTime()) && StrUtil.isBlank(req.getStartTime())) {
            result.getRecords()
                    .sort(Comparator.comparing(AiPlannerChatContact::getMsgTime).thenComparing(AiPlannerChatContact::getId));
        }
        return result;
    }

    @Override
    public List<AiPlannerChatContact> userChatMsgList(PlannerUserChatListReq req) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = LocalDateTime.parse(req.getTimePoint(), formatter);

        Page<AiPlannerChatContact> pageBefore = new Page<>(1, req.getSize());
        LambdaQueryWrapper<AiPlannerChatContact> wrapperBefore = new LambdaQueryWrapper<>();
        wrapperBefore.eq(AiPlannerChatContact::getPlannerNo, req.getPlannerNo())
                .eq(AiPlannerChatContact::getUserId, req.getUserId())
                .le(AiPlannerChatContact::getMsgTime, time)
                .orderByDesc(AiPlannerChatContact::getMsgTime);
        IPage<AiPlannerChatContact> pageResBefore = this.page(pageBefore, wrapperBefore);
        pageResBefore.getRecords().sort(Comparator.comparing(AiPlannerChatContact::getMsgTime));

        Page<AiPlannerChatContact> pageAfter = new Page<>(1, req.getSize());
        LambdaQueryWrapper<AiPlannerChatContact> wrapperAfter = new LambdaQueryWrapper<>();
        wrapperAfter.eq(AiPlannerChatContact::getPlannerNo, req.getPlannerNo())
                .eq(AiPlannerChatContact::getUserId, req.getUserId())
                .ge(AiPlannerChatContact::getMsgTime, time)
                .orderByAsc(AiPlannerChatContact::getMsgTime);
        IPage<AiPlannerChatContact> pageResAfter = this.page(pageAfter, wrapperAfter);

        if (CollUtil.isEmpty(pageResAfter.getRecords())) {
            return pageResBefore.getRecords();
        }
        if (CollUtil.isEmpty(pageResBefore.getRecords())) {
            return pageResAfter.getRecords();
        }

        LocalDateTime timeBefore = pageResBefore.getRecords().get(pageResBefore.getRecords().size() - 1).getMsgTime();
        LocalDateTime timeAfter = pageResAfter.getRecords().get(pageResAfter.getRecords().size() - 1).getMsgTime();
        long secondBefore = Math.abs(Duration.between(timeBefore, time).getSeconds());
        long secondAfter = Math.abs(Duration.between(timeAfter, time).getSeconds());
        // 若传入的时间点为日期，则优先取当天的聊天记录
        if (time.getHour() == 0 && time.getMinute() == 0 && time.getSecond() == 0 && secondAfter < 24 * 60 * 60
                || secondAfter <= secondBefore) {
            return pageResAfter.getRecords();
        } else {
            return pageResBefore.getRecords();
        }
    }

    @Override
    public List<AvayaGroupVO> getGroups() {
        return baseMapper.getGroups();
    }

    @Override
    public List<AvayaGroupVO> getGroupsModel() {
        return baseMapper.getGroupsModel();
    }

    @Override
    public LocalDateTime getAllMaxMsgTime() {
        return baseMapper.getAllMaxMsgTime(yrTenantId);
    }

    @Override
    public Map<String, List<AiPlannerChatContact>> fetchUserPlanerDayChat(String date) {
        LocalDateTime startOfDay = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endOfDay = LocalDateTime.now().minusDays(1).withHour(23).withMinute(59).withSecond(59);
        if (!Strings.isNullOrEmpty(date)) {
            LocalDateTime[] startAndEndOfDay = getStartAndEndOfDay(date);
            startOfDay = startAndEndOfDay[0];
            endOfDay = startAndEndOfDay[1];
        }
        // 昨天00:00:00-23:59:59
        LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.notIn(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.nonQwMsgTypeList)
                .ge(AiPlannerChatContact::getMsgTime, startOfDay)
                .le(AiPlannerChatContact::getMsgTime, endOfDay)
                .orderByAsc(AiPlannerChatContact::getMsgTime);
        List<AiPlannerChatContact> list = this.list(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<String, List<AiPlannerChatContact>> map = new HashMap<>();
        for (AiPlannerChatContact contact : list) {
            String key = contact.getPlannerNo() + "-" + contact.getUserId();
            List<AiPlannerChatContact> contacts = map.getOrDefault(key, new ArrayList<>());
            contacts.add(contact);
            map.put(key, contacts);
        }
        return map;
    }

    public LocalDateTime[] getStartAndEndOfDay(String dateString) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析输入字符串为 LocalDate
        LocalDate date = LocalDate.parse(dateString, formatter);

        // 获取当天的开始时间和结束时间
        // 当天00:00:00
        LocalDateTime startOfDay = date.atStartOfDay();
        // 当天23:59:59
        LocalDateTime endOfDay = date.atTime(23, 59, 59);

        return new LocalDateTime[]{startOfDay, endOfDay};
    }


    @Override
    public List<LocalDate> getWeChatTimeWithMonth(
            LocalDateTime startTime, LocalDateTime endTime, String plannerNo, String userId, String tenantId) {
        return baseMapper.getWeChatTimeWithMonth(startTime, endTime, plannerNo, userId, tenantId);
    }

    @Override
    public void syncQwMsgWithTime(boolean isAll) {
        log.info("按照时间同步企微消息开始...");

        StringBuilder strStart = new StringBuilder("按照时间同步");
        StringBuilder strEnd = new StringBuilder("按照时间同步");
        if (isAll) {
            strStart.append("全量");
            strEnd.append("全量");
        } else {
            strStart.append("增量");
            strEnd.append("增量");
        }
        strStart.append("企微消息开始");
        strEnd.append("企微消息结束");
        SendRobotReq.Content content = new SendRobotReq.Content();
        content.setContent(strStart.toString());
        SendRobotReq sendRobotReq = new SendRobotReq();
        sendRobotReq.setText(content);
        sendRobotReq.setMsgtype("text");
        try {
            HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
        } catch (Exception e) {
            log.error("发送企微群机器人通知失败", e);
        }

        try {
            String cache = jedisCluster.get(PlannerAchievementRankJob.KEY_PLANNER_ACHIEVEMENT_RANK);
            if (StrUtil.isBlank(cache)) {
                content.setContent("按照时间同步企微消息结束：理财师缓存列表为空");
                try {
                    HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                } catch (Exception e) {
                    log.error("发送企微群机器人通知失败", e);
                }
                log.info("按照时间同步企微消息结束：理财师缓存列表为空");
                return;
            }
            List<String> staffIdList = JSON.parseArray(cache, PlannerAchievementInfo.class).stream()
                    .map(PlannerAchievementInfo::getPlannerNo).collect(Collectors.toList());
            Map<String, PlannerVO> plannerMap = adsCsgDOrderDetailDfService.getPlannerMap(staffIdList);
            Map<String, String> plannerQwMap = plannerMap.values().stream()
                    .filter(p -> StrUtil.isNotBlank(p.getWxUserId()) && StrUtil.isNotBlank(p.getPlannerNumber()))
                    .collect(Collectors.toMap(PlannerVO::getWxUserId, PlannerVO::getPlannerNumber));
            if (MapUtil.isEmpty(plannerQwMap)) {
                content.setContent("按照时间同步企微消息结束：未查询到理财师企微信息");
                try {
                    HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
                } catch (Exception e) {
                    log.error("发送企微群机器人通知失败", e);
                }
                log.info("按照时间同步企微消息结束：未查询到理财师企微信息");
                return;
            }

            Long startId = null;
            LocalDateTime startTime = isAll ? null : LocalDate.now().minusDays(1).atStartOfDay();
            while (true) {
                LambdaQueryWrapper<WeChatContactMsg> msgWrapper = new LambdaQueryWrapper<>();
                if (Objects.nonNull(startId)) {
                    msgWrapper.gt(WeChatContactMsg::getId, startId);
                }
                if (Objects.nonNull(startTime)) {
                    msgWrapper.ge(WeChatContactMsg::getMsgTime, startTime);
                }
                msgWrapper.eq(WeChatContactMsg::getDelFlg, 0)
                        .isNotNull(WeChatContactMsg::getContact)
                        .and(w -> w.isNull(WeChatContactMsg::getRoomId).or().apply("trim(room_id) = ''"))
                        .apply("trim(contact) != ''")
                        .isNotNull(WeChatContactMsg::getFromId).apply("trim(from_id) != ''").notLike(WeChatContactMsg::getFromId, ",")
                        .isNotNull(WeChatContactMsg::getToList).apply("trim(to_list) != ''").notLike(WeChatContactMsg::getToList, ",")
                        .orderByAsc(WeChatContactMsg::getId).last("LIMIT 50");
                List<WeChatContactMsg> list = weChatContactMsgMapper.selectList(msgWrapper);
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }

                List<WeChatContactMsg> msgList = new ArrayList<>(list);
                List<String> msgIdList = list.stream().map(m -> String.valueOf(m.getId())).collect(Collectors.toList());
                LambdaQueryWrapper<AiPlannerChatContact> wrapper = new LambdaQueryWrapper<>();
                wrapper.select(AiPlannerChatContact::getBusId)
                        .in(AiPlannerChatContact::getBusId, msgIdList)
                        .notIn(AiPlannerChatContact::getOriginType, QwMsgTypeEnum.nonQwMsgTypeList)
                        .eq(AiPlannerChatContact::getTenantId, yrTenantId);
                List<AiPlannerChatContact> existList = list(wrapper);
                if (CollUtil.isNotEmpty(existList)) {
                    List<String> existIdList = existList.stream().map(AiPlannerChatContact::getBusId).collect(Collectors.toList());
                    msgList = msgList.stream().filter(m -> !existIdList.contains(String.valueOf(m.getId()))).collect(Collectors.toList());
                }

                if (CollUtil.isNotEmpty(msgList)) {
                    Set<String> externalUnionIdSet = new HashSet<>();
                    for (WeChatContactMsg weChatMsg : msgList) {
                        externalUnionIdSet.add(weChatMsg.getFromId());
                        externalUnionIdSet.add(weChatMsg.getToList());
                    }
                    LambdaQueryWrapper<WeCustomer> weCustomerWrapper = new LambdaQueryWrapper<>();
                    weCustomerWrapper.select(WeCustomer::getExternalUserid, WeCustomer::getUnionid)
                            .in(WeCustomer::getExternalUserid, externalUnionIdSet).eq(WeCustomer::getDelFlag, 0)
                            .isNotNull(WeCustomer::getUnionid).apply("trim(unionid) != ''")
                            .groupBy(WeCustomer::getExternalUserid);
                    List<WeCustomer> weCustomerList = weCustomerMapper.selectList(weCustomerWrapper);
                    if (CollUtil.isNotEmpty(weCustomerList)) {
                        Map<String, String> weCustomerMap = weCustomerList.stream()
                                .collect(Collectors.toMap(WeCustomer::getExternalUserid, WeCustomer::getUnionid));
                        for (WeChatContactMsg msg : msgList) {
                            LambdaQueryWrapper<AiPlannerTopUser> topUserWrapper = new LambdaQueryWrapper<>();
                            if (weCustomerMap.containsKey(msg.getFromId()) && plannerQwMap.containsKey(msg.getToList())) {
                                topUserWrapper.eq(AiPlannerTopUser::getPlannerNo, plannerQwMap.get(msg.getToList()))
                                        .eq(AiPlannerTopUser::getUserUnionId, weCustomerMap.get(msg.getFromId()));
                            } else if (weCustomerMap.containsKey(msg.getToList()) && plannerQwMap.containsKey(msg.getFromId())) {
                                topUserWrapper.eq(AiPlannerTopUser::getPlannerNo, plannerQwMap.get(msg.getFromId()))
                                        .eq(AiPlannerTopUser::getUserUnionId, weCustomerMap.get(msg.getToList()));
                            } else {
                                continue;
                            }
                            topUserWrapper.eq(AiPlannerTopUser::getTenantId, yrTenantId);
                            AiPlannerTopUser topUser = aiPlannerTopUserService.getOne(topUserWrapper);
                            if (Objects.isNull(topUser)) {
                                continue;
                            }

                            AiPlannerChatContact chatContact = new AiPlannerChatContact();
                            chatContact.setPlannerNo(topUser.getPlannerNo());
                            chatContact.setUserId(topUser.getUserId());
                            chatContact.setDirection(weCustomerMap.containsKey(msg.getFromId()) ?
                                    MsgSendDirectionEnum.FROM_CUS.getCode() : MsgSendDirectionEnum.FROM_CRM.getCode());
                            chatContact.setOriginContent(getOriginContent(msg));
                            chatContact.setOriginType(msg.getMsgType());
                            chatContact.setProcessedContent(getProcessedContent(msg));
                            chatContact.setMsgTime(msg.getMsgTime());
                            chatContact.setBusId(String.valueOf(msg.getId()));
                            chatContact.setTenantId(yrTenantId);
                            if (StrUtil.isNotBlank(chatContact.getOriginContent())) {
                                this.save(chatContact);
                                publishWechatMessageBizRecordEvent(msg.getId(),chatContact.getOriginContent(), chatContact.getPlannerNo(),
                                        chatContact.getUserId(), WechatMessageSourceEnum.ENTERPRISE_WECHAT.getCode(), chatContact.getTenantId());
                            }
                        }
                    }
                }

                if (list.size() < 50) {
                    break;
                }
                startId = list.get(list.size() - 1).getId();
            }

            content.setContent(strEnd.toString());
            try {
                HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
            } catch (Exception e) {
                log.error("发送企微群机器人通知失败", e);
            }
            log.info("按照时间同步企微消息结束");
        } catch (Exception e) {
            log.error("按照时间同步全量/增量语音发生异常，异常原因为", e);
            strEnd.append("\n异常原因为").append(e.getMessage());
            content.setContent(strEnd.toString());
            try {
                HttpClientProxyUtil.getInstance().postForJSon(robotSyncUrl, JSON.toJSONString(sendRobotReq));
            } catch (Exception exception) {
                log.error("发送企微群机器人通知失败", exception);
            }
            throw e;
        }
    }

    @Override
    public List<Long> getIdRangeWithinTimePeriod(Long endId, LocalDateTime startTime, LocalDateTime endTime, Integer size) {
        return baseMapper.getIdRangeWithinTimePeriod(endId, startTime, endTime, size);
    }

    @Override
    public Long getChatContactIdByUuid(String uuid) {
        return baseMapper.getChatContactIdByUuid(uuid);
    }

    @Override
    public Long getLatestMsgCountByPlannerNo(String plannerNo, String userId, List<String> nonQwMsgTypeList) {
        return baseMapper.getLatestMsgCountByPlannerNo(plannerNo, userId, nonQwMsgTypeList);
    }

    @Override
    public List<AiPlannerChatContact> getLatestMsgByPlannerNoWithPage(String plannerNo, String userId, List<String> nonQwMsgTypeList, Integer offset, Integer limit) {
        return baseMapper.getLatestMsgByPlannerNoWithPage(plannerNo, userId, nonQwMsgTypeList, offset, limit);
    }

    @Override
    public List<AiPlannerChatContact> fetchHongKongInsuranceDataForInit(LocalDateTime startTime, LocalDateTime endTime, Long lastId, Integer limit, String tenantId) {
        LambdaQueryWrapper<AiPlannerChatContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(AiPlannerChatContact::getMsgTime, startTime)
                .le(AiPlannerChatContact::getMsgTime, endTime)
                .in(AiPlannerChatContact::getOriginType, "call_center", "avaya")
                .isNotNull(AiPlannerChatContact::getProcessedContent)
                .ne(AiPlannerChatContact::getProcessedContent, "")
                .orderByAsc(AiPlannerChatContact::getId);

        // 添加租户限制
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            queryWrapper.eq(AiPlannerChatContact::getTenantId, tenantId);
        }

        if (lastId != null && lastId > 0) {
            queryWrapper.gt(AiPlannerChatContact::getId, lastId);
        }

        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        } else {
            queryWrapper.last("LIMIT 1000");
        }

        return this.list(queryWrapper);
    }

    /**
     * 发布微信消息业务记录事件
     */
    private void publishWechatMessageBizRecordEvent(Long messageId,String content, String plannerNo, String userId, Integer source, String tenantId) {
        try {
            WechatMessageBizRecordEvent event = new WechatMessageBizRecordEvent(this);
            event.setMessageId(messageId);
            event.setContent(content);
            event.setPlannerNo(plannerNo);
            event.setUserIds(Sets.newHashSet(userId));
            event.setSource(source);
            event.setTenantId(tenantId);
            applicationContext.publishEvent(event);
            log.debug("发布微信消息业务记录事件成功: plannerNo={}, userId={}", plannerNo, userId);
        } catch (Exception e) {
            log.error("发布微信消息业务记录事件失败: plannerNo={}, userId={}", plannerNo, userId, e);
        }
    }
}
