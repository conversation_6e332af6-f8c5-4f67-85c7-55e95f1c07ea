package com.yirendai.workbench.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.voiceaiserver.service.AiMediaService;
import com.yirendai.voiceaiserver.service.impl.AiVoiceToTextService;
import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.voiceaiserver.vo.response.AudioToTextVo;
import com.yirendai.workbench.config.NasProperty;
import com.yirendai.workbench.consumer.bean.CallCenterWechatMessageChangeEvent;
import com.yirendai.workbench.consumer.bean.WechatMessageBizRecordEvent;
import com.yirendai.workbench.entity.CallCenterWechatContact;
import com.yirendai.workbench.entity.CallCenterWechatMessage;
import com.yirendai.workbench.entity.callcenter.CallcenterWechatCustomerMapping;
import com.yirendai.workbench.enums.WechatMessageSourceEnum;
import com.yirendai.workbench.enums.WechatMessageTypeEnum;
import com.yirendai.workbench.mapper.CallCenterWechatMessageMapper;
import com.yirendai.workbench.service.HttpFileDownloader;
import com.yirendai.workbench.service.ICallCenterWechatContactService;
import com.yirendai.workbench.service.ICallCenterWechatMessageService;
import com.yirendai.workbench.service.callcenter.ICallcenterWechatCustomerMappingService;
import com.yirendai.workbench.util.WechatMessageUtil;
import com.yirendai.workbench.vo.req.WechatMessagePageReq;
import com.yirendai.workbench.vo.res.WechatMessagePageRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 微信聊天记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class CallCenterWechatMessageServiceImpl extends ServiceImpl<CallCenterWechatMessageMapper, CallCenterWechatMessage> implements ICallCenterWechatMessageService {

    @Resource
    private NasProperty nasProperty;

    private static final String ANMI_WECHAT_DIR = "anmiWechat/";

    @Resource
    private ICallCenterWechatContactService wechatContactService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private AiMediaService aiMediaService;


    @Resource
    private AiVoiceToTextService aiVoiceToTextService;

    @Resource
    private ICallcenterWechatCustomerMappingService callcenterWechatCustomerMappingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateMessageInfo(CallCenterWechatMessage callCenterWechatMessage, CallCenterWechatMessage existingMessage) {
        if (existingMessage != null) {
            // 存在记录，执行更新操作
            callCenterWechatMessage.setId(existingMessage.getId());
            callCenterWechatMessage.setCreateTime(existingMessage.getCreateTime());
            callCenterWechatMessage.setUpdateTime(LocalDateTime.now());

            // 处理资源URL（如果有新的资源URL）
            if (StringUtils.isNotBlank(callCenterWechatMessage.getResourceUrl())) {
                handleResourceDownload(callCenterWechatMessage);
            } else {
                // 如果没有新的资源URL，保留原有的nasPath
                callCenterWechatMessage.setNasPath(existingMessage.getNasPath());
            }

            // 保留原有的voiceContent（如果新数据没有的话）
            if (StringUtils.isBlank(callCenterWechatMessage.getVoiceContent()) && StringUtils.isNotBlank(existingMessage.getVoiceContent())) {
                callCenterWechatMessage.setVoiceContent(existingMessage.getVoiceContent());
            }

            this.updateById(callCenterWechatMessage);
            log.info("更新微信消息记录: wechatId={}", callCenterWechatMessage.getWechatId());
        } else {
            // 不存在记录，执行插入操作
            callCenterWechatMessage.setCreateTime(LocalDateTime.now());
            callCenterWechatMessage.setUpdateTime(LocalDateTime.now());

            // 处理资源URL下载
            handleResourceDownload(callCenterWechatMessage);

            this.save(callCenterWechatMessage);
            log.info("插入新微信消息记录: wechatId={}", callCenterWechatMessage.getWechatId());
        }

        // 触发语音转文字事件（如果需要）
        triggerVoiceToTextEventIfNeeded(callCenterWechatMessage);
        publishWechatMessageBizRecordEvent(callCenterWechatMessage);
    }

    @Override
    public Page<WechatMessagePageRes> pageWechatMessage(WechatMessagePageReq request) {
        String tenantId = AuthUtil.getTenantId();
        Page<CallCenterWechatMessage> pageInfo = this.lambdaQuery().eq(CallCenterWechatMessage::getTenantId, AuthUtil.getTenantId())
                .eq(CallCenterWechatMessage::getUserNo, request.getPlannerNo())
                .eq(CallCenterWechatMessage::getTenantId, tenantId)
                .and(w -> w.eq(CallCenterWechatMessage::getSender, request.getCustomerWechatId())
                        .or()
                        .eq(CallCenterWechatMessage::getRecipient, request.getCustomerWechatId()))
                .and(w -> w.eq(CallCenterWechatMessage::getSender, request.getPlannerWechatId())
                        .or()
                        .eq(CallCenterWechatMessage::getRecipient, request.getPlannerWechatId()))
                .ge(request.getStartTime() != null, CallCenterWechatMessage::getSendTime, request.getStartTime())
                .le(request.getEndTime() != null, CallCenterWechatMessage::getSendTime, request.getEndTime())
                .groupBy(CallCenterWechatMessage::getWechatId)
                .orderByDesc(CallCenterWechatMessage::getSendTime)
                .page(new Page<>(request.getPageNo(), request.getPageSize()));

        // 转换为响应对象
        Page<WechatMessagePageRes> result = new Page<>(request.getPageNo(), request.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());

        if (!CollectionUtils.isEmpty(pageInfo.getRecords())) {
            // 查询联系人信息获取头像
            CallCenterWechatContact contactInfo = getContactInfo(tenantId, request.getPlannerWechatId(), request.getCustomerWechatId());

            List<WechatMessagePageRes> responseList = pageInfo.getRecords().stream()
                    .map(message -> {
                        WechatMessagePageRes res = new WechatMessagePageRes();
                        // 基本字段复制
                        res.setId(message.getId());
                        res.setWechatId(message.getWechatId());
                        res.setType(message.getType());
                        res.setIsSend(message.getIsSend());
                        res.setSender(message.getSender());
                        res.setRecipient(message.getRecipient());
                        res.setContent(message.getContent());
                        res.setVoiceContent(message.getVoiceContent());
                        res.setSourceType(message.getSourceType());
                        res.setSendTime(message.getSendTime());
                        res.setResourceUrl(message.getResourceUrl());
                        if (StringUtils.isNotBlank(message.getNasPath())) {
                            res.setNasPath(nasProperty.getNasUrl() + message.getNasPath());
                        }
                        res.setDirection(1);
                        res.setResourceLength(message.getResourceLength());
                        res.setUserNo(message.getUserNo());
                        // 设置头像信息
                        if (contactInfo != null) {
                            res.setCustomerWechatImg(contactInfo.getCustomerWechatImg());
                            res.setPlannerWechatImg(contactInfo.getPlannerWechatImg());
                            res.setDirection(Objects.equals(contactInfo.getPlannerWechatId(), message.getSender()) ? 1 : 2);
                        }
                        // 生成微信格式的时长显示
                        String duration = WechatMessageUtil.generateDurationDisplay(
                                message.getType(),
                                message.getResourceLength()
                        );
                        res.setDuration(duration);
                        return res;
                    }).sorted(Comparator.comparing(WechatMessagePageRes::getSendTime)).collect(Collectors.toList());
            result.setRecords(responseList);
        }
        return result;

    }

    @Override
    public void handlerVoiceToText(CallCenterWechatMessage message) {

        if (StringUtils.isBlank(message.getNasPath()) || StringUtils.isNotBlank(message.getVoiceContent())) {
            return;
        }

        if (!message.getType().equals(WechatMessageTypeEnum.VOICE.getCallCenterType()) &&
                !message.getType().equals(WechatMessageTypeEnum.VOICE_VIDEO_CALL.getCallCenterType()) &&
                !message.getType().equals(WechatMessageTypeEnum.VIDEO.getCallCenterType())) {
            return;
        }

        if (message.getType().equals(WechatMessageTypeEnum.VOICE.getCallCenterType())) {
            processVoiceMessage(message);
        }

        if (message.getType().equals(WechatMessageTypeEnum.VIDEO.getCallCenterType())) {
            processVideoMessage(message);
        }

        if (message.getType().equals(WechatMessageTypeEnum.VOICE_VIDEO_CALL.getCallCenterType())) {
            processVoiceVideoCallMessage(message);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessageNasPath(CallCenterWechatMessage message, String resourceUrl) {
        String nasPath = "";
        if (StringUtils.isNotBlank(resourceUrl)) {
            nasPath = downloadResourceToNas(resourceUrl, message.getWechatId());
        }
        if (StringUtils.isNotBlank(nasPath)) {
            message.setNasPath(nasPath);
            this.lambdaUpdate().set(CallCenterWechatMessage::getNasPath, nasPath).eq(CallCenterWechatMessage::getId, message.getId())
                    .update();
            triggerVoiceToTextEventIfNeeded(message);
        }
    }

    @Override
    public String getWechatMessageFilePath(Long wechatMessageId) {
        // 根据ID查询微信聊天记录
        CallCenterWechatMessage wechatMessage = this.getById(wechatMessageId);
        if (wechatMessage == null || StringUtils.isBlank(wechatMessage.getNasPath())) {
            log.warn("微信聊天记录不存在: wechatMessageId={}", wechatMessageId);
            return null;
        }
        // 构建完整的文件路径
        return nasProperty.getNasPath() + wechatMessage.getNasPath();
    }


    private CallCenterWechatContact getContactInfo(String tenantId, String plannerWechatId, String customerWechatId) {
        return wechatContactService.lambdaQuery()
                .eq(CallCenterWechatContact::getTenantId, tenantId)
                .eq(CallCenterWechatContact::getPlannerWechatId, plannerWechatId)
                .eq(CallCenterWechatContact::getCustomerWechatId, customerWechatId)
                .last("limit 1").one();
    }


    /**
     * 从资源URL中提取文件格式
     */
    private String getResourceFormatFromUrl(String resourceUrl) {
        try {

            // 去掉查询参数部分
            String urlWithoutParams = resourceUrl.contains("?") ?
                    resourceUrl.substring(0, resourceUrl.indexOf("?")) : resourceUrl;

            // 提取文件扩展名
            int lastDotIndex = urlWithoutParams.lastIndexOf(".");
            if (lastDotIndex > 0 && lastDotIndex < urlWithoutParams.length() - 1) {
                return urlWithoutParams.substring(lastDotIndex + 1).toLowerCase();
            }
        } catch (Exception e) {
            log.warn("提取资源格式失败: resourceUrl={}", resourceUrl, e);
        }

        return "unknown";
    }

    /**
     * 处理语音消息转文字
     *
     * @param message 微信消息对象
     */
    private void processVoiceMessage(CallCenterWechatMessage message) {
        try {
            File targetFile = null;
            try {
                // 1、使用本地NAS路径的语音文件
                File sourceFile = new File(nasProperty.getNasPath() + message.getNasPath());
                if (!sourceFile.exists()) {
                    log.warn("本地语音文件不存在: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath());
                    return;
                }

                // 2、语音文件转换为mp3格式
                targetFile = aiMediaService.audioToMp3(sourceFile);
                if (targetFile == null) {
                    log.warn("语音文件格式转换失败: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath());
                    return;
                }

                // 3、调用语音转文字接口
                String voiceText = aiMediaService.audioToText(targetFile, WechatMessageTypeEnum.VOICE.getCallCenterType());

                // 4、更新语音转文字结果
                if (StringUtils.isNotBlank(voiceText)) {
                    message.setVoiceContent(voiceText);
                    this.updateById(message);
                    log.info("语音转文字处理完成: wechatId={}, voiceContent={}", message.getWechatId(), voiceText);
                } else {
                    log.warn("语音转文字结果为空: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath());
                }

            } finally {
                // 5、清理临时转换文件
                if (targetFile != null && targetFile.exists()) {
                    boolean deleted = targetFile.delete();
                    if (deleted) {
                        log.debug("临时转换文件删除成功: {}", targetFile.getAbsolutePath());
                    }
                }
            }
        } catch (Exception e) {
            log.error("语音转文字处理异常: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath(), e);
        }
    }


    /**
     * 处理视频消息转文字
     *
     * @param message 微信消息对象
     */
    private void processVideoMessage(CallCenterWechatMessage message) {
        try {
            File audioFile = null;
            try {
                // 1、使用本地NAS路径的视频文件
                File sourceFile = new File(nasProperty.getNasPath() + message.getNasPath());
                if (!sourceFile.exists()) {
                    log.warn("本地视频文件不存在: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath());
                    return;
                }

                // 2、视频文件转换为音频文件（参考AiMediaServiceImpl#videoHandle逻辑）
                audioFile = aiMediaService.videoToAudio(sourceFile);
                if (audioFile == null) {
                    log.warn("视频文件转音频失败: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath());
                    return;
                }

                // 3、音频文件转换为文字
                String voiceText = aiMediaService.audioToText(audioFile, WechatMessageTypeEnum.VIDEO.getCallCenterType());

                // 4、更新语音转文字结果
                if (StringUtils.isNotBlank(voiceText)) {
                    message.setVoiceContent(voiceText);
                    this.updateById(message);
                    log.info("视频转文字处理完成: wechatId={}, voiceContent={}", message.getWechatId(), voiceText);
                } else {
                    log.warn("视频转文字结果为空: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath());
                }

            } finally {
                // 5、清理临时音频文件
                if (audioFile != null && audioFile.exists()) {
                    boolean deleted = audioFile.delete();
                    if (deleted) {
                        log.debug("临时音频文件删除成功: {}", audioFile.getAbsolutePath());
                    }
                }
            }
        } catch (Exception e) {
            log.error("视频转文字处理异常: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath(), e);
        }
    }

    /**
     * 处理音视频通话消息转文字
     *
     * @param message 微信消息对象
     */
    private void processVoiceVideoCallMessage(CallCenterWechatMessage message) {
        try {
            // 1、使用本地NAS路径的音视频通话文件（已经是wav格式）
            File wavFile = new File(nasProperty.getNasPath() + message.getNasPath());
            if (!wavFile.exists()) {
                log.warn("本地音视频通话文件不存在: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath());
                return;
            }

            // 2、处理音视频通话文件转文字
            String voiceText = processVoiceVideoCallContent(wavFile);

            // 3、更新语音转文字结果
            if (StringUtils.isNotBlank(voiceText)) {
                message.setVoiceContent(voiceText);
                this.updateById(message);
                log.info("音视频通话转文字处理完成: wechatId={}, voiceContent={}", message.getWechatId(), voiceText);
            }
        } catch (Exception e) {
            log.error("音视频通话转文字处理异常: wechatId={}, nasPath={}", message.getWechatId(), message.getNasPath(), e);
            // 异常情况下也设置默认显示文本
            try {
                message.setVoiceContent("[音视频通话]");
                this.updateById(message);
            } catch (Exception updateException) {
                log.error("更新音视频通话默认文本失败: wechatId={}", message.getWechatId(), updateException);
            }
        }
    }

    /**
     * @param wavFile 音视频通话wav文件
     * @return 转换后的文字内容
     */
    private String processVoiceVideoCallContent(File wavFile) {
        List<File> leftFileList = new ArrayList<>();
        List<File> rightFileList = new ArrayList<>();
        List<File> fileList = null;
        StringBuilder sb = new StringBuilder();

        try {
            // 1、将音频文件按时间分段（每60秒一段）
            fileList = FileUtil.splitAudioFile(wavFile, 60, 0);

            // 2、处理每个音频分段
            for (File segmentFile : fileList) {
                String leftChannelOutputFile = RandomUtil.randomString(32) + ".wav";
                String rightChannelOutputFile = RandomUtil.randomString(32) + ".wav";

                try {
                    // 3、分离左右声道
                    FileUtil.splitAudioByChannel(segmentFile.getAbsolutePath(), leftChannelOutputFile, rightChannelOutputFile);
                    File leftFile = new File(leftChannelOutputFile);
                    File rightFile = new File(rightChannelOutputFile);
                    leftFileList.add(leftFile);
                    rightFileList.add(rightFile);

                    // 4、分别对左右声道进行语音识别
                    List<AudioToTextVo.Segment> allSegmentList = new ArrayList<>();
                    allSegmentList.addAll(getVoiceSegments(leftChannelOutputFile, "客户"));
                    allSegmentList.addAll(getVoiceSegments(rightChannelOutputFile, "理财师"));

                    // 5、按时间顺序排序并拼接结果
                    allSegmentList.sort(Comparator.comparingInt(AudioToTextVo.Segment::getStart));
                    String segmentContent = allSegmentList.stream()
                            .map(s -> s.getSpeaker() + ":" + s.getText())
                            .collect(Collectors.joining("\n"));
                    sb.append(segmentContent);

                } catch (Exception e) {
                    log.error("处理音频分段失败: segmentFile={}", segmentFile.getAbsolutePath(), e);
                }
            }

        } catch (Exception e) {
            log.error("处理音视频通话内容异常", e);
        } finally {
            // 6、清理所有临时文件
            if (CollectionUtil.isNotEmpty(fileList)) {
                fileList.forEach(File::delete);
            }
            if (CollectionUtil.isNotEmpty(leftFileList)) {
                leftFileList.forEach(File::delete);
            }
            if (CollectionUtil.isNotEmpty(rightFileList)) {
                rightFileList.forEach(File::delete);
            }
        }

        if (sb.length() > 0) {
            return sb.toString();
        }
        return null;
    }

    /**
     * 获取语音分段识别结果（参考QwCallService#getSegment逻辑）
     *
     * @param filePath 音频文件路径
     * @param speaker  说话人标识
     * @return 语音识别分段结果
     */
    private List<AudioToTextVo.Segment> getVoiceSegments(String filePath, String speaker) {
        try {
            JSONObject textJson = aiVoiceToTextService.voiceToTextJson(new File(filePath), "ce-asr", null, null, null, null);
            if (Objects.isNull(textJson)) {
                return Collections.emptyList();
            }

            AudioToTextVo audioToTextVo = JSON.parseObject(String.valueOf(textJson), AudioToTextVo.class);
            if (CollUtil.isEmpty(audioToTextVo.getSegments())) {
                return Collections.emptyList();
            }

            return audioToTextVo.getSegments().stream()
                    .peek(s -> s.setSpeaker(speaker))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取语音分段识别结果异常: filePath={}, speaker={}", filePath, speaker, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理资源URL下载到NAS的公共方法
     */
    private void handleResourceDownload(CallCenterWechatMessage callCenterWechatMessage) {
        if (StringUtils.isNotBlank(callCenterWechatMessage.getResourceUrl())) {
            String nasPath = downloadResourceToNas(callCenterWechatMessage.getResourceUrl(), callCenterWechatMessage.getWechatId());
            if (StringUtils.isNotBlank(nasPath)) {
                callCenterWechatMessage.setNasPath(nasPath);
            }
        }
    }

    /**
     * 下载资源文件到NAS存储
     *
     * @param resourceUrl 资源URL
     * @param wechatId    微信消息ID
     * @return NAS存储路径，下载失败返回null
     */
    private String downloadResourceToNas(String resourceUrl, Long wechatId) {
        try {
            // 获取资源格式（从URL中提取文件扩展名）
            String resourceFormat = getResourceFormatFromUrl(resourceUrl);

            // 构建文件目录和文件名
            String fileDir = nasProperty.getNasPath() + ANMI_WECHAT_DIR;
            String fileName = wechatId + "_" + System.currentTimeMillis() + "." + resourceFormat;

            // 处理URL替换
            String fileUrl = resourceUrl;
            fileUrl = fileUrl.replace("http://s3.yixin.com", "http://s3.jishu.idc");
            fileUrl = StringUtils.replace(fileUrl, "http://s3-test.yixin.com", "http://s3.caiwu.corp");

            // 下载文件到本地NAS
            HttpFileDownloader.downloadFileWithNIO(fileUrl, fileDir, fileName);

            // 返回相对路径
            String nasPath = ANMI_WECHAT_DIR + fileName;
            log.info("微信资源文件下载完成: wechatId={}, fileName={}, nasPath={}", wechatId, fileName, nasPath);
            return nasPath;
        } catch (Exception e) {
            log.error("微信资源文件下载失败: wechatId={}, resourceUrl={}", wechatId, resourceUrl, e);
            return null;
        }
    }

    /**
     * 触发语音转文字事件的公共方法
     */
    private void triggerVoiceToTextEventIfNeeded(CallCenterWechatMessage callCenterWechatMessage) {
        if (StringUtils.isNotBlank(callCenterWechatMessage.getNasPath()) && StringUtils.isBlank(callCenterWechatMessage.getVoiceContent())) {
            CallCenterWechatMessageChangeEvent event = new CallCenterWechatMessageChangeEvent(this);
            event.setCallCenterWechatMessage(callCenterWechatMessage);
            applicationContext.publishEvent(event);
        }
    }

    /**
     * 发布微信消息业务记录事件（个微消息）
     */
    private void publishWechatMessageBizRecordEvent(CallCenterWechatMessage message) {
        try {
            // 只处理文本消息或已转文字的语音消息
            String content = message.getContent();

            if (StringUtils.isBlank(content)) {
                log.debug("消息内容为空，跳过发布业务记录事件: wechatId={}", message.getWechatId());
                return;
            }

            // 从联系人表获取理财师编号和客户ID
            String plannerNo = message.getUserNo();
            if (StringUtils.isBlank(plannerNo)) {
                log.debug("理财师编号为空，跳过发布业务记录事件: wechatId={}", message.getWechatId());
                return;
            }

            // 通过联系人信息获取客户ID
            Set<String> customerIds = callcenterWechatCustomerMappingService.getCustomerIdsByWechatNo(message.getRecipient());
            if (CollectionUtils.isEmpty(customerIds)) {
                log.debug("无法获取客户ID，跳过发布业务记录事件: wechatId={}", message.getWechatId());
                return;
            }

            WechatMessageBizRecordEvent event = new WechatMessageBizRecordEvent(this);
            event.setMessageId(message.getWechatId());
            event.setContent(content);
            event.setPlannerNo(plannerNo);
            event.setUserIds(customerIds);
            event.setSource(WechatMessageSourceEnum.PERSONAL_WECHAT.getCode()); // 来源：2-个微
            event.setTenantId(message.getTenantId());

            applicationContext.publishEvent(event);
            log.debug("发布个微消息业务记录事件成功: wechatId={}, plannerNo={}, userIds={}", message.getWechatId(), plannerNo, customerIds);
        } catch (Exception e) {
            log.error("发布个微消息业务记录事件失败: wechatId={}", message.getWechatId(), e);
        }
    }
}