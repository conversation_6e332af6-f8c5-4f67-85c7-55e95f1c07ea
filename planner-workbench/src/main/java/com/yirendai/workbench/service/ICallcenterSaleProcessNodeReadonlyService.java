package com.yirendai.workbench.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode;

import java.util.List;

/**
 * <p>
 * 销售流程节点基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
public interface ICallcenterSaleProcessNodeReadonlyService extends IService<CallcenterSaleProcessNode> {

    List<CallcenterSaleProcessNode> getListByIdsReadonly(List<String> ids);

    CallcenterSaleProcessNode selectBeanByIdReadonly(String nodeId);

}
