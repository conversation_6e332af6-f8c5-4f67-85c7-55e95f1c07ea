package com.yirendai.workbench.service.callcenter;

import com.yirendai.workbench.vo.req.callcenter.ChangePlannerReq;
import com.yirendai.workbench.vo.res.callcenter.*;
import com.yirendai.workbench.wrapper.dto.UserRegistryRequest;
import com.yirendai.workbench.wrapper.dto.UserRegistryResponse;
import org.springframework.web.bind.annotation.PathVariable;

import java.time.LocalDateTime;
import java.util.List;

public interface CustomerDataService {
    /**
     * 获取用户福利信息
     * @param userId 用户ID
     * @return
     */
    CustomerWelfare getCustomerWelfare(String userId);


    /**
     * 获取客户策略信息
     * @param userId 用户ID
     * @param count 获取策略信息的数量
     * @param channel 渠道
     * @return
     */
    List<ReminderVo> getReminder(String userId, Integer count, String channel);

    /**
     * 获取客户活动报名信息
     * @param request
     * @return
     */
    List<UserRegistryResponse> getActivityRegistry(UserRegistryRequest request);

    /**
     * 获取客户联系记录
     * @param userId 用户ID
     * @param days 时间范围
     * @return
     */
    ContactRecord getContactRecord(String userId, Integer days);


    /**
     * 获取客户策略,活动报名信息,联系记录并整合
     * @param userId
     * @return
     */
    CustomerIntegrateVo getCustomerIntegrateVo(String userId);


    /**
     * 获取客户基本信息及资产配置
     * @param userId
     * @return
     */
    CustomerAndAssetResp getCustomerBaseInfoAndAsset(@PathVariable String userId);

    /**
     * 变更理财师
     * @param req
     * @return
     */
    Boolean changesPlanner(ChangePlannerReq req,Boolean checkAuthFlag);

    /**
     *
     * @param userId
     * @return
     */
    Boolean changesFlowUpStatus(String userId);


    /**
     * 获取客户所有基本信息(仅支持 宜人渠道 & 非虚拟用户)
     * 包括客户策略,联系记录,活动报名信息,客户福利,客户基本信息及在持资产查询,账户信息,客户备注,客户KYC明细
     */
    CustomerBasicInfoVo getCustomerBasicInfo(String userId, String tenantId);


    /**
     * 获取客户业务信息,包括客户详情中的资产信息,账户信息,账户流水
     */
    CustomerBusinessInfoVo getCustomerBusinessInfo(String userId, String tenantId, LocalDateTime startTime, LocalDateTime endTime);
}
