package com.yirendai.workbench.service.callcenter.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yirendai.voiceaiserver.mapper.owmuc.UserRegistryInfoMapper;
import com.yirendai.voiceaiserver.model.owmuc.UserRegistryInfo;
import com.yirendai.voiceaiserver.model.owmuc.UserVerifyInfo;
import com.yirendai.voiceaiserver.service.UserVerifyInfoService;
import com.yirendai.workbench.avaya.mapper.CxListStatusMapper;
import com.yirendai.workbench.constant.CallServiceConstant;
import com.yirendai.workbench.consumer.bean.CallCenterUserChangeEvent;
import com.yirendai.workbench.convert.CallcenterUserConvert;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.entity.callcenter.CallcenterUser;
import com.yirendai.workbench.entity.callcenter.CallcenterUserHis;
import com.yirendai.workbench.entity.callcenter.UserDevelopInfo;
import com.yirendai.workbench.entity.callcenter.UserFollowUpInfo;
import com.yirendai.workbench.enums.callcenter.*;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.mapper.CallcenterUserMapper;
import com.yirendai.workbench.mapper.PlannerUserBindMapper;
import com.yirendai.workbench.model.CallcenterUserDto;
import com.yirendai.workbench.model.PlannerInfoDto;
import com.yirendai.workbench.model.UserAllocateInfo;
import com.yirendai.workbench.service.IBladeDeptService;
import com.yirendai.workbench.service.IBladeUserService;
import com.yirendai.workbench.service.IPlannerUserBindHistoryService;
import com.yirendai.workbench.service.IPlannerUserBindService;
import com.yirendai.workbench.service.callcenter.*;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.callcenter.DelayDevelopCycleReq;
import com.yirendai.workbench.vo.res.callcenter.BatchDelayDevelopCycleRes;
import com.yirendai.workbench.vo.res.callcenter.DelayDevelopCycleTemplateRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springblade.core.excel.listener.DataListener;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yirendai.workbench.enums.callcenter.UserStatusEnum.ALLOCATION;
import static com.yirendai.workbench.exception.BusinessError.*;
import static java.util.Comparator.comparingLong;
import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * {@code @time} 2025/2/20 09:51
 **/
@Service
@Slf4j
public class CallcenterUserServiceImpl extends ServiceImpl<CallcenterUserMapper, CallcenterUser> implements CallcenterUserService {
    @Resource
    private CallcenterUserConvert callcenterUserConvert;

    @Resource
    private SceneService sceneService;

    @Resource
    private IPlannerUserBindService plannerUserBindService;

    @Resource
    private ICallCenterUserInboundRecordService callCenterUserInboundRecordService;

    @Resource
    private ICallcenterUserAllocationRecordService userPreAllocationRecordService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    @Lazy
    private ICallcenterUserLockService userLockService;

    @Resource
    private ICallCenterUserOutboundRecordService userOutboundRecordService;

    @Resource
    private UserRegistryInfoMapper userRegistryInfoMapper;

    @Resource
    private IBladeDeptService bladeDeptService;

    @Resource
    private IBladeUserService bladeUserService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private UserVerifyInfoService userVerifyInfoService;

    @Resource
    @Lazy
    private RetrieveService retrieveService;

    @Resource
    private CallcenterUserHisService callcenterUserHisService;

    @Resource
    CxListStatusMapper cxListStatusMapper;

    @Resource
    private IPlannerUserBindHistoryService iPlannerUserBindHistoryService;

    @Resource
    private CallcenterUserMapper callcenterUserMapper;


    private static final int USER_MAX_SIZE = 500;

    @Value("${callcenter.default.scene.id:40}")
    private Long crmDefaultSceneId;


    //判单是否是虚拟ID的正则
    private static final String VIRTUAL_ID_REGEX = "^\\d+$";

    private final String REDIS_LOCK_KEY = "callcenter:user:lock:%s";


    @Resource
    private PlannerUserBindMapper plannerUserBindMapper;

    @Override
    public CallcenterUserDto getCenterUserPhoneInfo(String userId, String userPhone) {

        if (StringUtils.isBlank(userId) && StringUtils.isBlank(userPhone)) {
            return null;
        }
        CallcenterUser callCenterUser = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(userPhone), CallcenterUser::getUserPhone, userPhone)
                .and(StringUtils.isNotBlank(userId),
                        i -> i.eq(CallcenterUser::getUserId, userId).or().eq(CallcenterUser::getVirtualUserId, userId)
                ).orderByDesc(CallcenterUser::getId).last("limit 1").one();
        return callcenterUserConvert.modelToDto(callCenterUser);
    }

    /**
     * 保存呼叫中心用户
     */
    @Override
    public CallcenterUserDto saveCallCenterUser(CallcenterUserDto callcenterUserDto) {
        CallcenterUser prepared = callcenterUserConvert.dtoToModel(callcenterUserDto);
        prepared.insert();
        return callcenterUserConvert.modelToDto(prepared.selectById());
    }

    @Override
    public Boolean userInfoIsExist(String userId) {
        if (StringUtils.isBlank(userId)) {
            return true;
        }

        return this.lambdaQuery().eq(CallcenterUser::getUserId, userId).or().eq(CallcenterUser::getVirtualUser, userId).count() > 0;
    }

    @Override
    public boolean isVirtualUser(Integer source, String userId) {
        return Objects.equals(CallcenterUserSourceEnum.AVAYA_SYNC.getCode(), source)
                && StringUtils.isNotBlank(userId) && userId.matches(VIRTUAL_ID_REGEX)
                && Long.parseLong(userId) > CallServiceConstant.MIN_VIRTUAL_NUMBER_USER_ID;
    }

    @Override
    public UserAllocateInfo updateUserStatusWhenRetrieve(Long id, int developCycle) {
        LocalDateTime now = LocalDateTime.now();
        UserAllocateInfo userAllocateInfo = new UserAllocateInfo();
        userAllocateInfo.setBindTime(now).setEndTime(now.plusDays(developCycle + 1).toLocalDate().atStartOfDay());

        LambdaUpdateWrapper<CallcenterUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CallcenterUser::getId, id).ne(CallcenterUser::getUserStatus, ALLOCATION.getCode())
                .set(CallcenterUser::getUserStatus, ALLOCATION.getCode())
                .set(CallcenterUser::getDevelopStartTime, userAllocateInfo.getBindTime())
                .set(CallcenterUser::getDevelopEndTime, userAllocateInfo.getEndTime())
                .set(CallcenterUser::getDevelopCycle, developCycle)
                .set(CallcenterUser::getFollowUpStatus, UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode());
        boolean success = this.baseMapper.update(null, updateWrapper) > 0;
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(id));
        return userAllocateInfo.setAllocateSuccess(success);
    }

    @Override
    public List<CallcenterUserDto> getUnAllocateUnLockUserList(Long groupId, Long offsetId, int size, String tenantId) {
        List<CallcenterUser> callcenterUsers = this.baseMapper.getUnAllocateUnLockUserList(groupId, offsetId, size, tenantId);
        return callcenterUsers.stream().map(callcenterUserConvert::modelToDto).collect(Collectors.toList());
    }

    @Override
    public List<UserDevelopInfo> getUnFollowUserList(Long offsetId, String tenantId, int size) {
        return this.baseMapper.getUnFollowUserList(offsetId, tenantId, size);
    }

    @Override
    public List<UserDevelopInfo> getAllocatedNoCrmList(Long offsetId, int size, String tenantId, LocalDateTime developStartDate, LocalDateTime developEndDate) {
        return this.baseMapper.getAllocatedNoCrmList(offsetId, tenantId, size, developStartDate, developEndDate);
    }

    @Override
    public int setCustomerToFollowUp(String userId, String virtualId, String tenantId) {
        LambdaUpdateWrapper<CallcenterUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StringUtils.isNotBlank(userId), CallcenterUser::getUserId, userId)
                .eq(StringUtils.isNotBlank(virtualId), CallcenterUser::getVirtualUserId, virtualId)
                .eq(CallcenterUser::getTenantId, tenantId)
                .set(CallcenterUser::getFollowUpStatus, UserFollowUpStatusEnum.FOLLOW_UP.getCode());
        return this.baseMapper.update(null, updateWrapper);
    }

    @Override
    public int batchSetCustomerToFollowUp(List<UserDevelopInfo> unFollowUpUserInfo) {
        if (unFollowUpUserInfo == null || unFollowUpUserInfo.isEmpty()) {
            return 0;
        }

        // 设置固定值：跟进状态
        List<UserFollowUpInfo> params = unFollowUpUserInfo.stream()
                .map(p -> {
                    String vid = StringUtils.isBlank(p.getUserId()) ? p.getVirtualUserId() : null;
                    return new UserFollowUpInfo(p.getUserId(), vid,
                            UserFollowUpStatusEnum.FOLLOW_UP.getCode(), p.getTenantId());
                })
                .collect(Collectors.toList());
        return baseMapper.batchSetCustomerToFollowUp(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processUserInbound(List<InBoundUserInfo> inBoundUsers, String sceneId) {
        if (CollectionUtils.isEmpty(inBoundUsers)) {
            return false;
        }

        //获取场景信息
        CallcenterSceneConfig sceneConfig = sceneService.lambdaQuery().eq(CallcenterSceneConfig::getSceneId, sceneId).one();
        if (Objects.isNull(sceneConfig)) {
            return false;
        }

        RLock lock = redissonClient.getLock(String.format(REDIS_LOCK_KEY, sceneId));
        if (!lock.tryLock()) {
            return false;
        }

        try {
            List<List<InBoundUserInfo>> usersList = Lists.partition(inBoundUsers, USER_MAX_SIZE);
            for (List<InBoundUserInfo> users : usersList) {
                List<String> userIds = users.stream().map(InBoundUserInfo::getUserId).map(String::trim).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                Map<String, InBoundUserInfo> userInfoMap = users.stream().collect(toMap(InBoundUserInfo::getUserId, e -> e, (k1, k2) -> k2));

                //锁定用户入库失败
                List<String> lockUserIds = userLockService.findLockUser(userIds);
                List<String> inboundFailureUsers = userIds.stream().filter(lockUserIds::contains).collect(toList());
                processLockUserInboundFailure(inboundFailureUsers, sceneConfig);
                userIds.removeAll(inboundFailureUsers);
                if (CollectionUtils.isEmpty(userIds)) {
                    continue;
                }

                //查询用户中心用户信息 ,过滤已注销的用户信息
                LambdaQueryWrapper<UserRegistryInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(UserRegistryInfo::getUserId, userIds).eq(UserRegistryInfo::getUserStatus, "0");
                List<UserRegistryInfo> userRegistryInfos = userRegistryInfoMapper.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(userRegistryInfos)) {
                    List<String> cancelOutUsers = userRegistryInfos.stream().map(UserRegistryInfo::getUserId).map(String::valueOf).collect(toList());
                    userIds.removeAll(cancelOutUsers);
                }
                if (CollectionUtils.isEmpty(userIds)) {
                    continue;
                }

                //查询名单池数据
                List<CallcenterUser> dbUsers = this.lambdaQuery().in(CallcenterUser::getUserId, userIds).eq(CallcenterUser::getTenantId, sceneConfig.getTenantId()).list();
                Map<String, CallcenterUser> dbUserMap = Optional.ofNullable(dbUsers).map(Collection::stream).orElse(Stream.empty())
                        .collect(toMap(CallcenterUser::getUserId, e -> e, (k1, k2) -> k2));
                List<Long> dbSceneIds = Optional.ofNullable(dbUsers).map(Collection::stream).orElse(Stream.empty()).map(CallcenterUser::getSceneId)
                        .collect(Collectors.toList());

                //查询入库人员场景信息
                Map<Long, CallcenterSceneConfig> sceneConfigMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(dbSceneIds)) {
                    List<CallcenterSceneConfig> scenelist = sceneService.lambdaQuery().in(CallcenterSceneConfig::getSceneId, dbSceneIds).list();
                    sceneConfigMap = Optional.ofNullable(scenelist).map(Collection::stream).orElse(Stream.empty())
                            .collect(toMap(CallcenterSceneConfig::getSceneId, e -> e, (k1, k2) -> k2));
                }

                //获取用户绑定理财师信息
                List<PlannerUserBind> userBinds = plannerUserBindService.lambdaQuery()
                        .in(PlannerUserBind::getUserId, userIds).eq(PlannerUserBind::getTenantId, sceneConfig.getTenantId())
                        .list();
                Map<String, PlannerUserBind> dbUserBindMap = Optional.ofNullable(userBinds).map(Collection::stream).orElse(Stream.empty())
                        .collect(toMap(PlannerUserBind::getUserId, e -> e, (k1, k2) -> k2));

                //过滤出未在名单池的用户数据
                Set<String> saveUserIds = userIds.stream().filter(e -> !dbUserMap.containsKey(e)).collect(Collectors.toSet());

                //已经存在名单池的数据
                Set<String> existUserIds = userIds.stream().filter(dbUserMap::containsKey).collect(Collectors.toSet());

                //未绑定理财师用户数据
                List<String> unbindUserIds = existUserIds.stream().filter(e -> !dbUserBindMap.containsKey(e)).collect(Collectors.toList());

                //绑定理财师的用户
                List<String> bindUserIds = existUserIds.stream().filter(dbUserBindMap::containsKey).collect(Collectors.toList());

                //处理名单池中没有的用户数据
                this.processNewUsersInbound(saveUserIds, sceneConfig, dbUserBindMap, userInfoMap);

                //处理名单池中没有绑定理财师的用户数据
                this.processUnbindUsersInbound(unbindUserIds, sceneConfig, dbUserMap, sceneConfigMap, userInfoMap);

                //处理名单池中绑定理财师用户数据
                this.processBindUsersInbound(bindUserIds, sceneConfig, dbUserMap, userInfoMap, sceneConfigMap, dbUserBindMap);
            }
        } catch (Exception e) {
            log.error("CallcenterUserServiceImpl.processUserInbound error", e);
        } finally {
            lock.unlock();
        }
        return true;
    }

    @Override
    public int setCustomerToCrm(String userId, String tenantId) {
        if (StringUtils.isBlank(userId)) {
            return 0;
        }
        LambdaUpdateWrapper<CallcenterUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CallcenterUser::getUserId, userId).eq(CallcenterUser::getTenantId, tenantId)
                .set(CallcenterUser::getUserType, UserTypeEnum.CRM.getCode());
        return this.baseMapper.update(null, updateWrapper);
    }

    /**
     * 记录锁定用户入库失败
     */
    private void processLockUserInboundFailure(List<String> inboundFailureUsers, CallcenterSceneConfig sceneConfig) {
        if (!CollectionUtils.isEmpty(inboundFailureUsers)) {
            List<CallcenterUserInboundRecord> inboundRecords = inboundFailureUsers.stream().map(e ->
                    this.buildCallCenterUserInboundRecords(e, sceneConfig, UserInbindStatusEnum.FAILURE, BusinessError.USER_LOCKED.getMessage())
            ).collect(Collectors.toList());
            callCenterUserInboundRecordService.saveBatch(inboundRecords);
        }
    }

    /**
     * 执行人员分配
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processUserAllocation() {
        List<CallcenterUser> users = this.lambdaQuery().eq(CallcenterUser::getUserStatus, UserStatusEnum.UN_ALLOCATION.getCode()).list();
        if (CollectionUtils.isEmpty(users)) {
            return false;
        }
        //过滤锁定用户
        List<String> userIds = users.stream().map(CallcenterUser::getUserId).filter(StringUtils::isNotBlank).collect(toList());
        List<String> virtualUserIds = users.stream().map(CallcenterUser::getVirtualUserId).filter(StringUtils::isNotBlank).collect(toList());
        userIds.addAll(virtualUserIds);
        List<String> lockUserIds = userLockService.findLockUser(userIds);
        users = users.stream().filter(e -> !lockUserIds.contains(e.getUserId()) && !lockUserIds.contains(e.getVirtualUserId())).collect(toList());
        if (CollectionUtils.isEmpty(users)) {
            return false;
        }

        Set<Long> sceneIds = users.stream().map(CallcenterUser::getSceneId).collect(Collectors.toSet());
        Map<Long, CallcenterSceneConfig> sceneMap = Optional.ofNullable(sceneService.listByIds(sceneIds)).map(Collection::stream).orElse(Stream.empty())
                .collect(toMap(CallcenterSceneConfig::getSceneId, e -> e, (k1, k2) -> k2));

        users.stream().collect(groupingBy(CallcenterUser::getSceneId, () -> new TreeMap<>(
                        Comparator.<Long>comparingLong(id -> sceneMap.get(id).getLevel()).reversed().thenComparingLong(id -> id) // 同 level 按 sceneId 再排序，避免 compare=0
                ), Collectors.toList()))
                .forEach((sceneId, values) -> {
                    RLock lock = redissonClient.getLock(String.format(REDIS_LOCK_KEY, sceneId));
                    if (lock.tryLock()) {
                        try {
                            CallcenterSceneConfig sceneConfig = sceneMap.get(sceneId);
                            if (Objects.nonNull(sceneConfig)) {
                                userPreAllocationRecordService.processCallCenterAllocation(values, sceneConfig);
                                this.updateBatchById(values);
                                buildRetrieveQueue(values, sceneConfig);
                            }
                        } catch (Exception e) {
                            log.error("CallCenterUserServiceImpl.processUserAllocation error", e);
                        } finally {
                            lock.unlock();
                        }
                    }
                });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processUserOutbound(UserOutboundInfo userOutboundInfo) {
        if (CollectionUtils.isEmpty(userOutboundInfo.getUserIds())) {
            return false;
        }
        Set<String> userIds = userOutboundInfo.getUserIds().stream().filter(StringUtils::isNotBlank).collect(toSet());
        userIds.remove("");
        if (CollectionUtils.isEmpty(userIds)) {
            return false;
        }

        List<CallcenterUser> dbUsers = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(userOutboundInfo.getTenantId()), CallcenterUser::getTenantId, userOutboundInfo.getTenantId())
                .and(CollectionUtils.isNotEmpty(userIds),
                        i -> i.in(CallcenterUser::getUserId, userIds).or().in(CallcenterUser::getVirtualUserId, userIds)
                ).list();

        if (UserOutbindTypeEnum.AUTO_OUTBOUND.equals(userOutboundInfo.getType()) || UserOutbindTypeEnum.UNLOCK_OUTBOUND.equals(userOutboundInfo.getType())) {
            dbUsers = dbUsers.stream().filter(e -> Objects.nonNull(e.getDevelopEndTime()) && e.getDevelopEndTime().isBefore(LocalDateTime.now())).collect(toList());
        }

        if (CollectionUtils.isEmpty(dbUsers)) {
            return true;
        }
        List<PlannerUserBind> userBinds = plannerUserBindService.lambdaQuery().in(PlannerUserBind::getUserId, userIds).list();
        if (Objects.nonNull(userOutboundInfo.getFlag()) && userOutboundInfo.getFlag()) {
            Set<Integer> bindUserIds = Optional.ofNullable(userBinds).map(Collection::stream).orElse(Stream.empty()).map(PlannerUserBind::getId).collect(toSet());
            if (CollectionUtils.isNotEmpty(bindUserIds)) {
                plannerUserBindService.removeByIds(bindUserIds);
            }
        }

        List<Long> ids = dbUsers.stream().map(CallcenterUser::getId).collect(toList());
        this.removeByIds(ids);
        //记录用户历史表信息
        saveUserHisBoundTime(dbUsers, userBinds);
        List<CallCenterUserOutboundRecord> outboundRecords = dbUsers.stream().map(e ->
                this.buildCallCenterUserOutboundRecord(e, userOutboundInfo.getType(), userOutboundInfo.getOperatorId())
        ).collect(toList());
        userOutboundRecordService.saveBatch(outboundRecords);
        return true;
    }

    /**
     * 用户历史表新增出库记录
     */
    private void saveUserHisBoundTime(List<CallcenterUser> dbUsers, List<PlannerUserBind> bindUsers) {
        if (CollectionUtils.isEmpty(dbUsers)) {
            return;
        }
        Map<String, PlannerUserBind> bindMap = Optional.ofNullable(bindUsers).map(Collection::stream).orElse(Stream.empty())
                .collect(toMap(PlannerUserBind::getUserId, e -> e, (k1, k2) -> k2));

        List<CallcenterUserHis> list = new ArrayList<>();
        dbUsers.stream().collect(partitioningBy(CallcenterUser::getVirtualUser)).forEach((key, values) -> {
            if (!values.isEmpty()) {
                for (CallcenterUser value : values) {
                    CallcenterUserHis userHis = new CallcenterUserHis();
                    BeanUtils.copyProperties(value, userHis);
                    userHis.setCallcenterUserId(value.getId());
                    userHis.setOutboundTime(LocalDateTime.now());
                    userHis.setInboundTime(value.getCreateTime());
                    String userId = value.getVirtualUser() ? value.getVirtualUserId() : value.getUserId();
                    PlannerUserBind bind = bindMap.getOrDefault(userId, null);
                    if (null != bind) {
                        userHis.setPlannerId(bind.getPlannerId());
                        userHis.setBindTime(bind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                    }
                    list.add(userHis);
                }
            }
        });
        callcenterUserHisService.saveBatch(list);
    }

    /**
     * 获取用户数据
     */
    @Override
    public CallcenterUserDto getCenterUserByUserId(String userId, String tenantId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        CallcenterUser callCenterUser = this.lambdaQuery().eq(CallcenterUser::getUserId, userId).or().eq(CallcenterUser::getVirtualUserId, userId)
                .eq(StringUtils.isNotBlank(tenantId), CallcenterUser::getTenantId, tenantId)
                .orderByDesc(CallcenterUser::getId).last("limit 1").one();
        return callcenterUserConvert.modelToDto(callCenterUser);
    }

    /**
     * 分配理财师更新用户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allocationPlannerUpdateUser(String userId, Long groupId, Long sceneId, String tenantId) {
        CallcenterUserDto dbCallCenterUser = this.getCenterUserByUserId(userId, tenantId);
        if (Objects.isNull(dbCallCenterUser)) {
            log.info("allocationPlannerUpdateUser.dbCallCenterUser is null");
            return;
        }
        CallcenterSceneConfig sceneConfig = sceneService.getById(sceneId);
        if (Objects.isNull(sceneConfig)) {
            log.info("allocationPlannerUpdateUser.sceneConfig is null");
            return;
        }
        CallcenterUser callCenterUser = new CallcenterUser();
        callCenterUser.setId(dbCallCenterUser.getId());
        callCenterUser.setGroupId(groupId);
        callCenterUser.setUserStatus(UserStatusEnum.ALLOCATION.getCode());
        callCenterUser.setDevelopStartTime(LocalDateTime.now());
        callCenterUser.setDevelopCycle(Integer.parseInt(sceneConfig.getDevelopCycle()));
        callCenterUser.setDevelopEndTime(LocalDateTime.now().plusDays(Integer.parseInt(sceneConfig.getDevelopCycle())));
        callCenterUser.setExtensionDevelopCycle(0);
        callCenterUser.setFollowUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode());
        callCenterUser.setSceneId(sceneConfig.getSceneId());
        callCenterUser.setSceneNo(sceneConfig.getSceneNo());
        this.updateById(callCenterUser);
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(callCenterUser.getId()));
    }


    /**
     * 注册更新虚拟用户userId
     */
    @Override
    @Transactional
    public Boolean registerUpdateVirtualUserId(String phone, String userId, String tenantId) {
        CallcenterUser dbUser = this.lambdaQuery()
                .eq(CallcenterUser::getUserPhone, phone).eq(CallcenterUser::getVirtualUser, true)
                .eq(StringUtils.isNotBlank(tenantId), CallcenterUser::getTenantId, tenantId)
                .last("limit 1").one();
        if (Objects.isNull(dbUser)) {
            return true;
        }
        CallcenterUser update = new CallcenterUser();
        update.setId(dbUser.getId());
        update.setUserId(userId);
        update.setVirtualUser(false);
        this.updateById(update);
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(update.getId()));
        return true;
    }

    /**
     * 变更手机号
     */
    @Override
    @Transactional
    public Boolean changePhone(String userId, String phone, String tenantId) {
        if (StringUtils.isBlank(userId)) {
            return false;
        }
        CallcenterUser dbUser = this.lambdaQuery()
                .eq(CallcenterUser::getUserId, userId)
                .eq(StringUtils.isNotBlank(tenantId), CallcenterUser::getTenantId, tenantId)
                .last("limit 1").one();
        if (Objects.isNull(dbUser)) {
            return true;
        }

        CallcenterUser phoneExist = this.lambdaQuery()
                .eq(CallcenterUser::getUserPhone, phone)
                .eq(StringUtils.isNotBlank(tenantId), CallcenterUser::getTenantId, tenantId)
                .last("limit 1").one();
        if (Objects.nonNull(phoneExist)) {
            String lockId = phoneExist.getVirtualUser() ? phoneExist.getVirtualUserId() : phoneExist.getUserId();
            Boolean lock = userLockService.isLock(lockId);
            if (lock) {
                userLockService.unlockWithoutType(lockId);
            }
            UserOutboundInfo userOutboundInfo = UserOutboundInfo.builder()
                    .userIds(Sets.newHashSet(phoneExist.getVirtualUser() ? phoneExist.getVirtualUserId() : phoneExist.getUserId()))
                    .type(UserOutbindTypeEnum.PHONE_CHANGES_OUTBOUND)
                    .operatorId("")
                    .tenantId(phoneExist.getTenantId())
                    .flag(true)
                    .build();
            this.processUserOutbound(userOutboundInfo);
        }
        CallcenterUser update = new CallcenterUser();
        update.setId(dbUser.getId());
        update.setUserPhone(phone);
        this.updateById(update);
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(update.getId()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeUserType(String userId) {
        if (StringUtils.isBlank(userId)) {
            return false;
        }
        CallcenterUser dbUser = this.lambdaQuery().eq(CallcenterUser::getUserId, userId).last("limit 1").one();
        if (Objects.isNull(dbUser)) {
            log.info("CalCenterUserServiceImpl.changeUserType.dbUser is null");
            return false;
        }
        CallcenterSceneConfig sceneConfig = sceneService.getById(crmDefaultSceneId);
        if (Objects.isNull(sceneConfig)) {
            log.info("CalCenterUserServiceImpl.changeUserType.sceneConfig is null");
            return false;
        }
        //如果用户被名单锁锁定，不升库
        if (userLockService.isListLock(userId)) {
            return false;
        }
        Set<String> userIds = Sets.newHashSet(dbUser.getUserId());
        UserOutboundInfo userOutboundInfo = UserOutboundInfo.builder().userIds(userIds)
                .type(UserOutbindTypeEnum.CHANGE_USER_TYPE)
                .operatorId("0")
                .tenantId(dbUser.getTenantId())
                .flag(false)
                .build();
        processUserOutbound(userOutboundInfo);

        PlannerUserBind userBind = plannerUserBindService.lambdaQuery()
                .eq(PlannerUserBind::getUserId, userId)
                .last("limit 1").one();

        if (Objects.nonNull(userBind)) {
            PlannerUserBind update = new PlannerUserBind();
            update.setId(userBind.getId());
            update.setBindTime(new Date());
            update.setEndTime(Date.from(LocalDateTime.now().plusDays(Integer.parseInt(sceneConfig.getDevelopCycle()))
                    .atZone(ZoneId.systemDefault()).toInstant()));
            update.setResource("2");
            update.setSceneId(crmDefaultSceneId);
            update.setUpdateTime(new Date());
            update.setRemark(sceneConfig.getSceneNo());
            plannerUserBindService.updateById(update);
        }

        CallcenterUser callCenterUser = new CallcenterUser();
        callCenterUser.setId(null);
        callCenterUser.setUserId(dbUser.getUserId());
        callCenterUser.setVirtualUserId(dbUser.getVirtualUserId());
        callCenterUser.setUserName(dbUser.getUserName());
        callCenterUser.setUserPhone(dbUser.getUserPhone());
        callCenterUser.setVirtualUser(dbUser.getVirtualUser());
        callCenterUser.setSource(dbUser.getSource());
        callCenterUser.setTenantId(dbUser.getTenantId());
        callCenterUser.setGroupId(dbUser.getGroupId());
        callCenterUser.setCreateUser("0");
        callCenterUser.setUserType(UserTypeEnum.CRM.getCode());
        callCenterUser.setSceneId(crmDefaultSceneId);
        callCenterUser.setSceneNo(sceneConfig.getSceneNo());
        callCenterUser.setDevelopStartTime(LocalDateTime.now());
        callCenterUser.setDevelopCycle(Integer.parseInt(sceneConfig.getDevelopCycle()));
        callCenterUser.setDevelopEndTime(LocalDateTime.now().plusDays(Integer.parseInt(sceneConfig.getDevelopCycle())));
        callCenterUser.setExtensionDevelopCycle(0);
        callCenterUser.setUserStatus(UserStatusEnum.ALLOCATION.getCode());
        callCenterUser.setFollowUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode());
        this.save(callCenterUser);
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(callCenterUser.getId()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delayDevelopCycle(DelayDevelopCycleReq req) {
        if (StringUtils.isBlank(req.getUserId())) {
            throw new BusinessException(DATA_NOT_EXIST);
        }
        CallcenterUser callcenterUser = this.lambdaQuery()
                .and(StringUtils.isNotBlank(req.getUserId()),
                        i -> i.eq(CallcenterUser::getUserId, req.getUserId()).or().eq(CallcenterUser::getVirtualUserId, req.getUserId())
                ).orderByDesc(CallcenterUser::getId).last("limit 1").one();
        if (Objects.isNull(callcenterUser)) {
            throw new BusinessException(USER_NOT_EXIST_INBOUND);
        }
        if (userLockService.isManageUserLock(req.getUserId())) {
            throw new BusinessException(USER_MANAGER_USER_LOCKED);
        }
        LocalDateTime endTime = DateUtil.parseLocalDateTime(req.getDelayTime());
        if (Objects.nonNull(callcenterUser.getDevelopEndTime()) && !endTime.isAfter(callcenterUser.getDevelopEndTime())) {
            throw new BusinessException(DELAY_END_DATE_MUST_GREATER_THAN_ORIGINAL_DEVELOP_END_DATE);
        }
        callcenterUser.setDevelopEndTime(endTime);
        callcenterUser.setUpdateUser(OwnAuthUtil.getRealName());
        callcenterUser.setUpdateTime(LocalDateTime.now());
        this.updateById(callcenterUser);
        this.updatePlannerBindEndTime(callcenterUser);
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(callcenterUser.getId()));
        return true;

    }

    /**
     * 延长开发期,更新bind表endTime
     */
    private void updatePlannerBindEndTime(CallcenterUser callcenterUser) {
        String userId = callcenterUser.getVirtualUser() ? callcenterUser.getVirtualUserId() : callcenterUser.getUserId();
        if (StringUtils.isBlank(userId)) {
            return;
        }
        PlannerUserBind dbPlannerUserBind = plannerUserBindService.lambdaQuery().eq(PlannerUserBind::getUserId, userId).last("limit 1").one();
        if (Objects.nonNull(dbPlannerUserBind)) {
            dbPlannerUserBind.setEndTime(Date.from(callcenterUser.getDevelopEndTime().atZone(ZoneId.systemDefault()).toInstant()));
            plannerUserBindService.updateById(dbPlannerUserBind);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BatchDelayDevelopCycleRes> batchDelayDevelopCycle(MultipartFile file) {
        DataListener<DelayDevelopCycleTemplateRes> dataListener = new DataListener<>();
        try {
            EasyExcel.read(file.getInputStream(), DelayDevelopCycleTemplateRes.class, dataListener).doReadAll();
        } catch (Exception e) {
            throw new BusinessException(ERROR_FILE_PARSING);
        }
        List<DelayDevelopCycleTemplateRes> importList = dataListener.getDataList();
        if (CollUtil.isNotEmpty(importList) && importList.size() > 10000) {
            throw new BusinessException(RECORD_TOO_MANY);
        }
        List<BatchDelayDevelopCycleRes> list = new ArrayList<>(importList.size());
        for (DelayDevelopCycleTemplateRes template : importList) {
            BatchDelayDevelopCycleRes delayDevelopCycleRes = BeanUtil.copyProperties(template,
                    BatchDelayDevelopCycleRes.class);
            try {
                if (StringUtils.isEmpty(template.getUserId()) || StringUtils.isEmpty(template.getDelayTime())) {
                    throw new BusinessException(PARAM_NULL_ERROR);
                }
                CallcenterUserDto callcenterUserDto = getCenterUserPhoneInfo(template.getUserId(), null);
                if (Objects.isNull(callcenterUserDto)) {
                    throw new BusinessException(USER_NOT_EXIST_IN_DB);
                }
                if (!callcenterUserDto.getUserStatus().equals(UserStatusEnum.ALLOCATION.getCode())) {
                    throw new BusinessException(USER_NOT_INT_DEVELOP_CYCLE);
                }
                BladeDept bladeDept = bladeDeptService.getById(callcenterUserDto.getGroupId());
                if (StringUtils.isEmpty(template.getGroupName()) || !template.getGroupName().equals(bladeDept.getDeptName())) {
                    throw new BusinessException(USER_GROUP_NAME_NOT_EQUAL);
                }
                DelayDevelopCycleReq delayDevelopCycleReq = BeanUtil.copyProperties(template,
                        DelayDevelopCycleReq.class);
                delayDevelopCycleReq.setDelayTime(delayDevelopCycleReq.getDelayTime() + " 23:59:59");

                delayDevelopCycle(delayDevelopCycleReq);

            } catch (Exception e) {
                delayDevelopCycleRes.setFailReason(e.getMessage());
                list.add(delayDevelopCycleRes);
            }
        }
        return list;
    }

    /**
     * 处理绑定理财师的用户数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void processBindUsersInbound(List<String> users,
                                        CallcenterSceneConfig sceneConfig,
                                        Map<String, CallcenterUser> dbUserMap,
                                        Map<String, InBoundUserInfo> userInfoMap,
                                        Map<Long, CallcenterSceneConfig> sceneConfigMap,
                                        Map<String, PlannerUserBind> dbUserBindMap) {
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        //处理入库失败的用户
        this.handlerInboundFailUsers(users, sceneConfig, dbUserMap);
        //入库成功用户
        List<String> userIds = users.stream().filter(e -> dbUserMap.get(e).getUserType().equals(UserTypeEnum.AVAYA.getCode()))
                .filter(e -> dbUserMap.get(e).getDevelopStartTime().isBefore(LocalDateTime.now().plusDays(-3)))
                .filter(e -> dbUserMap.get(e).getFollowUpStatus().equals(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode()))
                .filter(e -> !dbUserMap.get(e).getSceneId().equals(sceneConfig.getSceneId()))
                .filter(e -> sceneConfigMap.containsKey(dbUserMap.get(e).getSceneId()) && sceneConfigMap.get(dbUserMap.get(e).getSceneId()).getLevel() < sceneConfig.getLevel())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        //需要出库,然后重新入库
        List<CallcenterUser> dbCallCenterUsers = userIds.stream().map(dbUserMap::get).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dbCallCenterUsers)) {
            List<PlannerUserBind> userBinds = userIds.stream().map(dbUserBindMap::get).collect(toList());
            if (CollectionUtils.isNotEmpty(userBinds)) {
                Set<Integer> bindIds = userBinds.stream().map(PlannerUserBind::getId).collect(toSet());
                plannerUserBindService.removeByIds(bindIds);
            }
            List<Long> ids = dbCallCenterUsers.stream().map(CallcenterUser::getId).collect(Collectors.toList());
            this.removeByIds(ids);
            //记录用户历史表信息
            saveUserHisBoundTime(dbCallCenterUsers, userBinds);
            List<CallCenterUserOutboundRecord> outboundRecords = dbCallCenterUsers.stream().map(e ->
                    this.buildCallCenterUserOutboundRecord(e, UserOutbindTypeEnum.SCENE_CHANGES_OUTBOUND, null)
            ).collect(toList());
            userOutboundRecordService.saveBatch(outboundRecords);
        }
        // 执行预分配入库
        List<InBoundUserInfo> inBoundUserInfos = userIds.stream().map(userInfoMap::get).collect(toList());
        processPreAllocationInbound(inBoundUserInfos, sceneConfig);
    }

    private void handlerInboundFailUsers(List<String> users, CallcenterSceneConfig sceneConfig, Map<String, CallcenterUser> dbUserMap) {
        List<String> crmUsers = users.stream().filter(e -> dbUserMap.get(e).getUserType().equals(UserTypeEnum.CRM.getCode())).collect(Collectors.toList());
        List<String> statusErrorUsers = users.stream().filter(e -> dbUserMap.get(e).getUserType().equals(UserTypeEnum.AVAYA.getCode()))
                .filter(e -> dbUserMap.get(e).getDevelopStartTime().isAfter(LocalDateTime.now().plusDays(-3))
                        || dbUserMap.get(e).getFollowUpStatus().equals(UserFollowUpStatusEnum.FOLLOW_UP.getCode()))
                .collect(Collectors.toList());
        List<String> inboundFailureUserIds = new ArrayList<>();
        inboundFailureUserIds.addAll(crmUsers);
        inboundFailureUserIds.addAll(statusErrorUsers);

        //记录入库失败
        if (!CollectionUtils.isEmpty(inboundFailureUserIds)) {
            List<CallcenterUser> callCenterUsers = inboundFailureUserIds.stream().map(dbUserMap::get).collect(Collectors.toList());
            List<CallcenterUserInboundRecord> inboundRecords = callCenterUsers.stream().map(e ->
                    this.buildCallCenterUserInboundRecords(e.getUserId(), sceneConfig, UserInbindStatusEnum.FAILURE,
                            crmUsers.contains(e.getUserId()) ? BusinessError.CRM_USER.getMessage() : BusinessError.DEVELOP_PROTECTION_PERIOD.getMessage())
            ).collect(Collectors.toList());
            callCenterUserInboundRecordService.saveBatch(inboundRecords);
        }
    }

    /**
     * 处理未绑定的用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void processUnbindUsersInbound(List<String> unbindUsers,
                                          CallcenterSceneConfig sceneConfig,
                                          Map<String, CallcenterUser> dbUserMap,
                                          Map<Long, CallcenterSceneConfig> sceneConfigMap,
                                          Map<String, InBoundUserInfo> userInfoMap) {
        if (CollectionUtils.isEmpty(unbindUsers)) {
            return;
        }
        List<String> userIds = unbindUsers.stream().filter(e -> !dbUserMap.get(e).getSceneId().equals(sceneConfig.getSceneId()))
                .filter(e -> sceneConfigMap.containsKey(dbUserMap.get(e).getSceneId()) && sceneConfigMap.get(dbUserMap.get(e).getSceneId()).getLevel() < sceneConfig.getLevel())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        List<CallcenterUser> dbCallCenterUsers = userIds.stream().map(dbUserMap::get).collect(Collectors.toList());
        //需要旧场景出库,然后新场景入库
        if (!CollectionUtils.isEmpty(dbCallCenterUsers)) {
            List<Long> ids = dbCallCenterUsers.stream().map(CallcenterUser::getId).collect(Collectors.toList());
            this.removeByIds(ids);
            //记录用户历史表信息
            saveUserHisBoundTime(dbCallCenterUsers, null);
            List<CallCenterUserOutboundRecord> outboundRecords = dbCallCenterUsers.stream().map(e ->
                    this.buildCallCenterUserOutboundRecord(e, UserOutbindTypeEnum.SCENE_CHANGES_OUTBOUND, null)
            ).collect(toList());
            userOutboundRecordService.saveBatch(outboundRecords);
        }
        List<InBoundUserInfo> inBoundUserInfos = userIds.stream().map(userInfoMap::get).collect(toList());
        // 执行预分配入库
        processPreAllocationInbound(inBoundUserInfos, sceneConfig);
    }

    /**
     * 查询名单池中没有的用户数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void processNewUsersInbound(Set<String> saveUserIds, CallcenterSceneConfig sceneConfig,
                                       Map<String, PlannerUserBind> dbUserBindMap, Map<String, InBoundUserInfo> userInfoMap) {
        if (CollectionUtils.isEmpty(saveUserIds)) {
            return;
        }

        //区分是否是历史数据
        saveUserIds.stream().collect(partitioningBy(dbUserBindMap::containsKey)).forEach((key, values) -> {
            if (CollectionUtils.isNotEmpty(values)) {
                //历史数据直接入库
                if (!key) {
                    List<InBoundUserInfo> inBoundUserInfos = values.stream().map(userInfoMap::get).collect(toList());
                    processPreAllocationInbound(inBoundUserInfos, sceneConfig);
                }
            }
        });
    }

    /**
     * 执行预分配入库
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processPreAllocationInbound(List<InBoundUserInfo> values, CallcenterSceneConfig sceneConfig) {
        List<CallcenterUser> callCenterUsers = values.stream().map(e -> buildCallCenterUser(e, sceneConfig))
                .collect(Collectors.toList());
        //设置用户名
        List<String> userIds = callCenterUsers.stream().map(CallcenterUser::getUserId).filter(Objects::nonNull).collect(toList());
        if (!CollectionUtils.isEmpty(userIds)) {
            Map<Long, String> userVerifyMap = Optional.ofNullable(userVerifyInfoService.lambdaQuery().in(UserVerifyInfo::getUserId, userIds).list())
                    .map(Collection::stream).orElse(Stream.empty()).collect(toMap(UserVerifyInfo::getUserId, UserVerifyInfo::getUserName));
            callCenterUsers.forEach(e -> e.setUserName(userVerifyMap.getOrDefault(Long.valueOf(e.getUserId()), "")));
        }
        // 执行预分配
        userPreAllocationRecordService.processCallCenterAllocation(callCenterUsers, sceneConfig);
        saveBatch(callCenterUsers);

        //构造入库记录
        List<CallcenterUserInboundRecord> inboundRecords = callCenterUsers.stream().map(e ->
                this.buildCallCenterUserInboundRecords(e.getVirtualUser() ? e.getVirtualUserId() : e.getUserId(), sceneConfig, UserInbindStatusEnum.SUCCESS, "")
        ).collect(Collectors.toList());
        callCenterUserInboundRecordService.saveBatch(inboundRecords);

        Set<Long> ids = callCenterUsers.stream().map(CallcenterUser::getId).collect(toSet());
        this.pushCallCenterUserChangeEvent(ids);
        this.buildRetrieveQueue(callCenterUsers, sceneConfig);
    }

    private void buildRetrieveQueue(List<CallcenterUser> callCenterUsers, CallcenterSceneConfig sceneConfig) {
        if (CollectionUtils.isEmpty(callCenterUsers) || Objects.isNull(sceneConfig)) {
            return;
        }
        callCenterUsers.stream().collect(groupingBy(CallcenterUser::getGroupId)).forEach((key, value) -> {
            if (!value.isEmpty()) {
                List<CallcenterUserDto> data = value.stream().map(e -> callcenterUserConvert.modelToDto(e)).collect(toList());
                retrieveService.composeRetrieveQueue(data, sceneConfig.getLevel(), key);
            }
        });

    }

    /**
     * 分配小组入库
     */
    @Override
    public CallcenterUserDto processAllocationGroupInbound(InBoundUserInfo userInfo, CallcenterSceneConfig sceneConfig) {
        CallcenterUser callcenterUser = this.buildCallCenterUser(userInfo, sceneConfig);
        callcenterUser.setGroupId(Long.valueOf(userInfo.getGroupId()));
        if (StringUtils.isNotBlank(callcenterUser.getUserId())) {
            UserVerifyInfo userVerifyInfo = userVerifyInfoService.lambdaQuery().eq(UserVerifyInfo::getUserId, callcenterUser.getUserId()).one();
            callcenterUser.setUserName(Objects.isNull(userVerifyInfo) ? "" : userVerifyInfo.getUserName());
        }
        callcenterUser.insert();
        CallcenterUser result = callcenterUser.selectById();
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(result.getId()));
        //构造入库记录
        CallcenterUserInboundRecord records = this.buildCallCenterUserInboundRecords(userInfo.getUserId(), sceneConfig, UserInbindStatusEnum.SUCCESS, "");
        callCenterUserInboundRecordService.save(records);
        return callcenterUserConvert.modelToDto(result);
    }

    /**
     * 绑定理财师入库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CallcenterUserDto processBindPlannerInbound(InBoundUserInfo userInfo, CallcenterSceneConfig sceneConfig) {
        if (Objects.isNull(sceneConfig)) {
            return null;
        }
        CallcenterUser callCenterUser = this.buildCallCenterUser(userInfo, sceneConfig);
        if (StringUtils.isNotBlank(callCenterUser.getUserId())) {
            UserVerifyInfo userVerifyInfo = userVerifyInfoService.lambdaQuery().eq(UserVerifyInfo::getUserId, callCenterUser.getUserId()).one();
            callCenterUser.setUserName(Objects.isNull(userVerifyInfo) ? "" : userVerifyInfo.getUserName());
        }
        callCenterUser.setGroupId(Long.valueOf(userInfo.getGroupId()));
        callCenterUser.setUserStatus(UserStatusEnum.ALLOCATION.getCode());
        callCenterUser.setDevelopStartTime(LocalDateTime.now());
        callCenterUser.setDevelopCycle(Integer.parseInt(sceneConfig.getDevelopCycle()));
        callCenterUser.setDevelopEndTime(LocalDateTime.now().plusDays(Integer.parseInt(sceneConfig.getDevelopCycle())));
        callCenterUser.setExtensionDevelopCycle(0);
        callCenterUser.setFollowUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode());
        callCenterUser.insert();
        CallcenterUser result = callCenterUser.selectById();
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(result.getId()));
        //构造入库记录
        CallcenterUserInboundRecord records = this.buildCallCenterUserInboundRecords(callCenterUser.getVirtualUser() ? callCenterUser.getVirtualUserId() : callCenterUser.getUserId(), sceneConfig, UserInbindStatusEnum.SUCCESS, "");
        callCenterUserInboundRecordService.save(records);
        return callcenterUserConvert.modelToDto(result);
    }

    @Override
    public boolean isSceneAllocating(Long sceneId) {
        RLock lock = redissonClient.getLock(String.format(REDIS_LOCK_KEY, sceneId));
        return lock.isLocked();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoOutbound() {
        List<CallcenterUser> needOutBoundUsers = this.lambdaQuery().lt(CallcenterUser::getDevelopEndTime, LocalDateTime.now()).list();
        if (CollectionUtils.isEmpty(needOutBoundUsers)) {
            return;
        }
        List<String> userIds = needOutBoundUsers.stream().map(CallcenterUser::getUserId).filter(StringUtils::isNotBlank).collect(toList());
        List<String> virtualUserId = needOutBoundUsers.stream().map(CallcenterUser::getVirtualUserId).filter(StringUtils::isNotBlank).collect(toList());
        userIds.addAll(virtualUserId);
        List<String> lockUser = userLockService.findLockUser(userIds);
        userIds = userIds.stream().filter(e -> !lockUser.contains(e)).collect(toList());
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        needOutBoundUsers.stream().collect(groupingBy(CallcenterUser::getTenantId)).forEach((key, values) -> {
            if (!values.isEmpty()) {
                List<String> outBoundUserIds = values.stream().map(CallcenterUser::getUserId).filter(StringUtils::isNotBlank).collect(toList());
                List<String> outVirtualUserId = values.stream().map(CallcenterUser::getVirtualUserId).filter(StringUtils::isNotBlank).collect(toList());
                outBoundUserIds.addAll(outVirtualUserId);
                outBoundUserIds = outBoundUserIds.stream().filter(e -> !lockUser.contains(e)).collect(toList());
                UserOutboundInfo userOutboundInfo = UserOutboundInfo.builder()
                        .userIds(Sets.newHashSet(outBoundUserIds))
                        .type(UserOutbindTypeEnum.AUTO_OUTBOUND)
                        .operatorId("0")
                        .flag(true)
                        .tenantId(key).build();
                this.processUserOutbound(userOutboundInfo);
            }
        });

    }

    @Override
    public Boolean syncHistoryData(Set<Long> ids) {
        CallcenterSceneConfig crmScene = sceneService.getById(crmDefaultSceneId);
        if (CollectionUtils.isNotEmpty(ids)) {
            List<PlannerUserBind> plannerUserBinds = plannerUserBindService.listByIds(ids);
            this.processHistoryDataSync(plannerUserBinds, crmScene);
            return true;
        }
        Integer minId = 0;
        while (true) {
            List<PlannerUserBind> list = plannerUserBindService.lambdaQuery().gt(PlannerUserBind::getId, minId)
                    .orderByAsc(PlannerUserBind::getId).last("limit 200").list();
            this.processHistoryDataSync(list, crmScene);
            if (list.size() < 200) {
                break;
            }
            minId = list.get(199).getId();
        }
        return true;
    }

    @Override
    public void processHistoryDataSync(List<PlannerUserBind> plannerUserBinds, CallcenterSceneConfig crmScene) {
        if (CollectionUtils.isEmpty(plannerUserBinds)) {
            return;
        }

        if (Objects.isNull(crmScene)) {
            crmScene = sceneService.getById(crmDefaultSceneId);
        }
        final CallcenterSceneConfig fCrmScene = crmScene;
        plannerUserBinds.stream().collect(partitioningBy(e -> this.isVirtualUser(Integer.parseInt(e.getResource()), e.getUserId())))
                .forEach((key, values) -> {
                    if (!values.isEmpty()) {
                        if (key) {
                            //处理虚拟用户
                            this.handleVirtualUserHistoryData(values);
                        } else {
                            //处理宜人注册用户
                            this.handleRegisterUserHistoryData(values, fCrmScene);
                        }
                    }
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handlerUserHistory(Set<Long> ids) {
        List<CallCenterUserOutboundRecord> outboundRecords = userOutboundRecordService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(ids), CallCenterUserOutboundRecord::getId, ids)
                .gt(CallCenterUserOutboundRecord::getCreateTime, LocalDateTime.of(2025, 5, 10, 15, 0, 0))
                .list();
        if (CollectionUtils.isEmpty(outboundRecords)) {
            return false;
        }
        List<CallcenterUserHis> result = new ArrayList<>();
        CallcenterSceneConfig crmScene = sceneService.getById(crmDefaultSceneId);
        for (CallCenterUserOutboundRecord outboundRecord : outboundRecords) {
            boolean flag = Long.parseLong(outboundRecord.getCustomerUserId()) > CallServiceConstant.MIN_VIRTUAL_NUMBER_USER_ID;
            CallcenterUserHis callcenterUserHis = callcenterUserHisService.lambdaQuery()
                    .eq(flag, CallcenterUserHis::getVirtualUserId, outboundRecord.getCustomerUserId())
                    .eq(!flag, CallcenterUserHis::getUserId, outboundRecord.getCustomerUserId())
                    .isNotNull(CallcenterUserHis::getPlannerId)
                    .lt(CallcenterUserHis::getUpdateTime, outboundRecord.getCreateTime())
                    .last("limit 1")
                    .orderByDesc(CallcenterUserHis::getUpdateTime)
                    .one();
            if (Objects.nonNull(callcenterUserHis)) {
                CallcenterUserHis userHis = new CallcenterUserHis();
                BeanUtils.copyProperties(callcenterUserHis, userHis);
                userHis.setUpdateTime(LocalDateTime.now());
                userHis.setOutboundTime(outboundRecord.getCreateTime());
                result.add(userHis);
                continue;
            }
            if (StringUtils.isBlank(outboundRecord.getCustomerUserId())) {
                continue;
            }
            PlannerUserBind plannerUserBind = plannerUserBindMapper.findBindBakDataInfo(outboundRecord.getCustomerUserId());
            if (Objects.isNull(plannerUserBind)) {
                continue;
            }
            boolean virtualUser = this.isVirtualUser(Integer.parseInt(plannerUserBind.getResource()), plannerUserBind.getUserId());
            CallcenterSceneConfig scene = sceneService.lambdaQuery().eq(CallcenterSceneConfig::getSceneNo, plannerUserBind.getResource()).one();
            PlannerInfoDto plannerInfoDto = bladeUserService.getPlannerInfoByPlannerId(plannerUserBind.getPlannerId());
            if (virtualUser) {
                CallcenterUserHis userHis = CallcenterUserHis.builder()
                        .virtualUserId(plannerUserBind.getUserId())
                        .userPhone(this.getAvayaVirtualUserPhone(plannerUserBind))
                        .virtualUser(true)
                        .source(CallcenterUserSourceEnum.AVAYA_SYNC.getCode())
                        .tenantId(plannerUserBind.getTenantId())
                        .createUser("0")
                        .userType(UserTypeEnum.getEnumsByCode(Integer.parseInt(plannerUserBind.getResource())).getCode())
                        .userStatus(ALLOCATION.getCode())
                        .sceneId(Objects.nonNull(scene) ? scene.getSceneId() : 0)
                        .groupId(Objects.nonNull(plannerInfoDto) && StringUtils.isNotBlank(plannerInfoDto.getGroupId()) ? Long.parseLong(plannerInfoDto.getGroupId()) : 0)
                        .developStartTime(plannerUserBind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                        .developEndTime(plannerUserBind.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                        .followUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode())
                        .sceneNo(plannerUserBind.getRemark())
                        .createTime(getInboundTime(plannerUserBind, false))
                        .inboundTime(getInboundTime(plannerUserBind, false))
                        .bindTime(plannerUserBind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                        .plannerId(plannerUserBind.getPlannerId())
                        .outboundTime(outboundRecord.getCreateTime())
                        .build();
                result.add(userHis);
            } else {
                boolean isCrmUser = CallcenterUserSourceEnum.CRM_SYNC.getCode().equals(Integer.parseInt(plannerUserBind.getResource()));
                LambdaQueryWrapper<UserRegistryInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(UserRegistryInfo::getUserId, plannerUserBind.getUserId());
                UserRegistryInfo userRegistryInfos = userRegistryInfoMapper.selectOne(queryWrapper);
                CallcenterUserHis userHis = CallcenterUserHis.builder()
                        .userId(plannerUserBind.getUserId())
                        .userName(plannerUserBind.getUserName())
                        .userPhone(Objects.nonNull(userRegistryInfos) ? userRegistryInfos.getMobileNo() : "")
                        .virtualUser(false)
                        .source(isCrmUser ? CallcenterUserSourceEnum.CRM_SYNC.getCode() : CallcenterUserSourceEnum.AVAYA_SYNC.getCode())
                        .tenantId(plannerUserBind.getTenantId())
                        .createUser("0")
                        .userType(UserTypeEnum.getEnumsByCode(Integer.parseInt(plannerUserBind.getResource())).getCode())
                        .userStatus(ALLOCATION.getCode())
                        .sceneId(isCrmUser ? crmScene.getSceneId() : Objects.nonNull(scene) ? scene.getSceneId() : 0)
                        .groupId(Objects.nonNull(plannerInfoDto) && StringUtils.isNotBlank(plannerInfoDto.getGroupId()) ? Long.parseLong(plannerInfoDto.getGroupId()) : 0)
                        .developStartTime(plannerUserBind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                        .developCycle(isCrmUser ? Integer.valueOf(crmScene.getDevelopCycle()) : null)
                        .developEndTime(isCrmUser ? LocalDateTime.now().plusDays(Integer.parseInt(crmScene.getDevelopCycle())) : plannerUserBind.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                        .followUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode())
                        .sceneNo(plannerUserBind.getRemark())
                        .createTime(getInboundTime(plannerUserBind, isCrmUser))
                        .inboundTime(getInboundTime(plannerUserBind, false))
                        .bindTime(plannerUserBind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                        .plannerId(plannerUserBind.getPlannerId())
                        .outboundTime(outboundRecord.getCreateTime())
                        .build();
                result.add(userHis);
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            callcenterUserHisService.saveBatch(result);
        }
        return true;
    }

    @Override
    public Boolean changePlannerHistory(Set<Long> ids) {
        List<PlannerUserBindHistory> bindHistory = iPlannerUserBindHistoryService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(ids), PlannerUserBindHistory::getId, ids)
                .gt(PlannerUserBindHistory::getUpdateTime, LocalDateTime.of(2025, 5, 12, 0, 0, 0))
                .list();

        if (CollectionUtils.isEmpty(bindHistory)) {
            return false;
        }
        List<CallcenterUserHis> result = new ArrayList<>();
        CallcenterSceneConfig crmScene = sceneService.getById(crmDefaultSceneId);
        for (PlannerUserBindHistory plannerUserBindHistory : bindHistory) {
            if (StringUtils.isBlank(plannerUserBindHistory.getRemark())) {
                continue;
            }
            PlannerUserBind plannerUserBind = new PlannerUserBind();
            BeanUtils.copyProperties(plannerUserBindHistory, plannerUserBind);
            CallcenterSceneConfig scene = sceneService.lambdaQuery().eq(CallcenterSceneConfig::getSceneNo, plannerUserBind.getResource()).one();
            PlannerInfoDto plannerInfoDto = bladeUserService.getPlannerInfoByPlannerId(plannerUserBind.getPlannerId());
            boolean isCrmUser = CallcenterUserSourceEnum.CRM_SYNC.getCode().equals(Integer.parseInt(plannerUserBind.getResource()));
            LambdaQueryWrapper<UserRegistryInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserRegistryInfo::getUserId, plannerUserBind.getUserId());
            UserRegistryInfo userRegistryInfos = userRegistryInfoMapper.selectOne(queryWrapper);
            CallcenterUserHis userHis = CallcenterUserHis.builder()
                    .userId(plannerUserBind.getUserId())
                    .userName(plannerUserBind.getUserName())
                    .userPhone(Objects.nonNull(userRegistryInfos) ? userRegistryInfos.getMobileNo() : "")
                    .virtualUser(false)
                    .source(isCrmUser ? CallcenterUserSourceEnum.CRM_SYNC.getCode() : CallcenterUserSourceEnum.AVAYA_SYNC.getCode())
                    .tenantId(plannerUserBind.getTenantId())
                    .createUser("0")
                    .userType(UserTypeEnum.getEnumsByCode(Integer.parseInt(plannerUserBind.getResource())).getCode())
                    .userStatus(ALLOCATION.getCode())
                    .sceneId(isCrmUser ? crmScene.getSceneId() : Objects.nonNull(scene) ? scene.getSceneId() : 0)
                    .groupId(Objects.nonNull(plannerInfoDto) && StringUtils.isNotBlank(plannerInfoDto.getGroupId()) ? Long.parseLong(plannerInfoDto.getGroupId()) : 0)
                    .developStartTime(plannerUserBind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .developCycle(isCrmUser ? Integer.valueOf(crmScene.getDevelopCycle()) : null)
                    .developEndTime(isCrmUser ? LocalDateTime.now().plusDays(Integer.parseInt(crmScene.getDevelopCycle())) : plannerUserBind.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .followUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode())
                    .sceneNo(plannerUserBind.getRemark())
                    .createTime(getInboundTime(plannerUserBind, isCrmUser))
                    .inboundTime(getInboundTime(plannerUserBind, false))
                    .bindTime(plannerUserBind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .plannerId(plannerUserBind.getPlannerId())
                    .outboundTime(plannerUserBind.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .build();
            result.add(userHis);
        }
        if (CollectionUtils.isNotEmpty(result)) {
            callcenterUserHisService.saveBatch(result);
        }
        return true;
    }

    @Override
    public Boolean changeBindEndTime(Set<Long> ids) {
        List<PlannerUserBind> bindHistory = plannerUserBindService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(ids), PlannerUserBind::getId, ids)
                .list();

        if (CollectionUtils.isEmpty(bindHistory)) {
            return false;
        }
        for (PlannerUserBind bind : bindHistory) {
            CallcenterUserDto callCenterUserDTO = this.getCenterUserByUserId(bind.getUserId(), bind.getTenantId());
            if (Objects.isNull(callCenterUserDTO)) {
                continue;
            }
            PlannerUserBind update = new PlannerUserBind();
            update.setId(bind.getId());
            update.setBindTime(Date.from(callCenterUserDTO.getDevelopStartTime().atZone(ZoneId.systemDefault()).toInstant()));
            update.setEndTime(Date.from(callCenterUserDTO.getDevelopEndTime().atZone(ZoneId.systemDefault()).toInstant()));
            update.setResource(callCenterUserDTO.getUserType() + "");
            update.setSceneId(callCenterUserDTO.getSceneId());
            update.setUpdateTime(new Date());
            update.setRemark(callCenterUserDTO.getSceneNo());
            plannerUserBindService.updateById(update);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean changesFollowUpStatus(String userId) {
        CallcenterUserDto callcenterUserDto = this.getCenterUserByUserId(userId, AuthUtil.getTenantId());
        if (Objects.isNull(callcenterUserDto)) {
            log.warn("没有查询该租户下:{}的用户:{}", AuthUtil.getTenantId(), userId);
            return false;
        }
        LambdaUpdateWrapper<CallcenterUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CallcenterUser::getId, callcenterUserDto.getId())
                .eq(CallcenterUser::getFollowUpStatus, UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode())
                .set(CallcenterUser::getFollowUpStatus, UserFollowUpStatusEnum.FOLLOW_UP.getCode());
        this.update(updateWrapper);
        this.pushCallCenterUserChangeEvent(Sets.newHashSet(callcenterUserDto.getId()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserDeptId(BladeUser bladeUser, PlannerInfoDto plannerInfoDto) {
        Long minId = 0L;
        int pageSize = 100;
        //分批更新数据，防止内存溢出
        int total = 0;
        while (true) {
            //获取需要更新的用户数数据
            List<CallcenterUser> callCenterUsers = callcenterUserMapper.findNeedChangeDeptUser(bladeUser.getCode(), Long.valueOf(plannerInfoDto.getGroupId()), bladeUser.getTenantId(), minId, pageSize);
            if (CollectionUtils.isEmpty(callCenterUsers)) {
                break;
            }
            total += callCenterUsers.size();
            //执行更新用户部门信息
            List<CallcenterUser> updates = callCenterUsers.stream().map(e -> {
                CallcenterUser callcenterUser = new CallcenterUser();
                callcenterUser.setId(e.getId());
                callcenterUser.setGroupId(Long.valueOf(plannerInfoDto.getGroupId()));
                return callcenterUser;
            }).collect(toList());
            updateBatchById(updates);
            Set<Long> ids = callCenterUsers.stream().map(CallcenterUser::getId).collect(toSet());
            pushCallCenterUserChangeEvent(ids);
            if (callCenterUsers.size() < pageSize) {
                break;
            }
            minId = callCenterUsers.get(pageSize - 1).getId();
        }
        log.info("理财师变更部门,本此次更新callCenterUser表:{}条数据", total);
    }

    @Override
    public List<CallcenterUserDto> batchGetCenterUserInfo(List<String> userIds, String tenantId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        try {
            List<CallcenterUser> users = this.lambdaQuery()
                    .eq(StringUtils.isNotBlank(tenantId), CallcenterUser::getTenantId, tenantId)
                    .and(
                            i -> i.in(CallcenterUser::getUserId, userIds).or().in(CallcenterUser::getVirtualUserId, userIds)
                    ).list();
            // 批量查询用户信息
            if (CollectionUtils.isEmpty(users)) {
                return Collections.emptyList();
            }
            // 转换为DTO对象
            return users.stream()
                    .map(callcenterUserConvert::modelToDto)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("batchGetCenterUserInfo error, userIds: {}, tenantId: {}", userIds, tenantId, e);
            return Collections.emptyList();
        }
    }

    private void handleRegisterUserHistoryData(List<PlannerUserBind> values, CallcenterSceneConfig crmScene) {
        List<String> userIds = values.stream().map(PlannerUserBind::getUserId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        userIds.remove("");
        List<CallcenterUser> callCenterUsers = this.lambdaQuery().in(CallcenterUser::getUserId, userIds).list();
        List<String> dbUserIds = Optional.ofNullable(callCenterUsers).map(Collection::stream).orElse(Stream.empty()).map(CallcenterUser::getUserId).filter(StringUtils::isNotBlank).collect(toList());
        values = values.stream().filter(e -> !dbUserIds.contains(e.getUserId())).collect(toList());
        userIds.removeAll(dbUserIds);
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        LambdaQueryWrapper<UserRegistryInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserRegistryInfo::getUserId, userIds);
        List<UserRegistryInfo> userRegistryInfos = userRegistryInfoMapper.selectList(queryWrapper);
        Map<String, String> mobileMap = Optional.ofNullable(userRegistryInfos).map(Collection::stream).orElse(Stream.empty()).filter(e -> StringUtils.isNotBlank(e.getMobileNo()))
                .collect(toMap(e -> e.getUserId() + "", UserRegistryInfo::getMobileNo, (k1, k2) -> k2));
        List<String> sceneNos = values.stream().map(PlannerUserBind::getRemark).filter(StringUtils::isNotBlank).collect(toList());
        List<String> plannerIds = values.stream().map(PlannerUserBind::getPlannerId).filter(StringUtils::isNotBlank).collect(toList());
        Map<String, CallcenterSceneConfig> sceneConfigMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sceneNos)) {
            List<CallcenterSceneConfig> sceneList = sceneService.lambdaQuery().in(CallcenterSceneConfig::getSceneNo, sceneNos).list();
            sceneConfigMap = Optional.ofNullable(sceneList).map(Collection::stream).orElse(Stream.empty())
                    .collect(toMap(CallcenterSceneConfig::getSceneNo, e -> e, (k1, k2) -> k2));
        }
        Map<String, PlannerInfoDto> deptMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(plannerIds)) {
            List<PlannerInfoDto> plannerInfoList = bladeUserService.findPlannerInfoByPlannerIds(AuthUtil.getTenantId(), plannerIds);
            deptMap = Optional.ofNullable(plannerInfoList).map(Collection::stream).orElse(Stream.empty())
                    .collect(toMap(PlannerInfoDto::getPlannerId, e -> e, (k1, k2) -> k2));

        }
        List<CallcenterUser> callCenterUserList = new ArrayList<>();
        for (PlannerUserBind value : values) {
            boolean isCrmUser = CallcenterUserSourceEnum.CRM_SYNC.getCode().equals(Integer.parseInt(value.getResource()));
            PlannerInfoDto plannerInfoDto = deptMap.get(value.getPlannerId());
            CallcenterUser callCenterUser = CallcenterUser.builder()
                    .userId(value.getUserId())
                    .userName(value.getUserName())
                    .userPhone(mobileMap.getOrDefault(value.getUserId(), ""))
                    .virtualUser(false)
                    .source(isCrmUser ? CallcenterUserSourceEnum.CRM_SYNC.getCode() : CallcenterUserSourceEnum.AVAYA_SYNC.getCode())
                    .tenantId(value.getTenantId())
                    .createUser("0")
                    .userType(UserTypeEnum.getEnumsByCode(Integer.parseInt(value.getResource())).getCode())
                    .userStatus(ALLOCATION.getCode())
                    .sceneId(isCrmUser ? crmScene.getSceneId() : sceneConfigMap.containsKey(value.getRemark()) ? sceneConfigMap.get(value.getRemark()).getSceneId() : 0)
                    .groupId(Objects.nonNull(plannerInfoDto) && StringUtils.isNotBlank(plannerInfoDto.getGroupId()) ? Long.parseLong(plannerInfoDto.getGroupId()) : 0)
                    .developStartTime(value.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .developCycle(isCrmUser ? Integer.valueOf(crmScene.getDevelopCycle()) : null)
                    .developEndTime(isCrmUser ? LocalDateTime.now().plusDays(Integer.parseInt(crmScene.getDevelopCycle())) : value.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .followUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode())
                    .sceneNo(value.getRemark())
                    .createTime(getInboundTime(value, isCrmUser))
                    .build();
            callCenterUserList.add(callCenterUser);
        }
        saveBatch(callCenterUserList);
    }

    private LocalDateTime getInboundTime(PlannerUserBind value, boolean isCrmUser) {
        if (isCrmUser && Objects.nonNull(value.getBindTime())) {
            return value.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
        LambdaQueryWrapper<CxListStatus> queryWrapper = new LambdaQueryWrapper<>();
        DecimalFormat formatter = new DecimalFormat("0.#########E0");
        String scientificNotation = formatter.format(Double.parseDouble(value.getUserId()));
        queryWrapper.in(CxListStatus::getPassportId, Sets.newHashSet(scientificNotation, value.getUserId()));
        queryWrapper.orderByDesc(CxListStatus::getStatusId);
        List<CxListStatus> cxListStatuses = cxListStatusMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(cxListStatuses) && Objects.nonNull(cxListStatuses.get(0).getImportTime())) {
            return cxListStatuses.get(0).getImportTime();
        }
        return value.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    private void handleVirtualUserHistoryData(List<PlannerUserBind> values) {
        List<String> userIds = values.stream().map(PlannerUserBind::getUserId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        userIds.remove("");
        List<CallcenterUser> callCenterUsers = this.lambdaQuery().in(CallcenterUser::getVirtualUserId, userIds).list();
        List<String> dbVirtualUserIds = Optional.ofNullable(callCenterUsers).map(Collection::stream).orElse(Stream.empty())
                .map(CallcenterUser::getVirtualUserId).filter(StringUtils::isNotBlank).collect(toList());
        values = values.stream().filter(e -> !dbVirtualUserIds.contains(e.getUserId())).collect(toList());
        userIds.removeAll(dbVirtualUserIds);
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        List<String> sceneNos = values.stream().map(PlannerUserBind::getRemark).filter(StringUtils::isNotBlank).collect(toList());
        List<String> plannerIds = values.stream().map(PlannerUserBind::getPlannerId).filter(StringUtils::isNotBlank).collect(toList());
        Map<String, CallcenterSceneConfig> sceneConfigMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sceneNos)) {
            List<CallcenterSceneConfig> sceneList = sceneService.lambdaQuery().in(CallcenterSceneConfig::getSceneNo, sceneNos).list();
            sceneConfigMap = Optional.ofNullable(sceneList).map(Collection::stream).orElse(Stream.empty())
                    .collect(toMap(CallcenterSceneConfig::getSceneNo, e -> e, (k1, k2) -> k2));
        }
        Map<String, PlannerInfoDto> deptMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(plannerIds)) {
            List<PlannerInfoDto> plannerInfoList = bladeUserService.findPlannerInfoByPlannerIds(AuthUtil.getTenantId(), plannerIds);
            deptMap = Optional.ofNullable(plannerInfoList).map(Collection::stream).orElse(Stream.empty())
                    .collect(toMap(PlannerInfoDto::getPlannerId, e -> e, (k1, k2) -> k2));

        }
        List<CallcenterUser> callCenterUserList = new ArrayList<>();
        for (PlannerUserBind value : values) {
            PlannerInfoDto plannerInfoDto = deptMap.get(value.getPlannerId());
            CallcenterUser callCenterUser = CallcenterUser.builder()
                    .virtualUserId(value.getUserId())
                    .userPhone(this.getAvayaVirtualUserPhone(value))
                    .virtualUser(true)
                    .source(CallcenterUserSourceEnum.AVAYA_SYNC.getCode())
                    .tenantId(value.getTenantId())
                    .createUser("0")
                    .userType(UserTypeEnum.getEnumsByCode(Integer.parseInt(value.getResource())).getCode())
                    .userStatus(ALLOCATION.getCode())
                    .sceneId(sceneConfigMap.containsKey(value.getRemark()) ? sceneConfigMap.get(value.getRemark()).getSceneId() : 0)
                    .groupId(Objects.nonNull(plannerInfoDto) && StringUtils.isNotBlank(plannerInfoDto.getGroupId()) ? Long.parseLong(plannerInfoDto.getGroupId()) : 0)
                    .developStartTime(value.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .developEndTime(value.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .followUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode())
                    .sceneNo(value.getRemark())
                    .createTime(getInboundTime(value, false))
                    .build();
            callCenterUserList.add(callCenterUser);
        }
        saveBatch(callCenterUserList);
    }


    /**
     * 构建CallCenterUser 信息
     */
    private CallcenterUser buildCallCenterUser(InBoundUserInfo userInfo, CallcenterSceneConfig sceneConfig) {
        return CallcenterUser.builder()
                .userId(userInfo.getUserId())
                .virtualUserId(userInfo.getVirtualUserId())
                .userPhone(userInfo.getUserPhone())
                .virtualUser(Objects.nonNull(userInfo.getVirtualUserId()))
                .source(userInfo.getSourceEnum().getCode())
                .tenantId(sceneConfig.getTenantId())
                .userType(userInfo.getUserTypeEnum().getCode())
                .userStatus(userInfo.getUserStatusEnum().getCode())
                .sceneId(sceneConfig.getSceneId())
                .sceneNo(sceneConfig.getSceneNo())
                .followUpStatus(UserFollowUpStatusEnum.UN_FOLLOW_UP.getCode())
                .build();
    }

    /**
     * 构造入库记录
     */
    private CallcenterUserInboundRecord buildCallCenterUserInboundRecords(String userId,
                                                                          CallcenterSceneConfig sceneConfig,
                                                                          UserInbindStatusEnum inboundStatus,
                                                                          String failureReason) {
        return CallcenterUserInboundRecord.builder()
                .customerUserId(userId)
                .createTime(LocalDateTime.now())
                .sceneId(sceneConfig.getSceneId())
                .status(inboundStatus.getCode())
                .failureReason(failureReason)
                .inboundType(UserInbindTypeEnum.TAG.getCode())
                .tagType(StringUtils.isBlank(sceneConfig.getTagType()) ? Integer.valueOf(TagTypeEnum.T0.getCode()) : Integer.valueOf(sceneConfig.getTagType()))
                .tagName(sceneConfig.getTagName())
                .tenantId(sceneConfig.getTenantId())
                .build();
    }

    private String getAvayaVirtualUserPhone(PlannerUserBind plannerUserBind) {
        DecimalFormat formatter = new DecimalFormat("0.#########E0");
        String scientificNotation = formatter.format(Double.parseDouble(plannerUserBind.getUserId()));
        LambdaQueryWrapper<CxListStatus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CxListStatus::getPassportId, Sets.newHashSet(scientificNotation, plannerUserBind.getUserId()));
        List<CxListStatus> cxListStatuses = cxListStatusMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(cxListStatuses)) {
            return "";
        }
        return cxListStatuses.get(0).getPhone();
    }

    /**
     * 构建出库记录
     */
    private CallCenterUserOutboundRecord buildCallCenterUserOutboundRecord(CallcenterUser callcenterUser, UserOutbindTypeEnum userOutboundTypeEnum, String createUserId) {
        return CallCenterUserOutboundRecord.builder()
                .customerUserId(StringUtils.isBlank(callcenterUser.getUserId()) ? callcenterUser.getVirtualUserId() : callcenterUser.getUserId())
                .sceneId(callcenterUser.getSceneId())
                .outboundType(userOutboundTypeEnum.getCode())
                .createUser(createUserId)
                .build();
    }

    @Override
    @Transactional
    public void processUserOutbound(UserOutbindTypeEnum userOutbindTypeEnum, CallcenterUserDto callcenterUserDto, String operatorId) {
        Set<String> userIds = Sets.newHashSet(callcenterUserDto.getCurUserId());
        UserOutboundInfo userOutboundInfo = UserOutboundInfo.builder().userIds(userIds)
                .type(userOutbindTypeEnum).operatorId(operatorId)
                .tenantId(callcenterUserDto.getTenantId())
                .flag(true)
                .build();
        processUserOutbound(userOutboundInfo);
    }

    /**
     * 保存历史数据
     */
    private void pushCallCenterUserChangeEvent(Set<Long> ids) {
        CallCenterUserChangeEvent callCenterUserChangeEvent = new CallCenterUserChangeEvent(this);
        callCenterUserChangeEvent.setOccurTime(LocalDateTime.now());
        callCenterUserChangeEvent.setCallCenterUserId(ids);
        Object transactionId = TransactionSynchronizationManager.getCurrentTransactionName();
        callCenterUserChangeEvent.setTransactionId(Objects.nonNull(transactionId) ? transactionId.toString() : null);
        applicationContext.publishEvent(callCenterUserChangeEvent);
    }

}
