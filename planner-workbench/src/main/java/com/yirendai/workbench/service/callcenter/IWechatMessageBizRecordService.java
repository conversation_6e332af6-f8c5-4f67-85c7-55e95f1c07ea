package com.yirendai.workbench.service.callcenter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.consumer.bean.WechatMessageBizRecordEvent;
import com.yirendai.workbench.entity.CallcenterWechatMessageBizRecord;

/**
 * <p>
 * 聊天信息业务记录表 服务类
 * </p>
 *
 * @since 2025-10-13
 */
public interface IWechatMessageBizRecordService extends IService<CallcenterWechatMessageBizRecord> {

    /**
     * 处理微信消息业务记录事件
     * 解析消息内容并保存到业务记录表
     *
     * @param event 微信消息业务记录事件
     */
    void handleWechatMessageBizRecordEvent(WechatMessageBizRecordEvent event);
}
