package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeEdge;
import com.yirendai.workbench.mapper.CallcenterSaleProcessNodeEdgeMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeEdgeService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 销售流程节点边信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
@Service
public class CallcenterSaleProcessNodeEdgeServiceImpl extends ServiceImpl<CallcenterSaleProcessNodeEdgeMapper, CallcenterSaleProcessNodeEdge> implements ICallcenterSaleProcessNodeEdgeService {

    /**
     * 根据流程id获取 从前到后排序号的节点id列表
     * @param processId
     * @return
     */
    @Override
    public List<String> getNodeListByProcessId(Long processId){
        LambdaQueryWrapper<CallcenterSaleProcessNodeEdge> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CallcenterSaleProcessNodeEdge::getSaleProcessId, processId);
        wrapper.eq(CallcenterSaleProcessNodeEdge::getIsDeleted,false);
        List<CallcenterSaleProcessNodeEdge> allEdges = this.list(wrapper);
        if (CollectionUtils.isEmpty(allEdges)) {
            return new ArrayList<>();
        }

        // 2. 构建节点映射，便于快速查找
        Map<String, CallcenterSaleProcessNodeEdge> edgeMap = allEdges.stream()
                .collect(Collectors.toMap(CallcenterSaleProcessNodeEdge::getFromNodeId, edge -> edge));

        // 3. 找到起始节点（没有其他节点指向它的节点）
        String startNodeId = findStartNodeId(allEdges);
        if (startNodeId == null) {
            // 如果没有找到起始节点，尝试找到结束节点，然后反向遍历
            String endNodeId = findEndNodeId(allEdges);
            if (endNodeId != null) {
                return buildOrderedNodeIdsFromEnd(endNodeId, edgeMap);
            }
            // 如果既没有起始节点也没有结束节点，返回所有节点ID
            return allEdges.stream()
                    .map(CallcenterSaleProcessNodeEdge::getFromNodeId)
                    .distinct()
                    .collect(Collectors.toList());
        }

        // 4. 从起始节点开始遍历，构建有序列表
        return buildOrderedNodeIdsFromStart(startNodeId, edgeMap);
    }

    /**
     * 找到起始节点ID（没有其他节点指向它的节点）
     * @param edges 所有节点边
     * @return 起始节点ID
     */
    private String findStartNodeId(List<CallcenterSaleProcessNodeEdge> edges) {
        // 获取所有目标节点ID
        Set<String> targetNodeIds = edges.stream()
                .map(CallcenterSaleProcessNodeEdge::getToNodeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 找到不在目标节点中的起始节点
        return edges.stream()
                .map(CallcenterSaleProcessNodeEdge::getFromNodeId)
                .filter(nodeId -> !targetNodeIds.contains(nodeId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 找到结束节点ID（没有指向其他节点的节点）
     * @param edges 所有节点边
     * @return 结束节点ID
     */
    private String findEndNodeId(List<CallcenterSaleProcessNodeEdge> edges) {
        // 获取所有起始节点ID
        Set<String> sourceNodeIds = edges.stream()
                .map(CallcenterSaleProcessNodeEdge::getFromNodeId)
                .collect(Collectors.toSet());

        // 找到不在起始节点中的结束节点
        return edges.stream()
                .map(CallcenterSaleProcessNodeEdge::getToNodeId)
                .filter(Objects::nonNull)
                .filter(nodeId -> !sourceNodeIds.contains(nodeId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 从起始节点开始构建有序的节点ID列表
     * @param startNodeId 起始节点ID
     * @param edgeMap 节点边映射
     * @return 有序的节点ID列表
     */
    private List<String> buildOrderedNodeIdsFromStart(String startNodeId, Map<String, CallcenterSaleProcessNodeEdge> edgeMap) {
        List<String> orderedNodeIds = new ArrayList<>();
        String currentNodeId = startNodeId;
        Set<String> visitedNodes = new HashSet<>();

        while (currentNodeId != null && !visitedNodes.contains(currentNodeId)) {
            visitedNodes.add(currentNodeId);
            orderedNodeIds.add(currentNodeId);

            // 移动到下一个节点
            CallcenterSaleProcessNodeEdge currentEdge = edgeMap.get(currentNodeId);
            if (currentEdge != null && currentEdge.getToNodeId() != null) {
                currentNodeId = currentEdge.getToNodeId();
            } else {
                break;
            }
        }

        return orderedNodeIds;
    }

    /**
     * 从结束节点开始构建有序的节点ID列表（反向遍历后反转）
     * @param endNodeId 结束节点ID
     * @param edgeMap 节点边映射
     * @return 有序的节点ID列表
     */
    private List<String> buildOrderedNodeIdsFromEnd(String endNodeId, Map<String, CallcenterSaleProcessNodeEdge> edgeMap) {
        List<String> reverseOrderedNodeIds = new ArrayList<>();
        String currentNodeId = endNodeId;
        Set<String> visitedNodes = new HashSet<>();

        // 从结束节点开始反向遍历
        while (currentNodeId != null && !visitedNodes.contains(currentNodeId)) {
            visitedNodes.add(currentNodeId);
            reverseOrderedNodeIds.add(currentNodeId);

            // 移动到前一个节点
            String prevNodeId = findPreviousNodeId(currentNodeId, edgeMap);
            if (prevNodeId != null) {
                currentNodeId = prevNodeId;
            } else {
                break;
            }
        }

        // 反转列表，得到正确的顺序
        Collections.reverse(reverseOrderedNodeIds);
        return reverseOrderedNodeIds;
    }

    /**
     * 找到指定节点的前一个节点ID
     * @param nodeId 当前节点ID
     * @param edgeMap 节点边映射
     * @return 前一个节点ID
     */
    private String findPreviousNodeId(String nodeId, Map<String, CallcenterSaleProcessNodeEdge> edgeMap) {
        return edgeMap.values().stream()
                .filter(edge -> nodeId.equals(edge.getToNodeId()))
                .map(CallcenterSaleProcessNodeEdge::getFromNodeId)
                .findFirst()
                .orElse(null);
    }

}
