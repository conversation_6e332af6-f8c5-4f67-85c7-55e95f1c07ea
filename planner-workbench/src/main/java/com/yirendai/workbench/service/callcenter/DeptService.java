package com.yirendai.workbench.service.callcenter;

import org.springblade.system.entity.Dept;

import java.util.List;

public interface DeptService {

    /**
     * 获取当前用户小组信息category为3，参考
     *  @see com.yirendai.workbench.enums.callcenter.DeptCategoryEnum
     */
    Dept getUserGroupDeptInfo();
    /**
     * 查询小组信息，category为3，参考
     * @see com.yirendai.workbench.enums.callcenter.DeptCategoryEnum
     */
    Dept getGroupDept(Long groupId);

    /**
     * 查询所有小组信息，category为3，参考
     * @see com.yirendai.workbench.enums.callcenter.DeptCategoryEnum
     */
    List<Dept> getGroupDeptsByTenantId(String tenantId);
}
