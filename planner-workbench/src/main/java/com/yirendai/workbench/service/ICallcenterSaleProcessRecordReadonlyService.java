package com.yirendai.workbench.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessRecord;
import com.yirendai.workbench.vo.req.callcenter.CallcenterCustomerSalesProcessListReq;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessRecordRes;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerNodeInfoResp;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerSaleProcessInfoResp;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerSaleProcessRecordResp;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售流程进度记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
public interface ICallcenterSaleProcessRecordReadonlyService extends IService<CallcenterSaleProcessRecord> {

    Page<CallcenterCustomerSaleProcessRecordResp> getUserSalesProcessInfoList(CallcenterCustomerSalesProcessListReq req);

    CallcenterCustomerSaleProcessInfoResp getSalesProcessInfoById(String userId, String saleProcessRecordId);

    CallcenterCustomerNodeInfoResp getCustomerNodeInfo(String userId, String nodeId);

    Integer countSaleProcessRecordListPage(Map<String,Object> paramMap);

    List<CallcenterSaleProcessRecordRes> getSaleProcessRecordListPage(Map<String,Object> paramMap);

    Integer countCustomerSaleProcessRecordListPage(Map<String,Object> paramMap);

    List<CallcenterCustomerSaleProcessRecordResp> getCustomerSaleProcessRecordListPage(Map<String,Object> paramMap);

}
