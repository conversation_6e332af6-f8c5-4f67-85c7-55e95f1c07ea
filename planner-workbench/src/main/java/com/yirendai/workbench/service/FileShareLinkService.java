package com.yirendai.workbench.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.workbench.vo.req.FileShareLinkPageReq;
import com.yirendai.workbench.vo.req.callcenter.LinkDataDetailReq;
import com.yirendai.workbench.vo.req.FileShareLinkSaveOrUpdateReq;
import com.yirendai.workbench.vo.res.FileShareLinkDetailVo;
import com.yirendai.workbench.vo.res.FileShareLinkUploadVo;
import com.yirendai.workbench.model.callcenter.LinkVisitDetailVo;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

public interface FileShareLinkService  {

    /**
     * 上传文件转链接的原始文件
     */
    FileShareLinkUploadVo upload(MultipartFile file);

    /**
     * 保存或更新文件转链接Entity
     */
    Long saveOrUpdate(FileShareLinkSaveOrUpdateReq req);


    /**
     * 根据链接ID和用户ID查询特定用户访问详情
     */
    IPage<LinkVisitDetailVo> findLinkVisitInfo(@Valid LinkDataDetailReq request);

    /**
     * 根据链接名称,使用场景和时间查询文件转链接详情
     */
    IPage<FileShareLinkDetailVo> findFileShareLinkDetail(@Valid FileShareLinkPageReq req);

    /**
     * 校验是否为企划书下发消息
     */
    boolean isPlanBookDistribution(String content);
}
