package com.yirendai.workbench.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.yirendai.voiceaiserver.util.FileUtil;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.entity.FileShareLink;
import com.yirendai.workbench.entity.LinkFile;
import com.yirendai.workbench.enums.FileShareLinkSceneEnum;
import com.yirendai.workbench.enums.FileShareLinkTypeEnum;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.mapper.FileShareLinkMapper;
import com.yirendai.workbench.mapper.callcenter.LinkVisitRecordMapper;
import com.yirendai.workbench.model.callcenter.LinkStatisticsVo;
import com.yirendai.workbench.model.callcenter.LinkVisitDetailVo;
import com.yirendai.workbench.service.FileShareLinkService;
import com.yirendai.workbench.service.LinkFileService;
import com.yirendai.workbench.service.callcenter.IBladeTenantService;
import com.yirendai.workbench.util.DataScopeUtil;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.FileShareLinkPageReq;
import com.yirendai.workbench.vo.req.FileShareLinkSaveOrUpdateReq;
import com.yirendai.workbench.vo.req.callcenter.LinkDataDetailReq;
import com.yirendai.workbench.vo.res.FileShareLinkDetailVo;
import com.yirendai.workbench.vo.res.FileShareLinkUploadVo;
import com.yirendai.workbench.vo.res.LinkFileVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FileShareLinkServiceImpl implements FileShareLinkService {
    @Autowired
    private LinkFileService linkFileService;

    @Autowired
    private FileShareLinkMapper fileShareLinkMapper;

    @Autowired
    @Qualifier("convertImageRestTemplate")
    private RestTemplate convertImageRestTemplate;

    @Autowired
    private IBladeTenantService iBladeTenantService;

    @Autowired
    private CallcenterProperty callcenterProperty;

    @Value("${url.fileConvertImage:http://callcent-intent-flow.fso-yrcf-appserver.paas.test/file_to_images_nas}")
    private String fileConvertImageUrl;

    @Value("${fileShareLink.nasPath}")
    private String fileShareLinkNasPath;

    @Value("${fileShareLink.nasUrl}")
    private String fileShareLinkNasUrl;

    @Value("${fileShareLink.nasInnerUrl}")
    private String fileShareLinkNasInnerUrl;

    @Resource
    private LinkVisitRecordMapper linkVisitRecordMapper;

    @Resource
    DataScopeUtil dataScopeUtil;

    private static final Pattern pattern = Pattern.compile("/file2url/#/index\\?linkId=([^&\\s]+)");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileShareLinkUploadVo upload(MultipartFile file) {
        if (Objects.isNull(file) || StringUtils.isBlank(file.getOriginalFilename())) {
            throw new BusinessException(BusinessError.FILE_IS_NULL);
        }
        String originalFilename = file.getOriginalFilename();

        List<LinkFile> linkFileList = new ArrayList<>();
        String fileAbsolutePath = null;
        try {
            String fileName = saveFileAndGetFileName(file);
            fileAbsolutePath = fileShareLinkNasPath + fileName;
            //PDF需要转换成多张图片
            if (originalFilename.toLowerCase().endsWith(".pdf")) {
                List<String> convertedImagePathList = convertPdfToImage(fileShareLinkNasInnerUrl + fileName);
                if (CollectionUtils.isNotEmpty(convertedImagePathList)) {
                    List<LinkFile> pdfLinkFileList = convertedImagePathList.stream().map(path ->
                                    LinkFile.builder()
                                            .fileOriginName(originalFilename)
                                            .fileAbsolutePath(path)
                                            .build())
                            .collect(Collectors.toList());
                    linkFileList.addAll(pdfLinkFileList);
                }
            } else {
                linkFileList = Collections.singletonList(LinkFile.builder().fileOriginName(originalFilename).fileAbsolutePath(fileAbsolutePath).build());
            }
            if (CollectionUtils.isNotEmpty(linkFileList)) {
                linkFileService.saveBatch(linkFileList);
            }
            return FileShareLinkUploadVo.builder()
                    .linkFileIdList(linkFileList.stream().map(LinkFile::getId).collect(Collectors.toList()))
                    .fileUrl(fileShareLinkNasUrl + fileName)
                    .build();
        } catch (Exception e) {
            log.error("上传文件转链接 原文件异常:", e);
//            if (StringUtils.isNotBlank(fileAbsolutePath)) {
//                try {
//                    Files.deleteIfExists(Paths.get(fileAbsolutePath));
//                } catch (IOException ex) {
//                    log.warn("删除文件失败, path: {}", fileAbsolutePath, ex);
//                }
//            }
            throw new BusinessException(BusinessError.SAVE_FILE_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(FileShareLinkSaveOrUpdateReq req) {
        if (Objects.isNull(FileShareLinkSceneEnum.of(req.getScene()))) {
            throw new BusinessException(BusinessError.FILE_SHARE_LINK_SCENE_INVALID);
        }
        if (Objects.isNull(FileShareLinkTypeEnum.of(req.getFileType()))) {
            throw new BusinessException(BusinessError.FILE_SHARE_LINK_TYPE_INVALID);
        }
        List<Long> linkFileIdList = req.getFileIdList();
        List<LinkFile> linkFileList = linkFileService.getBaseMapper().selectBatchIds(linkFileIdList);
        if (CollectionUtils.isEmpty(linkFileList) || linkFileList.size() != linkFileIdList.size()) {
            throw new BusinessException(BusinessError.FILE_SHARE_LINK_ORIGIN_FILE_NOT_EXIST);
        }

        // 根据主键ID保存或更新文件转链接主表
        Long fileShareLinkId = Objects.isNull(req.getId()) ? saveFileShareLink(req) : updateFileShareLink(req);

        // 更新文件和链接的关联关系
        updateLinkFileRelation(fileShareLinkId, linkFileIdList);

        return fileShareLinkId;
    }

    @Override
    public IPage<LinkVisitDetailVo> findLinkVisitInfo(LinkDataDetailReq request) {

        // 创建分页对象
        Page<LinkVisitDetailVo> page = new Page<>(request.getPageNo(), request.getPageSize());


        // 执行分页查询
        return linkVisitRecordMapper.findLinkDataDetailsPage(page, request.getLinkId(), request.getUserId());
    }


    @Override
    public IPage<FileShareLinkDetailVo> findFileShareLinkDetail(FileShareLinkPageReq req) {
        Page<FileShareLink> page = new Page<>(req.getPageNo(), req.getPageSize());

        //权限管理
        boolean isSaas = iBladeTenantService.isSaasTenantId(OwnAuthUtil.getTenantId());
        List<String> plannerIdList = dataScopeUtil.getUserMenuDataScope(isSaas ?
                callcenterProperty.getSaasBermudaFile2UrlMenuCode() :
                callcenterProperty.getBermudaFile2UrlMenuCode(), "code");
        log.info("文件转链接数据权限管理plannerIdList:{}", JSON.toJSONString(plannerIdList));

        LambdaQueryWrapper<FileShareLink> queryWrapper = Wrappers.lambdaQuery(FileShareLink.class);
        queryWrapper.like(StringUtils.isNotBlank(req.getLinkName()), FileShareLink::getLinkName, req.getLinkName())
                .eq(Objects.nonNull(req.getScene()), FileShareLink::getScene, req.getScene())
                .eq(FileShareLink::getTenantId, OwnAuthUtil.getTenantId())
                .ge(Objects.nonNull(req.getUpdateStartTime()), FileShareLink::getUpdateTime, req.getUpdateStartTime())
                .le(Objects.nonNull(req.getUpdateEndTime()), FileShareLink::getUpdateTime, req.getUpdateEndTime())
                .in(CollectionUtils.isNotEmpty(plannerIdList), FileShareLink::getPlannerId, plannerIdList)
                .orderByDesc(FileShareLink::getUpdateTime);

        IPage<FileShareLink> result = fileShareLinkMapper.selectPage(page, queryWrapper);

        List<FileShareLink> fileShareLinks = result.getRecords();

        if (CollectionUtils.isEmpty(fileShareLinks)) {
            return new Page<>(req.getPageNo(), req.getPageSize());
        }

        //根据链接ID查询PV,UV
        List<String> linkIds = fileShareLinks.stream().map(FileShareLink::getLinkId).collect(Collectors.toList());
        Map<String, LinkStatisticsVo> stringLinkStatisticsVoMap = batchStatisticsAnonymousLinkPvUv(linkIds);

        List<FileShareLinkDetailVo> fileShareLinkDetailVos = new ArrayList<>(fileShareLinks.size());

        for (FileShareLink fileShareLink : fileShareLinks) {
            LinkStatisticsVo linkStatisticsVo = stringLinkStatisticsVoMap.get(fileShareLink.getLinkId());

            // 根据文件转链接主键ID查询原始文件信息
            LambdaQueryWrapper<LinkFile> linkFileLambdaQueryWrapper = Wrappers.lambdaQuery(LinkFile.class);
            linkFileLambdaQueryWrapper.eq(LinkFile::getFileShareLinkId, fileShareLink.getId()).orderByAsc(LinkFile::getSort);
            List<LinkFile> linkFileList = linkFileService.getBaseMapper().selectList(linkFileLambdaQueryWrapper);

            //PDF对应的多条记录的原始文件名称相同
            String fileOriginName = "【图片】";
            if (FileShareLinkTypeEnum.PDF.getCode().equals(fileShareLink.getFileType())) {
                fileOriginName = linkFileList.get(0).getFileOriginName();
            }

            //获取对应的文件详情信息
            List<LinkFileVo> linkFileVoList = linkFileList.stream().map(linkFile -> {
                LinkFileVo linkFileVo = LinkFileVo.builder().id(linkFile.getId()).build();
                if (FileShareLinkTypeEnum.PICTURE.getCode().equals(fileShareLink.getFileType())) {
                    linkFileVo.setFileUrl(fileShareLinkNasUrl + StringUtils.removeStart(linkFile.getFileAbsolutePath(), fileShareLinkNasPath));
                }
                return linkFileVo;
            }).collect(Collectors.toList());

            FileShareLinkDetailVo detailVo = FileShareLinkDetailVo.builder()
                    .id(fileShareLink.getId())
                    .linkId(fileShareLink.getLinkId())
                    .linkName(fileShareLink.getLinkName())
                    .fileOriginName(fileOriginName)
                    .scene(fileShareLink.getScene())
                    .fileType(fileShareLink.getFileType())
                    .operator(fileShareLink.getOperator())
                    .pv(Optional.ofNullable(linkStatisticsVo).map(LinkStatisticsVo::getPv).orElse(0))
                    .uv(Optional.ofNullable(linkStatisticsVo).map(LinkStatisticsVo::getUv).orElse(0))
                    .linkFileVoList(linkFileVoList)
                    .tenantId(fileShareLink.getTenantId())
                    .createTime(fileShareLink.getCreateTime())
                    .updateTime(fileShareLink.getUpdateTime())
                    .build();
            fileShareLinkDetailVos.add(detailVo);
        }

        Page<FileShareLinkDetailVo> voPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        voPage.setRecords(fileShareLinkDetailVos);
        return voPage;
    }

    @Override
    public boolean isPlanBookDistribution(String content) {
        Matcher matcher = pattern.matcher(content);

        if (!matcher.find()) {
            return false;
        }

        String linkId = matcher.group(1);
        if (StringUtils.isBlank(linkId)) {
            return false;
        }
        LambdaQueryWrapper<FileShareLink> lambdaQueryWrapper = Wrappers.lambdaQuery(FileShareLink.class);

        lambdaQueryWrapper
                .eq(FileShareLink::getLinkId, linkId)
                .eq(FileShareLink::getIsDeleted, 0)
                .eq(FileShareLink::getScene, FileShareLinkSceneEnum.DB_PLAN_BOOK.getCode());

        return fileShareLinkMapper.selectCount(lambdaQueryWrapper) > 0;
    }


    /**
     * 直接调用批量统计方法
     */
    public Map<String, LinkStatisticsVo> batchStatisticsAnonymousLinkPvUv(List<String> linkIds) {
        List<LinkStatisticsVo> linkStatisticsVoList = linkVisitRecordMapper.selectBatchAnonymousLinkStatistics(linkIds);
        if (CollectionUtils.isEmpty(linkStatisticsVoList)) {
            return Maps.newHashMap();
        }
        return linkStatisticsVoList.stream().collect(Collectors.toMap(LinkStatisticsVo::getLinkId, vo -> vo));
    }


    /**
     * 将文件保存至NAS并返回文件名称
     */
    private String saveFileAndGetFileName(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String suffix = "";
        String baseName = "file";
        if (originalFilename != null && originalFilename.contains(".")) {
            suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
            baseName = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        }
        String uniqueFileName = baseName + "_" + UUID.randomUUID().toString().replace("-", "") + suffix;
        File saveFile = FileUtil.saveUploadFile(file, fileShareLinkNasPath + uniqueFileName);
        if (Objects.isNull(saveFile)) {
            throw new BusinessException(BusinessError.SAVE_FILE_ERROR);
        }
        return uniqueFileName;
    }


    /**
     * 调用外部接口将PDF转换成图片, 并获取图片的绝对路径
     */
    public List<String> convertPdfToImage(String fileUrl) {
        Map<String, Object> params = new HashMap<>();
        params.put("s3_key", fileUrl);
        params.put("dpi", 100);
        params.put("use_proxy", false);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(params), headers);
        log.info("开始调用外部接口将PDF转换成图片, s3Key:{}, url:{}", fileUrl, fileConvertImageUrl);
        ResponseEntity<String> responseEntity = convertImageRestTemplate.postForEntity(fileConvertImageUrl, entity, String.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("调用外部接口将PDF转换成图片失败, ");
            throw new BusinessException(BusinessError.PDF_CONVERT_TO_IMAGE_FAILED);
        }
        JSONObject jsonObject = JSON.parseObject(responseEntity.getBody());
        return jsonObject.getJSONArray("image_paths").toJavaList(String.class);
    }


    /**
     * 保存文件转链接Entity
     */
    private Long saveFileShareLink(FileShareLinkSaveOrUpdateReq req) {
        FileShareLink fileShareLink = FileShareLink.builder()
                .linkId(RandomStringUtils.randomAlphanumeric(12))
                .linkName(req.getLinkName())
                .scene(req.getScene())
                .fileType(req.getFileType())
                .operator(OwnAuthUtil.getRealName())
                .tenantId(OwnAuthUtil.getTenantId())
                .plannerId(OwnAuthUtil.getPlannerNo())
                .build();
        fileShareLinkMapper.insert(fileShareLink);
        return fileShareLink.getId();
    }


    /**
     * 更新文件转链接Entity
     */
    private Long updateFileShareLink(FileShareLinkSaveOrUpdateReq req) {
        FileShareLink fileShareLink = FileShareLink.builder()
                .id(req.getId())
                .linkName(req.getLinkName())
                .scene(req.getScene())
                .fileType(req.getFileType())
                .operator(OwnAuthUtil.getRealName())
                .tenantId(OwnAuthUtil.getTenantId())
                .plannerId(OwnAuthUtil.getPlannerNo())
                .updateTime(LocalDateTime.now())
                .build();
        int updated = fileShareLinkMapper.updateById(fileShareLink);
        if (updated == 0) {
            throw new BusinessException(BusinessError.DATA_NOT_EXIST);
        }
        return req.getId();
    }


    /**
     * 更新文件和链接的关联关系
     */
    private void updateLinkFileRelation(Long fileShareLinkId, List<Long> newFileIdList) {
        // 清空旧的关联
        List<LinkFile> oldLinkFileList = linkFileService.list(Wrappers.<LinkFile>lambdaQuery().eq(LinkFile::getFileShareLinkId, fileShareLinkId));
        oldLinkFileList.forEach(f -> f.setFileShareLinkId(null));
        linkFileService.updateBatchById(oldLinkFileList);

        // 重新设置关联&顺序
        List<LinkFile> newLinkFileList = linkFileService.listByIds(newFileIdList);
        newLinkFileList.forEach(f -> {
            f.setFileShareLinkId(fileShareLinkId);
            f.setSort(newFileIdList.indexOf(f.getId())); // 顺序
        });
        linkFileService.updateBatchById(newLinkFileList);
    }
}
