package com.yirendai.workbench.service.callcenter.impl;

import com.google.common.collect.Lists;
import com.yirendai.workbench.entity.CallcenterRetrieveRule;
import com.yirendai.workbench.entity.CallcenterRetrieveToday;
import com.yirendai.workbench.entity.CallcenterSceneConfig;
import com.yirendai.workbench.enums.callcenter.UserStatusEnum;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.model.CallcenterUserDto;
import com.yirendai.workbench.model.PlannerInfoDto;
import com.yirendai.workbench.model.UserAllocateInfo;
import com.yirendai.workbench.service.IPlannerUserBindService;
import com.yirendai.workbench.service.callcenter.*;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.res.callcenter.RetrieveCustomerRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springblade.system.entity.Dept;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import redis.clients.jedis.JedisCluster;

import java.util.*;
import java.util.stream.Collectors;

import static com.yirendai.workbench.exception.BusinessError.GROUP_NOT_EXIST;
import static com.yirendai.workbench.exception.BusinessError.PARAM_NULL_ERROR;
import static com.yirendai.workbench.exception.CallCenterBusinessError.*;

@Slf4j
@Service
public class RetrieveServiceImpl implements RetrieveService {

    @Autowired
    private CallcenterRetrieveRuleService callcenterRetrieveRuleService;
    @Autowired
    private DeptService deptService;
    @Autowired
    private CallcenterUserService callcenterUserService;
    @Autowired
    private IPlannerUserBindService plannerUserBindService;
    @Autowired
    private SceneService sceneService;
    @Autowired
    private ICallcenterUserLockService callcenterUserLockService;

    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private JedisCluster jedisCluster;

    @Override
    public RetrieveCustomerRes retrieveCustomer(Long groupIdReq) {
        RetrieveCustomerRes retrieveCustomerInfo = new RetrieveCustomerRes();
        String userId = null;
        String tenantId = OwnAuthUtil.checkAndGetTenantId();
        /*
        校验是否超过领取上限
         */
        CallcenterRetrieveRule callcenterRetrieveRule = callcenterRetrieveRuleService.getTenantPublicRule();
        if (callcenterRetrieveRule != null && callcenterRetrieveRule.getCanRetrieve() == 1 && callcenterRetrieveRule.getIsLimited() == 1 && callcenterRetrieveRule.getLimitByPerson() > -1) {
            long hasRetrieveToday = plannerUserBindService.countPlannerRetrieveToday(OwnAuthUtil.getPlannerNo(), tenantId);
            if (hasRetrieveToday >= callcenterRetrieveRule.getLimitByPerson()) {
                throw new BusinessException(RETRIEVE_OVER_LIMIT);
            }
        }
        /*
        获取当前组id  如果小组为空，提示错误
         */
        Dept groupDept = getGroupInfo(groupIdReq);
        Long groupId = groupDept.getId();
        log.info("理财师领取客户, plannerNo:{} groupId:{}", OwnAuthUtil.getPlannerNo(), groupId);
        try {
            CallcenterUserDto userDto = null;
            /*
            根据组id获取可分配的用户（按场景优先级排序,需分配的用户）
             */
            int maxAttempts = 50; // 最大重试次数
            int attempts = 0;

            while (true) {
                if (attempts++ >= maxAttempts) {
                    throw new BusinessException(RETRIEVE_DATA_EMPTY);
                }
                userId = getRetrieveUserId(groupId);
                if (StringUtils.isBlank(userId)) {
                    throw new BusinessException(RETRIEVE_DATA_EMPTY);
                }

                userDto = callcenterUserService.getCenterUserPhoneInfo(userId, null);
                if (userDto == null) {
                    log.info("retrieveCustomer 客户已不在库 不进行领取 userId:{}", userId);
                    continue;
                }

                Long dtoGroupId = userDto.getGroupId();
                if (dtoGroupId == null || !dtoGroupId.equals(groupId)) {
                    log.info("retrieveCustomer 用户组别已变更 不进行领取 userId:{}", userId);
                    continue;
                }
                boolean isLocked = callcenterUserLockService.isLock(userId);
                if (isLocked) {
                    log.info("retrieveCustomer 用户已被锁定 不进行领取 userId:{}", userId);
                    continue;
                }
                // 满足所有条件，退出循环
                break;
            }
            /*
             判断是否在进行预分配定时任务，是则提示：客户资源正在分配中，请稍后领取
            */
            if (Objects.isNull(userDto)) {
                throw new BusinessException(RETRIEVE_USER_ERROR, "用户不存在 userId: " + userId);
            }
            checkSceneIsPreAllocating(userDto.getSceneId());
            CallcenterSceneConfig sceneConfig = sceneService.getById(userDto.getSceneId());
            /*
            更新用户已领取状态、更新用户开发时间
            新增绑定记录
             */
            final String allocateUserId = userId;
            final CallcenterUserDto retrieveUser = userDto;
            transactionTemplate.execute(status -> {
                UserAllocateInfo userAllocateInfo = callcenterUserService.updateUserStatusWhenRetrieve(retrieveUser.getId(), Integer.parseInt(sceneConfig.getDevelopCycle()));
                if (!userAllocateInfo.isAllocateSuccess()) {
                    throw new BusinessException(RETRIEVE_USER_ERROR, "更新用户状态异常 ID：" + allocateUserId);
                }
                PlannerInfoDto plannerInfo = new PlannerInfoDto();
                plannerInfo.setPlannerId(OwnAuthUtil.getPlannerNo()).setPlannerName(OwnAuthUtil.getRealName())
                        .setGroupName(groupDept.getDeptName()).setTenantId(tenantId);
                boolean bindSuccess = plannerUserBindService.retrieveToBindCustomer(retrieveUser, plannerInfo, userAllocateInfo);
                if (!bindSuccess) {
                    throw new BusinessException(RETRIEVE_USER_ERROR, "绑定用户失败：" + allocateUserId);
                }
                return true;
            });
            log.info("理财师领取用户成功， 理财师ID: {} 用户ID:{}, groupId:{}", OwnAuthUtil.getPlannerNo(), userId, groupId);
            return new RetrieveCustomerRes(userId, userDto.getUserName(), OwnAuthUtil.getPlannerNo(), OwnAuthUtil.getRealName());
        } catch (BusinessException e) {
            if (e.getCode() != RETRIEVE_USER_ERROR.getCode()) {
                //如果非领取用户异常，需要将用户重新放回队列
                putBackToRetrieveQueue(userId, groupId);
            }
            log.error("retrieveCustomer businessException", e);
            throw e;
        } catch (Exception e) {
            log.error("retrieveCustomer error", e);
            //如果异常，需要将用户重新放回队列
            putBackToRetrieveQueue(userId, groupId);
            throw e;
        }
    }

    /**
     * 场景是否在预分配中
     */
    private void checkSceneIsPreAllocating(Long sceneId) {
        boolean isRunning = callcenterUserService.isSceneAllocating(sceneId);
        if (isRunning) {
            throw new BusinessException(SCENE_PRE_ALLOCATING, "场景ID:" + sceneId);
        }
    }

    /**
     * 获取当前组信息
     */
    public Dept getGroupInfo(Long groupId) {
        Dept groupDept = groupId == null ? deptService.getUserGroupDeptInfo() : deptService.getGroupDept(groupId);
        if (groupDept == null) {
            throw new BusinessException(GROUP_NOT_EXIST);
        }
        return groupDept;
    }

    private void putBackToRetrieveQueue(String userId, Long groupId) {
        if (StringUtils.isBlank(userId)) {
            return;
        }
        CallcenterUserDto userDto = callcenterUserService.getCenterUserPhoneInfo(userId, null);
        if (Objects.isNull(userDto)) {
            return;
        }

        CallcenterSceneConfig sceneConfig = sceneService.getById(userDto.getSceneId());
        if (sceneConfig != null) {
            composeRetrieveQueue(Collections.singletonList(userDto), sceneConfig.getLevel(), groupId);
        }
    }

    @Override
    public void composeRetrieveQueue(List<CallcenterUserDto> users, int sceneLevel, Long groupId) {
        double baseScore = (sceneLevel % 4000) * BASE_SCOPE;
        String queueName = getRetrieveQueueName(groupId);
        for (CallcenterUserDto user : users) {
            String userId = user.getCurUserId();
            int randomIdx = RandomUtils.nextInt(0, BASE_SCOPE);
            jedisCluster.zadd(queueName, baseScore + randomIdx, userId);
        }
        jedisCluster.expire(queueName, 24 * 3600);
    }

    @Override
    public String getRetrieveUserId(Long groupId) {
        String queueName = getRetrieveQueueName(groupId);

        // Lua 脚本
        String luaScript =
                "local key = KEYS[1]; " +
                        "local minScore = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES')[2]; " +
                        "if minScore then " +
                        "    local userId = redis.call('ZRANGE', key, 0, 0)[1]; " +
                        "    redis.call('ZREM', key, userId); " +
                        "    return userId; " +
                        "else " +
                        "    return nil; " +
                        "end";

        // 执行 Lua 脚本
        Object result = jedisCluster.eval(luaScript, Collections.singletonList(queueName), Collections.emptyList());

        // 处理结果
        if (result instanceof String) {
            return (String) result;
        } else {
            return null;
        }
    }

    @Override
    public void clearRetrieveQueue(Long groupId) {
        String queueName = getRetrieveQueueName(groupId);
        jedisCluster.del(queueName);
    }

    @Override
    public boolean removeCustomerOfGroup(Long groupId, String customerId) {
        String queueName = getRetrieveQueueName(groupId);
        jedisCluster.zrem(queueName, customerId);
        return true;
    }

    @Override
    public void reComposeRetrieveQueue(Long groupId) {
        if (groupId == null) {
            throw new BusinessException(PARAM_NULL_ERROR, "groupId");
        }
        clearRetrieveQueue(groupId);
        Long offsetId = null;
        List<CallcenterUserDto> users;
        /*
        根据小组id遍历已经预分配的所有用户，重新分配到队列中
         */
        do {
            users = callcenterUserService.getUnAllocateUnLockUserList(groupId, offsetId, BATCH_RETRIEVE_SIZE, OwnAuthUtil.checkAndGetTenantId());
            if (CollectionUtils.isEmpty(users)) {
                break;
            }
            Map<Long, List<CallcenterUserDto>> userGroup = users.stream().collect(Collectors.groupingBy(CallcenterUserDto::getSceneId));
            for (Map.Entry<Long, List<CallcenterUserDto>> longListEntry : userGroup.entrySet()) {
                CallcenterSceneConfig sceneConfig = sceneService.getById(longListEntry.getKey());
                if (sceneConfig == null) {
                    log.error("\n 场景配置不存在, id：{}", longListEntry.getKey());
                    continue;
                }
                composeRetrieveQueue(longListEntry.getValue(), sceneConfig.getLevel(), groupId);
            }
            offsetId = users.get(users.size() - 1).getId();
        } while (CollectionUtils.isNotEmpty(users) && users.size() >= BATCH_RETRIEVE_SIZE);
    }

    @Override
    public CallcenterRetrieveToday getRetrieveToday() {
        CallcenterRetrieveToday retrieveToday = new CallcenterRetrieveToday();
        int hasRetrieveToday = plannerUserBindService.countPlannerRetrieveToday(OwnAuthUtil.getPlannerNo(), OwnAuthUtil.checkAndGetTenantId());
        retrieveToday.setHasRetrieveNums(hasRetrieveToday);
        CallcenterRetrieveRule publicRule = callcenterRetrieveRuleService.getTenantPublicRule();
        if (publicRule == null || publicRule.getIsLimited() == 0 || publicRule.getLimitByPerson() == -1) {
            retrieveToday.setHasUpLimit(false);
        } else {
            retrieveToday.setHasUpLimit(true);
            retrieveToday.setUpLimit(publicRule.getLimitByPerson());
        }
        return retrieveToday;
    }

    private String getRetrieveQueueName(Long groupId) {
        return "retrieveQueue_" + groupId;
    }

    private static final int BASE_SCOPE = 1000000;
    private static final int BATCH_RETRIEVE_SIZE = 200;


    /**
     * 校验并清理所有宜人小组的队列数据
     */
    @Override
    public void validateAndCleanupAllGroupQueues(String tenantId) {
        List<Dept> allGroups = deptService.getGroupDeptsByTenantId(tenantId);
        if (CollectionUtils.isEmpty(allGroups)) {
            log.info("validateAndCleanupAllGroupQueues: No groups found");
            return;
        }

        // 批量取所有队列的用户
        for (Dept group : allGroups) {
            String queueName = getRetrieveQueueName(group.getId());
            Set<String> userIds = jedisCluster.zrange(queueName, 0, -1);
            if (CollectionUtils.isEmpty(userIds)) {
                continue;
            }
            List<String> toRemove = new ArrayList<>();
            // 批量处理用户
            List<List<String>> partitionedUserIds = Lists.partition(new ArrayList<>(userIds), BATCH_RETRIEVE_SIZE);
            for (List<String> batchUserIds : partitionedUserIds) {
                // 批量获取用户信息
                List<CallcenterUserDto> userDtos = callcenterUserService.batchGetCenterUserInfo(batchUserIds, tenantId);
                Map<String, CallcenterUserDto> userMap = userDtos.stream()
                        .collect(Collectors.toMap(CallcenterUserDto::getCurUserId, user -> user, (v1, v2) -> v1));

                // 检查每个用户是否需要从队列中移除
                for (String userId : batchUserIds) {
                    if (shouldRemoveFromQueue(userMap.get(userId), group.getId())) {
                        toRemove.add(userId);
                    }
                }
            }

            // 批量从队列中移除用户
            if (!toRemove.isEmpty()) {
                String[] userIdsToRemove = toRemove.toArray(new String[0]);
                jedisCluster.zrem(queueName, userIdsToRemove);
                log.info("validateAndCleanupAllGroupQueues: Removed {} users from queue for group {}",
                        toRemove.size(), group.getId());
            }
        }
    }

    /**
     * 判断用户是否需要从队列中移除
     *
     * @param userDto 用户信息
     * @param groupId 小组ID
     * @return true if user should be removed from queue
     */
    private boolean shouldRemoveFromQueue(CallcenterUserDto userDto, Long groupId) {
        // 用户不存在
        if (userDto == null) {
            return true;
        }

        // 组别发生变更
        if (userDto.getGroupId() == null || !userDto.getGroupId().equals(groupId)) {
            return true;
        }

        // 用户已分配
        return userDto.getUserStatus() != null && userDto.getUserStatus().equals(UserStatusEnum.ALLOCATION.getCode());
    }


}
