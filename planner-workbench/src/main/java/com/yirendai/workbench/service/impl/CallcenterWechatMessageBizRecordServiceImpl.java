package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.consumer.bean.WechatMessageBizRecordEvent;
import com.yirendai.workbench.entity.CallcenterWechatMessageBizRecord;
import com.yirendai.workbench.enums.callcenter.WechatMessageBizTypeEnum;
import com.yirendai.workbench.mapper.CallcenterWechatMessageBizRecordMapper;
import com.yirendai.workbench.model.BusinessMatchContext;
import com.yirendai.workbench.service.FileShareLinkService;
import com.yirendai.workbench.service.callcenter.IWechatMessageBizRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 聊天信息业务记录表 服务实现类
 * </p>
 *
 * @since 2025-10-13
 */
@Slf4j
@Service
public class CallcenterWechatMessageBizRecordServiceImpl extends ServiceImpl<CallcenterWechatMessageBizRecordMapper, CallcenterWechatMessageBizRecord> implements IWechatMessageBizRecordService {

    @Resource
    private FileShareLinkService fileShareLinkService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleWechatMessageBizRecordEvent(WechatMessageBizRecordEvent event) {
        try {
            log.info("开始处理微信消息业务记录事件: plannerNo={}, userIds={}, source={}, messageId={}",
                    event.getPlannerNo(), event.getUserIds(), event.getSource(), event.getMessageId());

            // 参数校验
            if (event.getMessageId() == null || event.getSource() == null || CollectionUtils.isEmpty(event.getUserIds()) || event.getPlannerNo() == null) {
                log.warn("必要参数缺失，跳过处理");
                return;
            }

            // 检查消息是否已经处理过
            Integer count = this.lambdaQuery().eq(CallcenterWechatMessageBizRecord::getMessageId, event.getMessageId())
                    .eq(CallcenterWechatMessageBizRecord::getSource, event.getSource()).count();
            if (count > 0) {
                log.info("消息已处理过，跳过: messageId={}, source={}", event.getMessageId(), event.getSource());
                return;
            }

            // 构建业务匹配上下文
            BusinessMatchContext context = BusinessMatchContext.builder()
                    .content(event.getContent())
                    .source(event.getSource())
                    .service(FileShareLinkService.class, fileShareLinkService)
//                   .service(CallcenterUserService.class, callcenterUserService) 如果未来有其他业务类型，可以继续添加
                    .build();

            // 解析所有可能的业务类型
            Set<Integer> bizTypes = new HashSet<>();
            for (WechatMessageBizTypeEnum bizType : WechatMessageBizTypeEnum.values()) {
                if (bizType.matches(context)) {
                    bizTypes.add(bizType.getCode());
                    log.debug("消息匹配到业务类型: content={}, bizType={}", event.getContent(), bizType.getDescription());
                }
            }

            if (CollectionUtils.isEmpty(bizTypes)) {
                log.debug("未识别到任何业务类型，跳过保存: messageId={}", event.getMessageId());
                return;
            }

            // 为每个用户的每个业务类型创建记录
            List<CallcenterWechatMessageBizRecord> records = new ArrayList<>();
            for (String userId : event.getUserIds()) {
                for (Integer bizType : bizTypes) {
                    CallcenterWechatMessageBizRecord bizRecord = new CallcenterWechatMessageBizRecord();
                    bizRecord.setMessageId(event.getMessageId());
                    bizRecord.setSource(String.valueOf(event.getSource()));
                    bizRecord.setContent(event.getContent());
                    bizRecord.setPlannerNo(event.getPlannerNo());
                    bizRecord.setUserId(userId);
                    bizRecord.setBizType(bizType);
                    bizRecord.setTenantId(event.getTenantId());
                    bizRecord.setCreateTime(LocalDateTime.now());
                    bizRecord.setUpdateTime(LocalDateTime.now());
                    records.add(bizRecord);
                }
            }

            // 批量保存记录
            this.saveBatch(records);

            log.info("微信消息业务记录保存成功: messageId={}, userCount={}, bizTypeCount={}, totalRecords={}",
                    event.getMessageId(), event.getUserIds().size(), bizTypes.size(), records.size());

        } catch (Exception e) {
            log.error("处理微信消息业务记录事件失败: messageId={}, plannerNo={}, userIds={}",
                    event.getMessageId(), event.getPlannerNo(), event.getUserIds(), e);
            throw e;
        }
    }
}



