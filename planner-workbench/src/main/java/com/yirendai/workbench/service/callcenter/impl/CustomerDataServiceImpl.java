package com.yirendai.workbench.service.callcenter.impl;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.config.ThirdAccessProperty;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.entity.CallcenterSceneConfig;
import com.yirendai.workbench.entity.PlannerUserBind;
import com.yirendai.workbench.entity.callcenter.CallcenterUser;
import com.yirendai.workbench.entity.callcenter.CallcenterUserHis;
import com.yirendai.workbench.entity.callcenter.Reminder;
import com.yirendai.workbench.entity.callcenter.Strategy;
import com.yirendai.workbench.enums.RoleEnum;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.exception.CallCenterBusinessError;
import com.yirendai.workbench.mapper.callcenter.ReminderMapper;
import com.yirendai.workbench.mapper.callcenter.StrategyMapper;
import com.yirendai.workbench.model.CallcenterUserDto;
import com.yirendai.workbench.model.PlannerInfoDto;
import com.yirendai.workbench.service.*;
import com.yirendai.workbench.service.callcenter.*;
import com.yirendai.workbench.service.welfare.WelfareOrderService;
import com.yirendai.workbench.util.DataScopeUtil;
import com.yirendai.workbench.vo.req.UserKycDetailReq;
import com.yirendai.workbench.vo.req.UserRemarkDetailReq;
import com.yirendai.workbench.vo.req.callcenter.ChangePlannerReq;
import com.yirendai.workbench.vo.req.callcenter.OrderSearchReq;
import com.yirendai.workbench.vo.res.UserContactListRes;
import com.yirendai.workbench.vo.res.UserKycDetailRes;
import com.yirendai.workbench.vo.res.UserRemarkDetailRes;
import com.yirendai.workbench.vo.res.callcenter.*;
import com.yirendai.workbench.wrapper.*;
import com.yirendai.workbench.wrapper.dto.*;
import com.yirendai.workbench.wrapper.dto.life.OrderPageByCustomerRequest;
import com.yirendai.workbench.wrapper.dto.life.SimpleWelfareOrderInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerDataServiceImpl implements CustomerDataService {

    @Autowired
    private LenderCrmApiWrapper lenderCrmApiWrapper;

    @Autowired
    private YrbApiWrapper yrbApiWrapper;

    @Autowired
    private SupportApiWrapper supportApiWrapper;

    @Autowired
    private ActivityApiWrapper activityApiWrapper;

    @Autowired
    private ReminderMapper reminderMapper;

    @Autowired
    private StrategyMapper strategyMapper;

    @Autowired
    private CallcenterCallRecordService callcenterRecordService;
    @Autowired
    private ExtraInfoWrapper extraInfoWrapper;

    @Resource
    private IPlannerUserBindService plannerUserBindService;

    @Resource
    private DataScopeUtil dataScopeUtil;

    @Resource
    private ICallcenterUserLockService callCenterUserLockService;

    @Resource
    private CallcenterUserService callcenterUserService;

    @Resource
    private IBladeUserService iBladeUserService;

    @Resource
    private SceneService sceneService;

    @Resource
    private ThirdAccessProperty thirdAccessProperty;

    @Resource
    private CallcenterUserHisService callcenterUserHisService;

    @Resource
    private CallcenterProperty callcenterProperty;

    @Autowired
    private ICallcenterUserContactService callcenterUserContactService;

    @Autowired
    private ICallcenterUserRemarkService callcenterUserRemarkService;

    @Autowired
    private IUserKycService userKycService;

    @Autowired
    private CustomerAssetChartService customerAssetChartService;

    @Autowired
    private UserOrderService userOrderService;

    @Autowired
    private ReportFormsService reportFormsService;

    @Autowired
    private WelfareOrderService welfareOrderService;

    @Value("${lingxiang.projectId:166}")
    private String lingXiangProjectId;

    /**
     * 会员
     */
    @Value("${lingxiang.viewId.memeber:16272}")
    private String lingxiangViewIdMemeber;

    /**
     * D类
     */
    @Value("${lingxiang.viewId.dType:16273}")
    private String lingXiangViewIdDtype;

    /**
     * 长险
     */
    @Value("${lingxiang.viewId.Long:16274}")
    private String lingXiangViewIdLong;

    /**
     * 信美医疗&家财险
     */
    @Value("${lingxiang.viewId.xinMei:16275}")
    private String lingXiangViewIdXinMei;

    /**
     * 小金罐2.0
     */
    @Value("${lingxiang.viewId.xiaoJinGuan:16276}")
    private String lingxiangViewIdXiaoJinGuan;


    /**
     * 宜人渠道,查询积分
     */
    private static final String YIREN_CHANNEL = "1";

    /**
     * 信达渠道,查询宜享分,福卡,宜人币
     */
    private static final String XINDA_CHANNEL = "4";


    private static final SimpleDateFormat FORMATTER = new SimpleDateFormat("yyyy.MM.dd");


    private static final String ACTIVITY_DETAIL_FORMAT = "活动%s-%s-%s-%s";

    private static final Integer AI_ANALYSIS_CUSTOMER_DATA_COUNT = 100;


    private static final ThreadPoolExecutor customerWelfareThreadPoolExecutor = new ThreadPoolExecutor(10,
            10, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(50),
            new ThreadFactoryBuilder().setNamePrefix("customer-welfare-thread").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 客户福利信息
     *
     * @param userId 用户ID
     * @return
     */
    @Override
    public CustomerWelfare getCustomerWelfare(String userId) {
        try {
            log.info("开始获取客户福利信息, 客户ID:{}", userId);
            return buildCustomerWelfare(userId).get();
        } catch (Exception e) {
            log.error("获取客户福利信息失败, 客户ID:{}", userId);
            throw new BusinessException(CallCenterBusinessError.GET_CUSTOMER_WELFARE_FAIL, e);
        }
    }


    /**
     * 客户策略信息
     *
     * @param userId  用户ID
     * @param count   获取策略信息的数量
     * @param channel 渠道
     * @return
     */
    @Override
    public List<ReminderVo> getReminder(String userId, Integer count, String channel) {
        LambdaQueryWrapper<Reminder> reminderQueryWrapper = new LambdaQueryWrapper<>();
        reminderQueryWrapper.eq(Reminder::getUserId, userId)
                .eq(Reminder::getChannel, channel)
                .eq(Reminder::getStatus, 0)
                .orderByDesc(Reminder::getId);
        Page<Reminder> page = new Page<>(1, count);
        List<Reminder> latestReminders = reminderMapper.selectPage(page, reminderQueryWrapper).getRecords();
        List<Integer> typeList = latestReminders.stream().map(Reminder::getType).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(typeList)) {
            return Collections.emptyList();
        }
        //根据提醒类型查询strategy表
        LambdaQueryWrapper<Strategy> strategyQueryWrapper = new LambdaQueryWrapper<>();
        strategyQueryWrapper.in(Strategy::getId, typeList);
        List<Strategy> strategies = strategyMapper.selectList(strategyQueryWrapper);

        Map<Integer, Strategy> strategyMap = strategies.stream().collect(Collectors.toMap(Strategy::getId, Function.identity()));

        List<ReminderVo> reminderVos = new ArrayList<>(latestReminders.size());
        latestReminders.forEach(reminder -> {
            ReminderVo reminderVo = new ReminderVo();
            reminderVo.setId(reminder.getId());
            reminderVo.setUserId(reminder.getUserId());
            reminderVo.setPlannerId(reminder.getPlannerId());
            reminderVo.setType(reminder.getType());
            if (strategyMap.containsKey(reminder.getType())) {
                reminderVo.setRemark(strategyMap.get(reminder.getType()).getStrategyTitle());
            }
            reminderVo.setCreateTime(reminder.getCreateTime());
            reminderVo.setUpdateTime(reminder.getUpdateTime());
            reminderVos.add(reminderVo);
        });
        reminderVos.sort(Comparator.comparing(ReminderVo::getUpdateTime).reversed());
        return reminderVos;
    }

    /**
     * 客户活动报名信息
     *
     * @param request
     * @return
     */
    @Override
    public List<UserRegistryResponse> getActivityRegistry(UserRegistryRequest request) {
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getUserId())) {
            log.info("查询活动报名信息, 用户ID不能为空");
            return null;
        }
        return activityApiWrapper.getActivityRegistryInfo(request);
    }


    @Override
    public ContactRecord getContactRecord(String userId, Integer days) {
        List<CallcenterCallRecord> callRecordList = callcenterRecordService.getRecordByUserIdAndDuration(userId, days);
        //外呼总次数
        long callTimes = callRecordList.size();
        //接通总次数
        long answerTimes = callRecordList.stream().filter(record -> Objects.equals(2, record.getAnswered())).count();
        //通话时长
        long totalCallDuration = callRecordList.stream().map(CallcenterCallRecord::getCsBillsec).filter(Objects::nonNull).reduce(0L, Long::sum);
        //最近一次接通时间
        LocalDateTime latestSuccessCallTime = Optional.ofNullable(callcenterRecordService.getLatestCallRecord(userId, 2))
                .map(CallcenterCallRecord::getCsAnswerStamp).orElse(null);
        //最近一次拨打时间
        LocalDateTime latestCallTime = Optional.ofNullable(callcenterRecordService.getLatestCallRecord(userId, null))
                .map(CallcenterCallRecord::getStartStamp).orElse(null);

        return ContactRecord.builder().callTimes(callTimes).answerTimes(answerTimes)
                .sumLongtime(totalCallDuration).latestSuccessCallTime(latestSuccessCallTime)
                .latestCallTime(latestCallTime).build();
    }


    /**
     * 获取客户策略,活动报名信息,联系记录并整合
     *
     * @param userId 用户ID
     * @return
     */
    @Override
    public CustomerIntegrateVo getCustomerIntegrateVo(String userId) {
        CustomerIntegrateVo customerIntegrateVo = new CustomerIntegrateVo();
        //近5条客户策略信息
        List<ReminderVo> reminderVoList = this.getReminder(userId, 5, "yrcf");
        customerIntegrateVo.setReminderVoList(reminderVoList);

        //活动报名信息,近90天内
        String registerStartTime = LocalDateTime.now().minusDays(90).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String registerEndTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        UserRegistryRequest registryRequest = UserRegistryRequest.builder().userId(userId)
                .registerStartTime(registerStartTime).registerEndTime(registerEndTime).pageNum(1)
                .pageSize(5).build();
        List<UserRegistryResponse> registryList = this.getActivityRegistry(registryRequest);
        List<String> activityDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(registryList)) {
            customerIntegrateVo.setActivityDetailList(activityDetailList);
        } else {
            //按照活动报名时间倒叙排列,
            registryList = registryList.stream()
                    .sorted(Comparator.comparing(UserRegistryResponse::getRegisterTime).reversed()).collect(Collectors.toList());
            activityDetailList = registryList.stream().map(activity -> {
                //活动-[活动ID]-[活动名]-[在线/结束]-[报名时间]
                return String.format(ACTIVITY_DETAIL_FORMAT, activity.getId(), activity.getName(), activity.isOnline() ? "在线" : "结束", activity.getRegisterTime());
            }).collect(Collectors.toList());
            customerIntegrateVo.setActivityDetailList(activityDetailList);
        }

        //近30天内联系记录
        ContactRecord contactRecord = this.getContactRecord(userId, 30);
        customerIntegrateVo.setContactRecord(contactRecord);
        return customerIntegrateVo;
    }

    @Override
    public CustomerAndAssetResp getCustomerBaseInfoAndAsset(String userId) {
        CustomerAndAssetResp customerAndAssetResp = new CustomerAndAssetResp();
        extraInfoWrapper.initBaseAndAssetInfo(userId, customerAndAssetResp);
        return customerAndAssetResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changesPlanner(ChangePlannerReq req,Boolean checkAuthFlag) {
        if (StringUtils.isBlank(req.getUserId()) || StringUtils.isBlank(req.getPlannerId()) || Objects.isNull(req.getPlannerGroupId())) {
            BusinessError.PARAM_NULL_ERROR.throwBusinessException();
        }
        PlannerUserBind bind = plannerUserBindService.lambdaQuery().eq(PlannerUserBind::getUserId, req.getUserId())
                .eq(PlannerUserBind::getTenantId, thirdAccessProperty.getTenantId().get("yiren"))
                .last("limit 1").one();

        if (Objects.isNull(bind)) {
            BusinessError.USER_NO_ALLOCATION_PLANNER.throwBusinessException();
        }

        CallcenterSceneConfig sceneConfig = sceneService.getById(req.getSceneId());
        if (Objects.isNull(sceneConfig)) {
            BusinessError.SCENE_NOT_EXIST.throwBusinessException();
        }

        //校验数据权限
        if(checkAuthFlag) {
            this.checkDataAuth(bind.getPlannerId());
        }

        //校验用户是被锁定
        if (callCenterUserLockService.isLock(bind.getUserId())) {
            BusinessError.USER_EXCHANGE_PANNER_LOCKED.throwBusinessException();
        }

        if (req.getPlannerId().equals(bind.getPlannerId())) {
            BusinessError.SAME_PLANNER.throwBusinessException();
        }

        //获取新理财师数据
        PlannerInfoDto plannerInfoDto = iBladeUserService.getPlannerInfoByPlannerId(req.getPlannerId());
        if (Objects.isNull(plannerInfoDto)) {
            BusinessError.PLANNER_NOT_EXIST.throwBusinessException();
        }


        //变更理财师
        plannerUserBindService.changesPlanner(bind,plannerInfoDto);

        //记录用户历史表，维护旧理财师出库信息
        saveOldPlannerOutBoundTime(bind);

        //更新用户数据
        callcenterUserService.allocationPlannerUpdateUser(bind.getUserId(), req.getPlannerGroupId(),req.getSceneId(), bind.getTenantId());

        return true;
    }

    @Override
    public Boolean changesFlowUpStatus(String userId) {
        if(org.springframework.util.StringUtils.hasText(userId)){
            return this.callcenterUserService.changesFollowUpStatus(userId);
        }
        return false;
    }

    @Override
    public CustomerBasicInfoVo getCustomerBasicInfo(String userId, String tenantId) {
        //校验用户信息是否可获取
        if (!checkUserInfoAccess(userId, tenantId)) {
            return null;
        }
        //客户策略, 联系记录, 活动报名信息
        CustomerIntegrateVo customerIntegrateVo = getCustomerIntegrateVo(userId);

        //客户福利
        CustomerWelfare customerWelfare = getCustomerWelfare(userId);

        //客户基本信息及在持资产查询
        CustomerAndAssetResp customerBaseInfoAndAsset = getCustomerBaseInfoAndAsset(userId);

        //账户信息
        List<UserContactListRes> userContactListResList = callcenterUserContactService.list(userId, tenantId);

        //客户备注
        UserRemarkDetailReq  userRemarkDetailReq = new UserRemarkDetailReq();
        userRemarkDetailReq.setUserId(userId);
        UserRemarkDetailRes userRemarkDetailRes = callcenterUserRemarkService.detail(userRemarkDetailReq);

        //客户KYC
        UserKycDetailReq userKycDetailReq = new UserKycDetailReq();
        userKycDetailReq.setUserId(Long.valueOf(userId));
        UserKycDetailRes userKycDetailRes = userKycService.detail(userKycDetailReq);

        return CustomerBasicInfoVo.builder().reminderVoList(customerIntegrateVo.getReminderVoList())
                .contactRecord(customerIntegrateVo.getContactRecord())
                .activityDetailList(customerIntegrateVo.getActivityDetailList())
                .customerWelfare(customerWelfare)
                .customerAndAssetResp(customerBaseInfoAndAsset)
                .userContactListResList(userContactListResList)
                .userRemarkDetailRes(userRemarkDetailRes)
                .userKycDetailRes(userKycDetailRes)
                .build();
    }

    @Override
    public CustomerBusinessInfoVo getCustomerBusinessInfo(String userId, String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        //校验用户信息是否可获取
        if (!checkUserInfoAccess(userId, tenantId)) {
            return null;
        }
        //资产信息
        CustomerAssetChartResp customerAssetChartResp = customerAssetChartService.getUserAssetChart(userId);

        OrderSearchReq orderSearchReq = new OrderSearchReq();
        orderSearchReq.setCustomerUid(userId);
        orderSearchReq.setStartTime(startTime);
        orderSearchReq.setEndTime(endTime);
        orderSearchReq.setPageSize(AI_ANALYSIS_CUSTOMER_DATA_COUNT);
        //小固订单列表
        List<XgOrderInfo> xgOrderInfoList = userOrderService.getXgOrderList(orderSearchReq).getRecords();
        //大固订单列表
        List<DgOrderInfo> dgOrderInfoList = userOrderService.getDgOrderList(orderSearchReq).getRecords();
        //奖励金
        List<RewardCashflowInfo> rewardCashflowInfoList = userOrderService.getPrizeOrderList(orderSearchReq).getRecords();
        //宜人币
        List<YrbCashflowInfo> yrbCashflowInfoList = userOrderService.getYrbOrderList(orderSearchReq).getRecords();
        //福卡用户
        List<FcardInfoRes> fcardInfoResList = userOrderService.getFcardList(orderSearchReq).getRecords();
        //福卡加息记录
        List<FcardRecordInfoRes> fcardRecordInfoResList = userOrderService.getFcardRecordList(orderSearchReq).getRecords();
        //一起赚
        YqzInvestPageRes yqzInvestPageRes = userOrderService.getYqzInfo(orderSearchReq);

        LingXiangQueryDataRequest lingXiangQueryDataRequest = new LingXiangQueryDataRequest();
        lingXiangQueryDataRequest.setProjectId(lingXiangProjectId);
        lingXiangQueryDataRequest.setPageSize(10);
        lingXiangQueryDataRequest.setFiltersValues(Collections.singletonList(userId));
        lingXiangQueryDataRequest.setPageNo(1);
        //D类
        lingXiangQueryDataRequest.setViewId(lingXiangViewIdDtype);
        List<Object> dTypeRecordList = reportFormsService.queryLingXiangData_v2(lingXiangQueryDataRequest).getRecords();
        //长险
        lingXiangQueryDataRequest.setViewId(lingXiangViewIdLong);
        List<Object> longRecordList = reportFormsService.queryLingXiangData_v2(lingXiangQueryDataRequest).getRecords();
        //信美医疗&家财险
        lingXiangQueryDataRequest.setViewId(lingXiangViewIdXinMei);
        List<Object> xinMeiRecordList = reportFormsService.queryLingXiangData_v2(lingXiangQueryDataRequest).getRecords();
        //小金罐2.0
        lingXiangQueryDataRequest.setViewId(lingxiangViewIdXiaoJinGuan);
        List<Object> xiaoJinGuanRecordList = reportFormsService.queryLingXiangData_v2(lingXiangQueryDataRequest).getRecords();
        //会员
        lingXiangQueryDataRequest.setViewId(lingxiangViewIdMemeber);
        List<Object> memberRecordList = reportFormsService.queryLingXiangData_v2(lingXiangQueryDataRequest).getRecords();

        //福利平台
        OrderPageByCustomerRequest orderPageByCustomerRequest = new OrderPageByCustomerRequest();
        orderPageByCustomerRequest.setUserId(Long.parseLong(userId));
        orderPageByCustomerRequest.setPageSize(AI_ANALYSIS_CUSTOMER_DATA_COUNT);
        orderPageByCustomerRequest.setOrderCreateFrom(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderPageByCustomerRequest.setOrderCreateTo(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        //生活商城订单
        orderPageByCustomerRequest.setStoreName("YIREN_PLUS");
        List<SimpleWelfareOrderInfoVo> lifeMallOrderList = welfareOrderService.getWelfareOrderPage(orderPageByCustomerRequest).getRecords();
        //逛逛商城订单
        orderPageByCustomerRequest.setStoreName("XINDA");
        List<SimpleWelfareOrderInfoVo> guangGuangMallOrderList = welfareOrderService.getWelfareOrderPage(orderPageByCustomerRequest).getRecords();

        return CustomerBusinessInfoVo.builder().customerAssetChartResp(customerAssetChartResp)
                .xgOrderInfoList(xgOrderInfoList)
                .dgOrderInfoList(dgOrderInfoList)
                .rewardCashflowInfoList(rewardCashflowInfoList)
                .yrbCashflowInfoList(yrbCashflowInfoList)
                .fcardInfoResList(fcardInfoResList)
                .fcardRecordInfoResList(fcardRecordInfoResList)
                .yqzInvestPageRes(yqzInvestPageRes)
                .dTypeRecordList(dTypeRecordList)
                .longRecordList(longRecordList)
                .xinMeiRecordList(xinMeiRecordList)
                .xiaoJinGuanRecordList(xiaoJinGuanRecordList)
                .memberRecordList(memberRecordList)
                .lifeMallOrderList(lifeMallOrderList)
                .guangGuangMallOrderList(guangGuangMallOrderList)
                .build();
    }

    /**
     * 记录历史表，维护旧理财师的的出库信息
     *
     */
    private void saveOldPlannerOutBoundTime(PlannerUserBind bind) {
        CallcenterUser callCenterUser = callcenterUserService.lambdaQuery().eq(CallcenterUser::getUserId, bind.getUserId()).or().eq(CallcenterUser::getVirtualUserId, bind.getUserId())
                .eq(StringUtils.isNotBlank(bind.getTenantId()), CallcenterUser::getTenantId, bind.getTenantId())
                .orderByDesc(CallcenterUser::getId).last("limit 1").one();
        if(Objects.isNull(callCenterUser)) {
            return;
        }
        CallcenterUserHis callcenterUserHis= new CallcenterUserHis();
        BeanUtils.copyProperties(callCenterUser,callcenterUserHis);
        callcenterUserHis.setInboundTime(callCenterUser.getCreateTime());
        callcenterUserHis.setOutboundTime(LocalDateTime.now());
        callcenterUserHis.setPlannerId(bind.getPlannerId());
        callcenterUserHis.setBindTime(bind.getBindTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        callcenterUserHis.setCallcenterUserId(callCenterUser.getId());
        callcenterUserHisService.save(callcenterUserHis);
    }

    /**
     * 校验数据权限
     */
    private void checkDataAuth(String plannerId) {
        List<String> userMenuDataScope = dataScopeUtil.getUserMenuDataScope(callcenterProperty.getYumengCustomerListMenuCode(), TenantServiceHolder.SCOPE_PLANNER_FIELD);
        if(CollectionUtils.isEmpty(userMenuDataScope)) {
            return;
        }
        if (!userMenuDataScope.contains(plannerId)) {
            BusinessError.EXCHANGE_PANNER_NO_AUTH.throwBusinessException();
        }
    }


    private boolean checkUserInfoAccess(String userId, String tenantId) {
        CallcenterUserDto callcenterUserDto = callcenterUserService.getCenterUserByUserId(userId, tenantId);
        if (callcenterUserDto == null) {
            log.warn("未根据用户ID和租户获取到用户信息, userId:{}, tenantId:{}", userId, tenantId);
            return false;
        }
        if (Boolean.TRUE.equals(callcenterUserDto.getVirtualUser())) {
            log.warn("该用户为虚拟用户, 无法获取用户基本信息, userId:{}, tenantId:{}, callcenterUserDto:{}", userId, tenantId, JSONObject.toJSONString(callcenterUserDto));
            return false;
        }
        if (!StringUtils.equalsIgnoreCase(callcenterUserDto.getTenantId(), thirdAccessProperty.getTenantId().get("yiren"))) {
            log.warn("该用户非宜人渠道用户, 无法获取用户基本信息, userId:{}, tenantId:{}, callcenterUserDto:{}",  userId, tenantId, JSONObject.toJSONString(callcenterUserDto));
            return false;
        }
        return true;
    }




    /**
     * 异步获取用户福利信息并组装
     *
     * @param userId 用户Id
     * @return
     */
    private CompletableFuture<CustomerWelfare> buildCustomerWelfare(String userId) {
        CompletableFuture<BasicMemberInfoResponse> memberInfo = getMemberInfoAsync(userId);
        CompletableFuture<YrbExpiryInfoResponse> yrbInfo = getYrbExpiryInfoAsync(userId, XINDA_CHANNEL);
        CompletableFuture<FCardListWithIncomeResponse> fCardInfo = getFCardInfoAsync(userId, XINDA_CHANNEL);
        CompletableFuture<PointBalanceInfoResponse> pointInfo = getPointsInfoAsync(userId, YIREN_CHANNEL);
        CompletableFuture<PointBalanceInfoResponse> yxfInfo = getPointsInfoAsync(userId, XINDA_CHANNEL);
        CompletableFuture<AIMemberInfoResponse> aiMemberInfo = getAIMemberInfoAsync(userId);

        return CompletableFuture.allOf(memberInfo, yrbInfo, fCardInfo, pointInfo, yxfInfo)
                .thenApply(v -> {
                    try {
                        CustomerWelfare customerWelfare = new CustomerWelfare();
                        //会员信息
                        if (memberInfo.get() != null) {
                            customerWelfare.setMemberShipLevel(memberInfo.get().getLevelName());
                            Date endDate = memberInfo.get().getEndDate();
                            Optional.ofNullable(endDate)
                                    .ifPresent(date -> customerWelfare.setMemberLevelEndDate(FORMATTER.format(date)));
                        }
                        //积分信息
                        if (pointInfo.get() != null) {
                            customerWelfare.setPointCurrent(pointInfo.get().getTotalPoint());
                            customerWelfare.setYearExpiryPoint(pointInfo.get().getExpirePoints());
                            customerWelfare.setPointExpireDay(pointInfo.get().getExpiredDay());
                        }
                        //宜享分信息
                        if (yxfInfo.get() != null) {
                            customerWelfare.setYxfCurrent(yxfInfo.get().getTotalPoint());
                            customerWelfare.setYearExpiryYxf(yxfInfo.get().getExpirePoints());
                            customerWelfare.setYxfExpireDay(yxfInfo.get().getExpiredDay());
                        }
                        //宜人币信息
                        if (yrbInfo.get() != null) {
                            customerWelfare.setYrbCurrent(yrbInfo.get().getTotalAmount().intValue());
                            customerWelfare.setExpiryYrb(yrbInfo.get().getEstimatedExpiryAmount().intValue());
                            Date estimatedExpiryDate = yrbInfo.get().getEstimatedExpiryDate();
                            Optional.ofNullable(estimatedExpiryDate)
                                    .ifPresent(date -> customerWelfare.setYrbExpireDay(FORMATTER.format(date)));
                        }
                        //福卡信息
                        if (fCardInfo.get() != null) {
                            List<FCardDetailVO> fcardList = fCardInfo.get().getFcardList();
                            if (CollectionUtils.isEmpty(fcardList)) {
                                customerWelfare.setFcardNum(0);
                                customerWelfare.setExpiryFCard(null);
                                customerWelfare.setFCardExpireDay(null);
                            } else {
                                //按照到期时间排序
                                fcardList.sort(Comparator.comparing(fCardDetailVO -> {
                                    try {
                                        return FORMATTER.parse(fCardDetailVO.getEndTimeStr());
                                    } catch (ParseException e) {
                                        log.warn("福卡信息排序失败, e");
                                        throw new BusinessException(CallCenterBusinessError.GET_CUSTOMER_WELFARE_FAIL, e);
                                    }
                                }));
                                customerWelfare.setFcardNum(fcardList.size());
                                customerWelfare.setExpiryFCard(fcardList.get(0).getCardName());
                                customerWelfare.setFCardExpireDay(fcardList.get(0).getEndTimeStr());
                            }
                        }
                        //AI会员信息
                        if (aiMemberInfo.get() != null) {
                            customerWelfare.setPayState(aiMemberInfo.get().getPayState());
                            customerWelfare.setUnUseNum(aiMemberInfo.get().getUnUseNum());
                            customerWelfare.setEndTimeReminder(aiMemberInfo.get().getEndTimeReminder());
                        }
                        return customerWelfare;
                    } catch (Exception e) {
                        throw new BusinessException(CallCenterBusinessError.GET_CUSTOMER_WELFARE_FAIL, e);
                    }
                });
    }


    private CompletableFuture<BasicMemberInfoResponse> getMemberInfoAsync(String userId) {
        return CompletableFuture.supplyAsync(() -> lenderCrmApiWrapper.getMemberData(userId), customerWelfareThreadPoolExecutor);
    }

    private CompletableFuture<YrbExpiryInfoResponse> getYrbExpiryInfoAsync(String userId, String channel) {
        return CompletableFuture.supplyAsync(() -> yrbApiWrapper.getYrbExpiryInfo(userId, channel), customerWelfareThreadPoolExecutor);
    }


    private CompletableFuture<FCardListWithIncomeResponse> getFCardInfoAsync(String userId, String channel) {
        return CompletableFuture.supplyAsync(() -> supportApiWrapper.fcardList(userId, channel), customerWelfareThreadPoolExecutor);
    }

    private CompletableFuture<PointBalanceInfoResponse> getPointsInfoAsync(String userId, String channel) {
        return CompletableFuture.supplyAsync(() -> supportApiWrapper.queryPointBalance(userId, channel), customerWelfareThreadPoolExecutor);
    }

    private CompletableFuture<AIMemberInfoResponse> getAIMemberInfoAsync(String userId) {
        return CompletableFuture.supplyAsync(() -> activityApiWrapper.getAIMemberInfo(userId), customerWelfareThreadPoolExecutor);
    }


}
