package com.yirendai.workbench.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.CallcenterSaleProcessNode;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeAddReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeDelReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeDetailReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeEdgeUpdateReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeStartReq;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessNodeAddRes;

import java.util.List;

/**
 * <p>
 * 销售流程节点基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
public interface ICallcenterSaleProcessNodeService extends IService<CallcenterSaleProcessNode> {

    CallcenterSaleProcessNodeAddRes add(CallcenterSaleProcessNodeAddReq req);

    CallcenterSaleProcessNodeAddRes edit(CallcenterSaleProcessNodeAddReq req);

    Boolean del(CallcenterSaleProcessNodeDelReq req);

    CallcenterSaleProcessNodeAddRes detail(CallcenterSaleProcessNodeDetailReq req);

    Boolean updateEdge(CallcenterSaleProcessNodeEdgeUpdateReq req);

    Boolean updateStart(CallcenterSaleProcessNodeStartReq req);

    boolean copySaleProcessData(Long originalSaleProcessId, Long newSaleProcessId, String newVersionNo);

    void validateNode(Long saleProcessId);
}
