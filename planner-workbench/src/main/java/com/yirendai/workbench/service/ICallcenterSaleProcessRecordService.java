package com.yirendai.workbench.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.CallcenterSaleProcessNode;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeRecord;
import com.yirendai.workbench.entity.CallcenterSaleProcessRecord;
import com.yirendai.workbench.entity.CallcenterSalesProcess;
import com.yirendai.workbench.job.vo.InitProcessResult;
import com.yirendai.workbench.job.vo.ProcessNodeResult;
import com.yirendai.workbench.readonly.entity.PlannerUserBindReadonly;
import com.yirendai.workbench.vo.req.ManualAdvanceConditionReq;
import com.yirendai.workbench.vo.req.callcenter.CallcenterCustomerSalesProcessListReq;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerNodeInfoResp;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerSaleProcessInfoResp;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerSaleProcessRecordResp;

/**
 * <p>
 * 销售流程进度记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
public interface ICallcenterSaleProcessRecordService extends IService<CallcenterSaleProcessRecord> {

    void saleProcess(CallcenterSalesProcess callcenterSalesProcess, PlannerUserBindReadonly plannerUserBind);

    boolean manualFinishProcess(Long processId, String userId, Integer finishStatus, String manualFinishReason);

    boolean batchManualAdvanceCondition(Long processId, String userId,
            List<ManualAdvanceConditionReq.ConditionItemReq> conditionItems);

    InitProcessResult initProcessAndNodeRecord(
            CallcenterSalesProcess callcenterSalesProcess,
            PlannerUserBindReadonly plannerUserBind);

    ProcessNodeResult processNodeInTransaction(
            CallcenterSalesProcess callcenterSalesProcess,
            CallcenterSaleProcessNode callcenterSaleProcessNode,
            CallcenterSaleProcessRecord callcenterSaleProcessRecord,
            CallcenterSaleProcessNodeRecord callcenterSaleProcessNodeRecord,
            PlannerUserBindReadonly plannerUserBind);
}
