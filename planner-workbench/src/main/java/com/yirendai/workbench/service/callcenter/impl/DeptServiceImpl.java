package com.yirendai.workbench.service.callcenter.impl;

import com.yirendai.app.fortune.common.util.JsonUtils;
import com.yirendai.workbench.entity.BladeDept;
import com.yirendai.workbench.service.IBladeDeptService;
import com.yirendai.workbench.service.callcenter.CacheService;
import com.yirendai.workbench.service.callcenter.DeptService;
import com.yirendai.workbench.util.OwnAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springblade.system.entity.Dept;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.yirendai.workbench.enums.callcenter.DeptCategoryEnum.GROUP;

@Slf4j
@Service
public class DeptServiceImpl implements DeptService {
    @Resource
    private CacheService cacheService;
    @Autowired
    private IBladeDeptService bladeDeptService;

    @Override
    public Dept getUserGroupDeptInfo() {
        BladeDept dbBladeDept = bladeDeptService.getPlannerGroupBelong(OwnAuthUtil.getTenantId(), OwnAuthUtil.getDeptId());
        if(Objects.isNull(dbBladeDept)) {
            return null;
        }
        Dept dept = new Dept();
        BeanUtils.copyProperties(dbBladeDept, dept);
        return dept;
    }

    @Override
    public Dept getGroupDept(Long groupId) {
        Dept dept = cacheService.getDept(groupId);
        log.info(" dept info of  deptId:{} is :{}",  groupId, dept == null ? "null" : JsonUtils.beanToJson(dept));
        if (null != dept && dept.getIsDeleted() == 0 && GROUP.getCode() ==dept.getDeptCategory()) {
            return dept;
        }
        return null;
    }

    @Override
    public List<Dept> getGroupDeptsByTenantId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            log.warn("getAllGroupDepts: tenantId is empty");
            return Collections.emptyList();
        }

        // 使用bladeDeptService获取租户下所有部门
        List<BladeDept> bladeDepts = bladeDeptService.getTenantDept(tenantId);
        if (CollectionUtils.isEmpty(bladeDepts)) {
            return Collections.emptyList();
        }

        // 转换为Dept对象列表
        return bladeDepts.stream()
                .map(bladeDept -> {
                    Dept dept = new Dept();
                    BeanUtils.copyProperties(bladeDept, dept);
                    return dept;
                })
                .collect(Collectors.toList());
    }
}
