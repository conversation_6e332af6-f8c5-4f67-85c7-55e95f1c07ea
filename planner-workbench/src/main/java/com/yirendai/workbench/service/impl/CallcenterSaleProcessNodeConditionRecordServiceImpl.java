package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterSaleProcessNodeConditionRecord;
import com.yirendai.workbench.mapper.CallcenterSaleProcessNodeConditionRecordMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeConditionRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售流程节点条件进度记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
@Service
public class CallcenterSaleProcessNodeConditionRecordServiceImpl extends ServiceImpl<CallcenterSaleProcessNodeConditionRecordMapper, CallcenterSaleProcessNodeConditionRecord> implements ICallcenterSaleProcessNodeConditionRecordService {

}
