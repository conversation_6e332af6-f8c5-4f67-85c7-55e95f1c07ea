package com.yirendai.workbench.service.callcenter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.CallcenterSalesProcess;
import com.yirendai.workbench.vo.req.callcenter.CallcenterSalesProcessListReq;
import com.yirendai.workbench.vo.req.callcenter.CallcenterUserSalesProcessReq;
import com.yirendai.workbench.vo.res.CallCenterSalesProcessRecordExportRes;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessRecordRes;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @createDate 2025-09-30 16:33:09
*/
public interface CallcenterSalesProcessService extends IService<CallcenterSalesProcess> {

    CallcenterSalesProcess saveProcessService(CallcenterSalesProcess salesProcess,String copyId);

    Page<CallcenterSalesProcess> pageList(CallcenterSalesProcessListReq salesProcessListReq);

    List<CallCenterSalesProcessRecordExportRes> exportList(CallcenterUserSalesProcessReq req);

    Boolean exportListTask(String processNo);

    Page<LinkedHashMap<String, Object>> exportDataList(Map<String, Object> request);

    CallcenterSalesProcess getBeanById(Integer id);

    List<CallcenterSalesProcess> getListByProcessNo(String processNo);

    Boolean updateStatus(Integer id,Integer updateType);

    Page<CallcenterSaleProcessRecordRes> getUserSaleProcessList(CallcenterUserSalesProcessReq req);

    boolean updateNodeTextById(Long id,String nodeText);

    Long getProcessIdForeditButton(Long id);

}
