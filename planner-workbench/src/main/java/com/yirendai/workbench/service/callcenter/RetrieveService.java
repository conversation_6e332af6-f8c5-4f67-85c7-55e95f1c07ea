package com.yirendai.workbench.service.callcenter;

import com.yirendai.workbench.entity.CallcenterRetrieveToday;
import com.yirendai.workbench.entity.callcenter.CallcenterUser;
import com.yirendai.workbench.model.CallcenterUserDto;
import com.yirendai.workbench.vo.res.callcenter.RetrieveCustomerRes;

import java.util.List;

public interface RetrieveService {
    /**
     * 领取客户
     */
    RetrieveCustomerRes retrieveCustomer(Long groupId);

    CallcenterRetrieveToday getRetrieveToday();

    /**
     * 构建领取队列
     * @param users
     * @param sceneLevel
     * @param groupId
     */
    void composeRetrieveQueue(List<CallcenterUserDto> users, int sceneLevel, Long groupId);

    /**
     * 从领取队列，获取一个用户id
     * @param groupId
     * @return
     */
    String getRetrieveUserId(Long groupId);

    void clearRetrieveQueue(Long groupId);

    /**
     * 从领取队列中移除一个用户
     * @param groupId
     * @param customerId
     * @return
     */
    boolean removeCustomerOfGroup(Long groupId, String customerId);

    /**
     * 重新构建小组领取队列
     * @param groupId
     */
    void reComposeRetrieveQueue(Long groupId);
    /**
     * 校验并清理所有小组领取队列中的无效用户
     * @param tenantId
     */
    void validateAndCleanupAllGroupQueues(String tenantId);
}
