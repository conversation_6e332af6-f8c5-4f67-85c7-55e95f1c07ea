package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode;
import com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessNodeReadonlyMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeReadonlyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 销售流程节点基础信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2025-10-09
 */
@Service
@Slf4j
public class CallcenterSaleProcessNodeReadonlyServiceImpl
        extends ServiceImpl<CallcenterSaleProcessNodeReadonlyMapper, CallcenterSaleProcessNode>
        implements ICallcenterSaleProcessNodeReadonlyService {

    @Resource
    private CallcenterSaleProcessNodeReadonlyMapper saleProcessNodeReadonlyMapper;

    @Override
    public List<CallcenterSaleProcessNode> getListByIdsReadonly(List<String> ids){
        return saleProcessNodeReadonlyMapper.selectListByIds(ids);
    }

    @Override
    public CallcenterSaleProcessNode selectBeanByIdReadonly(String nodeId){
        return saleProcessNodeReadonlyMapper.selectBeanById(nodeId);
    }
}
