package com.yirendai.workbench.service.callcenter.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.config.CallServiceProperty;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.entity.CallcenterSalesProcess;
import com.yirendai.workbench.enums.callcenter.SalesProcessStatusEnum;
import com.yirendai.workbench.enums.callcenter.TaskTypeEnum;
import com.yirendai.workbench.mapper.CallcenterSalesProcessMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeReadonlyService;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeService;
import com.yirendai.workbench.service.ICallcenterSaleProcessRecordReadonlyService;
import com.yirendai.workbench.service.callcenter.CallcenterSalesProcessService;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.util.BeanToMapUtil;
import com.yirendai.workbench.util.ExcelAnnotationUtil;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.callcenter.CallcenterSalesProcessListReq;
import com.yirendai.workbench.vo.req.callcenter.CallcenterUserSalesProcessReq;
import com.yirendai.workbench.vo.res.CallCenterSalesProcessRecordExportRes;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessRecordRes;
import com.yirendai.workbench.wrapper.TaskScheduleWrapper;
import com.yirendai.workbench.wrapper.dto.CallCenterTask;
import com.yirendai.workbench.wrapper.dto.CallCenterTaskCreateDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.yirendai.workbench.constant.TaskScheduleConstants.GENERAL_EXCEL_EXPORT_TASK;

/**
* <AUTHOR>
* @createDate 2025-09-30 16:33:09
*/
@Service
@Slf4j
public class CallcenterSalesProcessServiceImpl extends ServiceImpl<CallcenterSalesProcessMapper, CallcenterSalesProcess> implements CallcenterSalesProcessService{

    @Resource
    TenantServiceHolder tenantServiceHolder;

    @Autowired
    private CallcenterProperty callcenterProperty;

    @Resource
    private ICallcenterSaleProcessRecordReadonlyService saleProcessRecordReadonlyService;
    @Resource
    private ICallcenterSaleProcessNodeReadonlyService saleProcessNodeReadonlyService;

    @Resource
    ICallcenterSaleProcessNodeService iCallcenterSaleProcessNodeService;

    @Resource
    private CallServiceProperty callServiceProperty;
    @Resource
    private TaskScheduleWrapper taskScheduleWrapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CallcenterSalesProcess saveProcessService(CallcenterSalesProcess salesProcess,String copyId){
        boolean flag = false;
        LocalDateTime now = LocalDateTime.now();
        salesProcess.setUpdateTime(now);
        CallcenterSalesProcess oldBean = null;
        if(null != salesProcess.getId()){
            log.info(" CallcenterSalesProcessServiceImpl.saveProcessService, salesProcess.id:{}",salesProcess.getId());
            oldBean = this.getById(salesProcess.getId());
        }
        if(null != salesProcess.getId() && SalesProcessStatusEnum.UNPUBLISH.getCode().equals(oldBean.getProcessStatus())){
            // id 不为空时 且版本状态为未发布 则不生成新版本，直接更新记录内容
            flag = this.updateById(salesProcess);
        }else {
            String processNo = null;
            if(null == salesProcess.getId()){
                // 生成新的流程编号 by 时间戳
                processNo = "SF-"+System.currentTimeMillis()/1000;
            }else {
                processNo = oldBean.getProcessNo();
                // 修改同流程编号下的历史版本的默认展示状态为0
                LambdaUpdateWrapper<CallcenterSalesProcess> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(CallcenterSalesProcess::getDefaultProcess, 0)
                        .eq(CallcenterSalesProcess::getProcessNo, processNo);
                this.update(updateWrapper);
            }
            // 生成新的版本号,新增版本记录
            String version = getProcessVersion(processNo);
            // nodeText字段前端不会传入值，需要自己查旧数据处理一下
            if(Objects.nonNull(oldBean)){
                salesProcess.setNodeText(oldBean.getNodeText());
            }else if(Objects.nonNull(copyId)){
                CallcenterSalesProcess orgBean = this.getById(copyId);
                salesProcess.setNodeText(Objects.isNull(orgBean) ? "" : orgBean.getNodeText());
            }
            salesProcess.setVersionNo(version);
            salesProcess.setProcessNo(processNo);
            salesProcess.setDefaultProcess(1);
            salesProcess.setCreateTime(now);
            salesProcess.setCreateUser(OwnAuthUtil.getPlannerNo());
            salesProcess.setUpdateUser(OwnAuthUtil.getPlannerNo());
            salesProcess.setProcessStatus(SalesProcessStatusEnum.UNPUBLISH.getCode());
            salesProcess.setId(null);
            log.info(" CallcenterSalesProcessServiceImpl.bean:{}",salesProcess);
            flag = this.save(salesProcess);
            // 生成流程节点记录
            if (Objects.nonNull(copyId) || Objects.nonNull(oldBean)) {
                Long oldId = Objects.nonNull(copyId) ? Long.valueOf(copyId) : oldBean.getId();
                log.info(" CallcenterSalesProcessServiceImpl.saveProcessService, oldId:{},salesProcess.id:{}",oldId,salesProcess.getId());
                iCallcenterSaleProcessNodeService.copySaleProcessData(oldId, salesProcess.getId(), version);
            }
        }
        return salesProcess;
    }


    private String getProcessVersion(String processNo){
        String version = "V001";
        LambdaQueryWrapper<CallcenterSalesProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallcenterSalesProcess::getProcessNo, processNo);
        queryWrapper.orderByDesc(CallcenterSalesProcess::getCreateTime);
        queryWrapper.last("limit 1");
        CallcenterSalesProcess oldVersionBean = this.getOne(queryWrapper);
        if(null != oldVersionBean){
            String oldVersion = oldVersionBean.getVersionNo();
            int versionNumber = NumberUtil.parseInt(StrUtil.subSuf(oldVersion, 1));
            version = String.format("V%03d", versionNumber + 1);
        }
        return version;
    }

    @Override
    public Page<CallcenterSalesProcess> pageList(CallcenterSalesProcessListReq salesProcessListReq){
        Page<CallcenterSalesProcess> page = new Page<>(salesProcessListReq.getPageNo(),salesProcessListReq.getPageSize());
        LambdaQueryWrapper<CallcenterSalesProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallcenterSalesProcess::getDefaultProcess,1)
            .like(StringUtils.isNotBlank(salesProcessListReq.getProcessName()), CallcenterSalesProcess::getProcessName, salesProcessListReq.getProcessName())
            .eq(salesProcessListReq.getProductType() != null,CallcenterSalesProcess::getProductType,salesProcessListReq.getProductType())
            .orderByDesc(CallcenterSalesProcess::getId);
        Page<CallcenterSalesProcess> pageList = this.page(page,queryWrapper);
        return pageList;
    }

    @Override
    public CallcenterSalesProcess getBeanById(Integer id){
        if(null == id){
            return null;
        }
        CallcenterSalesProcess bean = this.getById(id);
        return bean;
    }

    @Override
    public List<CallcenterSalesProcess> getListByProcessNo(String processNo){
        LambdaQueryWrapper<CallcenterSalesProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CallcenterSalesProcess::getId,
                CallcenterSalesProcess::getVersionNo,
                CallcenterSalesProcess::getUpdateUser,
                CallcenterSalesProcess::getUpdateTime);
        queryWrapper.eq(CallcenterSalesProcess::getProcessNo,processNo);
        return this.list(queryWrapper);
    }

    @Override
    public Boolean updateStatus(Integer id,Integer updateType){
        log.info("CallcenterSalesProcessServiceImpl.updateStatus,param:id={},updateType={}",id,updateType);
        CallcenterSalesProcess salesProcess = null;
        if(null == id){
            return false;
        }else {
            salesProcess = this.getById(id);
        }
        LocalDateTime now = LocalDateTime.now();
        LambdaUpdateWrapper<CallcenterSalesProcess> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CallcenterSalesProcess::getUpdateTime,now).set(CallcenterSalesProcess::getUpdateUser,OwnAuthUtil.getPlannerNo());
        switch (updateType){
            case 1:
                // 校验是否可发布
                iCallcenterSaleProcessNodeService.validateNode(Long.valueOf(id));
                //发布时，将当前版本流程修改为发布状态
                updateWrapper.set(CallcenterSalesProcess::getProcessStatus, SalesProcessStatusEnum.PAUSE.getCode())
                        .set(CallcenterSalesProcess::getPublishTime,now)
                        .eq(CallcenterSalesProcess::getId, id);
                this.update(updateWrapper);
                break;
            case 2:
                //暂停时，需要暂停所有已发布的版本记录的状态 并记录暂停时间
                updateWrapper.set(CallcenterSalesProcess::getProcessStatus, SalesProcessStatusEnum.PAUSE.getCode())
                        .set(CallcenterSalesProcess::getPauseTime,LocalDateTime.now())
                        .eq(CallcenterSalesProcess::getProcessNo, salesProcess.getProcessNo())
                        .eq(CallcenterSalesProcess::getProcessStatus,SalesProcessStatusEnum.PUBLISH.getCode());
                this.update(updateWrapper);
                break;
            case 3:
                // 启用时需要启用所有已暂停的版本记录的状态
                updateWrapper.set(CallcenterSalesProcess::getProcessStatus, SalesProcessStatusEnum.PUBLISH.getCode())
                        .eq(CallcenterSalesProcess::getProcessNo, salesProcess.getProcessNo())
                        .eq(CallcenterSalesProcess::getProcessStatus,SalesProcessStatusEnum.PAUSE.getCode());
                this.update(updateWrapper);
                break;
            case 4:
                //终止时,需要把所有版本的状态均改为结束终止态
                updateWrapper.set(CallcenterSalesProcess::getProcessStatus, SalesProcessStatusEnum.STOP.getCode())
                        .eq(CallcenterSalesProcess::getProcessNo, salesProcess.getProcessNo());
                this.update(updateWrapper);
                break;
            default:
                log.info("CallcenterSalesProcessServiceImpl.updateStatus updateType error,updateType:{}",updateType);
        }
        return true;
    }

    @Override
    public Page<CallcenterSaleProcessRecordRes> getUserSaleProcessList(CallcenterUserSalesProcessReq req){
        /*
        数据权限范围内的理财师
         */
        List<String> scopePlannerIds = tenantServiceHolder.getScopePlanners(callcenterProperty.getYumengSalesProcessMenuCode());
        // callcenter_sale_process_record 与 planner_user_bind 表连表查询
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("plannerIds",scopePlannerIds);
        paramMap.put("processNo",req.getProcessNo());
        paramMap.put("pageStart",(req.getPageNo()-1)*req.getPageSize());
        paramMap.put("pageSize",req.getPageSize());
        // 查询总的记录条数
        Integer totalCount = saleProcessRecordReadonlyService.countSaleProcessRecordListPage(paramMap);
        // 分页查询列表
        List<CallcenterSaleProcessRecordRes> recordResList = saleProcessRecordReadonlyService.getSaleProcessRecordListPage(paramMap);
        recordResList.stream().forEach(bean ->{
            String nodeId = bean.getCurrentNodeId();
            // 查询节点名称并set
            com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode node = saleProcessNodeReadonlyService.selectBeanByIdReadonly(nodeId);
            bean.setCurrentNodeName(node == null ? null : node.getName());
            // 查询流程表获取版本号
            CallcenterSalesProcess salesProcess = this.getById(bean.getSaleProcessId());
            bean.setVersionNo(salesProcess == null ? "" : salesProcess.getVersionNo());
            bean.setStartTimeStr(bean.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            bean.setEndTimeStr(bean.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        });
        Page<CallcenterSaleProcessRecordRes> page = new Page<>(req.getPageNo(),req.getPageSize());
        page.setTotal(totalCount);
        page.setPages((long) Math.ceil(((double) totalCount) / req.getPageSize()));
        page.setRecords(recordResList);
        return page;
    }

    @Override
    public List<CallCenterSalesProcessRecordExportRes> exportList(CallcenterUserSalesProcessReq req){
        /*
        数据权限范围内的理财师
         */
        List<String> scopePlannerIds = tenantServiceHolder.getScopePlanners(callcenterProperty.getYumengSalesProcessMenuCode());
        // callcenter_sale_process_record 与 planner_user_bind 表连表查询
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("plannerIds",scopePlannerIds);
        paramMap.put("processNo",req.getProcessNo());
        // 查询列表
        List<CallcenterSaleProcessRecordRes> recordResList = saleProcessRecordReadonlyService.getSaleProcessRecordListPage(paramMap);
        List<CallCenterSalesProcessRecordExportRes> result = Optional.ofNullable(recordResList).orElse(new ArrayList<>()).stream().map(bean ->{
            CallCenterSalesProcessRecordExportRes exportRes = new CallCenterSalesProcessRecordExportRes();
            BeanUtils.copyProperties(bean,exportRes);
            String nodeId = bean.getCurrentNodeId();
            // 查询节点名称并set
            com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode node = saleProcessNodeReadonlyService.selectBeanByIdReadonly(nodeId);
            exportRes.setCurrentNodeName(node == null ? null : node.getName());
            // 查询流程表获取版本号
            CallcenterSalesProcess salesProcess = this.getById(bean.getSaleProcessId());
            exportRes.setVersionNo(salesProcess == null ? "" : salesProcess.getVersionNo());
            exportRes.setStartTimeStr(bean.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            exportRes.setEndTimeStr(bean.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            return exportRes;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public Boolean exportListTask(String processNo) {
        Map<String,Object> param = new HashMap<>();
        param.put("processNo",processNo);
        CallCenterTaskCreateDTO create = new CallCenterTaskCreateDTO()
                .setModule("销售管理-销售流程管理")
                .setTaskName("销售流程["+processNo+"]客户列表")
                .setTaskType(TaskTypeEnum.EXPORT.getCode())
                .setBizCode(GENERAL_EXCEL_EXPORT_TASK)
                .setFileName("销售流程客户列表-"+processNo)
                .setServerName(callServiceProperty.getWorkbenchApplicationName())
                .setUrl("/workbench/callcenterSalesProcess/exportDataList/")
                .setExcelHeaders(ExcelAnnotationUtil.getExcelPropertyMap(CallCenterSalesProcessRecordExportRes.class))
                .setParam(JSON.toJSONString(param))
                .setUserId(OwnAuthUtil.getUserId())
                .setCreatUser(OwnAuthUtil.getRealName())
                .setTenantId(OwnAuthUtil.getTenantId());
        log.info("提交任务到调度中心 {}", create);
        CallCenterTask callCenterTask = taskScheduleWrapper.submitToTaskSchedule(create);
        return true;
    }

    @Override
    public Page<LinkedHashMap<String, Object>> exportDataList(Map<String, Object> request){
        /*
        数据权限范围内的理财师
         */
        List<String> scopePlannerIds = tenantServiceHolder.getScopePlanners(callcenterProperty.getYumengSalesProcessMenuCode());
        // callcenter_sale_process_record 与 planner_user_bind 表连表查询
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("plannerIds",scopePlannerIds);
        paramMap.put("processNo",request.get("processNo"));
        paramMap.put("pageStart",((Integer)request.get("pageNo")-1)*(Integer)request.get("pageSize"));
        paramMap.put("pageSize",request.get("pageSize"));
        // 查询总的记录条数
        Integer totalCount = saleProcessRecordReadonlyService.countSaleProcessRecordListPage(paramMap);
        // 分页查询列表
        List<CallcenterSaleProcessRecordRes> recordResList = saleProcessRecordReadonlyService.getSaleProcessRecordListPage(paramMap);
        recordResList.stream().forEach(bean ->{
            String nodeId = bean.getCurrentNodeId();
            // 查询节点名称并set
            com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode node = saleProcessNodeReadonlyService.selectBeanByIdReadonly(nodeId);
            bean.setCurrentNodeName(node == null ? null : node.getName());
            // 查询流程表获取版本号
            CallcenterSalesProcess salesProcess = this.getById(bean.getSaleProcessId());
            bean.setVersionNo(salesProcess == null ? "" : salesProcess.getVersionNo());
            bean.setStartTimeStr(bean.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            bean.setEndTimeStr(bean.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        });
        Page<LinkedHashMap<String, Object>> page = new Page<>((Integer)request.get("pageNo"),(Integer)request.get("pageSize"));
        page.setTotal(totalCount);
        page.setPages((long) Math.ceil(((double) totalCount) / (Integer)request.get("pageSize")));
        List<LinkedHashMap<String, Object>> result = recordResList.stream().map(bean ->{
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            map = BeanToMapUtil.beanToMap(bean);
            return map;
        }).collect(Collectors.toList());
        page.setRecords(result);
        return page;
    }

    /**
     * 根据流程id修改结点配置内容
     * @param id
     * @param nodeText
     * @return
     */
    @Override
    public boolean updateNodeTextById(Long id,String nodeText){
        log.info("updateNodeTextById,id:{},nodeText:{}",id,nodeText);
        LambdaUpdateWrapper<CallcenterSalesProcess> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CallcenterSalesProcess::getNodeText, nodeText)
                .set(CallcenterSalesProcess::getUpdateUser,OwnAuthUtil.getPlannerNo())
                .eq(CallcenterSalesProcess::getId, id);
        return this.update(updateWrapper);
    }


    @Override
    public Long getProcessIdForeditButton(Long id){
        log.info("getProcessIdForeditButton,id:{}",id);
        CallcenterSalesProcess oldProcess = this.getById(id);
        if(Objects.isNull(oldProcess)){
            return null;
        }
        // 若当前记录为未发布版本，则直接返回
        if(SalesProcessStatusEnum.UNPUBLISH.getCode().equals(oldProcess.getProcessStatus())){
            return id;
        }
        LambdaQueryWrapper<CallcenterSalesProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallcenterSalesProcess::getProcessNo,oldProcess.getProcessNo());
        queryWrapper.eq(CallcenterSalesProcess::getProcessStatus,SalesProcessStatusEnum.UNPUBLISH.getCode());
        CallcenterSalesProcess unSendSaleProcess = this.getOne(queryWrapper);
        if(!Objects.isNull(unSendSaleProcess)){
            // 有未发布版本，则直接返回未发布版本id
            log.info("getProcessIdForeditButton,returnId:{}",unSendSaleProcess.getId());
            return unSendSaleProcess.getId();
        }else {
            // 没有未发布版本，则需要新生成一个新版本的未发布版本
            // 修改同流程编号下的历史版本的默认展示状态为0
            LambdaUpdateWrapper<CallcenterSalesProcess> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CallcenterSalesProcess::getDefaultProcess, 0)
                    .eq(CallcenterSalesProcess::getProcessNo, oldProcess.getProcessNo());
            this.update(updateWrapper);
            // 生成新的版本号,新增版本记录
            String version = getProcessVersion(oldProcess.getProcessNo());
            oldProcess.setVersionNo(version);
            oldProcess.setDefaultProcess(1);
            oldProcess.setCreateTime(LocalDateTime.now());
            oldProcess.setCreateUser(OwnAuthUtil.getPlannerNo());
            oldProcess.setUpdateUser(OwnAuthUtil.getPlannerNo());
            oldProcess.setProcessStatus(SalesProcessStatusEnum.UNPUBLISH.getCode());
            oldProcess.setId(null);
            this.save(oldProcess);
            log.info(" CallcenterSalesProcessServiceImpl.saveProcessService, oldId:{},salesProcess.id:{}",id,oldProcess.getId());
            iCallcenterSaleProcessNodeService.copySaleProcessData(id, oldProcess.getId(), version);
            log.info("getProcessIdForeditButton,returnId:{}",oldProcess.getId());
            return oldProcess.getId();
        }
    }

}




