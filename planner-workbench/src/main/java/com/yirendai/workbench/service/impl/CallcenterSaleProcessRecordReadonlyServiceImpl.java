package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.*;
import com.yirendai.workbench.enums.SaleProcessStatusEnum;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode;
import com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessRecordReadonlyMapper;
import com.yirendai.workbench.service.*;
import com.yirendai.workbench.service.callcenter.CallcenterSalesProcessService;
import com.yirendai.workbench.vo.req.callcenter.CallcenterCustomerSalesProcessListReq;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessRecordRes;
import com.yirendai.workbench.vo.res.callcenter.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessRecord;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 销售流程进度记录表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2025-10-10
 */
@Service
@Slf4j
public class CallcenterSaleProcessRecordReadonlyServiceImpl
        extends ServiceImpl<CallcenterSaleProcessRecordReadonlyMapper, CallcenterSaleProcessRecord>
        implements ICallcenterSaleProcessRecordReadonlyService {

    @Resource
    ICallcenterSaleProcessNodeReadonlyService iCallcenterSaleProcessNodeReadonlyService;

    @Resource
    ICallcenterSaleProcessNodeConditionReadonlyService iCallcenterSaleProcessNodeConditionReadonlyService;

    @Resource
    ICallcenterSaleProcessNodeRecordReadonlyService iCallcenterSaleProcessNodeRecordReadonlyService;

    @Resource
    ICallcenterSaleProcessNodeConditionRecordReadonlyService iCallcenterSaleProcessNodeConditionRecordReadonlyService;

    @Resource
    ICallcenterSaleProcessNodeEdgeService iCallcenterSaleProcessNodeEdgeService;

    @Resource
    CallcenterSalesProcessService salesProcessService;

    @Resource
    private CallcenterSaleProcessRecordReadonlyMapper saleProcessRecordReadonlyMapper;

    @Override
    public Integer countSaleProcessRecordListPage(Map<String,Object> paramMap){
        return saleProcessRecordReadonlyMapper.countSaleProcessRecordListPage(paramMap);
    }

    @Override
    public List<CallcenterSaleProcessRecordRes> getSaleProcessRecordListPage(Map<String,Object> paramMap){
        return saleProcessRecordReadonlyMapper.getSaleProcessRecordListPage(paramMap);
    }

    @Override
    public Integer countCustomerSaleProcessRecordListPage(Map<String,Object> paramMap){
        return saleProcessRecordReadonlyMapper.countCustomerSaleProcessRecordListPage(paramMap);
    }

    @Override
    public List<CallcenterCustomerSaleProcessRecordResp> getCustomerSaleProcessRecordListPage(Map<String,Object> paramMap){
        return saleProcessRecordReadonlyMapper.getCustomerSaleProcessRecordListPage(paramMap);
    }


    @Override
    public Page<CallcenterCustomerSaleProcessRecordResp> getUserSalesProcessInfoList(CallcenterCustomerSalesProcessListReq req){
        // callcenter_sale_process_record 与  callcenter_sales_process连表查询
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("userId",req.getUserId());
        paramMap.put("pageStart",(req.getPageNo()-1)*req.getPageSize());
        paramMap.put("pageSize",req.getPageSize());
        if(StringUtils.isNotEmpty(req.getProcessName())){
            paramMap.put("processName",req.getProcessName());
        }
        if(!Objects.isNull(req.getProductType())){
            paramMap.put("productType",req.getProductType());
        }
        if(!Objects.isNull(req.getStatus())){
            paramMap.put("status",req.getStatus());
        }

        Integer totalCount = saleProcessRecordReadonlyMapper.countCustomerSaleProcessRecordListPage(paramMap);
        List<CallcenterCustomerSaleProcessRecordResp> recordResList = saleProcessRecordReadonlyMapper.getCustomerSaleProcessRecordListPage(paramMap);
        recordResList.stream().forEach(bean ->{
            bean.setStatusStr(SaleProcessStatusEnum.getMessageByCode(bean.getStatus()));
            bean.setStartTimeStr(bean.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            bean.setUpdateTimeStr(bean.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            //若当前流程未完成，则还需查询当前未完成节点的节点名称
            if(SaleProcessStatusEnum.PROCESSING.getCode() == bean.getStatus()){
                CallcenterSaleProcessNode node = iCallcenterSaleProcessNodeReadonlyService.getById(bean.getCurrentNodeId());
                bean.setNodeName(node == null ? null : node.getName());
            }
        });
        Page<CallcenterCustomerSaleProcessRecordResp> page = new Page<>(req.getPageNo(),req.getPageSize());
        page.setTotal(totalCount);
        page.setPages((long) Math.ceil(((double) totalCount) / req.getPageSize()));
        page.setRecords(recordResList);
        return page;
    }

    @Override
    public CallcenterCustomerSaleProcessInfoResp getSalesProcessInfoById(String userId,String saleProcessRecordId){
        if(StringUtils.isEmpty(saleProcessRecordId)){
            return null;
        }
        CallcenterSaleProcessRecord saleProcessRecord = this.getById(saleProcessRecordId);
        if(saleProcessRecord == null){
            log.error(" getSalesProcessInfoById.saleProcessRecordId:{} error",saleProcessRecordId);
            return null;
        }
        CallcenterCustomerSaleProcessInfoResp resp = new CallcenterCustomerSaleProcessInfoResp();
        resp.setId(saleProcessRecord.getId());
        resp.setSaleProcessId(saleProcessRecord.getSaleProcessId());
        resp.setProcessNo(saleProcessRecord.getProcessNo());
        resp.setProgressValue(saleProcessRecord.getProgressValue());
        resp.setManualFinishReason(saleProcessRecord.getManualFinishReason());
        CallcenterSalesProcess salesProcess = salesProcessService.getBeanById(saleProcessRecord.getSaleProcessId().intValue());
        resp.setProcessName(salesProcess.getProcessName());
        resp.setProcessVersion(salesProcess.getVersionNo());
        resp.setProductType(salesProcess.getProductType());
        // 查询节点列表信息 获取当前流程从前到后的节点id列表
        List<String> nodeIdList = iCallcenterSaleProcessNodeEdgeService.getNodeListByProcessId(saleProcessRecord.getSaleProcessId());
        if(CollectionUtils.isEmpty(nodeIdList)){
            // 该流程没有节点配置
            return resp;
        }
        // 组装流程节点信息
        List<CallcenterCustomerNodeInfo> nodeInfoList = new ArrayList<>(nodeIdList.size());
        // 根据节点id 查询节点信息 并组装map
        List<com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode> nodeList = iCallcenterSaleProcessNodeReadonlyService.getListByIdsReadonly(nodeIdList);
        Map<String,com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode> nodeIdBeanMap = new HashMap<>(nodeList.size());
        Map<String,Integer> nodeIdTotalNumberMap = new HashMap<>(nodeList.size());
        Map<String,Integer> nodeIdFinishedNumbMap = new HashMap<>(nodeList.size());
        nodeList.stream().forEach(bean ->{
            nodeIdBeanMap.put(bean.getId(),bean);
        });
        // 查询节点事项配置表与完成记录表，获取总的配置数、已完成数
        List<CallcenterNodeConditionNumbInfo> nodeTotalNumbers = iCallcenterSaleProcessNodeConditionReadonlyService.countNumberByNodeIdsReadonly(nodeIdList);
        nodeTotalNumbers.stream().forEach(bean ->{
            nodeIdTotalNumberMap.put(bean.getNodeId(),bean.getConditionNumb());
        });
        Map<String,Object> paramMap = new HashMap<>(2);
        paramMap.put("userId",userId);
        paramMap.put("nodeIds",nodeIdList);
        List<CallcenterNodeConditionNumbInfo> nodeFinishedNumbers = iCallcenterSaleProcessNodeConditionRecordReadonlyService.countNumberByNodeIdsReadonly(paramMap);
        nodeFinishedNumbers.stream().forEach(bean ->{
            nodeIdFinishedNumbMap.put(bean.getNodeId(),bean.getConditionNumb());
        });
        nodeIdList.stream().forEach(id ->{
            CallcenterCustomerNodeInfo nodeInfo = new CallcenterCustomerNodeInfo();
            nodeInfo.setId(id);
            if(id.equals(saleProcessRecord.getCurrentNodeId())){
                nodeInfo.setCurrentNode(true);
            }
            if(nodeIdBeanMap.containsKey(id)){
                com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode node = nodeIdBeanMap.get(id);
                nodeInfo.setNodeName(node.getName());
                nodeInfo.setNodeDesc(node.getDescription());
            }
            // 查询 nodeRecord表，获取节点的开始时间、结束时间
            com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeRecord nodeRecord = iCallcenterSaleProcessNodeRecordReadonlyService.getByNodeIdReadonly(id,userId);
            if(null != nodeRecord){
                nodeInfo.setNodeStartTimeStr(nodeRecord.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                nodeInfo.setNodeEndTimeStr(nodeRecord.getFinishTime() == null ? null : nodeRecord.getFinishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                nodeInfo.setNodeStatus(nodeRecord.getStatus());
            }

            // 设置节点总事项数、完成事项数
            if(nodeIdTotalNumberMap.containsKey(id)){
                nodeInfo.setTotalConditionNumb(nodeIdTotalNumberMap.get(id));
            }
            if(nodeIdFinishedNumbMap.containsKey(id)){
                nodeInfo.setFinishedConditionNumb(nodeIdFinishedNumbMap.get(id));
            }
            nodeInfoList.add(nodeInfo);
        });
        resp.setNodeInfoList(nodeInfoList);
        return resp;
    }

    @Override
    public CallcenterCustomerNodeInfoResp getCustomerNodeInfo(String userId, String nodeId){
        CallcenterCustomerNodeInfoResp resp = new CallcenterCustomerNodeInfoResp();
        resp.setId(nodeId);
        // 查询该节点所有的条件项列表
        List<com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeCondition> allNodeConditions = iCallcenterSaleProcessNodeConditionReadonlyService.selectListByNodeIdReadonly(nodeId);
        if(CollectionUtils.isEmpty(allNodeConditions)){
            return resp;
        }
        List<CallcenterCustomerNodeCondition> unDoConditionList = new ArrayList<>(allNodeConditions.size());
        List<CallcenterCustomerNodeCondition> doConditionList = new ArrayList<>(allNodeConditions.size());
        // 查询用户已完成记录
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("userId",userId);
        paramMap.put("nodeId",nodeId);
        paramMap.put("status",1);
        List<com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeConditionRecord> finishedConditions = iCallcenterSaleProcessNodeConditionRecordReadonlyService.selectListByMapReadonly(paramMap);
        Map<Long, com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeConditionRecord> finishedConditionMap = new HashMap<>(finishedConditions.size());
        finishedConditions.stream().forEach(bean ->{
            finishedConditionMap.put(bean.getConditionId(),bean);
        });
        allNodeConditions.stream().forEach(bean ->{
            CallcenterCustomerNodeCondition nodeCondition = new CallcenterCustomerNodeCondition();
            nodeCondition.setId(bean.getId());
            nodeCondition.setConditionName(bean.getName());
            if(CollectionUtils.isNotEmpty(finishedConditions) && finishedConditionMap.containsKey(bean.getId())){
                com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeConditionRecord conditionRecord = finishedConditionMap.get(bean.getId());
                nodeCondition.setFinishTime(conditionRecord.getFinishTime());
                nodeCondition.setFinishTimeStr(conditionRecord.getFinishTime()==null ? "" : conditionRecord.getFinishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                nodeCondition.setFinishType(conditionRecord.getFinishType()==1 ? "手动" : "系统");
                nodeCondition.setRemark(conditionRecord.getRemark());
                // todo 附加信息中若为附件需处理一下url
                nodeCondition.setOtherInfo(conditionRecord.getExtraInfo());
                nodeCondition.setOperater(conditionRecord.getFinisher());  // 操作人
                doConditionList.add(nodeCondition);
            }else {
                unDoConditionList.add(nodeCondition);
            }

        });
        if(CollectionUtils.isNotEmpty(doConditionList)){
            doConditionList.sort(Comparator.comparing(CallcenterCustomerNodeCondition::getFinishTime, Comparator.nullsLast(LocalDateTime::compareTo)));
        }
        resp.setUnDoConditionList(unDoConditionList);
        resp.setDoConditionList(doConditionList);
        return resp;
    }
}
