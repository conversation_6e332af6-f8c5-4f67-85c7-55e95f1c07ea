package com.yirendai.workbench.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeConditionRecord;
import com.yirendai.workbench.readonly.mapper.CallcenterSaleProcessNodeConditionRecordReadonlyMapper;
import com.yirendai.workbench.service.ICallcenterSaleProcessNodeConditionRecordReadonlyService;
import com.yirendai.workbench.vo.res.callcenter.CallcenterNodeConditionNumbInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售流程节点条件进度记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
@Service
public class CallcenterSaleProcessNodeConditionRecordReadonlyServiceImpl extends ServiceImpl<CallcenterSaleProcessNodeConditionRecordReadonlyMapper, CallcenterSaleProcessNodeConditionRecord> implements ICallcenterSaleProcessNodeConditionRecordReadonlyService {

    @Resource
    private CallcenterSaleProcessNodeConditionRecordReadonlyMapper saleProcessNodeConditionRecordReadonlyMapper;

    @Override
    public List<CallcenterNodeConditionNumbInfo> countNumberByNodeIdsReadonly(Map<String,Object> paramMap){
        return saleProcessNodeConditionRecordReadonlyMapper.countNumberByNodeIds(paramMap);
    }

    @Override
    public List<CallcenterSaleProcessNodeConditionRecord> selectListByMapReadonly(Map<String,Object> paramMap){
        return saleProcessNodeConditionRecordReadonlyMapper.selectListByMap(paramMap);
    }

}
