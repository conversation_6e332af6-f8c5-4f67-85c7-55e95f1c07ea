package com.yirendai.workbench.intercepter;

import com.alibaba.fastjson.JSON;
import com.yirendai.robot.modules.session.service.IRobotSessionService;
import com.yirendai.robot.util.RobotFormatUtil;
import com.yirendai.robot.util.bizcode.BizCodeEnum;
import com.yirendai.voiceaiserver.biz.asr.VadProcessFactory;
import com.yirendai.voiceaiserver.service.CallCenterService;
import com.yirendai.workbench.vo.req.CallRecordInfo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Slf4j
@Aspect
@Component
public class CallCenterFinishInterceptor {

    @Resource
    CallCenterService callCenterService;

    @Resource
    IRobotSessionService robotSessionService;

    @Resource
    VadProcessFactory vadProcessFactory;

    @AfterReturning(pointcut = "execution(* com.yirendai.workbench.controller.internal.CallRecordCallBakController.recordInfo(..))")
    public void afterReturningAdvice(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args.length > 0 && args[0] instanceof CallRecordInfo) {
            try {
                CallRecordInfo param = JSON.parseObject(JSON.toJSONString(args[0]), CallRecordInfo.class);
                if (param.getUuid() != null && (param.getUuid().contains(BizCodeEnum.ROBOT_UUID.getPrefix()) || param.getUuid().contains(RobotFormatUtil.ROBOT_CALL_IN_UUID_PREFIX))) {
                    log.info("callback recordInfo 是机器人外呼/呼入，直接调用");
                    robotSessionService.finishRobotSession(param);
                    return;
                }
                vadProcessFactory.removeMp3VadAsrProcessor(param.getUuid());
                callCenterService.finishNotify(param.getUuid());
            } catch (Exception e) {
                log.error("呼叫中心回调通知触发AI电话挂断通知流程发生异常，异常原因为", e);
            }
        }
    }
}
