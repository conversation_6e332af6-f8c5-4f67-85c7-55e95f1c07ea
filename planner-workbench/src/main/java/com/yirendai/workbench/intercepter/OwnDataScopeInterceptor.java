/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.workbench.intercepter;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.yirendai.workbench.config.annotation.OwnDataAuth;
import com.yirendai.workbench.util.DataScopeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springblade.core.mp.intercept.QueryInterceptor;
import org.springblade.core.tool.utils.StringUtil;

/**
 * <AUTHOR>
 * @time 2024/11/13 11:36
 **/
@Slf4j
@Intercepts({@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
        RowBounds.class, ResultHandler.class})})
public class OwnDataScopeInterceptor implements QueryInterceptor {

    private final DataScopeUtil dataScopeUtil;
    
    /**
     * Class 缓存 - 修复 Metaspace 泄漏问题
     * 避免每次 SQL 执行都调用 Class.forName
     * Date: 2025-10-27
     */
    private static final ConcurrentHashMap<String, Class<?>> CLASS_CACHE = new ConcurrentHashMap<>();

    public OwnDataScopeInterceptor(DataScopeUtil dataScopeUtil) {
        this.dataScopeUtil = dataScopeUtil;
    }

    @Override
    public void intercept(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds,
            ResultHandler resultHandler, BoundSql boundSql) {
        OwnDataAuth ownDataAuth = hasDataAuthAnnotation(ms);
        if (Objects.nonNull(ownDataAuth)) {
            String modifiedSql = sqlCondition(boundSql.getSql(), ownDataAuth);
            if (!StringUtil.isBlank(modifiedSql)) {
                PluginUtils.MPBoundSql mpBoundSql = PluginUtils.mpBoundSql(boundSql);
                mpBoundSql.sql(modifiedSql);
            }
        }
    }

    /**
     * 获取数据权限注解信息
     * @param ms
     * @return
     */
    private OwnDataAuth hasDataAuthAnnotation(MappedStatement ms) {
        String id = ms.getId();
        String className = id.substring(0, id.lastIndexOf('.'));
        String methodName = id.substring(id.lastIndexOf('.') + 1);
        try {
            // 使用缓存避免重复加载类 - 修复 Metaspace 泄漏
            Class<?> mapperClass = CLASS_CACHE.computeIfAbsent(className, k -> {
                try {
                    return Class.forName(k);
                } catch (ClassNotFoundException e) {
                    log.error("无法加载 Mapper 类: {}", k, e);
                    throw new RuntimeException("无法加载类: " + k, e);
                }
            });
            
            for (Method method : mapperClass.getDeclaredMethods()) {
                if (method.getName().equals(methodName)) {
                    if (method.isAnnotationPresent(OwnDataAuth.class)) {
                        return method.getAnnotation(OwnDataAuth.class);
                    }
                }
            }
        } catch (Exception e) {
            log.error("hasDataAuthAnnotation error", e);
        }
        return null;
    }

    private String sqlCondition(String originSql, OwnDataAuth ownDataAuth) {
        return dataScopeUtil.getSql(originSql, ownDataAuth);
    }

    @Override
    public int getOrder() {
        return QueryInterceptor.super.getOrder();
    }
}
