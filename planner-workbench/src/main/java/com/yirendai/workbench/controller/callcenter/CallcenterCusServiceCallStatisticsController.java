package com.yirendai.workbench.controller.callcenter;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.service.ICallcenterCusServiceCallStatisticsService;
import com.yirendai.workbench.vo.req.callcenter.CusServiceCallStatisticsPageReq;
import com.yirendai.workbench.vo.req.callcenter.saas.CusServiceCallStatisticsExportReq;
import com.yirendai.workbench.vo.res.callcenter.BatchOperationResult;
import com.yirendai.workbench.vo.res.callcenter.CusServiceCallStatisticsPageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 客服呼叫统计数据表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/workbench/callcenterCusServiceCallStatistics")
@Api(value = "客服呼叫统计数据", tags = "客服呼叫统计数据")
public class CallcenterCusServiceCallStatisticsController {

    @Resource
    ICallcenterCusServiceCallStatisticsService iCallcenterCusServiceCallStatisticsService;

    @PostMapping("page")
    @ApiOperation(value = "客服呼叫统计列表")
    public R<Page<CusServiceCallStatisticsPageRes>> page(@RequestBody CusServiceCallStatisticsPageReq req) {
        return R.data(iCallcenterCusServiceCallStatisticsService.page(req));
    }

    @PostMapping("exportTask")
    @ApiOperation(value = "客服呼叫统计导出任务")
    public R<BatchOperationResult> exportTask(
            @RequestBody CusServiceCallStatisticsExportReq cusServiceCallStatisticsExportReq) {
        return R.data(iCallcenterCusServiceCallStatisticsService.exportTask(cusServiceCallStatisticsExportReq));
    }
}