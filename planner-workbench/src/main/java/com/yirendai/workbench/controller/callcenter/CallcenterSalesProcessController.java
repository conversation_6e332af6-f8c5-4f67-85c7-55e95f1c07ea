package com.yirendai.workbench.controller.callcenter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.entity.CallcenterSalesProcess;
import com.yirendai.workbench.service.callcenter.CallcenterSalesProcessService;
import com.yirendai.workbench.vo.req.callcenter.CallcenterSalesProcessAddReq;
import com.yirendai.workbench.vo.req.callcenter.CallcenterSalesProcessListReq;
import com.yirendai.workbench.vo.req.callcenter.CallcenterSalesProcessSaveFlowReq;
import com.yirendai.workbench.vo.req.callcenter.CallcenterUserSalesProcessReq;
import com.yirendai.workbench.vo.res.CallCenterSalesProcessRecordExportRes;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessRecordRes;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.api.R;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售流程
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@RestController
@RequestMapping("/workbench/callcenterSalesProcess")
public class CallcenterSalesProcessController {

    @Resource
    CallcenterSalesProcessService callcenterSalesProcessService;

    @PostMapping("page")
    @ApiOperation(value = "流程列表")
    public R<Page<CallcenterSalesProcess>> pageList(@RequestBody CallcenterSalesProcessListReq salesProcessListReq) {
        // 查询 最新版本列表
        return R.data(callcenterSalesProcessService.pageList(salesProcessListReq));
    }


    @PostMapping("userList")
    @ApiOperation(value = "流程用户列表")
    public R<Page<CallcenterSaleProcessRecordRes>> userList(@RequestBody CallcenterUserSalesProcessReq req) {
        //查询当前流程下的所有版本的用户的数据
        return R.data(callcenterSalesProcessService.getUserSaleProcessList(req));
    }

    @GetMapping("export")
    @ApiOperation(value = "导出")
    public R<Boolean> export(HttpServletResponse response,@ApiParam(value = "流程编号", required = true) @RequestParam String processNo) {
        /*List<CallCenterSalesProcessRecordExportRes> list = callcenterSalesProcessService.exportList(
                req);
        ExcelUtil.export(response, "客户销售流程进度列表-"+req.getProcessNo(), req.getProcessNo(), list, CallCenterSalesProcessRecordExportRes.class);*/
        // 修改走任务中心的那套异步实现
        return R.data(callcenterSalesProcessService.exportListTask(processNo));
    }

    @PostMapping("exportDataList")
    @ApiOperation(value = "导出")
    public R<Page<LinkedHashMap<String, Object>>> exportDataList(@Validated @RequestBody @NotNull(message = "请求参数不能为空") Map<String, Object> request) {
        Page<LinkedHashMap<String, Object>> result = callcenterSalesProcessService.exportDataList(request);
        return R.data(result);
    }

    @PostMapping("saveProcess")
    @ApiOperation(value = "新增修改流程,复制按钮时，需单独传入复制的bean的id")
    public R<CallcenterSalesProcess> saveProcess(@RequestBody CallcenterSalesProcessAddReq salesProcessAddReq) {
        //新增流程时，修改所有历史版本的默认展示状态为0 ，当前新增版本的默认展示状态改为1 方便分页查询时使用
        CallcenterSalesProcess salesProcess = new CallcenterSalesProcess();
        BeanUtils.copyProperties(salesProcessAddReq,salesProcess);
        CallcenterSalesProcess process = callcenterSalesProcessService.saveProcessService(salesProcess,salesProcessAddReq.getCopyId());
        return R.data(process);
    }

    @GetMapping("processDetail")
    @ApiOperation(value = "查询流程详情")
    public R<CallcenterSalesProcess> processDetail(@ApiParam(value = "流程id", required = true) @RequestParam Integer processId) {
        return R.data(callcenterSalesProcessService.getBeanById(processId));
    }

    @GetMapping("processVersionList")
    @ApiOperation(value = "同一流程下的版本列表")
    public R<List<CallcenterSalesProcess>> processVersionList(@ApiParam(value = "流程编号", required = true) @RequestParam String processNo) {
        return R.data(callcenterSalesProcessService.getListByProcessNo(processNo));

    }

    @GetMapping("updateStatus")
    @ApiOperation(value = "暂停、启用、发布、终止按钮触发")
    public R<Boolean> updateStatus(@ApiParam(value = "流程id", required = true) @RequestParam Integer processId,@ApiParam(value = "按钮类型 1-发布；2-暂停；3-启动；4-终止", required = true) @RequestParam Integer updateType) {
        return R.data(callcenterSalesProcessService.updateStatus(processId,updateType));
    }

    @PostMapping("getProcessIdForeditButton")
    @ApiOperation(value = "编辑按钮点击时触发接口，返回当前流程id或新版本流程id")
    public R<Long> getProcessIdForeditButton(@RequestBody CallcenterSalesProcessSaveFlowReq req) {
        return R.data(callcenterSalesProcessService.getProcessIdForeditButton(Long.valueOf(req.getProcessId())));
    }

    @PostMapping("saveFlow")
    @ApiOperation(value = "画布位置调整修改text字段")
    public R<Boolean> saveFlow(@RequestBody CallcenterSalesProcessSaveFlowReq req) {
        return R.data(callcenterSalesProcessService.updateNodeTextById(Long.valueOf(req.getProcessId()),req.getNodeText()));
    }


}
