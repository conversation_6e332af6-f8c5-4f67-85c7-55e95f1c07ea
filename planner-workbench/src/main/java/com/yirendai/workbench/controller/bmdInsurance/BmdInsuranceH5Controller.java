package com.yirendai.workbench.controller.bmdInsurance;

import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.service.bmdInsurance.BmdInsuranceH5service;
import com.yirendai.workbench.vo.req.bmdInsurance.BmdInsuranceSignReq;
import com.yirendai.workbench.vo.res.bmdInsurance.BmdInsuranceH5ReviewResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/bmd-insurance/h5")
@Slf4j
@Api(value = "百慕大保险H5接口", tags = "百慕大保险H5接口")
public class BmdInsuranceH5Controller {

    @Autowired
    private BmdInsuranceH5service bmdInsuranceH5service;

    @ApiOperation(value = "查看文件列表详情")
    @GetMapping("/view/{applyId}")
    public R<BmdInsuranceH5ReviewResp> viewPdfList(@PathVariable Long applyId,
                                                   @RequestParam("token") String token,
                                                   @RequestParam("userRole") String userRole) {
        // 获取PDF文件列表，包含token验证
        BmdInsuranceH5ReviewResp resp = bmdInsuranceH5service.streamPdfFiles(applyId, userRole, token);
        return R.data(resp);
    }

    @GetMapping("/viewFile")
    @ApiOperation(value = "查看文件")
    public void viewPdf(@RequestParam(value = "token") String token, @RequestParam(value = "applyId") Long applyId,
                        @RequestParam(value = "userRole") String userRole,
                        @RequestParam(value = "isChinese", defaultValue = "true", required = false) Boolean isChinese,
                        @RequestParam(value = "pdfType") String pdfType, @RequestParam(value = "id") Long id,
                        HttpServletResponse response) {
        // 查看具体PDF文件，包含token验证
        bmdInsuranceH5service.viewPdf(token, applyId, userRole, pdfType, isChinese, id, response);
    }

    @ApiOperation(value = "签署文件")
    @PostMapping("/sign")
    public R<Boolean> signPdf(@Valid @RequestBody BmdInsuranceSignReq req) {
        bmdInsuranceH5service.signPdfFiles(req.getApplyId(), req.getUserRole(), req.getSignBase());
        return R.data(true);
    }
}
