package com.yirendai.workbench.controller;


import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.model.PlannerInfoDto;
import com.yirendai.workbench.service.IBladeUserService;
import com.yirendai.workbench.vo.req.PlannerInfoRequest;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查询理财师信息的内部接口,供CRM等服务调用
 * 由于yrcf-crm库中的planner_user_bind表中 理财师信息更新不及时, 且CRM服务未配置Blade数据源,无法获取最新的理财师数据
 * 因此提供该接口供其他服务（如 CRM）通过租户ID和理财师ID列表获取最新信息
 */
@RestController
@RequestMapping("/workbench/planner")
public class PlannerInfoController {
    @Resource
    private IBladeUserService iBladeUserService;

    @PostMapping("/info")
    public R<List<PlannerInfoDto>> getPlannerInfo(@RequestBody PlannerInfoRequest plannerInfoRequest) {
        List<PlannerInfoDto> plannerInfoDtoList = iBladeUserService.findPlannerInfoByPlannerIds(plannerInfoRequest.getTenantId(), plannerInfoRequest.getPlannerIds());
        return R.data(plannerInfoDtoList);
    }
}
