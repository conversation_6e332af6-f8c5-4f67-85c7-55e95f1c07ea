package com.yirendai.workbench.controller.callcenter;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.yirendai.workbench.service.ICallcenterSaleProcessNodeService;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeAddReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeDelReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeDetailReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeEdgeUpdateReq;
import com.yirendai.workbench.vo.req.CallcenterSaleProcessNodeStartReq;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessNodeAddRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 销售流程节点基础信息表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2025-10-09
 */
@RestController
@RequestMapping("/workbench/saleProcessNode")
@Api(tags = "销售流程节点相关接口", value = "销售流程节点相关接口")
public class CallcenterSaleProcessNodeController {

    @Resource
    private ICallcenterSaleProcessNodeService iCallcenterSaleProcessNodeService;

    @PostMapping("/add")
    @ApiOperation(value = "添加节点")
    public R<CallcenterSaleProcessNodeAddRes> add(CallcenterSaleProcessNodeAddReq req) {
        return R.data(iCallcenterSaleProcessNodeService.add(req));
    }

    @PostMapping("/edit")
    @ApiOperation(value = "编辑节点")
    public R<CallcenterSaleProcessNodeAddRes> edit(@RequestBody CallcenterSaleProcessNodeAddReq req) {
        return R.data(iCallcenterSaleProcessNodeService.edit(req));
    }

    @PostMapping("/del")
    @ApiOperation(value = "删除详情")
    public R<Boolean> del(CallcenterSaleProcessNodeDelReq req) {
        return R.data(iCallcenterSaleProcessNodeService.del(req));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "节点详情")
    public R<CallcenterSaleProcessNodeAddRes> detail(CallcenterSaleProcessNodeDetailReq req) {
        return R.data(iCallcenterSaleProcessNodeService.detail(req));
    }

    @ApiOperation(value = "连接两个节点或断开连接", notes = "连接或删除任意两个节点（除开始节点之外）之间的边时使用")
    @PostMapping("/updateEdge")
    public R<Boolean> updateEdge(@RequestBody @Valid CallcenterSaleProcessNodeEdgeUpdateReq req) {
        return R.data(iCallcenterSaleProcessNodeService.updateEdge(req));
    }

    @ApiOperation(value = "切换开始节点", notes = "连接或删除开始节点与下一个节点之间的边时使用")
    @PostMapping("/updateStart")
    public R<Boolean> updateStart(@RequestBody @Valid CallcenterSaleProcessNodeStartReq req) {
        return R.data(iCallcenterSaleProcessNodeService.updateStart(req));
    }
}
