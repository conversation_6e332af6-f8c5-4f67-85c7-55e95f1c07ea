package com.yirendai.workbench.controller;

import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.yirendai.workbench.config.annotation.Url;
import com.yirendai.workbench.util.HttpUtil;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.yirendai.workbench.util.RequestUtil.appendUrl;
import static com.yirendai.workbench.util.RequestUtil.getMap;

/**
 * <AUTHOR>
 * @time 2024/4/23 18:03
 **/
@RestController
@RequestMapping("ai")
@CrossOrigin("*")
public class AiController {

    @PostMapping(value = "transit/**", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Object transitForm(@RequestParam Map<String, Object> map, @Url String url) {
        getMap(map);
        return JSON.parseObject(HttpUtil.post(url, map));
    }

    @PostMapping(value = "transit/**", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Object transitJson(@RequestBody Map<String, Object> map, @Url String url) {
        getMap(map);
        return JSON.parseObject(HttpUtil.postForJson(url, JSON.toJSONString(map)));
    }

    @GetMapping(value = "transit/**")
    public Object transitGet(@Url String url) {
        return JSON.parseObject(HttpUtil.get(appendUrl(url)));
    }
}
