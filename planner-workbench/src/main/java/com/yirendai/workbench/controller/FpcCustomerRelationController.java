package com.yirendai.workbench.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.workbench.config.annotation.SaveLogAspect;
import com.yirendai.workbench.entity.EnumBean;
import com.yirendai.workbench.entity.InsuranceProductBeanVo;
import com.yirendai.workbench.entity.FpcCustomerRelation;
import com.yirendai.workbench.enums.AssignTypeEnum;
import com.yirendai.workbench.enums.FollowUpTypeEnum;
import com.yirendai.workbench.enums.LocationEnum;
import com.yirendai.workbench.service.IFpcCustomerRelationService;
import com.yirendai.workbench.service.IInsuranceProductService;
import com.yirendai.workbench.vo.req.AssignFpcReq;
import com.yirendai.workbench.vo.req.FpcCustomerRelationListReq;
import com.yirendai.workbench.vo.req.UpdateAssignFpcReq;
import com.yirendai.workbench.vo.res.FpcCustomerRelationRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * fpc客户关系表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2024-05-08
 */
@RestController
@RequestMapping("/workbench/fpcCustomerRelation")
@Api(value = "fpc客户关系", tags = "fpc客户关系")
public class FpcCustomerRelationController {

    @Resource
    IFpcCustomerRelationService iFpcCustomerRelationService;
    @Resource
    IInsuranceProductService insuranceProductService;

    @PostMapping("list")
    @ApiOperation(value = "查询列表")
    public R<IPage<FpcCustomerRelation>> list(FpcCustomerRelationListReq fpcCustomerRelationListReq) {
        return iFpcCustomerRelationService.page(fpcCustomerRelationListReq);
    }

    @PostMapping("detail")
    @ApiOperation(value = "明细")
    public R<FpcCustomerRelation> detail(@ApiParam("fpc客户关系id") @RequestParam Long id) {
        return iFpcCustomerRelationService.detail(id);
    }

    @GetMapping("export")
    @ApiOperation(value = "导出")
    public void export(HttpServletResponse response, FpcCustomerRelationListReq fpcCustomerRelationListReq) {
        List<FpcCustomerRelationRes> list = null;
        List<FpcCustomerRelation> fpcCustomerRelationList = iFpcCustomerRelationService.list(
                fpcCustomerRelationListReq);
        if (CollectionUtils.isNotEmpty(fpcCustomerRelationList)) {
            list = fpcCustomerRelationList.stream().map(f -> {
                FpcCustomerRelationRes res = BeanUtil.copyProperties(f, FpcCustomerRelationRes.class);
                res.setAssignType(AssignTypeEnum.getValueByCode(f.getAssignType()));
                res.setType(FollowUpTypeEnum.getValueByCode(f.getType()));
                res.setMemberCity(LocationEnum.getValueByCode(f.getMemberCity() + ""));
                res.setInsuranceCompany(insuranceProductService.getInsuranceMap().get(f.getInsuranceCompany()));
                res.setProduct(insuranceProductService.getProductMap().get(f.getProduct()));
                return res;
            }).collect(Collectors.toList());
        }
        ExcelUtil.export(response, "fpc客户关系", "fpc客户关系", list, FpcCustomerRelationRes.class);
    }

    @PostMapping("relieve")
    @ApiOperation(value = "解除")
    @SaveLogAspect(controllerkey="FpcCustomerRelationController.relieve")
    public R<Boolean> relieve(@ApiParam("fpc客户关系id") @RequestParam Long id) {
        return iFpcCustomerRelationService.relieve(id);
    }

    @GetMapping("getAssignFpc")
    @ApiOperation(value = "查询是否已分派fpc")
    public R<FpcCustomerRelation> getAssignFpc(@RequestParam Long userId) {
        return iFpcCustomerRelationService.getFpcByUserId(userId);
    }

    @PostMapping("assign")
    @ApiOperation(value = "分派")
    @SaveLogAspect(controllerkey="FpcCustomerRelationController.assign")
    public R<Boolean> assign(@Validated AssignFpcReq assignFpcReq) {
        return iFpcCustomerRelationService.assign(assignFpcReq);
    }

    @PostMapping("updateAssign")
    @ApiOperation(value = "修改分派")
    @SaveLogAspect(controllerkey="FpcCustomerRelationController.updateAssign")
    public R<Boolean> updateAssign(@Validated UpdateAssignFpcReq assignFpcReq) {
        return iFpcCustomerRelationService.updateAssign(assignFpcReq);
    }

    @PostMapping("getInsuranceProductMap")
    @ApiOperation(value = "保司产品信息表")
    public R<List<InsuranceProductBeanVo>> getInsuranceProductMap() {
        return iFpcCustomerRelationService.getInsuranceProductMap();
    }

    @GetMapping("getInsuranceList")
    @ApiOperation(value = "查询保司信息列表")
    public R<List<EnumBean>> getInsuranceList() {
        return R.data(insuranceProductService.getInsuranceList());
    }

    @GetMapping("getProductListByInsurance")
    @ApiOperation(value = "根据保司key查询产品信息列表")
    public R<List<EnumBean>> getProductListByInsurance(@RequestParam Integer insuranceKey) {
        return R.data(insuranceProductService.getProductListByInsurance(insuranceKey));
    }
}
