package com.yirendai.workbench.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.workbench.entity.AiPlanner;
import com.yirendai.workbench.entity.AiPlannerChatContact;
import com.yirendai.workbench.entity.AiPlannerUser;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.service.IAiPlannerChatContactService;
import com.yirendai.workbench.service.IAiPlannerTopUserService;
import com.yirendai.workbench.vo.req.PlannerChatContactReq;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/aiPlannerChatContact")
public class AiPlannerChatContactController {

    @Resource
    IAiPlannerTopUserService aiPlannerTopUserService;

    @Resource
    IAiPlannerChatContactService aiPlannerChatContactService;

    @GetMapping("/planners")
    @ApiOperation(value = "查询所有理财师信息接口")
    public R<List<AiPlanner>> listAllPlanner() {
        return R.data(aiPlannerTopUserService.listAllPlanner());
    }

    @GetMapping("/planner/{id}/relatedUsers")
    @ApiOperation(value = "根据理财师ID查询所有有关客户信息接口")
    public R<List<AiPlannerUser>> listRelatedUserByPlannerId(@PathVariable(value = "id") String plannerNo) {
        return R.data(aiPlannerTopUserService.listAllRelatedUserByPlannerNo(plannerNo));
    }

    @PostMapping("/partialContent")
    @ApiOperation(value = "聊天内容查询", notes = "包含初始内容以及部分处理后的内容")
    public R<IPage<AiPlannerChatContact>> listOriginContent(@Valid @RequestBody PlannerChatContactReq plannerChatContactReq) {
        return R.data(aiPlannerChatContactService.getPartialContent(plannerChatContactReq));
    }

    @GetMapping("/processedContent/{id}")
    @ApiOperation(value = "根据ID查询处理后的聊天内容")
    public R<String> listProcessedContent(@PathVariable Long id) {
        String processedContent = aiPlannerChatContactService.getProcessedContentById(id);
        if (StringUtils.isEmpty(processedContent)) {
            throw new BusinessException(BusinessError.NO_PROCESSED_CONTENT);
        }
        return R.data(processedContent);
    }
}
