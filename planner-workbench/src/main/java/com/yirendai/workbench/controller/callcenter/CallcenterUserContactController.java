package com.yirendai.workbench.controller.callcenter;

import java.util.List;
import javax.annotation.Resource;

import com.yirendai.workbench.service.callcenter.ICallcenterUserContactService;
import com.yirendai.workbench.vo.req.UserContactAddReq;
import com.yirendai.workbench.vo.req.UserContactDeleteReq;
import com.yirendai.workbench.vo.req.UserContactListReq;
import com.yirendai.workbench.vo.res.UserContactListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 客户联系人表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2024-11-25
 */
@RestController
@RequestMapping("/callservice/userContact")
@Api(value = "客户资料-客户联系人", tags = "客户资料-客户联系人")
public class CallcenterUserContactController {

    @Resource
    ICallcenterUserContactService iCallcenterUserContactService;

    @PostMapping("/saveOrUpdate")
    @ApiOperation("新增或更新")
    public R<Boolean> saveOrUpdate(@Validated @RequestBody UserContactAddReq userContactAddReq) {
        return R.data(iCallcenterUserContactService.saveOrUpdate(userContactAddReq));
    }

    @PostMapping("list")
    @ApiOperation("查询列表")
    public R<List<UserContactListRes>> list(@Validated @RequestBody UserContactListReq userContactListReq) {
        String tenantId = AuthUtil.getTenantId();
        return R.data(iCallcenterUserContactService.list(tenantId, userContactListReq.getUserId()));
    }

    @PostMapping("delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@Validated UserContactDeleteReq userContactDeleteReq) {
        return R.data(iCallcenterUserContactService.removeById(userContactDeleteReq.getId()));
    }
}
