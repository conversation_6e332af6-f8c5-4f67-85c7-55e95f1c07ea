package com.yirendai.workbench.controller;

import javax.annotation.Resource;

import com.yirendai.workbench.service.IUserKycService;
import com.yirendai.workbench.vo.req.UserKycAddReq;
import com.yirendai.workbench.vo.req.UserKycDetailReq;
import com.yirendai.workbench.vo.res.UserKycDetailRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2024-11-26
 */
@RestController
@RequestMapping("/callservice/userKyc")
@Api(value = "客户资料-客户KYC", tags = "客户资料-客户KYC")
public class UserKycController {

    @Resource
    IUserKycService iUserKycService;

    @PostMapping("/saveOrUpdate")
    @ApiOperation("新增或更新")
    public R<Boolean> saveOrUpdate(@RequestBody UserKycAddReq userKycAddReq) {
        return R.data(iUserKycService.saveOrUpdate(userKycAddReq));
    }

    @PostMapping("/detail")
    @ApiOperation("明细")
    public R<UserKycDetailRes> detail(@RequestBody UserKycDetailReq userKycDetailReq) {
        return R.data(iUserKycService.detail(userKycDetailReq));
    }
}
