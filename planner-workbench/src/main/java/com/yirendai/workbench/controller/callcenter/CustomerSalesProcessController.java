package com.yirendai.workbench.controller.callcenter;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.service.ICallcenterSaleProcessRecordReadonlyService;
import com.yirendai.workbench.service.ICallcenterSaleProcessRecordService;
import com.yirendai.workbench.vo.req.ManualAdvanceConditionReq;
import com.yirendai.workbench.vo.req.ManualFinishProcessReq;
import com.yirendai.workbench.vo.req.callcenter.CallcenterCustomerSalesProcessListReq;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerNodeInfoResp;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerSaleProcessInfoResp;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerSaleProcessRecordResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "客户销售进度查询", tags = "客户销售进度查询")
@RestController
@RequestMapping("/workbench/customerSalesProcessInfo")
@Slf4j
public class CustomerSalesProcessController {

    @Resource
    ICallcenterSaleProcessRecordService iCallcenterSaleProcessRecordService;

    @Autowired
    private ICallcenterSaleProcessRecordReadonlyService saleProcessRecordReadonlyService;

    @ApiOperation(value = "客户销售进度列表查询", notes = "客户销售进度列表查询")
    @PostMapping("/salesProcessInfos")
    public R<Page<CallcenterCustomerSaleProcessRecordResp>> getUserSalesProcessInfoList(@RequestBody CallcenterCustomerSalesProcessListReq req) {

        return R.data(saleProcessRecordReadonlyService.getUserSalesProcessInfoList(req));
    }

    @ApiOperation(value = "客户销售进度详情", notes = "客户销售进度详情")
    @GetMapping("/salesProcessInfo")
    public R<CallcenterCustomerSaleProcessInfoResp> getUserSalesProcessInfo(@RequestParam String userId, @RequestParam String saleProcessRecordId) {

        return R.data(saleProcessRecordReadonlyService.getSalesProcessInfoById(userId,saleProcessRecordId));
    }

    @ApiOperation(value = "根据节点id,查询节点事项列表", notes = "根据节点id,查询节点事项列表")
    @GetMapping("/getCustomerNodeInfo")
    public R<CallcenterCustomerNodeInfoResp> getCustomerNodeInfo(@RequestParam String userId, @RequestParam String nodeId) {

        return R.data(saleProcessRecordReadonlyService.getCustomerNodeInfo(userId,nodeId));
    }

    /**
     * 手动结束流程
     */
    @PostMapping("/manualFinish")
    @ApiOperation(value = "手动结束流程", notes = "根据完成类型手动结束用户的销售流程")
    public R<Boolean> manualFinishProcess(@Valid @RequestBody ManualFinishProcessReq req) {
        try {
            boolean result = iCallcenterSaleProcessRecordService.manualFinishProcess(req.getProcessId(),
                    req.getUserId(), req.getFinishStatus(), req.getManualFinishReason());
            return R.data(result);
        } catch (Exception e) {
            log.error("手动结束流程异常，processId: {}, userId: {}, finishType: {}", req.getProcessId(), req.getUserId(),
                    req.getFinishStatus(), e);
            return R.fail(e.getMessage());
        }
    }

    @PostMapping("/batchManualAdvanceCondition")
    @ApiOperation(value = "批量手动推进", notes = "批量手动推进")
    public R<Boolean> batchManualAdvanceCondition(@RequestBody @Valid ManualAdvanceConditionReq req) {
        return R.data(
                iCallcenterSaleProcessRecordService.batchManualAdvanceCondition(req.getProcessId(), req.getUserId(),
                        req.getConditionItems()));
    }


}
