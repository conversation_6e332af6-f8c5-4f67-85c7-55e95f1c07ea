package com.yirendai.workbench.controller.callcenter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.service.CallCenterStrategyUserAdditionalConfigService;
import com.yirendai.workbench.vo.req.callcenter.StrategyUserAdditionalConfigCreateReq;
import com.yirendai.workbench.vo.req.callcenter.StrategyUserAdditionalConfigImportReq;
import com.yirendai.workbench.vo.req.callcenter.StrategyUserAdditionalConfigQueryReq;
import com.yirendai.workbench.vo.req.callcenter.StrategyUserAdditionalConfigUpdateReq;
import com.yirendai.workbench.vo.res.callcenter.CallCenterStrategyUserAdditionalConfigRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/strategyUserConfig")
@Api(value = "策略用户附加信息管理", tags = "策略用户附加信息管理")
public class CallCenterStrategyUserAdditionalConfigController {

    @Resource
    private CallCenterStrategyUserAdditionalConfigService strategyUserAdditionalConfigService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询策略用户附加信息")
    public R<IPage<CallCenterStrategyUserAdditionalConfigRes>> pageList(@Valid @RequestBody StrategyUserAdditionalConfigQueryReq query) {
        return R.data(strategyUserAdditionalConfigService.pageList(query));
    }

    @PostMapping()
    @ApiOperation(value = "新增策略用户附加信息")
    public R<Boolean> create(@Valid @RequestBody StrategyUserAdditionalConfigCreateReq create) {
        return R.data(strategyUserAdditionalConfigService.create(create));
    }

    @PutMapping("/{id}")
    @ApiOperation(value = "修改策略用户附加信息")
    public R<Boolean> update(@PathVariable("id") Long id, @Valid @RequestBody StrategyUserAdditionalConfigUpdateReq update) {
        return R.data(strategyUserAdditionalConfigService.update(id, update));
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "根据ID删除策略用户附加信息")
    public R<Boolean> deleteById(@PathVariable("id") Integer id) {
        return R.data(strategyUserAdditionalConfigService.deleteById(id));
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "批量删除策略用户附加信息")
    public R<Boolean> deleteBatchByIds(@RequestBody List<Integer> ids) {
        return R.data(strategyUserAdditionalConfigService.deleteBatchByIds(ids));
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询策略用户附加信息")
    public R<CallCenterStrategyUserAdditionalConfigRes> detail(@PathVariable("id") Integer id) {
        return R.data(strategyUserAdditionalConfigService.detail(id));
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入策略用户附加信息-新增或修改")
    public R<Boolean> importDataForUpdate(@Valid @RequestBody StrategyUserAdditionalConfigImportReq importReq) {
        return R.data(strategyUserAdditionalConfigService.importDataForUpdate(importReq));
    }

    @PostMapping("/import-deleted")
    @ApiOperation(value = "导入策略用户附加信息-删除")
    public R<Boolean> importDataForDeleted(@Valid @RequestBody StrategyUserAdditionalConfigImportReq importReq) {
        return R.data(strategyUserAdditionalConfigService.importDataForDeleted(importReq));
    }
}