package com.yirendai.workbench.controller.callcenter;

import java.io.File;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.entity.CallcenterVisitRecord;
import com.yirendai.workbench.enums.VisitStatusEnum;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.service.callcenter.ICallcenterVisitRecordService;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.CallcenterVisitRecordDelReq;
import com.yirendai.workbench.vo.req.CallcenterVisitRecordListReq;
import com.yirendai.workbench.vo.req.CallcenterVisitRecordReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * <p>
 * 拜访表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2025-02-14
 */
@RestController
@RequestMapping("/workbench/visitRecord")
@Api(value = "拜访", tags = "拜访")

public class CallcenterVisitRecordController {

    @Resource
    ICallcenterVisitRecordService iCallcenterVisitRecordService;

    @PostMapping("save")
    @ApiOperation("新增")
    public R<Long> save(CallcenterVisitRecordReq req) {
        if (req.getSummaryType() == 1) {
            List<File> files = iCallcenterVisitRecordService.saveFile(req.getFileList());
            req.setFiles(files);
        }
        req.setUserAccount(OwnAuthUtil.getUserAccount());
        req.setPlannerNo(OwnAuthUtil.getPlannerNo());
        req.setTenantId(OwnAuthUtil.getTenantId());
        CallcenterVisitRecord callcenterVisitRecord = iCallcenterVisitRecordService.saveRecord(req);
        iCallcenterVisitRecordService.save(req, callcenterVisitRecord);
        return R.data(callcenterVisitRecord.getId());
    }

    @PostMapping("list")
    @ApiOperation("列表")
    public R<Page<CallcenterVisitRecord>> list(@RequestBody CallcenterVisitRecordListReq req) {
        Page<CallcenterVisitRecord> page = new Page<>(req.getPageNo(), req.getPageSize());
        LambdaQueryWrapper<CallcenterVisitRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(CallcenterVisitRecord::getStatus, VisitStatusEnum.SIX.getCode());
        queryWrapper.eq(CallcenterVisitRecord::getUserId, req.getUserId());
        queryWrapper.orderByDesc(CallcenterVisitRecord::getCreateTime);
        Page<CallcenterVisitRecord> list = iCallcenterVisitRecordService.page(page, queryWrapper);
        return R.data(list);
    }

    @PostMapping("detail")
    @ApiOperation("详情")
    public R<CallcenterVisitRecord> detail(@RequestBody CallcenterVisitRecordDelReq req) {
        LambdaQueryWrapper<CallcenterVisitRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CallcenterVisitRecord::getUserId, req.getId());
        CallcenterVisitRecord callcenterVisitRecord = iCallcenterVisitRecordService.getById(req.getId());
        return R.data(callcenterVisitRecord);
    }

    @PostMapping("delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody CallcenterVisitRecordDelReq req) {
        CallcenterVisitRecord callcenterVisitRecord = iCallcenterVisitRecordService.getById(req.getId());
        if (Objects.isNull(callcenterVisitRecord)) {
            throw new BusinessException(BusinessError.DATA_NOT_EXIST);
        }
        callcenterVisitRecord.setStatus(VisitStatusEnum.SIX.getCode());
        iCallcenterVisitRecordService.updateById(callcenterVisitRecord);
        return R.data(true);
    }
}
