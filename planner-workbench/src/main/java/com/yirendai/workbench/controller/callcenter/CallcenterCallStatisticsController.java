package com.yirendai.workbench.controller.callcenter;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.service.ICallcenterCallStatisticsService;
import com.yirendai.workbench.vo.req.callcenter.CallStatisticsPageReq;
import com.yirendai.workbench.vo.req.callcenter.saas.CallStatisticsExportReq;
import com.yirendai.workbench.vo.res.callcenter.BatchOperationResult;
import com.yirendai.workbench.vo.res.callcenter.CallStatisticsPageRes;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 呼叫统计数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@RestController
@RequestMapping("/workbench/callcenterCallStatistics")
public class CallcenterCallStatisticsController {

    @Resource
    ICallcenterCallStatisticsService iCallcenterCallStatisticsService;
    @PostMapping("page")
    @ApiOperation(value = "呼叫统计列表")
    public R<Page<CallStatisticsPageRes>> page(@RequestBody CallStatisticsPageReq callStatisticsPageReq) {
        return R.data(iCallcenterCallStatisticsService.page(callStatisticsPageReq));
    }
    @PostMapping("exportTask")
    @ApiOperation(value = "呼叫统计导出任务")
    public R<BatchOperationResult> exportTask(@RequestBody CallStatisticsExportReq callStatisticsExportReq) {
        return R.data(iCallcenterCallStatisticsService.exportTask(callStatisticsExportReq));
    }
}
