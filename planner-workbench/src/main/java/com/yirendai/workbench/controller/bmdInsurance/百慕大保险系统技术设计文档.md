# 百慕大保险系统技术设计文档

## 1. API 概览

### 1.1 系统架构
系统采用 RESTful API 设计，分为三个主要控制器：

1. **保险管理控制器** (BmdInsuranceMgrController)
   - 基础路径: `/bmd-insurance/mgr`
   - 主要负责保险申请的核心业务流程

2. **文件处理控制器** (BmdInsuranceFileController)
   - 基础路径: `/bmd-file`
   - 负责处理证件和 PDF 模板的上传

3. **H5 交互控制器** (BmdInsuranceH5Controller)
   - 基础路径: `/bmd-insurance/h5`
   - 处理 H5 端的文件查看和签署功能

### 1.2 接口分布

```mermaid
graph TB
    A[百慕大保险系统] --> B[保险管理模块]
    A --> C[文件处理模块]
    A --> D[H5交互模块]
    
    B --> B1[申请管理]
    B --> B2[审批流程]
    B --> B3[表单处理]
    
    C --> C1[证件上传]
    C --> C2[PDF模板上传]
    
    D --> D1[文件查看]
    D --> D2[文件签署]
```

---

## 2. 功能模块分析

### 2.1 保险管理模块 (BmdInsuranceMgrController)
- 保险申请列表管理
- 申请创建和提交
- 表单暂存功能
- 审批流程管理
- 签字链接管理

### 2.2 文件处理模块 (BmdInsuranceFileController)
- 证件上传（单文件）
- PDF 模板批量上传

### 2.3 H5 交互模块 (BmdInsuranceH5Controller)
- PDF 文件查看
- 文件签署
- 文件列表管理

---

## 3. 接口详细说明

### 3.1 保险管理接口

#### 3.1.1 申请列表接口
```http
POST /bmd-insurance/mgr/list
Content-Type: application/json
Body: BmdInsuranceListReq
Response: IPage<BmdInsuranceListResp>
```

#### 3.1.2 创建申请
```http
POST /bmd-insurance/mgr/application/create
Response: { "applicationId": "string" }
```

#### 3.1.3 表单提交
```http
POST /bmd-insurance/mgr/submit
Content-Type: application/json
Body: BmdInsuranceFormReq
Response: BmdInsuranceFromResp
```

#### 3.1.4 暂存表单
```http
POST /bmd-insurance/mgr/temporarySubmit
Content-Type: application/json
Body: BmdInsuranceFormReq
Response: BmdInsuranceFromResp
```

#### 3.1.5 审批
```http
POST /bmd-insurance/mgr/approve
Content-Type: application/json
Body: BmdInsuranceApproveReq
Response: Boolean
```

#### 3.1.6 查看签字链接
```http
GET /bmd-insurance/mgr/reviewLink?domainUrl=xxx&applyId=xxx
Response: List<BmdInsuranceReviewUrlResp>
```

### 3.2 文件处理接口

#### 3.2.1 证件上传
```http
POST /bmd-file/uploadCertificate
FormData:
  file: MultipartFile
  applicationId: String
  idType: String
  identityInsurer: String
Response: BmdInsuranceFileUploadResp
```

#### 3.2.2 PDF模板上传
```http
POST /bmd-file/uploadPdfTemplate
FormData:
  files: MultipartFile[]
Response: "上传成功"
```

### 3.3 H5交互接口

#### 3.3.1 文件列表查看
```http
GET /bmd-insurance/h5/view/{applyId}?token=xxx&userRole=xxx
Response: BmdInsuranceH5ReviewResp
```

#### 3.3.2 文件查看
```http
GET /bmd-insurance/h5/view?token=xxx&applyId=xxx&userRole=xxx&pdfType=xxx
Response: PDF 文件流
```

#### 3.3.3 文件签署
```http
POST /bmd-insurance/h5/sign/{applyId}
FormData:
  userRole: String
  signBase: String
Response: Boolean
```

---

## 6. 集成指南

###  接口调用流程

```mermaid
sequenceDiagram
    participant Client
    participant MgrController
    participant FileController
    participant H5Controller
    
    Client->>MgrController: 1. 创建申请
    MgrController-->>Client: 返回applicationId
    
    Client->>MgrController: 2. 填写表单
    MgrController-->>Client: 暂存或下一步
    
    Client->>FileController: 3. 上传证件
    FileController-->>Client: 上传结果
    
    Client->>MgrController: 4. 提交申请
    MgrController-->>Client: 提交结果
    
    Client->>H5Controller: 5. 查看文件
    H5Controller-->>Client: 文件内容
    
    Client->>H5Controller: 6. 签署文件
    H5Controller-->>Client: 签署结果
```

###  常见使用场景

1. 保险申请流程：创建申请 → 填写表单 → 暂存或下一步 → 上传证件 → 提交申请 → 等待审批 → 签署文件
2. 文件管理流程：上传证件 → 上传 PDF 模板 → 查看文件 → 签署文件
3. 审批流程：查看申请列表 → 查看申请详情 → 进行审批 → 生成签字链接

---

## 7. 数据流程图

### 7.1 保险申请主流程

```mermaid
graph TD
    A[开始] --> B[创建申请]
    B --> C[填写表单]
    C --> D{是否暂存?}
    D -->|是| C
    D -->|否| E[上传证件]
    E --> F[提交申请]
    F --> G[审批流程]
    G --> H{审批结果}
    H -->|通过| I[生成签字链接]
    H -->|拒绝| J[结束]
    I --> K[签署文件]
    K --> J
```

### 7.2 组件交互图

```mermaid
graph LR
    A[前端应用] --> B[API网关]
    B --> C[保险管理控制器]
    B --> D[文件处理控制器]
    B --> E[H5控制器]
    
    C --> F[业务服务层]
    D --> F
    E --> F
    
    F --> G[数据访问层]
    F --> H[文件存储服务]
    F --> I[PDF处理服务]
```

---

### 7.3 数据流转说明

1. 申请数据流转：User submits application → Save basic information → Generate application ID → Fill form data → Validate → Persist → Upload certificates → File storage → Associate with application ID → Render PDF → Save
2. 审批数据流转：Query application → Load details → Display data → Approval operation → Update status → Trigger subsequent process → Generate signing link → Notify relevant parties
3. 文件处理流转：Receive file → Validate → Store → File viewing request → Permission verification → Load file → Signing operation → Update file → Update status 