package com.yirendai.workbench.controller;

import com.yirendai.workbench.config.annotation.SaveLogAspect;
import com.yirendai.workbench.mapper.FpcReceptionDateTimeMapper;
import com.yirendai.workbench.service.IFpcReceptionDateTimeService;
import com.yirendai.workbench.vo.res.FpcReceptionDateTimeRes;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/workbench/fpcReceptionDateTime")
@Slf4j
public class FpcReceptionDateTimeController {

    @Autowired
    private FpcReceptionDateTimeMapper fpcReceptionDateTimeMapper;

    @Autowired
    private IFpcReceptionDateTimeService iFpcReceptionDateTimeService;


    @PostMapping("/list")
    @ResponseBody
    public R<FpcReceptionDateTimeRes> listFpcReceptionDateTime(@RequestParam String startDate,
                                                                               @RequestParam String endDate ) {
        return R.data(iFpcReceptionDateTimeService.listFpcReceptionDateTime(startDate,endDate));
    }

    @PostMapping("/update")
    @SaveLogAspect(controllerkey = "FpcReceptionDateTimeController.updateStatusWithinTimeRange")
    public R<String> updateStatusWithinTimeRange(@RequestParam Integer memberNo,
                                                 @RequestParam String startTime,
                                                 @RequestParam String endTime,
                                                 @RequestParam Integer status) {

        //校验所选FPC对应选择时段，是否已生成日历
        if (fpcReceptionDateTimeMapper.countFpcReceptionDateTimeInTimeRange(startTime, endTime, memberNo) <= 0) {
            return R.fail("所选时段未生成，请检查后再试");
        }
        int count = fpcReceptionDateTimeMapper.updateStatusWithinTimeRange(startTime, endTime, status, memberNo);
        return R.data(String.valueOf(count));
    }

    @ApiOperation("更新日程")
    @PostMapping("/renewFpcReception")
    @SaveLogAspect(controllerkey = "FpcReceptionDateTimeController.renewFpcReception")
    public R<Boolean> renewFpcReception() {
        Boolean renew = iFpcReceptionDateTimeService.renewFpcAssignCount();
        return R.data(renew);
    }
}
