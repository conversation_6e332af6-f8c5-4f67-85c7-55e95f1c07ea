package com.yirendai.workbench.controller.callcenter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.entity.CallCenterWechatMessage;
import com.yirendai.workbench.service.ICallCenterWechatContactService;
import com.yirendai.workbench.service.ICallCenterWechatMessageService;
import com.yirendai.workbench.vo.req.*;
import com.yirendai.workbench.vo.res.WechatMessagePageRes;
import com.yirendai.workbench.vo.res.callcenter.PlannerWechatListRes;
import com.yirendai.workbench.vo.res.callcenter.PlannerWechatRes;
import com.yirendai.workbench.vo.res.callcenter.WechatContactListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.File;
import java.util.List;

/**
 * <p>
 * 微信聊天记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/wechat/")
@Api(value = "微信聊天记录管理", tags = "微信聊天记录管理")
public class CallCenterWechatMessageController {

    @Autowired
    private ICallCenterWechatMessageService wechatMessageService;

    @Autowired
    private ICallCenterWechatContactService wechatContactService;

    @PostMapping("/planner/page")
    @ApiOperation(value = "获取理财师微信信息", notes = "获取理财师微信信息")
    public R<Page<PlannerWechatRes>> pagePlannerWechatInfo(@Valid @RequestBody PlannerWechatPageReq request) {
        return R.data(wechatContactService.pagePlannerWechatInfo(request));
    }

    @PostMapping("/planner/contact")
    @ApiOperation(value = "获取理财师微信联系人", notes = "获取理财师微信联系人")
    public R<Page<WechatContactListRes>> pageWechatContact(@Valid @RequestBody PlannerWechatContactPageReq request) {
        return R.data(wechatContactService.pageWechatContact(request));
    }


    @PostMapping("/planner-wechat-list")
    @ApiOperation(value = "获取员工所有微信列表", notes = "根据员工编号获取该员工所有的微信账号）")
    public R<List<PlannerWechatListRes>> getPlannerWechatList(@Validated @RequestBody PlannerWechatListReq request) {
        List<PlannerWechatListRes> result = wechatContactService.getPlannerWechatList(request);
        return R.data(result);
    }

    @PostMapping("/message-page")
    @ApiOperation(value = "分页查询员工和客户聊天记录", notes = "根据员工编号和客户微信ID分页查询聊天记录，支持时间范围过滤")
    public R<Page<WechatMessagePageRes>> pageWechatMessage(@Validated @RequestBody WechatMessagePageReq request) {
        Page<WechatMessagePageRes> result = wechatMessageService.pageWechatMessage(request);
        return R.data(result);
    }

    @GetMapping("/file/{wechatMessageId}")
    @ApiOperation(value = "根据微信聊天记录ID获取NAS音频文件流", notes = "返回存储在NAS上的wav音频文件流")
    public ResponseEntity<Resource> getWechatMessageFile(@PathVariable("wechatMessageId") Long wechatMessageId) {
        try {
            String filePath = wechatMessageService.getWechatMessageFilePath(wechatMessageId);
            if(StringUtils.isBlank(filePath)) {
                log.warn("NAS音频文件不存在: wechatMessageId={}, filePath={}", wechatMessageId, filePath);
                return ResponseEntity.notFound().build();
            }
            File file = new File(filePath);

            if (!file.exists() || !file.isFile()) {
                log.warn("NAS音频文件不存在: wechatMessageId={}, filePath={}", wechatMessageId, filePath);
                return ResponseEntity.notFound().build();
            }

            // 创建文件资源
            Resource fileResource = new FileSystemResource(file);

            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"");

            log.info("成功返回NAS音频文件流: wechatMessageId={}, fileName={}, fileSize={}",
                    wechatMessageId, file.getName(), file.length());

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .contentType(MediaType.parseMediaType("audio/wav"))
                    .body(fileResource);

        } catch (Exception e) {
            log.error("获取微信聊天记录音频文件流失败: wechatMessageId={}", wechatMessageId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


}