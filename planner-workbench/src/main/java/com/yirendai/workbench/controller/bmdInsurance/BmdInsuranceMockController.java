package com.yirendai.workbench.controller.bmdInsurance;

import com.yirendai.workbench.service.bmdInsurance.BmdInsuranceEmailService;
import com.yirendai.workbench.service.bmdInsurance.impl.PdfTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/bmd-insurance/mock")
public class BmdInsuranceMockController {

    @Autowired
    private BmdInsuranceEmailService bmdInsuranceTimer;

    @GetMapping("/sendEmailTest")
    public String sendEmailTest() {
        bmdInsuranceTimer.executorSendEmail();
        return "success";
    }

    @GetMapping("/sendEmail")
    public String sendEmail(Long id) {
        bmdInsuranceTimer.sendEmail(id);
        return "success";
    }

    @Autowired
    private PdfTest pdfTest;
    @GetMapping("/generatedPdf")
    public String generatedPdf(Long id) {
        pdfTest.generatedPdf(id);
        return "success";
    }
}
