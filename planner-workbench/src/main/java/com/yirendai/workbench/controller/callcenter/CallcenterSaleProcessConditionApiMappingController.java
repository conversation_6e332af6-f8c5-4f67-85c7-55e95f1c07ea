package com.yirendai.workbench.controller.callcenter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yirendai.workbench.entity.CallcenterSaleProcessConditionApiMapping;
import com.yirendai.workbench.service.ICallcenterSaleProcessConditionApiMappingService;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessConditionApiMappingListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 销售流程条件配置与接口映射表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2025-10-13
 */
@RestController
@RequestMapping("/workbench/conditionApiMapping")
@Api(tags = "销售流程条件配置与接口映射", value = "销售流程条件配置与接口映射")
public class CallcenterSaleProcessConditionApiMappingController {

    @Resource
    ICallcenterSaleProcessConditionApiMappingService callcenterSaleProcessConditionApiMappingService;

    public static final Map<String, String> DESCRIPTION_MAP = new HashMap<String, String>() {{
        put("EQUALS", "等于");
        put("NOT_EQUALS", "不等于");
        put("GREATER_THAN", "大于");
        put("GREATER_THAN_OR_EQUALS", "大于等于");
        put("LESS_THAN", "小于");
        put("LESS_THAN_OR_EQUALS", "小于等于");
        put("CONTAINS", "包含");
        put("NOT_CONTAINS", "不包含");
        put("IN", "在...中");
        put("NOT_IN", "不在...中");
        put("IS_NULL", "为空");
        put("IS_NOT_NULL", "不为空");
        put("BETWEEN", "在范围内");
        put("NOT_BETWEEN", "不在范围内");
    }};

    @GetMapping("/list")
    @ApiOperation(value = "条件映射列表")
    public R<List<CallcenterSaleProcessConditionApiMappingListRes>> list() {
        String tenantId = OwnAuthUtil.getTenantId();
        List<CallcenterSaleProcessConditionApiMapping> list = callcenterSaleProcessConditionApiMappingService.lambdaQuery()
                .eq(CallcenterSaleProcessConditionApiMapping::getTenantId, tenantId)
                .eq(CallcenterSaleProcessConditionApiMapping::getIsDeleted, 0).list();
        return R.data(list.stream().map(item -> {
            CallcenterSaleProcessConditionApiMappingListRes res = new CallcenterSaleProcessConditionApiMappingListRes();
            res.setId(item.getId());
            res.setParamType(item.getParamType());
            if (item.getParamType() == 4) {
                res.setParamValueList(JsonUtilExt.jsonToBean(item.getParamValue(),
                        new TypeReference<List<CallcenterSaleProcessConditionApiMappingListRes.ParamValue>>() {

                        }));
            }
            res.setName(item.getName());
            if (StringUtils.isNotBlank(item.getOperators())) {
                String[] operatorArray = item.getOperators().split(",");
                List<CallcenterSaleProcessConditionApiMappingListRes.ParamValue> operatorList = new ArrayList<>();
                for (String operator : operatorArray) {
                    operatorList.add(new CallcenterSaleProcessConditionApiMappingListRes.ParamValue(operator,
                            DESCRIPTION_MAP.get(operator)));
                }
                res.setOperators(operatorList);
            }
            return res;
        }).collect(Collectors.toList()));
    }
}
