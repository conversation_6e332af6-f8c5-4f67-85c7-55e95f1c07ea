package com.yirendai.workbench.controller.callcenter;

import javax.annotation.Resource;

import com.yirendai.workbench.service.callcenter.ICallcenterUserRemarkService;
import com.yirendai.workbench.vo.req.UserRemarkAddReq;
import com.yirendai.workbench.vo.req.UserRemarkDetailReq;
import com.yirendai.workbench.vo.res.UserRemarkDetailRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 客户备注表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2024-11-25
 */
@RestController
@RequestMapping("/callservice/userRemark")
@Api(value = "客户资料-客户备注", tags = "客户资料-客户备注")
public class CallcenterUserRemarkController {

    @Resource
    ICallcenterUserRemarkService iCallcenterUserRemarkService;

    @PostMapping("saveOrUpdate")
    @ApiOperation("新增或更新")
    public R<Boolean> saveOrUpdate(@Validated @RequestBody UserRemarkAddReq userRemarkAddReq) {
        return R.data(iCallcenterUserRemarkService.saveOrUpdate(userRemarkAddReq));
    }

    @PostMapping("detail")
    @ApiOperation("详情")
    public R<UserRemarkDetailRes> detail(@Validated @RequestBody UserRemarkDetailReq userRemarkDetailReq) {
        return R.data(iCallcenterUserRemarkService.detail(userRemarkDetailReq));
    }

}
