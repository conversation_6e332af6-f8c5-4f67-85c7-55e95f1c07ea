package com.yirendai.workbench.controller.bmdInsurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.common.ResultCode;
import com.yirendai.workbench.service.bmdInsurance.BmdInsuranceMgrService;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.bmdInsurance.BmdInsuranceApproveReq;
import com.yirendai.workbench.vo.req.bmdInsurance.BmdInsuranceFormReq;
import com.yirendai.workbench.vo.req.bmdInsurance.BmdInsuranceListReq;
import com.yirendai.workbench.vo.res.bmdInsurance.BmdInsuranceFromResp;
import com.yirendai.workbench.vo.res.bmdInsurance.BmdInsuranceListResp;
import com.yirendai.workbench.vo.res.bmdInsurance.BmdInsuranceReviewUrlResp;
import com.yirendai.workbench.vo.res.bmdInsurance.BmdInsuranceSubmitResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 百慕大保险后台接口
 */
@RestController
@RequestMapping("/bmd-insurance/mgr")
@Slf4j
@Api(value = "百慕大保险CRM接口", tags = "百慕大保险CRM接口")
public class BmdInsuranceMgrController {

    @Autowired
    private BmdInsuranceMgrService bmdInsuranceMgrService;

    @PostMapping("/list")
    @ApiOperation(value = "申请列表")
    public R<IPage<BmdInsuranceListResp>> list(@RequestBody BmdInsuranceListReq req) {
        return R.data(bmdInsuranceMgrService.getPageList(req));
    }

//    @PostMapping("/application/create")
//    public R<Map<String, Object>> createApplication() {
//        BmdInsuranceApplyRecordDto recordDto = bmdInsuranceMgrService.createRecord();
//        if (Objects.isNull(recordDto)) {
//            return R.fail("创建申请失败");
//        }
//        Map<String, Object> map = new HashMap<>();
//        map.put("applicationId", recordDto.getApplicationId());
//        map.put("applyId", recordDto.getId());
//        return R.data(map);
//    }

    @GetMapping("/getApplyDetail/{applyId}")
    @ApiOperation(value = "查看详情")
    public R<BmdInsuranceFromResp> getApplyDetail(@PathVariable("applyId") Long applyId) {
        return R.data(bmdInsuranceMgrService.getApplyDetail(applyId));
    }

    @PostMapping("/temporarySubmit")
    @ApiOperation(value = "暂存")
    public R<BmdInsuranceFromResp> temporarySubmit(@RequestBody BmdInsuranceFormReq req) {
        return R.data(bmdInsuranceMgrService.temporarySubmit(req));
    }

    /**
     * 提交保险申请表单
     * <p>
     * 错误码说明(用于前端页面跳转判断):
     * - code = 0: 提交成功
     * - code = 1: 参数为空 - 跳转到基础信息页面
     * - code = 2: 被保人信息有误 - 跳转到被保人信息页面
     * - code = 3: 过往保险信息有误 - 跳转到过往保险信息页面
     * - code = 4: 财务信息有误 - 跳转到财务情况页面
     * - code = 5: 医疗信息有误 - 跳转到医疗信息&健康史页面
     * - code = 6: 第三方参与人信息有误 - 跳转到第三方参与人页面
     * - code = 7: 受益人信息有误 - 跳转到受益人信息页面
     * - code = 8: 其他信息有误 - 跳转到其他信息页面
     * - code = 21: 数据库错误 - 显示错误提示，不跳转
     * - code = 26: PDF处理失败 - 显示错误提示，不跳转
     * - code = 其他: 系统异常 - 显示错误提示，不跳转
     */
    @PostMapping("/submit")
    @ApiOperation(value = "提交", notes = "提交保险申请表单，包含表单校验、数据保存、PDF生成等流程")
    @ApiResponses({
            @ApiResponse(code = 200, message = "统一返回200，通过data中的code判断成功失败")
    })
    public R<BmdInsuranceSubmitResp> submit(@Validated @RequestBody BmdInsuranceFormReq req) {
        log.info("接收到保险申请提交请求，applicationId: {}", req.getApplicationId());
        try {
            BmdInsuranceSubmitResp submitResp = bmdInsuranceMgrService.submit(req);
            if (submitResp.getSuccess()) {
                log.info("保险申请提交成功，applicationId: {}", req.getApplicationId());
            } else {
                log.warn("保险申请提交失败，applicationId: {}, code: {}, message: {}",
                        req.getApplicationId(), submitResp.getCode(), submitResp.getMessage());
            }
            return R.data(submitResp);
        } catch (Exception e) {
            log.error("保险申请提交发生未知错误，applicationId: {}", req.getApplicationId(), e);
            // 统一返回200，错误信息在data中
            return R.data(BmdInsuranceSubmitResp.fail(ResultCode.FAILURE.getCode(), "系统处理异常"));
        }
    }

    @DeleteMapping("/deleteRecord/{id}")
    @ApiOperation(value = "删除申请记录")
    public R<Void> deleteRecord(@PathVariable("id") Long id) {
        bmdInsuranceMgrService.deleteRecord(id);
        return R.success("删除成功");
    }

    @PostMapping("/approve")
    @ApiOperation(value = "审批")
    public R<Boolean> approve(@RequestBody @Validated BmdInsuranceApproveReq req) {
        return R.data(bmdInsuranceMgrService.approve(req));
    }

    @GetMapping("/reviewLink")
    @ApiOperation(value = "查看签字链接")
    public R<List<BmdInsuranceReviewUrlResp>> reviewLink(@RequestParam("applyId") long applyId) {
        return R.data(bmdInsuranceMgrService.reviewLink(applyId));
    }

    @PostMapping("/reCreatePdf")
    public R<String> reCreatePdf(Long recordId){
        String userName = OwnAuthUtil.getUserName();
        bmdInsuranceMgrService.processPdfAndEmailAsync(recordId, userName);
        return R.success("开始处理PDF和邮件");
    }

}
