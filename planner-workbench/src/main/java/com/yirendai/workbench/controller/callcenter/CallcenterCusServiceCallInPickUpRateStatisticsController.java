package com.yirendai.workbench.controller.callcenter;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.service.ICallcenterCusServiceCallInPickUpRateStatisticsService;
import com.yirendai.workbench.vo.req.callcenter.CusServiceCallInPickUpRateStatisticsPageReq;
import com.yirendai.workbench.vo.req.callcenter.saas.CusServiceCallInPickUpRateStatisticsExportReq;
import com.yirendai.workbench.vo.res.callcenter.BatchOperationResult;
import com.yirendai.workbench.vo.res.callcenter.CusServiceCallInPickUpRateStatisticsPageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 客服通话呼入接起率统计表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/workbench/callcenterCusServiceCallInPickUpRateStatistics")
@Api(value = "客服通话呼入接起率统计", tags = "客服通话呼入接起率统计")
public class CallcenterCusServiceCallInPickUpRateStatisticsController {

    @Resource
    ICallcenterCusServiceCallInPickUpRateStatisticsService callcenterCusServiceCallInPickUpRateStatisticsService;

    @PostMapping("page")
    @ApiOperation(value = "呼入接起率统计列表")
    public R<Page<CusServiceCallInPickUpRateStatisticsPageRes>> page(@RequestBody CusServiceCallInPickUpRateStatisticsPageReq req) {
        return R.data(callcenterCusServiceCallInPickUpRateStatisticsService.page(req));
    }

    @PostMapping("exportTask")
    @ApiOperation(value = "呼入接起率统计导出任务")
    public R<BatchOperationResult> exportTask(@RequestBody CusServiceCallInPickUpRateStatisticsExportReq req) {
        return R.data(callcenterCusServiceCallInPickUpRateStatisticsService.exportTask(req));
    }
}
