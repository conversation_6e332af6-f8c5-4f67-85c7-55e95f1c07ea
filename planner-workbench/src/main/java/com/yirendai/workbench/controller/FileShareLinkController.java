package com.yirendai.workbench.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.service.FileShareLinkService;
import com.yirendai.workbench.vo.req.FileShareLinkPageReq;
import com.yirendai.workbench.vo.req.FileShareLinkSaveOrUpdateReq;
import com.yirendai.workbench.vo.req.callcenter.LinkDataDetailReq;
import com.yirendai.workbench.vo.res.FileShareLinkDetailVo;
import com.yirendai.workbench.vo.res.FileShareLinkUploadVo;
import com.yirendai.workbench.model.callcenter.LinkVisitDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

@RestController
@RequestMapping("/fileShareLink")
@Api(value = "文件转链接", tags = "文件转链接")
public class FileShareLinkController {

    @Autowired
    private FileShareLinkService fileShareLinkService;

    @PostMapping("/uploadFile")
    @ApiOperation(value = "上传文件转链接的原始文件")
    public R<FileShareLinkUploadVo> upload(@RequestParam("file") @ApiParam(value = "文件", required = true) MultipartFile file) {
        return R.data(fileShareLinkService.upload(file));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新文件转链接信息")
    public R<Long> saveOrUpdate(@Valid @RequestBody FileShareLinkSaveOrUpdateReq req) {
        return R.data(fileShareLinkService.saveOrUpdate(req));
    }

    @PostMapping("/link-visit-info")
    @ApiOperation(value = "获取链接数据详情", notes = "分页查询链接的用户访问详情数据")
    public R<IPage<LinkVisitDetailVo>> findLinkVisitInfo(@Valid @RequestBody LinkDataDetailReq request) {
        IPage<LinkVisitDetailVo> result = fileShareLinkService.findLinkVisitInfo(request);
        return R.data(result);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "获取文件转链接数据详情", notes = "分页查询文件转链接数据详情")
    public R<IPage<FileShareLinkDetailVo>> detail(@Valid @RequestBody FileShareLinkPageReq req) {
        return R.data(fileShareLinkService.findFileShareLinkDetail(req));
    }

}
