package com.yirendai.workbench.controller.callcenter;

import java.util.List;
import javax.annotation.Resource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.enums.StrategyTaskExecuteActionEnum;
import com.yirendai.workbench.service.callcenter.ICallcenterStrategyTaskService;
import com.yirendai.workbench.vo.req.CallcenterStrategyTaskDetailReq;
import com.yirendai.workbench.vo.req.CallcenterStrategyTaskPageReq;
import com.yirendai.workbench.vo.req.CallcenterStrategyTaskSaveReq;
import com.yirendai.workbench.vo.req.CallcenterStrategyTaskSwitchReq;
import com.yirendai.workbench.vo.req.CallcenterStrategyTaskUpdateReq;
import com.yirendai.workbench.vo.res.CallcenterStrategyTaskDetailRes;
import com.yirendai.workbench.vo.res.CallcenterStrategyTaskGetExecuteActionRes;
import com.yirendai.workbench.vo.res.CallcenterStrategyTaskGetTriggerConditionRes;
import com.yirendai.workbench.vo.res.CallcenterStrategyTaskGetTriggerEventRes;
import com.yirendai.workbench.vo.res.CallcenterStrategyTaskPageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 策略任务表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2025-07-17
 */
@RestController
@RequestMapping("/workbench/callcenterStrategyTask")
@Api(value = "策略任务", tags = "策略任务")
public class CallcenterStrategyTaskController {

    @Resource
    ICallcenterStrategyTaskService iCallcenterStrategyTaskService;

    @PostMapping("/add")
    @ApiOperation(value = "添加策略任务")
    public R<Boolean> add(@RequestBody CallcenterStrategyTaskSaveReq req) {
        return R.data(iCallcenterStrategyTaskService.save(req));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新策略任务")
    public R<Boolean> update(@RequestBody CallcenterStrategyTaskUpdateReq req) {
        return R.data(iCallcenterStrategyTaskService.update(req));
    }

    @PostMapping("list")
    @ApiOperation(value = "策略任务列表")
    public R<Page<CallcenterStrategyTaskPageRes>> list(@RequestBody CallcenterStrategyTaskPageReq req) {
        return R.data(iCallcenterStrategyTaskService.page(req));
    }

    @PostMapping("switchStatus")
    @ApiOperation(value = "切换状态")
    public R<Boolean> switchStatus(@RequestBody CallcenterStrategyTaskSwitchReq req) {
        return R.data(iCallcenterStrategyTaskService.switchStatus(req));
    }

    @PostMapping("detail")
    @ApiOperation(value = "详情")
    public R<CallcenterStrategyTaskDetailRes> detail(@RequestBody CallcenterStrategyTaskDetailReq req) {
        return R.data(iCallcenterStrategyTaskService.detail(req));
    }

    @PostMapping("getTriggerEvent")
    @ApiOperation(value = "获取触发事件")
    public R<List<CallcenterStrategyTaskGetTriggerEventRes>> getTriggerEvent() {
        return R.data(iCallcenterStrategyTaskService.getTriggerEvent());
    }

    @PostMapping("getTriggerCondition")
    @ApiOperation(value = "获取触发条件")
    public R<CallcenterStrategyTaskGetTriggerConditionRes> getTriggerCondition() {
        return R.data(iCallcenterStrategyTaskService.getTriggerCondition());
    }

    @PostMapping("getExecuteAction")
    @ApiOperation(value = "获取执行动作")
    public R<List<CallcenterStrategyTaskGetExecuteActionRes>> getExecuteAction() {
        return R.data(StrategyTaskExecuteActionEnum.convertToResponseList());
    }

}
