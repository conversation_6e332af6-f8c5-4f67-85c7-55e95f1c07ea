package com.yirendai.workbench.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.workbench.entity.PlannerUserBindHistory;
import com.yirendai.workbench.service.IPlannerUserBindHistoryService;
import com.yirendai.workbench.vo.req.PlannerUserBindReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;


@RefreshScope
@RestController
@RequestMapping("/workbench/plannerUserBindHistory")
@RequiredArgsConstructor
@Slf4j
@Api(value = "用户与理财师绑定关系流水管理", tags = "用户与理财师绑定关系流水管理")
public class PlannerUserBindHistoryController {

    @Autowired
    private IPlannerUserBindHistoryService plannerUserBindHistoryService;

    // 列表查询
    @PostMapping("list")
    @ApiOperation(value = "查询列表")
    public R<IPage<PlannerUserBindHistory>> list(PlannerUserBindReq plannerUserBindReq) {
        return plannerUserBindHistoryService.page(plannerUserBindReq);
    }


    // 明细
    @PostMapping("detail")
    @ApiOperation(value = "明细")
    public R<PlannerUserBindHistory> detail(@ApiParam("主键id") @RequestParam Long id) {
        return plannerUserBindHistoryService.detail(id);
    }

    @ApiOperation("更新crm用户名&场景历史表数据")
    @GetMapping(value = "/updateCrmHistory")
    public void updateCrmHistory() {
        plannerUserBindHistoryService.updateCrmDataHistory();
    }


}
