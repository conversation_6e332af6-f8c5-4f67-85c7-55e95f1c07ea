package com.yirendai.workbench.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.workbench.config.annotation.SaveLogAspect;
import com.yirendai.workbench.entity.CsFpcUser;
import com.yirendai.workbench.entity.FpcUserRequestDto;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.service.CsFpcUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RefreshScope
@RestController
@RequestMapping("fpc/user")
@RequiredArgsConstructor
@Slf4j
@Api(value = "fpc用户管理", tags = "fpc用户管理")
public class FpcUserController {
    @Autowired
    private CsFpcUserService csFpcUserService;

    @ApiOperation("fpc用户列表-分页")
    @GetMapping(value = "/getUserPageList")
    public R<IPage<CsFpcUser>> getPageList(FpcUserRequestDto fpcUserRequestDto) {
        IPage<CsFpcUser> records = csFpcUserService.selectPage(fpcUserRequestDto);
        return R.data(records);
    }

    @ApiOperation("fpc用户列表-可用态")
    @GetMapping(value = "/getAbleUserList")
    public R<List<CsFpcUser>> getAbleUserList() {
        List<CsFpcUser> records = csFpcUserService.selectListOfAble();
        return R.data(records);
    }

    @ApiOperation("查看用户详情")
    @GetMapping("/getUserInfo")
    public R<CsFpcUser> getUserInfo(@ApiParam("员工表id") @RequestParam Long id) {
        if (null == id) {
            throw new BusinessException(BusinessError.PARAM_NULL_ERROR);
        }
        CsFpcUser userInfo = csFpcUserService.getUserInfoById(id);
        return R.data(userInfo);
    }

    @ApiOperation("保存用户")
    @PostMapping("/saveUserInfo")
    @SaveLogAspect(controllerkey="FpcUserController.saveUserInfo")
    public R<Boolean> saveUserInfo(CsFpcUser csFpcUser) {
        if (null == csFpcUser) {
            throw new BusinessException(BusinessError.PARAM_NULL_ERROR);
        }
        Boolean save = csFpcUserService.inserOrUpdate(csFpcUser);
        return R.data(save);
    }


    @ApiOperation("初始化日程")
    @PostMapping("/initFpcReception")
    @SaveLogAspect(controllerkey = "FpcUserController.initFpcReception")
    public R<Boolean> initFpcReception() {
        Boolean init = csFpcUserService.initFpcReception();
        return R.data(init);
    }

}
