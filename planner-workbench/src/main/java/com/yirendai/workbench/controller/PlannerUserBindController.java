package com.yirendai.workbench.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.util.ExcelUtil;
import com.yirendai.workbench.entity.PlannerUserBindVo;
import com.yirendai.workbench.service.IPlannerUserBindService;
import com.yirendai.workbench.vo.req.PlannerUserBindReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


@RefreshScope
@RestController
@RequestMapping("/workbench/plannerUserBind")
@RequiredArgsConstructor
@Slf4j
@Api(value = "用户与理财师绑定关系管理", tags = "用户与理财师绑定关系管理")
public class PlannerUserBindController {
    @Autowired
    private IPlannerUserBindService plannerUserBindService;

    @Resource
    private KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;

    @Value("${kafka.consumer.group.id}")
    String kafkaConsumerGroupId ;

    /*@ApiOperation("初始化avaya数据")
    @GetMapping(value = "/initAvaya")
    public void initAvaya() {
        plannerUserBindService.initAvayaData();
    }

    @ApiOperation("初始化crm数据")
    @GetMapping(value = "/initCrm")
    public void initCrm() {
        plannerUserBindService.initCrmData();
    }*/

    @ApiOperation("更新crm用户名&场景数据")
    @GetMapping(value = "/updateCrm")
    public void updateCrm() {
        plannerUserBindService.updateCrmData();
    }


    @GetMapping("/kafka/stop")
    @ApiOperation(value="停止kafka接收数据")
    public void stop() {
        log.info("stop consumer kafka start");
        MessageListenerContainer listenerContainer = kafkaListenerEndpointRegistry.getListenerContainer(kafkaConsumerGroupId);
        listenerContainer.stop();
        log.info("stop consumer kafka end");
    }

    @GetMapping("/kafka/start")
    @ApiOperation(value="开启kafka接收数据")
    public void start() {
        log.info("start consumer kafka start");
        MessageListenerContainer listenerContainer = kafkaListenerEndpointRegistry.getListenerContainer(kafkaConsumerGroupId);
        listenerContainer.start();
        log.info("start consumer kafka end");
    }


    // 列表查询
    @PostMapping("list")
    @ApiOperation(value = "查询列表")
    public R<IPage<PlannerUserBindVo>> list(PlannerUserBindReq plannerUserBindReq) {
        return plannerUserBindService.page(plannerUserBindReq);
    }


    // 明细
    @PostMapping("detail")
    @ApiOperation(value = "明细")
    public R<PlannerUserBindVo> detail(@ApiParam("主键id") @RequestParam Long id) {
        return plannerUserBindService.detail(id);
    }

    // 导出
    @GetMapping("export")
    @ApiOperation(value = "导出")
    public void export(HttpServletResponse response, PlannerUserBindReq plannerUserBindReq) throws IOException {
        List<PlannerUserBindVo> list = plannerUserBindService.list(plannerUserBindReq);
        ExcelUtil.exportToResponse(response, "客户理财师绑定关系", "客户理财师绑定关系", list, PlannerUserBindVo.class);
    }

    // 组别列表查询
    @PostMapping("groupList")
    @ApiOperation(value = "组别列表")
    public R<List<String>> getGroupList() {
        return plannerUserBindService.getGroupList();
    }

    @GetMapping("test")
    public void  test(){
         plannerUserBindService.initAvayaData();
    }

}
