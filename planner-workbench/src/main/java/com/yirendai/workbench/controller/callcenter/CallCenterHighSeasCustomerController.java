package com.yirendai.workbench.controller.callcenter;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yirendai.workbench.exception.BusinessError;
import com.yirendai.workbench.exception.BusinessException;
import com.yirendai.workbench.service.callcenter.CallCenterHighSeasCustomerService;
import com.yirendai.workbench.service.importExcel.easyExcel.CommonTemplateCellWriteHandler;
import com.yirendai.workbench.vo.req.callcenter.*;
import com.yirendai.workbench.vo.res.callcenter.BatchImportRegisterUserExcelRes;
import com.yirendai.workbench.vo.res.callcenter.BatchImportVirtualUserExcelRes;
import com.yirendai.workbench.vo.res.callcenter.BatchOutBoundErrorExcelRes;
import com.yirendai.workbench.vo.res.callcenter.HighSeasCustomerListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springblade.core.excel.listener.DataListener;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.yirendai.workbench.exception.BusinessError.ERROR_FILE_PARSING;

/**
 * <AUTHOR>
 * @time 2025/3/26 18:59
 **/
@Api(tags = "公海客户相关接口", value = "公海客户相关接口")
@RestController
@RequestMapping("/callservice/highSeasCustomer")
@Slf4j
public class CallCenterHighSeasCustomerController {
    @Resource
    private CallCenterHighSeasCustomerService highSeasCustomerService;

    @PostMapping("/list")
    @ApiOperation(value = "公开客户列表")
    public R<Page<HighSeasCustomerListRes>> list(@RequestBody HighSeasCustomerListReq req) {
        return R.data(highSeasCustomerService.listHighSeasCustomerPage(req));
    }

    @DeleteMapping("/outBound/{userId}")
    @ApiOperation("出库")
    public R<Boolean> outBound(@PathVariable(value = "userId") String userId) {
        return R.data(highSeasCustomerService.outBound(userId));
    }

    @PostMapping("/allocation")
    @ApiOperation("分配理财师")
    public R<Boolean> allocation(@RequestBody AllocationPlannerReq req) {
        return R.data(highSeasCustomerService.allocation(req));
    }

    @PostMapping("/batchOutBound")
    @ApiOperation(value = "批量出库")
    public void batchOutBound(MultipartFile file, HttpServletResponse response) {
        List<BatchOutBoundErrorExcelRes> list = batchOutBound(file);
        if (CollectionUtils.isEmpty(list)) {
            writeSuccessJson(response);
            return;
        }
        try {
            String fileName = URLEncoder.encode(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                    + "批量出库结果", "UTF-8").replaceAll("\\+", "%20");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), BatchOutBoundErrorExcelRes.class)
                    .registerWriteHandler(new CommonTemplateCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (Exception e) {
            log.error("批量出库失败", e);
            throw new BusinessException(BusinessError.ERROR_FILE_IMPORT, e);
        }
    }

    @PostMapping("/registered-user/import")
    @ApiOperation(value = "注册用户导入")
    public void registeredUserImport(MultipartFile file, HttpServletResponse response) {
        List<BatchImportRegisterUserExcelRes> list = handlerRegisteredUserImport(file);
        if (CollectionUtils.isEmpty(list)) {
             writeSuccessJson(response);
            return;
        }
        try {
            String fileName = URLEncoder.encode(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                    + "注册用户导入结果", "UTF-8").replaceAll("\\+", "%20");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), BatchImportRegisterUserExcelRes.class)
                    .registerWriteHandler(new CommonTemplateCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (Exception e) {
            log.error("注册用户导入失败", e);
            throw new BusinessException(BusinessError.ERROR_FILE_IMPORT, e);
        }
    }

    @PostMapping("/virtual-user/import")
    @ApiOperation(value = "虚拟用户导入")
    public void virtualUserImport(MultipartFile file, HttpServletResponse response) {
        List<BatchImportVirtualUserExcelRes> list = handlerVirtualUserImport(file);
        if (CollectionUtils.isEmpty(list)) {
            writeSuccessJson(response);
            return;
        }
        try {
            String fileName = URLEncoder.encode(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                    + "虚拟用户导入结果", "UTF-8").replaceAll("\\+", "%20");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), BatchImportVirtualUserExcelRes.class)
                    .registerWriteHandler(new CommonTemplateCellWriteHandler()).sheet("sheet1").doWrite(list);
        } catch (Exception e) {
            log.error("虚拟用户导入失败", e);
            throw new BusinessException(BusinessError.ERROR_FILE_IMPORT, e);
        }
    }

    /**
     * 虚拟用户导入
     */
    private List<BatchImportVirtualUserExcelRes> handlerVirtualUserImport(MultipartFile file) {
        DataListener<BatchImportVirtualUserExcelReq> dataListener = new DataListener<>();
        try {
            EasyExcel.read(file.getInputStream(), BatchImportVirtualUserExcelReq.class, dataListener).doReadAll();
        } catch (Exception e) {
            throw new BusinessException(ERROR_FILE_PARSING);
        }
        List<BatchImportVirtualUserExcelReq> list = dataListener.getDataList();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return highSeasCustomerService.handlerVirtualUserImport(list);
    }

    /**
     * 注册用户导入
     */
    private List<BatchImportRegisterUserExcelRes> handlerRegisteredUserImport(MultipartFile file) {
        DataListener<BatchImportRegisterUserExcelReq> dataListener = new DataListener<>();
        try {
            EasyExcel.read(file.getInputStream(), BatchImportRegisterUserExcelReq.class, dataListener).doReadAll();
        } catch (Exception e) {
            throw new BusinessException(ERROR_FILE_PARSING);
        }
        List<BatchImportRegisterUserExcelReq> list = dataListener.getDataList();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return highSeasCustomerService.handlerRegisteredUserImport(list);
    }


    private List<BatchOutBoundErrorExcelRes> batchOutBound(MultipartFile file) {
        DataListener<BatchOutBoundImportExcelReq> dataListener = new DataListener<>();
        try {
            EasyExcel.read(file.getInputStream(), BatchOutBoundImportExcelReq.class, dataListener).doReadAll();
        } catch (Exception e) {
            throw new BusinessException(ERROR_FILE_PARSING);
        }
        List<BatchOutBoundImportExcelReq> list = dataListener.getDataList();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return highSeasCustomerService.processBatchOutBound(list);
    }

    public static  void writeSuccessJson(HttpServletResponse response){
        try {
            response.setContentType("application/json");
            response.setCharacterEncoding(Charsets.UTF_8.name());
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write("{\"code\":200,\"msg\":\"导入成功\",\"success\": true}".getBytes());
        } catch (Exception e) {
            log.error("template error", e);
        }finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {log.error("", e);}
        }
    }

}
