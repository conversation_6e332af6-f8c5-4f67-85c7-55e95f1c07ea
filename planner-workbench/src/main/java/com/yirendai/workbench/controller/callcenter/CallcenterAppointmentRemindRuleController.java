package com.yirendai.workbench.controller.callcenter;

import java.io.IOException;
import java.util.Objects;
import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import com.yirendai.workbench.entity.CallcenterAppointmentRemindRule;
import com.yirendai.workbench.service.ICallcenterAppointmentRemindRuleService;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.res.AppointmentRemindRuleDetailRes;
import com.yirendai.workbench.vo.res.AppointmentRemindRuleSaveRes;
import com.yirendai.workbench.websocket.WebSocketHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.socket.TextMessage;

/**
 * <p>
 * 预约提醒规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@RestController
@RequestMapping("/workbench/appointmentRemindRule")
@Api(value = "预约提醒规则", tags = "预约提醒规则")
public class CallcenterAppointmentRemindRuleController {

    @Resource
    private ICallcenterAppointmentRemindRuleService callcenterAppointmentRemindRuleService;

    @GetMapping("/detail")
    @ApiOperation("预约提醒规则明细")
    public R<AppointmentRemindRuleDetailRes> detail(){
        CallcenterAppointmentRemindRule callcenterAppointmentRemindRule = callcenterAppointmentRemindRuleService.lambdaQuery().eq(CallcenterAppointmentRemindRule::getTenantId,
                OwnAuthUtil.getTenantId()).one();
        AppointmentRemindRuleDetailRes appointmentRemindRuleDetailRes = null;
        if(Objects.nonNull(callcenterAppointmentRemindRule)){
            appointmentRemindRuleDetailRes = BeanUtil.copyProperties(callcenterAppointmentRemindRule, AppointmentRemindRuleDetailRes.class);
        }else {
            appointmentRemindRuleDetailRes = new AppointmentRemindRuleDetailRes();
            appointmentRemindRuleDetailRes.setOpenStatus(1);
            appointmentRemindRuleDetailRes.setRemindTime("15");
        }
        return R.data(appointmentRemindRuleDetailRes);
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("新增或更新")
    public R<Boolean> saveOrUpdate(@Validated @RequestBody AppointmentRemindRuleSaveRes appointmentRemindRuleSaveRes){
        CallcenterAppointmentRemindRule callcenterAppointmentRemindRule = BeanUtil.copyProperties(appointmentRemindRuleSaveRes, CallcenterAppointmentRemindRule.class);
        callcenterAppointmentRemindRule.setTenantId(OwnAuthUtil.getTenantId());
        callcenterAppointmentRemindRuleService.saveOrUpdate(callcenterAppointmentRemindRule);
        return R.data(true);
    }

    @GetMapping("/send")
    @ApiOperation("测试发送接口")
    public R send(String text){
        try {
            WebSocketHandler.sessionMap.get(AuthUtil.getTenantId() + "_" + OwnAuthUtil.getPlannerNo()).sendMessage(new TextMessage(text));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return R.data("");
    }
}
