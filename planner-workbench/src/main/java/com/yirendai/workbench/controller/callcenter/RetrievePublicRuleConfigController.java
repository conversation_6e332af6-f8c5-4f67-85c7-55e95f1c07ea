package com.yirendai.workbench.controller.callcenter;

import com.yirendai.workbench.entity.CallcenterRetrieveRule;
import com.yirendai.workbench.service.callcenter.CallcenterRetrieveRuleService;
import com.yirendai.workbench.vo.req.callcenter.PublicRuleConfigReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/retrieveUserConfig/public")
@RequiredArgsConstructor
@Slf4j
@Api(value = "公海领取用户规则配置相关接口", tags = "领取用户规则配置相关接口")
public class RetrievePublicRuleConfigController {

    @Autowired
    private CallcenterRetrieveRuleService callcenterRetrieveRuleService;

    @GetMapping("/getRule")
    @ApiOperation("获取规则")
    public R<CallcenterRetrieveRule> getPublicRule() {
        return R.data(callcenterRetrieveRuleService.getTenantPublicRule());
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("添加或修改公海配置")
    public R<Boolean> addPublicRule(@Validated @RequestBody PublicRuleConfigReq publicRuleConfigReq) {
        return R.data(callcenterRetrieveRuleService.saveOrUpdatePublicRule(publicRuleConfigReq));
    }

}
