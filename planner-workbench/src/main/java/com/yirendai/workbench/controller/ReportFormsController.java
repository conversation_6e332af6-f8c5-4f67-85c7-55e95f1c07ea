package com.yirendai.workbench.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.voiceaiserver.common.R;
import com.yirendai.workbench.entity.ReportFormsConfigDetail;
import com.yirendai.workbench.entity.ReportFormsDirectory;
import com.yirendai.workbench.service.ReportFormsService;
import com.yirendai.workbench.vo.res.callcenter.BatchOperationResult;
import com.yirendai.workbench.wrapper.dto.LingXiangProjectAndViewSimpleInfo;
import com.yirendai.workbench.wrapper.dto.LingXiangQueryDataRequest;
import com.yirendai.workbench.wrapper.dto.LingXiangViewLocalConfig;
import com.yirendai.workbench.wrapper.dto.ReportFormsConfigSimpleVO;
import com.yirendai.workbench.wrapper.dto.ReportFormsDirectoryAddRequest;
import com.yirendai.workbench.wrapper.dto.ReportFormsDirectoryEditRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/report/forms")
@RequiredArgsConstructor
@Slf4j
@Api(value = "报表配置和展示相关", tags = "报表相关")
public class ReportFormsController {

    @Autowired
    private ReportFormsService reportFormsService;

    @ApiOperation(value = "1、查询当前配置信息（ 有则展示编辑信息  没有则进入新增页面）")
    @GetMapping("/getConfigInfo")
    public R<ReportFormsConfigSimpleVO> getConfigInfo() {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.getConfigInfo());
    }

    @ApiOperation(value = "2、查询所有已配置的规则")
    @GetMapping("/getConfigDetail")
    public R<List<LingXiangViewLocalConfig>> getConfigDetail() {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.getConfigDetail());
    }

    @ApiOperation(value = "3、查询已经配置的项目和对应的view信息")
    @GetMapping("/getLocalConfigProjectAndViews")
    public R<List<LingXiangProjectAndViewSimpleInfo>> getLocalConfigProjectAndViews() {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.getLocalConfigProjectAndViews());
    }

    @ApiOperation(value = "4、查询宜人渠道下的项目和对应的view信息")
    @GetMapping("/getLxProjectAndViews")
    public R<List<LingXiangProjectAndViewSimpleInfo>> getLxProjectAndViews() {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.getLxProjectAndViews());
    }

    @ApiOperation(value = "5、根据灵象view查询配置信息（灵象配置和本地配置）")
    @PostMapping("/getLxViewConfig")
    public R<List<LingXiangViewLocalConfig>> getLxViewConfig(@RequestBody List<LingXiangProjectAndViewSimpleInfo> req) {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.getLxViewConfig(req));
    }

    @ApiOperation(value = "6、保存配置规则")
    @PostMapping("/saveLxViewConfig")
    public R<Boolean> saveLxViewConfig(@RequestBody List<LingXiangViewLocalConfig> req) {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.saveLxViewConfig(req));
    }

    @ApiOperation(value = "7、根据projectId和viewId查询单个本地配置")
    @GetMapping("/getLxProjectAndViews/{projectId}/{viewId}")
    public R<ReportFormsConfigDetail> getSingleLocalConfig(@PathVariable("projectId") String projectId,
        @PathVariable("viewId") String viewId){
        return R.data(reportFormsService.getSingleLocalConfig(projectId, viewId));
    }

    @ApiOperation(value = "8、查询灵象数据")
    @PostMapping("/query")
    public R<IPage<Object>> query(@RequestBody LingXiangQueryDataRequest req) {
        return R.data(reportFormsService.queryLingXiangData_v2(req));
    }

    @ApiOperation(value = "9、导出灵象数据")
    @PostMapping("/export")
    public R<BatchOperationResult> export(@RequestBody LingXiangQueryDataRequest req) {
        return R.data(reportFormsService.exportDataToExcel(req));
    }

    @ApiOperation("10、查询所有目录")
    @GetMapping("/directory/getTree")
    public R<List<ReportFormsDirectory>> getChildrenByParentId() {
        List<ReportFormsDirectory> children = reportFormsService.getTree();
        return R.data(children);
    }

    @ApiOperation(value = "11、新增目录或者根节点")
    @PostMapping("/directory/add")
    public R<Boolean> addDirectory(@RequestBody ReportFormsDirectoryAddRequest req) {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.addDirectory(req));
    }

    @ApiOperation(value = "12、编辑目录或者根节点")
    @PostMapping("/directory/edit")
    public R<Boolean> editDirectory(@RequestBody ReportFormsDirectoryEditRequest req) {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.editDirectory(req));
    }

    @ApiOperation(value = "13、删除目录或者根节点")
    @GetMapping("/directory/delete/{nodeId}")
    public R<Boolean> deleteDirectory(@PathVariable("nodeId") Integer nodeId) {
        //暂时只支持宜人，待扩展
        return R.data(reportFormsService.deleteDirectory(nodeId));
    }
}
