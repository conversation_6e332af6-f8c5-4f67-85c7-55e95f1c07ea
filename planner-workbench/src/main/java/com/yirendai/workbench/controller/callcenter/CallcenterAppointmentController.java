package com.yirendai.workbench.controller.callcenter;

import javax.annotation.Resource;

import com.yirendai.workbench.service.callcenter.ICallcenterAppointmentService;
import com.yirendai.workbench.vo.req.AppointmentAddReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 预约表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2024-11-27
 */
@RestController
@RequestMapping("/callservice/appointment")
@Api(value = "预约", tags = "预约")
public class CallcenterAppointmentController {

    @Resource
    ICallcenterAppointmentService iCallcenterAppointmentService;

    @PostMapping("saveOrUpdate")
    @ApiOperation("新增或更新")
    public R<Boolean> saveOrUpdate(@Validated @RequestBody AppointmentAddReq appointmentAddReq) {
        return R.data(iCallcenterAppointmentService.saveOrUpdate(appointmentAddReq));
    }
}
