package com.yirendai.workbench.controller.bmdInsurance;

import com.yirendai.workbench.enums.bmdInsureance.IdTypeEnums;
import com.yirendai.workbench.enums.bmdInsureance.IdentityInsurerEnums;
import com.yirendai.workbench.service.bmdInsurance.BmdInsuranceFileService;
import com.yirendai.workbench.vo.res.bmdInsurance.BmdInsuranceFileUploadResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/bmd-file")
@Slf4j
@Api(value = "百慕大保险文件上传接口", tags = "百慕大保险文件上传接口")
public class BmdInsuranceFileController {

    @Autowired
    private BmdInsuranceFileService bmdInsuranceFileService;

    @PostMapping("/uploadCertificate")
    @ApiOperation(value = "上传证件", notes = "单张上传")
    public R<BmdInsuranceFileUploadResp> upload(@RequestParam("file") @ApiParam(value = "文件", required = true) MultipartFile file,
                                                @RequestParam("applicationId") @ApiParam(value = "应用程序Id", required = true) String applicationId,
                                                @RequestParam("idType") @ApiParam(value = "证件类型", required = true) String idType,
                                                @RequestParam("identityInsurer") @ApiParam(value = "角色类型", required = true) String identityInsurer) {
        IdTypeEnums idTypeEnums = IdTypeEnums.valueOf(idType);
        IdentityInsurerEnums identityInsurerEnums = IdentityInsurerEnums.valueOf(identityInsurer);
        return R.data(bmdInsuranceFileService.uploadFile(file, applicationId, idTypeEnums, identityInsurerEnums));
    }

    @PostMapping("/uploadPdfTemplate")
    @ApiOperation(value = "上传pdf模板", notes = "批量上传")
    public R<String> uploadPdfTemplate(@RequestParam("files") @ApiParam(value = "文件", required = true) MultipartFile[] files) {
        bmdInsuranceFileService.uploadPdfTemplate(java.util.Arrays.asList(files));
        return R.data("上传成功");
    }

}
