package com.yirendai.workbench.controller;

import com.yirendai.workbench.config.annotation.SaveLogAspect;
import com.yirendai.workbench.entity.FpcAssignCount;
import com.yirendai.workbench.entity.FpcAssignCountHis;
import com.yirendai.workbench.entity.FpcAssignCountVo;
import com.yirendai.workbench.service.FpcAssignCountHisService;
import com.yirendai.workbench.service.FpcAssignCountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RefreshScope
@RestController
@RequestMapping("fpc/assignCount")
@RequiredArgsConstructor
@Slf4j
@Api(value = "fpc分派计数管理", tags = "fpc分派计数管理")
public class FpcAssignCountController {
    @Autowired
    private FpcAssignCountService fpcAssignCountService;
    @Autowired
    private FpcAssignCountHisService fpcAssignCountHisService;

    @ApiOperation("当前分派计数列表")
    @GetMapping(value = "/getCurList")
    public R<List<FpcAssignCountVo>> getCurList() {
        List<FpcAssignCountVo> records = fpcAssignCountService.selectAllList();
        return R.data(records);
    }

    @ApiOperation("历史分配计数列表")
    @GetMapping(value = "/getHisList")
    public R<List<FpcAssignCountVo>> getHisList(String endTime) {
        List<FpcAssignCountVo> records = fpcAssignCountHisService.selectListByEndTime(endTime);
        return R.data(records);
    }

    @ApiOperation("开始新计数 - 人工触发新计数")
    @PostMapping("/initAssignCount")
    @SaveLogAspect(controllerkey = "FpcAssignCountController.initAssignCount")
    public R<Boolean> initAssignCount() {
        Boolean init = fpcAssignCountService.updateFpcAssignCountByArtificial();
        return R.data(init);
    }

}
