package com.yirendai.workbench.controller;

import com.yirendai.workbench.job.callcenter.anmi.SyncWechatContact;
import com.yirendai.workbench.job.callcenter.anmi.SyncWechatMessage;
import com.yirendai.workbench.job.callcenter.anmi.SyncWechatMessageByTime;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
@RestController
public class DemoController {

    @Resource
    private SyncWechatMessage syncWechatMessage;

    @Resource
    private SyncWechatContact syncWechatContact;

    @Resource
    private SyncWechatMessageByTime syncWechatMessageByTime;

    @GetMapping("/")
    public R<String> test() {
        return R.data("ok");
    }

    @PostMapping("test")
    public R<String> test2() {
        return R.data("test");
    }

    @PostMapping("/test3")
    public R<String> test3() {
        syncWechatMessageByTime.syncWechatMessageByTime();
        return R.data("success");
    }
}