package com.yirendai.workbench.model;

import lombok.Getter;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务匹配上下文
 */
@Getter
public class BusinessMatchContext {
    private final String content;
    private final Integer source;
    private final Map<Class<?>, Object> serviceMap;

    private BusinessMatchContext(String content, Integer source) {
        this.content = content;
        this.source = source;
        this.serviceMap = new HashMap<>();
    }

    @SuppressWarnings("unchecked")
    public <T> T getService(Class<T> serviceClass) {
        return (T) serviceMap.get(serviceClass);
    }

    public static class Builder {
        private String content;
        private Integer source;
        private final Map<Class<?>, Object> serviceMap = new HashMap<>();

        public Builder content(String content) {
            this.content = content;
            return this;
        }

        public Builder source(Integer source) {
            this.source = source;
            return this;
        }

        public <T> Builder service(Class<T> serviceClass, T service) {
            serviceMap.put(serviceClass, service);
            return this;
        }

        public BusinessMatchContext build() {
            BusinessMatchContext context = new BusinessMatchContext(content, source);
            context.serviceMap.putAll(serviceMap);
            return context;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}