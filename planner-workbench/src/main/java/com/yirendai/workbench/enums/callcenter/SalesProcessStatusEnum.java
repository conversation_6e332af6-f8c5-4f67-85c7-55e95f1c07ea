package com.yirendai.workbench.enums.callcenter;

import lombok.Getter;

/**
 * 销售流程版本状态
 *
 **/
public enum SalesProcessStatusEnum {


    UNPUBLISH(1, "未发布"),
    PUBLISH(2, "已发布"),
    PAUSE(3, "已暂停"),
    STOP(4, "已终止"),
        ;

    @Getter
    private Integer code;
    @Getter
    private String name;

    SalesProcessStatusEnum(Integer code, String name){
        this.code = code;
        this.name = name;
    }

    public static String getDescByCode(Integer code){
        if(code == null){
            return null;
        }
        for(SalesProcessStatusEnum statusEnum : SalesProcessStatusEnum.values()){
            if(code.equals(statusEnum.getCode())){
                return statusEnum.getName();
            }
        }
        return null;
    }

    public static SalesProcessStatusEnum getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(SalesProcessStatusEnum statusEnum : SalesProcessStatusEnum.values()){
            if(code.equals(statusEnum.getCode())){
                return statusEnum;
            }
        }
        return null;
    }
}
