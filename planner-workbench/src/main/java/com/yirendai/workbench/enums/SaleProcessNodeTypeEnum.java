package com.yirendai.workbench.enums;

public enum SaleProcessNodeTypeEnum {
    START(0, "开始节点"),
    CRITICAL(1, "关键节点"),
    NOT_CRITICAL(2, "非关键节点"),
    END(3, "结束节点"),
    ;

    private int code;
    private String message;

    SaleProcessNodeTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static SaleProcessNodeTypeEnum getByCode(Integer code) {
        for (SaleProcessNodeTypeEnum i : SaleProcessNodeTypeEnum.values()) {
            if (i.getCode() == code) {
                return i;
            }
        }
        return null;
    }
}
