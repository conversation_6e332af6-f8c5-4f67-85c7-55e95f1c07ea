package com.yirendai.workbench.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微信消息来源枚举
 */
@Getter
@AllArgsConstructor
public enum WechatMessageSourceEnum {

    /**
     * 企业微信
     */
    ENTERPRISE_WECHAT(1, "企微"),

    /**
     * 个人微信
     */
    PERSONAL_WECHAT(2, "个微");

    /**
     * 来源编码
     */
    private final Integer code;

    /**
     * 来源描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 来源编码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static WechatMessageSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WechatMessageSourceEnum source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 来源编码
     * @return 对应的描述，如果没找到返回null
     */
    public static String getDescriptionByCode(Integer code) {
        WechatMessageSourceEnum source = getByCode(code);
        return source != null ? source.getDescription() : null;
    }

    /**
     * 验证编码是否有效
     *
     * @param code 来源编码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
