package com.yirendai.workbench.enums;

/**
 * <AUTHOR>
 * @time 2025/10/10 17:19
 **/
public enum SimpleOperator {
    EQUALS,                    // 等于
    NOT_EQUALS,               // 不等于
    GREATER_THAN,             // 大于
    GREATER_THAN_OR_EQUALS,   // 大于等于
    LESS_THAN,                // 小于
    LESS_THAN_OR_EQUALS,      // 小于等于
    CONTAINS,                 // 包含
    NOT_CONTAINS,             // 不包含
    IN,                       // 在...中
    NOT_IN,                   // 不在...中
    IS_NULL,                  // 为空
    IS_NOT_NULL,              // 不为空
    BETWEEN,                  // 在范围内（闭区间）
    NOT_BETWEEN               // 不在范围内
}
