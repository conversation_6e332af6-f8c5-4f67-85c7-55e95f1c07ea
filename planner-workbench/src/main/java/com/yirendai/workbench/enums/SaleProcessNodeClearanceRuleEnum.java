package com.yirendai.workbench.enums;

import java.util.Optional;

public enum SaleProcessNodeClearanceRuleEnum {
    ALL(0, "节点条件项均达标"),
//    ANY(1, "节点条件项任一达标"),
//    NEXT_CRITICAL(2, "下级关键节点准出达标"),
    ALL_OR_NEXT_CRITICAL(3, "节点条件项均达标 or 下级关键节点准出达标"),

//    ANY_OR_NEXT_CRITICAL(4, "节点条件项任一达标 or 下级关键节点准出达标"),
    ;

    public final Integer code;
    public final String message;

    SaleProcessNodeClearanceRuleEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static Optional<SaleProcessNodeClearanceRuleEnum> getByCode(Integer code) {
        for (SaleProcessNodeClearanceRuleEnum i : SaleProcessNodeClearanceRuleEnum.values()) {
            if (i.getCode().equals(code)) {
                return Optional.of(i);
            }
        }
        return Optional.empty();
    }
}
