package com.yirendai.workbench.enums.callcenter;


import com.yirendai.workbench.model.BusinessMatchContext;
import com.yirendai.workbench.service.FileShareLinkService;
import com.yirendai.workbench.service.callcenter.CallcenterUserService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 微信消息业务类型枚举
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum WechatMessageBizTypeEnum {

    /**
     * 企划书下发
     */
    PLAN_BOOK_DISTRIBUTION(1, "计划书下发", context -> {
        if (StringUtils.isBlank(context.getContent())) {
            return false;
        }
        FileShareLinkService fileShareLinkService = context.getService(FileShareLinkService.class);
        if (fileShareLinkService == null) {
            log.warn("文件分享服务未注入");
            return false;
        }
        return fileShareLinkService.isPlanBookDistribution(context.getContent());
    }),

    ;

    private final Integer code;
    private final String description;
    private final BusinessMatcher matcher;

    @FunctionalInterface
    interface BusinessMatcher {
        boolean matches(BusinessMatchContext context);
    }

    public boolean matches(BusinessMatchContext context) {
        return matcher.matches(context);
    }

    public static WechatMessageBizTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WechatMessageBizTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static String getDescriptionByCode(Integer code) {
        WechatMessageBizTypeEnum type = getByCode(code);
        return type != null ? type.getDescription() : null;
    }
}