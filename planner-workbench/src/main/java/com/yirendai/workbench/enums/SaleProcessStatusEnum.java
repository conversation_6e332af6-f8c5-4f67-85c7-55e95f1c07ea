package com.yirendai.workbench.enums;

public enum SaleProcessStatusEnum {
    PROCESSING(0, "进行中"),
    SUCCESS(1, "已赢单"),
    FAIL(2, "已败单"),
    ;

    private int code;
    private String message;

    SaleProcessStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getMessageByCode(int code){
        for(SaleProcessStatusEnum statusEnum: SaleProcessStatusEnum.values()){
            if(code == statusEnum.getCode()){
                return statusEnum.getMessage();
            }
        }
        return null;
    }
}
