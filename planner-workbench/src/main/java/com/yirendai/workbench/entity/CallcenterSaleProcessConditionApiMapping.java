package com.yirendai.workbench.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 销售流程条件配置与接口映射表
 * </p>
 * <AUTHOR>
 * @since 2025-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CallcenterSaleProcessConditionApiMapping对象", description = "销售流程条件配置与接口映射表")
public class CallcenterSaleProcessConditionApiMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "条件类别")
    private Integer type;
    
    @ApiModelProperty(value = "参数类型 1 文本 2 数值/金额 3 日期 4 选项")
    private Integer paramType;

    @ApiModelProperty(value = "参数值，参数类型为选项需要")
    private String paramValue;

    @ApiModelProperty(value = "条件名称")
    private String name;

    @ApiModelProperty(value = "接口关联字段")
    private String apiRelationColumn;

    @ApiModelProperty(value = "操作符")
    private String operators;

    @ApiModelProperty(value = "接口id")
    private Long apiId;

    @ApiModelProperty(value = "是否删除（0-未删除，1-已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

}
