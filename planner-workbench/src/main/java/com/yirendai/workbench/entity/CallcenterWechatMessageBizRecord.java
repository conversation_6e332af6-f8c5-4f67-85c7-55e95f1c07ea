package com.yirendai.workbench.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 聊天信息业务记录表
 * </p>
 *
 * @since 2025-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("callcenter_wechat_message_biz_record")
@ApiModel(value="CallcenterWechatMessageBizRecord对象", description="聊天信息业务记录表")
public class CallcenterWechatMessageBizRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "消息ID")
    private Long messageId;

    @ApiModelProperty(value = "来源:1企微,2:微信")
    private String source;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "理财师工号")
    private String plannerNo;

    @ApiModelProperty(value = "客户user_id")
    private String userId;

    @ApiModelProperty(value = "业务类型")
    private Integer bizType;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;
}
