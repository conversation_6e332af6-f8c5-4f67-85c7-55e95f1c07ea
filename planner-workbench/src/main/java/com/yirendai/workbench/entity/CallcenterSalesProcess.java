package com.yirendai.workbench.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterSalesProcess对象", description="产品销售流程表")
@TableName("callcenter_sales_process")
public class CallcenterSalesProcess {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属租户id
     */
    private String tenantId;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 发布状态
     */
    private Integer processStatus;

    /**
     * 结点内容
     */
    private String nodeText;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 暂停时间
     */
    private LocalDateTime pauseTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否为默认流程，即最新版本流程
     */
    private Integer defaultProcess;

    /**
     * 结点进度计算方式
     */
    private Integer nodeCalType;

    @Override
    public String toString() {
        return "CallcenterSalesProcess{" +
                "id=" + id +
                ", productType=" + productType +
                ", processName='" + processName + '\'' +
                ", processNo='" + processNo + '\'' +
                ", versionNo='" + versionNo + '\'' +
                ", processStatus=" + processStatus +
                ", nodeText='" + nodeText + '\'' +
                ", publishTime=" + publishTime +
                ", pauseTime=" + pauseTime +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", defaultProcess=" + defaultProcess +
                ", nodeCalType=" + nodeCalType +
                ", tenantId=" + tenantId +
                '}';
    }
}
