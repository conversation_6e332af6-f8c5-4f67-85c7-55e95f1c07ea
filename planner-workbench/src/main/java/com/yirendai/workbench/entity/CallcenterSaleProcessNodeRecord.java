package com.yirendai.workbench.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 销售流程节点进度记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterSaleProcessNodeRecord对象", description="销售流程节点进度记录表")
public class CallcenterSaleProcessNodeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "销售流程id")
    private Long saleProcessId;

    @ApiModelProperty(value = "节点id")
    private String nodeId;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime finishTime;

    @ApiModelProperty(value = "完成状态 1 完成 2 未完成")
    private Integer status;

    @ApiModelProperty(value = "是否删除（0-未删除，1-已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;


}
