package com.yirendai.workbench.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * ai分析任务数据母表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_analysis_task_data")
@ApiModel(value = "AiAnalysisTaskData对象", description = "ai分析任务数据母表")
public class AiAnalysisTaskData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "ai分析任务Id ai_analysis_task表ID")
    private Long analysisTaskId;

    @ApiModelProperty(value = "场景ID")
    private String sceneIds;

    @ApiModelProperty(value = "客户ID")
    private String customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "分析结果")
    private String resultName;

    @ApiModelProperty(value = "分析结果描述")
    private String resultDes;

    @ApiModelProperty(value = "总子任务数")
    private Integer totalSubTaskCount;

    @ApiModelProperty(value = "已完成子任务数")
    private Integer finishedSubTaskCount;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUser;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "AI分析状态 0未开始，1进行中，2已完成,3失败")
    private Integer status;

    @ApiModelProperty(value = "AI分析完成时间")
    private LocalDateTime analysisTime;

    @ApiModelProperty(value = "是否已删除")
    private Integer isDeleted;

}
