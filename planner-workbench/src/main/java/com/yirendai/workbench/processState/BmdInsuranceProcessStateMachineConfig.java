package com.yirendai.workbench.processState;

import com.yirendai.workbench.enums.bmdInsureance.BmdInsuranceApproveEnums;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class BmdInsuranceProcessStateMachineConfig {

    /**
     * 审批流程支持以下状态转换
     * 草稿 -> 待审批（提交）  -----> 草稿 -> 翻译填充中
     * 待审批 -> 审批通过（通过）
     * 待审批 -> 审批不通过（驳回）
     * 待审批 -> 草稿（退回）
     * 审批通过 -> 待签字（开始签字）
     * 审批不通过 -> 草稿（退回）
     * 待签字 -> 已签字（完成签字）
     * 待签字 -> 草稿（退回）
     * @return
     */
    @Bean
    public List<BmdInsuranceProcessStateTransition> stateTransitions() {
        List<BmdInsuranceProcessStateTransition> transitions = new ArrayList<>();
        
        // 草稿状态转换
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.DRAFT, BmdInsuranceApproveEnums.TRANSLATION_FILLING_IN, "submit"));
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.TRANSLATION_FILLING_IN, BmdInsuranceApproveEnums.PENDING, "translation"));
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.TRANSLATION_FILLING_IN, BmdInsuranceApproveEnums.TRANSLATION_FILLING_FAIL, "translation"));

        // 待审批状态转换
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.PENDING, BmdInsuranceApproveEnums.APPROVE_PASS, "approve_pass"));
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.PENDING, BmdInsuranceApproveEnums.APPROVE_NOT_PASS, "approve_reject"));
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.PENDING, BmdInsuranceApproveEnums.DRAFT, "return_draft"));
        
        // 审批通过状态转换
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.APPROVE_PASS, BmdInsuranceApproveEnums.PENDING_SIGN, "start_sign"));
        
        // 审批不通过状态转换
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.APPROVE_NOT_PASS, BmdInsuranceApproveEnums.DRAFT, "return_draft"));
        
        // 待签字状态转换
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.PENDING_SIGN, BmdInsuranceApproveEnums.SIGNED, "complete_sign"));
        transitions.add(new BmdInsuranceProcessStateTransition(
                BmdInsuranceApproveEnums.PENDING_SIGN, BmdInsuranceApproveEnums.DRAFT, "return_draft"));

        return transitions;
    }
}