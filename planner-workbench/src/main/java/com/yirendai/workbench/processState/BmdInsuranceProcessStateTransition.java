package com.yirendai.workbench.processState;

import com.yirendai.workbench.enums.bmdInsureance.BmdInsuranceApproveEnums;
import lombok.Getter;

import java.util.Objects;

@Getter
public class BmdInsuranceProcessStateTransition {

    private final BmdInsuranceApproveEnums fromState;
    private final BmdInsuranceApproveEnums toState;
    private final String event;

    public BmdInsuranceProcessStateTransition(BmdInsuranceApproveEnums fromState,
                                              BmdInsuranceApproveEnums toState,
                                              String event) {
        this.fromState = fromState;
        this.toState = toState;
        this.event = event;
    }
    
    /**
     * 检查是否可以进行状态转换
     * @param currentState 当前状态
     * @param targetState 目标状态
     * @return 是否可以转换
     */
    public boolean canTransition(BmdInsuranceApproveEnums currentState, BmdInsuranceApproveEnums targetState) {
        return Objects.equals(currentState.getCode(), fromState.getCode()) &&
               Objects.equals(targetState.getCode(), toState.getCode());
    }
    
    /**
     * 获取转换后的状态
     * @return 转换后的状态
     */
    public BmdInsuranceApproveEnums getToState() {
        return toState;
    }

    @Override
    public String toString() {
        return String.format("StateTransition[%s -> %s, event=%s]", 
            fromState.getDesc(), toState.getDesc(), event);
    }
}