package com.yirendai.workbench.wrapper.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
@Builder
public class DOrderQueryReq {
    @ApiModelProperty(value = "操作人员工编号	")
    private String empNo;
    @ApiModelProperty(value = "客户ecif	")
    private String ecifId;
    @ApiModelProperty(value = "意向签约日期	")
    private LocalDateTime applyDate;
    @ApiModelProperty(value = "向前浮动查询天数，默认30")
    private Integer floatingBeforeDays;
    @ApiModelProperty(value = "渠道编号	{\"data\":[{\"code\":\"**********\",\"name\":\"内部运维\"},{\"code\":\"CFGL01\",\"name\":\"推荐渠道1\"},{\"code\":\"DLTJ01\",\"name\":\"独立推荐人\"},{\"code\":\"XHLC01\",\"name\":\"星火\"},{\"code\":\"XHJF01\",\"name\":\"星火金服\"},{\"code\":\"ZWHQ01\",\"name\":\"子午环球有限公司\"},{\"code\":\"AMON01\",\"name\":\"AMON (HONG KONG) LIMITED\"},{\"code\":\"RTSY01\",\"name\":\"瑞天实业（中国）有限公司\"},{\"code\":\"XGXH01\",\"name\":\"香港星火\"},{\"code\":\"PDA001\",\"name\":\"香港柏德顾问有限公司\"},{\"code\":\"FQLP01\",\"name\":\"睿智人生策划有限公司\"},{\"code\":\"OCAL01\",\"name\":\"Oceanus Capital Asia Limited\"},{\"code\":\"MHCO01\",\"name\":\"MH COMPANY\"},{\"code\":\"SUCE01\",\"name\":\"SUCESS DEVELOPMENT CO.\"},{\"code\":\"PTE001\",\"name\":\"PTE GLOBAL COMPANY  LIMITED\"},{\"code\":\"LWCC01\",\"name\":\"LI WING CHUNG COMPANY\"},{\"code\":\"DEGE01\",\"name\":\"DE GENNES LIMITED\"},{\"code\":\"PERP01\",\"name\":\"PERPETUAL CO\"},{\"code\":\"LFWM01\",\"name\":\"六福财富管理（香港）有限公司\"},{\"code\":\"FVWM01\",\"name\":\"富策财富管理有限公司\"},{\"code\":\"CSQD01\",\"name\":\"测试渠道\"},{\"code\":\"YRCF01\",\"name\":\"宜人财富\"},{\"code\":\"HXCH01\",\"name\":\"四号渠道\"},{\"code\":\"HYKJ01\",\"name\":\"厚予科技\"},{\"code\":\"HYDL01\",\"name\":\"厚予独立渠道\"},{\"code\":\"YRCFHK01\",\"name\":\"宜人HK\"},{\"code\":\"BCJX01\",\"name\":\"八号渠道\"},{\"code\":\"RCDX01\",\"name\":\"瑞承电销\"},{\"code\":\"TENGLONG01\",\"name\":\"TENGLONG01\"},{\"code\":\"YIBAO001\",\"name\":\"YIBAO001\"}]}")
    private List<String> channelCodeList;

}
