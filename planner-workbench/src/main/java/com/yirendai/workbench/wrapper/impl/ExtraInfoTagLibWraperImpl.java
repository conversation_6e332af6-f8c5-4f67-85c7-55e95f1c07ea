package com.yirendai.workbench.wrapper.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yirendai.workbench.config.CrmProperty;
import com.yirendai.workbench.entity.CallcenterSceneConfig;
import com.yirendai.workbench.model.CallcenterUserDto;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.callcenter.CallcenterUserService;
import com.yirendai.workbench.service.callcenter.SceneService;
import com.yirendai.workbench.util.AesUtils;
import com.yirendai.workbench.util.HttpUtil;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.SearchUserTagRequest;
import com.yirendai.workbench.vo.res.YrcfCrmResponse;
import com.yirendai.workbench.vo.res.callcenter.CustomerAndAssetResp;
import com.yirendai.workbench.vo.res.callcenter.CustomerAssetChartResp;
import com.yirendai.workbench.vo.res.callcenter.CustomerListResp;
import com.yirendai.workbench.wrapper.CustomerInfoWrapper;
import com.yirendai.workbench.wrapper.ExtraInfoWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import static com.yirendai.workbench.util.SensitiveDataUtils.decodeAndSensMobile;
import static com.yirendai.workbench.vo.req.SearchUserTagRequest.*;
import static com.yirendai.workbench.vo.req.SearchUserTagRequest.FieldEnum.SEX;

@Slf4j
@Service
public class ExtraInfoTagLibWraperImpl implements ExtraInfoWrapper {
    @Autowired
    private CrmProperty crmProperty;
    @Autowired
    private YrcfCutomerInfoWrapper yrcfCutomerInfoWrapper;
    @Value("${field.aes.key:2829E02D942F9448323CAA4F2B53BBB0}")
    private String aesKey;

    @Resource
    CallcenterUserService callcenterUserService;

    @Resource
    SceneService sceneService;

    @Override
    public void initListTagInfo(List<CustomerListResp> customerList) {
        SearchUserTagRequest searchUserTagRequest = new SearchUserTagRequest();
        searchUserTagRequest.setUserIds(customerList.stream().map(CustomerListResp::getUserId).distinct().collect(Collectors.toList()));
        searchUserTagRequest.setFields(CUSTOMER_LIST_FIELDS);

        if (CollectionUtils.isEmpty(searchUserTagRequest.getUserIds())){
            log.info("查询用户标签库信息，用户列表为空:{}", searchUserTagRequest.getUserIds());
            return;
        }

        String result = HttpUtil.postForJson(crmProperty.getCrmUrl()+"/userTagQuery/queryTagInfo", JSONUtil.toJsonStr(searchUserTagRequest));
        YrcfCrmResponse<List<CustomerListResp>> yrcfCrmResponse = JsonUtilExt.jsonToBean(result, new TypeReference<YrcfCrmResponse<List<CustomerListResp>>>() {});
        if (yrcfCrmResponse == null){
            log.warn("initListTagInfo query tag info result null");
            return;
        }
        if (!yrcfCrmResponse.success()) {
            log.warn("initListTagInfo query tag info error code:{} , msg:{}", yrcfCrmResponse.getErrorCode(), yrcfCrmResponse.getMsg());
            return;
        }
        List<CustomerListResp> tagData = yrcfCrmResponse.getData();
        if (tagData != null && tagData.size() > 0){
            Map<String, CustomerListResp> tagMap = tagData.stream().collect(Collectors.toMap(CustomerListResp::getUserId, tag -> tag));
            customerList.forEach(customer -> {
                BeanUtil.copyProperties(tagMap.get(customer.getUserId()), customer, CopyOptions.create().ignoreNullValue());
                customer.setWechatFlag(StringUtils.isBlank(customer.getUserWechatNum()) ? "否" : "是");
            });
        }
    }

    @Override
    public void initBaseAndAssetInfo(String userId, CustomerAndAssetResp customerAndAssetResp) {
        SearchUserTagRequest searchUserTagRequest = new SearchUserTagRequest();
        searchUserTagRequest.setUserIds(Collections.singletonList(userId));
        searchUserTagRequest.setFields(USER_BASIC_ASSET_FIELDS);

        String result = HttpUtil.postForJson(crmProperty.getCrmUrl()+"/userTagQuery/queryTagInfo", JSONUtil.toJsonStr(searchUserTagRequest));
        YrcfCrmResponse yrcfCrmResponse = JsonUtil.parse(result, YrcfCrmResponse.class);
        if (!yrcfCrmResponse.success()) {
            log.warn("initBaseAndAssetInfo query tag info error code:{} , msg:{}", yrcfCrmResponse.getErrorCode(), yrcfCrmResponse.getMsg());
        }
        List<Map<String,Object>> tagData = (List<Map<String,Object>>)yrcfCrmResponse.getData();
        if (tagData != null && tagData.size() > 0){
            Map<String,Object> userInfoInMap = tagData.get(0);
            CustomerAndAssetResp.UserBasicInfo user = BeanUtil.mapToBean(userInfoInMap, CustomerAndAssetResp.UserBasicInfo.class, false, CopyOptions.create());
            if (StringUtils.isBlank(user.getName())){
                SimpleUserInfoDto simpleUserInfoDto = yrcfCutomerInfoWrapper.getUserBasicInfoByUserId(userId, OwnAuthUtil.getTenantId());
                user.setName(simpleUserInfoDto == null ? "" : simpleUserInfoDto.getUserName());
            }
            if(StringUtils.isNotBlank(user.getPhone())){
                byte[] bt = new Base64().decode(user.getPhone());
                String mobile =  new String(bt);
                try {
                    String decrypt = AesUtils.encrypt(mobile, aesKey);
                    user.setEncryptPhone(decrypt);
                } catch (Exception e) {
                   log.error("initBaseAndAssetInfo decrypt mobile error", e);
                }
            }
            user.setPhone(decodeAndSensMobile(user.getPhone()));
            user.setGender(String.valueOf(userInfoInMap.get(SEX.getFieldName())));

            CallcenterUserDto callcenterUserDto = callcenterUserService.getCenterUserByUserId(userId, OwnAuthUtil.getTenantId());
            if(Objects.nonNull(callcenterUserDto) && Objects.nonNull(callcenterUserDto.getSceneId())){
                CallcenterSceneConfig sceneConfig = sceneService.getById(callcenterUserDto.getSceneId());
                user.setSceneInfo(sceneConfig.getSceneNo() + "(" + sceneConfig.getSceneName() + ")");
            }
            customerAndAssetResp.setUser(user);

            CustomerAndAssetResp.AssetInfo userAsset = BeanUtil.mapToBean(userInfoInMap, CustomerAndAssetResp.AssetInfo.class, false, CopyOptions.create());
            userAsset.intNextOverdue();
            customerAndAssetResp.setAssetInfo(userAsset);

            CustomerAndAssetResp.HoldAsset holdAsset =  BeanUtil.mapToBean(userInfoInMap, CustomerAndAssetResp.HoldAsset.class, false, CopyOptions.create());
            customerAndAssetResp.setHoldAsset(holdAsset.toHoldAssetDesc());

        }
    }

    @Override
    public void assetChartInfo(String userId, CustomerAssetChartResp customerAssetChartResp) {
        SearchUserTagRequest searchUserTagRequest = new SearchUserTagRequest();
        searchUserTagRequest.setUserIds(Collections.singletonList(userId));
        searchUserTagRequest.setFields(USER_ASSET_CHART_FIELDS);

        String result = HttpUtil.postForJson(crmProperty.getCrmUrl()+"/userTagQuery/queryTagInfo", JSONUtil.toJsonStr(searchUserTagRequest));
        YrcfCrmResponse<List<CustomerAssetChartResp>> yrcfCrmResponse = JsonUtilExt.jsonToBean(result, new TypeReference<YrcfCrmResponse<List<CustomerAssetChartResp>>>() {});
        if (yrcfCrmResponse == null){
            log.warn("assetChartInfo query tag info result null");
            return;
        }
        if (!yrcfCrmResponse.success()) {
            log.warn("assetChartInfo query tag info error code:{} , msg:{}", yrcfCrmResponse.getErrorCode(), yrcfCrmResponse.getMsg());
            return;
        }
        if (CollectionUtils.isNotEmpty(yrcfCrmResponse.getData())){
            BeanUtils.copyProperties(yrcfCrmResponse.getData().get(0), customerAssetChartResp);
            customerAssetChartResp.setTotalIncome(customerAssetChartResp.getSfixedAccIncome()==null? BigDecimal.ZERO:customerAssetChartResp.getSfixedAccIncome()
                    .add(customerAssetChartResp.getShunyiAccIncome() == null ? BigDecimal.ZERO : customerAssetChartResp.getShunyiAccIncome())
                    .add(customerAssetChartResp.getBankFinancingTotalProfit() == null ? BigDecimal.ZERO : customerAssetChartResp.getBankFinancingTotalProfit()));
        }
    }


}
