package com.yirendai.workbench.wrapper.dto.anmi;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 安米指定设备发短信请求
 * 
 * <AUTHOR>
 * @date 2025-10-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnmiSendSmsRequest {
    
    @ApiModelProperty(value = "0使用第一个卡槽手机卡,1使用第二个卡槽手机卡,2自动选择一个卡")
    private Integer solt;
    
    @ApiModelProperty(value = "必填，设备唯一标识imei", required = true)
    private String imei;
    
    @ApiModelProperty(value = "必填，需要发送的内容", required = true)
    private String content;
    
    @ApiModelProperty(value = "必填，需要发送的号码", required = true)
    private String targetNumber;
    
    @ApiModelProperty(value = "外部编号，用于关联业务")
    private String uuid;
    
    @ApiModelProperty(value = "子公司id")
    private Long cid;
}

