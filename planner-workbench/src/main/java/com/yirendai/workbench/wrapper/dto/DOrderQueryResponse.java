package com.yirendai.workbench.wrapper.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

@Accessors(chain = true)
@Data
public class DOrderQueryResponse {
    @ApiModelProperty(value = "true -成功 false-异常")
    private Boolean status;
    @ApiModelProperty(value = "0000—成功 9999-异常")
    private String errorCode;
    @ApiModelProperty(value = "")
    private String message;
    @ApiModelProperty(value = "")
    private List<NearestAppointmentVO> data;

    @Data
    public static class NearestAppointmentVO {
        @ApiModelProperty(value = "排期预约日期")
        private String applyDate;
        @ApiModelProperty(value = "预约产品id")
        private Long productId;
        @ApiModelProperty(value = "预约产品code")
        private String productCode;
        @ApiModelProperty(value = "预约产品")
        private String productName;
        @ApiModelProperty(value = "预约单状态1-待排期2-待签约3-已签约5-已取消")
        private String appointmentState;
        @ApiModelProperty(value = "保单号")
        private String policyNo;
        @ApiModelProperty(value = "投保产品id")
        private Long policyProductId;
        @ApiModelProperty(value = "投保产品Code")
        private String policyProductCode;
        @ApiModelProperty(value = "投保产品")
        private String policyProductName;
        @ApiModelProperty(value = "投保日期")
        private LocalDate holdDate;
        @ApiModelProperty(value = "投保状态1-核保中 2-撤件 3-拒保 4-有效(承保)")
        private String policyState;
        @ApiModelProperty(value = "支付状态 0-未全部打款 1-全部打款")
        private String payState;
        @ApiModelProperty(value = "币种 CNY(默认)/USD")
        private String currency;
        @ApiModelProperty(value = "数据更新时间")
        private String modifyTime;

    }
}
