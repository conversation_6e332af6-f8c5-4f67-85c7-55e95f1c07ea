package com.yirendai.workbench.wrapper.dto.life;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class OrderPageByCustomerRequest {
    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称, YIREN_PLUS:宜优选; XINDA:逛逛商城")
    @NotNull(message = "店铺名称不能为空")
    private String storeName;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 第几页
     */
    @ApiModelProperty(value = "第几页")
    private Integer pageNum = 0;

    /**
     * 每页显示多少条
     */
    @ApiModelProperty(value = "每页显示多少条")
    private Integer pageSize = 10;

    /**
     * 订单创建时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "开始时间-订单创建时间 yyyy-MM-dd HH:mm:ss")
    private String orderCreateFrom;

    /**
     * 订单创建时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "结束时间-订单创建时间 yyyy-MM-dd HH:mm:ss")
    private String orderCreateTo;
}
