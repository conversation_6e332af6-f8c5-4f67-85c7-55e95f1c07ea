package com.yirendai.workbench.wrapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.yirendai.workbench.config.AnmiCallServiceProperty;
import com.yirendai.workbench.exception.MakeCallException;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.anmi.AnMiAESUtils;
import com.yirendai.workbench.wrapper.dto.anmi.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.SortedMap;

import static com.yirendai.workbench.exception.CallCenterBusinessError.END_PHONE_CALL_ERROR;
import static com.yirendai.workbench.exception.CallCenterBusinessError.MAKE_CALL_FAILED;
import static com.yirendai.workbench.util.anmi.AnMiAESUtils.*;

/**
 * 安米接口封装
 */
@Slf4j
@Service
public class AnmiApiWrapper {


    @Autowired
    private AnmiCallServiceProperty anmiCallServiceProperty;
    @Autowired
    private WrapperUtils wrapperUtils;

    /**
     * 发起呼叫
     */
    public AnmiCallResponse<Void> makePhoneCall(AnmiCallRequest anmiCallRequest, String tenantId){
        try{
            String url = anmiCallServiceProperty.getTenantProperties(tenantId).getEndpoint()+"/palm/api/open/v2/push/anmi/make/call";
            String params = JsonUtilExt.beanToJson(anmiCallRequest);
            log.info("发起呼叫, url:{} param：{}", url, params);
            String accessKeyId = anmiCallServiceProperty.getTenantProperties(tenantId).getAccessKeyId();
            String encryptContent = AnMiAESUtils.encrypt(params, accessKeyId);

            AnmiEncryptRequest encryptRequest = new AnmiEncryptRequest(encryptContent);
            Map<String, String> headerParams = initHeaderParams(encryptRequest, tenantId);
            String requestBody = JsonUtilExt.beanToJson(encryptRequest);

            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);
            if (!response.getStatusCode().is2xxSuccessful()){
                log.error("安米手机呼叫失败, req:{}， status:{}", requestBody, response.getStatusCode());
                throw new MakeCallException(MAKE_CALL_FAILED, "安米手机呼叫失败");
            }else {
                log.info("安米手机呼叫成功, req:{}， response:{}", requestBody, response.getBody());
                AnmiCallResponse<Void> anmiCallResponse =  JsonUtilExt.jsonToBean(response.getBody(), new TypeReference<AnmiCallResponse<Void>>() { });
                if (!anmiCallResponse.getSuccess()){
                    throw new MakeCallException(MAKE_CALL_FAILED, "安米手机呼叫失败:"+anmiCallResponse.getMessage());
                }
                return anmiCallResponse;
            }
        }catch (MakeCallException e){
            log.error("安米手机呼叫失败", e);
            throw e;
        }catch (Exception e){
            log.error("安米手机呼叫异常", e);
            throw new MakeCallException(MAKE_CALL_FAILED, e.getMessage());
        }
    }

    /**
     * 挂断电话
     */
    public AnmiCallResponse<Void> endPhoneCall(AnmiEndCallRequest anmiCallRequest, String tenantId){
        try{
            String url = anmiCallServiceProperty.getTenantProperties(tenantId).getEndpoint()+"/palm/api/open/v2/push/end/call";
            String params = JsonUtilExt.beanToJson(anmiCallRequest);
            log.info("挂断电话, url:{} param：{}", url, params);
            String accessKeyId = anmiCallServiceProperty.getTenantProperties(tenantId).getAccessKeyId();
            String encryptContent = AnMiAESUtils.encrypt(params, accessKeyId);

            AnmiEncryptRequest encryptRequest = new AnmiEncryptRequest(encryptContent);
            Map<String, String> headerParams = initHeaderParams(encryptRequest, tenantId);
            String requestBody = JsonUtilExt.beanToJson(encryptRequest);

            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);
            if (!response.getStatusCode().is2xxSuccessful()){
                log.error("安米手机挂断电话失败, req:{}， status:{}", requestBody, response.getStatusCode());
                throw new MakeCallException(END_PHONE_CALL_ERROR, "安米手机挂断电话失败");
            }else {
                log.info("安米手机挂断电话成功, req:{}， response:{}", requestBody, response.getBody());
                AnmiCallResponse<Void> anmiCallResponse =  JsonUtilExt.jsonToBean(response.getBody(), new TypeReference<AnmiCallResponse<Void>>() { });
                if (!anmiCallResponse.getSuccess()){
                    throw new MakeCallException(END_PHONE_CALL_ERROR, "安米手机挂断电话失败:"+anmiCallResponse.getMessage());
                }
                return anmiCallResponse;
            }
        }catch (MakeCallException e){
            log.error("安米手机挂断电话失败", e);
            throw e;
        }catch (Exception e){
            log.error("安米手机挂断电话异常", e);
            throw new MakeCallException(END_PHONE_CALL_ERROR, e.getMessage());
        }
    }

    /**
     * 拨打结果查询
     */
    public AnmiCallResponse<List<AnmiCallCommandResult>> getPhoneCallResult(String uuid, String tenantId){
        try{
            AnmiGetCallResultRequest getCallResultRequest = new AnmiGetCallResultRequest(uuid);
            String url = anmiCallServiceProperty.getTenantProperties(tenantId).getEndpoint()+"/palm/api/open/mqtt/getMqttOperateStatusByOpenApi";
            Map<String, String> headerParams = initHeaderParams(getCallResultRequest, tenantId);
            String requestBody = JsonUtilExt.beanToJson(getCallResultRequest);
            log.info("安米手机拨打结果查询, url:{} param：{}", url, requestBody);

            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);
            if (!response.getStatusCode().is2xxSuccessful()){
                log.error("安米手机拨打结果查询失败, req:{}， status:{}", requestBody, response.getStatusCode());
                return AnmiCallResponse.fail("安米手机拨打结果查询失败");
            }else {
                log.info("安米手机拨打结果查询成功, req:{}， response:{}", requestBody, response.getBody());
                return JsonUtilExt.jsonToBean(response.getBody(), new TypeReference<AnmiCallResponse<List<AnmiCallCommandResult>>>() { });
            }
        }catch (Exception e){
            log.error("安米手机拨打结果查询异常", e);
        }
        return AnmiCallResponse.fail("安米手机拨打结果查询异常");
    }

    /**
     * 电话通话记录信息同步接⼝
     */
    public AnmiCallResponse<AnmiPageResponse<AnmiCallRecordResponse>> getPhoneCallRecord(AnmiCallRecordRequest anmiCallRecordRequest, String tenantId){
        try{
            String requestBody = JsonUtilExt.beanToJson(anmiCallRecordRequest);
            String url = anmiCallServiceProperty.getTenantProperties(tenantId).getEndpoint()+"/palm/api/open/v2/userCall/getUserCallListByOpenApi";
            Map<String, String> headerParams = initHeaderParams(anmiCallRecordRequest, tenantId);
            log.info("安米手机通话记录信息同步, url:{} param：{}", url, requestBody);
            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);
            if (!response.getStatusCode().is2xxSuccessful()){
                log.error("安米手机通话记录信息同步失败, req:{}， status:{}", requestBody, response.getStatusCode());
                return AnmiCallResponse.fail("安米手机通话记录信息同步失败");
            }else {
                String result = response.getBody();
                log.info("安米手机通话记录信息同步成功, req:{}， response:{}", requestBody, result);
                return JsonUtilExt.jsonToBean(result, new TypeReference<AnmiCallResponse<AnmiPageResponse<AnmiCallRecordResponse>>>() { });
            }
        }catch (Exception e){
            log.error("安米手机通话记录信息同步异常", e);
        }
        return AnmiCallResponse.fail("安米手机通话记录信息同步异常");
    }

    /**
     * 拉取设备接⼝
     */
    public AnmiCallResponse<AnmiPageResponse<AnmiGetPhoneDeviceInfo>> getPhoneDeviceList(AnmiGetPhoneDeviceListRequest getPhoneDeviceListRequest, String tenantId){
        try{
            String requestBody = JsonUtilExt.beanToJson(getPhoneDeviceListRequest);
            String url = anmiCallServiceProperty.getTenantProperties(tenantId).getEndpoint()+"/palm/api/open/v2/phoneDevice/v2/getPhoneDeviceListByOpenApi";
            Map<String, String> headerParams = initHeaderParams(getPhoneDeviceListRequest, tenantId);
            log.info("安米手机拉取设备, url:{} param：{}", url, requestBody);
            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);
            if (!response.getStatusCode().is2xxSuccessful()){
                log.error("安米手机拉取设备失败, req:{}， status:{}", requestBody, response.getStatusCode());
                return AnmiCallResponse.fail("安米手机拉取设备失败");
            }else {
                log.info("安米手机拉取设备成功, req:{}， response:{}", requestBody, response.getBody());
                return JsonUtilExt.jsonToBean(response.getBody(), new TypeReference<AnmiCallResponse<AnmiPageResponse<AnmiGetPhoneDeviceInfo>>>() { });
            }
        }catch (Exception e){
            log.error("安米手机拉取设备异常", e);
        }
        return AnmiCallResponse.fail("安米手机拉取设备异常");
    }

    /**
     * 通话详情接口查询
     */
    public AnmiCallResponse<List<AnmiCallMessageDetailResponse>> getUserCallMessageDetailList(AnmiCallDetailRequest anmiCallDetailRequest, String tenantId){
        try{
            String requestBody = JsonUtilExt.beanToJson(anmiCallDetailRequest);
            String url = anmiCallServiceProperty.getTenantProperties(tenantId).getEndpoint()+"/palm/api/open/v2/userCall/getUserCallMessageDetailList";
            Map<String, String> headerParams = initHeaderParams(anmiCallDetailRequest, tenantId);
            log.info("安米手机通话详情查询, url:{} param：{}", url, requestBody);
            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);
            if (!response.getStatusCode().is2xxSuccessful()){
                log.error("安米手机通话详情查询失败, req:{}， status:{}", requestBody, response.getStatusCode());
                return AnmiCallResponse.fail("安米手机通话详情查询失败");
            }else {
                String result = response.getBody();
                log.info("安米手机通话详情查询成功, req:{}， response:{}", requestBody, result);
                return JsonUtilExt.jsonToBean(result, new TypeReference<AnmiCallResponse<List<AnmiCallMessageDetailResponse>>>() { });
            }
        }catch (Exception e){
            log.error("安米手机通话详情查询异常", e);
        }
        return AnmiCallResponse.fail("安米手机通话详情查询异常");
    }

    /**
     * 手机短信信息列表同步接口
     */
    public AnmiCallResponse<AnmiPageResponse<AnmiSmsInfo>> getPhoneSmsList(AnmiGetSmsListRequest getSmsListRequest, String tenantId){
        try{
            String requestBody = JsonUtilExt.beanToJson(getSmsListRequest);
            String url = anmiCallServiceProperty.getTenantProperties().get(tenantId).getEndpoint()+"/palm/api/open/v2/sms/getSmsListByOpenApi";
            Map<String, String> headerParams = initHeaderParams(getSmsListRequest, tenantId);
            log.info("安米手机短信信息列表同步, url:{} param：{}", url, headerParams);

            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);
            if (!response.getStatusCode().is2xxSuccessful()){
                log.error("安米手机短信信息列表同步失败, req:{}， status:{}", requestBody, response.getStatusCode());
                return AnmiCallResponse.fail("安米手机短信信息列表同步失败");
            }

            String result = response.getBody();
            log.info("安米手机短信信息列表同步成功, req:{}， response:{}", requestBody, result);
            return JsonUtilExt.jsonToBean(result, new TypeReference<AnmiCallResponse<AnmiPageResponse<AnmiSmsInfo>>>() { });

        }catch (Exception e){
            log.error("安米手机短信信息列表同步异常", e);
        }
        return AnmiCallResponse.fail("安米手机短信信息列表同步异常");
    }

    /**
     * 微信信息列表同步接口
     */
    public AnmiCallResponse<AnmiPageResponse<AnmiWechatMessageInfo>> getWechatMessageList(AnMiWechatMessageListRequest getWechatMessageListRequest, String tenantId){
        try{
            String requestBody = JsonUtilExt.beanToJson(getWechatMessageListRequest);
            String url = anmiCallServiceProperty.getTenantProperties().get(tenantId).getEndpoint()+"/palm/api/open/v2/wechat/getWechatMessageListByOpenApi";
            Map<String, String> headerParams = initHeaderParams(getWechatMessageListRequest, tenantId);
            log.info("微信消息列表同步, url:{} param：{}", url, headerParams);

            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("微信消息列表同步失败, req:{}， status:{}", requestBody, response.getStatusCode());
                return AnmiCallResponse.fail("安米手机短信信息列表同步失败");
            }

            String result = response.getBody();
            log.info("微信消息列表同步成功, req:{}， response:{}", requestBody, result);
            // 解析响应
            return JsonUtilExt.jsonToBean(result, new TypeReference<AnmiCallResponse<AnmiPageResponse<AnmiWechatMessageInfo>>>() { });

        }catch (Exception e){
            log.error("微信消息列表同步失败", e);
        }
        return AnmiCallResponse.fail("安米手机短信信息列表同步异常");
    }


    /**
     * 查询微信联系人信息接口
     */
    public AnmiCallResponse<List<AnmiWechatContactInfo>> getWechatContactList(AnmiWechatContactListRequest request, String tenantId) {
        try {
            String requestBody = JsonUtilExt.beanToJson(request);

            String url = anmiCallServiceProperty.getTenantProperties().get(tenantId).getEndpoint() + "/palm/api/open/wechat/getWechatContactListAllByOpenApi";
            Map<String, String> headerParams = initHeaderParams(request, tenantId);
            log.info("微信联系人信息查询开始, url:{} param：{}", url, headerParams);
            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("微信联系人信息查询失败, req:{}， status:{}", requestBody, response.getStatusCode());
                return AnmiCallResponse.fail("微信联系人信息查询失败，HTTP状态码: " + response.getStatusCode());
            }

            String result = response.getBody();
            log.info("微信联系人信息查询成功, req:{}， response:{}", requestBody, result);

            return JsonUtilExt.jsonToBean(result, new TypeReference<AnmiCallResponse<List<AnmiWechatContactInfo>>>() { });

        } catch (Exception e) {
            log.error("微信联系人信息查询异常", e);
            return AnmiCallResponse.fail("微信联系人信息查询异常: " + e.getMessage());
        }
    }

    /**
     * 微信详情接口查询
     */
    public AnmiCallResponse<List<AnmiWechatMessageDetail>> getWechatMessageDetailList(AnmiWechatMessageDetailRequest request, String tenantId) {
        try {
            String requestBody = JsonUtilExt.beanToJson(request);
            String url = anmiCallServiceProperty.getTenantProperties().get(tenantId).getEndpoint() + "/palm/api/open/v2/wechat/getWechatMessageDetailList";
            Map<String, String> headerParams = initHeaderParams(request, tenantId);

            log.info("微信详情接口查询开始, url:{} param：{}", url, requestBody);
            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("微信详情接口查询失败, req:{}， status:{}", requestBody, response.getStatusCode());
                return AnmiCallResponse.fail("微信详情接口查询失败，HTTP状态码: " + response.getStatusCode());
            }

            String result = response.getBody();
            log.info("微信详情接口查询成功, req:{}， response:{}", requestBody, result);

            return JsonUtilExt.jsonToBean(result, new TypeReference<AnmiCallResponse<List<AnmiWechatMessageDetail>>>() { });

        } catch (Exception e) {
            log.error("微信详情接口查询异常", e);
            return AnmiCallResponse.fail("微信详情接口查询异常: " + e.getMessage());
        }
    }

    /**
     * 指定设备发短信（非定制商户使用）
     * 
     * @param sendSmsRequest 发送短信请求参数
     * @param tenantId 租户ID
     * @return 发送结果
     */
    public AnmiCallResponse<Void> sendSms(AnmiSendSmsRequest sendSmsRequest, String tenantId) {
        try {
            String url = anmiCallServiceProperty.getTenantProperties(tenantId).getEndpoint() + "/palm/api/open/v2/push/send/sms";
            String params = JsonUtilExt.beanToJson(sendSmsRequest);
            log.info("指定设备发短信, url:{} param：{}", url, params);
            
            String accessKeyId = anmiCallServiceProperty.getTenantProperties(tenantId).getAccessKeyId();
            String encryptContent = AnMiAESUtils.encrypt(params, accessKeyId);

            AnmiEncryptRequest encryptRequest = new AnmiEncryptRequest(encryptContent);
            Map<String, String> headerParams = initHeaderParams(encryptRequest, tenantId);
            String requestBody = JsonUtilExt.beanToJson(encryptRequest);

            ResponseEntity<String> response = wrapperUtils.postJson(url, requestBody, headerParams);
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("安米指定设备发短信失败, req:{}， status:{}", requestBody, response.getStatusCode());
                throw new MakeCallException(MAKE_CALL_FAILED, "安米指定设备发短信失败");
            } else {
                log.info("安米指定设备发短信成功, req:{}， response:{}", requestBody, response.getBody());
                AnmiCallResponse<Void> anmiCallResponse = JsonUtilExt.jsonToBean(response.getBody(), new TypeReference<AnmiCallResponse<Void>>() { });
                if (!anmiCallResponse.getSuccess()) {
                    throw new MakeCallException(MAKE_CALL_FAILED, "安米指定设备发短信失败:" + anmiCallResponse.getMessage());
                }
                return anmiCallResponse;
            }
        } catch (MakeCallException e) {
            log.error("安米指定设备发短信失败", e);
            throw e;
        } catch (Exception e) {
            log.error("安米指定设备发短信异常", e);
            throw new MakeCallException(MAKE_CALL_FAILED, e.getMessage());
        }
    }



    private Map<String, String> initHeaderParams(Object request, String tenantId) throws Exception {
        SortedMap contentMap = beanToMap(request);
        String accessKeyId = anmiCallServiceProperty.getTenantProperties().get(tenantId).getAccessKeyId();
        String content = getMd5Content(contentMap, anmiCallServiceProperty.getTenantProperties().get(tenantId).getAccessKeySecret());
        SortedMap map = getCommnMap(accessKeyId);
        String sign = getSign(content, map, anmiCallServiceProperty.getTenantProperties().get(tenantId).getAccessKeySecret());
        Map<String, String> headerParams = Maps.newHashMap();
        headerParams.put(X_APP_AUTHORIZATION, X_APP_AUTHORIZATION_HEAD + accessKeyId);
        headerParams.put(X_APP_KEY, accessKeyId);
        headerParams.put(X_APP_TIMESTAMP, String.valueOf( map.get(X_APP_TIMESTAMP)));
        headerParams.put(X_APP_NONCE, String.valueOf( map.get(X_APP_NONCE)));
        headerParams.put(CONTENT_MD5, content);
        headerParams.put(SIGN, sign);
        return headerParams;
    }
}
