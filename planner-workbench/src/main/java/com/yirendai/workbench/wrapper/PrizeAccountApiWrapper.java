package com.yirendai.workbench.wrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yirendai.workbench.util.HttpUtil;
import com.yirendai.workbench.wrapper.dto.OwmPayResult;
import com.yirendai.workbench.wrapper.dto.RewardCashflow;
import com.yirendai.workbench.wrapper.dto.RewardPageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

/**
 * 奖励金服务接口封装
 */
@Service
@Slf4j
public class PrizeAccountApiWrapper {

    @Value("${owm.pay.reward.accountCashFlowUrl}")
    private String rewardUrl;

    /**
     * 奖励金流水列表-支持分页
     * cashFlowType - r发奖，t税费扣除，w提现
     * @return
     */
    public RewardPageModel<RewardCashflow> getRewardAccountCashFlow(String userId, String cashFlowType, Integer pageNo, Integer pageSize, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.info("查询奖励金流水列表,用户ID:{}", userId);
            JSONObject params = new JSONObject();;
            params.put("userId", userId);
            params.put("pageNo", pageNo);
            params.put("pageSize", pageSize);
            params.put("channelId", "04");
            params.put("startTime", DateUtils.addMonths(new Date(), -12));
            if(StringUtils.isNotEmpty(cashFlowType)){
                params.put("cashFlowType", cashFlowType);
            }
            if (Objects.nonNull(startTime) ) {
                params.put("startTime", Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()));
            }
            if (Objects.nonNull(endTime) ) {
                params.put("endTime", Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));
            }
            log.info("查询奖励金流水列表,url:{},param:{}",rewardUrl,params.toString());
            String content = HttpUtil.postForJson(rewardUrl, params.toString());
            log.info("查询奖励金流水列表,param:{},result:{}",params.toString(),content);
            if(StringUtils.isEmpty(content)){
                throw new RuntimeException("查询奖励金流水列表异常");
            }
            OwmPayResult<RewardPageModel<RewardCashflow>> accountFlowPageResult = JSON.parseObject(content, new TypeReference<OwmPayResult<RewardPageModel<RewardCashflow>>>() {});
            if(accountFlowPageResult == null || !accountFlowPageResult.isSuccess()){
                throw new RuntimeException("查询奖励金流水列表异常");
            }
            return accountFlowPageResult.getData();
        } catch (Exception e) {
            log.info("查询奖励金流水列表异常,userId:{}", userId,e);
        }
        return null;
    }

}
