package com.yirendai.workbench.wrapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yirendai.app.fortune.common.util.JsonUtils;
import com.yirendai.voiceaiserver.model.owmuc.UserVerifyInfo;
import com.yirendai.voiceaiserver.service.UserVerifyInfoService;
import com.yirendai.workbench.config.DOrderQueryProperty;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.vo.res.callcenter.CallServiceResponse;
import com.yirendai.workbench.wrapper.dto.AddAgentRequest;
import com.yirendai.workbench.wrapper.dto.AddAgentResponse;
import com.yirendai.workbench.wrapper.dto.DOrderQueryReq;
import com.yirendai.workbench.wrapper.dto.DOrderQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * D类订单接口封装
 */
@Service
@Slf4j
public class DOrderWrapper {

    @Autowired
    private DOrderQueryProperty dOrderQueryProperty;
    @Resource
    private UserVerifyInfoService userVerifyInfoService;
    @Autowired
    private WrapperUtils wrapperUtils;

    public DOrderQueryResponse getDOrders(String userId, Integer floatingBeforeDays){
        try{
            UserVerifyInfo userVerifyInfo = userVerifyInfoService.lambdaQuery().eq(UserVerifyInfo::getUserId, userId).one();
            if (userVerifyInfo == null || userVerifyInfo.getEcifId() == null){
                log.error("查询D类订单返回码异常， 未找到用户认证信息");
                return new DOrderQueryResponse().setStatus(false).setMessage("查询D类订单， 用户认证信息为空 "+ userId);
            }
            DOrderQueryReq dOrderQueryReq = DOrderQueryReq.builder()
                    .ecifId(userVerifyInfo.getEcifId()+"").empNo(dOrderQueryProperty.getDOrderQueryUser()).applyDate(LocalDateTime.now())
                    .floatingBeforeDays(floatingBeforeDays)
                    .channelCodeList(Arrays.asList("YRCF01", "YRCFHK01"))
                    .build();
            String params = JsonUtilExt.beanToJson(dOrderQueryReq);
            String url = dOrderQueryProperty.getDOrderEndPoint() + "/appointment/nearestInvestment";
            log.info("查询D类订单 url:{} param：{}", url, params);
            ResponseEntity<String> response = wrapperUtils.postJson(url, params);
            String respBody = response.getBody();
            log.info("查询D类订单返回结果：{}", respBody);
            if (response.getStatusCode() != HttpStatus.OK){
                log.error("查询D类订单返回码异常， {}, {}", response.getStatusCode(), response.getBody());
                return new DOrderQueryResponse().setStatus(false).setMessage("查询D类订单返回码异常 "+ response.getStatusCode());
            }
            return JsonUtilExt.jsonToBean(respBody, new TypeReference<DOrderQueryResponse>() {});
        }catch (Exception e){
            log.error("查询D类订单异常", e);
        }
        return new DOrderQueryResponse().setStatus(false);
    }
    
}
