package com.yirendai.workbench.readonly.controller;

import com.yirendai.workbench.vo.req.salesProcess.DOrderApiReq;
import com.yirendai.workbench.vo.res.ApiRes;
import com.yirendai.workbench.wrapper.DOrderWrapper;
import com.yirendai.workbench.wrapper.dto.DOrderQueryResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Slf4j
@Api(value = "D类订单记录-只读", tags = "D类订单记录-只读")
@RestController
@RequestMapping("/api/crm/readonly/dOrder")
public class DOrderController {

    @Autowired
    private DOrderWrapper dOrderWrapper;

    @ApiOperation(value = "是否购买了D类-销售流程", tags = "是否购买了D类-销售流程")
    @PostMapping("/hasDOrder")
    public R<ApiRes> hasDOrder(@RequestBody DOrderApiReq apiReq) {
        // 计算当前时间和开始时间的相差天数
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = apiReq.getStartTime();
        Integer floatingBeforeDays = startTime != null ? (int) ChronoUnit.DAYS.between(startTime, now) + 1 : null;
        
        DOrderQueryResponse dOrderQueryResponse = dOrderWrapper.getDOrders(apiReq.getCustomerId(), floatingBeforeDays);
        if (dOrderQueryResponse == null || dOrderQueryResponse.getData() == null){
            return R.data(new ApiRes().setResult(false));
        }
        String policyState = apiReq.getPolicyState();
        for (DOrderQueryResponse.NearestAppointmentVO dOrder : dOrderQueryResponse.getData()) {
            if (isDOrderMatch(dOrder, policyState, apiReq.getStartTime().toLocalDate())){
                return R.data(new ApiRes().setResult(true)
                        .setFinishTime(dOrder.getHoldDate().atStartOfDay()).setRemark("保单号:"+dOrder.getPolicyNo()));
            }

        }
        return R.data(new ApiRes().setResult(false));
    }

    private boolean isDOrderMatch(DOrderQueryResponse.NearestAppointmentVO dOrder, String policyState, LocalDate holdDate){
        if (dOrder.getHoldDate() == null){
            log.info("投保日期为空， orderId: {}", dOrder.getPolicyNo());
            return false;
        }
        if (policyState == null || policyState.equals(dOrder.getPolicyState())){
            return !holdDate.isAfter(dOrder.getHoldDate());
        }
        return false;
    }


}
