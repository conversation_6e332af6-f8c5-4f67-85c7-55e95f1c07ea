package com.yirendai.workbench.readonly.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.readonly.entity.PlannerUserBindReadonly;
import com.yirendai.workbench.readonly.mapper.PlannerUserBindReadonlyMapper;
import com.yirendai.workbench.readonly.service.IPlannerUserBindReadonlyService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 理财师用户绑定历史表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2025-10-10
 */
@Service
public class PlannerUserBindReadonlyServiceImpl
        extends ServiceImpl<PlannerUserBindReadonlyMapper, PlannerUserBindReadonly>
        implements IPlannerUserBindReadonlyService {

    public List<PlannerUserBindReadonly> selectUncompletedByProcessNo(Integer startId, String processNo, Integer limit){
        return this.baseMapper.selectUncompletedByProcessNo(startId, processNo, limit);
    }
}
