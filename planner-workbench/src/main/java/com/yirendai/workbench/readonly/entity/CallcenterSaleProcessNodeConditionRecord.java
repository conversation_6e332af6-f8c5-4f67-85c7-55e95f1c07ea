package com.yirendai.workbench.readonly.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 销售流程节点条件进度记录表
 * </p>
 * <AUTHOR>
 * @since 2025-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CallcenterSaleProcessNodeConditionRecord对象", description = "销售流程节点条件进度记录表")
public class CallcenterSaleProcessNodeConditionRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "销售流程id")
    private Long saleProcessId;

    @ApiModelProperty(value = "节点id")
    private String nodeId;

    @ApiModelProperty(value = "条件id")
    private Long conditionId;

    @ApiModelProperty(value = "附加信息")
    private String extraInfo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime finishTime;

    @ApiModelProperty(value = "完成类型 1 手动 2 系统")
    private Integer finishType;

    @ApiModelProperty(value = "完成人")
    private String finisher;

    @ApiModelProperty(value = "完成状态 1 完成 2 未完成")
    private Integer status;

    @ApiModelProperty(value = "是否删除（0-未删除，1-已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

}
