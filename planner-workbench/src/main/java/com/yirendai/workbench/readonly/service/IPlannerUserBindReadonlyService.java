package com.yirendai.workbench.readonly.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.readonly.entity.PlannerUserBindReadonly;

/**
 * <p>
 * 理财师用户绑定历史表 服务类
 * </p>
 * <AUTHOR>
 * @since 2025-10-10
 */
public interface IPlannerUserBindReadonlyService extends IService<PlannerUserBindReadonly> {

    List<PlannerUserBindReadonly> selectUncompletedByProcessNo(Integer startId, String processNo, Integer limit);

}
