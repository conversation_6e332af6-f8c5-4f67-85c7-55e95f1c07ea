package com.yirendai.workbench.readonly.controller;

import com.yirendai.workbench.readonly.entity.SaleProcessFpcInfo;
import com.yirendai.workbench.readonly.service.CallRecordReadOnlyService;
import com.yirendai.workbench.vo.req.salesProcess.ApiReq;
import com.yirendai.workbench.vo.res.ApiRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "FPC分配记录-只读", tags = "FPC分配记录-只读")
@RestController
@RequestMapping("/api/crm/readonly/fpc")
public class FpcReadOnlyController {

    @Autowired
    private CallRecordReadOnlyService callRecordReadOnlyService;

    @ApiOperation(value = "是否分配了FPC-销售流程", tags = "是否分配了FPC-销售流程")
    @PostMapping("/hasFPCRelation")
    public R<ApiRes> hasFPCRelation(ApiReq apiReq) {
        SaleProcessFpcInfo fPCRelation = callRecordReadOnlyService.getFPCRelation(
                apiReq.getCustomerId(),
                apiReq.getStartTime());
        if (fPCRelation == null){
            return R.data(new ApiRes().setResult(false));
        }

        return R.data(new ApiRes().setResult(true)
                .setFinishTime(fPCRelation.getAssignTime()).setRemark("staffId:"+fPCRelation.getStaffId()));
    }

}
