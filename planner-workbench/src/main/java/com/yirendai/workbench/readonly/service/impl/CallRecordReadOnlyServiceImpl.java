package com.yirendai.workbench.readonly.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.readonly.entity.CallcenterCallRecordReadonly;
import com.yirendai.workbench.readonly.entity.SaleProcessCallRecordInfo;
import com.yirendai.workbench.readonly.entity.SaleProcessFpcInfo;
import com.yirendai.workbench.readonly.mapper.CallRecordReadOnlyMapper;
import com.yirendai.workbench.readonly.service.CallRecordReadOnlyService;
import com.yirendai.workbench.vo.res.salesProcess.CustomerCallSummaryInfo;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class CallRecordReadOnlyServiceImpl extends ServiceImpl<CallRecordReadOnlyMapper, CallcenterCallRecordReadonly>
        implements CallRecordReadOnlyService {
    
    @Override
    public SaleProcessCallRecordInfo getCallOfSpecificTimes(String customerId, int specificTimes,
                                                     LocalDateTime fromTime, LocalDateTime toTime,
                                                     String tenantId){
        specificTimes = specificTimes - 1;
        return baseMapper.getCallOfSpecificTimes(customerId, specificTimes, fromTime, toTime, tenantId);
    }

    @Override
    public SaleProcessCallRecordInfo getFirstAnsweredCall(String customerId,
                                                          LocalDateTime fromTime, LocalDateTime toTime,
                                                          String tenantId){
        return baseMapper.getFirstAnsweredCall(customerId, fromTime, toTime, tenantId);
    }

    @Override
    public SaleProcessCallRecordInfo getFirstOverBillSecCall(String customerId,
                                                             Long totalBillsec,
                                                             LocalDateTime fromTime, LocalDateTime toTime,
                                                             String tenantId) {
        return baseMapper.getFirstOverBillSecCall(customerId, totalBillsec, fromTime, toTime, tenantId);
    }

    @Override
    public SaleProcessFpcInfo getFPCRelation(String customerId, LocalDateTime sinceTime) {
        return baseMapper.getFPCRelation(customerId, sinceTime);
    }
}
