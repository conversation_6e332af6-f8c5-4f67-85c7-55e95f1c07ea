package com.yirendai.workbench.readonly.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeConditionRecord;
import com.yirendai.workbench.vo.res.callcenter.CallcenterNodeConditionNumbInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售流程节点条件进度记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
public interface CallcenterSaleProcessNodeConditionRecordReadonlyMapper extends BaseMapper<CallcenterSaleProcessNodeConditionRecord> {

    List<CallcenterNodeConditionNumbInfo> countNumberByNodeIds(Map<String,Object> paramMap);

    List<CallcenterSaleProcessNodeConditionRecord> selectListByMap(Map<String,Object> paramMap);

}
