package com.yirendai.workbench.readonly.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 自建呼叫中心通话数据
 * @TableName callcenter_call_record
 */
@TableName(value ="callcenter_call_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallcenterCallRecordReadonly implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * uuid（电话唯一标识）
     */
    private String uuid;

    /**
     * 系统类型（1: avaya, 2: new callcenter）
     */
    private Integer systemType;

    /**
     * 呼叫类型 (1: 呼入, 2： 呼出)
     */
    private Integer callType;
    /**
     * 触达方式    1,"系统"，2,"系统调用手机"， 3,"手机"
     */
    private Integer contactWay;
    /**
     * 坐席ID（分机）
     */
    private String agentId;

    /**
     * 所属部门
     */
    private String agentDept;

    /**
     * 坐席用户id
     */
    private Long agentUserId;

    /**
     * 拨打电话的理财师号
     */
    private String callPlannerId;

    /**
     * 坐席登录名(邮箱前缀)
     */
    private String user;

    /**
     * 员工姓名
     */
    private String agentName;

    /**
     * 客户uid，对应业务系统客户的唯一id
     */
    private String customerUid;

    /**
     * 业务系统客户姓名
     */
    private String customerName;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 通话是否接听（1:未接，2:接听）
     */
    private Integer answered;

    /**
     * 呼叫发起的日期/时间
     */
    private LocalDateTime startStamp;

    /**
     * 呼叫终止的日期/时间
     */
    private LocalDateTime endStamp;

    /**
     * 响铃时长
     */
    private Long ring;

    /**
     * 实际应答呼叫远端的日期/时间 如果未接听电话，则为空字符串
     */
    private LocalDateTime answerStamp;

    /**
     * 总呼叫持续时间
     */
    private Long duration;

    /**
     * 挂断方（1:客户Client 2 :坐席Staff）
     */
    private Integer hangupBy;

    /**
     * 挂断原因
     */
    private String hangupCause;

    /**
     * 呼叫方向，针对fs 来说，outbound、inbound、可以用来判断声道
     */
    private String direction;

    /**
     * 音频文件路径
     */
    private String recordUrl;

    @TableField(exist = false)
    private String nasPath;

    /**
     * 可计费的通话时长（秒）可计费时间不包括在远端接听电话之前在“早期媒体”中花费的通话时间
     */
    private Long billsec;

    /**
     * 是否需要小结
     */
    private Boolean needSumup;

    /**
     * 用户当前最新绑定的理财师
     */
    private String plannerId;

    /**
     * 记录呼叫的手机号是本人或其他关系
     */
    private String relationship;
    /**
     * 关联ID，同主叫方UUID
     */
    private String callUuid;

    /**
     *
     */
    private String coreUuid;

    /**
     * bridge的id
     */
    private String bridgeUuid;

    /**
     * 主叫方昵称
     */
    private String callerIdName;

    /**
     * 主叫号码
     */
    private String callerIdNumber;

    /**
     * 被叫号码
     */
    private String destinationNumber;

    /**
     * 挂断描述
     */
    private String hangupDisposition;

    /**
     * 网络ip
     */
    private String gwIp;

    /**
     * 网关名
     */
    private String gwName;

    /**
     * 是否10秒内通话（1:是,0 ：否;没接通是null）
     */
    private Integer callTalking10;

    /**
     * 会话类型（1:普通外呼 2:预测外呼）
     */
    private Integer sessionType;

    /**
     * 桥接挂断原因
     */
    private String bridgeHangupCause;

    /**
     * 业务数据（随路）
     */
    private String busData;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 挂断短语（FS话单返回内容）
     */
    private String hangupPhrase;

    /**
     * 等待时长（从呼叫开始到进入队列或有足够的空闲线路可用的时间）
     */
    private Long waitsec;

    /**
     * 桥接时间
     */
    private LocalDateTime bridgeStamp;

    /**
     * 坐席呼叫结果（1:未接 2:接听）
     */
    private Integer agentAnswered;

    /**
     * 坐席呼叫发起的日期/时间
     */
    private LocalDateTime staffStartStamp;

     /**
     * 客户呼叫结果（1:未接 2:接听）
     */
    private Integer csAnswered;
     /**
     * 客户实际应答呼叫远端的日期/时间 如果未接听电话，则为空字符串
     */
    private LocalDateTime csAnswerStamp;
     /**
     * 客户呼叫终止的日期/时间
     */
    private LocalDateTime csEndStamp;
     /**
     * 客户总呼叫持续时间
     */
    private Long csDuration;
     /**
     * 客户响铃时长
     */
    private Long csRing;
     /**
     * 客户可计费的通话时长（秒）可计费时间不包括在远端接听电话之前在“早期媒体”中花费的通话时间
     */
    private Long csBillsec;

    /**
     *
     */
    private LocalDateTime dataUpdateTime;

    /**
     *
     */
    private String tenantId;

    /**
     * 满意度（satisfied-满意, dissatisfied-不满意, basically_satisfied-一般）
     */
    private String satisfactionLevel;

    /**
     * 手机机型
     */
    private String deviceModel;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}