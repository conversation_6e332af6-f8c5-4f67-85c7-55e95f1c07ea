package com.yirendai.workbench.readonly.controller;

import com.yirendai.workbench.readonly.service.IWechatMessageBizRecordReadOnlyService;
import com.yirendai.workbench.vo.req.salesProcess.ApiReq;
import com.yirendai.workbench.vo.res.ApiRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 聊天信息业务记录表 控制器
 * </p>
 *
 * @since 2025-10-13
 */
@RestController
@RequestMapping("/api/crm/readonly/biz-message")
@Slf4j
@Api(value = "聊天信息业务记录管理", tags = "聊天信息业务记录管理")
public class WechatMessageBizRecordReadOnlyController {

    @Autowired
    private IWechatMessageBizRecordReadOnlyService wechatMessageBizRecordReadOnlyService;

    @PostMapping("/query/plan-book")
    @ApiOperation(value = "查询客户企划书下发状态", notes = "查询指定客户是否已下发企划书")
    public R<ApiRes> queryPlanBookStatus(@RequestBody ApiReq req) {
        ApiRes res = wechatMessageBizRecordReadOnlyService.queryPlanBookStatus(req);
        return R.data(res);
    }


}
