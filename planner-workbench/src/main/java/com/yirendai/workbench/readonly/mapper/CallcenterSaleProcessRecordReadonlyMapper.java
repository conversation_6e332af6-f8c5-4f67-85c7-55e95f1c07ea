package com.yirendai.workbench.readonly.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessRecord;
import com.yirendai.workbench.vo.res.CallcenterSaleProcessRecordRes;
import com.yirendai.workbench.vo.res.callcenter.CallcenterCustomerSaleProcessRecordResp;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售流程进度记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-14
 */
public interface CallcenterSaleProcessRecordReadonlyMapper extends BaseMapper<CallcenterSaleProcessRecord> {

    Integer countSaleProcessRecordListPage(Map<String,Object> paramMap);

    List<CallcenterSaleProcessRecordRes> getSaleProcessRecordListPage(Map<String,Object> paramMap);

    Integer countCustomerSaleProcessRecordListPage(Map<String,Object> paramMap);

    List<CallcenterCustomerSaleProcessRecordResp> getCustomerSaleProcessRecordListPage(Map<String,Object> paramMap);


}
