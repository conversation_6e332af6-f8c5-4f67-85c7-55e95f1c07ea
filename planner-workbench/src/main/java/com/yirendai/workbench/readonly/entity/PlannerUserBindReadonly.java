package com.yirendai.workbench.readonly.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 理财师用户绑定历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PlannerUserBind对象", description="理财师用户绑定历史表")
@TableName("planner_user_bind")
public class PlannerUserBindReadonly implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "理财师工号")
    private String plannerId;

    @ApiModelProperty(value = "理财师姓名")
    private String plannerName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "绑定开始时间")
    private LocalDateTime bindTime;

    @ApiModelProperty(value = "有效期截止时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "绑定备注说明")
    private String remark;

    @ApiModelProperty(value = "绑定来源 1-avaya 2-crm")
    private String resource;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "同步过来的源数据记录id")
    private Integer orgId;

    @ApiModelProperty(value = "绑定备注2")
    private String remark2;

    @ApiModelProperty(value = "绑定时理财师所在组")
    private String groupName;

    @ApiModelProperty(value = "上次接通时间")
    private LocalDateTime lastCallTime;

    @ApiModelProperty(value = "上次沟通时间")
    private LocalDateTime lastCommunicationTime;

    @ApiModelProperty(value = "一级结束码")
    private String visitParent;

    @ApiModelProperty(value = "二级结束吗")
    private String visitType;

    @ApiModelProperty(value = "营销分类")
    private String isSale;

    @ApiModelProperty(value = "预约沟通时间")
    private LocalDateTime appointmentTime;

    private String tenantId;

    @ApiModelProperty(value = "场景id")
    private Long sceneId;

    @ApiModelProperty(value = "开发周期-天")
    private String developCycle;

    @ApiModelProperty(value = "分配方式，1：理财师领取 2:管户指定 3：手动分配 4：后台理财师变更 5：app理财师变更")
    private Integer allocateType;


}
