package com.yirendai.workbench.readonly.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.readonly.entity.SaleProcessCallRecordInfo;
import com.yirendai.workbench.readonly.entity.SaleProcessFpcInfo;

import java.time.LocalDateTime;

public interface DOrderService {
    /**
     * 获取客户指定时间内，某次的通话信息
     */
    SaleProcessCallRecordInfo getCallOfSpecificTimes(String customerId,
                                                     int specificTimes,
                                                     LocalDateTime fromTime, LocalDateTime toTime,
                                                      String tenantId);



}
