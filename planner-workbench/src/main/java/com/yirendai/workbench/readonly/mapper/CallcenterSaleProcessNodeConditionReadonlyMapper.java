package com.yirendai.workbench.readonly.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNodeCondition;
import com.yirendai.workbench.vo.res.callcenter.CallcenterNodeConditionNumbInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 销售流程节点条件配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
public interface CallcenterSaleProcessNodeConditionReadonlyMapper extends BaseMapper<CallcenterSaleProcessNodeCondition> {

    List<CallcenterSaleProcessNodeCondition> selectListByIds(List<String> ids);

    List<CallcenterSaleProcessNodeCondition> selectListByNodeId(String nodeId);

    List<CallcenterNodeConditionNumbInfo> countNumberByNodeIds(List<String> nodeIds);
}
