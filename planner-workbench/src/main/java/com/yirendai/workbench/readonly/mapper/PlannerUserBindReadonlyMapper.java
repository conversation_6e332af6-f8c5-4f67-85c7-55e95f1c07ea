package com.yirendai.workbench.readonly.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.workbench.readonly.entity.PlannerUserBindReadonly;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 理财师用户绑定历史表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-10
 */
public interface PlannerUserBindReadonlyMapper extends BaseMapper<PlannerUserBindReadonly> {

    /**
     * 查询未完成指定流程编号的用户绑定记录
     *
     * @param startId 起始ID（大于此ID）
     * @param processNo 流程编号
     * @param limit 限制数量
     * @return 用户绑定记录列表
     */
    List<PlannerUserBindReadonly> selectUncompletedByProcessNo(
            @Param("startId") Integer startId,
            @Param("processNo") String processNo,
            @Param("limit") Integer limit
    );
}
