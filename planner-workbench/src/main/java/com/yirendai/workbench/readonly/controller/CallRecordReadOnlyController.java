package com.yirendai.workbench.readonly.controller;

import com.yirendai.workbench.readonly.entity.SaleProcessCallRecordInfo;
import com.yirendai.workbench.readonly.service.CallRecordReadOnlyService;
import com.yirendai.workbench.vo.req.salesProcess.ApiReq;
import com.yirendai.workbench.vo.res.ApiRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * CRM只读数据源控制器
 * 提供只读查询接口，减轻主库压力
 **/
@Api(value = "呼叫记录相关-只读", tags = "呼叫记录相关-只读")
@RestController
@RequestMapping("/api/crm/readonly/callrecord")
public class CallRecordReadOnlyController {

    @Autowired
    private CallRecordReadOnlyService callRecordReadOnlyService;

    @ApiOperation(value = "客户通话次数是否满足-销售流程", tags = "客户通话次数是否满足-销售流程")
    @PostMapping("/isCallTimesMath")
    public R<ApiRes> isSaleProcessCallTimesMath(ApiReq apiReq) {
        if (apiReq.getExpectedValue() == null){
            return R.fail("expectedValue不能为空");
        }
        int specificTimes = Integer.parseInt(apiReq.getExpectedValue());
        if (specificTimes <= 0){
            return R.fail("期望通话次数必须大于0");
        }
        SaleProcessCallRecordInfo callRecordInfo = callRecordReadOnlyService.getCallOfSpecificTimes(
                apiReq.getCustomerId(), specificTimes,
                apiReq.getStartTime(), LocalDateTime.now(),
                apiReq.getTenantId());
        if (callRecordInfo == null){
            return R.data(new ApiRes().setResult(false));
        }

        return R.data(new ApiRes().setResult(true)
                .setFinishTime(callRecordInfo.getStartStamp()).setRemark("uuid:"+callRecordInfo.getUuid()));
    }

    @ApiOperation(value = "客户通话是否已接通-销售流程", tags = "客户通话是否已接通-销售流程")
    @PostMapping("/isCallAnswered")
    public R<ApiRes> isSaleProcessCallAnswered(ApiReq apiReq) {
        SaleProcessCallRecordInfo callRecordInfo = callRecordReadOnlyService.getFirstAnsweredCall(
                apiReq.getCustomerId(), apiReq.getStartTime(), LocalDateTime.now(),
                apiReq.getTenantId());
        if (callRecordInfo == null){
            return R.data(new ApiRes().setResult(false));
        }

        return R.data(new ApiRes().setResult(true)
                .setFinishTime(callRecordInfo.getStartStamp()).setRemark("uuid:"+callRecordInfo.getUuid()));
    }

    @ApiOperation(value = "客户通话是否满足有效沟通-销售流程", tags = "客户通话是否满足有效沟通-销售流程")
    @PostMapping("/isCallBillTimeEnough")
    public R<ApiRes> isSaleProcessCallBillTimeEnough(ApiReq apiReq) {
        if (apiReq.getExpectedValue() == null){
            return R.fail("expectedValue不能为空");
        }
        //单位分钟
        Long totalBillsec = Long.parseLong(apiReq.getExpectedValue()) * 60;
        if (totalBillsec <= 0){
            return R.fail("期望通话时长必须大于0");
        }
        SaleProcessCallRecordInfo callRecordInfo = callRecordReadOnlyService.getFirstOverBillSecCall(
                apiReq.getCustomerId(), totalBillsec, apiReq.getStartTime(), LocalDateTime.now(),
                apiReq.getTenantId());
        if (callRecordInfo == null){
            return R.data(new ApiRes().setResult(false));
        }

        return R.data(new ApiRes().setResult(true)
                .setFinishTime(callRecordInfo.getStartStamp()).setRemark("uuid:"+callRecordInfo.getUuid()));
    }

}
