package com.yirendai.workbench.readonly.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 销售流程节点基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CallcenterSaleProcessNode对象", description="销售流程节点基础信息表")
public class CallcenterSaleProcessNode implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "销售流程id")
    private Long saleProcessId;

    @ApiModelProperty(value = "节点名称")
    private String name;

    @ApiModelProperty(value = "节点描述")
    private String description;

    @ApiModelProperty(value = "节点类型")
    private Integer type;

    @ApiModelProperty(value = "进度值")
    private BigDecimal progressValue;

    @ApiModelProperty(value = "准出规则")
    private Integer clearanceRule;

    @ApiModelProperty(value = "是否允许手动推进或结束 1 允许 2 不允许")
    private Integer allowManual;

    @ApiModelProperty(value = "是否删除（0-未删除，1-已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;


}
