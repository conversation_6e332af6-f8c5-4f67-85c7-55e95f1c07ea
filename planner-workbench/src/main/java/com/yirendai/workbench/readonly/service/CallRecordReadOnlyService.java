package com.yirendai.workbench.readonly.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.readonly.entity.CallcenterCallRecordReadonly;
import com.yirendai.workbench.readonly.entity.SaleProcessCallRecordInfo;
import com.yirendai.workbench.readonly.entity.SaleProcessFpcInfo;
import com.yirendai.workbench.vo.res.salesProcess.CustomerCallSummaryInfo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

public interface CallRecordReadOnlyService extends IService<CallcenterCallRecordReadonly> {
    /**
     * 获取客户指定时间内，某次的通话信息
     */
    SaleProcessCallRecordInfo getCallOfSpecificTimes(String customerId,
                                                     int specificTimes,
                                                     LocalDateTime fromTime, LocalDateTime toTime,
                                                      String tenantId);


    /**
     * 获取客户指定时间内，第一个接通的通话信息
     */
    SaleProcessCallRecordInfo getFirstAnsweredCall(String customerId,
                                                   LocalDateTime fromTime, LocalDateTime toTime,
                                                    String tenantId);


    /**
     * 获取总通话累计时长大于没长度，，第一个接通的通话信息
     */
    SaleProcessCallRecordInfo getFirstOverBillSecCall(String customerId,
                                                       Long totalBillsec,
                                                      LocalDateTime fromTime, LocalDateTime toTime,
                                                       String tenantId);

    SaleProcessFpcInfo getFPCRelation(String customerId, LocalDateTime sinceTime);
}
