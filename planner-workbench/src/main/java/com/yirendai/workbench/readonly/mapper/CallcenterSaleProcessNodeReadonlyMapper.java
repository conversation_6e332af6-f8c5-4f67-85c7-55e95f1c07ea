package com.yirendai.workbench.readonly.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.workbench.readonly.entity.CallcenterSaleProcessNode;

import java.util.List;

/**
 * <p>
 * 销售流程节点基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-10-14
 */
public interface CallcenterSaleProcessNodeReadonlyMapper extends BaseMapper<CallcenterSaleProcessNode> {

    public CallcenterSaleProcessNode selectBeanById(String id);

    List<CallcenterSaleProcessNode> selectListByIds(List<String> ids);


}
