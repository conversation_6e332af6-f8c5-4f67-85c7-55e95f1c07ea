package com.yirendai.workbench.readonly.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.workbench.entity.CallcenterCallRecord;
import com.yirendai.workbench.readonly.entity.CallcenterCallRecordReadonly;
import com.yirendai.workbench.readonly.entity.SaleProcessCallRecordInfo;
import com.yirendai.workbench.readonly.entity.SaleProcessFpcInfo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 只读数据库的通话记录Mapper
 * 复用CallcenterCallRecordMapper的所有SQL方法，但通过独立的XML配置避免bean冲突
 */
public interface CallRecordReadOnlyMapper extends BaseMapper<CallcenterCallRecordReadonly> {

    /**
     * 获取客户指定时间内，某次的通话信息
     */
    SaleProcessCallRecordInfo getCallOfSpecificTimes(@Param("customerId")String customerId,
                                                     @Param("specificTimes")int specificTimes,
                                                     @Param("fromTime")LocalDateTime fromTime, @Param("toTime")LocalDateTime toTime,
                                                     @Param("tenantId") String tenantId);


    /**
     * 获取客户指定时间内，第一个接通的通话信息
     */
    SaleProcessCallRecordInfo getFirstAnsweredCall(@Param("customerId")String customerId,
                           @Param("fromTime")LocalDateTime fromTime, @Param("toTime")LocalDateTime toTime,
                           @Param("tenantId") String tenantId);


    /**
     * 获取总通话累计时长大于没长度，，第一个接通的通话信息
     */
    SaleProcessCallRecordInfo getFirstOverBillSecCall(@Param("customerId")String customerId,
                                                      @Param("totalBillsec") Long totalBillsec,
                                                    @Param("fromTime")LocalDateTime fromTime, @Param("toTime")LocalDateTime toTime,
                                                    @Param("tenantId") String tenantId);

    SaleProcessFpcInfo getFPCRelation(String customerId, LocalDateTime sinceTime);
}
