package com.yirendai.workbench.readonly.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.workbench.entity.CallcenterWechatMessageBizRecord;
import com.yirendai.workbench.enums.callcenter.WechatMessageBizTypeEnum;
import com.yirendai.workbench.readonly.entity.CallcenterWechatMessageBizRecordReadOnly;
import com.yirendai.workbench.readonly.mapper.CallcenterWechatMessageBizRecordReadMapper;
import com.yirendai.workbench.readonly.service.IWechatMessageBizRecordReadOnlyService;
import com.yirendai.workbench.vo.req.salesProcess.ApiReq;
import com.yirendai.workbench.vo.res.ApiRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

@Slf4j
@Service
public class CallcenterWechatMessageBizRecordReadServiceImpl extends ServiceImpl<CallcenterWechatMessageBizRecordReadMapper, CallcenterWechatMessageBizRecordReadOnly> implements IWechatMessageBizRecordReadOnlyService {

    @Override
    public ApiRes queryPlanBookStatus(ApiReq req) {
        ApiRes apiRes = new ApiRes();
        apiRes.setResult(false);
        CallcenterWechatMessageBizRecordReadOnly record = this.lambdaQuery().eq(CallcenterWechatMessageBizRecordReadOnly::getUserId, req.getCustomerId())
                .eq(CallcenterWechatMessageBizRecordReadOnly::getBizType, WechatMessageBizTypeEnum.PLAN_BOOK_DISTRIBUTION.getCode())
                .ge(Objects.nonNull(req.getStartTime()), CallcenterWechatMessageBizRecordReadOnly::getCreateTime, req.getStartTime())
                .last("limit 1")
                .one();
        String result = "false";
        if (record != null) {
            result = "true";
        }
        apiRes.setResult(Objects.equals(req.getExpectedValue(), result));
        if (apiRes.getResult()) {
            apiRes.setFinishTime(Objects.nonNull(record) ? record.getCreateTime() : LocalDateTime.now());
        }
        return apiRes;
    }
}