package com.yirendai.robot.modules.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRule;
import com.yirendai.robot.modules.call.vo.*;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;

import java.util.List;

/**
 * 频次规则业务接口
 */
public interface IRobotCallFrequencyRuleService extends IService<RobotCallFrequencyRule> {

    IPage<RobotCallFrequencyRulePageVO> page(RobotCallFrequencyRuleQueryReq req);

    R<RobotCallFrequencyRuleOperateResp> create(RobotCallFrequencyRuleCreateReq req);

    R<RobotCallFrequencyRuleOperateResp> updateRule(RobotCallFrequencyRuleUpdateReq req);

    R<Void> changeStatus(RobotCallFrequencyRuleStatusReq req);

    IPage<RobotCallFrequencyOperationVO> operationPage(Long ruleId, Query query);

    List<RobotOptionVO> publishedRobotOptions(String keyword);
}
