/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 外呼通话任务表视图实体类
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@ApiModel(value = "RobotCallTasksVO对象", description = "外呼通话任务表")
public class RobotCallTasksVO  {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "任务ID")
	private Long id;

	@ApiModelProperty(value = "任务名称")
	private String taskName;

	@ApiModelProperty(value = "机器人ID")
	private Long robotId;

	@ApiModelProperty(value = "机器人名称")
	private String robotName;

	@ApiModelProperty(value = "机器人版本号")
	private Integer robotVersion;

	/**
	 * {@link com.yirendai.robot.modules.call.enums.PriorityEnum}
	 */
	@ApiModelProperty(value = "优先级（0：低，1：中，2：高）")
	private Integer priority;

	@ApiModelProperty(value = "最大并发数")
	private Integer maxConcurrency;

	/**
	 * {@link com.yirendai.robot.modules.call.enums.ValidityTypeEnum}
	 */
	@ApiModelProperty(value = "任务有效期类型（0：日期类型，1：无终止日期）")
	private Integer validityType;

	@ApiModelProperty(value = "任务开始日期")
	private LocalDate startDate;

	@ApiModelProperty(value = "任务结束日期")
	private LocalDate endDate;

	@ApiModelProperty(value = "呼叫时间段开始")
	private LocalTime callTimeStart;

	@ApiModelProperty(value = "呼叫时间段结束")
	private LocalTime callTimeEnd;
	/**
	 * {@link com.yirendai.robot.modules.call.enums.TaskStatusEnum}
	 */
	@ApiModelProperty(value = "任务状态（0：待启动，1：执行中，2：已暂停，3：已结束）")
	private Integer taskStatus;

	@ApiModelProperty(value = "创建时间")
	private LocalDateTime createTime;

	@ApiModelProperty(value = "创建人")
	private String createdBy;

	@ApiModelProperty(value = "修改时间")
	private LocalDateTime updateTime;

	@ApiModelProperty(value = "修改人")
	private String updateBy;

	@ApiModelProperty(value = "启动时间")
	private LocalDateTime startExecutionTime;

	@ApiModelProperty(value = "启动人")
	private String startedBy;

	@ApiModelProperty(value = "是否删除（0：否，1：是）")
	private Integer isDeleted;

	@ApiModelProperty(value = "任务编码")
	private String taskCode;

	@ApiModelProperty(value = "租户ID")
	private String tenantId;

	/**
	 * {@link com.yirendai.robot.modules.call.enums.TaskCompletionReasonEnum}
	 */
	@ApiModelProperty(value = "任务终止原因")
	private String completionReason;


	@ApiModelProperty(value = "任务终止时间")
	private LocalDateTime completionTime;

	@ApiModelProperty(value = "呼叫线路")
	private List<RobotCallTaskLinesVO> taskLines;

	@ApiModelProperty(value = "线路ID")
	private List<Long> lineId;


	/**
	 * 执行中状态 {@link com.yirendai.robot.modules.call.enums.RunningStatusEnum}
	 */
	@ApiModelProperty(value = "执行中状态1 休息 2排队 3执行中")
	private Integer runningStatus;

	@ApiModelProperty(value = "号码数量")
	private Integer phoneNoCount;

	@ApiModelProperty(value = "已呼数量")
	private Integer calledCount;

	@ApiModelProperty(value = "呼叫进度")
	private Integer callProgress;
	@ApiModelProperty(value = "启动星期（1：星期一，2：星期二，3：星期三，4：星期四，5：星期五，6：星期六，7：星期日）")
	@NotEmpty(message = "启动星期不能为空")
	private List<Integer> startDay;

	@ApiModelProperty(value = "首次启动时间")
	private LocalDateTime firstStartDate;
	@ApiModelProperty(value = "机器人版本号ID")
	private Long robotHistoryId;

	@ApiModelProperty(value = "启动星期多个以逗号分割（1：星期一，2：星期二，3：星期三，4：星期四，5：星期五，6：星期六，7：星期日）")
	private String callTimeWeek;


	@ApiModelProperty(value = "任务批次号")
	private String taskNo;

	@ApiModelProperty(value = "是否开启重播(0否1开启)")
	private Integer isReplayEnabled;

	@ApiModelProperty(value = "重报条件对应状态多个用,分割")
	private String replayConditions;

	@ApiModelProperty(value = "重播间隔*分钟")
	private Integer replayIntervalMinutes;

	@ApiModelProperty(value = "重播次数")
	private Integer maxReplayTimes;

	@ApiModelProperty(value = " 重报条件对应状态多个用,分割文本")
	private String replayConditionsDesc;
}
