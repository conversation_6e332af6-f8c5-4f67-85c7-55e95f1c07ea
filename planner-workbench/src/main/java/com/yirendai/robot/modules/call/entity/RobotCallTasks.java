package com.yirendai.robot.modules.call.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 外呼通话任务表
 * </p>
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_robot_call_tasks")
@ApiModel(value = "RobotCallTasks对象", description = "外呼通话任务表")
public class RobotCallTasks implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "机器人ID")
    private Long robotId;

    @ApiModelProperty(value = "机器人版本号")
    private Integer robotVersion;

    /**
     * {@link com.yirendai.robot.modules.call.enums.PriorityEnum}
     */
    @ApiModelProperty(value = "优先级（0：低，1：中，2：高）")
    private Integer priority;

    @ApiModelProperty(value = "最大并发数")
    private Integer maxConcurrency;

    /**
     * {@link com.yirendai.robot.modules.call.enums.ValidityTypeEnum}
     */
    @ApiModelProperty(value = "任务有效期类型（0：日期类型，1：无终止日期）")
    private Integer validityType;

    @ApiModelProperty(value = "任务开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "任务结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "呼叫时间段开始")
    private LocalTime callTimeStart;

    @ApiModelProperty(value = "呼叫时间段结束")
    private LocalTime callTimeEnd;
    /**
     * {@link com.yirendai.robot.modules.call.enums.TaskStatusEnum}
     */
    @ApiModelProperty(value = "任务状态（0：待启动，1：执行中，2：已暂停，3：已结束）")
    private Integer taskStatus;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "启动时间")
    private LocalDateTime startExecutionTime;

    @ApiModelProperty(value = "启动人")
    private String startedBy;

    @ApiModelProperty(value = "是否删除（0：否，1：是）")
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    @ApiModelProperty(value = "任务编码")
    private String taskCode;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * {@link com.yirendai.robot.modules.call.enums.TaskCompletionReasonEnum}
     */
    @ApiModelProperty(value = "任务终止原因")
    private String completionReason;

    @ApiModelProperty(value = "任务终止时间")
    private LocalDateTime completionTime;
    /**
     * 执行中状态 {@link com.yirendai.robot.modules.call.enums.RunningStatusEnum}
     */
    @ApiModelProperty(value = "执行中状态1 休息 2排队 3执行中")
    private Integer runningStatus;

    @ApiModelProperty(value = "启动星期多个以逗号分割（1：星期一，2：星期二，3：星期三，4：星期四，5：星期五，6：星期六，7：星期日）")
    private String callTimeWeek;

    @ApiModelProperty(value = "任务批次号")
    private String taskNo;

    @ApiModelProperty(value = "是否开启重播(0否1开启)")
    private Integer isReplayEnabled;

    @ApiModelProperty(value = "重报条件对应状态多个用,分割")
    private String replayConditions;

    @ApiModelProperty(value = "重播间隔*分钟")
    private Integer replayIntervalMinutes;

    @ApiModelProperty(value = "重播次数")
    private Integer maxReplayTimes;
}
