package com.yirendai.robot.modules.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.robot.constant.RobotConstant;
import com.yirendai.robot.enums.YesOrNoEnum;
import com.yirendai.robot.modules.call.biz.pool.ExecutionPool;
import com.yirendai.robot.modules.call.biz.pool.RunnableCall;
import com.yirendai.robot.modules.call.dto.RobotCallTasksAddDTO;
import com.yirendai.robot.modules.call.dto.RobotCallTasksUpdateDTO;
import com.yirendai.robot.modules.call.dto.TaskCallCountDTO;
import com.yirendai.robot.modules.call.entity.RobotCallTasks;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.enums.*;
import com.yirendai.robot.modules.call.mapper.RobotCallTasksCustomerMapper;
import com.yirendai.robot.modules.call.mapper.RobotCallTasksMapper;
import com.yirendai.robot.modules.call.service.IRobotCallTaskLinesService;
import com.yirendai.robot.modules.call.service.IRobotCallTasksOperationRecordsService;
import com.yirendai.robot.modules.call.service.IRobotCallTasksService;
import com.yirendai.robot.modules.call.vo.RobotCallTasksQueryReqVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksSelectVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksVO;
import com.yirendai.robot.modules.robot.entity.RobotHistory;
import com.yirendai.robot.modules.robot.mapper.RobotHistoryMapper;
import com.yirendai.robot.modules.session.mapper.RobotSessionMapper;
import com.yirendai.robot.modules.tenant.entity.RobotTenant;
import com.yirendai.robot.modules.tenant.enums.RobotTenantStatusEnum;
import com.yirendai.robot.modules.tenant.service.IRobotTenantService;
import com.yirendai.robot.util.bizcode.BizCodeEnum;
import com.yirendai.robot.util.bizcode.BizCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 外呼通话任务表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2025-02-17
 */
@Service
@Slf4j
public class RobotCallTasksServiceImpl extends ServiceImpl<RobotCallTasksMapper, RobotCallTasks> implements IRobotCallTasksService {

    @Autowired
    private IRobotCallTasksOperationRecordsService robotCallTasksOperationRecordsService;

    @Autowired
    private IRobotCallTaskLinesService robotCallTaskLinesService;

    @Autowired
    private BizCodeUtil bizCodeUtil;

    @Resource
    private RobotCallTasksCustomerMapper robotCallTaskCustomerMapper;

    @Resource
    private RobotSessionMapper robotSessionMapper;

    @Autowired
    private ExecutionPool executionPool;

    @Autowired
    private IRobotTenantService robotTenantService;

    @Resource
    private JedisCluster jedisCluster;

    @Resource
    private RobotHistoryMapper robotHistoryMapper;

    /**
     * 新增外呼通话任务
     * @param robotCallTasksAddDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R addCallTask(RobotCallTasksAddDTO robotCallTasksAddDTO) {
        LocalDateTime now = LocalDateTime.now();
        //1.校验参数
        if (ValidityTypeEnum.DATE_TYPE.getKey().equals(robotCallTasksAddDTO.getValidityType())) {
            if (robotCallTasksAddDTO.getStartDate() == null || robotCallTasksAddDTO.getEndDate() == null) {
                return R.fail("日期类型任务开始日期和结束日期不能为空");
            }
            if (robotCallTasksAddDTO.getStartDate().isAfter(robotCallTasksAddDTO.getEndDate())) {
                return R.fail("任务开始日期不能大于任务结束日期");
            }
            if (robotCallTasksAddDTO.getStartDate().isBefore(now.toLocalDate())) {
                return R.fail("任务开始日期不能小于当前日期");
            }
        }
        //校验任务名称是否重复
        int count = baseMapper.selectCountByTaskName(robotCallTasksAddDTO.getTaskName(), robotCallTasksAddDTO.getTenantId(), null);
        if (count > 0) {
            return R.fail("任务名称重复");
        }
        RobotHistory robotHistory = robotHistoryMapper.getNewRobot(robotCallTasksAddDTO.getRobotId());
        if (robotHistory == null) {
            log.info("机器人不存在 robotId={}", robotCallTasksAddDTO.getRobotId());
            return R.fail("机器人不存在");
        }
        if(robotCallTasksAddDTO.getIsReplayEnabled()== null || robotCallTasksAddDTO.getIsReplayEnabled() == 0){
            robotCallTasksAddDTO.setIsReplayEnabled(0);
            robotCallTasksAddDTO.setReplayConditions(null);
            robotCallTasksAddDTO.setReplayIntervalMinutes(null);
            robotCallTasksAddDTO.setMaxReplayTimes(null);
        }

        //2.保存任务
        RobotCallTasks robotCallTasks = new RobotCallTasks();
        BeanUtil.copyProperties(robotCallTasksAddDTO, robotCallTasks);
        robotCallTasks.setTaskStatus(TaskStatusEnum.PENDING.getKey());
        robotCallTasks.setCreateTime(now);
        robotCallTasks.setTaskCode(bizCodeUtil.createBizCode(BizCodeEnum.CALL_TASK_CODE));
        robotCallTasks.setRobotVersion(robotHistory.getVersion());
        String callTimeWeek = robotCallTasksAddDTO.getStartDay().stream().map(String::valueOf).collect(Collectors.joining(","));
        robotCallTasks.setCallTimeWeek(callTimeWeek);
        this.save(robotCallTasks);
        //3.保存线路
        robotCallTaskLinesService.addCallTaskLines(robotCallTasks.getId(), robotCallTasksAddDTO.getTenantId(), robotCallTasksAddDTO.getLineId());
        //4.保存新增记录
        robotCallTasksOperationRecordsService.addRecord(OperationTypeEnum.ADD, robotCallTasks.getId(), now, robotCallTasks.getCreatedBy(), null);
        return R.success("新增成功");
    }

    /**
     * 删除任务
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R remove(Long id) {
        LocalDateTime now = LocalDateTime.now();
        String userName = AuthUtil.getUserName();
        String tenantId = AuthUtil.getTenantId();
        log.info("删除任务 taskId={} tenantId={} userName={}", id, tenantId, userName);
        RobotCallTasks task = this.baseMapper.selectByIdAndTenantId(id, tenantId);
        if (task == null) {
            log.info("删除失败，任务不存在 taskId={} tenantId={} userName={}", id, tenantId, userName);
            return R.fail("删除失败，任务不存在");
        }
//        if (!TaskStatusEnum.PENDING.getKey().equals(task.getTaskStatus())) {
//            log.info("删除失败，任务状态不为待启动 {}", id);
//            return R.fail("删除失败，任务状态不为待启动");
//        }
        //删除
        baseMapper.deleteById(id);
        // 保存新增记录
        robotCallTasksOperationRecordsService.addRecord(OperationTypeEnum.DELETE, id, now, userName, null);
        return R.success("删除成功");
    }

    /**
     * 结束任务
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R completion(Long id, TaskCompletionReasonEnum taskCompletionReasonEnum) {
        LocalDateTime now = LocalDateTime.now();
        String userName = AuthUtil.getUserName();
        String tenantId = AuthUtil.getTenantId();
        log.info("结束任务 taskId={} tenantId={} userName={}", id, tenantId, userName);
        RobotCallTasks task = this.baseMapper.selectByIdAndTenantId(id, tenantId);
        if (task == null) {
            log.info("结束失败，任务不存在 taskId={} tenantId={} userName={}", id, tenantId, userName);
            return R.fail("结束失败，任务不存在");
        }
        task.setTaskStatus(TaskStatusEnum.COMPLETED.getKey());
        task.setCompletionTime(now);
        task.setCompletionReason(taskCompletionReasonEnum.getDescription());
        this.baseMapper.updateById(task);
        robotCallTasksOperationRecordsService.addRecord(OperationTypeEnum.COMPLETION, id, now, userName, taskCompletionReasonEnum.getDescription());
        return R.success("结束成功");
    }

    /**
     * 暂停任务
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R pause(Long id) {
        LocalDateTime now = LocalDateTime.now();
        String userName = AuthUtil.getUserName();
        String tenantId = AuthUtil.getTenantId();
        log.info("暂停任务 taskId={} tenantId={} userName={}", id, tenantId, userName);
        RobotCallTasks task = this.baseMapper.selectByIdAndTenantId(id, tenantId);
        if (task == null) {
            log.info("暂停失败，任务不存在 taskId={} tenantId={} userName={}", id, tenantId, userName);
            return R.fail("暂停失败，任务不存在");
        }
        task.setTaskStatus(TaskStatusEnum.PAUSED.getKey());
        task.setUpdateTime(now);
        this.baseMapper.updateById(task);
        robotCallTasksOperationRecordsService.addRecord(OperationTypeEnum.PAUSE, id, now, userName, null);
        return R.success("暂停成功");
    }

    /**
     * 启动任务
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R start(Long id) {
        LocalDateTime now = LocalDateTime.now();
        String userName = AuthUtil.getUserName();
        String tenantId = AuthUtil.getTenantId();
        log.info("启动任务 taskId={} tenantId={} userName={}", id, tenantId, userName);
        RobotCallTasks task = this.baseMapper.selectByIdAndTenantId(id, tenantId);
        if (task == null) {
            log.info("启动失败，任务不存在 taskId={} tenantId={} userName={}", id, tenantId, userName);
            return R.fail("启动失败，任务不存在");
        }
        if (TaskStatusEnum.COMPLETED.getKey().equals(task.getTaskStatus())) {
            log.info("启动失败，任务状态不能启动 {}", id);
            return R.fail("启动失败，已结束任务不能启动");
        }
        if (TaskStatusEnum.IN_PROGRESS.getKey().equals(task.getTaskStatus())) {
            log.info("启动失败，任务状态不能启动 {}", id);
            return R.fail("启动失败，任务已经启动");
        }
        if (ValidityTypeEnum.DATE_TYPE.getKey().equals(task.getValidityType())) {
            if (now.toLocalDate().isAfter(task.getEndDate())) {
                log.info("启动失败，任务已经结束 {}", id);
                return R.fail("启动失败，任务已经结束");
            }
        }
        task.setTaskStatus(TaskStatusEnum.IN_PROGRESS.getKey());
        task.setRunningStatus(RunningStatusEnum.RESTING.getKey());
        task.setStartExecutionTime(now);
        task.setStartedBy(userName);
        this.baseMapper.updateById(task);
        robotCallTasksOperationRecordsService.addRecord(OperationTypeEnum.START, id, now, userName, null);
        return R.success("启动成功");
    }

    /**
     * 复制任务
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R copy(Long id) {
        LocalDateTime now = LocalDateTime.now();
        String userName = AuthUtil.getUserName();
        String tenantId = AuthUtil.getTenantId();
        log.info("复制任务 taskId={} tenantId={} userName={}", id, tenantId, userName);
        RobotCallTasks task = this.baseMapper.selectByIdAndTenantId(id, tenantId);
        if (task == null) {
            log.info("复制失败，任务不存在 taskId={} tenantId={} userName={}", id, tenantId, userName);
            return R.fail("复制失败，任务不存在");
        }
        //1.拷贝任务
        RobotCallTasks robotCallTasks = new RobotCallTasks();
        BeanUtil.copyProperties(task, robotCallTasks);
        robotCallTasks.setId(null);
        robotCallTasks.setTaskStatus(TaskStatusEnum.PENDING.getKey());
        robotCallTasks.setCreateTime(now);
        robotCallTasks.setTaskCode(bizCodeUtil.createBizCode(BizCodeEnum.CALL_TASK_CODE));
        robotCallTasks.setStartExecutionTime(null);
        robotCallTasks.setStartedBy(null);
        robotCallTasks.setCompletionTime(null);
        robotCallTasks.setCompletionReason(null);
        robotCallTasks.setUpdateTime(now);
        robotCallTasks.setCreatedBy(userName);
        String taskName = task.getTaskName();

        // 使用正则匹配所有以 "副本"开头且结尾为数字的情况（如 "副本1"、"副本123"）
        java.util.regex.Matcher matcher = java.util.regex.Pattern.compile("(.+?)(-副本)(\\d*)$").matcher(taskName);
        if (matcher.find()) {
            // 提取基础名称（如 "任务"）、副本标识（如 "-副本" 或 "副本"）、数字部分
            String baseName = matcher.group(1);   // 示例："任务"
            String copyTag = matcher.group(2);    // 示例："-副本"

            taskName = baseName + copyTag ; //  如 "任务-副本"
        }
        else if (!taskName.endsWith("-副本")) {
            // 若不符合条件，默认添加 "-副本"
            taskName = taskName + "-副本";
        }
        String key = String.format(RobotConstant.TASK_NAME_TENANT, tenantId, taskName);
        Long num = jedisCluster.incr(key );
        if(num >1){
            taskName = taskName + (num-1);
        }
        // 更新任务名称（根据实际需求调用对应方法）
        robotCallTasks.setTaskName(taskName);
        this.save(robotCallTasks);
        //2.拷贝线路
        robotCallTaskLinesService.copy(task.getId(), robotCallTasks.getId());
        //3.保存新增记录
        robotCallTasksOperationRecordsService.addRecord(OperationTypeEnum.ADD, robotCallTasks.getId(), now, robotCallTasks.getCreatedBy(), null);
        return R.success("复制成功");

    }

    /**
     * 根据任务ID获取任务详情
     * @param id
     * @return
     */
    @Override
    public RobotCallTasksVO getRobotCallTasksById(Long id) {
        log.info("根据任务ID获取任务详情 id={}", id);
        String tenantId = AuthUtil.getTenantId();
        RobotCallTasks task = this.baseMapper.selectByIdAndTenantId(id, tenantId);
        if (task != null) {
            RobotCallTasksVO vo = new RobotCallTasksVO();
            BeanUtil.copyProperties(task, vo);
            RobotHistory robotHistoryDB = robotHistoryMapper.getNewRobot(task.getRobotId());
            if (robotHistoryDB != null) {
                vo.setRobotVersion(robotHistoryDB.getVersion());
                vo.setRobotName(robotHistoryDB.getRobotName());
                vo.setRobotHistoryId(robotHistoryDB.getId());
            }
            vo.setTaskLines(robotCallTaskLinesService.getRobotCallTaskLinesByTaskId(id));
            if (StringUtil.isNotBlank(task.getCallTimeWeek())) {
                List<Integer> weekList = Arrays.stream(task.getCallTimeWeek().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                vo.setStartDay(weekList);
            }
            List<TaskCallCountDTO> taskCallCountList = robotCallTaskCustomerMapper.selectTaskCallCount(Arrays.asList(id));
            if (CollectionUtil.isNotEmpty(taskCallCountList)) {
                vo.setCalledCount(taskCallCountList.get(0).getCallCount());
                vo.setPhoneNoCount(taskCallCountList.get(0).getPhoneCount());
            }
            if (vo.getCalledCount() != null && vo.getPhoneNoCount() != null && vo.getPhoneNoCount() > 0) {
                vo.setCallProgress((int) ((vo.getCalledCount() * 100) / vo.getPhoneNoCount()));
            }
            LocalDateTime firstStartDate = robotCallTasksOperationRecordsService.getFirstStartDate(id);
            vo.setFirstStartDate(firstStartDate);
            
            // 设置重播条件的中文描述
            if (StringUtil.isNotBlank(task.getReplayConditions())) {
                String[] conditionCodes = task.getReplayConditions().split(",");
                StringBuilder descBuilder = new StringBuilder();
                for (int i = 0; i < conditionCodes.length; i++) {
                    try {
                        Integer code = Integer.parseInt(conditionCodes[i].trim());
                        com.yirendai.robot.enums.RobotCallResult result = com.yirendai.robot.enums.RobotCallResult.getByCode(code);
                        if (result != null) {
                            if (i > 0) {
                                descBuilder.append(",");
                            }
                            descBuilder.append(result.getMessage());
                        }
                    } catch (NumberFormatException e) {
                        log.warn("Invalid replay condition code: {}", conditionCodes[i]);
                    }
                }
                vo.setReplayConditionsDesc(descBuilder.toString());
            }
            
            return vo;
        }
        else {
            return null;
        }
    }

    /**
     * 修改外呼通话任务
     * @param robotCallTasks
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateCallTask(RobotCallTasksUpdateDTO robotCallTasks) {
        String tenantId = AuthUtil.getTenantId();
        String userName = AuthUtil.getUserName();
        LocalDateTime now = LocalDateTime.now();
        log.info("修改任务 id={} tenantId={} ", robotCallTasks.getId(), tenantId);
        if (ValidityTypeEnum.DATE_TYPE.getKey().equals(robotCallTasks.getValidityType())) {
            if (robotCallTasks.getStartDate() == null || robotCallTasks.getEndDate() == null) {
                return R.fail("日期类型任务开始日期和结束日期不能为空");
            }
            if (robotCallTasks.getStartDate().isAfter(robotCallTasks.getEndDate())) {
                return R.fail("任务开始日期不能大于任务结束日期");
            }
            if (robotCallTasks.getStartDate().isBefore(now.toLocalDate())) {
                return R.fail("任务开始日期不能小于当前日期");
            }
        }
        RobotCallTasks task = this.baseMapper.selectByIdAndTenantId(robotCallTasks.getId(), tenantId);
        if (task == null) {
            log.info("修改失败，任务不存在 id={} tenantId={} ", robotCallTasks.getId(), tenantId);
            return R.fail("任务不存在");
        }
        if (TaskStatusEnum.IN_PROGRESS.getKey().equals(task.getTaskStatus())) {
            log.info("修改失败，任务状态不能修改 id={} tenantId={} ", robotCallTasks.getId(), tenantId);
            return R.fail("任务执行中不能修改");
        }
        //校验任务名称是否重复
        int count = baseMapper.selectCountByTaskName(robotCallTasks.getTaskName(),tenantId, robotCallTasks.getId());
        if (count > 0) {
            return R.fail("任务名称重复");
        }
        if(YesOrNoEnum.NO.getValue().equals(robotCallTasks.getIsReplayEnabled())){
            robotCallTasks.setReplayConditions(null);
            robotCallTasks.setReplayIntervalMinutes(null);
            robotCallTasks.setMaxReplayTimes(null);
        }


        String callTimeWeek = StringUtil.join(robotCallTasks.getStartDay(), ",");
        RobotCallTasks updateTask = new RobotCallTasks();
        BeanUtil.copyProperties(robotCallTasks, updateTask);
        updateTask.setCallTimeWeek(callTimeWeek);
        updateTask.setUpdateTime(now);
        updateTask.setUpdateBy(userName);
        //1.修改任务
        this.baseMapper.updateByIdSelf(updateTask);
        //2.修改线路
        robotCallTaskLinesService.updateCallTaskLines(robotCallTasks.getId(), tenantId, robotCallTasks.getLineId());
        //3.保存记录
        robotCallTasksOperationRecordsService.addRecord(OperationTypeEnum.UPDATE, robotCallTasks.getId(), now, userName, null);
        return R.success("修改成功");
    }

    /**
     * 分页查询任务列表
     * @param queryReqVO
     * @param page
     * @return
     */
    @Override
    public IPage<RobotCallTasksVO> pageList(RobotCallTasksQueryReqVO queryReqVO, IPage<RobotCallTasksVO> page) {
        IPage<RobotCallTasksVO> pages = this.baseMapper.pageList(queryReqVO, page);
        if (pages.getRecords() != null && pages.getRecords().size() > 0) {
            List<Long> taskIds = new ArrayList<>();
            List<Long> robotIds = new ArrayList<>();
            for (RobotCallTasksVO vo : pages.getRecords()) {
                taskIds.add(vo.getId());
                robotIds.add(vo.getRobotId());
                List<Long> lineIds = robotCallTaskLinesService.getRobotCallTaskLineIdByTaskId(vo.getId());
                vo.setLineId(lineIds);
            }

            List<TaskCallCountDTO> taskCallCountList = robotCallTaskCustomerMapper.selectTaskCallCount(taskIds);
            List<RobotHistory> robotHistoryList = robotHistoryMapper.getNewRobotVersion(robotIds);
            for (RobotCallTasksVO vo : pages.getRecords()) {
                for (TaskCallCountDTO dto : taskCallCountList) {
                    if (vo.getId().equals(dto.getTaskId())) {
                        vo.setCalledCount(dto.getCallCount());
                        vo.setPhoneNoCount(dto.getPhoneCount());
                    }
                }
                if (vo.getCalledCount() != null && vo.getPhoneNoCount() != null && vo.getPhoneNoCount() > 0) {
                    vo.setCallProgress((int) ((vo.getCalledCount() * 100) / vo.getPhoneNoCount()));
                }
                for (RobotHistory robotHistory : robotHistoryList) {
                    if (vo.getRobotId().equals(robotHistory.getRobotId())) {
                        vo.setRobotVersion(robotHistory.getVersion());
                        vo.setRobotName(robotHistory.getRobotName());
                        vo.setRobotHistoryId(robotHistory.getId());
                    }
                }
                if (StringUtil.isNotBlank(vo.getCallTimeWeek())) {
                    List<Integer> weekList = Arrays.stream(vo.getCallTimeWeek().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                    vo.setStartDay(weekList);
                }
            }
        }
        return pages;
    }

    /**
     * 测试外呼任务
     * @param data
     * @return
     */
    @Override
    public R testCallTask(RobotCallTasksCustomer data) {
        RunnableCall runnableCall = new RunnableCall();
        runnableCall.setRobotCallTasksCustomer(data);
        RobotCallTasks task = this.baseMapper.selectByIdAndTenantId(data.getTaskId(), data.getTenantId());
        runnableCall.setRobotCallTasks(task);
        RobotTenant robotTenant = robotTenantService.getRobotTenantById(task.getTenantId());
        if (robotTenant == null) {
            return R.fail("租户外呼权限不存在");
        }
        if (robotTenant.getStatus().equals(RobotTenantStatusEnum.DISABLED.getKey())) {
            return R.fail("租户外呼权限已停用");
        }
        if (robotTenant.getRobotConcurrency() <= 0) {
            return R.fail("租户外呼并发量不足");
        }
        runnableCall.setRobotTenant(robotTenantService.getRobotTenantById(task.getTenantId()));
        runnableCall.setIsTest(YesOrNoEnum.YES.getValue());
        return executionPool.execute(runnableCall);
    }

    /**
     * 查询租户下所有启动状态的任务
     * @param tenantId
     * @return
     */
    @Override
    public List<RobotCallTasks> getEnableCallTasks(String tenantId) {
        return this.baseMapper.getEnableCallTasks(tenantId);
    }

    /**
     * 更新任务执行状态
     * @param taskId
     * @param runningStatus
     */
    @Override
    public void updateRunningStatus(Long taskId, Integer runningStatus) {
        this.baseMapper.updateRunningStatus(taskId, runningStatus);
    }

    /**
     * 获取任务执行状态
     * @param taskId
     * @return
     */
    @Override
    public Integer getStatus(Long taskId) {
        return baseMapper.getStatus(taskId);
    }

    /**
     * 任务下拉选项
     * @param tenantId
     * @return
     */
    @Override
    public List<RobotCallTasksSelectVO> selectList(String tenantId) {
        return baseMapper.selectTaskList(tenantId);
    }

}
