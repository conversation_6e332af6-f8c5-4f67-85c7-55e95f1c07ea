package com.yirendai.robot.modules.call.biz.pool;

import com.yirendai.robot.modules.call.biz.pool.constant.CallResultCode;
import com.yirendai.robot.modules.call.entity.RobotCallTasks;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.enums.RunningStatusEnum;
import com.yirendai.robot.modules.call.enums.TaskCompletionReasonEnum;
import com.yirendai.robot.modules.call.enums.ValidityTypeEnum;
import com.yirendai.robot.modules.call.service.IRobotCallTasksCustomerService;
import com.yirendai.robot.modules.call.service.IRobotCallTasksService;
import com.yirendai.robot.modules.tenant.entity.RobotTenant;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.yirendai.robot.modules.call.biz.pool.constant.CallRedisKeyConstant.CALL_TENANT_ENTRANCE_LOCK;

/**
 * 呼叫入口
 *
 */
@Component
@Slf4j
public class CallEntrance {

    @Autowired
    private ExecutionPool executionPool;
    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private IRobotCallTasksService robotCallTasksService;

    @Autowired
    private IRobotCallTasksCustomerService robotCallTasksCustomerService;

    @Autowired
    private WebHookUtil webHookUtil;
    /**
     * 执行呼叫
     * @param robotTenant 租户信息
     */
    @Async("aiCallExecutor")
    public void execute(RobotTenant robotTenant) {
        log.info("CallEntrance execute  执行呼叫开始 tenantId={} ", robotTenant.getTenantId());
        String lockKey =  String.format(CALL_TENANT_ENTRANCE_LOCK, robotTenant.getTenantId());
        RLock lock = redissonClient.getLock(lockKey);
        try{
            boolean lockFlag = lock.tryLock(3, 10*60, TimeUnit.SECONDS);
            if (!lockFlag) {
                log.info("CallEntrance execute  执行呼叫没有获取锁 tenantId={} ", robotTenant.getTenantId());
                return ;
            }
            //查询租户下所有启动状态的任务 条件：启动状态，在任务有效执行日期，在任务呼叫时间。
            List<RobotCallTasks> enableCallTasks =robotCallTasksService.getEnableCallTasks(robotTenant.getTenantId());
            if (enableCallTasks == null || enableCallTasks.isEmpty()) {
                log.info("CallEntrance execute  执行呼叫没有获取到任务 tenantId={} ", robotTenant.getTenantId());
                return ;
            }
            //租户配置的机器人并发量
            Integer robotConcurrency = robotTenant.getRobotConcurrency();
            //通话任务状态
            Integer runningStatus =RunningStatusEnum.RUNNING.getKey();
            //是否测试 0否1是
            Integer isTest = 0;
            for (RobotCallTasks callTask : enableCallTasks) {
                if(!checkRobotCallTasks(callTask)){
                    log.info("CallEntrance execute  执行呼叫任务状态不是可执行状态 tenantId={} taskId={} ", robotTenant.getTenantId(), callTask.getId());
                    continue;
                }
                if (robotConcurrency<=0 || RunningStatusEnum.QUEUEING.getKey().equals(runningStatus)) {
                    log.info("CallEntrance execute  执行呼叫任务并发已满 tenantId={} taskId={} ", robotTenant.getTenantId(), callTask.getId());
                    //更新任务状态为排队中
                    robotCallTasksService.updateRunningStatus(callTask.getId(), RunningStatusEnum.QUEUEING.getKey());
                    continue;
                }
                log.info("CallEntrance execute  执行呼叫开始执行任务 tenantId={} taskId={} ", robotTenant.getTenantId(), callTask.getId());
                //通话任务对应的号码
                List<RobotCallTasksCustomer> robotCallTasksCustomers = robotCallTasksCustomerService.getCallTasksCustomers(callTask.getId(),robotConcurrency,callTask.getMaxReplayTimes());
                for(int i=0;i<robotCallTasksCustomers.size();i++){
                    RunnableCall runnableCall = getRunnableCall(robotCallTasksCustomers.get(i),callTask,robotTenant,isTest);
                    R r = executionPool.execute(runnableCall);
                    if(!r.isSuccess()){
                        log.info("CallEntrance execute  执行呼叫失败，异常原因为 code={} msg={} req={}",r.getCode(), r.getMsg(),runnableCall);
                        if(r.getCode()== CallResultCode.CALL_ATTEMPT_FAILED.getCode() || r.getCode()== CallResultCode.CALL_GATEWAY_LIMIT_EXCEEDED.getCode() || r.getCode() == CallResultCode.PHONE_ENCRYPTION_LIMIT_EXCEEDED.getCode()){
                            if(i ==0){
                                //更新任务状态为排队中
                                robotCallTasksService.updateRunningStatus(callTask.getId(), RunningStatusEnum.QUEUEING.getKey());
                            }
                            if(r.getCode() == CallResultCode.CALL_ATTEMPT_FAILED.getCode()){
                                //后续任务直接更新为排队中
                                log.info("CallEntrance execute  执行呼叫失败后续任务直接更新为排队中 " );
                                runningStatus = RunningStatusEnum.QUEUEING.getKey();
                            }
                            break;
                        }else{
                            if(i ==0){
                                //拨打第一个号码后 更新执行中任务状态为执行中
                                robotCallTasksService.updateRunningStatus(callTask.getId(), RunningStatusEnum.RUNNING.getKey());
                            }
                        }
                    }else{
                        log.info("CallEntrance execute  执行呼叫成功，执行结果为 code={} msg={} req={}",r.getCode(), r.getMsg(),robotCallTasksCustomers.get(i));
                        if(i==0){
                            //拨打第一个号码后 更新执行中任务状态为执行中
                            robotCallTasksService.updateRunningStatus(callTask.getId(), RunningStatusEnum.RUNNING.getKey());
                        }
                    }

                }
                robotConcurrency -= robotCallTasksCustomers.size();
            }
        }catch(Exception ex){
            log.error("CallEntrance execute  执行呼叫发生异常，异常原因为", ex);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, "执行呼叫"+robotTenant.getTenantId(),"CallEntrance execute  执行呼叫发生异常，异常原因为", ex);
        }finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 检查任务
     * @param task
     * @return
     */
    public boolean checkRobotCallTasks(RobotCallTasks task){
        LocalDateTime now = LocalDateTime.now();
        LocalTime nowTime = now.toLocalTime();
        //超时任务改成已过期
        if(ValidityTypeEnum.DATE_TYPE.getKey().equals(task.getValidityType()) ){
            if(task.getEndDate() == null || now.toLocalDate().isAfter(task.getEndDate())){
                robotCallTasksService.completion(task.getId(), TaskCompletionReasonEnum.TASK_EXPIRED);
                return false;
            }
        }
        //不在执行时间内的任务 改为  执行中（休息）
        if((task.getCallTimeStart()!= null && nowTime.isBefore(task.getCallTimeStart()) ) || (task.getCallTimeEnd() != null && nowTime.isAfter(task.getCallTimeEnd()))){
            log.info("CallEntrance execute  执行呼叫任务不在执行时间段内  taskId={} ",   task.getId());
            robotCallTasksService.updateRunningStatus(task.getId(), RunningStatusEnum.RESTING.getKey());
            return false;
        }
        //不在执行星期的 改为执行中（休息)
        if( task.getCallTimeWeek() == null || !task.getCallTimeWeek().contains(now.getDayOfWeek().getValue()+"")){
            log.info("CallEntrance execute  执行呼叫任务不在执行星期内  taskId={} ",   task.getId());
            robotCallTasksService.updateRunningStatus(task.getId(), RunningStatusEnum.RESTING.getKey());
            return false;
        }


        return true;
    }


    public RunnableCall getRunnableCall(RobotCallTasksCustomer robotCallTasksCustomer,RobotCallTasks robotCallTasks,RobotTenant robotTenant,Integer isTest){
        RunnableCall runnableCall = new RunnableCall();
        runnableCall.setRobotCallTasksCustomer(robotCallTasksCustomer);
        runnableCall.setRobotCallTasks(robotCallTasks);
        runnableCall.setRobotTenant(robotTenant);
        runnableCall.setIsTest(isTest);
        return runnableCall;
    }
}
