package com.yirendai.robot.modules.call.biz.pool;

import com.yirendai.robot.enums.YesOrNoEnum;
import com.yirendai.robot.modules.call.biz.pool.constant.CallResultCode;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.service.IRobotCallTasksCustomerService;
import com.yirendai.robot.modules.call.service.IRobotCallTasksService;
import com.yirendai.robot.modules.call.service.RobotCallFrequencyMonitorService;
import com.yirendai.robot.modules.robot.entity.RobotHistory;
import com.yirendai.robot.modules.robot.mapper.RobotHistoryMapper;
import com.yirendai.robot.modules.session.entity.RobotSession;
import com.yirendai.robot.modules.session.req.RobotStartSessionReq;
import com.yirendai.robot.modules.session.service.IRobotSessionService;
import com.yirendai.robot.modules.tenant.entity.RobotTenantPhoneLine;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import com.yirendai.workbench.entity.CallcenterForbidden;
import com.yirendai.workbench.model.callcenter.AfterAiCallDto;
import com.yirendai.workbench.service.callcenter.CallcenterForbiddenService;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.wrapper.CustomerAfterCallWrapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import static com.yirendai.robot.modules.call.biz.pool.constant.CallRedisKeyConstant.CALL_TENANT_CALL_NUMBER_LOCK;

/**
 * 通话执行器
 */
@Component
@Slf4j
public class ExecutionPool {


    @Autowired
    private TenantCounter tenantCounter;

    @Autowired
    private TaskCounter taskCounter;

    @Autowired
    private LineCounter lineCounter;

    @Autowired
    @Lazy
    private IRobotSessionService robotSessionService;

    @Autowired
    @Lazy
    private IRobotCallTasksCustomerService robotCallTasksCustomerService;

    @Resource
    private RobotHistoryMapper robotHistoryMapper;

    @Autowired
    private IRobotCallTasksService robotCallTasksService;

    @Resource
    private TenantServiceHolder tenantServiceHolder;

    @Autowired
    private WebHookUtil webHookUtil;


    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private CallcenterForbiddenService callcenterForbiddenService;

    @Autowired
    private RobotCallFrequencyMonitorService robotCallFrequencyMonitorService;
    /**
     * 执行通话
     *
     * @param runnableCall
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public R execute(RunnableCall runnableCall) {
        String tenantId = runnableCall.getRobotTenant().getTenantId();
        String calledNumber = runnableCall.getRobotCallTasksCustomer().getCalledNumber();
        String lockKey =  String.format(CALL_TENANT_CALL_NUMBER_LOCK, tenantId,calledNumber);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean lockFlag = lock.tryLock(3, 30, TimeUnit.SECONDS);
            if (!lockFlag) {
                log.info("ExecutionPool 执行呼叫没有获取锁 {} {}",calledNumber,tenantId);
                return R.fail("执行呼叫没有获取锁");
            }
            log.info("ExecutionPool开始执行任务 runnableCall={} ", runnableCall);
            log.info("ExecutionPool开始执行任务 {} ", calledNumber);
            if(!checkCallNumber(calledNumber,tenantId)){
                log.info("ExecutionPool 执行呼叫号码被禁用 {} {}",calledNumber,tenantId);
                return R.fail("号码被禁用");
            }
            //判断是否拨打过
            RobotCallTasksCustomer robotCallTasksCustomer = robotCallTasksCustomerService.getById(runnableCall.getRobotCallTasksCustomer().getId());
            if(robotCallTasksCustomer==null){
                log.info("ExecutionPool 执行呼叫任务不存在 {} {}",calledNumber,tenantId);
                return R.fail("执行呼叫任务不存在");
            }
//            Long robotId = runnableCall.getRobotCallTasks().getRobotId();
//            if (robotCallFrequencyMonitorService.isBlocked(robotId, calledNumber)) {
//                log.info("ExecutionPool 执行呼叫被频次限制拦截 {} {}", calledNumber, tenantId);
//                markCustomerFrequencyBlocked(robotCallTasksCustomer);
//                return R.fail(CallResultCode.FREQUENCY_LIMIT_BLOCKED);
//            }
            //是否开启重播
            boolean isReplayEnabled = YesOrNoEnum.YES.getValue().equals(runnableCall.getRobotCallTasks().getIsReplayEnabled());
            //重播次数
            Integer replayTimes = runnableCall.getRobotCallTasks().getMaxReplayTimes();
            //重报条件对应状态多个用,分割
            String replayConditions = runnableCall.getRobotCallTasks().getReplayConditions();
            //重播间隔分钟
            Integer replayIntervalMinutes = runnableCall.getRobotCallTasks().getReplayIntervalMinutes();
            // 判断是否拨打过 robotCallTasksCustomer.getCallRounds()>0
            if (robotCallTasksCustomer.getCallRounds() != null && robotCallTasksCustomer.getCallRounds()>0) {
                    log.info("ExecutionPool 执行呼叫已拨打过 {} {}",calledNumber,tenantId);
                
                // 重播逻辑判断
                if (!isReplayEnabled) {
                    log.info("ExecutionPool 重播功能未开启 {} {}",calledNumber,tenantId);
                    return R.fail("执行呼叫已拨打过");
                }
                
                // 1. 判断是否大于最大重播次数
                if (replayTimes != null && robotCallTasksCustomer.getCallRounds() > replayTimes) {
                    log.info("ExecutionPool 已达到最大重播次数 {} {} callRounds={} maxReplayTimes={}",calledNumber,tenantId, robotCallTasksCustomer.getCallRounds(), replayTimes);
                    return R.fail("已达到最大重播次数");
                }
                RobotSession lastSession = robotSessionService.getById(robotCallTasksCustomer.getLastSessionId());
                // 2. 判断上次通话的挂断原因是否符合重播条件
                if (StringUtil.isNotBlank(replayConditions)) {

                    if (lastSession == null) {
                        log.info("ExecutionPool 上次通话记录不存在 {} {} lastSessionId={}",calledNumber,tenantId, robotCallTasksCustomer.getLastSessionId());
                        return R.fail("上次通话记录不存在");
                    }
                    
                    Integer lastCallResult = lastSession.getCallResult();
                    if (lastCallResult == null) {
                        log.info("ExecutionPool 上次通话结果为空 {} {} lastSessionId={}",calledNumber,tenantId, robotCallTasksCustomer.getLastSessionId());
                        return R.fail("上次通话结果为空");
                    }
                    
                    // 检查上次通话结果是否在重播条件中
                    String[] conditionCodes = replayConditions.split(",");
                    boolean canReplay = false;
                    for (String conditionCode : conditionCodes) {
                        try {
                            Integer code = Integer.parseInt(conditionCode.trim());
                            if (code.equals(lastCallResult)) {
                                canReplay = true;
                                break;
                            }
                        } catch (NumberFormatException e) {
                            log.warn("Invalid replay condition code: {}", conditionCode);
                        }
                    }
                    
                    if (!canReplay) {
                        log.info("ExecutionPool 上次通话结果不符合重播条件 {} {} lastCallResult={} replayConditions={}",calledNumber,tenantId, lastCallResult, replayConditions);
                        RobotCallTasksCustomer robotcallCustomerUpdate = new RobotCallTasksCustomer();
                        robotcallCustomerUpdate.setId(robotCallTasksCustomer.getId());
                        robotcallCustomerUpdate.setDupCallStatus(YesOrNoEnum.YES.getValue());
                        robotCallTasksCustomerService.updateById(robotcallCustomerUpdate);
                        return R.fail("上次通话结果不符合重播条件");
                    }
                }else{
                    log.info("ExecutionPool 重播条件未设置 {} {}",calledNumber,tenantId);
                    return R.fail("重播条件未设置");
                }
                
                // 3. 判断重播间隔是否满足要求
                if (replayIntervalMinutes != null && replayIntervalMinutes > 0) {
                    if (lastSession != null && lastSession.getCallEndTime() != null) {
                        LocalDateTime now = LocalDateTime.now();
                        LocalDateTime lastCallEndTime = lastSession.getCallEndTime();
                        long minutesSinceLastCall = java.time.Duration.between(lastCallEndTime, now).toMinutes();
                        
                        if (minutesSinceLastCall < replayIntervalMinutes) {
                            log.info("ExecutionPool 重播间隔未满足要求 {} {} minutesSinceLastCall={} replayIntervalMinutes={}",calledNumber,tenantId, minutesSinceLastCall, replayIntervalMinutes);
                            return R.fail("重播间隔未满足要求");
                        }
                    }
                }else{
                    log.info("ExecutionPool 重播间隔未设置 {} {}",calledNumber,tenantId);
                    return R.fail("重播间隔未设置");
                }
                
                log.info("ExecutionPool 满足重播条件，继续执行呼叫 {} {}",calledNumber,tenantId);
            }

            Integer robotConcurrency = runnableCall.getRobotTenant().getRobotConcurrency();
            Integer maxConcurrency = runnableCall.getRobotCallTasks().getMaxConcurrency();
            Long taskId = runnableCall.getRobotCallTasks().getId();
            R tenantCounterRes = this.tenantCounter.increment(tenantId, robotConcurrency);
            if (tenantCounterRes.isSuccess()) {
                R taskCounterRes = this.taskCounter.increment(tenantId, taskId, maxConcurrency);
                if (taskCounterRes.isSuccess()) {
                    R<RobotTenantPhoneLine> lineCounterRes = this.lineCounter.increment(tenantId, taskId);
                    if (lineCounterRes.isSuccess()) {
                        Integer gatewayId = lineCounterRes.getData().getGatewayId();
                        try {
                            RobotHistory robotHistory = robotHistoryMapper.getNewRobot(runnableCall.getRobotCallTasks().getRobotId());
                            String callerDisplayNumber = lineCounterRes.getData().getGatewayName();

                            int callRounds = 1; //重播次数
                            if (runnableCall.getRobotCallTasksCustomer().getCallRounds() != null) {
                                callRounds = runnableCall.getRobotCallTasksCustomer().getCallRounds() + 1;
                            }
                            //开始执行任务。
                            log.info("ExecutionPool开始执行任务 tenantId={} taskId={} callerDisplayNumber={} historyId={}", tenantId, taskId, callerDisplayNumber, robotHistory.getId());
                            RobotStartSessionReq req = new RobotStartSessionReq();
                            req.setTaskId(taskId);
                            req.setCalledNumber(runnableCall.getRobotCallTasksCustomer().getCalledNumber());
                            req.setCallerDisplayNumber(callerDisplayNumber);
                            req.setVariables(runnableCall.getRobotCallTasksCustomer().getVariables());
                            req.setCallRounds(callRounds);
                            req.setRobotHistoryId(robotHistory.getId());
                            req.setGatewayId(gatewayId);
                            req.setIsTest(runnableCall.getIsTest());
                            req.setCallTasksCustomerId(runnableCall.getRobotCallTasksCustomer().getId());
                            RobotSession robotSession = robotSessionService.startSession(req);
                            RobotCallTasksCustomer customerUpdate = new RobotCallTasksCustomer();
                            customerUpdate.setId(runnableCall.getRobotCallTasksCustomer().getId());
                            customerUpdate.setCallRounds(callRounds);
                            customerUpdate.setLastSessionId(robotSession.getId());
                            robotCallTasksCustomerService.updateById(customerUpdate);
                            return R.success("执行成功");
                        } catch (Exception e) {
                            log.error("ExecutionPool执行通话失败 tenantId={} taskId={} {}", tenantId, taskId, e.getMessage(), e);
                            lineCounter.decrement(tenantId, gatewayId);
                            taskCounter.decrement(tenantId, taskId);
                            tenantCounter.decrement(tenantId);
                            webHookUtil.weChatMsgDev(MsgTypeEnum.CALL_RESERVATION, "ExecutionPool外呼机器人执行过程中异常",e.getMessage(), e);
                            throw e;
                        }
                    } else {
                        log.info("ExecutionPool LineCounter increment 线路计数器已经达到最大值 tenantId={} taskId={} ", tenantId, taskId);
                        tenantCounter.decrement(tenantId);
                        taskCounter.decrement(tenantId, taskId);
                        return R.fail(CallResultCode.CALL_GATEWAY_LIMIT_EXCEEDED);
                    }
                } else {
                    log.info("ExecutionPool TaskCounter increment 任务计数器已经达到最大值 tenantId={} taskId={} maxConcurrency={}", tenantId, taskId, maxConcurrency);
                    tenantCounter.decrement(tenantId);
                    return R.fail(CallResultCode.PHONE_ENCRYPTION_LIMIT_EXCEEDED);
                }
            } else {
                log.info("ExecutionPool TenantCounter increment 租户计数器已经达到最大值 tenantId={} taskId={} robotConcurrency={} maxConcurrency={}", tenantId, taskId, robotConcurrency, maxConcurrency);
                return R.fail(CallResultCode.CALL_ATTEMPT_FAILED);
            }
        }catch(Exception ex){
            log.error("ExecutionPool 执行呼叫发生异常，异常原因为", ex);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_CALL_ERROR, "执行呼叫"+calledNumber,"ExecutionPool 执行呼叫发生异常，异常原因为", ex);
            return R.fail("执行呼叫发生异常");
        }finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 执行结束
     *
     * @param robotSession
     */
    public void shutdown(RobotSession robotSession) {
        log.info("ExecutionPool 执行结束 uuid={} tenantId={} taskId={} sessionId={}", robotSession.getUuid(), robotSession.getTenantId(), robotSession.getTaskId(), robotSession.getId());
        String tenantId = robotSession.getTenantId();
        Long taskId = robotSession.getTaskId();
        Integer gatewayId = robotSession.getGatewayId();
        lineCounter.decrement(tenantId, gatewayId);
        taskCounter.decrement(tenantId, taskId);
        tenantCounter.decrement(tenantId);

        RobotCallTasksCustomer customer = robotCallTasksCustomerService.selectOneBySessionId(robotSession.getId());
        if(customer == null){
            log.info("ExecutionPool 执行结束 sessionId={} 没有找到对应的客户信息", robotSession.getId());
            return;
        }
        if ( YesOrNoEnum.NO.getValue().equals(customer.getIsTest())) {
            try{
                CustomerAfterCallWrapper customerAfterCallWrapper = tenantServiceHolder.getCustomerAfterCallWrapper(tenantId);
                AfterAiCallDto afterAiCallDto = new AfterAiCallDto();
                afterAiCallDto.setAiCallTime(robotSession.getCallStartTime());
                afterAiCallDto.setRobotId(robotSession.getRobotId());
                afterAiCallDto.setCallResult(robotSession.getCallResult());
                afterAiCallDto.setIntentionDes(robotSession.getIntentionDes());
                afterAiCallDto.setIntentType(robotSession.getRecentIntention());
                afterAiCallDto.setCustomerId(customer.getUserId());
                afterAiCallDto.setCallStatus(robotSession.getCallStatus());
                afterAiCallDto.setTenantId(tenantId);
                customerAfterCallWrapper.afterAiCall(afterAiCallDto);
                log.info("ExecutionPool afterAiCall 执行结束 sessionId={} uuid={}  成功", robotSession.getId(),   robotSession.getUuid());
            }catch (Exception ex){
                log.error("ExecutionPool afterAiCall 执行异常 sessionId={} uuid={}  error={}", robotSession.getId(),   robotSession.getUuid(), ex.getMessage(), ex);
            }
        }else{
            log.info("测试通话回调不更新任务状态 taskId={}", taskId);
        }

    }

    /**
     * 检验手机号是否可被呼叫
     * @param phone
     * @return
     */
    public boolean checkCallNumber(String phone,String tenantId){
        try{
            CallcenterForbidden callcenterForbidden = callcenterForbiddenService.getByPhoneOfRobot(phone,tenantId);
            if(callcenterForbidden != null){
                return false;
            }else {
                return true;
            }
        }catch (Exception e){
            log.error("CallEntrance checkCallNumber {}  检验手机号是否可被呼叫发生异常，异常原因为 {}",phone ,e.getMessage());
            return false;
        }

    }
}
