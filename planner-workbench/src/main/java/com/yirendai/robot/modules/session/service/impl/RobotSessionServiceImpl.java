package com.yirendai.robot.modules.session.service.impl;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.base.Strings;
import com.yirendai.app.fortune.common.util.JsonUtils;
import com.yirendai.robot.constant.RobotConstant;
import com.yirendai.robot.enums.*;
import com.yirendai.robot.exception.RobotException;
import com.yirendai.robot.modules.api.dto.ChannelApiCfgDTO;
import com.yirendai.robot.modules.api.entity.ChannelApiCfg;
import com.yirendai.robot.modules.api.service.ChannelApiService;
import com.yirendai.robot.modules.attribute.entity.RobotAttributeBranch;
import com.yirendai.robot.modules.attribute.entity.RobotAttributeIntent;
import com.yirendai.robot.modules.call.biz.pool.constant.CallResultCode;
import com.yirendai.robot.modules.robot.entity.RobotAttributeVariable;
import com.yirendai.robot.modules.attribute.service.IRobotAttributeBranchService;
import com.yirendai.robot.modules.attribute.service.IRobotAttributeIntentService;
import com.yirendai.robot.modules.attribute.service.IRobotAttributeVariableService;
import com.yirendai.robot.modules.call.biz.pool.ExecutionPool;
import com.yirendai.robot.modules.call.dto.RobotCallBackChannelDTO;
import com.yirendai.robot.modules.call.entity.RobotCallTasks;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.service.IRobotCallTasksCustomerService;
import com.yirendai.robot.modules.call.service.IRobotCallTasksService;
import com.yirendai.robot.modules.call.service.RobotCallFrequencyMonitorService;
import com.yirendai.robot.modules.knowledge.dto.AnswerDTO;
import com.yirendai.robot.modules.knowledge.entity.RobotKnowledgeRela;
import com.yirendai.robot.modules.knowledge.service.IRobotKnowledgeRelaService;
import com.yirendai.robot.modules.knowledge.service.IRobotKnowledgeService;
import com.yirendai.robot.tripartite.res.knowledge.KnowledgeResponse;
import com.yirendai.robot.tripartite.res.knowledge.KnowledgeRecords;
import com.yirendai.robot.modules.robot.dto.*;
import com.yirendai.robot.modules.robot.entity.AiRobotDialogEdge;
import com.yirendai.robot.modules.robot.entity.AiRobotDialogNode;
import com.yirendai.robot.modules.robot.entity.Robot;
import com.yirendai.robot.modules.robot.entity.RobotHistory;
import com.yirendai.robot.modules.robot.entity.RobotPrompt;
import com.yirendai.robot.modules.robot.enums.RobotStatusEnum;
import com.yirendai.robot.modules.robot.mapper.RobotHistoryMapper;
import com.yirendai.robot.modules.robot.service.*;
import com.yirendai.robot.modules.robot.vo.RobotConfigVO;
import com.yirendai.robot.modules.rule.entity.RobotRule;
import com.yirendai.robot.modules.rule.entity.RobotRuleNode;
import com.yirendai.robot.modules.rule.entity.RobotRuleOne;
import com.yirendai.robot.modules.rule.service.IRobotRuleNodeService;
import com.yirendai.robot.modules.rule.service.IRobotRuleOneService;
import com.yirendai.robot.modules.rule.service.IRobotRuleService;
import com.yirendai.robot.modules.session.dto.RobotSessionChatDTO;
import com.yirendai.robot.modules.session.entity.RobotSession;
import com.yirendai.robot.modules.session.entity.RobotSessionComposedTalk;
import com.yirendai.robot.modules.session.entity.RobotSessionSummary;
import com.yirendai.robot.modules.session.entity.RobotSessionEdgeDst;
import com.yirendai.robot.modules.session.entity.RobotSessionFlow;
import com.yirendai.robot.modules.session.entity.RobotSessionInfo;
import com.yirendai.robot.modules.session.entity.RobotSessionInfoModel;
import com.yirendai.robot.modules.session.entity.RobotSessionNodeState;
import com.yirendai.robot.modules.session.mapper.RobotSessionFlowMapper;
import com.yirendai.robot.modules.session.mapper.RobotSessionMapper;
import com.yirendai.robot.modules.session.req.RobotCallRequest;
import com.yirendai.robot.modules.session.req.RobotStartSessionReq;
import com.yirendai.robot.modules.session.service.*;
import com.yirendai.robot.modules.session.service.chat.InsuranceDialogEngine;
import com.yirendai.robot.modules.session.service.chat.RobotAiService;
import com.yirendai.robot.modules.session.service.chat.entity.DialogResponse;
import com.yirendai.robot.modules.session.service.chat.entity.DialogSession;
import com.yirendai.robot.modules.session.service.chat.entity.DialogTurn;
import com.yirendai.robot.modules.session.service.chat.entity.Node;
import com.yirendai.robot.modules.session.vo.RobotSessionTestChatVO;
import com.yirendai.robot.modules.session.vo.RobotSessionVoiceVO;
import com.yirendai.robot.modules.session.vo.RobotSessionQueryResultVO;
import com.yirendai.robot.modules.session.vo.RobotSessionRecordVO;
import com.yirendai.robot.modules.session.req.RobotSessionQueryReq;
import com.yirendai.robot.modules.tenant.entity.RobotTenantSms;
import com.yirendai.robot.modules.tenant.entity.TenantAttributeVariable;
import com.yirendai.robot.modules.tenant.service.IRobotTenantSmsService;
import com.yirendai.robot.modules.tenant.service.ITenantAttributeVariableService;
import com.yirendai.robot.modules.tts.entity.RobotTtsRecord;
import com.yirendai.robot.modules.tts.enums.TtsStatusEnum;
import com.yirendai.robot.modules.tts.service.IRobotTtsRecordService;
import com.yirendai.robot.modules.tts.vo.TtsAuditionReq;
import com.yirendai.robot.tripartite.res.ChannelHttpResp;
import com.yirendai.robot.util.RobotFormatUtil;
import com.yirendai.robot.util.bizcode.BizCodeEnum;
import com.yirendai.robot.util.bizcode.BizCodeUtil;
import com.yirendai.voiceaiserver.enums.AiConversationProcessTypeEnum;
import com.yirendai.voiceaiserver.service.AIConversationProcessService;
import com.yirendai.voiceaiserver.util.TextSplitter;
import com.yirendai.voiceaiserver.util.msg.MsgTypeEnum;
import com.yirendai.voiceaiserver.util.msg.WebHookUtil;
import com.yirendai.voiceaiserver.vo.request.AiConversationProcessReq;
import com.yirendai.workbench.enums.CallBackTypeEnum;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.callcenter.ChannelRobotCallbackService;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.vo.req.CallRecordInfo;
import com.yirendai.workbench.vo.res.callcenter.CallServiceResponse;
import com.yirendai.workbench.wrapper.CustomerInfoWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import redis.clients.jedis.JedisCluster;

/**
 * <p>
 * AI呼叫机器人会话表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Slf4j
@RefreshScope
@Service
public class RobotSessionServiceImpl extends ServiceImpl<RobotSessionMapper, RobotSession> implements IRobotSessionService {

    @Resource
    private IRobotCallTasksService robotCallTasksService;

    @Autowired
    private RobotCallFrequencyMonitorService robotCallFrequencyMonitorService;

    @Resource
    private IRobotSessionComposedTalkService robotSessionComposedTalkService;

    @Resource
    private IRobotTtsRecordService robotTtsRecordService;

    @Resource
    private IRobotService robotService;

    @Resource
    private ExecutorService aiRobotTtsThreadPoolExecutor;

    @Resource
    private IRobotSessionNodeStateService robotSessionNodeStateService;

    @Resource
    private IRobotAttributeIntentService robotAttributeIntentService;

    @Resource
    private IRobotRuleNodeService robotRuleNodeService;

    @Resource
    private IRobotRuleService robotRuleService;

    @Resource
    private IRobotPromptService robotPromptService;

    @Resource
    private RobotAiService robotAiService;

    @Resource
    private InfoCollectionService infoCollectionService;

    @Resource
    private IRobotSessionFlowService robotSessionFlowService;

    @Resource
    private RobotHistoryMapper robotHistoryMapper;

    @Resource
    private AiRobotDialogNodeService aiRobotDialogNodeService;

    @Resource
    private AiRobotDialogEdgeService aiRobotDialogEdgeService;

    @Resource
    private IRobotKnowledgeService robotKnowledgeService;

    @Resource
    private AIConversationProcessService aiConversationProcessService;

    @Resource
    private IRobotSessionInfoService robotSessionInfoService;

    @Resource
    private TenantServiceHolder tenantServiceHolder;

    @Resource
    private InsuranceDialogEngine insuranceDialogEngine;

    @Resource
    private IRobotAttributeVariableService robotAttributeVariableService;

    @Resource
    private ITenantAttributeVariableService tenantAttributeVariableService;

    @Resource
    private IRobotConfigService robotConfigService;

    @Resource
    private IRobotSessionSummaryService robotSessionSummaryService;

    @Value("${robot.yxh.url}")
    private String yxhCallServiceEndPoint;

    @Value("${robot.session.maxHistory}")
    private Integer maxHistorySize;

    @Value("${ai.text.split.threshold}")
    private int textSplitThreshold;

    @Value("${ai.text.split.factor}")
    private double textSplitFactor;

    // 是否支持系统提示词
    @Value("${robot.session.system.enabled}")
    private boolean isSystem = false;

    // 是否支持打断
    @Value("${robot.session.interrupt.enabled}")
    private boolean robotSessionInterruptEnabled = true;

    // 是否开启引导语
    @Value("${robot.session.default.leader.enabled}")
    private boolean robotDefaultLeaderEnabled = false;

    /**
     * 默认对话结束音频
     */
    @Value("${robot.session.default.end.audio}")
    private String robotDefaultEndAudio = "/robot/278576/AIVOICE20250423202725000144.wav";

    /**
     * 默认对话结束语
     */
    @Value("${robot.session.default.end.text}")
    private String robotDefaultEndSpeechText = "抱歉，打扰了！拜拜";

    /**
     * 要求重复
     */
    @Value("${robot.session.default.intent.repeat}")
    private String robotRepeatIntent = "要求重复";

    /**
     * 要求重复描述
     */
    @Value("${robot.session.default.intent.repeat_desc}")
    private String robotRepeatIntentDesc = "仅在用户明确表示听不清或没明白或需要重复一次的时候匹配";

    /**
     * 投诉辱骂
     */
    @Value("${robot.session.default.intent.rude}")
    private String robotRudeIntent = "投诉辱骂";

    /**
     * 投诉辱骂描述
     */
    @Value("${robot.session.default.intent.rude_desc}")
    private String robotRudeIntentDesc = "仅在用户明确表示要投诉对方或者明确辱骂对方的时候匹配";

    /**
     * 其它分支
     */
    @Value("${robot.session.default.intent.other}")
    private String robotOtherBranch = "其它";

    /**
     * 其它分支描述
     */
    @Value("${robot.session.default.intent.other_desc}")
    private String robotOtherBranchDesc = "其它意图未匹配或者匹配度极低时选择此意图";

    @Value("${robot.session.default.voice.tts.all}")
    private boolean robotTtsAll = true;

    /**
     * 信息采集AI模型名称
     */
    @Value("${robot.session.info.collection.model:qwen3-max}")
    private String infoCollectionModel = "qwen3-max";

    /**
     * 知识库答案验证开关
     */
    @Value("${robot.session.knowledge.verify.enabled:true}")
    private boolean knowledgeVerifyEnabled = true;

    /**
     * 知识库验证模型（使用小模型提速）
     */
    @Value("${robot.session.knowledge.verify.model:qwen3-mini}")
    private String knowledgeVerifyModel = "qwen3-mini";

    /**
     * 相关性阈值（1-5分，4分以上才返回）
     */
    @Value("${robot.session.knowledge.verify.threshold:4}")
    private Integer knowledgeRelevanceThreshold = 4;


    @Resource
    private RestTemplate restTemplate;

    @Resource
    private JedisCluster jedisCluster;

    @Autowired
    private ExecutionPool executionPool;

    @Resource
    private ChannelApiService channelApiService;

    @Autowired
    private IRobotKnowledgeRelaService robotKnowledgeRelaService;

    @Autowired
    private WebHookUtil webHookUtil;

    @Resource
    private IRobotTenantSmsService robotTenantSmsService;

    @Autowired
    private BizCodeUtil bizCodeUtil;

    @Resource
    private IRobotAttributeBranchService robotAttributeBranchService;

    @Resource
    private IRobotRuleOneService robotRuleOneService;

    @Autowired
    @Lazy
    private IRobotCallTasksCustomerService robotCallTasksCustomerService;

    @Resource
    private ChannelRobotCallbackService channelRobotCallbackService;

    @Resource
    private RobotSessionFlowMapper robotSessionFlowMapper;

    @Override
    public RobotSession startSession(RobotStartSessionReq req) {
        // 0. 准备数据
        Long taskId = req.getTaskId();
        String calledNumber = req.getCalledNumber();
        String callerDisplayNumber = req.getCallerDisplayNumber();
        String variables = req.getVariables();
        Integer callRounds = req.getCallRounds();
        Long robotHistoryId = req.getRobotHistoryId();
        Integer gatewayId = req.getGatewayId();
        RobotCallTasks tasks = robotCallTasksService.getById(taskId);
        if (tasks == null) {
            throw new RobotException(RobotResult.TASK_NOT_EXIST);
        }
        if (YesOrNoEnum.NO.getValue().equals(req.getIsTest())) {
            // 不是测试执行才执行的逻辑
            if (!Objects.equals(tasks.getTaskStatus(), RobotTaskStatus.EXECUTING.getCode())) {
                throw new RobotException(RobotResult.TASK_NOT_STARTED);
            }
        }

        RobotHistory robotHistory = robotHistoryMapper.selectById(robotHistoryId);
        if (robotHistory == null) {
            throw new RobotException(RobotResult.ROBOT_HISTORY_NOT_EXIST);
        }
        Long robotId = robotHistory.getRobotId();
        if (!Objects.equals(robotHistory.getStatus(), RobotStatusEnum.PUBLISH.getValue())) {
            throw new RobotException(RobotResult.ROBOT_NOT_PUBLISHED);
        }
        Long toneId = robotHistory.getToneId();
        String tenantId = tasks.getTenantId();
        //String uuid = RobotFormatUtil.generateCallUuid();
        String uuid = bizCodeUtil.createBizCode(BizCodeEnum.ROBOT_UUID);

        // 1. 创建会话
        RobotSession robotSession = new RobotSession();
        robotSession.setUuid(uuid);
        robotSession.setTaskId(taskId);
        robotSession.setRobotId(robotId);
        robotSession.setCalledNumber(calledNumber);
        robotSession.setCallerDisplayNumber(callerDisplayNumber);
        robotSession.setVariables(variables);
        robotSession.setToneId(toneId);
        robotSession.setRobotHistoryId(robotHistoryId);
        robotSession.setCallStatus(RobotCallStatus.UNCALLED.getCode());
        robotSession.setCallRounds(callRounds);
        robotSession.setGatewayId(gatewayId);
        robotSession.setCreateTime(LocalDateTime.now());
        robotSession.setUpdateTime(LocalDateTime.now());
        robotSession.setInterruptEnable(robotHistory.getInterruptEnable());
        robotSession.setTenantId(tenantId);
        robotSession.setCallTasksCustomerId(req.getCallTasksCustomerId());
        if (!save(robotSession)) {
            throw new RobotException(RobotResult.SESSION_CREATE_FAILED);
        }

        // 2. 变量填充和TTS拼接
        Map<String, Object> variablesMap = RobotFormatUtil.parseStringToMap(variables);
        //  获取机器人对应的全部对话节点
        List<AiRobotDialogNode> robotDialogNodes = aiRobotDialogNodeService.getByHistoryRobotId(robotHistoryId);
        if (robotDialogNodes == null || robotDialogNodes.isEmpty()) {
            throw new RobotException(RobotResult.ROBOT_NODE_NOT_EXIST);
        }
        log.info("robot session buildTts  robotId={} ,toneId={},taskId={},uuid={},tenantId={},historyId={},map={}", robotId, toneId, taskId, uuid, robotSession.getTenantId(), robotHistoryId, variablesMap);
        List<RobotSessionComposedTalk> composedTalks = buildStaticVariable(variablesMap, robotDialogNodes, robotHistory, uuid);
        log.info("robot session TTS processing started  composedTalksSize={}", composedTalks.size());
        // 保存所有 ComposedTalk
        if (!composedTalks.isEmpty()) {
            if (!robotSessionComposedTalkService.saveOrUpdateBatch(composedTalks)) {
                log.error("robot session Error occurred during ComposedTalk saving");
                throw new RobotException(RobotResult.SESSION_CREATE_FAILED);
            }
        }
        log.info("robot session TTS processing completed successfully");

        // 频次限制
        if (robotCallFrequencyMonitorService.isBlocked(robotId, calledNumber)) {
            log.info("ExecutionPool 执行呼叫被频次限制拦截 {} {}", calledNumber, tenantId);
            robotSession.setCallStatus(RobotCallStatus.OVER_LIMIT.getCode());
            robotSession.setCallResult(RobotCallResult.OVER_LIMIT.getCode());
            updateById(robotSession);
            log.info("robot session Frequency limit blocked: {}", calledNumber);
            executionPool.shutdown(robotSession);
            return robotSession;
        }

        // 3. initCall
        RobotCallRequest makeCallRequest = new RobotCallRequest(gatewayId, calledNumber, uuid, robotHistory.getInterruptEnable());
        CallServiceResponse<String> stringCallServiceResponse = makeCall(makeCallRequest);
        // 示例： (status=0000, msg=操作成功, data=null)
        if (!(Objects.equals(stringCallServiceResponse.getStatus(), "0000"))) {
            robotSession.setCallStatus(RobotCallStatus.UNCALLED_FAIL.getCode());
            robotSession.setCallResult(RobotCallResult.INTERCEPT_LINE.getCode());
            robotSession.setHangupDisposition(stringCallServiceResponse.getMsg());
            updateById(robotSession);
            log.error("robot session Error occurred during initCall, error data:{}", stringCallServiceResponse);
            throw new RobotException(RobotResult.SESSION_CALL_FAILED);
        }

        // 4. 更新session状态
        robotSession.setCallStartTime(LocalDateTime.now());
        robotSession.setCallStatus(RobotCallStatus.CALLING.getCode());
        if (!updateById(robotSession)) {
            throw new RobotException(RobotResult.SESSION_UPDATE_FAILED);
        }
        return robotSession;
    }

    @Override
    public Boolean publishPreData(Long robotHistoryId) {
        RobotHistory robotHistory = robotHistoryMapper.selectById(robotHistoryId);
        Long curNodeId = 0L;
        int type = 0;
        try {
            List<AiRobotDialogNode> robotDialogNodeList = aiRobotDialogNodeService.getByHistoryRobotId(robotHistoryId);
            for (AiRobotDialogNode robotDialogNode : robotDialogNodeList) {
                curNodeId = robotDialogNode.getId();
                String subDialogs = robotDialogNode.getSubDialogs();
                if (StringUtils.isBlank(subDialogs)) {
                    continue;
                }
                RobotDialogDTO robotDialogDTO = JSON.parseObject(subDialogs, RobotDialogDTO.class);
                List<RobotDialog> dialogList = robotDialogDTO.getDialogList();
                for (RobotDialog robotDialog : dialogList) {
                    buildNodeTextTts(robotHistory, robotDialog);
                }
                robotDialogNode.setSubDialogs(JSON.toJSONString(robotDialogDTO));
                aiRobotDialogNodeService.updateById(robotDialogNode);

                //节点规则语音生成
                List<RobotRuleNode> ruleNodes = robotRuleNodeService.getByNodeId(robotDialogNode.getId(), robotHistoryId);
                type = 1;
                for (RobotRuleNode ruleNode : ruleNodes) {
                    curNodeId = ruleNode.getNodeId();
                    String endSpeechText = ruleNode.getEndSpeechText();
                    if (StringUtils.isBlank(endSpeechText)) {
                        continue;
                    }
                    TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, endSpeechText);
                    RobotTtsRecord robotTtsRecord = robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
                    if (robotTtsRecord != null && robotTtsRecord.getStatus() == TtsStatusEnum.COMPLETED.getCode()) {
                        ruleNode.setEndSpeechAudio(robotTtsRecord.getAudioPath());
                    }
                }
                robotRuleNodeService.updateBatchById(ruleNodes);
                // 机器人规则语音生成
                RobotRuleOne robotRuleOne = robotRuleOneService.getAvailable(robotHistoryId, RobotRuleOneConfigType.COMPLAINT.getCode());
                if (robotRuleOne != null && !Strings.isNullOrEmpty(robotRuleOne.getEndSpeechText())) {
                    TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, robotRuleOne.getEndSpeechText());
                    RobotTtsRecord robotTtsRecord = robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
                    if (robotTtsRecord != null && robotTtsRecord.getStatus() == TtsStatusEnum.COMPLETED.getCode()) {
                        robotRuleOne.setEndSpeechAudio(robotTtsRecord.getAudioPath());
                        robotRuleOneService.updateById(robotRuleOne);
                    }
                }
            }
        } catch (Exception e) {
            log.error("robot session nodeBuildTts buildNodeTts error, curNodeId,{}, type,{}", curNodeId, type, e);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_AUDIO_ERROR, "节点音频批量生成异常 curNodeId:" + curNodeId + " type:" + type + " error:" + e);
            return false;
        }
        try {
            buildKnowledgeTts(robotHistory);
        } catch (Exception e) {
            log.error("robot session nodeBuildTts buildKnowledgeTts error", e);
            return false;
        }
        buildDefaultPrompt(robotHistory);
        return true;
    }

    @Override
    public void buildDefaultPrompt(RobotHistory robotHistory) {
        robotPromptService.buildDefaultRobotPrompt(robotHistory);
    }

    private void buildNodeTextTts(RobotHistory robotHistory, RobotDialog robotDialog) {
        String text = robotDialog.getHandledText();
        if (Strings.isNullOrEmpty(text)) {
            return;
        }
        if (text.contains("$") || text.contains("#")) {
            List<String> stringList = RobotFormatUtil.splitDialogTextSentence(text);
            List<String> newStringList = new ArrayList<>();
            for (String s : stringList) {
                log.debug("robot session buildTts section text={}", s);
                if (Strings.isNullOrEmpty(s)) {
                    log.error("robot session Error occurred during buildTts, text is null");
                    continue;
                }
                if (s.contains("$") || s.contains("#")) {
                    newStringList.add(s);
                    continue;
                }
                TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, s);
                RobotTtsRecord robotTtsRecord = robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
                if (robotTtsRecord != null && robotTtsRecord.getStatus() == TtsStatusEnum.COMPLETED.getCode()) {
                    newStringList.add(robotTtsRecord.getAudioPath());
                } else {
                    log.error("robot session Error occurred during buildTts section text={}", s);
                }
            }
            robotDialog.setAudio(String.join(",", newStringList));
        } else {
            log.debug("robot session buildTts full text={}", text);
            TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, text);
            RobotTtsRecord robotTtsRecord = robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
            if (robotTtsRecord != null && robotTtsRecord.getStatus() == TtsStatusEnum.COMPLETED.getCode()) {
                robotDialog.setAudio(robotTtsRecord.getAudioPath());
            } else {
                log.error("robot session Error occurred during buildTts full text={}", text);
            }
        }
    }

    /**
     * 知识库语音生成
     */
    @Override
    public void buildKnowledgeTts(RobotHistory robotHistory) {
        long startTime = System.currentTimeMillis();
        log.info("robot session buildKnowledgeTts start startTime={} historyId={}", startTime, robotHistory.getId());
        RobotKnowledgeRela dto = new RobotKnowledgeRela();
        dto.setRobotHistoryId(robotHistory.getId());
        List<RobotKnowledgeRela> list = robotKnowledgeRelaService.selectRobotKnowledgeList(dto);
        if (CollectionUtil.isEmpty(list)) {
            log.info("robot session buildKnowledgeTts error, list is empty");
            return;
        }
        for (RobotKnowledgeRela robotKnowledgeRela : list) {
            R<List<String>> answersRes = robotKnowledgeService.querySegments(robotKnowledgeRela.getRobotKnowledgeId());
            if (answersRes.isSuccess() && CollectionUtil.isNotEmpty(answersRes.getData())) {
                for (String answer : answersRes.getData()) {
                    if (Strings.isNullOrEmpty(answer)) {
                        continue;
                    }
                    TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, answer);
                    robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
                }
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("robot session buildKnowledgeTts costTime={} historyId={}", endTime - startTime, robotHistory.getId());
    }

    @Override
    public TtsAuditionReq buildTtsAuditionReq(RobotHistory robotHistory, String text) {
        TtsAuditionReq ttsAuditionReq = new TtsAuditionReq();
        ttsAuditionReq.setToneId(robotHistory.getToneId());
        ttsAuditionReq.setRobotId(robotHistory.getRobotId());
        ttsAuditionReq.setTaskId(0L);
        ttsAuditionReq.setTtsText(text);
        ttsAuditionReq.setTenantId(robotHistory.getTenantId());
        return ttsAuditionReq;
    }

    @Override
    @Async("commonThreadPoolExecutor")
    public void finishRobotSession(CallRecordInfo callRecordInfo) {
        if (callRecordInfo == null) {
            log.error("robot session finishRobotSession error, callRecordInfo is null");
            return;
        }
        log.info("robot session finishRobotSession start, uuid: {}", callRecordInfo.getUuid());
        String uuid = callRecordInfo.getUuid();
        String recordUrl = callRecordInfo.getRecordUrl();
        jedisCluster.del(RobotConstant.ROBOT_SESSION_KEY + uuid);
        // 获取录音文件
        RobotSession robotSession = getByUuid(uuid);
        if (robotSession == null) {
            log.error("robot session finishRobotSession error, robotSession is null, uuid: {}", uuid);
            return;
        }

        RobotSessionNodeState nodeState = robotSessionNodeStateService.getNodeState(uuid);
        if (nodeState == null) {
            log.error("robot session finishRobotSession error, nodeState is null, uuid: {}", uuid);
        }
        String historyText = nodeState == null ? "" : nodeState.getHistoryText();
        if (!StringUtils.isEmpty(recordUrl)) {
            robotSession.setCallVideo(recordUrl);
        }
        String hangupCause = callRecordInfo.getHangupCause();
        String hangupPhrase = callRecordInfo.getHangupPhrase();
        String tenantId = robotSession.getTenantId();
        String calledNumber = robotSession.getCalledNumber();
        String userId = getUserIdByPhoneNumber(tenantId, calledNumber);
        robotSession.setUserId(userId);
        robotSession.setCallerIdName(callRecordInfo.getCallerIdName());
        robotSession.setRing(callRecordInfo.getRing());
        robotSession.setCsRing(callRecordInfo.getCsRing());
        robotSession.setHangupBy(callRecordInfo.getHangupBy());
        robotSession.setHangupCause(hangupCause);
        robotSession.setHangupDisposition(callRecordInfo.getHangupDisposition());
        int csAnswer = callRecordInfo.getCsAnswered();
        log.info("robot session finishRobotSession uuid: {} csAnswer: {}", uuid, csAnswer);
        robotSession.setCallStatus(csAnswer == RobotCallRecordAnswerStatus.ANSWERED.getCode() ? RobotCallStatus.CALLED.getCode() : RobotCallStatus.UNCALLED_FAIL.getCode());
        robotSession.setCallResult(RobotCallResult.UNKNOWN.getCode());
        if (!Strings.isNullOrEmpty(hangupCause)) {
            log.info("robot session finishRobotSession uuid: {} hangupCause: {}", uuid, hangupCause);
            if (FsHangupCause.NO_USER_RESPONSE.getCode().equals(hangupCause)) {
                robotSession.setCallResult(RobotCallResult.NO_ANSWER.getCode());
            } else if (FsHangupCause.CALL_REJECTED.getCode().equals(hangupCause)) {
                robotSession.setCallResult(RobotCallResult.REFUSE_CALL.getCode());
            } else if (FsHangupCause.USER_BUSY.getCode().equals(hangupCause)) {
                robotSession.setCallResult(RobotCallResult.USER_BUSY.getCode());
            } else if (FsHangupCause.RECOVERY_ON_TIMER_EXPIRE.getCode().equals(hangupCause)) {
                robotSession.setCallResult(RobotCallResult.RECOVERY_ON_TIMER_EXPIRE.getCode());
            } else if (FsHangupCause.NORMAL_CLEARING.getCode().equals(hangupCause)) {
                robotSession.setCallResult(RobotCallResult.NORMAL_CLEARING.getCode());
            } else if (FsHangupCause.ORIGINATOR_CANCEL.getCode().equals(hangupCause)) {
                robotSession.setCallResult(RobotCallResult.SYSTEM_INTERCEPT.getCode());
            }
        }
        if (RobotCallStatus.CALLED.getCode().equals(robotSession.getCallStatus())) {
            try {
                robotCallFrequencyMonitorService.increaseOnSuccess(robotSession.getRobotId(), calledNumber, callRecordInfo.getStartStamp(), tenantId);
            } catch (Exception ex) {
                log.error("robot session finishRobotSession frequency monitor update failed robotId={} phone={}", robotSession.getRobotId(), calledNumber, ex);
            }
        }
        int round = nodeState == null ? 0 : nodeState.getRound();
        robotSession.setDialogueRounds(round);
        robotSession.setCallVideo(recordUrl);
//        try {
//            robotSession.setAudioDuration((int)FileUtil.getAudioDuration(recordUrl));
//        } catch (Exception e) {
//            log.error("robot session finishRobotSession error, getWavDuration error, uuid: {}", uuid, e);
//        }
        robotSession.setCallText(historyText);
        robotSession.setCallStartTime(callRecordInfo.getStartStamp());
        robotSession.setCallEndTime(callRecordInfo.getEndStamp());
        robotSession.setCallDuration(callRecordInfo.getDuration());
        robotSession.setAudioDuration(Math.toIntExact(callRecordInfo.getDuration()));
        robotSession.setUpdateTime(LocalDateTime.now());
        robotSession.setCallerDisplayNumber(callRecordInfo.getCallerIdNumber());
        log.info("robot session finishRobotSession update, uuid: {}, session: {}", uuid, robotSession);
        updateById(robotSession);
        executionPool.shutdown(robotSession);
        // 创建机器人信息存储
        RobotSessionInfo robotSessionInfo = robotSessionInfoService.getByUuid(uuid);
        if (robotSessionInfo == null) {
            robotSessionInfo = new RobotSessionInfo();
        }
        robotSessionInfo.setUuid(uuid);
        robotSessionInfo.setDirection(callRecordInfo.getDirection());
        robotSessionInfo.setGwIp(callRecordInfo.getGwIp());
        robotSessionInfo.setGwName(callRecordInfo.getGwName());
        robotSessionInfo.setAnswered(callRecordInfo.getAnswered());
        robotSessionInfo.setCallTalking10(callRecordInfo.getCallTalking10());
        robotSessionInfo.setBridgeHangupCause(callRecordInfo.getBridgeHangupCause());
        robotSessionInfo.setWaitsec(callRecordInfo.getWaitsec());
        robotSessionInfo.setBillsec(callRecordInfo.getBillsec());
        robotSessionInfo.setHangupPhrase(hangupPhrase);
        robotSessionInfo.setCallRecordInfo(JSON.toJSONString(callRecordInfo));
        robotSessionInfo.setCsAnswerStamp(callRecordInfo.getCsAnswerStamp());
        robotSessionInfo.setCsEndStamp(callRecordInfo.getCsEndStamp());
        robotSessionInfo.setCsDuration(callRecordInfo.getCsDuration());
        robotSessionInfo.setCsBillsec(callRecordInfo.getCsBillsec());
        robotSessionInfo.setAnswerStamp(callRecordInfo.getAnswerStamp());
        robotSessionInfo.setCreateTime(LocalDateTime.now());
        robotSessionInfo.setUpdateTime(LocalDateTime.now());
        robotSessionInfo.setIsDeleted(0);
        robotSessionInfo.setTenantId(tenantId);
        robotSessionInfoService.saveOrUpdate(robotSessionInfo);

        // 通知渠道
        RobotCallBackChannelDTO callBackChannelDTO = buildCallBackDto(robotSession, callRecordInfo);
        if (callBackChannelDTO != null) {
            channelRobotCallbackService.initChannelRecordCallback(uuid, tenantId);
            String dealIds = callBackChannelDTO.getDealId();
            if (StringUtils.isBlank(dealIds)) {
                channelRobotCallbackService.recordCallbackAsync(callBackChannelDTO, tenantId);
            } else {
                if (dealIds.indexOf(",") > -1) {
                    String dealIdList[] = dealIds.split(",");
                    String uuidOld = callBackChannelDTO.getUuid();
                    for (String dealId : dealIdList) {
                        callBackChannelDTO.setDealId(dealId);
                        callBackChannelDTO.setUuid(uuidOld + "-" + dealId);
                        channelRobotCallbackService.recordCallbackAsync(callBackChannelDTO, tenantId);
                    }
                } else {
                    channelRobotCallbackService.recordCallbackAsync(callBackChannelDTO, tenantId);
                }
            }

        }

        // 提交任务
        Long sessionId = robotSession.getId();
        log.info("robot session finishRobotSession uuid:{}, round:{}", uuid, round);
        if (round > 0) {
            log.info("robot session finishRobotSession submitTask ROBOT_CALL_USER_SUMMARY, uuid: {}, sessionId: {}", uuid, sessionId);
            aiConversationProcessService.submitTask(AiConversationProcessReq.builder().chatContactId(sessionId).taskType(AiConversationProcessTypeEnum.ROBOT_CALL_USER_SUMMARY).tenantId(tenantId).build());
        }
        String originContent = historyText;
        try {
            historyText = TextSplitter.mergeSpeakerTexts(historyText);
        } catch (Exception e) {
            historyText = originContent;
            log.error("robot session finishRobotSession merge content failed", e);
        }
        int length = historyText.length();
        log.info("robot session finishRobotSession uuid:{}, length:{}", uuid, length);
        // 小于分片阈值 并且 用户有回应的时候
        if (length <= textSplitThreshold * textSplitFactor && round > 0) {
            log.info("robot session finishRobotSession submitTask ROBOT_CALL_USER_INTENTION:  uuid: {}, sessionId: {}", uuid, sessionId);
            preSubmitIntentionTask(robotSession.getRobotHistoryId(), sessionId, tenantId);
        }
    }

    private RobotCallBackChannelDTO buildCallBackDto(RobotSession robotSession, CallRecordInfo callRecordInfo) {
        RobotCallTasksCustomer customer = robotCallTasksCustomerService.selectOneBySessionId(robotSession.getId());
        if (customer != null && YesOrNoEnum.NO.getValue().equals(customer.getIsTest())) {
            int successCount = robotSessionFlowMapper.selectCountComplete(robotSession.getUuid());
            Map<String, Object> variablesMap = RobotFormatUtil.parseStringToMap(customer.getVariables());
            String dealId = null;
            if (!CollectionUtils.isEmpty(variablesMap)) {
                Object dealIdObj = variablesMap.get("dealId");
                if (dealIdObj != null) {
                    dealId = (String) dealIdObj;
                }
            }

            RobotCallBackChannelDTO dto = new RobotCallBackChannelDTO();
            BeanUtils.copyProperties(callRecordInfo, dto);
            dto.setUserId(customer.getUserId());
            dto.setCallBackType(CallBackTypeEnum.ROBOT.getCode());
            dto.setDealId(dealId);
            dto.setReturnVisitSuccess(0);
            if (successCount > 0) {
                dto.setReturnVisitSuccess(1);
            }
            return dto;
        }
        return null;
    }

    @Override
    @Async("commonThreadPoolExecutor")
    public void intentionTaskCallBack(Long sessionId, String tenantId) {
        log.info("robot session intentionTaskCallBack, sessionId: {}", sessionId);
        RobotSession robotSession = getById(sessionId);
        if (robotSession == null) {
            log.error("robot session intentionTaskCallBack error, sessionId: {} not found", sessionId);
            return;
        }
        preSubmitIntentionTask(robotSession.getRobotHistoryId(), sessionId, tenantId);
    }

    /**
     * 预提交意图任务处理
     */
    private void preSubmitIntentionTask(Long historyId, Long sessionId, String tenantId) {
        RobotConfigVO robotCfg = robotConfigService.getRobotCfg(historyId);
        if (robotCfg == null) {
            log.error("robot session intentionTaskCallBack error, robotCfg not found, sessionId: {}", sessionId);
            return;
        }
        if (robotCfg.getIsEnabled() == 0) {
            log.info("robot session intentionTaskCallBack error, robotCfg isEnabled is 0, sessionId: {}", sessionId);
            return;
        }
        aiConversationProcessService.submitTask(AiConversationProcessReq.builder().chatContactId(sessionId).taskType(AiConversationProcessTypeEnum.ROBOT_CALL_USER_INTENTION).tenantId(tenantId).build());
    }

    private String getUserIdByPhoneNumber(String tenantId, String phoneNumber) {
        CustomerInfoWrapper customerInfoWrapper = tenantServiceHolder.getCustomerInfoWrapper(tenantId);
        if (customerInfoWrapper != null) {
            SimpleUserInfoDto userBasicInfoByPhone = customerInfoWrapper.getUserBasicInfoByPhone(phoneNumber, tenantId);
            if (userBasicInfoByPhone != null) {
                return userBasicInfoByPhone.getUserId();
            }
        }
        return null;
    }

    @Override
    public RobotSession getByUuid(String uuid) {
        return getOne(new LambdaQueryWrapper<RobotSession>().eq(RobotSession::getUuid, uuid));
    }

    @Override
    public RobotSessionVoiceVO chat(RobotSessionChatDTO dto) {
        // 0. 准备数据
        RobotSessionVoiceVO chatVO = new RobotSessionVoiceVO();
        String intent = "";
        String model = "";
        RobotSessionInfoModel infoModel = null;
        String nextNodeId = "";
        try {
            String uuid = dto.getUuid();
            chatVO.setUuid(uuid);
            String input = dto.getInput();
            String key = RobotConstant.ROBOT_SESSION_KEY + uuid;
            String jsonModel = jedisCluster.get(key);
            if (Strings.isNullOrEmpty(jsonModel)) {
                log.error("robot session chat InfoModel is null, uuid: {}", uuid);
                // 测试fs-解决初始默认语音响应导致直接挂断问题
//                return null;
                throw new RobotException(RobotResult.SESSION_NODE_NOT_INIT);
            }
            try {
                infoModel = JSON.parseObject(jsonModel, RobotSessionInfoModel.class);
                log.info("robot session chat InfoModel is: {}", infoModel);
            } catch (Exception e) {
                log.error("robot session parse chat InfoModel is error, uuid: {}", uuid);
                throw new RobotException(RobotResult.SESSION_DATA_ERROR);
            }
            Long robotHistoryId = infoModel.getRobotHistoryId();
            Long robotId = infoModel.getRobotId();
            String nodeId = infoModel.getNodeId();
            String tenantId = infoModel.getTenantId();
            String infoModelQuestion = infoModel.getQuestion();
            infoModel.setRound(infoModel.getRound() == null ? 1 : infoModel.getRound() + 1);
            List<String> history = infoModel.getHistory();
            if (nodeId == null) {
                log.error("robot session chat node id is null, uuid: {}", uuid);
                throw new RobotException(RobotResult.SESSION_STATUS_ERROR);
            }
            if (infoModel.getNodeType() == RobotNodeType.END.getCode()) {
                log.error("robot session chat node is end, uuid: {}", uuid);
                throw new RobotException(RobotResult.SESSION_ENDED);
            }
            Map<String, Integer> repeatTimeMap = infoModel.getRepeatTimeMap();
            if (repeatTimeMap == null) {
                repeatTimeMap = new HashMap<>();
            }
            String historyStr = String.join("\\n", history);

            // 打断处理
            if (dto.getInterrupt() == 1 && robotSessionInterruptEnabled) {
                String systemPrompt = "# 打断意图分析专家\n" +
                        "\n" +
                        "## 核心职责\n" +
                        "△ 精准识别用户打断意图（准确率要求≥95%）\n" +
                        "△ 严格遵循可用意图列表分类\n" +
                        "\n" +
                        "## 分析框架（三级递进判断）\n" +
                        "1. **关键词匹配层**（优先级最高）\n" +
                        "   - 检测明确打断意图表达（如\"先停一下\"→打断，\"你先别说\"→打断）\n" +
                        "   - 识别模糊打断意图（如\"等一下\"→可能打断，需结合上下文判断）\n" +
                        "\n" +
                        "2. **情绪分析层**\n" +
                        "   - 积极情绪+确认意向→非打断\n" +
                        "   - 中性情绪+模糊表达→可能打断\n" +
                        "   - 消极情绪+拒绝意向→非打断（通常为否定）\n" +
                        "\n" +
                        "3. **上下文关联层**\n" +
                        "   - 延续当前话题的请求→非打断\n" +
                        "   - 完全无关话题→打断\n" +
                        "   - 提出新问题→打断\n" +
                        "\n" +
                        "## 关键判定标准\n" +
                        "◉ 打断意图特征：\n" +
                        "- 明确打断词汇（先停一下/你先别说/等一下/我问个问题）\n" +
                        "- 提出新问题（这个XX是啥意思？/什么是XX？）\n" +
                        "- 要求暂停（暂停一下/停一下）\n" +
                        "\n" +
                        "◉ 非打断意图特征：\n" +
                        "- 明确同意词汇（好/行/可以/继续）\n" +
                        "- 积极反馈（嗯嗯/对的/没问题）\n" +
                        "- 延续对话的问句（然后呢？/接下来怎么做？）\n" +
                        "\n" +
                        "## 黄金案例库\n" +
                        "| 场景                | 正确分类 | 错误示范纠正          |\n" +
                        "|---------------------|----------|-----------------------|\n" +
                        "| Q: 需要帮您开通吗？<br>A: 先停一下，我问个问题 | 打断     | 错误归类→其它         |\n" +
                        "| Q: 确认投保金额<br>A: 你先别说，我想想       | 打断     | 错误归类→否定         |\n" +
                        "| Q: 产品介绍清楚吗<br>A: 这个功能是啥意思？ | 打断     | 错误归类→其它         |\n" +
                        "| Q: 需要帮您开通吗？<br>A: 嗯嗯对对好 | 非打断   | 错误归类→打断         |\n" +
                        "\n" +
                        "## 校验机制\n" +
                        "✅ 必须执行：\n" +
                        "- 对疑问句进行意图推定（\"怎么操作？\"→非打断）\n" +
                        "- 二次确认模糊回答（\"等一下\"需结合上下文判断）\n" +
                        "\n" +
                        "❌ 严格禁止：\n" +
                        "- 将含业务关键词但无明确打断意图的回复归为打断\n" +
                        "- 对测试性输入进行过度解读（如数学题→直接归非打断）";
                String userPrompt = "## 打断意图识别任务\n" +
                        "\n" +
                        "### 输入参数\n" +
                        "【可用意图列表，map，key是意图类别，value是意图描述】\n" +
                        "{\"打断\": \"用户有打断当前问题的意图\", \"非打断\": \"用户没有打断当前问题的意图\"}\n" +
                        "\n" +
                        "【对话记忆】\n" +
                        "最近3轮对话（参考权重20%%）：\n" +
                        "%s\n" +
                        "\n" +
                        "【当前问题】\n" +
                        "需重点分析的提问内容（核心权重30%%）：\n" +
                        "%s\n" +
                        "\n" +
                        "【用户回答】\n" +
                        "待分类的核心文本（决定权重50%%）：\n" +
                        "%s\n" +
                        "\n" +
                        "### 处理流程\n" +
                        "1. 关键词扫描 → 匹配预定义打断特征库\n" +
                        "2. 情绪分析 → 判断积极/中性/消极情绪\n" +
                        "3. 上下文对齐 → 检测话题连续性\n" +
                        "4. 列表匹配 → 从「可用意图列表」选择最匹配项\n" +
                        "\n" +
                        "### 输出规则\n" +
                        "△ 严格格式：{意图类别}\n" +
                        "△ 禁止：添加任何解释或标点\n" +
                        "△ 示例：\n" +
                        "用户说\"嗯嗯对对好\" → {非打断}\n" +
                        "用户说\"先停一下，我问个问题\" → {打断}\n" +
                        "用户说\"这个功能是啥意思？\" → {打断}\n" +
                        "用户说\"然后呢？\" → {非打断}";
                String prompt = String.format(userPrompt, history, infoModelQuestion, input);
                String string = robotAiService.intent(systemPrompt, prompt, "qwen-plus", 0.7f, 5);
                String intentA = robotAiService.parseIntentA(string);
                log.info("InsuranceDialogEngine handleRequest answer:{} intentA:{}， ", input, intentA);
                if ("非打断".equals(intentA)) {
                    chatVO.setUuid(uuid);
                    chatVO.setAction(RobotChatFSAction.NO_ACTION.getValue());
                    chatVO.setTtsUrl("");
                    return chatVO;
                }
            }

            // 1. 获取意图
            // 1.1 准备意图列表
            Map<String, String> intentNodeMap = new HashMap<>();
            Map<String, RobotFlowIntentType> intentTypeMap = new HashMap<>();
            Map<String, String> intentDesMap = new HashMap<>();
            // 可见意图列表/对应的节点列表
            List<AiRobotDialogEdge> nextNodeDataList = aiRobotDialogEdgeService.getNextNodeData(nodeId);
            if (nextNodeDataList == null || nextNodeDataList.isEmpty()) {
                log.error("robot session chat next node data is null, uuid: {}", uuid);
                throw new RobotException(RobotResult.ROBOT_NODE_MISSING_NEXT);
            }
            // 获取全局意图列表
            List<AiRobotDialogEdge> intentListByGlobalType = aiRobotDialogEdgeService.getIntentListByType(RobotIntentType.GLOBAL, robotHistoryId);
            List<AiRobotDialogEdge> intentListByType = new ArrayList<>();
            
            // 收集需要查询重复规则的节点ID
            List<Long> nodeIdsToQuery = new ArrayList<>();
            for (AiRobotDialogEdge intentL : intentListByGlobalType) {
                String nodeGlobalId = String.valueOf(intentL.getToNodeId());
                if (repeatTimeMap.containsKey(nodeGlobalId)) {
                    nodeIdsToQuery.add(Long.valueOf(nodeGlobalId));
                }
            }
            
            // 批量查询重复规则配置
            Map<Long, RobotRuleNode> repeatRuleNodeMap = new HashMap<>();
            if (!nodeIdsToQuery.isEmpty()) {
                List<RobotRuleNode> repeatRuleNodes = robotRuleNodeService.searchByNodeConfigTypeBatch(nodeIdsToQuery, RobotRuleConfigType.REPEAT.getCode(), robotHistoryId);
                for (RobotRuleNode ruleNode : repeatRuleNodes) {
                    repeatRuleNodeMap.put(ruleNode.getNodeId(), ruleNode);
                }
            }
            
            // 过滤全局意图
            for (AiRobotDialogEdge intentL : intentListByGlobalType) {
                String nodeGlobalId = String.valueOf(intentL.getToNodeId());
                if (repeatTimeMap.containsKey(nodeGlobalId)) {
                    Integer countG = repeatTimeMap.get(nodeGlobalId);
                    Long nodeIdLong = Long.valueOf(nodeGlobalId);
                    RobotRuleNode robotRepeatRuleNodeGlobal = repeatRuleNodeMap.get(nodeIdLong);
                    
                    if (robotRepeatRuleNodeGlobal != null && countG > robotRepeatRuleNodeGlobal.getMaxRetryTimes()) {
                        continue;
                    }
                }
                intentListByType.add(intentL);
            }
            nextNodeDataList.addAll(intentListByType);
            List<RobotSessionEdgeDst> nextNodeData = new ArrayList<>();
            List<Long> operationIdList = new ArrayList<>();
            Map<Long, RobotAttributeIntent> nextNodeMap = new HashMap<>();
            for (AiRobotDialogEdge nextNode : nextNodeDataList) {
                if (nextNode != null && (nextNode.getType() == RobotIntentType.SYSTEM.getCode() || nextNode.getType() == RobotIntentType.CUSTOM.getCode() || nextNode.getType() == RobotIntentType.GLOBAL.getCode())) {
                    operationIdList.add(nextNode.getOperationId());
                }
            }
            // 批量查询意图详情
            if (!operationIdList.isEmpty()) {
                List<RobotAttributeIntent> robotAttributeIntents = robotAttributeIntentService.listByIds(operationIdList);
                for (RobotAttributeIntent robotAttributeIntent : robotAttributeIntents) {
                    if (robotAttributeIntent == null) {
                        continue;
                    }
                    nextNodeMap.put(robotAttributeIntent.getId(), robotAttributeIntent);
                }
            }
            for (AiRobotDialogEdge nextNode : nextNodeDataList) {
                if (nextNode != null) {
                    Long intentId = nextNode.getOperationId();
                    RobotSessionEdgeDst robotSessionEdgeDst = new RobotSessionEdgeDst();
                    robotSessionEdgeDst.setEdgeType(nextNode.getType());
                    if (nextNodeMap.containsKey(intentId)) {
                        robotSessionEdgeDst.setEdgeProps(nextNodeMap.get(intentId));
                    }
                    robotSessionEdgeDst.setSrc(nodeId);
                    robotSessionEdgeDst.setDst(String.valueOf(nextNode.getToNodeId()));
                    nextNodeData.add(robotSessionEdgeDst);
                }
            }
            // 0.1.2 判断是否为任意意图
            if (nextNodeData.get(0).getEdgeType() == RobotIntentType.EMPTY.getCode()) {
                // 任意意图，不需要意图识别，直接进入下一节点
                nextNodeId = nextNodeData.get(0).getDst();
                int count = 0;
                if (repeatTimeMap.containsKey(nextNodeId)) {
                    count = repeatTimeMap.get(nextNodeId);
                }
                return handlerNodeChat(chatVO, uuid, nextNodeId, key, infoModel, input, count, RobotFlowType.EMPTY_INTENT, null, null);
            }
            // 0.2 判断是否为长时间未回应
            if (Strings.isNullOrEmpty(input)) {
                // 长时间未响应
                int longTimeoutCount = 0;
                String longTimeKey = nodeId + RobotConstant.LONG_TIMEOUT_SUFFIX;
                if (repeatTimeMap.containsKey(longTimeKey)) {
                    longTimeoutCount = repeatTimeMap.get(longTimeKey);
                }
                longTimeoutCount++;
                RobotRuleNode robotRuleNode = robotRuleNodeService.searchByNodeConfigType(Long.valueOf(nodeId), RobotRuleConfigType.TIMEOUT.getCode(), robotHistoryId);
                if (robotRuleNode != null) {
                    log.info("robot session chat node is timeout, robotRuleNode, uuid: {}, nodeId: {}, count: {}", uuid, nodeId, longTimeoutCount);
                    if (robotRuleNode.getSecondaryType() == RobotRuleSecondType.OTHER.getValue()) {
                        // 未知走其它
                        for (RobotSessionEdgeDst node : nextNodeData) {
                            if (node.getEdgeType() == RobotIntentType.OTHER.getCode()) {
                                // 其它意图
                                return handlerNodeChat(chatVO, uuid, node.getDst(), key, infoModel, input, longTimeoutCount, RobotFlowType.UNKNOWN_OTHER, RobotRuleConfigType.TIMEOUT.getMessage(), "");
                            }
                        }
                    } else if (longTimeoutCount >= robotRuleNode.getMaxRetryTimes()) {
                        // 多次未响应默认结束
                        log.debug("robot session chat node is timeout, robotRuleNode, maxTimes, uuid: {}, nodeId: {}, count: {}", uuid, nodeId, longTimeoutCount);
                        return handlerEndChat(chatVO, robotRuleNode.getEndSpeechText(), robotRuleNode.getEndSpeechAudio(), key, infoModel, input, intent, model);
                    } else {
                        repeatTimeMap.put(longTimeKey, longTimeoutCount);
                        infoModel.setRepeatTimeMap(repeatTimeMap);
                        log.debug("robot session chat node is timeout, robotRuleNode, times, uuid: {}, nodeId: {}, count: {}", uuid, nodeId, longTimeoutCount);
                        return handlerNodeChat(chatVO, uuid, nodeId, key, infoModel, input, longTimeoutCount, RobotFlowType.UNKNOWN_REPEAT, RobotRuleConfigType.TIMEOUT.getMessage(), "");
                    }
                } else {
                    log.info("robot session chat node is timeout, global robotRule, uuid: {}, nodeId: {}, count: {}", uuid, nodeId, longTimeoutCount);
                    RobotRule robotRule = robotRuleService.searchByType(RobotRuleConfigType.TIMEOUT.getCode(), tenantId);
                    if (robotRule.getSecondaryType() == RobotRuleSecondType.OTHER.getValue()) {
                        // 未知走其它
                        for (RobotSessionEdgeDst node : nextNodeData) {
                            if (node.getEdgeType() == RobotIntentType.OTHER.getCode()) {
                                // 其它意图
                                return handlerNodeChat(chatVO, uuid, node.getDst(), key, infoModel, input, longTimeoutCount, RobotFlowType.UNKNOWN_OTHER, RobotRuleConfigType.TIMEOUT.getMessage(), "");
                            }
                        }
                    } else if (longTimeoutCount > robotRule.getMaxRetryTimes()) {
                        log.debug("robot session chat node is timeout, global robotRule, max retry times, uuid: {}, nodeId: {}, count: {}", uuid, nodeId, longTimeoutCount);
                        return handlerEndChat(chatVO, robotRule.getEndSpeechText(), null, key, infoModel, input, intent, model);
                    } else {
                        repeatTimeMap.put(longTimeKey, longTimeoutCount);
                        infoModel.setRepeatTimeMap(repeatTimeMap);
                        log.debug("robot session chat node is timeout, global robotRule, times, uuid: {}, nodeId: {}, count: {}", uuid, nodeId, longTimeoutCount);
                        return handlerNodeChat(chatVO, uuid, nodeId, key, infoModel, input, longTimeoutCount, RobotFlowType.REPEAT, RobotRuleConfigType.TIMEOUT.getMessage(), "");
                    }
                }
            }
            // 4. 知识库
            List<RobotKnowledgeRela> robotKnowledgeList = robotKnowledgeRelaService.selectByRobotId(robotHistoryId);
            if (robotKnowledgeList != null && !robotKnowledgeList.isEmpty()) {
                if (robotKnowledgeList.size() == 1) {
                    RobotKnowledgeRela robotIntentKnowledge = robotKnowledgeList.get(0);
                    if (robotIntentKnowledge != null && robotIntentKnowledge.getId() != null) {
                        log.info("robot session chat use knowledge base recognition: uuid:{}, robotIntentKnowledge: {}", uuid, robotIntentKnowledge.getId());
                        RobotSessionVoiceVO voiceVO = handlerKnowledge(input, robotIntentKnowledge.getRobotKnowledgeId(), robotHistoryId, tenantId, repeatTimeMap, chatVO, infoModel, infoModelQuestion, nodeId, historyStr, key);
                        if (voiceVO != null) {
                            return voiceVO;
                        }
                    } else {
                        log.warn("robot session chat knowledge relation is null or has null ID: uuid:{}", uuid);
                        webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                            "知识库关联对象为空 uuid:" + uuid + " nodeId:" + nodeId);
                    }
                } else {
                    // 多知识库
                    log.info("robot session chat use knowledge base recognition: uuid:{}, robotKnowledgeList: {}", uuid, robotKnowledgeList);
                    for (RobotKnowledgeRela robotKnowledgeRela : robotKnowledgeList) {
                        String intentName = robotKnowledgeRela.getIntentName();
                        intentNodeMap.put(intentName, String.valueOf(robotKnowledgeRela.getRobotKnowledgeId()));
                        intentDesMap.put(intentName, "");
                        intentTypeMap.put(intentName, RobotFlowIntentType.KNOWLEDGE_INTENT);
                        // TODO 知识库意图训练
                        isSystem = false;
                    }
                }
            }
            // 3. 获取下一个节点相关信息
            for (RobotSessionEdgeDst node : nextNodeData) {
                if (node.getEdgeType() == RobotIntentType.OTHER.getCode()) {
                    // 其它意图
                    intentNodeMap.put(robotOtherBranch, node.getDst());
                    intentNodeMap.put(robotRepeatIntent, node.getSrc());
                    intentNodeMap.put(robotRudeIntent, node.getSrc());
                    intentTypeMap.put(robotOtherBranch, RobotFlowIntentType.INVISIBLE_INTENT);
                    intentDesMap.put(robotOtherBranch, robotOtherBranchDesc);
                    continue;
                }
                RobotAttributeIntent edgeProps = node.getEdgeProps();
                if (edgeProps != null) {
                    if (edgeProps.getModelType() != null) {
                        isSystem &= Objects.equals(edgeProps.getModelType(), RobotIntentModelType.SYSTEM_INTENT.getCode());
                    }
//                    isTrain &= Objects.equals(edgeProps.getModelType(), RobotIntentModelType.TRAINED_INTENT.getCode());
                    intentNodeMap.put(edgeProps.getIntentName(), node.getDst());
                    RobotFlowIntentType robotFlowIntentType = Objects.equals(edgeProps.getType(), RobotIntentType.GLOBAL.getCode()) ? RobotFlowIntentType.GLOBAL_INTENT : RobotFlowIntentType.BRANCH_INTENT;
                    intentTypeMap.put(edgeProps.getIntentName(), robotFlowIntentType);
                    intentDesMap.put(edgeProps.getIntentName(), edgeProps.getDescription());
                }
            }
            // 1.3 准备不可见意图
            intentTypeMap.put(robotRepeatIntent, RobotFlowIntentType.INVISIBLE_INTENT);
            intentDesMap.put(robotRepeatIntent, robotRepeatIntentDesc);
            intentTypeMap.put(robotRudeIntent, RobotFlowIntentType.INVISIBLE_INTENT);
            intentDesMap.put(robotRudeIntent, robotRudeIntentDesc);
            // 2. 选择意图识别模型
            if (isSystem) {
                // 系统级意图识别
                log.info("robot session chat use system intent recognition");
                RobotPrompt lastRobotPrompt = robotPromptService.getLastSystemRobotPrompt(tenantId);
                if (lastRobotPrompt == null) {
                    log.error("robot session chat last robot system prompt is null, uuid: {}", uuid);
                    lastRobotPrompt = robotPromptService.getLastAllSystemRobotPrompt();
                    if (lastRobotPrompt == null) {
                        log.error("robot session chat last robot all system prompt is null, uuid: {}", uuid);
                        throw new RobotException(RobotResult.ROBOT_NODE_MISSING_PROMPT);
                    }
                }
                model = lastRobotPrompt.getModel();
                String question = String.format(lastRobotPrompt.getUserPrompt(), historyStr, infoModelQuestion, input);
                String response = robotAiService.intent(lastRobotPrompt.getSystemPrompt(), question, model, lastRobotPrompt.getTemperature(), lastRobotPrompt.getMaxTokens());
                intent = robotAiService.parseIntentA(response);
            } else {
                //机器人专属模型或通用模型
                log.info("robot session chat use description intent recognition");
                RobotPrompt lastRobotPrompt = robotPromptService.getLastRobotPrompt(robotId, RobotIntentModelType.DESCRIPTION_INTENT.getCode(), tenantId);
                if (lastRobotPrompt == null) {
                    log.error("robot session chat last robot prompt is null, uuid: {}", uuid);
                    throw new RobotException(RobotResult.ROBOT_NODE_MISSING_PROMPT);
                }
                String formattedIntentString = intentDesMap.entrySet().stream()
                        .map(entry -> {
                            String k = entry.getKey();
                            Object v = entry.getValue();
                            return k + "={" + v + "}";
                        })
                        .collect(Collectors.joining("\n"));
                String question = String.format(lastRobotPrompt.getUserPrompt(), formattedIntentString, historyStr, infoModelQuestion, input);
                model = lastRobotPrompt.getModel();
                String response = robotAiService.intent(lastRobotPrompt.getSystemPrompt(), question, model, lastRobotPrompt.getTemperature(), lastRobotPrompt.getMaxTokens());
                intent = robotAiService.parseIntentA(response);
            }
            log.info("robot session chat intent: {}, intentTypeMap:{}, intentNodeMap:{}", intent, intentTypeMap, intentNodeMap);
            // 3. 识别意图
            // 查询重复匹配的最大次数规则（已在前面定义过）
            RobotRuleNode robotRepeatRuleNode = repeatRuleNodeMap.get(Long.valueOf(nodeId));
            // 其他分支
            String otherNodeId = intentNodeMap.get(robotOtherBranch);
            if (intentTypeMap.containsKey(intent) && intentTypeMap.get(intent).getVisible() && !robotOtherBranch.equals(intent)) {
                // 命中意图
                String hitNodeId = intentNodeMap.get(intent);
                nextNodeId = hitNodeId;
                if (hitNodeId == null) {
                    log.error("robot session chat next node id is null, uuid: {}", uuid);
                    throw new RobotException(RobotResult.ROBOT_NODE_MISSING_NEXT);
                }
                int count = 0;
                if (repeatTimeMap.containsKey(hitNodeId)) {
                    count = repeatTimeMap.get(hitNodeId);
                }
                if (RobotFlowIntentType.GLOBAL_INTENT.equals(intentTypeMap.get(intent))) {
                    count++;
                    if (robotRepeatRuleNode != null && count > robotRepeatRuleNode.getMaxRetryTimes()) {
                        log.info("robot session chat repeat time is over, uuid: {}, nodeId: {}, intent: {}, count: {}", uuid, nodeId, intent, count);
                        return handlerRuleFinalAction(chatVO, robotRepeatRuleNode.getFinalAction(), uuid, nodeId, key, infoModel, input, intent, model, otherNodeId, tenantId, robotRepeatRuleNode.getEndSpeechText(), robotRepeatRuleNode.getEndSpeechAudio());
                    }
                    return handlerNodeChat(chatVO, uuid, hitNodeId, key, infoModel, input, count, RobotFlowType.GLOBAL_INTENT, intent, model);
                }
                return handlerNodeChat(chatVO, uuid, hitNodeId, key, infoModel, input, count, RobotFlowType.NORMAL, intent, model);
            } else if (intentTypeMap.containsKey(intent) && intentTypeMap.get(intent) == RobotFlowIntentType.KNOWLEDGE_INTENT) {
                // 知识库意图
                String hitKnowledgeId = intentNodeMap.get(intent);
                log.info("robot session chat hit knowledge id: {}", hitKnowledgeId);
                RobotSessionVoiceVO voiceVO = handlerKnowledge(input, Long.valueOf(hitKnowledgeId), robotHistoryId, tenantId, repeatTimeMap, chatVO, infoModel, infoModelQuestion, nodeId, historyStr, key);
                if (voiceVO != null) {
                    return voiceVO;
                }
            }
            // 其它分支/隐藏意图
            // TODO 此处全部调用大模型
            if (YesOrNoEnum.YES.getValue() == 0) {
                String systemPrompt = " # 角色设定\n" +
                        "    ▲ 专业保险销售对话生成专家\n" +
                        "    ▲ 核心能力：自然引导对话走向\n" +
                        "    \n" +
                        "    # 核心任务\n" +
                        "    根据上下文生成符合业务目标的10-15字引导语\n" +
                        "    \n" +
                        "    # 引导策略\n" +
                        "    1. 自然承接：用「咱/您」等亲昵称谓建立信任\n" +
                        "    2. 价值强调：突显「每天仅需X元」「赠送XXX」等核心卖点\n" +
                        "    3. 渐进引导：使用「先...再...最后」等推进话术\n" +
                        "    4. 场景联想：关联「矿泉水钱」「一顿早餐」等日常类比\n" +
                        "    \n" +
                        "    # 生成规则\n" +
                        "    ✅ 必须\n" +
                        "    - 以疑问句/建议句结尾（使用「吧」「吗」「呢」）\n" +
                        "    - 包含至少2个主线提问中的关键词\n" +
                        "    - 保持口语化（使用缩略语「咱」「带您」）\n" +
                        "    - 长度严格控制在12-18个汉字\n" +
                        "    \n" +
                        "    ❌ 禁止\n" +
                        "    - 专业术语（如「保费」「保额」）\n" +
                        "    - 重复用词（避免连续出现相同词汇）\n" +
                        "    - 绝对化表述（如「肯定」「100%」）\n" +
                        "    - 任何符号（括号、星号等）\n" +
                        "    \n" +
                        "    # 输入要素\n" +
                        "    1. 【对话历史】: \n" +
                        "       {{historyStr}}\n" +
                        "    \n" +
                        "    2. 【当前状态】: \n" +
                        "       主线问题：{{mainQuestion}}\n" +
                        "       用户最后回复：{{input}}\n" +
                        "    \n" +
                        "    # 输出示例\n" +
                        "    ◉ 正面案例\n" +
                        "    输入：用户说\"太多了？\"\n" +
                        "    输出 → \"这个价格很划算呢，每天省瓶水钱就能保全家，我教您操作？\"\n" +
                        "    \n" +
                        "    ◉ 负面案例\n" +
                        "    输入：用户说\"考虑下\"\n" +
                        "    错误 → \"好的\" （缺少引导）\n" +
                        "    正确 → \"张先生是担心费用吗？其实每天1.3元还能赚积分，要不再看看？\"\n" +
                        "    \n" +
                        "    ◉ 负面案例\n" +
                        "    输入：用户说\"怎么能清洗？\"\n" +
                        "    错误 → \"这项服务超划算呢，咱先听听详细流程？\" （缺少用户内容回复）\n" +
                        "    正确 → \"这个清洗服务您买了就可以享受，咱先听听详细流程？\"\n" +
                        "    # 生成要求\n" +
                        "    请直接生成引导话术（严格遵循规则），不要有多余输出";
                String question = String.format("【对话上下文】\n" +
                        "    历史对话：%s\n" +
                        "    主线引导：%s\n" +
                        "    用户最新回复：%s\n" +
                        "    \n" +
                        "    请生成引导语：", historyStr, infoModel.getMainQuestion(), input);
                String answer = robotAiService.intent(systemPrompt, question, model, 1.0f, 30);
                log.info("robot session chat use knowledge base recognition, uuid: {}, question: {}, answer: {}", uuid, question, answer);
                chatVO.setAction(RobotChatFSAction.NO_ACTION.getValue());
                chatVO.setChat(answer);
                chatVO.setRound(infoModel.getRound() + 1);
                // 实时合成音频
                RobotHistory robotHistory = robotHistoryMapper.selectById(robotHistoryId);
                TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, answer);
                RobotTtsRecord robotTtsRecord = robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
                if (robotTtsRecord == null || Strings.isNullOrEmpty(robotTtsRecord.getAudioPath())) {
                    log.error("robot session chat use knowledge base recognition, uuid: {}, question: {}, answer: {}, ttsAuditionReq: {}", uuid, question, answer, ttsAuditionReq);
                    throw new RobotException(RobotResult.TTS_RESULT_EMPTY);
                }
                chatVO.setTtsUrl(robotTtsRecord.getAudioPath());
                infoModel.addDialog(infoModel.getQuestion(), RobotSessionInfoModel.TalkTypeEnum.ROBOT_RESPONSE);
                infoModel.addDialog(input, RobotSessionInfoModel.TalkTypeEnum.CUSTOMER_INPUT);
                infoModel.setQuestion(answer);
                infoModel.setRepeatTimeMap(repeatTimeMap);
                jedisCluster.setex(key, RobotConstant.ROBOT_SESSION_EXPIRE_TIME, JSON.toJSONString(infoModel));
                robotSessionNodeStateService.updateStateData(uuid, RobotNodeType.NORMAL.getCode(), nodeId, 0, input, answer, infoModel.getRound());
                robotSessionFlowService.updateFlowData(uuid, nodeId, nodeId, infoModel, input, RobotFlowType.AI_MODEL_REALTIME_REPLY.getCode(), tenantId, "", RobotFlowType.AI_MODEL_REALTIME_REPLY.getMessage(), answer, LocalDateTime.now(), chatVO.getTtsUrl());
                return chatVO;
            }
            nextNodeId = otherNodeId;
            if (robotRepeatIntent.equals(intent)) {
                // 要求重复
                // 上一次是知识库节点，且上一次问题与当前问题相同
                if (!Strings.isNullOrEmpty(infoModelQuestion) && !infoModelQuestion.equals(infoModel.getQuestion())) {
                    // 将问题和答案一起进行知识库匹配，因为此处有两种可能：1、用户没听清要求重复；2、用户问了一个相关的子问题
                    RobotSessionVoiceVO voiceVO = handlerKnowledge(infoModel.getQuestion() + input, infoModel.getLastKnowLedgeId(), robotHistoryId, tenantId, repeatTimeMap, chatVO, infoModel, infoModelQuestion, nodeId, historyStr, key);
                    if (voiceVO != null) {
                        return voiceVO;
                    }
                }
//                RobotRuleNode robotRuleNode = robotRuleNodeService.searchByNodeConfigType(Long.valueOf(nodeId), RobotRuleConfigType.REPEAT.getCode(), robotHistoryId);
                if (robotRepeatRuleNode != null) {
                    int count = 0;
                    if (repeatTimeMap.containsKey(nodeId)) {
                        count = repeatTimeMap.get(nodeId);
                    }
                    count++;
                    log.info("robot session chat repeat intent, robotRuleNode, uuid: {}, nodeId:{}, count:{}", uuid, nodeId, count);
                    if (count > robotRepeatRuleNode.getMaxRetryTimes()) {
                        log.debug("robot session chat repeat intent, robotRuleNode,  max retry times, uuid: {}, nodeId:{}, count:{}", uuid, nodeId, count);
                        return handlerRuleFinalAction(chatVO, robotRepeatRuleNode.getFinalAction(), uuid, nodeId, key, infoModel, input, intent, model, otherNodeId, tenantId, robotRepeatRuleNode.getEndSpeechText(), robotRepeatRuleNode.getEndSpeechAudio());
                    } else {
                        return handlerNodeChat(chatVO, uuid, nodeId, key, infoModel, input, count, RobotFlowType.REPEAT, intent, model);
                    }
                } else {
                    log.info("robot session chat repeat intent, robotRule, uuid: {}, nodeId:{}", uuid, nodeId);
                    RobotRule robotRule = robotRuleService.searchByType(RobotRuleConfigType.REPEAT.getCode(), tenantId);
                    if (robotRule != null) {
                        int count = 0;
                        if (repeatTimeMap.containsKey(nodeId)) {
                            count = repeatTimeMap.get(nodeId);
                        }
                        count++;
                        if (count > robotRule.getMaxRetryTimes()) {
                            log.debug("robot session chat repeat intent, robotRule, max retry times, uuid: {}, nodeId:{}, count:{}", uuid, nodeId, count);
                            return handlerRuleFinalAction(chatVO, robotRule.getFinalAction(), uuid, nodeId, key, infoModel, input, intent, model, otherNodeId, tenantId, robotRule.getEndSpeechText(), null);
                        } else {
                            log.debug("robot session chat repeat intent, robotRule, times, uuid: {}, nodeId:{}, count:{}", uuid, nodeId, count);
                            return handlerNodeChat(chatVO, uuid, nodeId, key, infoModel, input, count, RobotFlowType.REPEAT, intent, model);
                        }
                    }
                }
            } else if (robotRudeIntent.equals(intent)) {
                // 投诉辱骂
                RobotRuleOne robotRuleOne = robotRuleOneService.getAvailable(robotHistoryId, RobotRuleOneConfigType.COMPLAINT.getCode());
                if (robotRuleOne == null) {
                    log.error("robot session chat rude intent, robotRuleOne is null, uuid: {}, nodeId:{}", uuid, nodeId);
                    throw new RobotException(RobotResult.ROBOT_DIMENSION_RULE_CONFIG_ERROR);
                }
                log.info("robot session chat rude intent, robotRuleOne, uuid: {}, nodeId:{}", uuid, nodeId);
                return handlerRuleFinalAction(chatVO, RobotRuleFinalAction.END_BRANCH.getValue(), uuid, nodeId, key, infoModel, input, intent, model, otherNodeId, tenantId, robotRuleOne.getEndSpeechText(), robotRuleOne.getEndSpeechAudio());
            } else {
                // 未知意图
                //未知
                RobotRuleNode robotRuleNode = robotRuleNodeService.searchByNodeConfigType(Long.valueOf(nodeId), RobotRuleConfigType.UNKNOWN_INTENT.getCode(), robotHistoryId);
                if (robotRuleNode != null) {
                    log.info("robot session chat un know intent robotRuleNode: uuid:{}, nodeId: {}", uuid, nodeId);
                    if (robotRuleNode.getSecondaryType().equals(RobotRuleSecondType.REPEAT.getValue())) {
                        int count = 0;
                        if (repeatTimeMap.containsKey(nodeId)) {
                            count = repeatTimeMap.get(nodeId);
                        }
                        count++;
                        if (count <= robotRuleNode.getMaxRetryTimes()) {
                            return handlerNodeChat(chatVO, uuid, nodeId, key, infoModel, input, count, RobotFlowType.UNKNOWN_REPEAT, intent, model);
                        } else {
                            log.debug("robot session chat un know intent robotRuleNode, max retry times, uuid: {}, nodeId:{}, count:{}", uuid, nodeId, count);
                            return handlerRuleFinalAction(chatVO, robotRuleNode.getFinalAction(), uuid, nodeId, key, infoModel, input, intent, model, otherNodeId, tenantId, robotRuleNode.getEndSpeechText(), robotRuleNode.getEndSpeechAudio());
                        }
                    } else if (robotRuleNode.getSecondaryType().equals(RobotRuleSecondType.OTHER.getValue())) {
                        int count = 0;
                        if (repeatTimeMap.containsKey(otherNodeId)) {
                            count = repeatTimeMap.get(otherNodeId);
                        }
                        log.debug("robot session chat un know intent robotRuleNode, other, uuid: {}, nodeId:{}, count:{}", uuid, nodeId, count);
                        return handlerNodeChat(chatVO, uuid, otherNodeId, key, infoModel, input, count, RobotFlowType.OVER_MAX_COUNT_OTHERS, intent, model);
                    }
                } else {
                    log.info("robot session chat un know intent robotRule: uuid:{}, nodeId: {}", uuid, nodeId);
                    RobotRule robotRule = robotRuleService.searchByType(RobotRuleConfigType.UNKNOWN_INTENT.getCode(), tenantId);
                    if (robotRule.getSecondaryType().equals(RobotRuleSecondType.REPEAT.getValue())) {
                        int count = 0;
                        if (repeatTimeMap.containsKey(nodeId)) {
                            count = repeatTimeMap.get(nodeId);
                        }
                        count++;
                        if (count <= robotRule.getMaxRetryTimes()) {
                            return handlerNodeChat(chatVO, uuid, nodeId, key, infoModel, input, count, RobotFlowType.UNKNOWN_REPEAT, intent, model);
                        } else {
                            log.debug("robot session chat un know intent robotRule, max retry times, uuid: {}, nodeId:{}, count:{}", uuid, nodeId, count);
                            return handlerRuleFinalAction(chatVO, robotRule.getFinalAction(), uuid, nodeId, key, infoModel, input, intent, model, otherNodeId, tenantId, robotRule.getEndSpeechText(), null);
                        }
                    } else if (robotRule.getSecondaryType().equals(RobotRuleSecondType.OTHER.getValue())) {
                        int count = 0;
                        if (repeatTimeMap.containsKey(otherNodeId)) {
                            count = repeatTimeMap.get(otherNodeId);
                        }
                        log.debug("robot session chat un know intent robotRule, other, uuid: {}, nodeId:{}, count:{}", uuid, nodeId, count);
                        return handlerNodeChat(chatVO, uuid, otherNodeId, key, infoModel, input, count, RobotFlowType.OVER_MAX_COUNT_OTHERS, intent, model);
                    }
                }
            }

            throw new RobotException(RobotResult.SESSION_FLOW_CONFIG_ERROR);
        } catch (Throwable e) {
            String uuid = "";
            String nodeId = "";
            log.error("robot session chat error:", e);
            String robotDefaultEndAudio = this.robotDefaultEndAudio;
            if (dto != null) {
                try {
                    uuid = dto.getUuid();
                    String key = RobotConstant.ROBOT_SESSION_KEY + uuid;
                    jedisCluster.del(key);
                    if (infoModel != null) {
                        nodeId = infoModel.getNodeId();
                        chatVO.setRound(infoModel.getRound());
                        robotSessionFlowService.updateFlowData(uuid, infoModel.getNodeId(), RobotConstant.ROBOT_DEFAULT_ERROR_END_NODE_ID, infoModel, dto.getInput(), RobotFlowType.ERROR.getCode(), infoModel.getTenantId(), intent, model, String.valueOf(e), LocalDateTime.now(), robotDefaultEndAudio);
                        robotSessionNodeStateService.updateStateData(uuid, RobotNodeAction.HANG_UP.getValue(), RobotConstant.ROBOT_DEFAULT_ERROR_END_NODE_ID, 0, dto.getInput(), robotDefaultEndSpeechText, infoModel.getRound());
                    }
                } catch (Throwable e1) {
                    log.error("robot session chat error handler exception:", e1);
                }
            }
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, "uuid=" + uuid + " nodeId=" + nodeId + " nextNodeId=" + nextNodeId + " error:" + e);
            chatVO.setTtsUrl(robotDefaultEndAudio);
            chatVO.setAction(RobotChatFSAction.HANG_UP.getValue());
            chatVO.setChat(robotDefaultEndSpeechText);
            return chatVO;
        }
    }

    @Resource
    private Cache<String, DialogSession> aiDialogSessionCache;

    @Override
    public RobotSessionVoiceVO chatAi(RobotSessionChatDTO dto) {
        DialogSession session = aiDialogSessionCache.getIfPresent(dto.getUuid());
        if (session == null) {
            session = new DialogSession();
        }
        Node current = session.getCurrent();
        DialogResponse dialogResponse = insuranceDialogEngine.handleRequest(session, dto.getInput());
        log.info("robot session chat ai: {}", dialogResponse);
        int action = RobotChatFSAction.NO_ACTION.getValue();
        if (Objects.equals(dialogResponse.getNextNode(), "exit")) {
            action = RobotChatFSAction.HANG_UP.getValue();
        }
        RobotSessionFlow robotSessionFlow = new RobotSessionFlow();
        robotSessionFlow.setRobotTalk(dialogResponse.getResponseText());
        robotSessionFlow.setUserTalk(dto.getInput());
        robotSessionFlow.setFlowType(RobotFlowType.AI_MODEL_SKIP.getCode());
        robotSessionFlow.setFlowIntent(dialogResponse.getNextNode());
        robotSessionFlow.setModelType("gpt-4o");
        robotSessionFlow.setRobotHistoryId(0L);
        robotSessionFlow.setTenantId("");
        robotSessionFlow.setUuid(dto.getUuid());
        robotSessionFlow.setFromNodeId(current.getId());
        robotSessionFlow.setToNodeId(dialogResponse.getNextNode());
        robotSessionFlow.setHistoryTalk(session.getHistory().toString());
        robotSessionFlow.setConfidence(1f);
        robotSessionFlow.setCreateTime(LocalDateTime.now());
        robotSessionFlow.setUpdateTime(LocalDateTime.now());
        robotSessionFlow.setIsDeleted(0);
        robotSessionFlowService.save(robotSessionFlow);
        return new RobotSessionVoiceVO(dto.getUuid(), action, dialogResponse.getResponseText(), session.getRound(), dto.getUuid());
    }

    @Override
    public RobotSessionTestChatVO welcomeAi() {
        String uuid = RobotFormatUtil.generateAiUuid();
        DialogSession session = new DialogSession();
        session.setSessionId(uuid);
        String start = "喂，您好";
        List<DialogTurn> history = new ArrayList<>();
        DialogTurn turn = new DialogTurn("理财师", start);
        history.add(turn);
        session.setHistory(history);
        Node node = new Node();
        node.setId("greeting");
        node.setDescription("初始问候阶段");
        node.setSuccessPath("product_intro");
        node.setFailurePath("exit");
        HashSet<String> successSet = new HashSet<>();
        successSet.add("product_interest");
        successSet.add("ask_detail");
        node.setSuccessIntents(successSet);
        HashSet<String> failSet = new HashSet<>();
        failSet.add("refuse");
        failSet.add("hangup");
        node.setFailureIntents(failSet);
        session.setCurrent(node);
        session.setLastActiveTime(LocalDateTime.now());
        aiDialogSessionCache.put(uuid, session);
        return new RobotSessionTestChatVO(uuid, start, RobotChatFSAction.NO_ACTION.getValue());
    }

    /**
     * 处理规则节点的结束逻辑
     */
    private RobotSessionVoiceVO handlerRuleFinalAction(RobotSessionVoiceVO chatVO, int finalAction, String uuid, String nodeId, String key, RobotSessionInfoModel infoModel, String input, String intent, String model, String otherNodeId, String tenantId, String endSpeechText, String endSpeechAudio) {
        if (finalAction == RobotRuleFinalAction.END_BRANCH.getValue()) {
            return handlerEndChat(chatVO, endSpeechText, endSpeechAudio, key, infoModel, input, intent, model);
        } else if (finalAction == RobotRuleFinalAction.OTHER_BRANCH.getValue()) {
            Integer count = 0;
            Map<String, Integer> repeatTimeMap = infoModel.getRepeatTimeMap();
            if (repeatTimeMap != null) {
                if (repeatTimeMap.containsKey(otherNodeId)) {
                    count = repeatTimeMap.get(otherNodeId);
                }
            }
            return handlerNodeChat(chatVO, uuid, otherNodeId, key, infoModel, input, count, RobotFlowType.OVER_MAX_COUNT_OTHERS, intent, model);
        }
        return chatVO;
    }

    /**
     * 处理节点的最终流转逻辑
     */
    private RobotSessionVoiceVO handlerNodeChat(RobotSessionVoiceVO chatVO, String uuid, String toNodeId, String key, RobotSessionInfoModel infoModel, String input, Integer order, RobotFlowType flowType, String intent, String model) {
        AiRobotDialogNode aiRobotDialogNode = aiRobotDialogNodeService.getById(toNodeId);
        if (aiRobotDialogNode == null) {
            log.error("handlerNodeChat robot node not exist:{}", toNodeId);
            throw new RobotException(RobotResult.ROBOT_NODE_NOT_EXIST);
        }
        
        // 信息采集（每次进入节点时都执行）
        executeInfoCollection(aiRobotDialogNode, infoModel, input, uuid);
        Map<String, Object> varMap = infoModel.getVarMap();
        Map<String, String> varMapStr = new HashMap<>();
        for (Map.Entry<String, Object> entry : varMap.entrySet()) {
            varMapStr.put(entry.getKey(), entry.getValue().toString());
        }
        String nodeId = infoModel.getNodeId();
        // 逻辑判断处理
        if (aiRobotDialogNode.getType() == RobotNodeType.JUDGE.getCode()) {
            String nextNodeId = "";
            List<AiRobotDialogEdge> nextNodeData = aiRobotDialogEdgeService.getNextNodeData(toNodeId);
            List<Long> operationIds = new ArrayList<>();
            Map<Long, Long> operationIdMap = new HashMap<>();
            for (AiRobotDialogEdge aiRobotDialogEdge : nextNodeData) {
                if (aiRobotDialogEdge.getType() == RobotIntentType.LOGIC.getCode()) {
                    operationIds.add(aiRobotDialogEdge.getOperationId());
                    operationIdMap.put(aiRobotDialogEdge.getOperationId(), aiRobotDialogEdge.getToNodeId());
                } else if (aiRobotDialogEdge.getType() == RobotIntentType.OTHER.getCode()) {
                    nextNodeId = String.valueOf(aiRobotDialogEdge.getToNodeId());
                }
            }
            if (!operationIds.isEmpty()) {
                List<RobotAttributeBranch> robotAttributeBranches = robotAttributeBranchService.listByIds(operationIds);
                for (RobotAttributeBranch robotAttributeBranch : robotAttributeBranches) {
                    RobotBranch branch = new RobotBranch();
                    branch.setId(robotAttributeBranch.getId());
                    branch.setName(robotAttributeBranch.getName());
                    List<RobotBranchCondition> robotBranchConditionList = JSON.parseArray(robotAttributeBranch.getConditions()).toJavaList(RobotBranchCondition.class);
                    branch.setConditionList(robotBranchConditionList);
                    branch.setRelation(robotAttributeBranch.getRelation());
                    R<Boolean> result = robotAttributeBranchService.robotBranchConditionExecute(branch, varMapStr);
                    if (result.isSuccess() && result.getData()) {
                        nextNodeId = String.valueOf(operationIdMap.get(robotAttributeBranch.getId()));
                        break;
                    }
                }
            }
            int count = 0;
            Map<String, Integer> repeatTimeMap = infoModel.getRepeatTimeMap();
            if (repeatTimeMap == null) {
                repeatTimeMap = new HashMap<>();
            }
            if (repeatTimeMap.containsKey(nextNodeId)) {
                count = repeatTimeMap.get(nextNodeId);
            }
            repeatTimeMap.put(nextNodeId, count + 1);
            aiRobotDialogNode = aiRobotDialogNodeService.getById(nextNodeId);
            robotSessionFlowService.updateFlowData(uuid, nodeId, toNodeId, infoModel, input, flowType.getCode(), infoModel.getTenantId(), intent, model, "", LocalDateTime.now(), "");
            flowType = RobotFlowType.LOGIC;
            toNodeId = nextNodeId;
        }
        String fullText;
        int nodeType;
        String subDialogs = aiRobotDialogNode.getSubDialogs();
        RobotDialogDTO dialogDTO = JSON.parseObject(subDialogs, RobotDialogDTO.class);
        RobotDialog robotDialog;
        int orderAll = 0;
        try {
            log.info("handlerNodeChat robot node dialog:{}, order:{}", dialogDTO, order);
            List<RobotDialog> dialogList = dialogDTO.getDialogList();
            // 循环取话术
            orderAll = order % dialogList.size();
            robotDialog = dialogList.get(orderAll);
            fullText = robotDialog.getHandledText();
        } catch (Exception e) {
            log.error("handlerNodeChat robot node dialog error:{}", toNodeId, e);
            throw new RobotException(RobotResult.SESSION_NODE_SUB_DIALOGS_NOT_EXIST);
        }
        // 话术赋值
        nodeType = aiRobotDialogNode.getType();
        if (robotTtsAll) {
            if (!fullText.contains("$") && fullText.contains("#")) {
                RobotSessionComposedTalk robotSessionComposedTalk = robotSessionComposedTalkService.getByNodeIdAndOrder(uuid, toNodeId, orderAll);
                chatVO.setTtsUrl(robotSessionComposedTalk.getAudioPath());
                chatVO.setChat(robotSessionComposedTalk.getFullText());
                fullText = robotSessionComposedTalk.getFullText();
            } else {
                fullText = getFullTtsFormNode(robotDialog, aiRobotDialogNode, varMap, uuid, chatVO);
            }
        } else {
            fullText = getFullTtsFormNode(robotDialog, aiRobotDialogNode, varMap, uuid, chatVO);
        }

        if (nodeType == RobotNodeType.END.getCode()) {
            chatVO.setAction(RobotChatFSAction.HANG_UP.getValue());
            jedisCluster.del(key);
            flowType = RobotFlowType.END;

        } else {
            chatVO.setAction(RobotChatFSAction.NO_ACTION.getValue());
        }
        // 更新缓存会话信息
        Map<String, Integer> repeatTimeMap = infoModel.getRepeatTimeMap();
        if (repeatTimeMap == null) {
            repeatTimeMap = new HashMap<>();
        }
        // 全局意图每次都调用
        if (order == 0 || flowType == RobotFlowType.GLOBAL_INTENT) {
            // 结束前接口调用
            action(aiRobotDialogNode, infoModel, uuid);
        }
        repeatTimeMap.put(toNodeId, order);
        infoModel.setRepeatTimeMap(repeatTimeMap);
        infoModel.setNodeId(toNodeId);
        infoModel.addDialog(infoModel.getQuestion(), RobotSessionInfoModel.TalkTypeEnum.ROBOT_RESPONSE);
        infoModel.addDialog(input, RobotSessionInfoModel.TalkTypeEnum.CUSTOMER_INPUT);
        infoModel.setQuestion(fullText);
        infoModel.setMainQuestion(fullText);
        infoModel.setNodeType(nodeType);
        jedisCluster.setex(key, RobotConstant.ROBOT_SESSION_EXPIRE_TIME, JSON.toJSONString(infoModel));
        robotSessionFlowService.updateFlowData(uuid, nodeId, toNodeId, infoModel, input, flowType.getCode(), infoModel.getTenantId(), intent, model, "", LocalDateTime.now(), chatVO.getTtsUrl());
        robotSessionNodeStateService.updateStateData(uuid, infoModel.getNodeType(), infoModel.getNodeId(), order, input, fullText, infoModel.getRound());
        chatVO.setRound(infoModel.getRound());
        if (chatVO.getAction() == RobotChatFSAction.HANG_UP.getValue()) {
            jedisCluster.del(key);
        }
        return chatVO;
    }

    private void action(AiRobotDialogNode node, RobotSessionInfoModel infoModel, String uuid) {
        String action = node.getAction();
        if (action == null) {
            log.info("handlerNodeChat robot node action is null:{}", uuid);
            return;
        }
        String[] actions = action.split(",");
        for (String actionStr : actions) {
            actionStr = actionStr.trim();
            if (actionStr.isEmpty()) {
                log.warn("Skipping empty action in: {}", action);
                continue;
            }
            log.info("handlerNodeChat robot node action:{}", actionStr);
            if (Integer.parseInt(actionStr) == RobotNodeAction.CALL_API.getValue() && node.getApiId() != null) {
                interfaceCall(node, infoModel, uuid);
            } else if (Integer.parseInt(actionStr) == RobotNodeAction.SEND_SMS.getValue() && node.getSmsId() != null) {
                sendRobotMessage(uuid, infoModel, node);
            }
        }
    }

    /**
     * 接口调用
     */
    private void interfaceCall(AiRobotDialogNode aiRobotDialogNode, RobotSessionInfoModel infoModel, String uuid) {
        log.info("handlerNodeChat robot node action call api:{}", uuid);
        Map<String, Object> varMap = infoModel.getVarMap();
        if (varMap == null) {
            varMap = new HashMap<>();
        }
        Map<String, Object> reqVarMap = new HashMap<>();
        if (aiRobotDialogNode.getApiId() != null) {
            // 变量查询和赋值
            String apiReqParamMappings = aiRobotDialogNode.getApiReqParamMappings();
            if (!Strings.isNullOrEmpty(apiReqParamMappings)) {
                try {
                    // 请求参数准备
                    List<ApiParamMapping> apiParams = JSON.parseArray(apiReqParamMappings, ApiParamMapping.class);
                    log.info("handlerNodeChat robot node action call req param uuid:{}, api:{}, varMap:{}", uuid, apiParams, varMap);
                    for (ApiParamMapping apiParam : apiParams) {
                        Long varId = apiParam.getVarId();
                        String paramName = apiParam.getParamName();
                        String variableKey = "";
                        // 关联变量
                        Integer varType = apiParam.getVarType();
                        if (RobotVarTypeEnum.CUSTOM.getCode().equals(varType)) {
                            RobotAttributeVariable variable = robotAttributeVariableService.getById(varId);
                            if (variable != null) {
                                variableKey = RobotFormatUtil.formatVarKey(variable.getVariableKey(), RobotVarTypeEnum.CUSTOM);
                                if (varMap.containsKey(variableKey)) {
                                    reqVarMap.put(paramName, varMap.get(variableKey));
                                }
                            }
                        } else if (RobotVarTypeEnum.DYNAMIC.getCode().equals(varType)) {
                            TenantAttributeVariable variable = tenantAttributeVariableService.getById(varId);
                            if (variable != null) {
                                variableKey = RobotFormatUtil.formatVarKey(variable.getVariableKey(), RobotVarTypeEnum.DYNAMIC);
                                if (varMap.containsKey(variableKey)) {
                                    reqVarMap.put(paramName, varMap.get(variableKey));
                                }
                            }
                        } else {
                            log.error("handlerNodeChat robot node action call param api error:{}", uuid);
                        }
                    }
                } catch (Exception e) {
                    log.error("handlerNodeChat robot node action call req param api error:{}", uuid, e);
                    webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                        "接口调用参数处理失败 nodeId:" + aiRobotDialogNode.getId() + " uuid:" + uuid + " error:" + e.getMessage());
                }
            }
            // 获取接口配置并处理URL中的变量
            ChannelApiCfg apiCfg = channelApiService.getById(aiRobotDialogNode.getApiId());
            if (apiCfg == null) {
                log.error("API config not found, apiId: {}, uuid: {}", aiRobotDialogNode.getApiId(), uuid);
                webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                    "接口配置不存在 apiId:" + aiRobotDialogNode.getApiId() + " uuid:" + uuid);
                return;
            }
            
            ChannelApiCfgDTO dto = new ChannelApiCfgDTO();
            dto.setId(aiRobotDialogNode.getApiId());
            dto.setReqParamsMap(reqVarMap);
            dto.setTenantId(aiRobotDialogNode.getTenantId());
            
            // 处理URL中的变量替换
            String originalUrl = apiCfg.getUrl();
            if (!Strings.isNullOrEmpty(originalUrl)) {
                String processedUrl = processUrlVariables(originalUrl, varMap, aiRobotDialogNode.getTenantId());
                dto.setUrl(processedUrl); // 设置处理后的URL
                log.info("URL variable processing: original={}, processed={}, uuid={}", originalUrl, processedUrl, uuid);
            }
            
            ChannelHttpResp<Map<String, Object>> mapChannelHttpResp = channelApiService.channelApiRequest(dto);
            log.info("handlerNodeChat robot node action call api res uuid:{}, resp:{}", uuid, mapChannelHttpResp);
            if (ChannelHttpResp.isSuccess(mapChannelHttpResp)) {
                Map<String, Object> data = mapChannelHttpResp.getData();
                // 遍历加上前缀
                String apiResParamMappings = aiRobotDialogNode.getApiResParamMappings();
                if (!Strings.isNullOrEmpty(apiResParamMappings)) {
                    try {
                        List<ApiParamMapping> apiParams = JSON.parseArray(apiResParamMappings, ApiParamMapping.class);
                        log.info("handlerNodeChat robot node action call res param uuid:{}, api:{}, varMap:{}", uuid, apiParams, varMap);
                        for (ApiParamMapping apiParam : apiParams) {
                            Long varId = apiParam.getVarId();
                            String paramName = apiParam.getParamName();
                            Object paramValue = data.getOrDefault(paramName, null);
                            if (paramValue == null) {
                                continue;
                            }
                            String variableKey = "";
                            // 关联变量
                            Integer varType = apiParam.getVarType();
                            if (RobotVarTypeEnum.CUSTOM.getCode().equals(varType)) {
                                RobotAttributeVariable variable = robotAttributeVariableService.getById(varId);
                                if (variable != null) {
                                    variableKey = RobotFormatUtil.formatVarKey(variable.getVariableKey(), RobotVarTypeEnum.CUSTOM);
                                    varMap.put(variableKey, paramValue);
                                }
                            } else if (RobotVarTypeEnum.DYNAMIC.getCode().equals(varType)) {
                                TenantAttributeVariable variable = tenantAttributeVariableService.getById(varId);
                                if (variable != null) {
                                    variableKey = RobotFormatUtil.formatVarKey(variable.getVariableKey(), RobotVarTypeEnum.DYNAMIC);
                                    varMap.put(variableKey, paramValue);
                                }
                            } else {
                                log.error("handlerNodeChat robot node action call res param api error:{}", uuid);
                            }
                        }
                    } catch (Exception e) {
                        log.error("handlerNodeChat robot node action call param api error:{}", uuid, e);
                        webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                            "接口调用返回参数处理失败 nodeId:" + aiRobotDialogNode.getId() + " uuid:" + uuid + " error:" + e.getMessage());
                        return;
                    }
                }
            } else {
                // 接口调用失败
                log.error("handlerNodeChat robot node action call api failed:{}, response:{}", uuid, mapChannelHttpResp);
                webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                    "接口调用失败 nodeId:" + aiRobotDialogNode.getId() + " uuid:" + uuid + " response:" + mapChannelHttpResp);
            }
            // 异步合成动态变量的音频
            robotSessionComposedTalkService.buildDynamicTts(uuid, aiRobotDialogNode, varMap, infoModel.getRobotId());
        }
        infoModel.setVarMap(varMap);
    }

    /**
     * 执行信息采集
     */
    private void executeInfoCollection(AiRobotDialogNode node, RobotSessionInfoModel infoModel, String userInput, String uuid) {
        if (Strings.isNullOrEmpty(userInput) || node.getCollectInfo() == null || InfoCollectionTypeEnum.NONE.getCode().equals(node.getCollectInfo())) {
            return;
        }
        
        try {
            // 同步处理正则采集
            String collectedValue = infoCollectionService.executeCollection(userInput, node, infoModel.getVarMap(), uuid);
            
            if (!Strings.isNullOrEmpty(collectedValue) && node.getCollectInfoVar() != null && Objects.equals(node.getCollectInfo(), InfoCollectionTypeEnum.REGEX.getCode())) {
                // 获取关联的动态变量信息
                TenantAttributeVariable variable = tenantAttributeVariableService.getById(node.getCollectInfoVar());
                if (variable != null) {
                    String variableKey = RobotFormatUtil.formatVarKey(variable.getVariableKey(), RobotVarTypeEnum.DYNAMIC);
                    Map<String, Object> varMap = infoModel.getVarMap();
                    if (varMap == null) {
                        varMap = new HashMap<>();
                    }
                    
                    // 将采集到的信息存储到动态变量中
                    varMap.put(variableKey, collectedValue);
                    infoModel.setVarMap(varMap);
                    
                    log.info("Regex info collection success: uuid={}, nodeId={}, variable={}, value={}", 
                            uuid, node.getId(), variable.getVariableKey(), collectedValue);
                }
            }
            
            // 同步处理知识库采集
            if (Objects.equals(node.getCollectInfo(), InfoCollectionTypeEnum.KNOWLEDGE.getCode()) && node.getCollectInfoVar() != null) {
                String knowledgeId = node.getCollectInfoRule();
                if (!Strings.isNullOrEmpty(knowledgeId)) {
                    try {
                        Long knowledgeIdLong = Long.parseLong(knowledgeId);
                        R<KnowledgeResponse> knowledgeResponse = robotKnowledgeService.retrieve(userInput, knowledgeIdLong);

                        if (knowledgeResponse != null && knowledgeResponse.isSuccess() && knowledgeResponse.getData() != null) {
                            KnowledgeResponse response = knowledgeResponse.getData();
                            if (response.getRecords() != null && !response.getRecords().isEmpty()) {
                                // 取第一个（分数最高的）
                                KnowledgeRecords firstRecord = response.getRecords().get(0);
                                String content = firstRecord.getSegment().getContent();

                                // 解析JSON内容，提取id字段
                                try {
                                    JSONObject jsonObject = JSON.parseObject(content);
                                    String extractedId = jsonObject.getString("id");

                                    if (!Strings.isNullOrEmpty(extractedId)) {
                                        // 获取关联的动态变量信息
                                        TenantAttributeVariable variable = tenantAttributeVariableService.getById(node.getCollectInfoVar());
                                        if (variable != null) {
                                            String variableKey = RobotFormatUtil.formatVarKey(variable.getVariableKey(), RobotVarTypeEnum.DYNAMIC);
                                            Map<String, Object> varMap = infoModel.getVarMap();
                                            if (varMap == null) {
                                                varMap = new HashMap<>();
                                            }

                                            // 将提取到的id存储到动态变量中
                                            varMap.put(variableKey, extractedId);
                                            infoModel.setVarMap(varMap);

                                            log.info("Knowledge info collection success: uuid={}, nodeId={}, variable={}, value={}",
                                                    uuid, node.getId(), variable.getVariableKey(), extractedId);
                                        }
                                    }
                                } catch (Exception parseException) {
                                    log.error("Failed to parse knowledge response JSON: uuid={}, nodeId={}, content={}",
                                            uuid, node.getId(), content, parseException);
                                }
                            } else {
                                log.info("Knowledge search returned no results: uuid={}, nodeId={}, query={}",
                                        uuid, node.getId(), userInput);
                            }
                        } else {
                            log.warn("Knowledge search failed: uuid={}, nodeId={}, knowledgeId={}",
                                    uuid, node.getId(), knowledgeId);
                        }
                    } catch (NumberFormatException e) {
                        log.error("Invalid knowledge ID format: uuid={}, nodeId={}, knowledgeId={}",
                                uuid, node.getId(), knowledgeId, e);
                    }
                }
            }

            // 异步处理大模型采集（不阻塞主流程）
            if (Objects.equals(node.getCollectInfo(), InfoCollectionTypeEnum.AI_MODEL.getCode())) {
                infoCollectionService.executeCollectionAsync(userInput, node, infoModel.getVarMap(), uuid, infoCollectionModel);
            }
            
        } catch (Exception e) {
            log.error("Info collection failed: uuid={}, nodeId={}", uuid, node.getId(), e);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                "信息采集失败 nodeId:" + node.getId() + " uuid:" + uuid + " error:" + e.getMessage());
        }
    }

    /**
c     * 从候选答案中选择最佳答案（批量AI评分过滤）
     */
    private AnswerDTO selectBestAnswer(List<AnswerDTO> candidates, String userInput, String uuid) {
        if (!knowledgeVerifyEnabled || candidates.isEmpty()) {
            return candidates.get(0); // 返回置信度最高的（原有逻辑）
        }

        try {
            StringBuilder systemPrompt = new StringBuilder("对以下答案与问题的相关性进行评分（1-5分），只返回分数，用逗号分隔（如:4,2,5）\n问题：").append(userInput).append("\n答案选项：");
            
            for (int i = 0; i < candidates.size(); i++) {
                String answer = candidates.get(i).getAnswers().get(0);
                String shortAnswer = answer.substring(0, Math.min(50, answer.length())); // 取50字符用于评分
                systemPrompt.append("\n").append(i + 1).append(". ").append(shortAnswer);
            }
            systemPrompt.append("\n评分结果:");

            String result = robotAiService.intent("评估答案相关性", systemPrompt.toString(), knowledgeVerifyModel, 0.1f, 10);
            String cleanResult = robotAiService.parseIntentA(result);

            // 解析评分结果
            String[] scores = cleanResult.trim().split(",");
            int bestIndex = -1;
            int bestScore = 0;

            for (int i = 0; i < Math.min(scores.length, candidates.size()); i++) {
                try {
                    int score = Integer.parseInt(scores[i].trim());
                    if (score >= knowledgeRelevanceThreshold && score > bestScore) {
                        bestScore = score;
                        bestIndex = i;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid score format: {}, uuid: {}", scores[i], uuid);
                }
            }

            if (bestIndex >= 0) {
                log.info("AI selected answer index: {}, score: {}, threshold: {}, uuid: {}", bestIndex, bestScore, knowledgeRelevanceThreshold, uuid);
                return candidates.get(bestIndex);
            } else {
                log.info("No answer meets threshold {}, fallback to first answer, uuid: {}", knowledgeRelevanceThreshold, uuid);
                return candidates.get(0);
            }
        } catch (Exception e) {
            log.warn("Best answer selection failed, fallback to first answer: {}, uuid: {}", e.getMessage(), uuid);
            return candidates.get(0); // 失败时返回置信度最高的
        }
    }

    /**
     * 发送短信
     */
    private void sendRobotMessage(String uuid, RobotSessionInfoModel infoModel, AiRobotDialogNode node) {
        log.info("handlerNodeChat robot node action send sms:{}", uuid);
        
        try {
            // 发短信
            Map<String, Object> modelVarMap = infoModel.getVarMap();
            String phoneKey = RobotFormatUtil.formatVarKey(RobotConstant.USER_PHONE_KEY, RobotVarTypeEnum.CUSTOM);
            Long smsId = node.getSmsId();
            
            if (smsId == null) {
                log.error("handlerNodeChat robot node action send sms error - smsId is null:{}", uuid);
                webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                    "短信发送失败-短信ID为空 nodeId:" + node.getId() + " uuid:" + uuid);
                return;
            }
            
            RobotTenantSms robotTenantSms = robotTenantSmsService.getById(smsId);
            if (robotTenantSms == null) {
                log.error("handlerNodeChat robot node action send sms error - robotTenantSms not found:{}", uuid);
                webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                    "短信发送失败-短信配置不存在 nodeId:" + node.getId() + " smsId:" + smsId + " uuid:" + uuid);
                return;
            }
            
            String content = robotTenantSms.getContent();
            List<String> stringList = RobotFormatUtil.extractSmsPlaceholders(content);
            log.info("handlerNodeChat robot node action send sms uuid:{}, params:{}, modelVarMap:{}", uuid, stringList, modelVarMap);
            
            if (modelVarMap == null || !modelVarMap.containsKey(phoneKey)) {
                log.error("handlerNodeChat robot node action send sms error - phone number missing:{}", uuid);
                webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                    "短信发送失败-手机号缺失 nodeId:" + node.getId() + " uuid:" + uuid);
                return;
            }
            
            if (stringList.isEmpty()) {
                robotTenantSmsService.sendSms(uuid, node.getId(), smsId, (String) modelVarMap.get(phoneKey), null);
                return;
            }
            
            Map<String, Object> paramMap = new HashMap<>();
            for (String s : stringList) {
                String key = RobotFormatUtil.formatVarKey(s, RobotVarTypeEnum.CUSTOM);
                if (modelVarMap.containsKey(key)) {
                    paramMap.put(s, modelVarMap.get(key));
                }
            }
            
            robotTenantSmsService.sendSms(uuid, node.getId(), smsId, (String) modelVarMap.get(phoneKey), paramMap);
            log.info("handlerNodeChat robot node action send sms success:{}", uuid);
            
        } catch (Exception e) {
            log.error("handlerNodeChat robot node action send sms unexpected error:{}", uuid, e);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, 
                "短信发送异常 nodeId:" + node.getId() + " uuid:" + uuid + " error:" + e.getMessage());
        }
    }

    private String getFullTtsFormNode(RobotDialog robotDialog, AiRobotDialogNode aiRobotDialogNode, Map<String, Object> varMap, String uuid, RobotSessionVoiceVO chatVO) {
        // 话术赋值
        String audioPath = robotDialog.getAudio();
        String fullText = robotDialog.getHandledText();
        if ((fullText.contains("$") || fullText.contains("#")) && varMap != null) {
            fullText = RobotFormatUtil.replaceTextVariables(fullText, varMap);
        }
        if (Strings.isNullOrEmpty(audioPath)) {
            RobotHistory robotHistory = robotHistoryMapper.selectById(aiRobotDialogNode.getRobotHistoryId());
            if (robotHistory == null) {
                log.error("handlerNodeChat robot history not exist:{}", aiRobotDialogNode.getRobotHistoryId());
                throw new RobotException(RobotResult.ROBOT_HISTORY_NOT_EXIST);
            }
            buildNodeTextTts(robotHistory, robotDialog);
            audioPath = robotDialog.getAudio();
        }
        if (audioPath.contains(",")) {
            // 合成音频
            String[] split = audioPath.split(",");
            List<String> psthList = new ArrayList<>();
            List<String> varList = new ArrayList<>();
            for (String s : split) {
                if (s.startsWith("$") || s.startsWith("#")) {
                    String singleTaggedText = RobotFormatUtil.extractSingleTaggedText(s);
                    varList.add(singleTaggedText);
                }
            }
            Map<String, String> audioPathMap = robotSessionComposedTalkService.getAudioPathMap(varList, uuid);
            for (String s : split) {
                if (s.startsWith("#")) {
                    String nodeVarTts = getNodeVarTts(audioPathMap, s, RobotVarTypeEnum.CUSTOM);
                    if (Strings.isNullOrEmpty(nodeVarTts)) {
                        continue;
                    }
                    psthList.add(nodeVarTts);
                    continue;
                } else if (s.startsWith("$")) {
                    String nodeVarTts = getNodeVarTts(audioPathMap, s, RobotVarTypeEnum.DYNAMIC);
                    if (Strings.isNullOrEmpty(nodeVarTts)) {
                        continue;
                    }
                    psthList.add(nodeVarTts);
                    continue;
                }
                psthList.add(s);
            }
            log.info("getFullTtsFormNode mergeAudio:{}, pathList:{}", uuid, psthList);
            if (psthList.size() == 1) {
                chatVO.setTtsUrl(psthList.get(0));
                return fullText;
            }
//            String path = "";
            String path = robotTtsRecordService.mergeAudio(psthList, aiRobotDialogNode.getTenantId());
            chatVO.setTtsUrl(path);
        } else {
            // 获取合成音频
            chatVO.setTtsUrl(audioPath);
        }
        chatVO.setChat(fullText);
        return fullText;
    }

    private String getNodeVarTts(Map<String, String> audioPathMap, String s, RobotVarTypeEnum type) {
        String singleTaggedText = RobotFormatUtil.extractSingleTaggedText(s);
        String text = RobotFormatUtil.formatVarKey(singleTaggedText, type);
        if (audioPathMap.containsKey(text)) {
            String string = audioPathMap.get(text);
            if (!Strings.isNullOrEmpty(string)) {
                return string;
            }
        }
        return null;
    }

    /**
     * 处理规则结束逻辑
     */
    private RobotSessionVoiceVO handlerEndChat(RobotSessionVoiceVO chatVO, String endSpeechText, String endSpeechAudio, String key, RobotSessionInfoModel infoModel, String input, String intent, String model) {
        chatVO.setAction(RobotChatFSAction.HANG_UP.getValue());
        chatVO.setTtsUrl(endSpeechAudio);
        chatVO.setChat(endSpeechText);
        chatVO.setRound(infoModel.getRound());
        infoModel.addDialog(infoModel.getQuestion(), RobotSessionInfoModel.TalkTypeEnum.ROBOT_RESPONSE);
        infoModel.addDialog(input, RobotSessionInfoModel.TalkTypeEnum.CUSTOMER_INPUT);
        infoModel.setQuestion(endSpeechText);
        robotSessionFlowService.updateFlowData(infoModel.getUuid(), infoModel.getNodeId(), RobotConstant.ROBOT_DEFAULT_END_NODE_ID, infoModel, input, RobotFlowType.OVER_MAX_COUNT_END.getCode(), infoModel.getTenantId(), intent, model, "", LocalDateTime.now(), endSpeechAudio);
        // 结束，需要删掉缓存
        jedisCluster.del(key);
        robotSessionNodeStateService.updateStateData(infoModel.getUuid(), RobotNodeType.END.getCode(), RobotConstant.ROBOT_DEFAULT_END_NODE_ID, 0, input, endSpeechText, infoModel.getRound());
        return chatVO;
    }

    /**
     * 处理知识库逻辑
     */
    private RobotSessionVoiceVO handlerKnowledge(String input, Long knowledgeId, Long robotHistoryId, String tenantId, Map<String, Integer> repeatTimeMap, RobotSessionVoiceVO chatVO, RobotSessionInfoModel infoModel, String infoModelQuestion, String nodeId, String historyStr, String key) {
        infoModel.setLastKnowLedgeId(knowledgeId);
        R<List<AnswerDTO>> listR = robotKnowledgeService.retrieveKnowledge(input, knowledgeId);
        if (listR.isSuccess() && !listR.getData().isEmpty()) {
            List<AnswerDTO> answerList = listR.getData();
            
            // 第1步：过滤掉重复次数超限的答案
            List<AnswerDTO> candidateAnswers = new ArrayList<>();
            for (AnswerDTO answerDTO : answerList) {
                String segmentId = answerDTO.getSegmentId();
                int knowCount = 0;
                if (repeatTimeMap.containsKey(segmentId)) {
                    knowCount = repeatTimeMap.get(segmentId);
                }
                if (knowCount < RobotConstant.ROBOT_KNOWLEDGE_REPEAT_MAX_COUNT && answerDTO.getAnswers().size() > knowCount) {
                    candidateAnswers.add(answerDTO);
                }
            }
            
            if (candidateAnswers.isEmpty()) {
                return null; // 没有可用答案
            }
            
            // 第2步：AI选择最佳答案（批量处理，限制候选数量以控制prompt长度）
            List<AnswerDTO> topCandidates = candidateAnswers.subList(0, Math.min(3, candidateAnswers.size())); // 最多取前3个
            AnswerDTO bestAnswer = selectBestAnswer(topCandidates, input, chatVO.getUuid());
            
            // 第3步：处理选中的答案
            String segmentId = bestAnswer.getSegmentId();
            int knowCount = repeatTimeMap.getOrDefault(segmentId, 0);
            
            log.info("handlerKnowledge uuid:{}, selected segmentId:{}, knowCount:{}", chatVO.getUuid(), segmentId, knowCount);
            
            RobotHistory robotHistory = robotHistoryMapper.selectById(robotHistoryId);
            repeatTimeMap.put(segmentId, knowCount + 1);
            
            String answer = bestAnswer.getAnswers().get(knowCount);
            TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, answer);
            RobotTtsRecord robotTtsRecord = robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
            if (robotTtsRecord == null || Strings.isNullOrEmpty(robotTtsRecord.getAudioPath())) {
                throw new RobotException(RobotResult.TTS_RESULT_EMPTY);
            }
            String audioPath = robotTtsRecord.getAudioPath();
            String ttsPath = audioPath;
            String uuid = infoModel.getUuid();
            if (robotDefaultLeaderEnabled) {
                String leaderAudioPath = robotTtsRecordService.getPath(tenantId);
                ttsPath = audioPath + "," + leaderAudioPath;
                robotAiService.handlerKnownLeaderAudio(historyStr, infoModel, input, answer, leaderAudioPath, robotHistory, nodeId, tenantId, answer, infoModelQuestion, repeatTimeMap, key, bestAnswer.getQuestion(), knowCount, ttsPath);
            } else {
                infoModel.setNodeId(nodeId);
                infoModel.addDialog(infoModel.getQuestion(), RobotSessionInfoModel.TalkTypeEnum.ROBOT_RESPONSE);
                infoModel.addDialog(input, RobotSessionInfoModel.TalkTypeEnum.CUSTOMER_INPUT);
                infoModel.setQuestion(answer);
                infoModel.setRepeatTimeMap(repeatTimeMap);
                jedisCluster.setex(key, RobotConstant.ROBOT_SESSION_EXPIRE_TIME, JSON.toJSONString(infoModel));
                robotSessionNodeStateService.updateStateData(uuid, RobotNodeType.NORMAL.getCode(), nodeId, knowCount, input, answer, infoModel.getRound());
                robotSessionFlowService.updateFlowData(uuid, nodeId, nodeId, infoModel, input, RobotFlowType.KNOWLEDGE.getCode(), tenantId, answer, RobotConstant.ROBOT_KNOWLEDGE_MODEL, "", LocalDateTime.now(), ttsPath);
            }
            // 引导语
            chatVO.setChat(answer);
            chatVO.setTtsUrl(ttsPath);
            chatVO.setRound(infoModel.getRound());
            chatVO.setAction(RobotChatFSAction.NO_ACTION.getValue());
            return chatVO;
        }
        return null;
    }

    @Override
    public RobotSessionVoiceVO welcome(String uuid) {
        log.info("robot session welcome uuid:{}", uuid);
        RobotSessionVoiceVO chatVO = new RobotSessionVoiceVO();
        try {
            RobotSession robotSession = getByUuid(uuid);
            AiRobotDialogNode startNode = aiRobotDialogNodeService.getStartNodeByHistoryRobotId(robotSession.getRobotHistoryId());
            if (startNode == null) {
                log.error("robot session node start is null, uuid: {}", uuid);
                throw new RobotException(RobotResult.SESSION_START_POINT_NOT_EXIST);
            }
            String fullText = "";
            String audioPath = "";
            String subDialogs = startNode.getSubDialogs();
            Map<String, Object> varMap = new HashMap<>();
            if (StringUtils.isEmpty(subDialogs)) {
                throw new RobotException(RobotResult.SESSION_DATA_ERROR);
            }
            // 初始化静态变量
            List<RobotSessionComposedTalk> composedTalks = robotSessionComposedTalkService.getByUuid(uuid);
            for (RobotSessionComposedTalk composedTalk : composedTalks) {
                if (StringUtils.isNotBlank(composedTalk.getVariables())) {
                    varMap.put(RobotFormatUtil.formatVarKey(composedTalk.getVariables(), RobotVarTypeEnum.CUSTOM), composedTalk.getFullText());
                }
            }
            // 初始化手机号变量
            varMap.put(RobotFormatUtil.formatVarKey(RobotConstant.USER_PHONE_KEY, RobotVarTypeEnum.CUSTOM), robotSession.getCalledNumber());
            RobotDialogDTO dialogDTO = JSON.parseObject(subDialogs, RobotDialogDTO.class);
            List<RobotDialog> dialogList = dialogDTO.getDialogList();
            RobotDialog robotDialog = dialogList.get(0);
            fullText = robotDialog.getHandledText();
            // 获取完整话术和音频
            if (robotTtsAll) {
                chatVO.setTtsUrl(robotDialog.getAudio());
                if (!fullText.contains("$") && fullText.contains("#")) {
                    RobotSessionComposedTalk robotSessionComposedTalk = robotSessionComposedTalkService.getByNodeIdAndOrder(uuid, String.valueOf(startNode.getId()), 0);
                    chatVO.setTtsUrl(robotSessionComposedTalk.getAudioPath());
                    chatVO.setChat(robotSessionComposedTalk.getFullText());
                    fullText = robotSessionComposedTalk.getFullText();
                    audioPath = robotSessionComposedTalk.getAudioPath();
                } else {
                    getFullTtsFormNode(robotDialog, startNode, varMap, uuid, chatVO);
                    fullText = chatVO.getChat();
                    audioPath = chatVO.getTtsUrl();
                }
            } else {
                getFullTtsFormNode(robotDialog, startNode, varMap, uuid, chatVO);
                fullText = chatVO.getChat();
                audioPath = chatVO.getTtsUrl();
            }

            chatVO.setTtsUrl(audioPath);
            chatVO.setAction(RobotNodeType.START.getCode());
            // 初始化节点状态
            String nodeId = String.valueOf(startNode.getId());
            log.info("初始化节点状态, uuid:{}, nodeId:{}", uuid, nodeId);
            // 插入节点状态
            RobotSessionInfoModel nodeInfoModel = new RobotSessionInfoModel();
            nodeInfoModel.setMaxHistorySize(maxHistorySize);
            nodeInfoModel.setUuid(uuid);
            nodeInfoModel.setNodeId(nodeId);
            nodeInfoModel.setTenantId(startNode.getTenantId());
            nodeInfoModel.setRobotHistoryId(startNode.getRobotHistoryId());
            nodeInfoModel.setRobotId(robotSession.getRobotId());
            nodeInfoModel.setNodeType(startNode.getType());
            nodeInfoModel.setHistory(new ArrayList<>());
            nodeInfoModel.setQuestion(fullText);
            nodeInfoModel.setMainQuestion(fullText);
            nodeInfoModel.setVarMap(varMap);
            
            // 信息采集（开始节点 - welcome）
            executeInfoCollection(startNode, nodeInfoModel, "", uuid);
            
            action(startNode, nodeInfoModel, uuid);
            jedisCluster.setex(RobotConstant.ROBOT_SESSION_KEY + uuid, RobotConstant.ROBOT_SESSION_EXPIRE_TIME, JSON.toJSONString(nodeInfoModel));
            if (robotSessionNodeStateService.getNodeState(uuid) == null) {
                robotSessionNodeStateService.initNodeState(uuid, robotSession.getRobotHistoryId(), robotSession.getTenantId(), nodeId, fullText);
            }
            return chatVO;
        } catch (Throwable e) {
            log.error("robot session welcome error, uuid:{}", uuid, e);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_TASK_EXECUTE_ERROR, "welcome 数据问题" + " uuid=" + uuid);
            chatVO.setTtsUrl(robotDefaultEndAudio);
            chatVO.setAction(RobotNodeType.END.getCode());
            return chatVO;
        }
    }

    @Override
    public RobotSessionVoiceVO testStart(RobotTestStartDTO dto) {
        String oldUuid = dto.getUuid();
        if (!Strings.isNullOrEmpty(oldUuid)) {
            jedisCluster.del(RobotConstant.ROBOT_SESSION_KEY + dto.getUuid());
        }
        log.info("robot session test start, dto:{}", dto);
        Long robotId = dto.getRobotId();
        Integer version = dto.getVersion();
        LambdaQueryWrapper<RobotHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RobotHistory::getRobotId, robotId).eq(RobotHistory::getVersion, version).eq(RobotHistory::getIsDeleted, 0);

        RobotHistory robotHistory = robotHistoryMapper.selectOne(queryWrapper);
        Long robotHistoryId = robotHistory.getId();
        List<RobotTestStartDTO.Variable> variables = dto.getVariable();
        RobotSessionVoiceVO chatVO = new RobotSessionVoiceVO();
        Robot robot = robotService.getById(robotId);
        String tenantId = robot.getTenantId();
        String uuid = RobotFormatUtil.generateTestUuid();
        Map<String, Object> variableMap = new HashMap<>();
        if (variables != null && !variables.isEmpty()) {
            variableMap = variables.stream().filter(v -> v.getName() != null && v.getValue() != null).collect(Collectors.toMap(RobotTestStartDTO.Variable::getName, RobotTestStartDTO.Variable::getValue));
        }
        List<AiRobotDialogNode> robotDialogNodes = aiRobotDialogNodeService.getByHistoryRobotId(robotHistoryId);
        if (robotDialogNodes == null || robotDialogNodes.isEmpty()) {
            throw new RobotException(RobotResult.ROBOT_NODE_NOT_EXIST);
        }
        // 初始化全部节点
        log.info("testStart初始化全部节点, robotId:{}, 全部节点数量:{}", robotId, robotDialogNodes.size());
        List<RobotSessionComposedTalk> sessionComposedTalks = buildStaticVariable(variableMap, robotDialogNodes, robotHistory, uuid);
        // 静态变量初始化
        Map<String, Object> varMap = new HashMap<>();
        for (RobotSessionComposedTalk sessionComposedTalk : sessionComposedTalks) {
            if (StringUtils.isNotBlank(sessionComposedTalk.getVariables())) {
                varMap.put(RobotFormatUtil.formatVarKey(sessionComposedTalk.getVariables(), RobotVarTypeEnum.CUSTOM), sessionComposedTalk.getFullText());
            }
        }
        robotSessionComposedTalkService.saveOrUpdateBatch(sessionComposedTalks);
        AiRobotDialogNode startNode = aiRobotDialogNodeService.getStartNodeByHistoryRobotId(robotHistoryId);
        if (startNode == null) {
            log.error("robot session node start is null, robotId: {}", robotId);
            throw new RobotException(RobotResult.SESSION_START_POINT_NOT_EXIST);
        }
        log.info("testStart初始化节点状态, uuid:{}, nodeId:{}", uuid, startNode.getId());
        String subDialogs = startNode.getSubDialogs();
        RobotDialogDTO dialogDTO = JSON.parseObject(subDialogs, RobotDialogDTO.class);
        List<RobotDialog> dialogList = dialogDTO.getDialogList();
        RobotDialog robotDialog = dialogList.get(0);
        RobotSessionInfoModel nodeInfoModel = new RobotSessionInfoModel();
        nodeInfoModel.setMaxHistorySize(maxHistorySize);
        nodeInfoModel.setUuid(uuid);
        nodeInfoModel.setNodeId(String.valueOf(startNode.getId()));
        nodeInfoModel.setTenantId(tenantId);
        nodeInfoModel.setRobotHistoryId(robotHistoryId);
        nodeInfoModel.setRobotId(robotId);
        nodeInfoModel.setNodeType(startNode.getType());
        nodeInfoModel.setHistory(new ArrayList<>());
        nodeInfoModel.setVarMap(varMap);
        // 音频拼接变量
//        varMap.put(RobotFormatUtil.formatVarKey("name", RobotVarTypeEnum.DYNAMIC), "");
        String fullText = robotDialog.getHandledText();
        // 获取完整话术和音频
        if (robotTtsAll) {
            chatVO.setTtsUrl(robotDialog.getAudio());
            if (!fullText.contains("$") && fullText.contains("#")) {
                RobotSessionComposedTalk robotSessionComposedTalk = robotSessionComposedTalkService.getByNodeIdAndOrder(uuid, String.valueOf(startNode.getId()), 0);
                chatVO.setTtsUrl(robotSessionComposedTalk.getAudioPath());
                chatVO.setChat(robotSessionComposedTalk.getFullText());
                fullText = robotSessionComposedTalk.getFullText();
            } else {
                getFullTtsFormNode(robotDialog, startNode, varMap, uuid, chatVO);
                fullText = chatVO.getChat();
            }
        } else {
            getFullTtsFormNode(robotDialog, startNode, varMap, uuid, chatVO);
            fullText = chatVO.getChat();
        }
        nodeInfoModel.setQuestion(fullText);
        nodeInfoModel.setMainQuestion(fullText);
        nodeInfoModel.setRound(0);
        
        // 信息采集（开始节点 - testStart）
        executeInfoCollection(startNode, nodeInfoModel, "", uuid);
        
        action(startNode, nodeInfoModel, uuid);
        jedisCluster.setex(RobotConstant.ROBOT_SESSION_KEY + uuid, RobotConstant.ROBOT_SESSION_EXPIRE_TIME, JSON.toJSONString(nodeInfoModel));
        chatVO.setUuid(uuid);
        chatVO.setRound(0);
        chatVO.setAction(RobotNodeType.START.getCode());
        return chatVO;
    }

    @Override
    public List<RobotSessionComposedTalk> buildStaticVariable(Map<String, Object> variableMap, List<AiRobotDialogNode> sceneNodeData, RobotHistory robotHistory, String uuid) {
        List<RobotSessionComposedTalk> composedTalks = new ArrayList<>();
        if (robotTtsAll) {
            for (AiRobotDialogNode node : sceneNodeData) {
                if (node.getIsIncludeVar()) {
                    String subDialogs = node.getSubDialogs();
                    RobotDialogDTO dialogDTO = JSON.parseObject(subDialogs, RobotDialogDTO.class);
                    List<RobotDialog> dialogList = dialogDTO.getDialogList();
                    int order = 0;
                    for (RobotDialog robotDialog : dialogList) {
                        RobotSessionComposedTalk composedTalk = new RobotSessionComposedTalk();
                        String fullText = robotDialog.getHandledText();
                        if ((!fullText.contains("$") && fullText.contains("#")) && variableMap != null) {
                            fullText = RobotFormatUtil.replaceTextVariables(fullText, variableMap);
                            log.info("robotTtsAll, fullText:{}, variableMap:{}", fullText, variableMap);
                            TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, fullText);
                            RobotTtsRecord robotTtsRecord = robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
                            log.info("robotTtsAll, ttsAuditionReq:{}, robotTtsRecord:{}", ttsAuditionReq, robotTtsRecord);
                            if (robotTtsRecord == null || robotTtsRecord.getStatus() != TtsStatusEnum.COMPLETED.getCode()) {
                                throw new RobotException(RobotResult.TTS_EXECUTE_FAIL);
                            }
                            composedTalk.setCreateTime(LocalDateTime.now());
                            composedTalk.setRobotHistoryId(robotHistory.getId());
                            composedTalk.setRobotId(robotHistory.getRobotId());
                            composedTalk.setUuid(uuid);
                            composedTalk.setNodeId(String.valueOf(node.getId()));
                            composedTalk.setNodeType(node.getType());
                            composedTalk.setOrderType(RobotVarTypeEnum.CUSTOM.getCode());
                            composedTalk.setVariables("");
                            composedTalk.setDialogText(robotDialog.getHandledText());
                            composedTalk.setFullText(fullText);
                            composedTalk.setTenantId(robotHistory.getTenantId());
                            composedTalk.setAudioPath(robotTtsRecord.getAudioPath());
                            composedTalk.setAudioOrder(order);
                            composedTalk.setUpdateTime(LocalDateTime.now());
                            order++;
                            composedTalks.add(composedTalk);
                        }
                    }
                }
            }
        }
        // 变量去重
        Set<String> variableKeySet = new HashSet<>();
        for (AiRobotDialogNode node : sceneNodeData) {
            if (node.getIsIncludeVar()) {
                String subDialogs = node.getSubDialogs();
                if (Strings.isNullOrEmpty(subDialogs)) {
                    continue;
                }
                RobotDialogDTO dialogDTO = JSON.parseObject(subDialogs, RobotDialogDTO.class);
                if (dialogDTO == null) {
                    continue;
                }
                List<Long> customVarIdList = dialogDTO.getCustomVarIdList();
                if (CollUtil.isNotEmpty(customVarIdList)) {
                    List<RobotAttributeVariable> robotAttributeVariables = robotAttributeVariableService.listByIds(customVarIdList);
                    for (RobotAttributeVariable robotAttributeVariable : robotAttributeVariables) {
                        String variableKey = robotAttributeVariable.getVariableKey();
                        log.debug("初始化节点静态变量, uuid:{}, nodeId:{}, 变量:{}， variableMap:{}, include:{}", uuid, node.getId(), variableKey, variableMap, variableMap.containsKey(variableKey));
                        if (variableKey != null && variableMap.containsKey(variableKey) && !variableKeySet.contains(variableKey)) {
                            String s = (String) variableMap.get(variableKey);
                            TtsAuditionReq ttsAuditionReq = buildTtsAuditionReq(robotHistory, s);
                            RobotTtsRecord robotTtsRecord = robotTtsRecordService.getOrExecuteTts(ttsAuditionReq);
                            log.debug("初始化节点静态变量音频响应, uuid:{}, nodeId:{}, 变量:{}, 变量值:{}, robotTtsRecord={}", uuid, node.getId(), variableKey, s, robotTtsRecord);
                            if (robotTtsRecord == null) {
                                continue;
                            }
                            variableKeySet.add(variableKey);
                            RobotSessionComposedTalk robotSessionComposedTalk = buildStaticComposedTalk(robotHistory.getId(), uuid, String.valueOf(node.getId()), node.getType(), variableKey, s, node.getTenantId(), robotTtsRecord.getAudioPath(), 0, robotHistory.getRobotId(), variableKey);
                            composedTalks.add(robotSessionComposedTalk);
                        }
                    }
                }
            }
        }
        return composedTalks;
    }

    @Override
    public RobotSessionVoiceVO callInStart(RobotTestStartDTO dto) {
        log.info("robot session callInStart, dto:{}", dto);
        RobotSessionVoiceVO chatVO = new RobotSessionVoiceVO();
        try {
            Long robotId = dto.getRobotId();
            LambdaQueryWrapper<RobotHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RobotHistory::getRobotId, robotId).eq(RobotHistory::getStatus, 1).eq(RobotHistory::getIsDeleted, 0).orderByDesc(RobotHistory::getId).last("limit 1");
            RobotHistory robotHistory = robotHistoryMapper.selectOne(queryWrapper);
            Long robotHistoryId = robotHistory.getId();
            List<RobotTestStartDTO.Variable> variables = dto.getVariable();
            Robot robot = robotService.getById(robotId);
            String tenantId = robot.getTenantId();
            String uuid = dto.getUuid();
            String phoneNo = dto.getCallNumber();
            SimpleUserInfoDto customerInfo = tenantServiceHolder.getCustomerInfoWrapper(tenantId).getUserBasicInfoByPhone(phoneNo, tenantId);
            String customerUid = Objects.nonNull(customerInfo) ? customerInfo.getUserId() : String.valueOf(Long.parseLong(phoneNo) * 4);
            RobotSession robotSession = new RobotSession();
            robotSession.setUuid(uuid);
            robotSession.setTaskId(0L);
            robotSession.setCallType(1);
            robotSession.setRobotId(robotId);
            robotSession.setCalledNumber(phoneNo);
            robotSession.setUserId(customerUid);
            robotSession.setCallerDisplayNumber("");
            robotSession.setVariables(variables == null ? "" : variables.toString());
            robotSession.setToneId(robotHistory.getToneId());
            robotSession.setRobotHistoryId(robotHistoryId);
            robotSession.setCallStatus(RobotCallStatus.CALLING.getCode());
            robotSession.setCallRounds(1);
            robotSession.setGatewayId(0);
            robotSession.setCreateTime(LocalDateTime.now());
            robotSession.setUpdateTime(LocalDateTime.now());
            robotSession.setInterruptEnable(robotHistory.getInterruptEnable());
            robotSession.setTenantId(tenantId);
            if (!save(robotSession)) {
                throw new RobotException(RobotResult.SESSION_CREATE_FAILED);
            }
            Map<String, Object> variableMap = new HashMap<>();
            if (variables != null && !variables.isEmpty()) {
                variableMap = variables.stream().filter(v -> v.getName() != null && v.getValue() != null).collect(Collectors.toMap(RobotTestStartDTO.Variable::getName, RobotTestStartDTO.Variable::getValue));
            }
            List<AiRobotDialogNode> robotDialogNodes = aiRobotDialogNodeService.getByHistoryRobotId(robotHistoryId);
            if (robotDialogNodes == null || robotDialogNodes.isEmpty()) {
                throw new RobotException(RobotResult.ROBOT_NODE_NOT_EXIST);
            }
            // 初始化全部节点
            log.info("callInStart 初始化全部节点, robotId:{}, 全部节点数量:{}", robotId, robotDialogNodes.size());
            List<RobotSessionComposedTalk> sessionComposedTalks = buildStaticVariable(variableMap, robotDialogNodes, robotHistory, uuid);
            // 静态变量初始化
            Map<String, Object> varMap = new HashMap<>();
            for (RobotSessionComposedTalk sessionComposedTalk : sessionComposedTalks) {
                if (StringUtils.isNotBlank(sessionComposedTalk.getVariables())) {
                    varMap.put(RobotFormatUtil.formatVarKey(sessionComposedTalk.getVariables(), RobotVarTypeEnum.CUSTOM), sessionComposedTalk.getFullText());
                }
            }
            robotSessionComposedTalkService.saveOrUpdateBatch(sessionComposedTalks);
            AiRobotDialogNode startNode = aiRobotDialogNodeService.getStartNodeByHistoryRobotId(robotHistoryId);
            if (startNode == null) {
                log.error("callInStart robot session node start is null, robotId: {}", robotId);
                throw new RobotException(RobotResult.SESSION_START_POINT_NOT_EXIST);
            }
            log.info("callInStart 初始化节点状态, uuid:{}, nodeId:{}", uuid, startNode.getId());
            String subDialogs = startNode.getSubDialogs();
            RobotDialogDTO dialogDTO = JSON.parseObject(subDialogs, RobotDialogDTO.class);
            List<RobotDialog> dialogList = dialogDTO.getDialogList();
            String nodeId = String.valueOf(startNode.getId());
            RobotDialog robotDialog = dialogList.get(0);
            RobotSessionInfoModel nodeInfoModel = new RobotSessionInfoModel();
            nodeInfoModel.setMaxHistorySize(maxHistorySize);
            nodeInfoModel.setUuid(uuid);
            nodeInfoModel.setNodeId(nodeId);
            nodeInfoModel.setTenantId(tenantId);
            nodeInfoModel.setRobotHistoryId(robotHistoryId);
            nodeInfoModel.setRobotId(robotId);
            nodeInfoModel.setNodeType(startNode.getType());
            nodeInfoModel.setHistory(new ArrayList<>());
            nodeInfoModel.setVarMap(varMap);
            String fullText = robotDialog.getHandledText();
            chatVO.setTtsUrl(robotDialog.getAudio());
            // 获取完整话术和音频
            if (robotTtsAll) {
                chatVO.setTtsUrl(robotDialog.getAudio());
                if (!fullText.contains("$") && fullText.contains("#")) {
                    RobotSessionComposedTalk robotSessionComposedTalk = robotSessionComposedTalkService.getByNodeIdAndOrder(uuid, String.valueOf(startNode.getId()), 0);
                    chatVO.setTtsUrl(robotSessionComposedTalk.getAudioPath());
                    chatVO.setChat(robotSessionComposedTalk.getFullText());
                    fullText = robotSessionComposedTalk.getFullText();
                } else {
                    getFullTtsFormNode(robotDialog, startNode, varMap, uuid, chatVO);
                    fullText = chatVO.getChat();
                }
            } else {
                getFullTtsFormNode(robotDialog, startNode, varMap, uuid, chatVO);
                fullText = chatVO.getChat();
            }
            nodeInfoModel.setQuestion(fullText);
            nodeInfoModel.setMainQuestion(fullText);
            nodeInfoModel.setRound(0);
            
            // 信息采集（开始节点 - callInStart）
            executeInfoCollection(startNode, nodeInfoModel, "", uuid);
            
            action(startNode, nodeInfoModel, uuid);
            jedisCluster.setex(RobotConstant.ROBOT_SESSION_KEY + uuid, RobotConstant.ROBOT_SESSION_EXPIRE_TIME, JSON.toJSONString(nodeInfoModel));
            if (robotSessionNodeStateService.getNodeState(uuid) == null) {
                robotSessionNodeStateService.initNodeState(uuid, robotSession.getRobotHistoryId(), robotSession.getTenantId(), nodeId, fullText);
            }
            chatVO.setUuid(uuid);
            chatVO.setRound(0);
            chatVO.setAction(RobotNodeType.START.getCode());
            return chatVO;
        } catch (Exception e) {
            log.error("callInStart error, uuid:{}", dto.getUuid(), e);
            webHookUtil.weChatMsgDev(MsgTypeEnum.AI_ROBOT_CALL_IN_ERROR, "uuid=" + dto.getUuid() + " nodeId=" + 0 + " nextNodeId=" + 0 + " error:" + e);
            chatVO.setTtsUrl(robotDefaultEndAudio);
            chatVO.setAction(RobotChatFSAction.HANG_UP.getValue());
            chatVO.setChat(robotDefaultEndSpeechText);
            return chatVO;
        }
    }


    public RobotSessionComposedTalk buildStaticComposedTalk(Long robotHistoryId, String uuid, String nodeId, Integer nodeType, String dialogText, String fullText, String tenantId, String audioPath, Integer order, Long robotId, String variables) {
        RobotSessionComposedTalk composedTalk = robotSessionComposedTalkService.getByUuidAndNodeIdAndOrder(uuid, variables, RobotVarTypeEnum.CUSTOM);
        if (composedTalk == null) {
            composedTalk = new RobotSessionComposedTalk();
            composedTalk.setCreateTime(LocalDateTime.now());
        }
        composedTalk.setRobotHistoryId(robotHistoryId);
        composedTalk.setRobotId(robotId);
        composedTalk.setUuid(uuid);
        composedTalk.setNodeId(nodeId);
        composedTalk.setNodeType(nodeType);
        composedTalk.setOrderType(RobotVarTypeEnum.CUSTOM.getCode());
        composedTalk.setVariables(variables);
        composedTalk.setDialogText(dialogText);
        composedTalk.setFullText(fullText);
        composedTalk.setTenantId(tenantId);
        composedTalk.setAudioPath(audioPath);
        composedTalk.setAudioOrder(order);
        composedTalk.setUpdateTime(LocalDateTime.now());
        return composedTalk;
    }

    public CallServiceResponse<String> makeCall(RobotCallRequest callRequest) {
        try {
            String params = JsonUtils.beanToJson(callRequest);
            String url = yxhCallServiceEndPoint + "/api/v1/callCenter/ivbotCall";
            log.info("发起呼叫, url:{} param：{}", url, params);
            ResponseEntity<String> response = postJson(url, params);
            String respBody = response.getBody();
            log.info("发起呼叫返回结果：{}", respBody);
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("发起呼叫返回码异常， {}, {}", response.getStatusCode(), response.getBody());
                return CallServiceResponse.fail("发起呼叫请求异常");
            }
            return JsonUtilExt.jsonToBean(respBody, new TypeReference<CallServiceResponse<String>>() {

            });
        } catch (Exception e) {
            log.error("发起呼叫异常", e);
        }
        return CallServiceResponse.fail("发起呼叫异常");
    }

    private ResponseEntity<String> postJson(String url, String body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        return restTemplate.postForEntity(url, request, String.class);
    }

    @Override
    public RobotSessionQueryResultVO querySessionRecords(RobotSessionQueryReq req) {
        log.info("查询机器人会话记录: {}", req);
        req.setCallStatus(RobotCallStatus.CALLED.getCode());
        req.setTenantId(OwnAuthUtil.getTenantId());
//        req.setTenantId("928849");
        
        // 创建分页对象
        Page<RobotSessionRecordVO> page = new Page<>(req.getCurrent(), req.getSize());
        
        // 执行分页查询
        IPage<RobotSessionRecordVO> result = baseMapper.selectSessionRecordPage(page, req);
        
        // 如果有记录，则查询小结信息
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            // 提取所有uuid
            List<String> uuids = result.getRecords().stream()
                    .map(RobotSessionRecordVO::getUuid)
                    .collect(Collectors.toList());
            
            // 批量查询小结信息
            List<RobotSessionSummary> summaries = baseMapper.selectSummaryByUuids(uuids);
            
            // 构建uuid到小结的映射
            Map<String, RobotSessionSummary> summaryMap = summaries.stream()
                    .collect(Collectors.toMap(RobotSessionSummary::getUuid, summary -> summary));
            
            // 组装数据
            result.getRecords().forEach(record -> {
                // 处理小结信息
                RobotSessionSummary summary = summaryMap.get(record.getUuid());
                if (summary != null) {
                    record.setSummaryContentRobot(summary.getSummaryContentRobot());
                    record.setSummaryContentUser(summary.getSummaryContentUser());
                    record.setFeedbackStatus(summary.getFeedbackStatus());
                }
                
                // 处理callText切分
                if (record.getCallText() != null && !record.getCallText().trim().isEmpty()) {
                    record.setCallTextList(Arrays.asList(record.getCallText().split("\\\\n")));
                } else {
                    record.setCallTextList(new ArrayList<>());
                }
            });
        }
        
        // 构建返回结果
        RobotSessionQueryResultVO queryResult = new RobotSessionQueryResultVO();
        queryResult.setRecords(result.getRecords());
        queryResult.setTotal(result.getTotal());
        queryResult.setCurrent(result.getCurrent());
        queryResult.setSize(result.getSize());
        queryResult.setPages(result.getPages());
        
        log.info("查询机器人会话记录完成，总记录数: {}", result.getTotal());
        return queryResult;
    }

    /**
     * 处理URL中的变量替换
     * $变量中文名$：动态变量（通过中文名查找英文key，再从varMap中获取）
     * #变量中文名#：静态变量（通过中文名查找英文key，再从varMap中获取）
     */
    private String processUrlVariables(String url, Map<String, Object> varMap, String tenantId) {
        if (Strings.isNullOrEmpty(url)) {
            return url;
        }

        String processedUrl = url;

        try {
            // 处理动态变量 $变量中文名$
            Pattern dynamicPattern = Pattern.compile("\\$([^$]+)\\$");
            Matcher dynamicMatcher = dynamicPattern.matcher(processedUrl);
            while (dynamicMatcher.find()) {
                String variableChineseName = dynamicMatcher.group(1);
                
                // 通过中文名查找对应的英文variableKey
                LambdaQueryWrapper<TenantAttributeVariable> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TenantAttributeVariable::getVariableName, variableChineseName)
                           .eq(TenantAttributeVariable::getTenantId, tenantId)
                           .eq(TenantAttributeVariable::getIsDeleted, 0)
                           .last("limit 1");
                
                TenantAttributeVariable variable = tenantAttributeVariableService.getOne(queryWrapper);
                String replacement = "";
                if (variable != null) {
                    String variableKey = RobotFormatUtil.formatVarKey(variable.getVariableKey(), RobotVarTypeEnum.DYNAMIC);
                    Object value = varMap.get(variableKey);
                    replacement = value != null ? String.valueOf(value) : "";
                }
                
                processedUrl = processedUrl.replace("$" + variableChineseName + "$", replacement);
                log.info("Replaced dynamic variable: ${}$ -> {}", variableChineseName, replacement);
            }

            // 处理静态变量 #变量中文名#
            Pattern staticPattern = Pattern.compile("#([^#]+)#");
            Matcher staticMatcher = staticPattern.matcher(processedUrl);
            while (staticMatcher.find()) {
                String variableChineseName = staticMatcher.group(1);
                
                // 通过中文名查找对应的英文variableKey
                LambdaQueryWrapper<RobotAttributeVariable> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(RobotAttributeVariable::getVariableName, variableChineseName)
                           .eq(RobotAttributeVariable::getTenantId, tenantId)
                           .eq(RobotAttributeVariable::getIsDeleted, 0)
                           .last("limit 1");
                
                RobotAttributeVariable variable = robotAttributeVariableService.getOne(queryWrapper);
                String replacement = "";
                if (variable != null) {
                    String variableKey = RobotFormatUtil.formatVarKey(variable.getVariableKey(), RobotVarTypeEnum.CUSTOM);
                    Object value = varMap.get(variableKey);
                    replacement = value != null ? String.valueOf(value) : "";
                }
                
                processedUrl = processedUrl.replace("#" + variableChineseName + "#", replacement);
                log.info("Replaced static variable: #{}# -> {}", variableChineseName, replacement);
            }

        } catch (Exception e) {
            log.error("Error processing URL variables: url={}, tenantId={}", url, tenantId, e);
            return url; // 出错时返回原URL
        }

        return processedUrl;
    }

    @Override
    public Boolean feedback(String uuid, int accurate) {
        RobotSessionSummary sessionSummary = robotSessionSummaryService.getByUuid(uuid);
        sessionSummary.setFeedbackStatus(accurate);
        sessionSummary.setUpdateTime(LocalDateTime.now());
        return robotSessionSummaryService.updateById(sessionSummary);
    }
}
