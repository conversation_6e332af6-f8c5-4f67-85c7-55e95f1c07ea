package com.yirendai.robot.modules.call.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRecord;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRule;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRuleRobot;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRecordMapper;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRuleMapper;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRuleRobotMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.YearMonth;

/**
 * 频次限制监控服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RobotCallFrequencyMonitorService {

    private final RobotCallFrequencyRuleMapper ruleMapper;
    private final RobotCallFrequencyRecordMapper recordMapper;
    private final RobotCallFrequencyRuleRobotMapper ruleRobotMapper;

    /**
     * 获取当前自然月的开始时间
     */
    private LocalDateTime getCurrentMonthStart() {
        YearMonth currentMonth = YearMonth.now();
        return currentMonth.atDay(1).atStartOfDay();
    }

    /**
     * 获取当前自然月的结束时间
     */
    private LocalDateTime getCurrentMonthEnd() {
        YearMonth currentMonth = YearMonth.now();
        return currentMonth.atEndOfMonth().atTime(23, 59, 59);
    }

    public RobotCallFrequencyRule getActiveRule(Long robotId) {
        if (robotId == null) {
            return null;
        }
        RobotCallFrequencyRuleRobot relation = ruleRobotMapper.selectRobotCallFrequencyByRobotId(robotId);
        if (relation == null) {
            return null;
        }
        RobotCallFrequencyRule rule = ruleMapper.selectById(relation.getRuleId());
        if (rule == null || !Integer.valueOf(1).equals(rule.getStatus())) {
            return null;
        }
        return rule;
    }

    public boolean isBlocked(Long robotId, String phone) {
        if (robotId == null || StringUtils.isBlank(phone)) {
            return false;
        }
        RobotCallFrequencyRule rule = getActiveRule(robotId);
        if (rule == null || rule.getLimitCount() == null || rule.getLimitCount() <= 0) {
            return false;
        }

        // 第一步：查询规则下的所有机器人ID
        java.util.List<Long> robotIds = ruleRobotMapper.selectRobotIdsByRuleId(rule.getId());
        if (robotIds == null || robotIds.isEmpty()) {
            return false;
        }

        // 第二步：统计这些机器人在当前自然月内对该手机号的接通次数
        int count = recordMapper.countByRobotIdsAndPhoneInPeriod(
                robotIds,
                phone,
                getCurrentMonthStart(),
                getCurrentMonthEnd()
        );

        // 检查接通次数是否达到限制
        boolean blocked = count >= rule.getLimitCount();
        if (blocked) {
            log.info("手机号已达到频次限制 robotId={} phone={} ruleId={} robotCount={} currentCount={} limit={}",
                    robotId, phone, rule.getId(), robotIds.size(), count, rule.getLimitCount());
        }
        return blocked;
    }

    @Transactional(rollbackFor = Exception.class)
    public void increaseOnSuccess(Long robotId, String phone, LocalDateTime connectTime, String tenantId) {
        if (robotId == null || StringUtils.isBlank(phone)) {
            return;
        }

        // 无论是否有频次规则，只要接通就插入记录
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime actualConnectTime = connectTime == null ? now : connectTime;

        RobotCallFrequencyRecord record = new RobotCallFrequencyRecord();
        record.setRobotId(robotId);
        record.setPhone(phone);
        record.setConnectTime(actualConnectTime);
        record.setTenantId(tenantId);
        record.setCreateTime(now);

        recordMapper.insert(record);
        log.info("记录接通日志 robotId={} phone={} connectTime={}", robotId, phone, actualConnectTime);
    }

    @Transactional(rollbackFor = Exception.class)
    public void clearRecords(Long ruleId) {
        if (ruleId == null) {
            return;
        }
        // 查询规则下的所有机器人ID
        java.util.List<Long> robotIds = ruleRobotMapper.selectRobotIdsByRuleId(ruleId);
        if (robotIds == null || robotIds.isEmpty()) {
            return;
        }
        // 删除这些机器人的所有记录
        LambdaQueryWrapper<RobotCallFrequencyRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RobotCallFrequencyRecord::getRobotId, robotIds);
        int count = recordMapper.delete(wrapper);
        log.info("清理规则下机器人的接通记录 ruleId={} robotCount={} recordCount={}", ruleId, robotIds.size(), count);
    }
}
