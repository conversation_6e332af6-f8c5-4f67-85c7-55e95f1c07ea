package com.yirendai.robot.modules.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyOperation;
import com.yirendai.robot.modules.call.vo.RobotCallFrequencyOperationVO;
import org.apache.ibatis.annotations.Param;

/**
 * 频次规则操作记录 Mapper
 */
public interface RobotCallFrequencyOperationMapper extends BaseMapper<RobotCallFrequencyOperation> {

    IPage<RobotCallFrequencyOperationVO> pageRobotFrequencyByRule(@Param("page") IPage<RobotCallFrequencyOperationVO> page,
                                                                  @Param("ruleId") Long ruleId);
}
