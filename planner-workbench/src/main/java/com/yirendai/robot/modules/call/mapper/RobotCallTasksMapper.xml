<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.robot.modules.call.mapper.RobotCallTasksMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.robot.modules.call.entity.RobotCallTasks">
        <id column="id" property="id" />
        <result column="task_name" property="taskName" />
        <result column="robot_id" property="robotId" />
        <result column="robot_version" property="robotVersion" />
        <result column="priority" property="priority" />
        <result column="max_concurrency" property="maxConcurrency" />
        <result column="validity_type" property="validityType" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="call_time_start" property="callTimeStart" />
        <result column="call_time_end" property="callTimeEnd" />
        <result column="task_status" property="taskStatus" />
        <result column="create_time" property="createTime" />
        <result column="created_by" property="createdBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="start_execution_time" property="startExecutionTime" />
        <result column="started_by" property="startedBy" />
        <result column="is_deleted" property="isDeleted" />
        <result column="task_code" property="taskCode" />
        <result column="tenant_id" property="tenantId" />
        <result column="completion_reason" property="completionReason" />
        <result column="completion_time" property="completionTime" />
        <result column="running_status" property="runningStatus" />
        <result column="call_time_week" property="callTimeWeek" />
        <result column="task_no" property="taskNo" />
    </resultMap>
    <resultMap id="BaseResultVO" type="com.yirendai.robot.modules.call.vo.RobotCallTasksVO">
        <id column="id" property="id" />
        <result column="task_name" property="taskName" />
        <result column="robot_id" property="robotId" />
        <result column="robot_version" property="robotVersion" />
        <result column="priority" property="priority" />
        <result column="max_concurrency" property="maxConcurrency" />
        <result column="validity_type" property="validityType" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="call_time_start" property="callTimeStart" />
        <result column="call_time_end" property="callTimeEnd" />
        <result column="task_status" property="taskStatus" />
        <result column="create_time" property="createTime" />
        <result column="created_by" property="createdBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="start_execution_time" property="startExecutionTime" />
        <result column="started_by" property="startedBy" />
        <result column="is_deleted" property="isDeleted" />
        <result column="task_code" property="taskCode" />
        <result column="tenant_id" property="tenantId" />
        <result column="completion_reason" property="completionReason" />
        <result column="completion_time" property="completionTime" />
        <result column="running_status" property="runningStatus" />
        <result column="task_no" property="taskNo" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_name, robot_id,robot_version, priority, max_concurrency, validity_type, start_date, end_date, call_time_start, call_time_end, task_status, created_time, created_by, update_time, update_by, start_execution_time, started_by, is_deleted, task_code, tenant_id
            , completion_reason, completion_time, running_status
    </sql>

    <update id="updateRobotVersion">
        update ai_robot_call_tasks set robot_version = #{robotVersion} where robot_id = #{robotId} and is_deleted = 0
    </update>
    <update id="updateRunningStatus">
        update ai_robot_call_tasks set running_status = #{runningStatus} where id = #{taskId} and is_deleted = 0
    </update>
    <update id="updateByIdSelf">
        update ai_robot_call_tasks
        <set>
            <if test="req.taskName != null and req.taskName != ''">
                task_name = #{req.taskName},
            </if>
            <if test="req.robotId != null">
                robot_id = #{req.robotId},
            </if>
            <if test="req.robotVersion != null">
                robot_version = #{req.robotVersion},
            </if>
            <if test="req.priority != null">
                priority = #{req.priority},
            </if>
            <if test="req.maxConcurrency != null">
                max_concurrency = #{req.maxConcurrency},
            </if>
            <if test="req.validityType != null">
                validity_type = #{req.validityType},
                <if test="req.validityType==1">
                    end_date = null,
                </if>
            </if>
            <if test="req.startDate != null">
                start_date = #{req.startDate},
            </if>
            <if test="req.endDate != null and req.validityType==0">
                end_date = #{req.endDate},
            </if>
            <if test="req.callTimeStart != null">
                call_time_start = #{req.callTimeStart},
            </if>
            <if test="req.callTimeEnd != null">
                call_time_end = #{req.callTimeEnd},
            </if>
            <if test="req.taskStatus != null">
                task_status = #{req.taskStatus},
            </if>
            <if test="req.createTime != null">
                create_time = #{req.createTime},
            </if>
            <if test="req.createdBy != null">
                created_by = #{req.createdBy},
            </if>
            <if test="req.updateTime != null">
                update_time = #{req.updateTime},
            </if>
            <if test="req.updateBy != null">
                update_by = #{req.updateBy},
            </if>
            <if test="req.startExecutionTime != null">
                start_execution_time = #{req.startExecutionTime},
            </if>
            <if test="req.startedBy != null">
                started_by = #{req.startedBy},
            </if>
            <if test="req.isDeleted != null">
                is_deleted = #{req.isDeleted},
            </if>
            <if test="req.taskCode != null">
                task_code = #{req.taskCode},
            </if>
            <if test="req.tenantId != null">
                tenant_id = #{req.tenantId},
            </if>
            <if test="req.completionReason != null">
                completion_reason = #{req.completionReason},
            </if>
            <if test="req.completionTime != null">
                completion_time = #{req.completionTime},
            </if>
            <if test="req.runningStatus != null">
                running_status = #{req.runningStatus},
            </if>
            <if test="req.callTimeWeek != null">
                call_time_week = #{req.callTimeWeek},
            </if>
            <if test="req.callTimeStart == null">
                call_time_start = null,
            </if>
            <if test="req.callTimeEnd == null">
                call_time_end = null,
            </if>
            <if test="req.taskNo != null">
                task_no = #{req.taskNo},
            </if>
            <if test="req.isReplayEnabled != null">
                is_replay_enabled = #{req.isReplayEnabled},
            </if>
            <if test="req.replayConditions != null and req.replayConditions !=''">
                replay_conditions = #{req.replayConditions},
            </if>
            <if test="req.replayIntervalMinutes != null  ">
                replay_interval_minutes = #{req.replayIntervalMinutes},
            </if>
            <if test="req.maxReplayTimes != null">
                max_replay_times = #{req.maxReplayTimes},
            </if>

            <if test="req.replayConditions ==null ">
                replay_conditions = null,
            </if>
            <if test="req.replayIntervalMinutes == null  ">
                replay_interval_minutes = null,
            </if>
            <if test="req.maxReplayTimes == null">
                max_replay_times = null,
            </if>
        </set>
        where id = #{req.id}
    </update>

    <select id="selectRobotCount" resultType="java.lang.Integer">
        select count(*) from ai_robot_call_tasks where robot_id = #{robotId} and is_deleted = 0
    </select>
    <select id="selectByIdAndTenantId" resultType="com.yirendai.robot.modules.call.entity.RobotCallTasks">
        select *
        from ai_robot_call_tasks
        where id = #{id}
          <if test="tenantId != null and tenantId != ''">
              and tenant_id = #{tenantId}
          </if>
          and is_deleted = 0
    </select>
    <select id="pageList"  resultMap="BaseResultVO">
        select
            *
        from ai_robot_call_tasks
        <where>
            <if test="req.tenantId != null and req.tenantId != ''">
                and tenant_id = #{req.tenantId}
            </if>
            <if test="req.taskName != null and req.taskName != ''">
                and task_name like concat('%', #{req.taskName}, '%')
            </if>
            <if test="req.robotId != null and req.robotId.size() > 0">
                and robot_id in (
                    <foreach collection="req.robotId" item="robotId" separator=",">
                        #{robotId}
                    </foreach>
                )
            </if>
            <if test="req.priority != null and req.priority.size() > 0">
                and priority in (
                    <foreach collection="req.priority" item="priority" separator=",">
                        #{priority}
                    </foreach>
                )
            </if>
            <if test="req.taskStatusList != null and req.taskStatusList.size() > 0">
                and task_status in (
                    <foreach collection="req.taskStatusList" item="taskStatus" separator=",">
                        #{taskStatus}
                    </foreach>
                )
            </if>
            <if test="req.taskStatus != null">
                and task_status = #{req.taskStatus}
            </if>
            <if test="req.createTimeBeg != null">
                and create_time >= #{req.createTimeBeg}
            </if>
            <if test="req.createTimeEnd != null">
                and create_time &lt;= #{req.createTimeEnd}
            </if>
            and is_deleted = 0
        </where>
        order by create_time desc
    </select>
    <select id="getEnableCallTasks" resultMap="BaseResultMap">
        select task.*
        from ai_robot_call_tasks task
        where task.task_status = 1
          and task.is_deleted = 0
          and task.tenant_id = #{tenantId}
        order by task.priority desc, task.start_date asc , task.call_time_start asc
    </select>
    <select id="getStatus" resultType="java.lang.Integer">
        select task_status
        from ai_robot_call_tasks
        where id = #{taskId}
          and is_deleted = 0
    </select>
    <select id="selectCountByTaskName" resultType="java.lang.Integer">
        select count(*)
        from ai_robot_call_tasks
        where task_name = #{taskName}
          and tenant_id = #{tenantId}
          and is_deleted = 0
          <if test="id != null">
              and id != #{id}
          </if>
    </select>
    <select id="selectTaskList" resultType="com.yirendai.robot.modules.call.vo.RobotCallTasksSelectVO">
        select id, task_name
        from ai_robot_call_tasks
        where tenant_id = #{tenantId}
          and is_deleted = 0
        and task_status in (0, 1,2)
        order by id desc
    </select>
    <select id="getByTaskNo" resultType="com.yirendai.robot.modules.call.entity.RobotCallTasks">
        select *
        from ai_robot_call_tasks
        where task_no = #{taskNo}
          and tenant_id = #{tenantId}
          and is_deleted = 0
        order by update_time desc
        limit 1
    </select>
</mapper>
