package com.yirendai.robot.modules.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 机器人外呼接通记录表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_robot_call_frequency_record")
@ApiModel(value = "RobotCallFrequencyRecord", description = "机器人外呼接通记录表")
public class RobotCallFrequencyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "机器人ID")
    private Long robotId;

    @ApiModelProperty(value = "被叫号码")
    private String phone;

    @ApiModelProperty(value = "接通时间")
    private LocalDateTime connectTime;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
