package com.yirendai.robot.modules.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 机器人外呼频次限制规则
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_robot_call_frequency_rule")
@ApiModel(value = "RobotCallFrequencyRule", description = "机器人外呼频次限制规则")
public class RobotCallFrequencyRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "接通次数限制")
    private Integer limitCount;

    @ApiModelProperty(value = "规则状态 1-启用 0-停用")
    private Integer status;

    @ApiModelProperty(value = "启用时间")
    private LocalDateTime enabledTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    private Long updateUser;

    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
