package com.yirendai.robot.modules.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 频次限制规则与机器人关联
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_robot_call_frequency_rule_robot")
@ApiModel(value = "RobotCallFrequencyRuleRobot", description = "频次限制规则与机器人关联")
public class RobotCallFrequencyRuleRobot implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "规则ID")
    private Long ruleId;

    @ApiModelProperty(value = "机器人ID")
    private Long robotId;

    @ApiModelProperty(value = "机器人名称")
    private String robotName;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "关联时间")
    private LocalDateTime createTime;
}
