package com.yirendai.robot.modules.call.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.robot.modules.call.service.IRobotCallFrequencyRuleService;
import com.yirendai.robot.modules.call.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 机器人外呼频次规则控制器
 */
@RestController
@RequestMapping("/ai/robot/frequency/rule")
@Api(value = "机器人外呼频次规则", tags = "机器人外呼频次规则")
@RequiredArgsConstructor
@Validated
public class RobotCallFrequencyRuleController {

    private final IRobotCallFrequencyRuleService frequencyRuleService;

    @PostMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页查询频次规则", notes = "分页查询频次规则")
    public R<IPage<RobotCallFrequencyRulePageVO>> page(@RequestBody RobotCallFrequencyRuleQueryReq req) {
        return R.data(frequencyRuleService.page(req));
    }

    @PostMapping("/create")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "新建频次规则", notes = "新建频次规则")
    public R<RobotCallFrequencyRuleOperateResp> create(@Valid @RequestBody RobotCallFrequencyRuleCreateReq req) {
        return frequencyRuleService.create(req);
    }

    @PostMapping("/update")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "编辑频次规则", notes = "编辑频次规则")
    public R<RobotCallFrequencyRuleOperateResp> update(@Valid @RequestBody RobotCallFrequencyRuleUpdateReq req) {
        return frequencyRuleService.updateRule(req);
    }

    @PostMapping("/status")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "启用/停用规则", notes = "启用/停用规则")
    public R<Void> changeStatus(@Valid @RequestBody RobotCallFrequencyRuleStatusReq req) {
        return frequencyRuleService.changeStatus(req);
    }

    @PostMapping("/operation/page")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "规则操作记录", notes = "规则操作记录")
    public R<IPage<RobotCallFrequencyOperationVO>> operationPage(@NotNull(message = "规则ID不能为空") @RequestParam Long ruleId,
                                                                  Query query) {
        return R.data(frequencyRuleService.operationPage(ruleId, query));
    }

    @GetMapping("/robots")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "已发布机器人列表", notes = "已发布机器人列表")
    public R<List<RobotOptionVO>> robotOptions(@RequestParam(required = false) String keyword) {
        return R.data(frequencyRuleService.publishedRobotOptions(keyword));
    }
}
