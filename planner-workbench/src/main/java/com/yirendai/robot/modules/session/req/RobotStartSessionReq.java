package com.yirendai.robot.modules.session.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RobotStartSessionReq {
    @ApiModelProperty(value = "任务ID")
    private Long taskId;
    @ApiModelProperty(value = "被叫号码（机器人要拨打的)")
    private String calledNumber;
    @ApiModelProperty(value = "主叫号码（客户侧显示的号码）")
    private String callerDisplayNumber;
    @ApiModelProperty(value = "替换变量（格式：[变量名称]:{变量赋值内容}，多个变量换行显示）")
    private String variables;
    @ApiModelProperty(value = "重播次数")
    private Integer callRounds;
    @ApiModelProperty(value = "线路id")
    private Integer gatewayId;
    @ApiModelProperty(value = "机器人版本ID")
    private Long robotHistoryId;
    @ApiModelProperty(value = "是否测试 0否1是")
    private Integer isTest;
    @ApiModelProperty(value = "呼叫用户ID")
    private Long callTasksCustomerId;
}
