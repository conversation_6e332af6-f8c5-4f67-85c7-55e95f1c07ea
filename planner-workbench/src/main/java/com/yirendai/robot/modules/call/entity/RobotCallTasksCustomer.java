package com.yirendai.robot.modules.call.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 外呼通话任务客户号码导入表
 * </p>
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_robot_call_tasks_customer")
@ApiModel(value = "RobotCallTasksCustomer对象", description = "外呼通话任务客户号码导入表")
public class RobotCallTasksCustomer implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "被叫号码")
    private String calledNumber;

    @ApiModelProperty(value = "变量列表与值，以约定格式存储")
    private String variables;

    @ApiModelProperty(value = "导入时间")
    private LocalDateTime importTime;

    @ApiModelProperty(value = "导入用户")
    private String importedBy;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "任务ID，关联到ai_outbound_call_tasks表")
    private Long taskId;

    @ApiModelProperty(value = "通话次数包括首次和重播的次数")
    private Integer callRounds;

    @ApiModelProperty(value = "最后一次通话ID")
    private Long lastSessionId;

    @ApiModelProperty(value = "是否测试 0否1是")
    private Integer isTest;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 业务的唯一ID,为了校验重复，和重复推送唯一检验使用
     */
    @ApiModelProperty("业务的唯一ID,为了校验重复，和重复推送唯一检验使用")
    private String bizUniqueId;

    @ApiModelProperty(value = "重播状态 0 允许重播，1禁止重播")
    private Integer dupCallStatus;
}
