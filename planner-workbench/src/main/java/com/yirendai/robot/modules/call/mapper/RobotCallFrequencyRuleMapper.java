package com.yirendai.robot.modules.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRule;
import com.yirendai.robot.modules.call.vo.RobotCallFrequencyRulePageVO;
import com.yirendai.robot.modules.call.vo.RobotCallFrequencyRuleQueryReq;
import org.apache.ibatis.annotations.Param;

/**
 * 频次限制规则 Mapper
 */
public interface RobotCallFrequencyRuleMapper extends BaseMapper<RobotCallFrequencyRule> {

    IPage<RobotCallFrequencyRulePageVO> pageRuleResultList(@Param("page") IPage<RobotCallFrequencyRulePageVO> page,
                                                           @Param("req") RobotCallFrequencyRuleQueryReq req);
}
