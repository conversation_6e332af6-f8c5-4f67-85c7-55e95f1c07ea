package com.yirendai.robot.modules.call.biz.pool.constant;

import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.IResultCode;
@AllArgsConstructor
public enum CallResultCode implements IResultCode {
    /**
     * 线路计数器已经达到最大值
     */
    CALL_GATEWAY_LIMIT_EXCEEDED(5001, "线路计数器已经达到最大值"),
    /**
     * 任务计数器已经达到最大值
     */
    PHONE_ENCRYPTION_LIMIT_EXCEEDED(5002, "任务计数器已经达到最大值"),
    /**
     * 任务计数器已经达到最大值
     */
    CALL_ATTEMPT_FAILED(5003, "租户计数器已经达到最大值"),
    FREQUENCY_LIMIT_BLOCKED(5004, "号码已触发外呼频次限制"),
    ;
    final int code;

    final String message;


    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public int getCode() {
        return code;
    }
}
