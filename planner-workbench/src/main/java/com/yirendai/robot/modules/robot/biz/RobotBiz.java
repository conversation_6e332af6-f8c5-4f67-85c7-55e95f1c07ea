package com.yirendai.robot.modules.robot.biz;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;

import com.yirendai.robot.constant.RobotConstant;
import com.yirendai.robot.enums.RobotAttributeIntentionLevelEnum;
import com.yirendai.robot.enums.RobotAttributeTypeEnum;
import com.yirendai.robot.enums.RobotIntentType;
import com.yirendai.robot.enums.YesOrNoEnum;
import com.yirendai.robot.exception.RobotException;
import com.yirendai.robot.modules.attribute.dto.RobotAttributeVariableDTO;
import com.yirendai.robot.modules.attribute.dto.RobotPositionDTO;
import com.yirendai.robot.modules.attribute.entity.RobotAttributeGlobalIntention;
import com.yirendai.robot.modules.attribute.entity.RobotAttributeGlobalVariable;
import com.yirendai.robot.modules.attribute.entity.RobotAttributeIntention;
import com.yirendai.robot.modules.robot.entity.RobotAttributeVariable;
import com.yirendai.robot.modules.attribute.mapper.RobotAttributeGlobalIntentionMapper;
import com.yirendai.robot.modules.attribute.mapper.RobotAttributeGlobalVariableMapper;
import com.yirendai.robot.modules.attribute.mapper.RobotAttributeIntentionMapper;
import com.yirendai.robot.modules.attribute.mapper.RobotAttributeVariableMapper;
import com.yirendai.robot.modules.attribute.service.IRobotAttributeVariableService;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRule;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRuleMapper;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRuleRobotMapper;
import com.yirendai.robot.modules.call.mapper.RobotCallTasksMapper;
import com.yirendai.robot.modules.knowledge.entity.RobotKnowledgeRela;
import com.yirendai.robot.modules.knowledge.mapper.RobotKnowledgeRelaMapper;
import com.yirendai.robot.modules.robot.dto.RobotConfigDTO;
import com.yirendai.robot.modules.robot.entity.Robot;
import com.yirendai.robot.modules.robot.entity.RobotConfig;
import com.yirendai.robot.modules.robot.entity.RobotHistory;
import com.yirendai.robot.modules.robot.enums.RobotRecordingTypeEnum;
import com.yirendai.robot.modules.robot.enums.RobotStatusEnum;
import com.yirendai.robot.modules.robot.mapper.RobotConfigMapper;
import com.yirendai.robot.modules.robot.mapper.RobotHistoryMapper;
import com.yirendai.robot.modules.robot.mapper.RobotMapper;
import com.yirendai.robot.modules.robot.service.AiRobotDialogNodeService;
import com.yirendai.robot.modules.robot.service.IRobotConfigService;
import com.yirendai.robot.modules.robot.service.IRobotService;
import com.yirendai.robot.modules.session.service.IRobotSessionService;
import com.yirendai.voiceaiserver.config.AiServerException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
public class RobotBiz {

    @Resource
    RobotMapper robotMapper;

    @Resource
    IRobotService robotService;

    @Resource
    RobotHistoryMapper robotHistoryMapper;

    @Resource
    RedissonClient redissonClient;

    @Resource
    RobotAttributeGlobalIntentionMapper robotAttributeGlobalIntentionMapper;

    @Resource
    RobotAttributeIntentionMapper robotAttributeIntentionMapper;

    @Resource
    RobotConfigMapper robotConfigMapper;

    @Resource
    RobotAttributeVariableMapper robotAttributeVariableMapper;

    @Resource
    RobotCallTasksMapper robotCallTasksMapper;

    @Resource
    IRobotConfigService robotConfigService;

    @Resource
    IRobotAttributeVariableService robotAttributeVariableService;

    @Resource
    IRobotSessionService robotSessionService;

    @Resource
    AiRobotDialogNodeService aiRobotDialogNodeService;

    @Resource
    RobotAttributeGlobalVariableMapper robotAttributeGlobalVariableMapper;

    @Resource
    RobotKnowledgeRelaMapper robotKnowledgeRelaMapper;

    @Resource
    RobotCallFrequencyRuleRobotMapper robotCallFrequencyRuleRobotMapper;

    @Resource
    RobotCallFrequencyRuleMapper robotCallFrequencyRuleMapper;

    public Long saveRobot(Robot robot, BladeUser user) {
        checkBasic(robot);

        // 加载企业全局意向配置
        List<RobotAttributeIntention> robotAttributeIntentionList = getIntentionList(user.getTenantId());
        List<RobotAttributeVariable> variables = getGlobalVariableList(user.getTenantId());
        RLock lock = null;
        try {
            lock = redissonClient.getLock(String.format(RobotConstant.ROBOT_OPERATE_LOCK, user.getTenantId()));
            boolean lockFlag = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new AiServerException(com.yirendai.voiceaiserver.common.ResultCode.HANDING);
            }

            robot.setTenantId(user.getTenantId());
            int nameCount = robotMapper.selectByRobotName(robot);
            if (nameCount > 0) {
                throw new RobotException("名称已存在");
            }

            robot.setVersion(RobotConstant.INIT_VERSION);
            robot.setStatus(RobotStatusEnum.NOT_PUBLISH.getValue());
            robot.setCreateTime(LocalDateTime.now());
            robot.setCreatedBy(user.getUserId());
            robot.setTenantId(user.getTenantId());
            RobotHistory robotHistory = new RobotHistory();
            BeanUtil.copyProperties(robot, robotHistory);
            robot.setUpdateTime(robot.getCreateTime());
            robot.setUpdatedBy(user.getUserId());
            robotService.saveRobot(robot, robotHistory, robotAttributeIntentionList,variables);
            return robot.getId();
        } catch (RobotException e) {
            throw e;
        } catch (InterruptedException e) {
            log.error("保存机器人获取锁异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "保存机器人获取锁异常");
        } catch (Exception e) {
            log.error("保存机器人异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "保存机器人失败");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void updateRobot(Robot robot, BladeUser user) {
        if (robot.getId() == null) {
            throw new RobotException("ID不能为空");
        }

        checkBasic(robot);
        RLock lock = null;
        try {
            lock = redissonClient.getLock(String.format(RobotConstant.ROBOT_OPERATE_LOCK, user.getTenantId()));
            boolean lockFlag = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new AiServerException(com.yirendai.voiceaiserver.common.ResultCode.HANDING);
            }

            Robot robotDB = robotMapper.selectById(robot.getId());
            if (robotDB == null) {
                throw new RobotException("机器人不存在");
            }

            if (!robotDB.getVersion().equals(robot.getVersion())) {
                throw new RobotException("记录有变更  请刷新页面");
            }

            RobotHistory robotHistoryDB = robotHistoryMapper
                    .selectByRobotIdAndVersion(robot.getId(), robotDB.getVersion());
            if (robotHistoryDB == null) {
                throw new RobotException("数据异常 历史记录不存在");
            }

            robot.setTenantId(user.getTenantId());
            int nameCount = robotMapper.selectByRobotName(robot);
            if (nameCount > 0) {
                throw new RobotException("名称已存在");
            }

            boolean needNewVersion = false;
            if (RobotStatusEnum.PUBLISH.getValue().equals(robotDB.getStatus())) {
                // 已发布状态 发音人变更需生成新版本
                if (!robot.getToneId().equals(robotDB.getToneId())) {
                    needNewVersion = true;
                }
            }

            Robot robotUpdate = new Robot();
            robotUpdate.setId(robot.getId());
            robotUpdate.setRobotName(robot.getRobotName());
            robotUpdate.setToneId(robot.getToneId());
            robotUpdate.setSpeaker(robot.getSpeaker());
            robot.setUpdatedBy(user.getUserId());
            if (needNewVersion) {
                // 机器人配置
                RobotConfig robotConfig = robotConfigMapper.selectByHistoryId(robotHistoryDB.getId());
                // 意向
                List<RobotAttributeIntention> robotAttributeIntentionList = robotAttributeIntentionMapper
                        .selectByHistoryId(robotHistoryDB.getId());
                // 变量
                List<RobotAttributeVariable> robotAttributeVariables = robotAttributeVariableMapper
                        .selectByHistoryId(robotHistoryDB.getId());
                // 知识库
                List<RobotKnowledgeRela> knowledgeRelaList = robotKnowledgeRelaMapper.selectByHistoryId(robotHistoryDB.getId());
                robotDB.setRobotName(robot.getRobotName());
                robotDB.setToneId(robot.getToneId());
                robotDB.setSpeaker(robot.getSpeaker());
                robotService.createNewVersion(robotDB, robotHistoryDB, robotAttributeIntentionList, robotConfig,
                        robotAttributeVariables,knowledgeRelaList, user);
            }
            else {
                robotHistoryDB.setRobotName(robotUpdate.getRobotName());
                robotHistoryDB.setToneId(robotUpdate.getToneId());
                robotHistoryDB.setSpeaker(robotUpdate.getSpeaker());
                robotUpdate.setUpdateTime(LocalDateTime.now());
                robotService.updateRobotBasicInfo(robotUpdate, robotHistoryDB);
            }

        } catch (RobotException e) {
            throw e;
        } catch (InterruptedException e) {
            log.error("修改机器人获取锁异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "修改机器人获取锁异常");
        } catch (Exception e) {
            log.error("修改机器人异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "修改机器人失败");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void top(Long id, BladeUser bladeUser) {
        if (id == null) {
            throw new RobotException("ID不能为空");
        }

        Robot robotDB = robotMapper.selectById(id);
        if (robotDB == null) {
            throw new RobotException("机器人不存在");
        }

        if (YesOrNoEnum.YES.getValue().equals(robotDB.getTopStatus())) {
            return;
        }

        Robot update = new Robot();
        update.setId(id);
        update.setTopStatus(YesOrNoEnum.YES.getValue());
        update.setTopTime(LocalDateTime.now());
        update.setUpdatedBy(bladeUser.getUserId());
        robotService.updateById(update);
    }

    public void cancelTop(Long id, BladeUser bladeUser) {
        if (id == null) {
            throw new RobotException("ID不能为空");
        }

        Robot robotDB = robotMapper.selectById(id);
        if (robotDB == null) {
            throw new RobotException("机器人不存在");
        }

        if (!YesOrNoEnum.YES.getValue().equals(robotDB.getTopStatus())) {
            return;
        }

        Robot update = new Robot();
        update.setId(id);
        update.setUpdatedBy(bladeUser.getUserId());
        robotMapper.cancelTop(update);
    }

    public void copy(Long id, BladeUser user) {
        if (id == null) {
            throw new RobotException("ID不能为空");
        }

        Robot robotDB = robotMapper.selectById(id);
        if (robotDB == null) {
            throw new RobotException("机器人不存在");
        }

        RobotHistory robotHistoryDB = robotHistoryMapper.selectByRobotIdAndVersion(id, robotDB.getVersion());
        if (robotHistoryDB == null) {
            throw new RobotException("数据异常 历史记录不存在");
        }

        // 机器人配置
        RobotConfig robotConfig = robotConfigMapper.selectByHistoryId(robotHistoryDB.getId());
        // 意向
        List<RobotAttributeIntention> robotAttributeIntentionList = robotAttributeIntentionMapper
                .selectByHistoryId(robotHistoryDB.getId());
        // 变量
        List<RobotAttributeVariable> robotAttributeVariables = robotAttributeVariableMapper
                .selectByHistoryId(robotHistoryDB.getId());

        // 知识库
        List<RobotKnowledgeRela> knowledgeRelaList = robotKnowledgeRelaMapper.selectByHistoryId(robotHistoryDB.getId());
        RLock lock = null;
        try {
            lock = redissonClient.getLock(String.format(RobotConstant.ROBOT_OPERATE_LOCK, user.getTenantId()));
            boolean lockFlag = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new AiServerException(com.yirendai.voiceaiserver.common.ResultCode.HANDING);
            }

            int nameCount = robotMapper.selectCountByLikeRobotName(robotDB.getTenantId(), robotDB.getRobotName());
            Robot toSave = new Robot();
            BeanUtil.copyProperties(robotDB, toSave);
            toSave.setId(null);
            toSave.setCreateTime(LocalDateTime.now());
            toSave.setTopTime(null);
            toSave.setTopStatus(null);
            toSave.setStatus(RobotStatusEnum.NOT_PUBLISH.getValue());
            toSave.setVersion(RobotConstant.INIT_VERSION);
            String name = "副本_" + robotDB.getRobotName() + "_" + (nameCount + 1);
            if (name.length() > 100) {
                throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "机器人的名称字数超限，复制失败！");
            }
            toSave.setRobotName(name);
            toSave.setAlreadyPublish(YesOrNoEnum.NO.getValue());
            RobotHistory robotHistory = new RobotHistory();
            BeanUtil.copyProperties(toSave, robotHistory);
            toSave.setCreatedBy(user.getUserId());
            toSave.setUpdatedBy(user.getUserId());
            toSave.setUpdateTime(toSave.getCreateTime());
            robotHistory.setCreatedBy(user.getUserId());
            robotHistory.setUpdatedBy(user.getUserId());
            robotHistory.setUpdateTime(toSave.getCreateTime());
            robotService.copy(robotDB, robotHistoryDB.getId(), toSave, robotHistory, robotAttributeIntentionList,
                    robotConfig, robotAttributeVariables,knowledgeRelaList);
            robotSessionService.buildDefaultPrompt(robotHistory);
        } catch (RobotException e) {
            throw e;
        } catch (InterruptedException e) {
            log.error("机器人复制获取锁异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "机器人复制获取锁异常");
        } catch (Exception e) {
            log.error("机器人复制异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "机器人复制失败");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void publish(Long id, BladeUser bladeUser) {
        if (id == null) {
            throw new RobotException("ID不能为空");
        }

        Robot robotDB = robotMapper.selectById(id);
        if (robotDB == null) {
            throw new RobotException("机器人不存在");
        }

        if (RobotStatusEnum.PUBLISH.getValue().equals(robotDB.getStatus())) {
            return;
        }

        RobotHistory robotHistoryDB = robotHistoryMapper.selectByRobotIdAndVersion(id, robotDB.getVersion());
        if (robotHistoryDB == null) {
            throw new RobotException("数据异常 历史记录不存在");
        }

        // 校验对话配置节点
        aiRobotDialogNodeService.checkGraph(robotHistoryDB.getId());

        LocalDateTime now = LocalDateTime.now();
        Robot update = new Robot();
        update.setId(id);
        update.setStatus(RobotStatusEnum.PUBLISHING.getValue());
        update.setUpdatedBy(bladeUser.getUserId());
        update.setUpdateTime(now);
        robotService.updateById(update);
        new Thread(new Runnable() {

            @Override
            public void run() {
                Boolean success = robotSessionService.publishPreData(robotHistoryDB.getId());
                if (success) {
                    LocalDateTime now = LocalDateTime.now();
                    Robot update = new Robot();
                    update.setId(id);
                    update.setStatus(RobotStatusEnum.PUBLISH.getValue());
                    update.setPublishTime(now);
                    update.setUpdatedBy(bladeUser.getUserId());
                    update.setAlreadyPublish(YesOrNoEnum.YES.getValue());
                    update.setUpdateTime(now);

                    RobotHistory historyUpdate = new RobotHistory();
                    historyUpdate.setId(robotHistoryDB.getId());
                    historyUpdate.setStatus(RobotStatusEnum.PUBLISH.getValue());
                    historyUpdate.setPublishTime(now);
                    robotService.publishSuccess(update, robotDB, historyUpdate);
                } else {
                    Robot updateFail = new Robot();
                    updateFail.setId(id);
                    updateFail.setStatus(RobotStatusEnum.FAIL.getValue());
                    updateFail.setUpdatedBy(bladeUser.getUserId());
                    updateFail.setUpdateTime(now);
                    robotService.updateById(updateFail);
                }
            }
        }).start();
    }

    public void unPublish(Long id, BladeUser bladeUser) {
        if (id == null) {
            throw new RobotException("ID不能为空");
        }

        Robot robotDB = robotMapper.selectById(id);
        if (robotDB == null) {
            throw new RobotException("机器人不存在");
        }

        if (!RobotStatusEnum.PUBLISH.getValue().equals(robotDB.getStatus())) {
            return;
        }

        Robot update = new Robot();
        update.setId(id);
        update.setStatus(RobotStatusEnum.NOT_PUBLISH.getValue());
        update.setUpdatedBy(bladeUser.getUserId());
        update.setUpdateTime(LocalDateTime.now());
        this.robotMapper.unPublish(update);
    }

    public void deleteByRobotId(Long id, BladeUser bladeUser) {
        if (id == null) {
            throw new RobotException("ID不能为空");
        }

        Robot robotDB = robotMapper.selectById(id);
        if (robotDB == null) {
            throw new RobotException("机器人不存在");
        }

        int count = robotCallTasksMapper.selectRobotCount(id);
        if (count > 0) {
            throw new RobotException(robotDB.getRobotName() + "，已被任务使用，不支持删除。");
        }

        // 查询该机器人绑定的所有规则ID
        List<Long> boundRuleIds = robotCallFrequencyRuleRobotMapper.selectRuleIdsByRobotId(id);

        // 删除机器人的呼叫频次规则绑定关系
        robotCallFrequencyRuleRobotMapper.deleteByRobotId(id);

        // 检查并删除空绑定关系的规则
        if (boundRuleIds != null && !boundRuleIds.isEmpty()) {
            for (Long ruleId : boundRuleIds) {
                // 检查该规则是否还有其他绑定的机器人
                List<Long> remainingRobotIds = robotCallFrequencyRuleRobotMapper.selectRobotIdsByRuleId(ruleId);
                if (remainingRobotIds == null || remainingRobotIds.isEmpty()) {
                    // 如果没有绑定的机器人了，删除该规则
                    robotCallFrequencyRuleMapper.deleteById(ruleId);
                    log.info("删除机器人[{}]后，规则[{}]无绑定机器人，已自动删除", id, ruleId);
                }
            }
        }

        Robot update = new Robot();
        update.setId(id);
        update.setIsDeleted(YesOrNoEnum.YES.getValue());
        update.setUpdatedBy(bladeUser.getUserId());
        update.setUpdateTime(LocalDateTime.now());
        this.robotMapper.updateById(update);
    }

    public void saveRobotCfg(RobotConfigDTO robotConfigDto, BladeUser user) {
        if (robotConfigDto.getIsEnabled() == null) {
            throw new RobotException("开启配置不能为空");
        }

        if (robotConfigDto.getRobotId() == null) {
            throw new RobotException("机器人ID不能为空");
        }

        if (robotConfigDto.getRobotHistoryId() == null) {
            throw new RobotException("机器人历史ID不能为空");
        }

        RLock lock = null;
        try {
            lock = redissonClient.getLock(String.format(RobotConstant.ROBOT_OPERATE_LOCK, user.getTenantId()));
            boolean lockFlag = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new AiServerException(com.yirendai.voiceaiserver.common.ResultCode.HANDING);
            }

            Robot robot = robotMapper.selectById(robotConfigDto.getRobotId());
            if (robot == null) {
                throw new RobotException("机器人不存在");
            }

            if (RobotStatusEnum.PUBLISH.getValue().equals(robot.getStatus())) {
                throw new RobotException("机器人已发布不能变更");
            }

            RobotHistory robotHistory = robotHistoryMapper.selectById(robotConfigDto.getRobotHistoryId());
            if (robotHistory == null) {
                throw new RobotException("机器人历史不存在");
            }

            if (!robot.getVersion().equals(robotHistory.getVersion())) {
                throw new RobotException("记录有变更  请刷新页面");
            }

            List<RobotAttributeIntention> intentionList = robotConfigDto.getIntentionList();
            if (!CollectionUtils.isEmpty(intentionList)) {
                Set<Integer> levelSet = new HashSet<>();
                for (RobotAttributeIntention item : intentionList) {
                    if (item.getLevel() == null) {
                        throw new RobotException("意向等级不能为空");
                    }

                    if (RobotAttributeIntentionLevelEnum.getByKey(item.getLevel()) == null) {
                        throw new RobotException("意向等级输入有误");
                    }

                    if (levelSet.contains(item.getLevel())) {
                        throw new RobotException("意向等级有重复");
                    }

                    levelSet.add(item.getLevel());
                }

            }

            if (robotConfigDto.getId() != null) {
                RobotConfig robotConfig = robotConfigMapper.selectById(robotConfigDto.getId());
                if (robotConfig == null) {
                    throw new RobotException("配置不存在");
                }
            }
            robotConfigDto.setTenantId(user.getTenantId());
            robotConfigService.saveRobotCfg(robotConfigDto, robotHistory, user);
        } catch (RobotException e) {
            throw e;
        } catch (InterruptedException e) {
            log.error("保存机器人配置获取锁异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "保存机器人配置获取锁异常");
        } catch (Exception e) {
            log.error("保存机器人配置异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "保存机器人配置失败");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public Long saveRobotAttributeVariable(RobotAttributeVariableDTO dto, BladeUser user) {
        checkRobotAttributeVariableBasic(dto);

        if (dto.getRobotHistoryId() == null) {
            throw new RobotException("机器人历史ID不能为空");
        }

        RLock lock = null;
        try {
            lock = redissonClient.getLock(String.format(RobotConstant.ROBOT_OPERATE_LOCK, user.getTenantId()));
            boolean lockFlag = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new AiServerException(com.yirendai.voiceaiserver.common.ResultCode.HANDING);
            }

            RobotHistory robotHistory = robotHistoryMapper.selectById(dto.getRobotHistoryId());
            if (robotHistory == null) {
                throw new RobotException("机器人历史不存在");
            }

            Robot robot = robotMapper.selectById(robotHistory.getRobotId());
            if (robot == null) {
                throw new RobotException("机器人不存在");
            }

            if (RobotStatusEnum.PUBLISH.getValue().equals(robot.getStatus())) {
                throw new RobotException("机器人已发布不能变更");
            }

            if (!robot.getVersion().equals(robotHistory.getVersion())) {
                throw new RobotException("机器人版本有变更  请刷新页面");
            }

            int count = robotAttributeVariableMapper.selectCountByName(dto);
            if (count > 0) {
                throw new RobotException("变量名称已存在");
            }

            int countKey = robotAttributeVariableMapper.selectCountByKey(dto);
            if (countKey > 0) {
                throw new RobotException("变量关键字已存在");
            }

            dto.setCreatedBy(user.getUserId());
            dto.setTenantId(user.getTenantId());
            dto.setRobotId(robotHistory.getRobotId());
            robotAttributeVariableService.saveRobotAttributeVariable(dto, user);
            return dto.getId();
        } catch (RobotException e) {
            throw e;
        } catch (InterruptedException e) {
            log.error("保存变量配置获取锁异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "保存变量配置获取锁异常");
        } catch (Exception e) {
            log.error("保存变量配置异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "保存变量配置失败");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void updateRobotAttributeVariable(RobotAttributeVariableDTO dto, BladeUser user) {
        checkRobotAttributeVariableBasic(dto);
        if (dto.getId() == null) {
            throw new RobotException("ID不能为空");
        }

        RLock lock = null;
        try {
            lock = redissonClient.getLock(String.format(RobotConstant.ROBOT_OPERATE_LOCK, user.getTenantId()));
            boolean lockFlag = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new AiServerException(com.yirendai.voiceaiserver.common.ResultCode.HANDING);
            }

            RobotAttributeVariable robotAttributeVariableDB = robotAttributeVariableMapper.selectById(dto.getId());
            if (robotAttributeVariableDB == null) {
                throw new RobotException("记录不存在");
            }

            RobotHistory robotHistory = robotHistoryMapper.selectById(dto.getRobotHistoryId());
            if (robotHistory == null) {
                throw new RobotException("机器人历史不存在");
            }

            Robot robot = robotMapper.selectById(robotHistory.getRobotId());
            if (robot == null) {
                throw new RobotException("机器人不存在");
            }

            if (RobotStatusEnum.PUBLISH.getValue().equals(robot.getStatus())) {
                throw new RobotException("机器人已发布不能变更");
            }

            if (!robot.getVersion().equals(robotHistory.getVersion())) {
                throw new RobotException("机器人版本有变更  请刷新页面");
            }

            int countName = robotAttributeVariableMapper.selectCountByName(dto);
            if (countName > 0) {
                throw new RobotException("变量名称已存在");
            }

            int countKey = robotAttributeVariableMapper.selectCountByKey(dto);
            if (countKey > 0) {
                throw new RobotException("变量关键字已存在");
            }
            dto.setTenantId(user.getTenantId());
            dto.setUpdatedBy(user.getUserId());
            robotAttributeVariableService.updateRobotAttributeVariable(dto, user);
        } catch (RobotException e) {
            throw e;
        } catch (InterruptedException e) {
            log.error("修改变量配置获取锁异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "修改变量配置获取锁异常");
        } catch (Exception e) {
            log.error("修改变量配置异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "修改变量配置失败");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void deleteRobotAttributeVariable(Long id, BladeUser user) {
        RobotAttributeVariable entity = robotAttributeVariableService.getById(id);
        if (entity == null) {
            throw new RobotException("变量不存在");
        }
        List<RobotPositionDTO> positionList = robotAttributeVariableService.getVarPositions(entity);
        if (!CollectionUtils.isEmpty(positionList)) {
            throw new RobotException("变量被引用不能删除");
        }

        RobotAttributeVariable update = new RobotAttributeVariable();
        update.setId(id);
        update.setIsDeleted(YesOrNoEnum.YES.getValue());
        update.setUpdatedBy(user.getUserId());
        robotAttributeVariableService.deleteRobotAttributeVariable(id, user);
    }

    private void checkRobotAttributeVariableBasic(RobotAttributeVariableDTO dto) {
        if (StringUtil.isBlank(dto.getVariableName())) {
            throw new RobotException("变量名称不能为空");
        }

        if (StringUtil.isBlank(dto.getVariableKey())) {
            throw new RobotException("变量关键字不能为空");
        }

        if (dto.getVariableType() == null) {
            throw new RobotException("变量类型不能为空");
        }

        if (RobotAttributeTypeEnum.getByKey(dto.getVariableType()) == null) {
            throw new RobotException("变量类型输入有误");
        }
    }

    public Long createNewVersion(Long id, BladeUser user) {
        if (id == null) {
            throw new RobotException("ID不能为空");
        }

        RLock lock = null;
        try {
            lock = redissonClient.getLock(String.format(RobotConstant.ROBOT_OPERATE_LOCK, user.getTenantId()));
            boolean lockFlag = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockFlag) {
                throw new AiServerException(com.yirendai.voiceaiserver.common.ResultCode.HANDING);
            }

            Robot robotDB = robotMapper.selectById(id);
            if (robotDB == null) {
                throw new RobotException("机器人不存在");
            }

            RobotHistory robotHistoryDB = robotHistoryMapper.selectByRobotIdAndVersion(id, robotDB.getVersion());
            if (robotHistoryDB == null) {
                throw new RobotException("数据异常 历史记录不存在");
            }

            // 非发布状态  返回当前版本ID
            if (!RobotStatusEnum.PUBLISH.getValue().equals(robotDB.getStatus())) {
                return robotHistoryDB.getId();
            }

            // 机器人配置
            RobotConfig robotConfig = robotConfigMapper.selectByHistoryId(robotHistoryDB.getId());
            // 意向
            List<RobotAttributeIntention> robotAttributeIntentionList = robotAttributeIntentionMapper
                    .selectByHistoryId(robotHistoryDB.getId());
            // 变量
            List<RobotAttributeVariable> robotAttributeVariables = robotAttributeVariableMapper
                    .selectByHistoryId(robotHistoryDB.getId());
            // 知识库
            List<RobotKnowledgeRela> knowledgeRelaList = robotKnowledgeRelaMapper.selectByHistoryId(robotHistoryDB.getId());
            return robotService.createNewVersion(robotDB, robotHistoryDB, robotAttributeIntentionList, robotConfig,
                    robotAttributeVariables,knowledgeRelaList, user);
        } catch (RobotException e) {
            throw e;
        } catch (InterruptedException e) {
            log.error("机器人创建新版本获取锁异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "机器人创建新版本获取锁异常");
        } catch (Exception e) {
            log.error("机器人创建新版本异常", e);
            throw new RobotException(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "机器人创建新版本失败");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private List<RobotAttributeVariable> getGlobalVariableList(String tenantId) {
        List<RobotAttributeGlobalVariable> globalVariables = robotAttributeGlobalVariableMapper
                .selectAll();
        if (!CollectionUtils.isEmpty(globalVariables)) {
            List<RobotAttributeVariable> list = new ArrayList<>();
            for (RobotAttributeGlobalVariable variable : globalVariables) {
                RobotAttributeVariable item = new RobotAttributeVariable();
                BeanUtil.copyProperties(variable, item);
                item.setGlobalVariableId(variable.getId());
                item.setSystemDefault(YesOrNoEnum.YES.getValue());
                item.setVariableType(RobotAttributeTypeEnum.REPLACE.getKey());
                list.add(item);
            }
            return list;
        }

        return null;
    }

    private List<RobotAttributeIntention> getIntentionList(String tenantId) {
        List<RobotAttributeGlobalIntention> globalIntentionList = robotAttributeGlobalIntentionMapper
                .selectAll( );
        if (!CollectionUtils.isEmpty(globalIntentionList)) {
            List<RobotAttributeIntention> list = new ArrayList<>();
            for (RobotAttributeGlobalIntention globalIntention : globalIntentionList) {
                RobotAttributeIntention item = new RobotAttributeIntention();
                BeanUtil.copyProperties(globalIntention, item);
                item.setType(RobotIntentType.SYSTEM.getCode());
                item.setGlobalIntentionId(globalIntention.getId());
                list.add(item);
            }
            return list;
        }

        return null;
    }

    private void checkBasic(Robot robot) {
        if (StringUtil.isBlank(robot.getRobotName())) {
            throw new RobotException("名称不能为空");
        }

        if (robot.getToneId() == null) {
            throw new RobotException("发音人ID不能为空");
        }

        if (robot.getRecordingType() == null) {
            throw new RobotException("录音类型不能为空");
        }

        RobotRecordingTypeEnum robotRecordingTypeEnum = RobotRecordingTypeEnum.getByKey(robot.getRecordingType());
        if (robotRecordingTypeEnum == null) {
            throw new RobotException("录音类型输入有误");
        }
    }
}
