/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 外呼通话任务客户号码导入表视图实体类
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@ApiModel(value = "RobotCallTasksCustomerVO对象", description = "外呼通话任务客户号码导入表")
public class RobotCallTasksCustomerVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID")
	private Long id;

	@ApiModelProperty(value = "被叫号码")
	private String calledNumber;

	@ApiModelProperty(value = "变量列表与值，以约定格式存储")
	private String variables;

	@ApiModelProperty(value = "导入时间")
	private LocalDateTime importTime;

	@ApiModelProperty(value = "导入用户")
	private String importedBy;

	@ApiModelProperty(value = "租户ID")
	private String tenantId;

	@ApiModelProperty(value = "任务ID，关联到ai_outbound_call_tasks表")
	private Long taskId;

    @ApiModelProperty(value = "呼叫状态（0:未拨打, 1:拨打中, 2:已接通, 3:未接通, 4:超频拦截）")
    private Integer callStatus;

    @ApiModelProperty(value = "呼叫结果（未接通原因）（0:无, 1:无人接听, 2:关机, 3:停机, 4:空号, 5:用户拒接, 6:线路忙, 7:线路拦截, 8:系统拦截, 12:超频拦截）")
    private Integer callResult;

	@ApiModelProperty(value = "意向分类")
	private String intentType;

	@ApiModelProperty(value = "最近意向")
	private String intention ;
	@ApiModelProperty(value = "主叫号码（客户侧显示的号码）")
	private String callerDisplayNumber;

	@ApiModelProperty(value = "拨打时间")
	private LocalDateTime callStartTime;

	@ApiModelProperty(value = "结束时间")
	private LocalDateTime callEndTime;

	@ApiModelProperty(value = "通话时长（最后一次通话总时长，单位：秒，含响铃）")
	private Integer callDuration;

	@ApiModelProperty(value = "对话轮数（最后一次通话总轮数，以主动发起与回应为一轮）")
	private Integer dialogueRounds;

	@ApiModelProperty(value = "意向名称")
	private String intentionDes;

    @ApiModelProperty(value = "机器人版本号ID")
    private Long robotHistoryId;

	@ApiModelProperty(value = "呼叫次数")
	private Integer callRounds;
}
