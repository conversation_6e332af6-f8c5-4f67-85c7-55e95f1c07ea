package com.yirendai.robot.modules.tenant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.yirendai.robot.enums.*;
import com.yirendai.robot.exception.RobotException;
import com.yirendai.robot.modules.tenant.dto.RobotTenantSmsSendLogDTO;
import com.yirendai.robot.modules.tenant.dto.RobotTenantSmsSendLogPageDTO;
import com.yirendai.robot.modules.tenant.dto.RobotTenantSmsSendLogSearchConditionDTO;
import com.yirendai.robot.modules.tenant.entity.RobotTenantSms;
import com.yirendai.robot.modules.tenant.entity.RobotTenantSmsReqLog;
import com.yirendai.robot.modules.tenant.entity.RobotTenantSmsSendLog;
import com.yirendai.robot.modules.tenant.mapper.RobotTenantSmsMapper;
import com.yirendai.robot.modules.tenant.mapper.RobotTenantSmsReqLogMapper;
import com.yirendai.robot.modules.tenant.mapper.RobotTenantSmsSendLogMapper;
import com.yirendai.robot.modules.tenant.service.IRobotTenantSmsSendLogService;
import com.yirendai.robot.util.ExtractorUtil;
import com.yirendai.workbench.blade.mapper.BladeDeptMapper;
import com.yirendai.workbench.blade.mapper.BladeUserMapper;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.entity.BladeDept;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.util.DataScopeUtil;
import com.yirendai.workbench.util.DesensitizationUtil;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.util.SaasDataScopeUtil;
import com.yirendai.workbench.vo.req.sms.SmsSendLogReq;
import com.yirendai.workbench.vo.res.callcenter.BatchOperationResult;
import com.yirendai.workbench.wrapper.TaskScheduleWrapper;
import com.yirendai.workbench.wrapper.dto.CallCenterTask;
import com.yirendai.workbench.wrapper.dto.anmi.AnmiSmsInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 短信发送记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Slf4j
@Service
@RefreshScope
public class RobotTenantSmsSendLogServiceImpl extends ServiceImpl<RobotTenantSmsSendLogMapper, RobotTenantSmsSendLog> implements IRobotTenantSmsSendLogService {

    @Value("${sms.menu.code.saas}")
    private String smsMenuCodeSaas;

    @Resource
    RobotTenantSmsMapper smsMapper;

    @Resource
    DataScopeUtil dataScopeUtil;

    @Resource
    SaasDataScopeUtil saasDataScopeUtil;

    @Resource
    BladeUserMapper userMapper;

    @Resource
    BladeDeptMapper deptMapper;

    @Resource
    TaskScheduleWrapper taskScheduleWrapper;

    @Resource
    RobotTenantSmsReqLogMapper smsReqLogMapper;

    @Resource
    TenantServiceHolder tenantServiceHolder;

    @Resource
    CallcenterProperty callcenterProperty;

    /**
     * 短信变量格式符号
     */
    private static final String SMS_VAR_FORMAT_SYMBOL = "$";

    @Override
    public Page<RobotTenantSmsSendLogDTO> page(RobotTenantSmsSendLogPageDTO pageDTO) {
        Set<Integer> sourceSet = paramValid(pageDTO);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
        LocalDateTime sendTimeStart = StrUtil.isBlank(pageDTO.getSendTimeStart()) ?
                null : LocalDate.parse(pageDTO.getSendTimeStart(), formatter).atStartOfDay(),
                sendTimeEnd = StrUtil.isBlank(pageDTO.getSendTimeEnd()) ?
                        null : LocalDate.parse(pageDTO.getSendTimeEnd(), formatter).plusDays(1L).atStartOfDay();
        if (Objects.nonNull(sendTimeStart) && Objects.nonNull(sendTimeEnd) && sendTimeEnd.compareTo(sendTimeStart) <= 0) {
            throw new RobotException(RobotResult.PARAM_INVALID.getCode(), "结束日期必须大于等于开始日期");
        }

        Page<RobotTenantSmsSendLogDTO> res = new Page<>();
        res.setCurrent(pageDTO.getCurrent());
        res.setSize(pageDTO.getSize());

        Set<String> plannerIdSet = null;
        try {
            plannerIdSet = getPlannerScope(pageDTO);
        } catch (RobotException e) {
            if (e.getCode().equals(RobotResult.SMS_SEND_LOG_EXPORT_EMPTY.getCode())) {
                return res;
            }
            throw e;
        }

        Page<RobotTenantSmsSendLog> page = new Page<>(pageDTO.getCurrent(), pageDTO.getSize());
        LambdaQueryWrapper<RobotTenantSmsSendLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(pageDTO.getUserId()), RobotTenantSmsSendLog::getUserId, pageDTO.getUserId())
                .eq(Objects.nonNull(pageDTO.getUserType()), RobotTenantSmsSendLog::getUserType, pageDTO.getUserType())
                .like(StrUtil.isNotBlank(pageDTO.getMobile()), RobotTenantSmsSendLog::getMobile, pageDTO.getMobile())
                .eq(Objects.nonNull(pageDTO.getSmsId()), RobotTenantSmsSendLog::getSmsId, pageDTO.getSmsId())
                .eq(Objects.nonNull(pageDTO.getStatus()), RobotTenantSmsSendLog::getStatus, pageDTO.getStatus())
                .in(CollUtil.isNotEmpty(sourceSet), RobotTenantSmsSendLog::getSource, sourceSet)
                .ge(Objects.nonNull(sendTimeStart), RobotTenantSmsSendLog::getCreateTime, sendTimeStart)
                .eq(StringUtils.isNotBlank(pageDTO.getPlannerPhone()), RobotTenantSmsSendLog::getPlannerPhone, pageDTO.getPlannerPhone())
                .eq(Objects.nonNull(pageDTO.getSendWay()), RobotTenantSmsSendLog::getSendWay, pageDTO.getSendWay())
                .lt(Objects.nonNull(sendTimeEnd), RobotTenantSmsSendLog::getCreateTime, sendTimeEnd)
                .in(CollUtil.isNotEmpty(plannerIdSet), RobotTenantSmsSendLog::getPlannerId, plannerIdSet)
                .orderByDesc(RobotTenantSmsSendLog::getCreateTime);

        // 当position是客户详情页时，
        if (Objects.nonNull(pageDTO.getPosition()) && PositionEnum.CUSTOMER_DETAIL.getCode().equals(pageDTO.getPosition())) {
            String plannerNo = OwnAuthUtil.getPlannerNo();
            if (StrUtil.isNotBlank(plannerNo)) {
                // 通过系统发送的短信对所有账号可见,或者通过手机发送的短信只能看到当前登录账号发送的
                wrapper.and(w -> w.eq(RobotTenantSmsSendLog::getSendWay, SendWayEnum.SYSTEM.getCode())
                        .or(sw -> sw.eq(RobotTenantSmsSendLog::getSendWay, SendWayEnum.MOBILE.getCode())
                                .eq(RobotTenantSmsSendLog::getPlannerId, plannerNo)));
            }
        }


        Page<RobotTenantSmsSendLog> pageResult = page(page, wrapper);

        BeanUtil.copyProperties(pageResult, res);
        if (CollUtil.isEmpty(pageResult.getRecords())) {
            return res;
        }

        Set<Long> smsIdSet = pageResult.getRecords().stream()
                .filter(e-> SendWayEnum.SYSTEM.getCode().equals(e.getSendWay()))
                .map(RobotTenantSmsSendLog::getSmsId).collect(Collectors.toSet());
        Map<Long, RobotTenantSms> smsMap = new HashMap<>();
        if (CollUtil.isNotEmpty(smsIdSet)) {
            smsMap = smsMapper.selectBatchIds(smsIdSet).stream().collect(Collectors.toMap(RobotTenantSms::getId, Function.identity()));
        }

        Map<String, com.yirendai.workbench.entity.BladeUser> plannerMap = new HashMap<>();
        Map<Long, String> deptMap = new HashMap<>();
        if (Boolean.TRUE.equals(pageDTO.getNeedPlannerInfo())) {
            Set<String> plannerCodeSet = pageResult.getRecords().stream().map(RobotTenantSmsSendLog::getPlannerId).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(plannerCodeSet)) {
                LambdaQueryWrapper<com.yirendai.workbench.entity.BladeUser> userWrapper = new LambdaQueryWrapper<>();
                userWrapper.in(com.yirendai.workbench.entity.BladeUser::getCode, plannerCodeSet)
                        .eq(com.yirendai.workbench.entity.BladeUser::getTenantId, OwnAuthUtil.getTenantId());
                plannerMap = userMapper.selectList(userWrapper).stream().collect(Collectors.toMap(com.yirendai.workbench.entity.BladeUser::getCode, Function.identity()));
            }

            Set<Long> deptIdSet = plannerMap.values().stream().map(com.yirendai.workbench.entity.BladeUser::getDeptId).filter(StrUtil::isNotBlank)
                    .map(Func::toLongList).flatMap(List::stream).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(deptIdSet)) {
                List<BladeDept> deptList = new ArrayList<>();
                int count = (int) Math.ceil(deptIdSet.size() / 100.0);
                for (int i = 0; i < count; i++) {
                    deptList.addAll(deptMapper.selectBatchIds(deptIdSet.stream().skip(i * 100L).limit(100).collect(Collectors.toSet())));
                }

                Set<Long> deptIds = deptList.stream().map(d -> d.getAncestors() + "," + d.getId()).map(Func::toLongList)
                        .flatMap(List::stream).filter(Objects::nonNull).collect(Collectors.toSet());
                Map<Long, String> deptNameMap = new HashMap<>();
                int size = (int) Math.ceil(deptIds.size() / 100.0);
                for (int i = 0; i < size; i++) {
                    deptNameMap.putAll(deptMapper.selectBatchIds(deptIds.stream().skip(i * 100L).limit(100).collect(Collectors.toSet()))
                            .stream().filter(d -> StrUtil.isNotBlank(d.getDeptName())).collect(Collectors.toMap(BladeDept::getId, BladeDept::getDeptName)));
                }

                for (BladeDept dept : deptList) {
                    List<Long> deptIdList = Func.toLongList(dept.getAncestors() + "," + dept.getId()).stream().filter(Objects::nonNull).collect(Collectors.toList());
                    List<String> deptNameList = deptIdList.stream().map(deptNameMap::get).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                    deptMap.put(dept.getId(), String.join("/", deptNameList));
                }
            }
        }

        List<RobotTenantSmsSendLogDTO> dataList = new ArrayList<>();
        for (RobotTenantSmsSendLog log : pageResult.getRecords()) {
            RobotTenantSmsSendLogDTO logDTO = new RobotTenantSmsSendLogDTO();
            BeanUtil.copyProperties(log, logDTO);
            logDTO.setMobile(DesensitizationUtil.hidePhone(log.getMobile()));
            if (smsMap.containsKey(log.getSmsId())) {
                logDTO.setSmsTitle(smsMap.get(log.getSmsId()).getTitle());
                Map<String, String> requestParam = JSON.parseObject(log.getRequestParam(), new TypeReference<HashMap<String, String>>(){});
                Map<String, String> varMap = new HashMap<>();
                if (StrUtil.isNotBlank(requestParam.get("keywords"))) {
                    Map<String, String> keywordMap = JSON.parseObject(requestParam.get("keywords"), new TypeReference<HashMap<String, String>>(){});
                    for (Map.Entry<String, String> entry : keywordMap.entrySet()) {
                        varMap.put(SMS_VAR_FORMAT_SYMBOL + entry.getKey() + SMS_VAR_FORMAT_SYMBOL, entry.getValue());
                    }
                }
                logDTO.setSmsContent(ExtractorUtil.replaceVar(smsMap.get(log.getSmsId()).getContent(), varMap));
            }
            if(SendWayEnum.MOBILE.getCode().equals(log.getSendWay())) {
                logDTO.setSmsContent(log.getContent());
            }
            if (StrUtil.isNotBlank(log.getPlannerId()) && plannerMap.containsKey(log.getPlannerId())) {
                com.yirendai.workbench.entity.BladeUser planner = plannerMap.get(log.getPlannerId());
                logDTO.setPlannerName(planner.getRealName());
                if (StrUtil.isNotBlank(planner.getDeptId())) {
                    List<Long> deptIds = Func.toLongList(planner.getDeptId()).stream().filter(Objects::nonNull).collect(Collectors.toList());
                    List<String> deptNameList = deptIds.stream().map(deptMap::get).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                    logDTO.setPlannerDept(String.join(";", deptNameList));
                }
            }
            dataList.add(logDTO);
        }
        res.setRecords(dataList);
        return res;
    }

    private Set<Integer> paramValid(RobotTenantSmsSendLogSearchConditionDTO conditionDTO) {
        BladeUser user = OwnAuthUtil.getUser();
        String tenantId = OwnAuthUtil.getTenantId();
        if (Objects.isNull(user) || Objects.isNull(user.getUserId()) || StrUtil.isBlank(tenantId)) {
            throw new RobotException(RobotResult.NO_LOGIN);
        }
        if (Objects.nonNull(conditionDTO.getUserType()) && Objects.isNull(UserTypeEnum.getByCode(conditionDTO.getUserType()))) {
            throw new RobotException(RobotResult.PARAM_INVALID.getCode(), "对方身份未知");
        }
        if (Objects.isNull(SystemSourceEnum.getByCode(conditionDTO.getSystemSource()))) {
            throw new RobotException(RobotResult.PARAM_INVALID.getCode(), "系统来源未知");
        }
        if (Objects.isNull(PositionEnum.getByCode(conditionDTO.getPosition()))) {
            throw new RobotException(RobotResult.PARAM_INVALID.getCode(), "位置未知");
        }
        Set<Integer> sourceSet = CollUtil.isEmpty(conditionDTO.getSourceSet()) ?
                null : conditionDTO.getSourceSet().stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(sourceSet)) {
            for (Integer source : sourceSet) {
                if (Objects.isNull(SmsSourceEnum.getByCode(source))) {
                    throw new RobotException(RobotResult.PARAM_INVALID.getCode(), "来源未知");
                }
            }
        }
        if (Objects.nonNull(conditionDTO.getSmsId())) {
            RobotTenantSms sms = smsMapper.selectById(conditionDTO.getSmsId());
            if (Objects.isNull(sms)) {
                throw new RobotException(RobotResult.PARAM_INVALID.getCode(), "短信id非法");
            }
        }
        return sourceSet;
    }

    private Set<String> getPlannerScope(RobotTenantSmsSendLogSearchConditionDTO conditionDTO) {
        Set<String> plannerIdSet = new HashSet<>();
        if (PositionEnum.getByCode(conditionDTO.getPosition()).isUseDataScope()) {
            if (SystemSourceEnum.YUMENG.getCode().equals(conditionDTO.getSystemSource())) {
                plannerIdSet = new HashSet<>(dataScopeUtil.getUserMenuDataScope(callcenterProperty.getYumengSmsMenuCode(),TenantServiceHolder.SCOPE_PLANNER_FIELD));
            } else {
                List<String> plannerIdList = saasDataScopeUtil.findDataScopeFiled(smsMenuCodeSaas, TenantServiceHolder.SCOPE_PLANNER_FIELD);
                if (Objects.isNull(plannerIdList)) {
                    throw new RobotException(RobotResult.SMS_PERMISSION_DATA_ERROR);
                }
                plannerIdSet = new HashSet<>(plannerIdList);
            }
        }
        Set<String> plannerIdDeptSet = null, plannerIdNameSet = null;
        Set<String> plannerDeptIdSet = StringUtils.isBlank(conditionDTO.getPlannerDeptIdSet()) ?
                null : Sets.newHashSet(conditionDTO.getPlannerDeptIdSet());
        if (CollUtil.isNotEmpty(plannerDeptIdSet)) {
            List<String> temp = dataScopeUtil.getPlannerNoByDeptId(String.join(",", plannerDeptIdSet));
            if (Objects.isNull(temp)) {
                throw new RobotException(RobotResult.GET_DEPT_PLANNER_INFO_ERR);
            }
            if (CollUtil.isEmpty(temp)) {
                throw new RobotException(RobotResult.SMS_SEND_LOG_EXPORT_EMPTY);
            }
            plannerIdDeptSet = new HashSet<>(temp);
        }
        if (StrUtil.isNotBlank(conditionDTO.getPlannerName())) {
            LambdaQueryWrapper<com.yirendai.workbench.entity.BladeUser> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.like(com.yirendai.workbench.entity.BladeUser::getRealName, conditionDTO.getPlannerName())
                    .eq(com.yirendai.workbench.entity.BladeUser::getTenantId, OwnAuthUtil.getTenantId())
                    .eq(com.yirendai.workbench.entity.BladeUser::getIsDeleted, YesOrNoEnum.NO.getValue());
            plannerIdNameSet = userMapper.selectList(userWrapper).stream().map(com.yirendai.workbench.entity.BladeUser::getCode)
                    .filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            if (CollUtil.isEmpty(plannerIdNameSet)) {
                throw new RobotException(RobotResult.SMS_SEND_LOG_EXPORT_EMPTY);
            }
        }
        if (CollUtil.isNotEmpty(plannerIdDeptSet) && CollUtil.isNotEmpty(plannerIdNameSet)) {
            plannerIdDeptSet.retainAll(plannerIdNameSet);
            if (CollUtil.isEmpty(plannerIdDeptSet)) {
                throw new RobotException(RobotResult.SMS_SEND_LOG_EXPORT_EMPTY);
            }
        } else if (CollUtil.isNotEmpty(plannerIdNameSet)) {
            plannerIdDeptSet = plannerIdNameSet;
        }
        if (CollUtil.isNotEmpty(plannerIdDeptSet)) {
            if (CollUtil.isEmpty(plannerIdSet)) {
                plannerIdSet = plannerIdDeptSet;
            } else {
                plannerIdSet.retainAll(plannerIdDeptSet);
                if (CollUtil.isEmpty(plannerIdSet)) {
                    throw new RobotException(RobotResult.SMS_SEND_LOG_EXPORT_EMPTY);
                }
            }
        }
        return plannerIdSet;
    }

    @Override
    public BatchOperationResult exportSubmitTask(RobotTenantSmsSendLogSearchConditionDTO conditionDTO) {
        Set<Integer> sourceSet = paramValid(conditionDTO);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
        LocalDateTime sendTimeStart = StrUtil.isBlank(conditionDTO.getSendTimeStart()) ?
                null : LocalDate.parse(conditionDTO.getSendTimeStart(), formatter).atStartOfDay(),
                sendTimeEnd = StrUtil.isBlank(conditionDTO.getSendTimeEnd()) ?
                        null : LocalDate.parse(conditionDTO.getSendTimeEnd(), formatter).plusDays(1L).atStartOfDay();
        if (Objects.nonNull(sendTimeStart) && Objects.nonNull(sendTimeEnd) && sendTimeEnd.compareTo(sendTimeStart) <= 0) {
            throw new RobotException(RobotResult.PARAM_INVALID.getCode(), "结束日期必须大于等于开始日期");
        }

        Set<String> plannerIdSet = getPlannerScope(conditionDTO);

        LambdaQueryWrapper<RobotTenantSmsSendLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(conditionDTO.getUserId()), RobotTenantSmsSendLog::getUserId, conditionDTO.getUserId())
                .eq(Objects.nonNull(conditionDTO.getUserType()), RobotTenantSmsSendLog::getUserType, conditionDTO.getUserType())
                .like(StrUtil.isNotBlank(conditionDTO.getMobile()), RobotTenantSmsSendLog::getMobile, conditionDTO.getMobile())
                .eq(Objects.nonNull(conditionDTO.getSmsId()), RobotTenantSmsSendLog::getSmsId, conditionDTO.getSmsId())
                .eq(Objects.nonNull(conditionDTO.getStatus()), RobotTenantSmsSendLog::getStatus, conditionDTO.getStatus())
                .in(CollUtil.isNotEmpty(sourceSet), RobotTenantSmsSendLog::getSource, sourceSet)
                .ge(Objects.nonNull(sendTimeStart), RobotTenantSmsSendLog::getCreateTime, sendTimeStart)
                .lt(Objects.nonNull(sendTimeEnd), RobotTenantSmsSendLog::getCreateTime, sendTimeEnd)
                .in(CollUtil.isNotEmpty(plannerIdSet), RobotTenantSmsSendLog::getPlannerId, plannerIdSet)
                .orderByDesc(RobotTenantSmsSendLog::getCreateTime);
        if (count(wrapper) < 1) {
            throw new RobotException(RobotResult.SMS_SEND_LOG_EXPORT_EMPTY);
        }

        SmsSendLogReq smsSendLogReq = new SmsSendLogReq();
        BeanUtil.copyProperties(conditionDTO, smsSendLogReq);
        smsSendLogReq.setPlannerIdSet(plannerIdSet);
        smsSendLogReq.setSendTimeStart(sendTimeStart);
        smsSendLogReq.setSendTimeEnd(sendTimeEnd);
        CallCenterTask taskInfo = taskScheduleWrapper.smsSendLogExportSubmitTask(smsSendLogReq);
        return new BatchOperationResult(true, taskInfo.getId(), taskInfo.getTaskName());
    }

    @Override
    public void handleSmsReqLog() {
        log.info("处理短信历史发送记录任务开始执行");

        Long startId = null;
        while (true) {
            LambdaQueryWrapper<RobotTenantSmsReqLog> wrapper = new LambdaQueryWrapper<>();
            wrapper.gt(Objects.nonNull(startId), RobotTenantSmsReqLog::getId, startId).orderByAsc(RobotTenantSmsReqLog::getId).last("limit 100");
            List<RobotTenantSmsReqLog> list = smsReqLogMapper.selectList(wrapper);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            List<RobotTenantSmsSendLog> dataList = new ArrayList<>();
            for (RobotTenantSmsReqLog reqLog : list) {
                RobotTenantSmsSendLog sendLog = new RobotTenantSmsSendLog();
                BeanUtil.copyProperties(reqLog, sendLog);
                sendLog.setId(null);
                Map<String, List<String>> reqMap = JSON.parseObject(reqLog.getRequestParam(), new TypeReference<Map<String, List<String>>>(){});
                Map<String, String> paramMap = reqMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, r -> r.getValue().get(0)));
                sendLog.setRequestParam(JSON.toJSONString(paramMap));
                sendLog.setMobile(paramMap.get("mobile"));
                try {
                    SimpleUserInfoDto customerInfo = tenantServiceHolder.getCustomerInfoWrapper(reqLog.getTenantId())
                            .getUserBasicInfoByPhone(sendLog.getMobile(), reqLog.getTenantId());
                    if (Objects.nonNull(customerInfo) && StrUtil.isNotBlank(customerInfo.getUserId())) {
                        sendLog.setUserId(customerInfo.getUserId());
                        sendLog.setUserType(UserTypeEnum.CUSTOMER.getCode());
                    }
                } catch (Exception e) {
                    log.error("根据手机号mobile=[{}]查询客户id发生异常，异常原因为", sendLog.getMobile(), e);
                }
                dataList.add(sendLog);
            }
            saveBatch(dataList);

            if (list.size() < 100) {
                break;
            }
            startId = list.get(list.size() - 1).getId();
        }

        log.info("处理短信历史发送记录任务执行结束");
    }

    @Override
    public String getMsgByCustomerId(String customerId, String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        if (StringUtils.isBlank(customerId) || StringUtils.isBlank(tenantId)) {
            log.warn("客户ID或租户ID为空，customerId: {}, tenantId: {}", customerId, tenantId);
            return "";
        }

        try {
            // 构建查询条件
            LambdaQueryWrapper<RobotTenantSmsSendLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RobotTenantSmsSendLog::getUserId, customerId)
                    .eq(RobotTenantSmsSendLog::getTenantId, tenantId)
                    .ge(Objects.nonNull(startTime), RobotTenantSmsSendLog::getCreateTime, startTime)
                    .lt(Objects.nonNull(endTime), RobotTenantSmsSendLog::getCreateTime, endTime)
                    // 发送成功
                    .eq(RobotTenantSmsSendLog::getStatus, true)
                    // 最多100条
                    .last("LIMIT 100");

            List<RobotTenantSmsSendLog> smsList = list(queryWrapper);

            if (CollUtil.isEmpty(smsList)) {
                log.info("未找到客户短信记录，customerId: {}, tenantId: {}", customerId, tenantId);
                return "";
            }

            // 拼接短信内容
            StringBuilder contentBuilder = new StringBuilder();
            for (int i = 0; i < smsList.size(); i++) {
                RobotTenantSmsSendLog smsLog = smsList.get(i);
                contentBuilder.append("【短信").append(i + 1).append("】")
                        .append("时间: ").append(smsLog.getCreateTime())
                        .append(", 内容: ").append(smsLog.getContent())
                        .append("\n");
            }

            log.info("查询客户短信记录成功，customerId: {}, tenantId: {}, 记录数: {}", 
                    customerId, tenantId, smsList.size());
            return contentBuilder.toString();

        } catch (Exception e) {
            log.error("查询客户短信记录失败，customerId: {}, tenantId: {}, error: {}", 
                    customerId, tenantId, e.getMessage(), e);
            return "";
        }
    }
}
