package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "RobotCallFrequencyRuleOperateResp", description = "频次规则操作结果")
public class RobotCallFrequencyRuleOperateResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则ID")
    private Long ruleId;

    @ApiModelProperty(value = "绑定失败的机器人ID列表")
    private List<Long> failedRobotIds;
}
