package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 频次规则分页展示
 */
@Data
@ApiModel(value = "RobotCallFrequencyRulePageVO", description = "频次规则分页展示")
public class RobotCallFrequencyRulePageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则ID")
    private Long id;

    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "机器人ID集合")
    private java.util.List<Long> robotIds;

    @ApiModelProperty(value = "机器人名称集合")
    private java.util.List<String> robotNames;

    @ApiModelProperty(value = "可用机器人列表（包含已绑定和未绑定的机器人）")
    private java.util.List<RobotOptionVO> robots;

    @ApiModelProperty(value = "接通次数限制")
    private Integer limitCount;

    @ApiModelProperty(value = "规则状态 1-启用 0-停用")
    private Integer status;

    @ApiModelProperty(value = "启用时间")
    private LocalDateTime enabledTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    private String updateUserName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
