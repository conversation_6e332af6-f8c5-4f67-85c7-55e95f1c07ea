package com.yirendai.robot.modules.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRuleRobot;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface RobotCallFrequencyRuleRobotMapper extends BaseMapper<RobotCallFrequencyRuleRobot> {

    int deleteByRuleIdAndRobotIds(@Param("ruleId") Long ruleId, @Param("robotIds") Collection<Long> robotIds);

    List<RobotCallFrequencyRuleRobot> selectRobotCallFrequencyByRuleIds(@Param("ruleIds") Collection<Long> ruleIds);

    RobotCallFrequencyRuleRobot selectRobotCallFrequencyByRobotId(@Param("robotId") Long robotId);

    List<Long> selectRobotIdsByRuleId(@Param("ruleId") Long ruleId);

    int deleteByRobotId(@Param("robotId") Long robotId);

    List<Long> selectRuleIdsByRobotId(@Param("robotId") Long robotId);
}
