<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yirendai.robot.modules.call.mapper.RobotCallTasksCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer">
        <id column="id" property="id" />
        <result column="called_number" property="calledNumber" />
        <result column="variables" property="variables" />
        <result column="import_time" property="importTime" />
        <result column="imported_by" property="importedBy" />
        <result column="tenant_id" property="tenantId" />
        <result column="task_id" property="taskId" />
        <result column="call_rounds" property="callRounds" />
        <result column="last_session_id" property="lastSessionId" />
        <result column="is_test" property="isTest" />
        <result column="user_id" property="userId" />
        <result column="dup_call_status" property="dupCallStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        *
    </sql>
    <insert id="copyCustomer">
        insert into ai_robot_call_tasks_customer(
            task_id,
            called_number,
            variables,
            import_time,
            imported_by,
            tenant_id,
            is_test,
            user_id
        )
        select
            #{targetTaskId} as task_id,
            cus.called_number,
            cus.variables,
            now(),
            #{importedBy},
            cus.tenant_id,
            cus.is_test,
            cus.user_id
        from ai_robot_call_tasks_customer cus
                 left join ai_robot_session session on cus.last_session_id = session.id
        where cus.task_id = #{sourceTaskId}
          and cus.is_test = 0
         and session.call_status  = 3
        and session.call_result in (0,1,2,3,5,6,7,8,9,10,11,12)
        and cus.called_number not in (
            select called_number
            from ai_robot_call_tasks_customer
            where task_id = #{targetTaskId}
            )
    </insert>
    <delete id="deleteByIdAndSessionIsNull">
        delete from ai_robot_call_tasks_customer
        where  id = #{taskCustomerId}
          and last_session_id is null
    </delete>
    <select id="countByCalledNumber" resultType="java.lang.Long">
        select count(1)
        from ai_robot_call_tasks_customer
        where called_number = #{calledNumber}
          and task_id = #{taskId}
          and is_test = 0
    </select>
    <select id="selectTaskCallCount" resultType="com.yirendai.robot.modules.call.dto.TaskCallCountDTO">
        select task_id as taskId,
        count(*) as phoneCount,
        count(last_session_id) as callCount
        from ai_robot_call_tasks_customer
        where task_id in (
        <foreach collection="taskIds" item="item" separator=",">
            #{item}
        </foreach>
        )
        and  is_test = 0
        group by task_id

    </select>
    <select id="pageList" resultType="com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerVO">
        select customer.id,
        customer.called_number as calledNumber,
        customer.variables,
        customer.import_time as importTime,
        customer.imported_by as importedBy,
        customer.tenant_id as tenantId,
        customer.task_id as taskId,
        customer.call_rounds as callRounds,
        robotSession.call_status as callStatus,
        robotSession.call_result as callResult,
        robotSession.intention_level as intentType,
        robotSession.recent_intention as intention,
        robotSession.caller_display_number as callerDisplayNumber,
        robotSession.call_start_time as callStartTime,
        robotSession.call_end_time as callEndTime,
        robotSession.call_duration as callDuration,
        robotSession.dialogue_rounds as dialogueRounds,
        robotSession.intention_des as intentionDes,
        robotSession.robot_history_id as robotHistoryId
        from ai_robot_call_tasks_customer customer
        left join ai_robot_session robotSession on customer.last_session_id = robotSession.id
        <where>
            and is_test = 0
            <if test="req.taskId != null">
                and customer.task_id = #{req.taskId}
            </if>
            <if test="req.calledNumber != null">
                and customer.called_number = #{req.calledNumber}
            </if>
            <if test="req.callStatus != null">
                <if test="req.callStatus == 0">
                    and (robotSession.call_status = 0 or robotSession.call_status is null)
                </if>
                <if test="req.callStatus != 0">
                    and robotSession.call_status = #{req.callStatus}
                </if>

            </if>
            <if test="req.callResult != null">
                and robotSession.call_result = #{req.callResult}
            </if>
            <if test="req.robotId != null">
                and robotSession.robot_id = #{req.robotId}
            </if>
            <if test="req.intentionDes != null">
                and robotSession.intention_des = #{req.intentionDes}
            </if>
            <if test="req.userId != null">
                and customer.user_id = #{req.userId}
            </if>
        </where>
        order by customer.id desc
    </select>
    <select id="pageListReCall" resultType="com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerVO">
        select customer.id,
        customer.called_number as calledNumber,
        customer.variables,
        customer.import_time as importTime,
        customer.imported_by as importedBy,
        customer.tenant_id as tenantId,
        customer.task_id as taskId,
        robotSession.call_status as callStatus,
        robotSession.call_result as callResult,
        robotSession.intention_level as intentType,
        robotSession.recent_intention as intention,
        robotSession.caller_display_number as callerDisplayNumber,
        robotSession.call_start_time as callStartTime,
        robotSession.call_end_time as callEndTime,
        robotSession.call_duration as callDuration,
        robotSession.dialogue_rounds as dialogueRounds,
        robotSession.intention_des as intentionDes,
        robotSession.robot_history_id as robotHistoryId
        from ai_robot_call_tasks_customer customer
        left join ai_robot_session robotSession on customer.id = robotSession.call_tasks_customer_id
        <where>
            and is_test = 0
            <if test="req.id != null">
                and customer.id = #{req.id}
            </if>
            <if test="req.taskId != null">
                and customer.task_id = #{req.taskId}
            </if>
            <if test="req.calledNumber != null">
                and customer.called_number = #{req.calledNumber}
            </if>
            <if test="req.callStatus != null">
                <if test="req.callStatus == 0">
                    and (robotSession.call_status = 0 or robotSession.call_status is null)
                </if>
                <if test="req.callStatus != 0">
                    and robotSession.call_status = #{req.callStatus}
                </if>
            </if>
            <if test="req.callResult != null">
                and robotSession.call_result = #{req.callResult}
            </if>
            <if test="req.robotId != null">
                and robotSession.robot_id = #{req.robotId}
            </if>
            <if test="req.intentionDes != null">
                and robotSession.intention_des = #{req.intentionDes}
            </if>
            <if test="req.userId != null">
                and customer.user_id = #{req.userId}
            </if>
        </where>
        order by customer.id desc
    </select>
    <select id="getCallTasksCustomers" resultMap="BaseResultMap">
        select *
        from ai_robot_call_tasks_customer
        where task_id = #{taskId}
          and is_test = 0
          <if test="maxReplayTimes != null">
              and call_rounds &lt;= #{maxReplayTimes}
          </if>
        <if test="maxReplayTimes == null">
            and call_rounds =0
        </if>
         and dup_call_status =0
        order by id asc
            limit #{robotConcurrency}
    </select>
    <select id="selectRobotCount" resultType="java.lang.Integer">
        select count(id) - count(last_session_id)
        from ai_robot_call_tasks_customer
        where task_id = #{taskId}  and is_test = 0
    </select>
    <select id="selectOneBySessionId"
            resultType="com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer">
        select *
        from ai_robot_call_tasks_customer
        where last_session_id = #{sessionId}
    </select>
    <select id="countByBizUniqueId" resultType="java.lang.Long">
        select count(1)
        from ai_robot_call_tasks_customer
        where biz_unique_id = #{bizUniqueId}
          and is_test = 0
          and task_id = #{taskId}
    </select>

</mapper>
