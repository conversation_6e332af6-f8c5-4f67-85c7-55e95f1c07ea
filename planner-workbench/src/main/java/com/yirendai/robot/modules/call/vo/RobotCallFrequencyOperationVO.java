package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 频次规则操作记录展示
 */
@Data
@ApiModel(value = "RobotCallFrequencyOperationVO", description = "频次规则操作记录展示")
public class RobotCallFrequencyOperationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime createTime;
}
