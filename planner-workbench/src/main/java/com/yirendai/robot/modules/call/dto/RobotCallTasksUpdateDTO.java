/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.robot.modules.call.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 外呼通话任务表数据修改
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
public class RobotCallTasksUpdateDTO {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "任务ID")
    @NotNull(message = "任务ID不能为空")
    private Long id;

    @ApiModelProperty(value = "任务名称")
    @NotEmpty(message = "任务名称不能为空")
    private String taskName;

    /**
     * {@link com.yirendai.robot.modules.call.enums.PriorityEnum}
     */
    @ApiModelProperty(value = "优先级（0：低，1：中，2：高）")
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    @ApiModelProperty(value = "最大并发数")
    @NotNull(message = "最大并发数不能为空")
    private Integer maxConcurrency;

    /**
     * {@link com.yirendai.robot.modules.call.enums.ValidityTypeEnum}
     */
    @ApiModelProperty(value = "任务有效期类型（0：日期类型，1：无终止日期）")
    @NotNull(message = "任务有效期类型不能为空")
    private Integer validityType;

    @ApiModelProperty(value = "任务开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "任务结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "呼叫时间段开始")
    @JsonFormat(pattern = "HH:mm[:ss[.SSS]]")
    private LocalTime callTimeStart;

    @ApiModelProperty(value = "呼叫时间段结束")
    @JsonFormat(pattern = "HH:mm[:ss[.SSS]]")
    private LocalTime callTimeEnd;

    @ApiModelProperty(value = "启动星期（1：星期一，2：星期二，3：星期三，4：星期四，5：星期五，6：星期六，7：星期日）")
    @NotEmpty(message = "启动星期不能为空")
    private List<Integer> startDay;

    @ApiModelProperty(value = "线路ID")
    @NotEmpty(message = "线路不能为空")
    private List<Long> lineId;

    /**
     * {@link com.yirendai.robot.modules.call.enums.TaskStatusEnum}
     */
    @ApiModelProperty(value = "任务状态（0：待启动，1：执行中，2：已暂停，3：已结束）" ,hidden = true)
    private Integer taskStatus;


    @ApiModelProperty(value = "任务批次号")
    private String taskNo;

    @ApiModelProperty(value = "是否开启重播(0否1开启)")
    private Integer isReplayEnabled;

    @ApiModelProperty(value = "重报条件对应状态多个用,分割")
    private String replayConditions;

    @ApiModelProperty(value = "重播间隔*分钟")
    private Integer replayIntervalMinutes;

    @ApiModelProperty(value = "重播次数")
    private Integer maxReplayTimes;

}
