package com.yirendai.robot.modules.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.robot.modules.call.entity.RobotCallTasks;
import com.yirendai.robot.modules.call.vo.RobotCallTasksQueryReqVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksSelectVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外呼通话任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
public interface RobotCallTasksMapper extends BaseMapper<RobotCallTasks> {

    int updateRobotVersion(@Param("robotId")Long robotId,@Param("robotVersion")Integer robotVersion);

    int selectRobotCount(@Param("robotId")Long robotId);

    RobotCallTasks selectByIdAndTenantId(@Param("id")Long id,@Param("tenantId") String tenantId);

    /**
     * 分页查询
     * @param queryReqVO
     * @param page
     * @return
     */
    IPage<RobotCallTasksVO> pageList(@Param("req") RobotCallTasksQueryReqVO queryReqVO,@Param("page") IPage<RobotCallTasksVO> page);


    /**
     * 查询租户下所有启动状态的任务
     * @param tenantId tenantId
     * @return
     */
    List<RobotCallTasks> getEnableCallTasks(@Param("tenantId")  String tenantId );

    /**
     *  更新执行中状态
     * @param taskId
     * @param runningStatus
     */
    void updateRunningStatus(@Param("taskId")Long taskId,@Param("runningStatus") Integer runningStatus);

    /**
     *  查询状态
     * @param taskId
     * @return
     */
    Integer getStatus(@Param("taskId")Long taskId);

    /**
     * 根据任务名称查询
     * @param taskName
     * @param tenantId
     * @param id 任务ID
     * @return
     */
    int selectCountByTaskName(@Param("taskName") String taskName,@Param("tenantId")  String tenantId,@Param("id") Long id);

    /**
     * 更新任务
     * @param updateTask
     */
    void updateByIdSelf(@Param("req") RobotCallTasks updateTask);

    List<RobotCallTasksSelectVO>  selectTaskList(@Param("tenantId") String tenantId);

    /**
     * 根据任务编号查询
     * @param tenantId
     * @param taskNo
     * @return
     */
    RobotCallTasks getByTaskNo(String tenantId, String taskNo);
}
