package com.yirendai.robot.modules.call.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 简单的机器人下拉项
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "RobotOptionVO", description = "机器人下拉选项")
public class RobotOptionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机器人ID")
    private Long id;

    @ApiModelProperty(value = "机器人名称")
    private String name;
}
