package com.yirendai.robot.modules.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.robot.modules.call.dto.CustomerAddDTO;
import com.yirendai.robot.modules.call.dto.SelectedCustomerAddDTO;
import com.yirendai.robot.modules.call.entity.RobotCallTasks;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerDetailVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerQueryReqVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerVO;
import com.yirendai.robot.modules.robot.entity.RobotAttributeVariable;
import com.yirendai.workbench.wrapper.dto.CallCenterTask;
import org.apache.poi.ss.usermodel.Workbook;
import org.springblade.core.tool.api.R;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 外呼通话任务客户号码导入表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
public interface IRobotCallTasksCustomerService extends IService<RobotCallTasksCustomer> {

    /**
     * 获取变量
     * @param taskId
     * @return
     */
    R<List<RobotAttributeVariable>> getAttribute(Long taskId);

    /**
     * 下载模板
     * @param robotCallTasks
     * @return
     */
    Workbook downTemplate(RobotCallTasks robotCallTasks);


    /**
     * 导入用户 此方法不再使用
     * @param file
     * @param taskId
     * @return
     */
    @Deprecated
    R<String> importCustomer(MultipartFile file, Long taskId,Integer importType);

    /**
     * 导入用户
     * @param file
     * @param taskId
     * @return
     */
    R<CallCenterTask> importCustomerV2(MultipartFile file, Long taskId, Integer importType);

    /**
     * 添加号码
     * @param customerAddDTO
     * @param isTest 是否是测试 0否1日
     * @return
     */
    R<RobotCallTasksCustomer> addCustomer(CustomerAddDTO customerAddDTO,Integer isTest);

    /**
     * 通话任务用户列表
     * @param queryReqVO
     * @param page
     * @return
     */
    IPage<RobotCallTasksCustomerVO> pageList(RobotCallTasksCustomerQueryReqVO queryReqVO, IPage<RobotCallTasksCustomerVO> page);
    /**
     * 通话重播列表
     * @param queryReqVO
     * @param page
     * @return
     */
    IPage<RobotCallTasksCustomerVO> pageReCall(RobotCallTasksCustomerQueryReqVO queryReqVO, IPage<RobotCallTasksCustomerVO> page);

    /**
     * 删除号码
     * @param taskCustomerId
     * @return
     */
    R delete(Long taskCustomerId);

    /**
     * 通话任务用户详情
     * @param taskCustomerId
     * @return
     */
    RobotCallTasksCustomerDetailVO detail(Long taskCustomerId);


    /**
     * 根据任务ID获取任待拨打务客户列表
     * @param taskId
     * @param robotConcurrency 获取条数
     * @return
     */
    List<RobotCallTasksCustomer> getCallTasksCustomers(Long taskId, Integer robotConcurrency,Integer maxReplayTimes);

    /**
     * 待呼叫总数
     * @param taskId
     * @return
     */

    public int selectRobotCount(  Long taskId);

    /**
     * 根据sessionId查询一条记录
     * @param sessionId
     * @return
     */
    RobotCallTasksCustomer selectOneBySessionId(Long sessionId);

    /**
     * 批量添加客户到AI外呼任务
     * @param selectedCustomerAddDTO
     * @return 成功条数
     */
    R<Integer> addSelectedCustomer(@Valid SelectedCustomerAddDTO selectedCustomerAddDTO );

    IPage<RobotCallTasksCustomerVO>  pageListByUserId(RobotCallTasksCustomerQueryReqVO queryReqVO, IPage<RobotCallTasksCustomerVO> page);

    int copyCustomer(Long targetTaskId, Long sourceTaskId);

    /**
     * 导出
     * @param queryReqVO
     * @return
     */
    CallCenterTask exportBatch(RobotCallTasksCustomerQueryReqVO queryReqVO);
}
