/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright customer,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  customer, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.yirendai.robot.modules.call.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.yirendai.robot.enums.RobotResult;
import com.yirendai.robot.enums.YesOrNoEnum;
import com.yirendai.robot.exception.RobotException;
import com.yirendai.robot.modules.robot.entity.RobotAttributeVariable;
import com.yirendai.robot.modules.call.dto.CustomerAddDTO;
import com.yirendai.robot.modules.call.entity.RobotCallTasks;
import com.yirendai.robot.modules.call.service.IRobotCallTasksCustomerService;
import com.yirendai.robot.modules.call.service.IRobotCallTasksService;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerDetailVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerQueryReqVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerVO;
import com.yirendai.robot.modules.session.entity.RobotSession;
import com.yirendai.robot.modules.session.service.IRobotSessionService;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.vo.req.QuerUserInfoDTO;
import com.yirendai.workbench.vo.res.callcenter.BatchOperationResult;
import com.yirendai.workbench.wrapper.CustomerInfoWrapper;
import com.yirendai.workbench.wrapper.dto.CallCenterTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;


/**
 * 外呼通话任务客户号码导入表 控制器
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ai/robot/call/tasks/customer")
@Api(value = "外呼通话任务客户号码导入表", tags = "外呼通话任务客户号码导入表接口")
@Slf4j
public class RobotCallTasksCustomerController  {

	private IRobotCallTasksCustomerService robotCallTasksCustomerService;

	@Autowired
	private IRobotCallTasksService robotCallTasksService;

	@Autowired
	private IRobotSessionService robotSessionService;

	@Autowired
	private RestTemplate restTemplate;

	@javax.annotation.Resource
	private TenantServiceHolder tenantServiceHolder;
	/**
	 *  准备添加号码，获取变量
	 */
	@GetMapping("/getAttribute")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "准备添加号码，获取机器人变量", notes = "传入任务ID")
	public R<List<RobotAttributeVariable>> getAttribute(@ApiParam(value = "任务ID", required = true) @RequestParam Long taskId) {
		return robotCallTasksCustomerService.getAttribute(taskId);
	}

	/**
	 *  下载模板
	 */
	@GetMapping("/downTemplate")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "下载模板", notes = "传入任务ID")
	public void downTemplate(@ApiParam(value = "任务ID", required = true) @RequestParam Long taskId, HttpServletResponse response) {
		RobotCallTasks robotCallTasks = robotCallTasksService.getById(taskId);
		if (robotCallTasks == null) {
			log.error("任务不存在");
			response.setStatus(HttpServletResponse.SC_NOT_FOUND);
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-Type", "text/plain;charset=UTF-8");
			try {
				response.getWriter().write("任务不存在");
			} catch (IOException e) {
				log.error("任务不存在", e);
			}
			return;
		}
		Workbook workbook =  robotCallTasksCustomerService.downTemplate(robotCallTasks);
		if(workbook == null){
			log.error("下载模板异常");
			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-Type", "text/plain;charset=UTF-8");
			try {
				response.getWriter().write("下载模板异常");
			} catch (IOException e) {
				log.error("下载模板异常", e);
			}
		}


		// 将工作簿写入响应输出流
		try {
			// 设置响应头
			response.setContentType("application/vnd.ms-excel");
			String fileName = URLEncoder.encode(robotCallTasks.getTaskName() + "_AI外呼导入号码模板.xlsx", "UTF-8");
			response.setHeader("Content-Disposition", "attachment; filename="+fileName);
			workbook.write(response.getOutputStream());
		} catch (IOException e) {
			log.error("下载模板异常", e);
		}finally {
			try {
				workbook.close();
			} catch (IOException e) {
				log.info("关闭工作簿异常", e);
			}
		}

	}

	/**
	 * 此导入接口不再使用，请使用importCustomerV2
	 * @param file
	 * @param taskId
	 * @param importType
	 * @return
	 */
	@PostMapping("/importCustomer")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "导入用户", notes = "传入任务ID和文件")
	@Deprecated
	public R<String> importCustomer(@RequestParam("file") MultipartFile file,@ApiParam(value = "任务ID", required = true) @RequestParam Long taskId,@ApiParam(value = "导入类型 0手机号，1用户ID", required = true) @RequestParam Integer importType) {
		R<String> r= robotCallTasksCustomerService.importCustomer(file,taskId, importType);
		return r;
	}

	@PostMapping("/importCustomerV2")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "导入用户", notes = "传入任务ID和文件")
	@Deprecated
	public R<CallCenterTask> importCustomerV2(@RequestParam("file") MultipartFile file, @ApiParam(value = "任务ID", required = true) @RequestParam Long taskId, @ApiParam(value = "导入类型 0手机号，1用户ID", required = true) @RequestParam Integer importType) {
		R<CallCenterTask> r= robotCallTasksCustomerService.importCustomerV2(file,taskId, importType);
		return r;
	}


	@PostMapping("/querUserInfo")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查询用户信息", notes = "根据用户ID和租户ID查询用户信息")
	public R<SimpleUserInfoDto> querUserInfo(@RequestBody @Valid QuerUserInfoDTO querUserInfoDTO) {
		CustomerInfoWrapper customerInfoWrapper = tenantServiceHolder.getCustomerInfoWrapper(querUserInfoDTO.getTenantId());
		SimpleUserInfoDto response = customerInfoWrapper.getUserBasicInfoByUserId(querUserInfoDTO.getUserId(), querUserInfoDTO.getUserId());
		if(response != null){
			return R.data(response);
		}else{
			return R.fail("用户不存在");
		}
	}

	/**
	 *  添加号码
	 */
	@PostMapping("/addCustomer")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "添加号码", notes = "传入CustomerAddDTO")
	public R  addCustomer(@Valid @RequestBody CustomerAddDTO customerAddDTO) {
		return robotCallTasksCustomerService.addCustomer(customerAddDTO, YesOrNoEnum.NO.getValue());
	}

	/**
	 *  通话任务用户列表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "通话任务用户列表", notes = "传入RobotCallTasksCustomerQueryReqVO")
	public R<IPage<RobotCallTasksCustomerVO>> page(RobotCallTasksCustomerQueryReqVO queryReqVO, Query query) {
		if (queryReqVO.getUserId() == null && StringUtils.isNotBlank(queryReqVO.getCustomerId())) {
			queryReqVO.setUserId(queryReqVO.getCustomerId());
		}
		if(queryReqVO.getTaskId() == null && StringUtils.isBlank(queryReqVO.getUserId())){
			return R.fail("任务ID和用户ID不能同时为空");
		}
		IPage<RobotCallTasksCustomerVO> pages = null;
		if(queryReqVO.getTaskId() != null){
			pages = robotCallTasksCustomerService.pageList(queryReqVO, Condition.getPage(query));
		}else  {
			pages = robotCallTasksCustomerService.pageListByUserId(queryReqVO, Condition.getPage(query));
		}

		return R.data(pages);
	}

	/**
	 *  通话任务用户列表 重播
	 */
	@GetMapping("/pageReCall")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "通话任务用户列表", notes = "传入RobotCallTasksCustomerQueryReqVO")
	public R<IPage<RobotCallTasksCustomerVO>> pageReCall(RobotCallTasksCustomerQueryReqVO queryReqVO, Query query) {

		IPage<RobotCallTasksCustomerVO> pages = null;
		pages = robotCallTasksCustomerService.pageReCall(queryReqVO, Condition.getPage(query));

		return R.data(pages);
	}

	@GetMapping("/exportBatch")
	@ApiOperation("批量导出通话记录分析结果")
	public R<BatchOperationResult> exportBatch( RobotCallTasksCustomerQueryReqVO queryReqVO) {
		log.info("exportBatch:{}  ", queryReqVO );
		CallCenterTask taskInfo =  robotCallTasksCustomerService.exportBatch(  queryReqVO);
		return R.data(new BatchOperationResult(true, taskInfo.getId(), taskInfo.getTaskName()));
	}





	/**
	 *  通话任务用户详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "通话任务用户详情", notes = "传入taskCustomerId")
	public R<RobotCallTasksCustomerDetailVO> detail(@ApiParam(value = "用户记录ID", required = true) @RequestParam Long taskCustomerId) {
		RobotCallTasksCustomerDetailVO robotCallTasksCustomerDetailVO = robotCallTasksCustomerService.detail(taskCustomerId);
		return R.data(robotCallTasksCustomerDetailVO);
	}

	/**
	 *  删除号码
	 */
	@GetMapping("/delete")
	@ApiOperation(value = "删除号码", notes = "传入taskCustomerId")
	public R delete(@ApiParam(value = "用户记录ID", required = true) @RequestParam Long taskCustomerId) {
		return  robotCallTasksCustomerService.delete(taskCustomerId);
	}


	@ApiOperation(value = "获取录音文件", notes = "获取录音文件")
	@GetMapping("/getAudio")
	public ResponseEntity<Resource> streamAudio(
			@RequestParam("uuid") String uuid,
			@RequestHeader HttpHeaders headers) {

		RobotSession robotSession = robotSessionService.getByUuid(uuid);
		if (robotSession == null){
			throw new RobotException(RobotResult.SESSION_NOT_EXIST);
		}

		try {
			String idcUrl = robotSession.getCallVideo();
			idcUrl = StringUtils.replace(idcUrl, "http://yr-open-callhub-web.fso-yrcf-appserver.paas.corp/", "http://yr-open-callhub-web.fso-yrcf-appserver.paas.idc/");
			// 发送请求到外部链接获取音频文件
			ResponseEntity<Resource> response = restTemplate.exchange(idcUrl, HttpMethod.GET, null, Resource.class);

			if (response.getStatusCode().is2xxSuccessful()) {
				HttpHeaders responseHeaders = response.getHeaders();
				Resource resource = response.getBody();

				return ResponseEntity.ok()
						.headers(responseHeaders)
						.body(resource);
			} else {
				return ResponseEntity.status(response.getStatusCode()).build();
			}
		} catch (Exception e) {
			log.error("获取音频失败 uuid:{}", uuid, e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
		}
	}
	/**
	 * 拷贝接听失败的用户到另一个任务
	 * @param targetTaskId
	 * @param sourceTaskId
	 * @return
	 */
	@PostMapping("/copyCustomer")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "拷贝接听失败的用户到另一个任务", notes = "拷贝接听失败的用户到另一个任务")
	public R copyCustomer(@ApiParam(value = "目录任务ID", required = true) @RequestParam Long targetTaskId,@ApiParam(value = "源任务ID", required = true) @RequestParam Long sourceTaskId) {
		return R.data(robotCallTasksCustomerService.copyCustomer(targetTaskId,sourceTaskId));
	}
}
