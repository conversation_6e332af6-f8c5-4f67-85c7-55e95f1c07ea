package com.yirendai.robot.modules.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yirendai.robot.modules.call.dto.TaskCallCountDTO;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerQueryReqVO;
import com.yirendai.robot.modules.call.vo.RobotCallTasksCustomerVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 外呼通话任务客户号码导入表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
public interface RobotCallTasksCustomerMapper extends BaseMapper<RobotCallTasksCustomer> {

    /**
     * 根据被叫号码查询
     * @param taskId
     * @param calledNumber
     * @return
     */
    Long countByCalledNumber(Long taskId, String calledNumber);

    /**
     * 根据任务ID查询通话次数
     * @param taskIds
     * @return
     */
    List<TaskCallCountDTO> selectTaskCallCount(@Param("taskIds") List<Long> taskIds);

    /**
     * 分页查询
     * @param queryReqVO
     * @param page
     * @return
     */
    IPage<RobotCallTasksCustomerVO> pageList(@Param("req")RobotCallTasksCustomerQueryReqVO queryReqVO,@Param("page") IPage<RobotCallTasksCustomerVO> page);
    /**
     * 分页查询重播
     * @param queryReqVO
     * @param page
     * @return
     */
    IPage<RobotCallTasksCustomerVO> pageListReCall(@Param("req")RobotCallTasksCustomerQueryReqVO queryReqVO,@Param("page") IPage<RobotCallTasksCustomerVO> page);

    /**
     * 根据任务客户ID删除
     * @param taskCustomerId
     * @return
     */
    int deleteByIdAndSessionIsNull(@Param("taskCustomerId") Long taskCustomerId);

    /**
     * 根据任务ID查询任务客户
     * @param taskId
     * @param robotConcurrency 获取条数
     * @return
     */
    List<RobotCallTasksCustomer> getCallTasksCustomers(@Param("taskId") Long taskId,@Param("robotConcurrency") Integer robotConcurrency,@Param("maxReplayTimes")Integer maxReplayTimes);

    /**
     * 待呼叫总数
     * @param taskId
     * @return
     */
    int selectRobotCount(@Param("taskId") Long taskId);

    /**
     * 根据会话ID查询
     * @param sessionId
     * @return
     */
    RobotCallTasksCustomer selectOneBySessionId(Long sessionId);

    /**
     * 拷贝接听失败的用户到另一个任务
     * @param targetTaskId
     * @param sourceTaskId
     * @return
     */
    int copyCustomer(@Param("targetTaskId") Long targetTaskId,@Param("sourceTaskId") Long sourceTaskId,@Param("importedBy") String importedBy);

    Long countByBizUniqueId(Long taskId, String bizUniqueId);

}
