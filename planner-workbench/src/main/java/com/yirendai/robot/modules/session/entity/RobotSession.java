package com.yirendai.robot.modules.session.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * AI呼叫机器人会话表
 * </p>
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_robot_session")
@ApiModel(value = "RobotSession对象", description = "AI呼叫机器人会话表")
public class RobotSession implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "呼叫唯一ID")
    private String uuid;

    @ApiModelProperty(value = "机器人ID")
    private Long robotId;

    @ApiModelProperty(value = "机器人版本号ID")
    private Long robotHistoryId;

    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "呼叫类型，0-呼出； 1-呼入")
    private Integer callType;

    @ApiModelProperty(value = "被叫号码（机器人要拨打的电话号码）")
    private String calledNumber;

    @ApiModelProperty(value = "被呼叫方用户ID")
    private String userId;

    @ApiModelProperty(value = "主叫号码（客户侧显示的号码）")
    private String callerDisplayNumber;

    @ApiModelProperty(value = "替换变量（格式：[变量名称]:{变量赋值内容}，多个变量换行显示）,例如：name:{John}\n age:{25}")
    private String variables;
    /**
     * 主叫方昵称
     */
    @ApiModelProperty(value = "主叫方昵称")
    private String callerIdName;
    /**
     * 响铃时长
     */
    @ApiModelProperty(value = "响铃时长")
    private Long ring;
    /**
     * 客户响铃时长
     */
    @ApiModelProperty(value = "客户响铃时长")
    private Long csRing;
    /**
     * 线路ID
     */
    @ApiModelProperty(value = "线路ID")
    private Integer gatewayId;
    /**
     * 挂断方（1:客户Client 2 :坐席Staff）
     */
    @ApiModelProperty(value = "挂断方（1:客户Client 2 :坐席Staff）")
    private Integer hangupBy;
    /**
     * 挂断原因
     */
    @ApiModelProperty(value = "挂断原因")
    private String hangupCause;
    /**
     * 挂断原因描述
     */
    @ApiModelProperty(value = "挂断原因描述")
    private String hangupDisposition;

    @ApiModelProperty(value = "呼叫状态（0:未拨打, 1:拨打中, 2:已接通, 3:未接通）")
    private Integer callStatus;

    @ApiModelProperty(value = "呼叫结果（未接通原因）（0:无, 1:无人接听, 2:关机, 3:停机, 4:空号, 5:用户拒接, 6:线路忙, 7:线路拦截, 8:系统拦截, 12:超频拦截）")
    private Integer callResult;

    @ApiModelProperty(value = "音色ID")
    private Long toneId;

    @ApiModelProperty(value = "打断是否开启,0-关闭，1-开启")
    private Integer interruptEnable;

    @ApiModelProperty(value = "意向等级")
    private Integer intentionLevel;

    @ApiModelProperty(value = "意向名称")
    private String intentionDes;

    @ApiModelProperty(value = "最近意向")
    private String recentIntention;

    @ApiModelProperty(value = "当前重播次数")
    private Integer callRounds;

    @ApiModelProperty(value = "拨打时间")
    private LocalDateTime callStartTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime callEndTime;

    @ApiModelProperty(value = "通话时长（最后一次通话总时长，单位：秒，含响铃）")
    private Long callDuration;

    @ApiModelProperty(value = "对话轮数（最后一次通话总轮数，以主动发起与回应为一轮）")
    private Integer dialogueRounds;

    @ApiModelProperty(value = "电话音频地址")
    private String callVideo;

    @ApiModelProperty(value = "音频时长")
    private Integer audioDuration;

    @ApiModelProperty(value = "拨打文字记录")
    private String callText;

    @ApiModelProperty(value = "记录创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "记录更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除（0-未删除，1-已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    @ApiModelProperty(value = "呼叫用户ID")
    private Long callTasksCustomerId;

}
