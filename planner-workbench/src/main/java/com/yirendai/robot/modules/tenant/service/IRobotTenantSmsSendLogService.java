package com.yirendai.robot.modules.tenant.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yirendai.robot.modules.tenant.dto.RobotTenantSmsSendLogDTO;
import com.yirendai.robot.modules.tenant.dto.RobotTenantSmsSendLogPageDTO;
import com.yirendai.robot.modules.tenant.dto.RobotTenantSmsSendLogSearchConditionDTO;
import com.yirendai.robot.modules.tenant.entity.RobotTenantSmsSendLog;
import com.yirendai.workbench.vo.res.callcenter.BatchOperationResult;

import java.time.LocalDateTime;

/**
 * <p>
 * 短信发送记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface IRobotTenantSmsSendLogService extends IService<RobotTenantSmsSendLog> {

    Page<RobotTenantSmsSendLogDTO> page(RobotTenantSmsSendLogPageDTO pageDTO);

    BatchOperationResult exportSubmitTask(RobotTenantSmsSendLogSearchConditionDTO conditionDTO);

    void handleSmsReqLog();

    /**
     * 根据客户ID获取短信内容
     * 
     * @param customerId 客户ID
     * @param tenantId 租户ID
     * @return 拼接后的短信内容
     */
    String getMsgByCustomerId(String customerId, String tenantId, LocalDateTime startTime, LocalDateTime endTime);
}
