package com.yirendai.robot.modules.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyOperation;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRecord;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRuleRobot;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRule;
import com.yirendai.robot.modules.call.enums.RobotCallFrequencyOperationTypeEnum;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyOperationMapper;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRecordMapper;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRuleMapper;
import com.yirendai.robot.modules.call.mapper.RobotCallFrequencyRuleRobotMapper;
import com.yirendai.robot.modules.call.service.IRobotCallFrequencyRuleService;
import com.yirendai.robot.modules.call.service.IRobotCallTasksCustomerService;
import com.yirendai.robot.modules.call.vo.*;
import com.yirendai.robot.modules.robot.entity.Robot;
import com.yirendai.robot.modules.robot.service.IRobotService;
import com.yirendai.workbench.util.OwnAuthUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 频次规则业务实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RobotCallFrequencyRuleServiceImpl extends ServiceImpl<RobotCallFrequencyRuleMapper, RobotCallFrequencyRule> implements IRobotCallFrequencyRuleService {

    private final RobotCallFrequencyOperationMapper operationMapper;
    private final RobotCallFrequencyRecordMapper recordMapper;
    private final RobotCallFrequencyRuleRobotMapper ruleRobotMapper;
    private final IRobotService robotService;
    private final IRobotCallTasksCustomerService robotCallTasksCustomerService;

    @Override
    public IPage<RobotCallFrequencyRulePageVO> page(RobotCallFrequencyRuleQueryReq req) {
        IPage<RobotCallFrequencyRulePageVO> page = new Page<>(req.getCurrent(), req.getSize());
        req.setTenantId(AuthUtil.getTenantId());
        IPage<RobotCallFrequencyRulePageVO> result = baseMapper.pageRuleResultList(page, req);
        if (result != null && result.getRecords() != null && !result.getRecords().isEmpty()) {
            String tenantId = AuthUtil.getTenantId();
            List<Long> ruleIds = result.getRecords().stream().map(RobotCallFrequencyRulePageVO::getId).collect(Collectors.toList());

            // 查询所有规则的绑定关系
            List<RobotCallFrequencyRuleRobot> relations = ruleRobotMapper.selectRobotCallFrequencyByRuleIds(ruleIds);
            Map<Long, List<RobotCallFrequencyRuleRobot>> relationMap = relations.stream()
                    .collect(Collectors.groupingBy(RobotCallFrequencyRuleRobot::getRuleId));

            for (RobotCallFrequencyRulePageVO vo : result.getRecords()) {
                List<RobotCallFrequencyRuleRobot> list = relationMap.getOrDefault(vo.getId(), Collections.emptyList());
                vo.setRobotIds(list.stream().map(RobotCallFrequencyRuleRobot::getRobotId).collect(Collectors.toList()));
                vo.setRobotNames(list.stream().map(RobotCallFrequencyRuleRobot::getRobotName).collect(Collectors.toList()));

                // 组装 robots 字段：已绑定当前规则的机器人 + 未绑定任何规则的机器人
                // 添加当前规则已绑定的机器人
                List<RobotOptionVO> robots = list.stream()
                        .map(r -> new RobotOptionVO(r.getRobotId(), r.getRobotName())).collect(Collectors.toList());
                vo.setRobots(robots);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<RobotCallFrequencyRuleOperateResp> create(RobotCallFrequencyRuleCreateReq req) {
        if (req.getRobotIds() == null || req.getRobotIds().isEmpty()) {
            return R.fail("请选择机器人");
        }
        List<Long> robotIds = req.getRobotIds().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (robotIds.isEmpty()) {
            return R.fail("请选择机器人");
        }
        LocalDateTime now = LocalDateTime.now();
        String tenantId = AuthUtil.getTenantId();

        // 如果规则名称为空，生成默认值
        String ruleName = req.getRuleName();
        if (StringUtil.isBlank(ruleName)) {
            ruleName = "一个自然月内，最多可拨打" + req.getLimitCount() + "次";
        }

        RobotCallFrequencyRule entity = new RobotCallFrequencyRule();
        entity.setRuleName(ruleName);
        entity.setLimitCount(req.getLimitCount());
        entity.setStatus(req.getStatus() == null ? 1 : req.getStatus());
        entity.setRemark(req.getRemark());
        entity.setTenantId(tenantId);
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setCreateUser(OwnAuthUtil.getUserId());
        entity.setCreateUserName(OwnAuthUtil.getRealName());
        entity.setUpdateUser(OwnAuthUtil.getUserId());
        entity.setUpdateUserName(OwnAuthUtil.getRealName());
        if (Objects.equals(entity.getStatus(), 1)) {
            entity.setEnabledTime(now);
        }
        this.save(entity);

        List<Long> failedRobotIds = bindRobots(entity, robotIds);

        // 检查是否所有机器人都绑定失败
        if (failedRobotIds.size() == robotIds.size()) {
            // 所有机器人都绑定失败，删除该规则
            this.removeById(entity.getId());
            log.info("新建规则[{}]所有机器人绑定失败，已自动删除该规则", entity.getId());
            return R.data(new RobotCallFrequencyRuleOperateResp(null, failedRobotIds));
        }

        // 新增规则只记录一条操作记录，不区分启用或停用状态
        addOperation(entity.getId(), RobotCallFrequencyOperationTypeEnum.CREATE, null);
        return R.data(new RobotCallFrequencyRuleOperateResp(entity.getId(), failedRobotIds));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<RobotCallFrequencyRuleOperateResp> updateRule(RobotCallFrequencyRuleUpdateReq req) {
        RobotCallFrequencyRule db = this.getById(req.getId());
        if (db == null) {
            return R.fail("规则不存在");
        }
        if (req.getRobotIds() == null || req.getRobotIds().isEmpty()) {
            return R.fail("请选择机器人");
        }
        List<Long> robotIds = req.getRobotIds().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (robotIds.isEmpty()) {
            return R.fail("请选择机器人");
        }

        LocalDateTime now = LocalDateTime.now();
        Integer originalStatus = db.getStatus();
        Integer targetStatus = req.getStatus() == null ? originalStatus : req.getStatus();

        // 如果规则名称为空，生成默认值
        String ruleName = req.getRuleName();
        if (StringUtil.isBlank(ruleName)) {
            ruleName = "一个自然月内，最多可拨打" + req.getLimitCount() + "次";
        }

        db.setRuleName(ruleName);
        db.setLimitCount(req.getLimitCount());
        db.setRemark(req.getRemark());
        db.setStatus(targetStatus);
        if (Objects.equals(targetStatus, 1)) {
            db.setEnabledTime(now);
        }
        db.setUpdateTime(now);
        db.setUpdateUser(OwnAuthUtil.getUserId());
        db.setUpdateUserName(OwnAuthUtil.getRealName());
        this.updateById(db);

        List<RobotCallFrequencyRuleRobot> oldRelations = ruleRobotMapper.selectRobotCallFrequencyByRuleIds(Collections.singleton(db.getId()));
        Set<Long> oldRobotIds = oldRelations.stream().map(RobotCallFrequencyRuleRobot::getRobotId).collect(Collectors.toSet());
        Set<Long> newRobotIds = new HashSet<>(robotIds);

        List<Long> toRemove = oldRobotIds.stream().filter(id -> !newRobotIds.contains(id)).collect(Collectors.toList());
        if (!toRemove.isEmpty()) {
            ruleRobotMapper.deleteByRuleIdAndRobotIds(db.getId(), toRemove);
        }

        List<Long> toAdd = newRobotIds.stream().filter(id -> !oldRobotIds.contains(id)).collect(Collectors.toList());
        List<Long> failedRobotIds = bindRobots(db, toAdd);

        // 检查该规则是否还有绑定的机器人
        List<Long> remainingRobotIds = ruleRobotMapper.selectRobotIdsByRuleId(db.getId());
        if (remainingRobotIds == null || remainingRobotIds.isEmpty()) {
            // 没有绑定的机器人了，删除该规则
            this.removeById(db.getId());
            log.info("编辑规则[{}]后无绑定机器人，已自动删除该规则", db.getId());
            return R.data(new RobotCallFrequencyRuleOperateResp(null, failedRobotIds));
        }

        addOperation(db.getId(), RobotCallFrequencyOperationTypeEnum.UPDATE, null);

        if (!Objects.equals(originalStatus, targetStatus)) {
            if (Objects.equals(targetStatus, 1)) {
                addOperation(db.getId(), RobotCallFrequencyOperationTypeEnum.ENABLE, null);
            } else {
                addOperation(db.getId(), RobotCallFrequencyOperationTypeEnum.DISABLE, null);
            }
        }

        return R.data(new RobotCallFrequencyRuleOperateResp(db.getId(), failedRobotIds));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> changeStatus(RobotCallFrequencyRuleStatusReq req) {
        RobotCallFrequencyRule db = this.getById(req.getId());
        if (db == null) {
            return R.fail("规则不存在");
        }
        Integer status = req.getStatus();
        if (!Objects.equals(status, 0) && !Objects.equals(status, 1)) {
            return R.fail("状态非法");
        }
        if (Objects.equals(db.getStatus(), status)) {
            return R.success("操作成功");
        }
        db.setStatus(status);
        db.setUpdateTime(LocalDateTime.now());
        db.setUpdateUser(OwnAuthUtil.getUserId());
        db.setUpdateUserName(OwnAuthUtil.getRealName());
        if (Objects.equals(status, 1)) {
            db.setEnabledTime(LocalDateTime.now());
        }
        this.updateById(db);

        List<Long> robotIds = ruleRobotMapper.selectRobotIdsByRuleId(db.getId());
        if (!robotIds.isEmpty()) {
            if (Objects.equals(status, 1)) {
                addOperation(db.getId(), RobotCallFrequencyOperationTypeEnum.ENABLE, null);
            } else {
                addOperation(db.getId(), RobotCallFrequencyOperationTypeEnum.DISABLE, null);
            }
        }
        return R.success("操作成功");
    }

    @Override
    public IPage<RobotCallFrequencyOperationVO> operationPage(Long ruleId, Query query) {
        Page<RobotCallFrequencyOperationVO> page = new Page<>(query.getCurrent(), query.getSize());
        IPage<RobotCallFrequencyOperationVO> result = operationMapper.pageRobotFrequencyByRule(page, ruleId);
        if (result != null && result.getRecords() != null) {
            result.getRecords().forEach(vo -> {
                if (vo.getOperationType() != null) {
                    try {
                        RobotCallFrequencyOperationTypeEnum typeEnum = RobotCallFrequencyOperationTypeEnum.valueOf(vo.getOperationType());
                        vo.setOperationType(typeEnum.getDesc());
                    } catch (IllegalArgumentException ignored) {
                        // 保持原始值
                    }
                }
            });
        }
        return result;
    }

    @Override
    public List<RobotOptionVO> publishedRobotOptions(String keyword) {
        String tenantId = AuthUtil.getTenantId();
        List<Robot> robots = robotService.listByStatusAndTenant(tenantId);
        if (robots == null || robots.isEmpty()) {
            return Collections.emptyList();
        }
        List<RobotCallFrequencyRuleRobot> relations = ruleRobotMapper.selectList(null);
        Set<Long> boundRobotIds = relations.stream().map(RobotCallFrequencyRuleRobot::getRobotId).collect(Collectors.toSet());
        String lowerKeyword = keyword == null ? null : keyword.toLowerCase();
        return robots.stream()
                .filter(robot -> !boundRobotIds.contains(robot.getId()))
                .filter(robot -> {
                    if (lowerKeyword == null) {
                        return true;
                    }
                    String name = robot.getRobotName();
                    return name != null && name.toLowerCase().contains(lowerKeyword);
                })
                .map(robot -> new RobotOptionVO(robot.getId(), robot.getRobotName()))
                .collect(Collectors.toList());
    }

    private boolean existsOtherRule(Long robotId, Long excludeRuleId) {
        RobotCallFrequencyRuleRobot relation = ruleRobotMapper.selectRobotCallFrequencyByRobotId(robotId);
        return relation != null && !Objects.equals(relation.getRuleId(), excludeRuleId);
    }

    private List<Long> bindRobots(RobotCallFrequencyRule rule, Collection<Long> robotIds) {
        if (robotIds == null || robotIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> failed = new ArrayList<>();
        List<Long> success = new ArrayList<>();
        String tenantId = rule.getTenantId();
        LocalDateTime now = LocalDateTime.now();
        for (Long robotId : robotIds) {
            if (robotId == null) {
                continue;
            }
            try {
                if (existsOtherRule(robotId, rule.getId())) {
                    failed.add(robotId);
                    continue;
                }
                Robot robot = robotService.getById(robotId);
                if (robot == null || robot.getAlreadyPublish() == null || robot.getAlreadyPublish() == 0) {
                    failed.add(robotId);
                    continue;
                }
                RobotCallFrequencyRuleRobot relation = new RobotCallFrequencyRuleRobot();
                relation.setRuleId(rule.getId());
                relation.setRobotId(robotId);
                relation.setRobotName(robot.getRobotName());
                relation.setTenantId(StringUtil.isNotBlank(robot.getTenantId()) ? robot.getTenantId() : tenantId);
                relation.setCreateTime(now);
                ruleRobotMapper.insert(relation);
                success.add(robotId);
            } catch (Exception ex) {
                log.warn("绑定机器人失败 ruleId={} robotId={} err={}", rule.getId(), robotId, ex.getMessage());
                failed.add(robotId);
            }
        }
        return failed;
    }

    private void addOperation(Long ruleId, RobotCallFrequencyOperationTypeEnum type, String remark) {
        RobotCallFrequencyOperation record = new RobotCallFrequencyOperation();
        record.setRuleId(ruleId);
        record.setOperationType(type.name());
        record.setOperatorId(OwnAuthUtil.getUserId());
        record.setOperatorName(OwnAuthUtil.getRealName());
        record.setRemark(remark);
        record.setCreateTime(LocalDateTime.now());
        operationMapper.insert(record);
    }
}
