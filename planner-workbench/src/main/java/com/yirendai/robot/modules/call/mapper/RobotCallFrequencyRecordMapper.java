package com.yirendai.robot.modules.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yirendai.robot.modules.call.entity.RobotCallFrequencyRecord;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 机器人外呼接通记录 Mapper
 */
public interface RobotCallFrequencyRecordMapper extends BaseMapper<RobotCallFrequencyRecord> {

    /**
     * 统计指定机器人列表在指定时间范围内对某手机号的接通次数
     * @param robotIds 机器人ID列表
     * @param phone 手机号
     * @param startTime 开始时间（自然月第一天）
     * @param endTime 结束时间（自然月最后一天）
     * @return 接通次数
     */
    int countByRobotIdsAndPhoneInPeriod(@Param("robotIds") java.util.List<Long> robotIds,
                                        @Param("phone") String phone,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);
}
