package com.yirendai.robot.modules.call.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yirendai.robot.constant.RobotConstant;
import com.yirendai.robot.exception.RobotException;
import com.yirendai.robot.enums.RobotCallResult;
import com.yirendai.robot.enums.RobotCallStatus;
import com.yirendai.robot.enums.YesOrNoEnum;
import com.yirendai.robot.modules.attribute.mapper.RobotAttributeVariableMapper;
import com.yirendai.robot.modules.call.dto.CellDto;
import com.yirendai.robot.modules.call.dto.CustomerAddDTO;
import com.yirendai.robot.modules.call.dto.SelectedCustomerAddDTO;
import com.yirendai.robot.modules.call.entity.RobotCallTasks;
import com.yirendai.robot.modules.call.entity.RobotCallTasksCustomer;
import com.yirendai.robot.modules.call.mapper.RobotCallTasksCustomerMapper;
import com.yirendai.robot.modules.call.service.IRobotCallTasksCustomerService;
import com.yirendai.robot.modules.call.service.IRobotCallTasksService;
import com.yirendai.robot.modules.call.vo.*;
import com.yirendai.robot.modules.robot.entity.RobotAttributeVariable;
import com.yirendai.robot.modules.robot.entity.RobotHistory;
import com.yirendai.robot.modules.robot.service.IRobotService;
import com.yirendai.robot.modules.session.entity.RobotSession;
import com.yirendai.robot.modules.session.entity.RobotSessionSummary;
import com.yirendai.robot.modules.session.mapper.RobotSessionMapper;
import com.yirendai.robot.modules.session.mapper.RobotSessionSummaryMapper;
import com.yirendai.robot.modules.session.vo.RobotSessionSummaryVO;
import com.yirendai.robot.modules.session.vo.RobotSessionVO;
import com.yirendai.voiceaiserver.util.ExcelUtil;
import com.yirendai.workbench.config.CallcenterProperty;
import com.yirendai.workbench.enums.ImportTypeEnum;
import com.yirendai.workbench.enums.callcenter.TaskTypeEnum;
import com.yirendai.workbench.model.callcenter.ImportRobotTaskCustomerParam;
import com.yirendai.workbench.model.callcenter.RobotAttributeVariableDto;
import com.yirendai.workbench.model.callcenter.SimpleUserInfoDto;
import com.yirendai.workbench.service.callcenter.TenantServiceHolder;
import com.yirendai.workbench.util.DesensitizationUtil;
import com.yirendai.workbench.util.JsonUtilExt;
import com.yirendai.workbench.util.OwnAuthUtil;
import com.yirendai.workbench.util.UploadUtil;
import com.yirendai.workbench.wrapper.CustomerInfoWrapper;
import com.yirendai.workbench.wrapper.TaskScheduleWrapper;
import com.yirendai.workbench.wrapper.dto.CallCenterTask;
import com.yirendai.workbench.wrapper.dto.CallCenterTaskCreateDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_PATTERN;
import static com.yirendai.workbench.constant.TaskScheduleConstants.AI_TASK_CUSTOMER_EXPORT_TASK;
import static com.yirendai.workbench.constant.TaskScheduleConstants.AI_TASK_CUSTOMER_IMPORT_TASK;

/**
 * <p>
 * 外呼通话任务客户号码导入表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2025-02-17
 */
@Service
@Slf4j
public class RobotCallTasksCustomerServiceImpl extends ServiceImpl<RobotCallTasksCustomerMapper, RobotCallTasksCustomer> implements IRobotCallTasksCustomerService {

    private static final String callUserNameKey = "callUserName";

    @Resource
    RobotAttributeVariableMapper robotAttributeVariableMapper;

    @Autowired
    IRobotCallTasksService robotCallTasksService;

    @Autowired
    IRobotService robotService;

    @Resource
    RobotSessionMapper robotSessionMapper;

    @Resource
    RobotSessionSummaryMapper robotSessionSummaryMapper;

    @Resource
    private TenantServiceHolder tenantServiceHolder;

    @Value("${robot.domain:http://planner-workbench3.fso-yrcf-appserver.spider.test}")
    private String robotDomain;
    @Autowired
    private CallcenterProperty callcenterProperty;

    @Autowired
    private TaskScheduleWrapper taskScheduleWrapper;

    /**
     * 获取变量
     * @param taskId
     * @return
     */
    @Override
    public R<List<RobotAttributeVariable>> getAttribute(Long taskId) {
        RobotCallTasks robotCallTasks = robotCallTasksService.getById(taskId);
        if (robotCallTasks == null) {
            return R.fail("任务不存在");
        }
        RobotHistory robotHistory = robotService.getNewRobot(robotCallTasks.getRobotId());
        if (robotHistory == null) {
            return R.fail("机器人不存在");
        }
        List<RobotAttributeVariable> robotAttributeVariableList = selectByHistoryId(robotHistory.getId());
        return R.data(robotAttributeVariableList);
    }

    /**
     * 下载模板
     * @param robotCallTasks
     * @return
     */
    @Override
    public Workbook downTemplate(RobotCallTasks robotCallTasks) {
        RobotHistory robotHistory = robotService.getNewRobot(robotCallTasks.getRobotId());
        if (robotHistory == null) {
            log.info("机器人不存在 robotId={}", robotCallTasks.getRobotId());
            return null;
        }
        List<RobotAttributeVariable> robotAttributeVariableList = selectByHistoryId(robotHistory.getId());
        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("用户手机号");

        // 创建单元格样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置列宽
        sheet.setColumnWidth(0, 30 * 256); // 列宽设置为30个字符宽度

        // 写入数据
        Row row = sheet.createRow(0);

        int colNum = 0;
        for (RobotAttributeVariable cellData :robotAttributeVariableList) {
            sheet.setColumnWidth(colNum, 30 * 256); // 列宽设置为30个字符宽度
            Cell cell1 = row.createCell(colNum++);
            cell1.setCellValue(cellData.getVariableName());
            cell1.setCellStyle(headerStyle);
        }
        return workbook;
    }



    /**
     * 导入用户 此方法不再使用 改用importCustomerV2
     * @param file
     * @param taskId
     * @param importType {@link ImportTypeEnum}
     * @return
     */
    @Override
    @Deprecated
    public R<String> importCustomer(MultipartFile file, Long taskId,Integer importType) {
        String userName = AuthUtil.getUserName();
        String tenantId = AuthUtil.getTenantId();
        LocalDateTime createdAt = LocalDateTime.now();
        R<List<RobotAttributeVariable>> r = getAttribute(taskId);
        if (!r.isSuccess()) {
            log.info("获取变量失败 {} {}", r.getMsg(), taskId);
            return R.fail(r.getMsg());
        }
        CustomerInfoWrapper customerInfoWrapper = tenantServiceHolder.getCustomerInfoWrapper();
        List<RobotAttributeVariable> variableList = r.getData();
        try {
            boolean success = false;
            List<List<CellDto>> data = ExcelUtil.importExcel(file);
            if (data != null && data.size() > 1) {
                    // 校验字段
                    List<CellDto> rowData = data.get(0);
                    if (variableList.size() == rowData.size()  ) {
                        for (int i = 0; i < variableList.size(); i++) {
                            if (!variableList.get(i).getVariableName().equals(rowData.get(i ).getValue())) {
                                log.info("文件中字段内容校验失败 {} {} var={}", taskId, rowData.get(i),variableList.get(i));
                                return R.fail("文件中字段内容校验失败");
                            }
                        }
                    }else{
                        log.info("文件中字段内容校验失败 {} {} var={}", taskId, rowData,variableList );
                        return R.fail("文件中字段内容校验失败");
                    }

                for (int i = 1; i < data.size(); i++) {
                    rowData = data.get(i);
                    if (rowData != null && rowData.size() > 0) {
                        RobotCallTasksCustomer robotCallTasksCustomer = new RobotCallTasksCustomer();
                        if (StringUtils.isBlank(rowData.get(0).getValue()) || rowData.get(0).getIndex() != 0) {
                            log.info("第{}行被叫号码为空 {}", i, rowData.get(0));
                            continue;
                        }

                        else {
                            String calledNumber = rowData.get(0).getValue();
                            String callUserName = null;
                            if (importType==  ImportTypeEnum.USER_ID.getCode()) {
                                String userId = calledNumber;
                                robotCallTasksCustomer.setUserId(userId);
                                SimpleUserInfoDto response = customerInfoWrapper.getUserBasicInfoByUserId(userId, tenantId);
                                if(response != null && response.getMobileNo() != null){
                                    calledNumber = response.getMobileNo();
                                    callUserName = response.getUserName();
                                    Long count = baseMapper.countByCalledNumber(taskId,calledNumber);
                                    if (count > 0) {
                                        log.info("导入号码已存在 {}",rowData.get(0));
                                        continue;
                                    }
                                } else{
                                    log.info("addCustomer用户ID不存在 {}", userId);
                                    continue;
                                }
                            } else {
                                Long count = baseMapper.countByCalledNumber(taskId,rowData.get(0).getValue());
                                if (count > 0) {
                                    log.info("号码已存在 {}",rowData.get(0));
                                    continue;
                                }
                            }

                            // 是否存在客户姓名
                            boolean exitCustomerName = StringUtils.isNotBlank(callUserName);
                            robotCallTasksCustomer.setCalledNumber(calledNumber);
                                StringBuilder value = new StringBuilder();
                                for (int j = 0; j < rowData.size()&&j < variableList.size(); j++) {
                                    if(j  == rowData.get(j).getIndex() ){
                                        String key = variableList.get(j ).getVariableKey();
                                        if (exitCustomerName && key.equals(callUserNameKey)) {
                                            continue;
                                        }
                                        value.append(key).append(":{");
                                        value.append(rowData.get(j).getValue());
                                        value.append("}\n");
                                    }

                                }
                                // 变量增加用户姓名
                                if (exitCustomerName) {
                                    value.append(callUserNameKey).append(":{");
                                    value.append(callUserName);
                                    value.append("}\n");
                                }
                                robotCallTasksCustomer.setVariables(value.toString());
                            robotCallTasksCustomer.setImportedBy(userName);
                            robotCallTasksCustomer.setTenantId(tenantId);
                            robotCallTasksCustomer.setImportTime(createdAt);
                            robotCallTasksCustomer.setTaskId(taskId);
                            baseMapper.insert(robotCallTasksCustomer);
                            success = true;
                        }

                    }

                }
            }
            if (!success) {
                log.info("文件中被叫号码无有效数据 {}", taskId);
                return R.fail("文件中被叫号码无有效数据，请检查重试！");
            }
            return R.success("导入成功");
        } catch (IOException e) {
            log.error("导入用户失败 {}",e.getMessage(), e);
            return R.fail("导入用户失败");
        }
    }

    /**
     * 导入用户
     * @param file
     * @param taskId
     * @param importType {@link ImportTypeEnum}
     * @return
     */
    @Override
    public R<CallCenterTask> importCustomerV2(MultipartFile file, Long taskId, Integer importType) {
        RobotCallTasks robotCallTasks = robotCallTasksService.getById(taskId);
        if(robotCallTasks == null){
            log.error("呼叫任务不存在");
        }
        R<List<RobotAttributeVariable>> r = getAttribute(taskId);
        if (!r.isSuccess()) {
            log.info("获取变量失败 {} {}", r.getMsg(), taskId);
            return R.fail(r.getMsg());
        }
        List<RobotAttributeVariable> variableList = r.getData();
        List<RobotAttributeVariableDto> variableListDto = variableList.stream().map(item -> {
            RobotAttributeVariableDto dto = new RobotAttributeVariableDto();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());

        String tenantId = AuthUtil.getTenantId();
        /*
         * 将文件file写入到指定目录
         */
        String fileName = "导入AI呼叫用户"+ DateUtil.format(LocalDateTime.now(), PURE_DATETIME_PATTERN)+".xlsx";
        String filePath = UploadUtil.uploadFileToDateDir(file, callcenterProperty.getImportFileDir(), fileName);

        String paramString = null;
        String taskName = robotCallTasks.getTaskName();
        ImportRobotTaskCustomerParam importRobotTaskCustomerParam = new ImportRobotTaskCustomerParam();
        importRobotTaskCustomerParam.setTaskId(taskId);
        importRobotTaskCustomerParam.setImportType(importType);
        importRobotTaskCustomerParam.setQueryUserURL(this.getQueryUserURL());
        importRobotTaskCustomerParam.setVariableList(variableListDto);
        paramString = JSON.toJSONString(importRobotTaskCustomerParam);
        CallCenterTaskCreateDTO create = new CallCenterTaskCreateDTO()
                .setModule("AI外呼-任务列表")
                .setTaskName(taskName)
                .setTaskType(TaskTypeEnum.IMPORT.getCode())
                .setBizCode(AI_TASK_CUSTOMER_IMPORT_TASK)
                .setFileName(fileName)
                .setFilePath(filePath)
                .setServerName("")
                .setUrl("")
                .setParam(paramString)
                .setUserId(OwnAuthUtil.getUserId())
                .setCreatUser(OwnAuthUtil.getRealName())
                .setTenantId(tenantId);
        CallCenterTask taskSchedule = taskScheduleWrapper.submitToTaskSchedule(create);
        log.info("提交任务到调度中心 {}", taskSchedule);
        return R.data(taskSchedule);
    }

    /**
     * 添加号码
     * @param customerAddDTO
     * @return
     */
    @Override
    public R<RobotCallTasksCustomer> addCustomer(CustomerAddDTO customerAddDTO,Integer isTest) {
        R<List<RobotAttributeVariable>> r = getAttribute(customerAddDTO.getTaskId());
        if (!r.isSuccess()) {
            log.info("获取变量失败 {} {}", r.getMsg(), customerAddDTO.getTaskId());
            return R.fail(r.getMsg());
        }
        String calledNumber = null;
        String callUserName = null;
        String userId = null;
        if (customerAddDTO.getImportType() == ImportTypeEnum.PHONE_NUMBER.getCode()) {
            calledNumber = customerAddDTO.getVariablesMap().get(RobotConstant.CALLED_NUMBER);
        } else if (customerAddDTO.getImportType() == ImportTypeEnum.USER_ID.getCode()) {
            userId = customerAddDTO.getVariablesMap().get(RobotConstant.CALLED_NUMBER);
            CustomerInfoWrapper customerInfoWrapper = tenantServiceHolder.getCustomerInfoWrapper();
            SimpleUserInfoDto response = customerInfoWrapper.getUserBasicInfoByUserId(userId, OwnAuthUtil.getTenantId());
            if(response != null && response.getMobileNo() != null){
                calledNumber = response.getMobileNo();
                callUserName = response.getUserName();
            } else{
                log.info("addCustomer用户ID不存在 {}", userId);
            }
        } else {
            log.info("导入类型错误 {}", customerAddDTO.getImportType());
            return R.fail("导入类型错误");
        }
        if (StringUtils.isBlank(calledNumber)) {
            log.info("被叫号码为空 {}", customerAddDTO.getVariablesMap());
            return R.fail("被叫号码为空");
        }

        if(YesOrNoEnum.NO.getValue().equals(isTest)){
            Long count = baseMapper.countByCalledNumber(customerAddDTO.getTaskId(),calledNumber);
            if (count > 0) {
                log.info("号码已存在 {}",calledNumber);
                return R.fail("当前号码已存在");
            }
        }

        String userName = AuthUtil.getUserName();
        String tenantId = AuthUtil.getTenantId();
        LocalDateTime createdAt = LocalDateTime.now();
        RobotCallTasksCustomer robotCallTasksCustomer = new RobotCallTasksCustomer();
        robotCallTasksCustomer.setCalledNumber(calledNumber);
        robotCallTasksCustomer.setUserId(userId);
        robotCallTasksCustomer.setImportedBy(userName);
        robotCallTasksCustomer.setTenantId(tenantId);
        robotCallTasksCustomer.setImportTime(createdAt);
        if (CollectionUtil.isNotEmpty(r.getData())) {
            Set<String> keySet = customerAddDTO.getVariablesMap().keySet();
            StringBuilder value = new StringBuilder();
            // 是否存在客户姓名
            boolean exitCustomerName = StringUtils.isNotBlank(callUserName);
            for (String key : keySet) {
                if (exitCustomerName && key.equals(callUserNameKey)) {
                    continue;
                }
                value.append(key).append(":{");
                value.append(customerAddDTO.getVariablesMap().get(key));
                value.append("}\n");
            }
            // 变量增加用户姓名
            if (exitCustomerName) {
                value.append(callUserNameKey).append(":{");
                value.append(callUserName);
                value.append("}\n");
            }
            robotCallTasksCustomer.setVariables(value.toString());
        }



        robotCallTasksCustomer.setTaskId(customerAddDTO.getTaskId());
        robotCallTasksCustomer.setIsTest(isTest);
        robotCallTasksCustomer.setUserId(userId);
        baseMapper.insert(robotCallTasksCustomer);
        return R.data(robotCallTasksCustomer);
    }

    /**
     * 通话任务用户列表
     * @param queryReqVO
     * @param page
     * @return
     */
    @Override
    public IPage<RobotCallTasksCustomerVO> pageList(RobotCallTasksCustomerQueryReqVO queryReqVO, IPage<RobotCallTasksCustomerVO> page) {
        if (StringUtils.isBlank(queryReqVO.getUserId()) && StringUtils.isNotBlank(queryReqVO.getCustomerId())) {
            queryReqVO.setUserId(queryReqVO.getCustomerId());
        }
        RobotCallTasks robotCallTasks = robotCallTasksService.getById(queryReqVO.getTaskId());
        List<RobotAttributeVariable> robotAttributeVariableList = null;
        if (robotCallTasks != null) {
            RobotHistory robotHistory = robotService.getNewRobot(robotCallTasks.getRobotId());
            if (robotHistory != null) {
                robotAttributeVariableList = selectByHistoryId(robotHistory.getId());
            }
        }
        IPage<RobotCallTasksCustomerVO> pages = baseMapper.pageList(queryReqVO, page);
        if (pages != null && pages.getRecords().size() > 0){
            for (RobotCallTasksCustomerVO vo : pages.getRecords()) {
                vo.setCalledNumber(DesensitizationUtil.hidePhone(vo.getCalledNumber(),3,4));
                if(vo.getCallStatus() == null){
                    vo.setCallStatus(RobotCallStatus.UNCALLED.getCode());
                }
                if(robotAttributeVariableList != null){
                    vo.setVariables(getVariables(vo.getVariables(),robotAttributeVariableList));
                }
            }
        }
        return pages;
    }
    /**
     * 通话任务重播列表
     * @param queryReqVO
     * @param page
     * @return
     */
    @Override
    public IPage<RobotCallTasksCustomerVO> pageReCall(RobotCallTasksCustomerQueryReqVO queryReqVO, IPage<RobotCallTasksCustomerVO> page) {
        RobotCallTasks robotCallTasks = robotCallTasksService.getById(queryReqVO.getTaskId());
        List<RobotAttributeVariable> robotAttributeVariableList = null;
        if (robotCallTasks != null) {
            RobotHistory robotHistory = robotService.getNewRobot(robotCallTasks.getRobotId());
            if (robotHistory != null) {
                robotAttributeVariableList = selectByHistoryId(robotHistory.getId());
            }
        }
        IPage<RobotCallTasksCustomerVO> pages = baseMapper.pageListReCall(queryReqVO, page);
        if (pages != null && pages.getRecords().size() > 0){
            for (RobotCallTasksCustomerVO vo : pages.getRecords()) {
                vo.setCalledNumber(DesensitizationUtil.hidePhone(vo.getCalledNumber(),3,4));
                if(vo.getCallStatus() == null){
                    vo.setCallStatus(RobotCallStatus.UNCALLED.getCode());
                }
                if(robotAttributeVariableList != null){
                    vo.setVariables(getVariables(vo.getVariables(),robotAttributeVariableList));
                }
            }
        }
        return pages;
    }
    /**
     * 删除号码
     * @param taskCustomerId
     * @return
     */
    @Override
    public R delete(Long taskCustomerId) {
         RobotCallTasksCustomer exist = baseMapper.selectById(taskCustomerId);
         int count  = baseMapper.deleteByIdAndSessionIsNull(taskCustomerId);
         if (count > 0) {
             return R.success("删除成功");
         }else{
             return R.fail("删除失败");
         }
    }

    /**
     * 通话任务用户详情
     * @param taskCustomerId
     * @return
     */
    @Override
    public RobotCallTasksCustomerDetailVO detail(Long taskCustomerId) {
        RobotCallTasksCustomerDetailVO robotCallTasksCustomerDetailVO = new RobotCallTasksCustomerDetailVO();
        RobotCallTasksCustomer robotCallTasksCustomer = baseMapper.selectById(taskCustomerId);
        String variables = robotCallTasksCustomer.getVariables();
        if (robotCallTasksCustomer != null) {
            //通话任务
            RobotCallTasks robotCallTasks = robotCallTasksService.getById(robotCallTasksCustomer.getTaskId());
            RobotCallTasksVO robotCallTasksVO = BeanUtil.copyProperties(robotCallTasks, RobotCallTasksVO.class);
            RobotHistory robotHistory = robotService.getNewRobot(robotCallTasks.getRobotId());
            if (robotHistory != null) {
                robotCallTasksVO.setRobotVersion(robotHistory.getVersion());
                robotCallTasksVO.setRobotName(robotHistory.getRobotName());
                robotCallTasksVO.setRobotHistoryId(robotHistory.getId());
                List<RobotAttributeVariable> robotAttributeVariableList = selectByHistoryId(robotHistory.getId());
                variables =  getVariables(variables,robotAttributeVariableList );
            }
            robotCallTasksCustomerDetailVO.setRobotCallTasksVO(robotCallTasksVO);
            //通话记录
            RobotSession robotSession = robotSessionMapper.selectById(robotCallTasksCustomer.getLastSessionId());
            if(robotSession != null){
                RobotSessionVO robotSessionVO = BeanUtil.copyProperties(robotSession, RobotSessionVO.class);
                robotSessionVO.setCalledNumber(DesensitizationUtil.hidePhone(robotSession.getCalledNumber(),3,4));
                robotCallTasksCustomerDetailVO.setRobotSessionVO(robotSessionVO);
                //通话小结
                RobotSessionSummary robotSessionSummary =  robotSessionSummaryMapper.selectOneByUUid(robotSession.getUuid());
                if(robotSessionSummary != null){
                    RobotSessionSummaryVO robotSessionSummaryVO = BeanUtil.copyProperties(robotSessionSummary, RobotSessionSummaryVO.class);
                    if (robotSessionSummary.getUserContentValid() == 0 && robotSessionSummaryVO != null) {
                        robotSessionSummaryVO.setSummaryContentUser(null);
                    }
                    robotCallTasksCustomerDetailVO.setRobotSessionSummaryVO(robotSessionSummaryVO);
                }else{
                    log.info("通话小结为空 {}", taskCustomerId);
                }
            }else{
                log.info("通话记录为空 {}", taskCustomerId);
                RobotSessionVO robotSessionVO = new RobotSessionVO();
                robotSessionVO.setCalledNumber(DesensitizationUtil.hidePhone(robotCallTasksCustomer.getCalledNumber(),3,4));
                robotSessionVO.setVariables(robotCallTasksCustomer.getVariables());
                robotCallTasksCustomerDetailVO.setRobotSessionVO(robotSessionVO);
            }
        }
        robotCallTasksCustomerDetailVO.getRobotSessionVO().setVariables(variables);


        return robotCallTasksCustomerDetailVO;
    }

    private String getVariables(String variables ,List<RobotAttributeVariable> robotAttributeVariableList) {
        if(variables != null && variables.contains("\n")){
            //把文本中的手机号脱敏中间5位
            variables = variables.replaceAll("\\{(\\d{3})\\d{4}(\\d{4})\\}", "{$1*****$2}");
        }

        if(CollectionUtil.isNotEmpty(robotAttributeVariableList)){
            for(RobotAttributeVariable robotAttributeVariable : robotAttributeVariableList){
                if(variables.contains(robotAttributeVariable.getVariableKey())){
                    variables = variables.replace(robotAttributeVariable.getVariableKey(),robotAttributeVariable.getVariableName());
                }
            }
        }
        return variables;
    }

    /**
     * 根据任务ID获取任待拨打务客户列表
     * @param taskId
     * @param robotConcurrency 获取条数
     * @return
     */
    @Override
    public List<RobotCallTasksCustomer> getCallTasksCustomers(Long taskId, Integer robotConcurrency,Integer maxReplayTimes) {
        return this.baseMapper.getCallTasksCustomers(taskId, robotConcurrency, maxReplayTimes);
    }

    /**
     * 待呼叫总数
     * @param taskId
     * @return
     */
    @Override
    public int selectRobotCount(  Long taskId){
        return baseMapper.selectRobotCount(taskId);
    }

    @Override
    public RobotCallTasksCustomer selectOneBySessionId(Long sessionId) {
        return baseMapper.selectOneBySessionId(sessionId);
    }

    /**
     * 批量添加客户到AI外呼任务
     * @param selectedCustomerAddDTO
     * @return R<成功条数>
     */
    @Override
    public R<Integer> addSelectedCustomer(SelectedCustomerAddDTO selectedCustomerAddDTO) {
        String userName = AuthUtil.getUserName();
        String tenantId = AuthUtil.getTenantId();
        LocalDateTime createdAt = LocalDateTime.now();
        R<List<RobotAttributeVariable>> r = getAttribute(selectedCustomerAddDTO.getTaskId());
        if (!r.isSuccess()) {
            log.info("获取变量失败 {} {}", r.getMsg(), selectedCustomerAddDTO.getTaskId());
            return R.fail(r.getMsg());
        }
        //客户列表 Map<用户属性,属性值>
        List<Map<String,String>> customerList = selectedCustomerAddDTO.getCustomerList();
        if(CollectionUtil.isEmpty(customerList)){
            log.info("客户列表为空 {}", customerList);
            return R.fail("获取的客户列表为空");
        }
        String callNumberKey = selectedCustomerAddDTO.getVariablesMap().get(RobotConstant.CALLED_NUMBER);
        if (StringUtils.isBlank(callNumberKey)) {
            log.info("被叫号码对应的用户属性为空 {}", selectedCustomerAddDTO.getVariablesMap());
            return R.fail("被叫号码对应的用户属性为空");
        }
        int successCount = 0;
        Map<String,String> variablesMap = selectedCustomerAddDTO.getVariablesMap();
        List<RobotCallTasksCustomer> addList = new ArrayList<>();
        for (Map<String,String> map : customerList){
            String calledNumber = map.get(callNumberKey);
            if (StringUtils.isBlank(calledNumber)) {
                log.info("被叫号码为空 {}", map);
                continue;
            }
            RobotCallTasksCustomer robotCallTasksCustomer = new RobotCallTasksCustomer();
            robotCallTasksCustomer.setCalledNumber(calledNumber);
            robotCallTasksCustomer.setImportedBy(userName);
            robotCallTasksCustomer.setTenantId(tenantId);
            robotCallTasksCustomer.setImportTime(createdAt);
            if (CollectionUtil.isNotEmpty(r.getData())) {
                Set<String> keySet = variablesMap.keySet();
                StringBuilder value = new StringBuilder();
                for (String key : keySet) {
                    value.append(key).append(":{");
                    value.append(map.get(variablesMap.get(key)));
                    value.append("}\n");
                }
                robotCallTasksCustomer.setVariables(value.toString());
            }
            robotCallTasksCustomer.setTaskId(selectedCustomerAddDTO.getTaskId());
            robotCallTasksCustomer.setIsTest(YesOrNoEnum.NO.getValue());
            robotCallTasksCustomer.setUserId(map.get("customerId"));
            addList.add(robotCallTasksCustomer);
        }
        if(CollectionUtil.isNotEmpty(addList)){
            boolean b = this.saveBatch(addList);
            if(b){
                successCount = addList.size();
                log.info("批量导入成功 {}", successCount);
            }
        }
        return R.data(successCount);
    }

    @Override
    public IPage<RobotCallTasksCustomerVO> pageListByUserId(RobotCallTasksCustomerQueryReqVO queryReqVO, IPage<RobotCallTasksCustomerVO> page) {

        if (StringUtils.isBlank(queryReqVO.getUserId()) && StringUtils.isNotBlank(queryReqVO.getCustomerId())) {
            queryReqVO.setUserId(queryReqVO.getCustomerId());
        }
        IPage<RobotCallTasksCustomerVO> pages = baseMapper.pageList(queryReqVO, page);
        if (pages != null && pages.getRecords().size() > 0){
            for (RobotCallTasksCustomerVO vo : pages.getRecords()) {
                vo.setCalledNumber(DesensitizationUtil.hidePhone(vo.getCalledNumber(),3,4));
                if(vo.getCallStatus() == null){
                    vo.setCallStatus(RobotCallStatus.UNCALLED.getCode());
                }
                List<RobotAttributeVariable> robotAttributeVariableList = null;
                if (vo.getRobotHistoryId() != null) {
                    robotAttributeVariableList = selectByHistoryId(vo.getRobotHistoryId());
                }
                if(robotAttributeVariableList != null){
                    vo.setVariables(getVariables(vo.getVariables(),robotAttributeVariableList));
                }
            }
        }
        return pages;
    }

    /**
     * 拷贝接听失败的用户到另一个任务
     * @param targetTaskId
     * @param sourceTaskId
     * @return
     */
    @Override
    public int copyCustomer(Long targetTaskId, Long sourceTaskId) {
        String userName = AuthUtil.getUserName();
        return baseMapper.copyCustomer(targetTaskId, sourceTaskId,userName);
    }

    /**
     * 批量导出
     * @param queryReqVO
     * @return
     */
    @Override
    public CallCenterTask exportBatch(RobotCallTasksCustomerQueryReqVO queryReqVO) {
        if (StringUtils.isBlank(queryReqVO.getUserId()) && StringUtils.isNotBlank(queryReqVO.getCustomerId())) {
            queryReqVO.setUserId(queryReqVO.getCustomerId());
        }
        RobotCallTasks robotCallTasks = robotCallTasksService.getById(queryReqVO.getTaskId());
        if (robotCallTasks == null) {
            throw new RobotException("任务不存在");
        }
        RobotHistory robotHistory = robotService.getNewRobot(robotCallTasks.getRobotId());
        if (robotHistory != null) {
            List<RobotAttributeVariable>  robotAttributeVariableList = selectByHistoryId(robotHistory.getId());
            List<RobotAttributeVariableVO> robotAttributeVariableVOList = robotAttributeVariableList.stream().map( v->{
                RobotAttributeVariableVO robotAttributeVariableVO = new RobotAttributeVariableVO();
                BeanUtils.copyProperties(v,robotAttributeVariableVO);
                return robotAttributeVariableVO;
            }).collect(Collectors.toList());
            queryReqVO.setRobotAttributeVariableList(robotAttributeVariableVOList);
        }


        String paramString = null;
        paramString = JsonUtilExt.beanToJson(queryReqVO);

        CallCenterTaskCreateDTO create = new CallCenterTaskCreateDTO()
                .setModule("AI外呼-任务列表")
                .setTaskName("AI外呼-任务列表")
                .setTaskType(TaskTypeEnum.EXPORT.getCode())
                .setBizCode(AI_TASK_CUSTOMER_EXPORT_TASK)
                .setFilePath("")
                .setFileName("")
                .setServerName("")
                .setUrl("")
                .setParam(paramString)
                .setUserId(OwnAuthUtil.getUserId())
                .setCreatUser(OwnAuthUtil.getRealName())
                .setTenantId(OwnAuthUtil.getTenantId());
        return taskScheduleWrapper.submitToTaskSchedule(create);
    }

    /**
     * 查询变量
     * @param robotHistoryId
     * @return
     */
    private List<RobotAttributeVariable> selectByHistoryId(Long robotHistoryId){
        List<RobotAttributeVariable> robotAttributeVariableList = robotAttributeVariableMapper.selectByHistoryId(robotHistoryId);
        // robotAttributeVariableList 重新排序把 RobotConstant.CALLED_NUMBER 放在第一个
        robotAttributeVariableList.sort(Comparator.comparing((RobotAttributeVariable var) ->
                RobotConstant.CALLED_NUMBER.equals(var.getVariableKey()) ? "" : var.getVariableKey()
        ));
        return robotAttributeVariableList;
    }

    private String getQueryUserURL(){
        return robotDomain+"/ai/robot/call/tasks/customer/querUserInfo";
    }
}
